// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGAttributeSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGAttributeSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGMetadata_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGAttributeType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeType;
static UEnum* EAuracronPCGAttributeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeType>()
{
	return EAuracronPCGAttributeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Boolean.DisplayName", "Boolean" },
		{ "Boolean.Name", "EAuracronPCGAttributeType::Boolean" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute data types supported by the system\n" },
#endif
		{ "Double.DisplayName", "Double" },
		{ "Double.Name", "EAuracronPCGAttributeType::Double" },
		{ "Float.DisplayName", "Float" },
		{ "Float.Name", "EAuracronPCGAttributeType::Float" },
		{ "Int32.DisplayName", "Int32" },
		{ "Int32.Name", "EAuracronPCGAttributeType::Int32" },
		{ "Int64.DisplayName", "Int64" },
		{ "Int64.Name", "EAuracronPCGAttributeType::Int64" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
		{ "Name.DisplayName", "Name" },
		{ "Name.Name", "EAuracronPCGAttributeType::Name" },
		{ "Quat.DisplayName", "Quaternion" },
		{ "Quat.Name", "EAuracronPCGAttributeType::Quat" },
		{ "Rotator.DisplayName", "Rotator" },
		{ "Rotator.Name", "EAuracronPCGAttributeType::Rotator" },
		{ "SoftClassPath.DisplayName", "Soft Class Path" },
		{ "SoftClassPath.Name", "EAuracronPCGAttributeType::SoftClassPath" },
		{ "SoftObjectPath.DisplayName", "Soft Object Path" },
		{ "SoftObjectPath.Name", "EAuracronPCGAttributeType::SoftObjectPath" },
		{ "String.DisplayName", "String" },
		{ "String.Name", "EAuracronPCGAttributeType::String" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute data types supported by the system" },
#endif
		{ "Transform.DisplayName", "Transform" },
		{ "Transform.Name", "EAuracronPCGAttributeType::Transform" },
		{ "Vector.DisplayName", "Vector" },
		{ "Vector.Name", "EAuracronPCGAttributeType::Vector" },
		{ "Vector2.DisplayName", "Vector2" },
		{ "Vector2.Name", "EAuracronPCGAttributeType::Vector2" },
		{ "Vector4.DisplayName", "Vector4" },
		{ "Vector4.Name", "EAuracronPCGAttributeType::Vector4" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeType::Float", (int64)EAuracronPCGAttributeType::Float },
		{ "EAuracronPCGAttributeType::Double", (int64)EAuracronPCGAttributeType::Double },
		{ "EAuracronPCGAttributeType::Int32", (int64)EAuracronPCGAttributeType::Int32 },
		{ "EAuracronPCGAttributeType::Int64", (int64)EAuracronPCGAttributeType::Int64 },
		{ "EAuracronPCGAttributeType::Vector2", (int64)EAuracronPCGAttributeType::Vector2 },
		{ "EAuracronPCGAttributeType::Vector", (int64)EAuracronPCGAttributeType::Vector },
		{ "EAuracronPCGAttributeType::Vector4", (int64)EAuracronPCGAttributeType::Vector4 },
		{ "EAuracronPCGAttributeType::Rotator", (int64)EAuracronPCGAttributeType::Rotator },
		{ "EAuracronPCGAttributeType::Quat", (int64)EAuracronPCGAttributeType::Quat },
		{ "EAuracronPCGAttributeType::Transform", (int64)EAuracronPCGAttributeType::Transform },
		{ "EAuracronPCGAttributeType::String", (int64)EAuracronPCGAttributeType::String },
		{ "EAuracronPCGAttributeType::Name", (int64)EAuracronPCGAttributeType::Name },
		{ "EAuracronPCGAttributeType::Boolean", (int64)EAuracronPCGAttributeType::Boolean },
		{ "EAuracronPCGAttributeType::SoftObjectPath", (int64)EAuracronPCGAttributeType::SoftObjectPath },
		{ "EAuracronPCGAttributeType::SoftClassPath", (int64)EAuracronPCGAttributeType::SoftClassPath },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeType",
	"EAuracronPCGAttributeType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeType.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeType ***************************************************

// ********** Begin Enum EAuracronPCGAttributeInterpolation ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation;
static UEnum* EAuracronPCGAttributeInterpolation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeInterpolation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeInterpolation>()
{
	return EAuracronPCGAttributeInterpolation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interpolation modes for attribute blending\n" },
#endif
		{ "Cubic.DisplayName", "Cubic" },
		{ "Cubic.Name", "EAuracronPCGAttributeInterpolation::Cubic" },
		{ "Curve.DisplayName", "Curve Based" },
		{ "Curve.Name", "EAuracronPCGAttributeInterpolation::Curve" },
		{ "Custom.DisplayName", "Custom Function" },
		{ "Custom.Name", "EAuracronPCGAttributeInterpolation::Custom" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EAuracronPCGAttributeInterpolation::Linear" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGAttributeInterpolation::None" },
		{ "Smootherstep.DisplayName", "Smootherstep" },
		{ "Smootherstep.Name", "EAuracronPCGAttributeInterpolation::Smootherstep" },
		{ "Smoothstep.DisplayName", "Smoothstep" },
		{ "Smoothstep.Name", "EAuracronPCGAttributeInterpolation::Smoothstep" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interpolation modes for attribute blending" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeInterpolation::None", (int64)EAuracronPCGAttributeInterpolation::None },
		{ "EAuracronPCGAttributeInterpolation::Linear", (int64)EAuracronPCGAttributeInterpolation::Linear },
		{ "EAuracronPCGAttributeInterpolation::Cubic", (int64)EAuracronPCGAttributeInterpolation::Cubic },
		{ "EAuracronPCGAttributeInterpolation::Smoothstep", (int64)EAuracronPCGAttributeInterpolation::Smoothstep },
		{ "EAuracronPCGAttributeInterpolation::Smootherstep", (int64)EAuracronPCGAttributeInterpolation::Smootherstep },
		{ "EAuracronPCGAttributeInterpolation::Curve", (int64)EAuracronPCGAttributeInterpolation::Curve },
		{ "EAuracronPCGAttributeInterpolation::Custom", (int64)EAuracronPCGAttributeInterpolation::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeInterpolation",
	"EAuracronPCGAttributeInterpolation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeInterpolation ******************************************

// ********** Begin Enum EAuracronPCGAttributeFilterMode *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode;
static UEnum* EAuracronPCGAttributeFilterMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeFilterMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeFilterMode>()
{
	return EAuracronPCGAttributeFilterMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute filtering modes\n" },
#endif
		{ "Exclude.DisplayName", "Exclude" },
		{ "Exclude.Name", "EAuracronPCGAttributeFilterMode::Exclude" },
		{ "ExcludePattern.DisplayName", "Exclude Pattern" },
		{ "ExcludePattern.Name", "EAuracronPCGAttributeFilterMode::ExcludePattern" },
		{ "Include.DisplayName", "Include" },
		{ "Include.Name", "EAuracronPCGAttributeFilterMode::Include" },
		{ "IncludePattern.DisplayName", "Include Pattern" },
		{ "IncludePattern.Name", "EAuracronPCGAttributeFilterMode::IncludePattern" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute filtering modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeFilterMode::Include", (int64)EAuracronPCGAttributeFilterMode::Include },
		{ "EAuracronPCGAttributeFilterMode::Exclude", (int64)EAuracronPCGAttributeFilterMode::Exclude },
		{ "EAuracronPCGAttributeFilterMode::IncludePattern", (int64)EAuracronPCGAttributeFilterMode::IncludePattern },
		{ "EAuracronPCGAttributeFilterMode::ExcludePattern", (int64)EAuracronPCGAttributeFilterMode::ExcludePattern },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeFilterMode",
	"EAuracronPCGAttributeFilterMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeFilterMode *********************************************

// ********** Begin Enum EAuracronPCGAttributeValidation *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation;
static UEnum* EAuracronPCGAttributeValidation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeValidation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeValidation>()
{
	return EAuracronPCGAttributeValidation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute validation rules\n" },
#endif
		{ "Custom.DisplayName", "Custom Validation" },
		{ "Custom.Name", "EAuracronPCGAttributeValidation::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGAttributeValidation::None" },
		{ "NotEmpty.DisplayName", "Not Empty" },
		{ "NotEmpty.Name", "EAuracronPCGAttributeValidation::NotEmpty" },
		{ "NotNull.DisplayName", "Not Null" },
		{ "NotNull.Name", "EAuracronPCGAttributeValidation::NotNull" },
		{ "Pattern.DisplayName", "Pattern Match" },
		{ "Pattern.Name", "EAuracronPCGAttributeValidation::Pattern" },
		{ "Range.DisplayName", "Range" },
		{ "Range.Name", "EAuracronPCGAttributeValidation::Range" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute validation rules" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeValidation::None", (int64)EAuracronPCGAttributeValidation::None },
		{ "EAuracronPCGAttributeValidation::Range", (int64)EAuracronPCGAttributeValidation::Range },
		{ "EAuracronPCGAttributeValidation::NotNull", (int64)EAuracronPCGAttributeValidation::NotNull },
		{ "EAuracronPCGAttributeValidation::NotEmpty", (int64)EAuracronPCGAttributeValidation::NotEmpty },
		{ "EAuracronPCGAttributeValidation::Pattern", (int64)EAuracronPCGAttributeValidation::Pattern },
		{ "EAuracronPCGAttributeValidation::Custom", (int64)EAuracronPCGAttributeValidation::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeValidation",
	"EAuracronPCGAttributeValidation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeValidation *********************************************

// ********** Begin Enum EAuracronPCGAttributeAggregation ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation;
static UEnum* EAuracronPCGAttributeAggregation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAttributeAggregation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeAggregation>()
{
	return EAuracronPCGAttributeAggregation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Average.DisplayName", "Average" },
		{ "Average.Name", "EAuracronPCGAttributeAggregation::Average" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute aggregation operations\n" },
#endif
		{ "Count.DisplayName", "Count" },
		{ "Count.Name", "EAuracronPCGAttributeAggregation::Count" },
		{ "First.DisplayName", "First" },
		{ "First.Name", "EAuracronPCGAttributeAggregation::First" },
		{ "Last.DisplayName", "Last" },
		{ "Last.Name", "EAuracronPCGAttributeAggregation::Last" },
		{ "Max.DisplayName", "Maximum" },
		{ "Max.Name", "EAuracronPCGAttributeAggregation::Max" },
		{ "Median.DisplayName", "Median" },
		{ "Median.Name", "EAuracronPCGAttributeAggregation::Median" },
		{ "Min.DisplayName", "Minimum" },
		{ "Min.Name", "EAuracronPCGAttributeAggregation::Min" },
		{ "Mode.DisplayName", "Mode" },
		{ "Mode.Name", "EAuracronPCGAttributeAggregation::Mode" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGAttributeAggregation::None" },
		{ "StandardDev.DisplayName", "Standard Deviation" },
		{ "StandardDev.Name", "EAuracronPCGAttributeAggregation::StandardDev" },
		{ "Sum.DisplayName", "Sum" },
		{ "Sum.Name", "EAuracronPCGAttributeAggregation::Sum" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute aggregation operations" },
#endif
		{ "Variance.DisplayName", "Variance" },
		{ "Variance.Name", "EAuracronPCGAttributeAggregation::Variance" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAttributeAggregation::None", (int64)EAuracronPCGAttributeAggregation::None },
		{ "EAuracronPCGAttributeAggregation::Sum", (int64)EAuracronPCGAttributeAggregation::Sum },
		{ "EAuracronPCGAttributeAggregation::Average", (int64)EAuracronPCGAttributeAggregation::Average },
		{ "EAuracronPCGAttributeAggregation::Min", (int64)EAuracronPCGAttributeAggregation::Min },
		{ "EAuracronPCGAttributeAggregation::Max", (int64)EAuracronPCGAttributeAggregation::Max },
		{ "EAuracronPCGAttributeAggregation::Count", (int64)EAuracronPCGAttributeAggregation::Count },
		{ "EAuracronPCGAttributeAggregation::First", (int64)EAuracronPCGAttributeAggregation::First },
		{ "EAuracronPCGAttributeAggregation::Last", (int64)EAuracronPCGAttributeAggregation::Last },
		{ "EAuracronPCGAttributeAggregation::Median", (int64)EAuracronPCGAttributeAggregation::Median },
		{ "EAuracronPCGAttributeAggregation::Mode", (int64)EAuracronPCGAttributeAggregation::Mode },
		{ "EAuracronPCGAttributeAggregation::StandardDev", (int64)EAuracronPCGAttributeAggregation::StandardDev },
		{ "EAuracronPCGAttributeAggregation::Variance", (int64)EAuracronPCGAttributeAggregation::Variance },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAttributeAggregation",
	"EAuracronPCGAttributeAggregation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation.InnerSingleton;
}
// ********** End Enum EAuracronPCGAttributeAggregation ********************************************

// ********** Begin ScriptStruct FAuracronPCGAttributeDescriptor ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor;
class UScriptStruct* FAuracronPCGAttributeDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGAttributeDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Attribute Descriptor\n * Describes the properties and constraints of a PCG attribute\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute Descriptor\nDescribes the properties and constraints of a PCG attribute" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeType_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationRule_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueRange_MetaData[] = {
		{ "Category", "Validation" },
		{ "EditCondition", "ValidationRule == EAuracronPCGAttributeValidation::Range" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationPattern_MetaData[] = {
		{ "Category", "Validation" },
		{ "EditCondition", "ValidationRule == EAuracronPCGAttributeValidation::Pattern" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsReadOnly_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRequired_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AttributeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AttributeType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationRule_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationRule;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValueRange;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationPattern;
	static void NewProp_bIsReadOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsReadOnly;
	static void NewProp_bIsRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRequired;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGAttributeDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeType = { "AttributeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, AttributeType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeType_MetaData), NewProp_AttributeType_MetaData) }; // 4182054873
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationRule_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationRule = { "ValidationRule", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, ValidationRule), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeValidation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationRule_MetaData), NewProp_ValidationRule_MetaData) }; // 828365112
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValueRange = { "ValueRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, ValueRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueRange_MetaData), NewProp_ValueRange_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationPattern = { "ValidationPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, ValidationPattern), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationPattern_MetaData), NewProp_ValidationPattern_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsReadOnly_SetBit(void* Obj)
{
	((FAuracronPCGAttributeDescriptor*)Obj)->bIsReadOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsReadOnly = { "bIsReadOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAttributeDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsReadOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsReadOnly_MetaData), NewProp_bIsReadOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsRequired_SetBit(void* Obj)
{
	((FAuracronPCGAttributeDescriptor*)Obj)->bIsRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsRequired = { "bIsRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAttributeDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsRequired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRequired_MetaData), NewProp_bIsRequired_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeDescriptor, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_AttributeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationRule_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationRule,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValueRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_ValidationPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsReadOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_bIsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewProp_Tags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGAttributeDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGAttributeDescriptor),
	alignof(FAuracronPCGAttributeDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGAttributeDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGAttributeOperation ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation;
class UScriptStruct* FAuracronPCGAttributeOperation::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGAttributeOperation"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Attribute Operation Descriptor\n * Describes an operation to be performed on attributes\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute Operation Descriptor\nDescribes an operation to be performed on attributes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceAttribute_MetaData[] = {
		{ "Category", "Operation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetAttribute_MetaData[] = {
		{ "Category", "Operation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Operation_MetaData[] = {
		{ "Category", "Operation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationParameter_MetaData[] = {
		{ "Category", "Operation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateIfNotExists_MetaData[] = {
		{ "Category", "Operation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetAttribute;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationParameter;
	static void NewProp_bCreateIfNotExists_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateIfNotExists;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGAttributeOperation>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_SourceAttribute = { "SourceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeOperation, SourceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceAttribute_MetaData), NewProp_SourceAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_TargetAttribute = { "TargetAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeOperation, TargetAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetAttribute_MetaData), NewProp_TargetAttribute_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeOperation, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Operation_MetaData), NewProp_Operation_MetaData) }; // 3035535659
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_OperationParameter = { "OperationParameter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGAttributeOperation, OperationParameter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationParameter_MetaData), NewProp_OperationParameter_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_bCreateIfNotExists_SetBit(void* Obj)
{
	((FAuracronPCGAttributeOperation*)Obj)->bCreateIfNotExists = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_bCreateIfNotExists = { "bCreateIfNotExists", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGAttributeOperation), &Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_bCreateIfNotExists_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateIfNotExists_MetaData), NewProp_bCreateIfNotExists_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_SourceAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_TargetAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_OperationParameter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewProp_bCreateIfNotExists,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGAttributeOperation",
	Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::PropPointers),
	sizeof(FAuracronPCGAttributeOperation),
	alignof(FAuracronPCGAttributeOperation),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGAttributeOperation **************************************

// ********** Begin Class UAuracronPCGAttributeCreatorSettings *************************************
void UAuracronPCGAttributeCreatorSettings::StaticRegisterNativesUAuracronPCGAttributeCreatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings;
UClass* UAuracronPCGAttributeCreatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeCreatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeCreatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeCreatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_NoRegister()
{
	return UAuracronPCGAttributeCreatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributesToCreate_MetaData[] = {
		{ "Category", "Attribute Creation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attributes to create\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attributes to create" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOverwriteExisting_MetaData[] = {
		{ "Category", "Creation Options" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Creation options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creation options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateOnCreation_MetaData[] = {
		{ "Category", "Creation Options" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateMetadataIfMissing_MetaData[] = {
		{ "Category", "Creation Options" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBatchCreation_MetaData[] = {
		{ "Category", "Batch Creation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch creation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch creation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributePrefix_MetaData[] = {
		{ "Category", "Batch Creation" },
		{ "EditCondition", "bUseBatchCreation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchCount_MetaData[] = {
		{ "Category", "Batch Creation" },
		{ "EditCondition", "bUseBatchCreation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchType_MetaData[] = {
		{ "Category", "Batch Creation" },
		{ "EditCondition", "bUseBatchCreation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttributesToCreate_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AttributesToCreate;
	static void NewProp_bOverwriteExisting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOverwriteExisting;
	static void NewProp_bValidateOnCreation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateOnCreation;
	static void NewProp_bCreateMetadataIfMissing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateMetadataIfMissing;
	static void NewProp_bUseBatchCreation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBatchCreation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributePrefix;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchCount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BatchType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BatchType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeCreatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributesToCreate_Inner = { "AttributesToCreate", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, METADATA_PARAMS(0, nullptr) }; // 890614519
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributesToCreate = { "AttributesToCreate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeCreatorSettings, AttributesToCreate), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributesToCreate_MetaData), NewProp_AttributesToCreate_MetaData) }; // 890614519
void Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bOverwriteExisting_SetBit(void* Obj)
{
	((UAuracronPCGAttributeCreatorSettings*)Obj)->bOverwriteExisting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bOverwriteExisting = { "bOverwriteExisting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeCreatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bOverwriteExisting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOverwriteExisting_MetaData), NewProp_bOverwriteExisting_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bValidateOnCreation_SetBit(void* Obj)
{
	((UAuracronPCGAttributeCreatorSettings*)Obj)->bValidateOnCreation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bValidateOnCreation = { "bValidateOnCreation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeCreatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bValidateOnCreation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateOnCreation_MetaData), NewProp_bValidateOnCreation_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bCreateMetadataIfMissing_SetBit(void* Obj)
{
	((UAuracronPCGAttributeCreatorSettings*)Obj)->bCreateMetadataIfMissing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bCreateMetadataIfMissing = { "bCreateMetadataIfMissing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeCreatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bCreateMetadataIfMissing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateMetadataIfMissing_MetaData), NewProp_bCreateMetadataIfMissing_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bUseBatchCreation_SetBit(void* Obj)
{
	((UAuracronPCGAttributeCreatorSettings*)Obj)->bUseBatchCreation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bUseBatchCreation = { "bUseBatchCreation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeCreatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bUseBatchCreation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBatchCreation_MetaData), NewProp_bUseBatchCreation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributePrefix = { "AttributePrefix", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeCreatorSettings, AttributePrefix), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributePrefix_MetaData), NewProp_AttributePrefix_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchCount = { "BatchCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeCreatorSettings, BatchCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchCount_MetaData), NewProp_BatchCount_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchType = { "BatchType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeCreatorSettings, BatchType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchType_MetaData), NewProp_BatchType_MetaData) }; // 4182054873
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributesToCreate_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributesToCreate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bOverwriteExisting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bValidateOnCreation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bCreateMetadataIfMissing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_bUseBatchCreation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_AttributePrefix,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::NewProp_BatchType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeCreatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeCreatorSettings);
UAuracronPCGAttributeCreatorSettings::~UAuracronPCGAttributeCreatorSettings() {}
// ********** End Class UAuracronPCGAttributeCreatorSettings ***************************************

// ********** Begin Class UAuracronPCGAttributeModifierSettings ************************************
void UAuracronPCGAttributeModifierSettings::StaticRegisterNativesUAuracronPCGAttributeModifierSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings;
UClass* UAuracronPCGAttributeModifierSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeModifierSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeModifierSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeModifierSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_NoRegister()
{
	return UAuracronPCGAttributeModifierSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Operations_MetaData[] = {
		{ "Category", "Modification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Modification operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modification operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInterpolation_MetaData[] = {
		{ "Category", "Interpolation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interpolation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interpolation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationMode_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "EditCondition", "bUseInterpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationCurve_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "EditCondition", "InterpolationMode == EAuracronPCGAttributeInterpolation::Curve" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseConditionalModification_MetaData[] = {
		{ "Category", "Conditional" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Conditional modification\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conditional modification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionAttribute_MetaData[] = {
		{ "Category", "Conditional" },
		{ "EditCondition", "bUseConditionalModification" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionRange_MetaData[] = {
		{ "Category", "Conditional" },
		{ "EditCondition", "bUseConditionalModification" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseParallelProcessing_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Operations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Operations;
	static void NewProp_bUseInterpolation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInterpolation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterpolationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterpolationMode;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InterpolationCurve;
	static void NewProp_bUseConditionalModification_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseConditionalModification;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionAttribute;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConditionRange;
	static void NewProp_bUseParallelProcessing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseParallelProcessing;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeModifierSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_Operations_Inner = { "Operations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation, METADATA_PARAMS(0, nullptr) }; // 3751331859
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_Operations = { "Operations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeModifierSettings, Operations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Operations_MetaData), NewProp_Operations_MetaData) }; // 3751331859
void Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseInterpolation_SetBit(void* Obj)
{
	((UAuracronPCGAttributeModifierSettings*)Obj)->bUseInterpolation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseInterpolation = { "bUseInterpolation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeModifierSettings), &Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseInterpolation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInterpolation_MetaData), NewProp_bUseInterpolation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationMode = { "InterpolationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeModifierSettings, InterpolationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationMode_MetaData), NewProp_InterpolationMode_MetaData) }; // 1837227816
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationCurve = { "InterpolationCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeModifierSettings, InterpolationCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationCurve_MetaData), NewProp_InterpolationCurve_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseConditionalModification_SetBit(void* Obj)
{
	((UAuracronPCGAttributeModifierSettings*)Obj)->bUseConditionalModification = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseConditionalModification = { "bUseConditionalModification", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeModifierSettings), &Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseConditionalModification_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseConditionalModification_MetaData), NewProp_bUseConditionalModification_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_ConditionAttribute = { "ConditionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeModifierSettings, ConditionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionAttribute_MetaData), NewProp_ConditionAttribute_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_ConditionRange = { "ConditionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeModifierSettings, ConditionRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionRange_MetaData), NewProp_ConditionRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseParallelProcessing_SetBit(void* Obj)
{
	((UAuracronPCGAttributeModifierSettings*)Obj)->bUseParallelProcessing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseParallelProcessing = { "bUseParallelProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeModifierSettings), &Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseParallelProcessing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseParallelProcessing_MetaData), NewProp_bUseParallelProcessing_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_Operations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_Operations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseInterpolation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_InterpolationCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseConditionalModification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_ConditionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_ConditionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::NewProp_bUseParallelProcessing,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeModifierSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeModifierSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeModifierSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeModifierSettings);
UAuracronPCGAttributeModifierSettings::~UAuracronPCGAttributeModifierSettings() {}
// ********** End Class UAuracronPCGAttributeModifierSettings **************************************

// ********** Begin Class UAuracronPCGAttributeInterpolatorSettings ********************************
void UAuracronPCGAttributeInterpolatorSettings::StaticRegisterNativesUAuracronPCGAttributeInterpolatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings;
UClass* UAuracronPCGAttributeInterpolatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeInterpolatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeInterpolatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeInterpolatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_NoRegister()
{
	return UAuracronPCGAttributeInterpolatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceAttributeA_MetaData[] = {
		{ "Category", "Interpolation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Source attributes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Source attributes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceAttributeB_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetAttribute_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlphaAttribute_MetaData[] = {
		{ "Category", "Interpolation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interpolation control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interpolation control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAlpha_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationMethod_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationCurve_MetaData[] = {
		{ "Category", "Curve Interpolation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Curve-based interpolation\n" },
#endif
		{ "EditCondition", "InterpolationMethod == EAuracronPCGAttributeInterpolation::Curve" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curve-based interpolation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bClampAlpha_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeAlpha_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlphaRange_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceAttributeA;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceAttributeB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AlphaAttribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultAlpha;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterpolationMethod_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterpolationMethod;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InterpolationCurve;
	static void NewProp_bClampAlpha_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClampAlpha;
	static void NewProp_bNormalizeAlpha_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeAlpha;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AlphaRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeInterpolatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_SourceAttributeA = { "SourceAttributeA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, SourceAttributeA), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceAttributeA_MetaData), NewProp_SourceAttributeA_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_SourceAttributeB = { "SourceAttributeB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, SourceAttributeB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceAttributeB_MetaData), NewProp_SourceAttributeB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_TargetAttribute = { "TargetAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, TargetAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetAttribute_MetaData), NewProp_TargetAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_AlphaAttribute = { "AlphaAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, AlphaAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlphaAttribute_MetaData), NewProp_AlphaAttribute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_DefaultAlpha = { "DefaultAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, DefaultAlpha), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAlpha_MetaData), NewProp_DefaultAlpha_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationMethod_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationMethod = { "InterpolationMethod", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, InterpolationMethod), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationMethod_MetaData), NewProp_InterpolationMethod_MetaData) }; // 1837227816
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationCurve = { "InterpolationCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, InterpolationCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationCurve_MetaData), NewProp_InterpolationCurve_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bClampAlpha_SetBit(void* Obj)
{
	((UAuracronPCGAttributeInterpolatorSettings*)Obj)->bClampAlpha = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bClampAlpha = { "bClampAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeInterpolatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bClampAlpha_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bClampAlpha_MetaData), NewProp_bClampAlpha_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bNormalizeAlpha_SetBit(void* Obj)
{
	((UAuracronPCGAttributeInterpolatorSettings*)Obj)->bNormalizeAlpha = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bNormalizeAlpha = { "bNormalizeAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeInterpolatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bNormalizeAlpha_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeAlpha_MetaData), NewProp_bNormalizeAlpha_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_AlphaRange = { "AlphaRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeInterpolatorSettings, AlphaRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlphaRange_MetaData), NewProp_AlphaRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_SourceAttributeA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_SourceAttributeB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_TargetAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_AlphaAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_DefaultAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationMethod_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationMethod,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_InterpolationCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bClampAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_bNormalizeAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::NewProp_AlphaRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeInterpolatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeInterpolatorSettings);
UAuracronPCGAttributeInterpolatorSettings::~UAuracronPCGAttributeInterpolatorSettings() {}
// ********** End Class UAuracronPCGAttributeInterpolatorSettings **********************************

// ********** Begin Class UAuracronPCGAttributeFilterSettings **************************************
void UAuracronPCGAttributeFilterSettings::StaticRegisterNativesUAuracronPCGAttributeFilterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings;
UClass* UAuracronPCGAttributeFilterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeFilterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeFilterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeFilterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_NoRegister()
{
	return UAuracronPCGAttributeFilterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterMode_MetaData[] = {
		{ "Category", "Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filter settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filter settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeNames_MetaData[] = {
		{ "Category", "Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterPattern_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "FilterMode == EAuracronPCGAttributeFilterMode::IncludePattern || FilterMode == EAuracronPCGAttributeFilterMode::ExcludePattern" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByType_MetaData[] = {
		{ "Category", "Type Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Type filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedTypes_MetaData[] = {
		{ "Category", "Type Filter" },
		{ "EditCondition", "bFilterByType" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByTags_MetaData[] = {
		{ "Category", "Tag Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tag filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tag filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Tag Filter" },
		{ "EditCondition", "bFilterByTags" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExcludedTags_MetaData[] = {
		{ "Category", "Tag Filter" },
		{ "EditCondition", "bFilterByTags" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputFilteredAttributes_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveMetadata_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FilterMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FilterMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AttributeNames;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterPattern;
	static void NewProp_bFilterByType_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AllowedTypes_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AllowedTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedTypes;
	static void NewProp_bFilterByTags_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExcludedTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExcludedTags;
	static void NewProp_bOutputFilteredAttributes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputFilteredAttributes;
	static void NewProp_bPreserveMetadata_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveMetadata;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeFilterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterMode = { "FilterMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, FilterMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterMode_MetaData), NewProp_FilterMode_MetaData) }; // 3068241411
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AttributeNames_Inner = { "AttributeNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AttributeNames = { "AttributeNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, AttributeNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeNames_MetaData), NewProp_AttributeNames_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterPattern = { "FilterPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, FilterPattern), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterPattern_MetaData), NewProp_FilterPattern_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByType_SetBit(void* Obj)
{
	((UAuracronPCGAttributeFilterSettings*)Obj)->bFilterByType = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByType = { "bFilterByType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeFilterSettings), &Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByType_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByType_MetaData), NewProp_bFilterByType_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes_Inner = { "AllowedTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, METADATA_PARAMS(0, nullptr) }; // 4182054873
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes = { "AllowedTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, AllowedTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedTypes_MetaData), NewProp_AllowedTypes_MetaData) }; // 4182054873
void Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByTags_SetBit(void* Obj)
{
	((UAuracronPCGAttributeFilterSettings*)Obj)->bFilterByTags = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByTags = { "bFilterByTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeFilterSettings), &Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByTags_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByTags_MetaData), NewProp_bFilterByTags_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_RequiredTags_Inner = { "RequiredTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, RequiredTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_ExcludedTags_Inner = { "ExcludedTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_ExcludedTags = { "ExcludedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeFilterSettings, ExcludedTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExcludedTags_MetaData), NewProp_ExcludedTags_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bOutputFilteredAttributes_SetBit(void* Obj)
{
	((UAuracronPCGAttributeFilterSettings*)Obj)->bOutputFilteredAttributes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bOutputFilteredAttributes = { "bOutputFilteredAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeFilterSettings), &Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bOutputFilteredAttributes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputFilteredAttributes_MetaData), NewProp_bOutputFilteredAttributes_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bPreserveMetadata_SetBit(void* Obj)
{
	((UAuracronPCGAttributeFilterSettings*)Obj)->bPreserveMetadata = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bPreserveMetadata = { "bPreserveMetadata", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeFilterSettings), &Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bPreserveMetadata_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveMetadata_MetaData), NewProp_bPreserveMetadata_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AttributeNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AttributeNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_FilterPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_AllowedTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bFilterByTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_RequiredTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_ExcludedTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_ExcludedTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bOutputFilteredAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::NewProp_bPreserveMetadata,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeFilterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeFilterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeFilterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeFilterSettings);
UAuracronPCGAttributeFilterSettings::~UAuracronPCGAttributeFilterSettings() {}
// ********** End Class UAuracronPCGAttributeFilterSettings ****************************************

// ********** Begin Class UAuracronPCGAttributeValidatorSettings ***********************************
void UAuracronPCGAttributeValidatorSettings::StaticRegisterNativesUAuracronPCGAttributeValidatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings;
UClass* UAuracronPCGAttributeValidatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeValidatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeValidatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeValidatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_NoRegister()
{
	return UAuracronPCGAttributeValidatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationRules_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation rules\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStopOnFirstError_MetaData[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation behavior\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputValidationResults_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRemoveInvalidEntries_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogValidationErrors_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomValidation_MetaData[] = {
		{ "Category", "Custom Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomValidationExpression_MetaData[] = {
		{ "Category", "Custom Validation" },
		{ "EditCondition", "bUseCustomValidation" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationRules;
	static void NewProp_bStopOnFirstError_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStopOnFirstError;
	static void NewProp_bOutputValidationResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputValidationResults;
	static void NewProp_bRemoveInvalidEntries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRemoveInvalidEntries;
	static void NewProp_bLogValidationErrors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogValidationErrors;
	static void NewProp_bUseCustomValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomValidation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomValidationExpression;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeValidatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_ValidationRules_Inner = { "ValidationRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, METADATA_PARAMS(0, nullptr) }; // 890614519
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_ValidationRules = { "ValidationRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeValidatorSettings, ValidationRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationRules_MetaData), NewProp_ValidationRules_MetaData) }; // 890614519
void Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bStopOnFirstError_SetBit(void* Obj)
{
	((UAuracronPCGAttributeValidatorSettings*)Obj)->bStopOnFirstError = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bStopOnFirstError = { "bStopOnFirstError", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeValidatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bStopOnFirstError_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStopOnFirstError_MetaData), NewProp_bStopOnFirstError_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bOutputValidationResults_SetBit(void* Obj)
{
	((UAuracronPCGAttributeValidatorSettings*)Obj)->bOutputValidationResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bOutputValidationResults = { "bOutputValidationResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeValidatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bOutputValidationResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputValidationResults_MetaData), NewProp_bOutputValidationResults_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bRemoveInvalidEntries_SetBit(void* Obj)
{
	((UAuracronPCGAttributeValidatorSettings*)Obj)->bRemoveInvalidEntries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bRemoveInvalidEntries = { "bRemoveInvalidEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeValidatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bRemoveInvalidEntries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRemoveInvalidEntries_MetaData), NewProp_bRemoveInvalidEntries_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bLogValidationErrors_SetBit(void* Obj)
{
	((UAuracronPCGAttributeValidatorSettings*)Obj)->bLogValidationErrors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bLogValidationErrors = { "bLogValidationErrors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeValidatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bLogValidationErrors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogValidationErrors_MetaData), NewProp_bLogValidationErrors_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bUseCustomValidation_SetBit(void* Obj)
{
	((UAuracronPCGAttributeValidatorSettings*)Obj)->bUseCustomValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bUseCustomValidation = { "bUseCustomValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeValidatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bUseCustomValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomValidation_MetaData), NewProp_bUseCustomValidation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_CustomValidationExpression = { "CustomValidationExpression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeValidatorSettings, CustomValidationExpression), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomValidationExpression_MetaData), NewProp_CustomValidationExpression_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_ValidationRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_ValidationRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bStopOnFirstError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bOutputValidationResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bRemoveInvalidEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bLogValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_bUseCustomValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::NewProp_CustomValidationExpression,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeValidatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeValidatorSettings);
UAuracronPCGAttributeValidatorSettings::~UAuracronPCGAttributeValidatorSettings() {}
// ********** End Class UAuracronPCGAttributeValidatorSettings *************************************

// ********** Begin Class UAuracronPCGAttributeAggregatorSettings **********************************
void UAuracronPCGAttributeAggregatorSettings::StaticRegisterNativesUAuracronPCGAttributeAggregatorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings;
UClass* UAuracronPCGAttributeAggregatorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeAggregatorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeAggregatorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeAggregatorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_NoRegister()
{
	return UAuracronPCGAttributeAggregatorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AggregationOperations_MetaData[] = {
		{ "Category", "Aggregation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aggregation operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aggregation operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGrouping_MetaData[] = {
		{ "Category", "Grouping" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grouping\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grouping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupByAttributes_MetaData[] = {
		{ "Category", "Grouping" },
		{ "EditCondition", "bUseGrouping" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputGroupStatistics_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output options\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output options" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveOriginalData_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResultAttributePrefix_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AggregationOperations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AggregationOperations;
	static void NewProp_bUseGrouping_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGrouping;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupByAttributes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GroupByAttributes;
	static void NewProp_bOutputGroupStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputGroupStatistics;
	static void NewProp_bPreserveOriginalData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveOriginalData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResultAttributePrefix;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeAggregatorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_AggregationOperations_Inner = { "AggregationOperations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation, METADATA_PARAMS(0, nullptr) }; // 3751331859
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_AggregationOperations = { "AggregationOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeAggregatorSettings, AggregationOperations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AggregationOperations_MetaData), NewProp_AggregationOperations_MetaData) }; // 3751331859
void Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bUseGrouping_SetBit(void* Obj)
{
	((UAuracronPCGAttributeAggregatorSettings*)Obj)->bUseGrouping = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bUseGrouping = { "bUseGrouping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeAggregatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bUseGrouping_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGrouping_MetaData), NewProp_bUseGrouping_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_GroupByAttributes_Inner = { "GroupByAttributes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_GroupByAttributes = { "GroupByAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeAggregatorSettings, GroupByAttributes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupByAttributes_MetaData), NewProp_GroupByAttributes_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bOutputGroupStatistics_SetBit(void* Obj)
{
	((UAuracronPCGAttributeAggregatorSettings*)Obj)->bOutputGroupStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bOutputGroupStatistics = { "bOutputGroupStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeAggregatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bOutputGroupStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputGroupStatistics_MetaData), NewProp_bOutputGroupStatistics_MetaData) };
void Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bPreserveOriginalData_SetBit(void* Obj)
{
	((UAuracronPCGAttributeAggregatorSettings*)Obj)->bPreserveOriginalData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bPreserveOriginalData = { "bPreserveOriginalData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAttributeAggregatorSettings), &Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bPreserveOriginalData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveOriginalData_MetaData), NewProp_bPreserveOriginalData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_ResultAttributePrefix = { "ResultAttributePrefix", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAttributeAggregatorSettings, ResultAttributePrefix), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResultAttributePrefix_MetaData), NewProp_ResultAttributePrefix_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_AggregationOperations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_AggregationOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bUseGrouping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_GroupByAttributes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_GroupByAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bOutputGroupStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_bPreserveOriginalData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::NewProp_ResultAttributePrefix,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::ClassParams = {
	&UAuracronPCGAttributeAggregatorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeAggregatorSettings);
UAuracronPCGAttributeAggregatorSettings::~UAuracronPCGAttributeAggregatorSettings() {}
// ********** End Class UAuracronPCGAttributeAggregatorSettings ************************************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function AggregateAttribute *************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms
	{
		const UPCGMetadata* Metadata;
		FString AttributeName;
		EAuracronPCGAttributeAggregation Operation;
		FString Result;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Result;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeAggregation, METADATA_PARAMS(0, nullptr) }; // 3035535659
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms, Result), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "AggregateAttribute", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventAggregateAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execAggregateAttribute)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_ENUM(EAuracronPCGAttributeAggregation,Z_Param_Operation);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_Result);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::AggregateAttribute(Z_Param_Metadata,Z_Param_AttributeName,EAuracronPCGAttributeAggregation(Z_Param_Operation),Z_Param_Out_Result);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function AggregateAttribute ***************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function BatchCreateAttributes **********
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms
	{
		UPCGMetadata* Metadata;
		TArray<FAuracronPCGAttributeDescriptor> Descriptors;
		bool bOverwriteExisting;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch operations\n" },
#endif
		{ "CPP_Default_bOverwriteExisting", "false" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptors_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Descriptors;
	static void NewProp_bOverwriteExisting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOverwriteExisting;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Descriptors_Inner = { "Descriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, METADATA_PARAMS(0, nullptr) }; // 890614519
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Descriptors = { "Descriptors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms, Descriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptors_MetaData), NewProp_Descriptors_MetaData) }; // 890614519
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_bOverwriteExisting_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms*)Obj)->bOverwriteExisting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_bOverwriteExisting = { "bOverwriteExisting", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_bOverwriteExisting_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Descriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_Descriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_bOverwriteExisting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "BatchCreateAttributes", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::AuracronPCGAttributeSystemUtils_eventBatchCreateAttributes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execBatchCreateAttributes)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_TARRAY_REF(FAuracronPCGAttributeDescriptor,Z_Param_Out_Descriptors);
	P_GET_UBOOL(Z_Param_bOverwriteExisting);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::BatchCreateAttributes(Z_Param_Metadata,Z_Param_Out_Descriptors,Z_Param_bOverwriteExisting);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function BatchCreateAttributes ************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function BatchModifyAttributes **********
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms
	{
		UPCGMetadata* Metadata;
		TArray<FAuracronPCGAttributeOperation> Operations;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Operations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Operations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Operations;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Operations_Inner = { "Operations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation, METADATA_PARAMS(0, nullptr) }; // 3751331859
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Operations = { "Operations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms, Operations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Operations_MetaData), NewProp_Operations_MetaData) }; // 3751331859
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Operations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_Operations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "BatchModifyAttributes", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::AuracronPCGAttributeSystemUtils_eventBatchModifyAttributes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execBatchModifyAttributes)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_TARRAY_REF(FAuracronPCGAttributeOperation,Z_Param_Out_Operations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::BatchModifyAttributes(Z_Param_Metadata,Z_Param_Out_Operations);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function BatchModifyAttributes ************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function CopyAttribute ******************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms
	{
		UPCGMetadata* SourceMetadata;
		UPCGMetadata* TargetMetadata;
		FString AttributeName;
		FString NewAttributeName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "CPP_Default_NewAttributeName", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewAttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMetadata;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMetadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewAttributeName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_SourceMetadata = { "SourceMetadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms, SourceMetadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_TargetMetadata = { "TargetMetadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms, TargetMetadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_NewAttributeName = { "NewAttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms, NewAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewAttributeName_MetaData), NewProp_NewAttributeName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_SourceMetadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_TargetMetadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_NewAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "CopyAttribute", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::AuracronPCGAttributeSystemUtils_eventCopyAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execCopyAttribute)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_SourceMetadata);
	P_GET_OBJECT(UPCGMetadata,Z_Param_TargetMetadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FStrProperty,Z_Param_NewAttributeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::CopyAttribute(Z_Param_SourceMetadata,Z_Param_TargetMetadata,Z_Param_AttributeName,Z_Param_NewAttributeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function CopyAttribute ********************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function CreateAttribute ****************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms
	{
		UPCGMetadata* Metadata;
		FAuracronPCGAttributeDescriptor Descriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute creation and management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 890614519
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "CreateAttribute", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventCreateAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execCreateAttribute)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_STRUCT_REF(FAuracronPCGAttributeDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::CreateAttribute(Z_Param_Metadata,Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function CreateAttribute ******************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function FilterAttributesByPattern ******
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms
	{
		const UPCGMetadata* Metadata;
		FString Pattern;
		EAuracronPCGAttributeFilterMode FilterMode;
		TArray<FString> FilteredAttributes;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FilterMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FilterMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilteredAttributes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FilteredAttributes;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms, Pattern), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilterMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilterMode = { "FilterMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms, FilterMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeFilterMode, METADATA_PARAMS(0, nullptr) }; // 3068241411
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilteredAttributes_Inner = { "FilteredAttributes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilteredAttributes = { "FilteredAttributes", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms, FilteredAttributes), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilterMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilterMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilteredAttributes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_FilteredAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "FilterAttributesByPattern", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::AuracronPCGAttributeSystemUtils_eventFilterAttributesByPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execFilterAttributesByPattern)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_Pattern);
	P_GET_ENUM(EAuracronPCGAttributeFilterMode,Z_Param_FilterMode);
	P_GET_TARRAY_REF(FString,Z_Param_Out_FilteredAttributes);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::FilterAttributesByPattern(Z_Param_Metadata,Z_Param_Pattern,EAuracronPCGAttributeFilterMode(Z_Param_FilterMode),Z_Param_Out_FilteredAttributes);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function FilterAttributesByPattern ********

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function GetAttributeEntryCount *********
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms
	{
		const UPCGMetadata* Metadata;
		FString AttributeName;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "GetAttributeEntryCount", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeEntryCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execGetAttributeEntryCount)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::GetAttributeEntryCount(Z_Param_Metadata,Z_Param_AttributeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function GetAttributeEntryCount ***********

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function GetAttributeNames **************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms
	{
		const UPCGMetadata* Metadata;
		EAuracronPCGAttributeType TypeFilter;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute analysis\n" },
#endif
		{ "CPP_Default_TypeFilter", "Float" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TypeFilter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TypeFilter;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_TypeFilter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_TypeFilter = { "TypeFilter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms, TypeFilter), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, METADATA_PARAMS(0, nullptr) }; // 4182054873
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_TypeFilter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_TypeFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "GetAttributeNames", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeNames_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execGetAttributeNames)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_ENUM(EAuracronPCGAttributeType,Z_Param_TypeFilter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::GetAttributeNames(Z_Param_Metadata,EAuracronPCGAttributeType(Z_Param_TypeFilter));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function GetAttributeNames ****************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function GetAttributeType ***************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms
	{
		const UPCGMetadata* Metadata;
		FString AttributeName;
		EAuracronPCGAttributeType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms, ReturnValue), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeType, METADATA_PARAMS(0, nullptr) }; // 4182054873
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "GetAttributeType", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execGetAttributeType)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPCGAttributeType*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::GetAttributeType(Z_Param_Metadata,Z_Param_AttributeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function GetAttributeType *****************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function GetAttributeValue **************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms
	{
		const UPCGMetadata* Metadata;
		FString AttributeName;
		int32 EntryIndex;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EntryIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_EntryIndex = { "EntryIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms, EntryIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_EntryIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "GetAttributeValue", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::AuracronPCGAttributeSystemUtils_eventGetAttributeValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execGetAttributeValue)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FIntProperty,Z_Param_EntryIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::GetAttributeValue(Z_Param_Metadata,Z_Param_AttributeName,Z_Param_EntryIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function GetAttributeValue ****************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function InterpolateAttributeValues *****
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms
	{
		FString ValueA;
		FString ValueB;
		float Alpha;
		EAuracronPCGAttributeInterpolation Method;
		FString Result;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValueA;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValueB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Alpha;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Method_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Method;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Result;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ValueA = { "ValueA", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms, ValueA), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueA_MetaData), NewProp_ValueA_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ValueB = { "ValueB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms, ValueB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueB_MetaData), NewProp_ValueB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Alpha = { "Alpha", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms, Alpha), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Method_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Method = { "Method", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms, Method), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAttributeInterpolation, METADATA_PARAMS(0, nullptr) }; // 1837227816
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms, Result), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ValueA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ValueB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Alpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Method_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Method,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "InterpolateAttributeValues", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::AuracronPCGAttributeSystemUtils_eventInterpolateAttributeValues_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execInterpolateAttributeValues)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValueA);
	P_GET_PROPERTY(FStrProperty,Z_Param_ValueB);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Alpha);
	P_GET_ENUM(EAuracronPCGAttributeInterpolation,Z_Param_Method);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_Result);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::InterpolateAttributeValues(Z_Param_ValueA,Z_Param_ValueB,Z_Param_Alpha,EAuracronPCGAttributeInterpolation(Z_Param_Method),Z_Param_Out_Result);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function InterpolateAttributeValues *******

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function SetAttributeValue **************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms
	{
		UPCGMetadata* Metadata;
		FString AttributeName;
		int32 EntryIndex;
		FString Value;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute value operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute value operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EntryIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Value;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_EntryIndex = { "EntryIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms, EntryIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_EntryIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "SetAttributeValue", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::AuracronPCGAttributeSystemUtils_eventSetAttributeValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execSetAttributeValue)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FIntProperty,Z_Param_EntryIndex);
	P_GET_PROPERTY(FStrProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::SetAttributeValue(Z_Param_Metadata,Z_Param_AttributeName,Z_Param_EntryIndex,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function SetAttributeValue ****************

// ********** Begin Class UAuracronPCGAttributeSystemUtils Function ValidateAttribute **************
struct Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics
{
	struct AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms
	{
		const UPCGMetadata* Metadata;
		FAuracronPCGAttributeDescriptor Descriptor;
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Attribute System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 890614519
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms), &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, nullptr, "ValidateAttribute", Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::AuracronPCGAttributeSystemUtils_eventValidateAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGAttributeSystemUtils::execValidateAttribute)
{
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_STRUCT_REF(FAuracronPCGAttributeDescriptor,Z_Param_Out_Descriptor);
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGAttributeSystemUtils::ValidateAttribute(Z_Param_Metadata,Z_Param_Out_Descriptor,Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGAttributeSystemUtils Function ValidateAttribute ****************

// ********** Begin Class UAuracronPCGAttributeSystemUtils *****************************************
void UAuracronPCGAttributeSystemUtils::StaticRegisterNativesUAuracronPCGAttributeSystemUtils()
{
	UClass* Class = UAuracronPCGAttributeSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AggregateAttribute", &UAuracronPCGAttributeSystemUtils::execAggregateAttribute },
		{ "BatchCreateAttributes", &UAuracronPCGAttributeSystemUtils::execBatchCreateAttributes },
		{ "BatchModifyAttributes", &UAuracronPCGAttributeSystemUtils::execBatchModifyAttributes },
		{ "CopyAttribute", &UAuracronPCGAttributeSystemUtils::execCopyAttribute },
		{ "CreateAttribute", &UAuracronPCGAttributeSystemUtils::execCreateAttribute },
		{ "FilterAttributesByPattern", &UAuracronPCGAttributeSystemUtils::execFilterAttributesByPattern },
		{ "GetAttributeEntryCount", &UAuracronPCGAttributeSystemUtils::execGetAttributeEntryCount },
		{ "GetAttributeNames", &UAuracronPCGAttributeSystemUtils::execGetAttributeNames },
		{ "GetAttributeType", &UAuracronPCGAttributeSystemUtils::execGetAttributeType },
		{ "GetAttributeValue", &UAuracronPCGAttributeSystemUtils::execGetAttributeValue },
		{ "InterpolateAttributeValues", &UAuracronPCGAttributeSystemUtils::execInterpolateAttributeValues },
		{ "SetAttributeValue", &UAuracronPCGAttributeSystemUtils::execSetAttributeValue },
		{ "ValidateAttribute", &UAuracronPCGAttributeSystemUtils::execValidateAttribute },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils;
UClass* UAuracronPCGAttributeSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAttributeSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAttributeSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAttributeSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_NoRegister()
{
	return UAuracronPCGAttributeSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Attribute System Utilities\n * Utility functions for advanced attribute operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGAttributeSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAttributeSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute System Utilities\nUtility functions for advanced attribute operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_AggregateAttribute, "AggregateAttribute" }, // 3354098312
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchCreateAttributes, "BatchCreateAttributes" }, // 4225328927
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_BatchModifyAttributes, "BatchModifyAttributes" }, // 2122486592
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CopyAttribute, "CopyAttribute" }, // 773074812
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_CreateAttribute, "CreateAttribute" }, // 223218813
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_FilterAttributesByPattern, "FilterAttributesByPattern" }, // 2559574870
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeEntryCount, "GetAttributeEntryCount" }, // 2654294816
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeNames, "GetAttributeNames" }, // 313706207
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeType, "GetAttributeType" }, // 3097821684
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_GetAttributeValue, "GetAttributeValue" }, // 2355348121
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_InterpolateAttributeValues, "InterpolateAttributeValues" }, // 328854444
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_SetAttributeValue, "SetAttributeValue" }, // 3379954207
		{ &Z_Construct_UFunction_UAuracronPCGAttributeSystemUtils_ValidateAttribute, "ValidateAttribute" }, // 3221758882
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAttributeSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::ClassParams = {
	&UAuracronPCGAttributeSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAttributeSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGAttributeSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils.OuterSingleton;
}
UAuracronPCGAttributeSystemUtils::UAuracronPCGAttributeSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAttributeSystemUtils);
UAuracronPCGAttributeSystemUtils::~UAuracronPCGAttributeSystemUtils() {}
// ********** End Class UAuracronPCGAttributeSystemUtils *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGAttributeType_StaticEnum, TEXT("EAuracronPCGAttributeType"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4182054873U) },
		{ EAuracronPCGAttributeInterpolation_StaticEnum, TEXT("EAuracronPCGAttributeInterpolation"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeInterpolation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1837227816U) },
		{ EAuracronPCGAttributeFilterMode_StaticEnum, TEXT("EAuracronPCGAttributeFilterMode"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeFilterMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3068241411U) },
		{ EAuracronPCGAttributeValidation_StaticEnum, TEXT("EAuracronPCGAttributeValidation"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeValidation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 828365112U) },
		{ EAuracronPCGAttributeAggregation_StaticEnum, TEXT("EAuracronPCGAttributeAggregation"), &Z_Registration_Info_UEnum_EAuracronPCGAttributeAggregation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3035535659U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGAttributeDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGAttributeDescriptor_Statics::NewStructOps, TEXT("AuracronPCGAttributeDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGAttributeDescriptor), 890614519U) },
		{ FAuracronPCGAttributeOperation::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGAttributeOperation_Statics::NewStructOps, TEXT("AuracronPCGAttributeOperation"), &Z_Registration_Info_UScriptStruct_FAuracronPCGAttributeOperation, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGAttributeOperation), 3751331859U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAttributeCreatorSettings, UAuracronPCGAttributeCreatorSettings::StaticClass, TEXT("UAuracronPCGAttributeCreatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeCreatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeCreatorSettings), 88817065U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeModifierSettings, UAuracronPCGAttributeModifierSettings::StaticClass, TEXT("UAuracronPCGAttributeModifierSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeModifierSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeModifierSettings), 3195773677U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeInterpolatorSettings, UAuracronPCGAttributeInterpolatorSettings::StaticClass, TEXT("UAuracronPCGAttributeInterpolatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeInterpolatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeInterpolatorSettings), 1371840526U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeFilterSettings, UAuracronPCGAttributeFilterSettings::StaticClass, TEXT("UAuracronPCGAttributeFilterSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeFilterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeFilterSettings), 2376771036U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeValidatorSettings, UAuracronPCGAttributeValidatorSettings::StaticClass, TEXT("UAuracronPCGAttributeValidatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeValidatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeValidatorSettings), 102250606U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeAggregatorSettings, UAuracronPCGAttributeAggregatorSettings::StaticClass, TEXT("UAuracronPCGAttributeAggregatorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAttributeAggregatorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeAggregatorSettings), 324978232U) },
		{ Z_Construct_UClass_UAuracronPCGAttributeSystemUtils, UAuracronPCGAttributeSystemUtils::StaticClass, TEXT("UAuracronPCGAttributeSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGAttributeSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAttributeSystemUtils), 3795230241U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_2172450926(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAttributeSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
