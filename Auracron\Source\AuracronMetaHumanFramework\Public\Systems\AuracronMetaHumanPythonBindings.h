﻿// AURACRON MetaHuman Python Bindings - Python Integration for UE 5.6
// Provides Python API access to all MetaHuman Framework functionality
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "PythonScriptPlugin.h"
#include "PyWrapperBase.h"
#include "PyWrapperObject.h"
#include "PyWrapperStruct.h"
#include "PyWrapperEnum.h"
#include "PyWrapperDelegate.h"
#include "PyWrapperArray.h"
#include "PyWrapperMap.h"
#include "PyWrapperSet.h"
#include "PyWrapperText.h"
#include "PyWrapperName.h"
#include "PyWrapperTypeRegistry.h"
#include "Async/AsyncWork.h"

#include "AuracronMetaHumanPythonBindings.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanPython, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;

/**
 * Python API Categories
 */
UENUM(BlueprintType)
enum class EAuracronPythonAPICategory : uint8
{
    Core                UMETA(DisplayName = "Core Framework"),
    DNA                 UMETA(DisplayName = "DNA System"),
    Animation           UMETA(DisplayName = "Animation System"),
    Texture             UMETA(DisplayName = "Texture System"),
    Performance         UMETA(DisplayName = "Performance System"),
    Clothing            UMETA(DisplayName = "Clothing System"),
    Utility             UMETA(DisplayName = "Utility Functions"),
    All                 UMETA(DisplayName = "All APIs")
};

/**
 * Python Script Execution Result
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronPythonResult
{
    GENERATED_BODY()

    /** Whether the execution was successful */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    /** Result message */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Message;

    /** Python output */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Output;

    /** Python error output */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorOutput;

    /** Execution time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ExecutionTimeMS = 0.0f;

    /** Return value as JSON string */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ReturnValue;

    FAuracronPythonResult()
    {
        // Default constructor
    }

    FAuracronPythonResult(bool bInSuccess, const FString& InMessage)
        : bSuccess(bInSuccess)
        , Message(InMessage)
    {
    }
};

/**
 * Python Script Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronPythonScriptConfig
{
    GENERATED_BODY()

    /** Script name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script")
    FString ScriptName;

    /** Script content */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script")
    FString ScriptContent;

    /** Script file path (alternative to ScriptContent) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script")
    FString ScriptFilePath;

    /** Script arguments */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script")
    TMap<FString, FString> Arguments;

    /** Enable debug output */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script")
    bool bEnableDebugOutput = false;

    /** Script timeout in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Script", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float TimeoutSeconds = 30.0f;

    FAuracronPythonScriptConfig()
    {
        // Default constructor
    }
};

/**
 * Python API Documentation Entry
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronPythonAPIDoc
{
    GENERATED_BODY()

    /** Function name */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    FString FunctionName;

    /** Function description */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    FString Description;

    /** Function signature */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    FString Signature;

    /** Parameters documentation */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    TArray<FString> Parameters;

    /** Return value documentation */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    FString ReturnValue;

    /** Usage examples */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    TArray<FString> Examples;

    /** API category */
    UPROPERTY(BlueprintReadOnly, Category = "Documentation")
    EAuracronPythonAPICategory Category = EAuracronPythonAPICategory::Core;

    FAuracronPythonAPIDoc()
    {
        // Default constructor
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronPythonScriptComplete, const FAuracronPythonResult&, Result, const FString&, ScriptID);

/**
 * AURACRON MetaHuman Python Bindings
 * Comprehensive Python integration for MetaHuman Framework
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|Python")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanPythonBindings : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanPythonBindings();
    virtual ~UAuracronMetaHumanPythonBindings();

    // === Core Python Operations ===

    /**
     * Execute Python script
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Core", CallInEditor)
    FAuracronPythonResult ExecutePythonScript(const FAuracronPythonScriptConfig& Config);

    /**
     * Execute Python code string
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Core", CallInEditor)
    FAuracronPythonResult ExecutePythonCode(const FString& PythonCode);

    /**
     * Execute Python file
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Core", CallInEditor)
    FAuracronPythonResult ExecutePythonFile(const FString& FilePath);

    /**
     * Evaluate Python expression
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Core", CallInEditor)
    FAuracronPythonResult EvaluatePythonExpression(const FString& Expression);

    // === Async Python Operations ===

    /**
     * Execute Python script asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Async", CallInEditor)
    FString ExecutePythonScriptAsync(const FAuracronPythonScriptConfig& Config);

    /**
     * Execute Python code asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Async", CallInEditor)
    FString ExecutePythonCodeAsync(const FString& PythonCode);

    /**
     * Check if async execution is complete
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Async", CallInEditor)
    bool IsAsyncExecutionComplete(const FString& ScriptID) const;

    /**
     * Get async execution result
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Async", CallInEditor)
    FAuracronPythonResult GetAsyncExecutionResult(const FString& ScriptID);

    // === Python Environment Management ===

    /**
     * Initialize Python environment
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Environment", CallInEditor)
    bool InitializePythonEnvironment();

    /**
     * Shutdown Python environment
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Environment", CallInEditor)
    void ShutdownPythonEnvironment();

    /**
     * Import Python module
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Environment", CallInEditor)
    bool ImportPythonModule(const FString& ModuleName);

    /**
     * Install Python package
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Environment", CallInEditor)
    FAuracronPythonResult InstallPythonPackage(const FString& PackageName);

    /**
     * List installed Python packages
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Environment", CallInEditor)
    TArray<FString> ListInstalledPythonPackages();

    // === API Bindings ===

    /**
     * Register MetaHuman Framework APIs with Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Bindings", CallInEditor)
    bool RegisterMetaHumanAPIs(EAuracronPythonAPICategory Category = EAuracronPythonAPICategory::All);

    /**
     * Unregister MetaHuman Framework APIs from Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Bindings", CallInEditor)
    void UnregisterMetaHumanAPIs();

    /**
     * Get available API functions
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Bindings", CallInEditor)
    TArray<FString> GetAvailableAPIFunctions(EAuracronPythonAPICategory Category = EAuracronPythonAPICategory::All);

    /**
     * Get API documentation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Bindings", CallInEditor)
    TArray<FAuracronPythonAPIDoc> GetAPIDocumentation(EAuracronPythonAPICategory Category = EAuracronPythonAPICategory::All);

    // === Data Exchange ===

    /**
     * Set Python variable
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Data", CallInEditor)
    bool SetPythonVariable(const FString& VariableName, const FString& Value);

    /**
     * Get Python variable
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Data", CallInEditor)
    FString GetPythonVariable(const FString& VariableName);

    /**
     * Convert UE object to Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Data", CallInEditor)
    bool ConvertUEObjectToPython(UObject* Object, const FString& PythonVariableName);

    /**
     * Convert Python object to UE
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Data", CallInEditor)
    UObject* ConvertPythonObjectToUE(const FString& PythonVariableName, UClass* TargetClass);

    // === Script Management ===

    /**
     * Load Python script from file
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Scripts", CallInEditor)
    FString LoadPythonScriptFromFile(const FString& FilePath);

    /**
     * Save Python script to file
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Scripts", CallInEditor)
    bool SavePythonScriptToFile(const FString& ScriptContent, const FString& FilePath);

    /**
     * Validate Python script syntax
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Scripts", CallInEditor)
    FAuracronPythonResult ValidatePythonScript(const FString& ScriptContent);

    /**
     * Get Python script templates
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Scripts", CallInEditor)
    TMap<FString, FString> GetPythonScriptTemplates();

    // === Debugging and Profiling ===

    /**
     * Enable Python debugging
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Debug", CallInEditor)
    bool EnablePythonDebugging(bool bEnable);

    /**
     * Set Python breakpoint
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Debug", CallInEditor)
    bool SetPythonBreakpoint(const FString& FilePath, int32 LineNumber);

    /**
     * Profile Python script execution
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Debug", CallInEditor)
    FAuracronPythonResult ProfilePythonScript(const FAuracronPythonScriptConfig& Config);

    /**
     * Get Python execution statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Debug", CallInEditor)
    FString GetPythonExecutionStatistics();

    // === Utility Functions ===

    /**
     * Initialize Python bindings
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Utility", CallInEditor)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown Python bindings
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Utility", CallInEditor)
    void Shutdown();

    /**
     * Check if Python bindings are initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Utility", CallInEditor)
    bool IsInitialized() const;

    /**
     * Get Python version
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Utility", CallInEditor)
    FString GetPythonVersion();

    /**
     * Get Python system information
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Python|Utility", CallInEditor)
    TMap<FString, FString> GetPythonSystemInfo();

public:
    // === Events ===

    /** Called when an async Python script execution completes */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Python|Events")
    FAuracronPythonScriptComplete OnPythonScriptComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    bool bPythonEnvironmentInitialized = false;

    UPROPERTY()
    FString LastErrorMessage;

    // Async execution tracking
    TMap<FString, TSharedPtr<class FAuracronPythonAsyncTask>> ActiveAsyncExecutions;
    FCriticalSection AsyncExecutionsMutex;

    // Python API registry
    TMap<EAuracronPythonAPICategory, TArray<FAuracronPythonAPIDoc>> APIDocumentation;

    // Script templates
    TMap<FString, FString> ScriptTemplates;

private:
    // === Internal Methods ===

    FString GenerateScriptID() const;
    void CleanupCompletedAsyncExecutions();
    bool ValidateScriptConfig(const FAuracronPythonScriptConfig& Config, FString& OutError) const;
    FAuracronPythonResult ExecutePythonInternal(const FString& Code, const TMap<FString, FString>& Arguments);
    bool RegisterCoreAPIs();
    bool RegisterDNAAPIs();
    bool RegisterAnimationAPIs();
    bool RegisterTextureAPIs();
    bool RegisterPerformanceAPIs();
    bool RegisterClothingAPIs();
    void InitializeScriptTemplates();
    void InitializeAPIDocumentation();
};

