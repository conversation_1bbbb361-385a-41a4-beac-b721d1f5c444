// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMetaHumanFramework.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanFramework_generated_h
#error "AuracronMetaHumanFramework.generated.h already included, missing '#pragma once' in AuracronMetaHumanFramework.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanFramework_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronMetaHumanAnimationSystem;
class UAuracronMetaHumanClothingSystem;
class UAuracronMetaHumanDNASystem;
class UAuracronMetaHumanPerformanceSystem;
class UAuracronMetaHumanPythonBindings;
class UAuracronMetaHumanTextureSystem;
struct FAuracronMetaHumanConfig;
struct FAuracronMetaHumanMetrics;

// ********** Begin ScriptStruct FAuracronMetaHumanConfig ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_66_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMetaHumanConfig;
// ********** End ScriptStruct FAuracronMetaHumanConfig ********************************************

// ********** Begin ScriptStruct FAuracronMetaHumanMetrics *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMetaHumanMetrics;
// ********** End ScriptStruct FAuracronMetaHumanMetrics *******************************************

// ********** Begin Class UAuracronMetaHumanFramework **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateSystemRequirements); \
	DECLARE_FUNCTION(execGetSupportedSDKVersion); \
	DECLARE_FUNCTION(execGetFrameworkVersion); \
	DECLARE_FUNCTION(execResetPerformanceMetrics); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceMetrics); \
	DECLARE_FUNCTION(execGetPythonBindings); \
	DECLARE_FUNCTION(execGetClothingSystem); \
	DECLARE_FUNCTION(execGetPerformanceSystem); \
	DECLARE_FUNCTION(execGetTextureSystem); \
	DECLARE_FUNCTION(execGetAnimationSystem); \
	DECLARE_FUNCTION(execGetDNASystem); \
	DECLARE_FUNCTION(execUpdateFrameworkConfiguration); \
	DECLARE_FUNCTION(execGetFrameworkConfiguration); \
	DECLARE_FUNCTION(execIsFrameworkInitialized); \
	DECLARE_FUNCTION(execShutdownFramework); \
	DECLARE_FUNCTION(execInitializeFramework);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanFramework(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanFramework_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanFramework, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanFramework)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanFramework(UAuracronMetaHumanFramework&&) = delete; \
	UAuracronMetaHumanFramework(const UAuracronMetaHumanFramework&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanFramework); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanFramework); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanFramework)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_152_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h_155_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanFramework;

// ********** End Class UAuracronMetaHumanFramework ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h

// ********** Begin Enum EAuracronMetaHumanQuality *************************************************
#define FOREACH_ENUM_EAURACRONMETAHUMANQUALITY(op) \
	op(EAuracronMetaHumanQuality::Low) \
	op(EAuracronMetaHumanQuality::Medium) \
	op(EAuracronMetaHumanQuality::High) \
	op(EAuracronMetaHumanQuality::Cinematic) \
	op(EAuracronMetaHumanQuality::Custom) 

enum class EAuracronMetaHumanQuality : uint8;
template<> struct TIsUEnumClass<EAuracronMetaHumanQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanQuality>();
// ********** End Enum EAuracronMetaHumanQuality ***************************************************

// ********** Begin Enum EAuracronMetaHumanPriority ************************************************
#define FOREACH_ENUM_EAURACRONMETAHUMANPRIORITY(op) \
	op(EAuracronMetaHumanPriority::Background) \
	op(EAuracronMetaHumanPriority::Low) \
	op(EAuracronMetaHumanPriority::Normal) \
	op(EAuracronMetaHumanPriority::High) \
	op(EAuracronMetaHumanPriority::Critical) 

enum class EAuracronMetaHumanPriority : uint8;
template<> struct TIsUEnumClass<EAuracronMetaHumanPriority> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanPriority>();
// ********** End Enum EAuracronMetaHumanPriority **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
