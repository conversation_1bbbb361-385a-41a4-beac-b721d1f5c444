// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/AuracronMetaHumanFramework.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanFramework() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMetaHumanConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronMetaHumanQuality *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMetaHumanQuality;
static UEnum* EAuracronMetaHumanQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronMetaHumanQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanQuality>()
{
	return EAuracronMetaHumanQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EAuracronMetaHumanQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MetaHuman Framework Quality Levels\n */" },
#endif
		{ "Custom.DisplayName", "Custom Quality" },
		{ "Custom.Name", "EAuracronMetaHumanQuality::Custom" },
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EAuracronMetaHumanQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EAuracronMetaHumanQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EAuracronMetaHumanQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MetaHuman Framework Quality Levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMetaHumanQuality::Low", (int64)EAuracronMetaHumanQuality::Low },
		{ "EAuracronMetaHumanQuality::Medium", (int64)EAuracronMetaHumanQuality::Medium },
		{ "EAuracronMetaHumanQuality::High", (int64)EAuracronMetaHumanQuality::High },
		{ "EAuracronMetaHumanQuality::Cinematic", (int64)EAuracronMetaHumanQuality::Cinematic },
		{ "EAuracronMetaHumanQuality::Custom", (int64)EAuracronMetaHumanQuality::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronMetaHumanQuality",
	"EAuracronMetaHumanQuality",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanQuality.InnerSingleton;
}
// ********** End Enum EAuracronMetaHumanQuality ***************************************************

// ********** Begin Enum EAuracronMetaHumanPriority ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronMetaHumanPriority;
static UEnum* EAuracronMetaHumanPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronMetaHumanPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanPriority>()
{
	return EAuracronMetaHumanPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Background.DisplayName", "Background" },
		{ "Background.Name", "EAuracronMetaHumanPriority::Background" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MetaHuman Framework Processing Priority\n */" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronMetaHumanPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronMetaHumanPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronMetaHumanPriority::Low" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronMetaHumanPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MetaHuman Framework Processing Priority" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronMetaHumanPriority::Background", (int64)EAuracronMetaHumanPriority::Background },
		{ "EAuracronMetaHumanPriority::Low", (int64)EAuracronMetaHumanPriority::Low },
		{ "EAuracronMetaHumanPriority::Normal", (int64)EAuracronMetaHumanPriority::Normal },
		{ "EAuracronMetaHumanPriority::High", (int64)EAuracronMetaHumanPriority::High },
		{ "EAuracronMetaHumanPriority::Critical", (int64)EAuracronMetaHumanPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronMetaHumanPriority",
	"EAuracronMetaHumanPriority",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronMetaHumanPriority.InnerSingleton;
}
// ********** End Enum EAuracronMetaHumanPriority **************************************************

// ********** Begin ScriptStruct FAuracronMetaHumanConfig ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig;
class UScriptStruct* FAuracronMetaHumanConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMetaHumanConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronMetaHumanConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MetaHuman Framework Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MetaHuman Framework Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUAcceleration_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable GPU acceleration for processing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable GPU acceleration for processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMultiThreading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable multi-threading for operations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable multi-threading for operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWorkerThreads_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "32" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum number of worker threads */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum number of worker threads" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryPoolSizeMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ClampMax", "8192" },
		{ "ClampMin", "64" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory pool size in MB */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory pool size in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable detailed logging */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable detailed logging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceProfiling_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance profiling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance profiling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultQuality_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default quality level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default quality level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPriority_MetaData[] = {
		{ "Category", "Processing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default processing priority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default processing priority" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUAcceleration;
	static void NewProp_bEnableMultiThreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMultiThreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxWorkerThreads;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryPoolSizeMB;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bEnablePerformanceProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceProfiling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultQuality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMetaHumanConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableGPUAcceleration_SetBit(void* Obj)
{
	((FAuracronMetaHumanConfig*)Obj)->bEnableGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableGPUAcceleration = { "bEnableGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMetaHumanConfig), &Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUAcceleration_MetaData), NewProp_bEnableGPUAcceleration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableMultiThreading_SetBit(void* Obj)
{
	((FAuracronMetaHumanConfig*)Obj)->bEnableMultiThreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableMultiThreading = { "bEnableMultiThreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMetaHumanConfig), &Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableMultiThreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMultiThreading_MetaData), NewProp_bEnableMultiThreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_MaxWorkerThreads = { "MaxWorkerThreads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanConfig, MaxWorkerThreads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWorkerThreads_MetaData), NewProp_MaxWorkerThreads_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_MemoryPoolSizeMB = { "MemoryPoolSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanConfig, MemoryPoolSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryPoolSizeMB_MetaData), NewProp_MemoryPoolSizeMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((FAuracronMetaHumanConfig*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMetaHumanConfig), &Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnablePerformanceProfiling_SetBit(void* Obj)
{
	((FAuracronMetaHumanConfig*)Obj)->bEnablePerformanceProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnablePerformanceProfiling = { "bEnablePerformanceProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMetaHumanConfig), &Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnablePerformanceProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceProfiling_MetaData), NewProp_bEnablePerformanceProfiling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultQuality = { "DefaultQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanConfig, DefaultQuality), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultQuality_MetaData), NewProp_DefaultQuality_MetaData) }; // 2631883390
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultPriority = { "DefaultPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanConfig, DefaultPriority), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronMetaHumanPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPriority_MetaData), NewProp_DefaultPriority_MetaData) }; // 3618240919
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableMultiThreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_MaxWorkerThreads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_MemoryPoolSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_bEnablePerformanceProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewProp_DefaultPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronMetaHumanConfig",
	Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::PropPointers),
	sizeof(FAuracronMetaHumanConfig),
	alignof(FAuracronMetaHumanConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMetaHumanConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMetaHumanConfig ********************************************

// ********** Begin ScriptStruct FAuracronMetaHumanMetrics *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics;
class UScriptStruct* FAuracronMetaHumanMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronMetaHumanMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MetaHuman Framework Performance Metrics\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MetaHuman Framework Performance Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalProcessingTimeMS_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total processing time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total processing time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory usage in MB */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory usage in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUsagePercent_MetaData[] = {
		{ "Category", "GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GPU usage percentage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU usage percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTasks_MetaData[] = {
		{ "Category", "Tasks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of active tasks */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of active tasks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedTasks_MetaData[] = {
		{ "Category", "Tasks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of completed tasks */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of completed tasks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheHitRatio_MetaData[] = {
		{ "Category", "Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache hit ratio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache hit ratio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last update timestamp */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last update timestamp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalProcessingTimeMS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUsagePercent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveTasks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedTasks;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CacheHitRatio;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMetaHumanMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_TotalProcessingTimeMS = { "TotalProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, TotalProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalProcessingTimeMS_MetaData), NewProp_TotalProcessingTimeMS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_GPUUsagePercent = { "GPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, GPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUsagePercent_MetaData), NewProp_GPUUsagePercent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_ActiveTasks = { "ActiveTasks", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, ActiveTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTasks_MetaData), NewProp_ActiveTasks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_CompletedTasks = { "CompletedTasks", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, CompletedTasks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedTasks_MetaData), NewProp_CompletedTasks_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_CacheHitRatio = { "CacheHitRatio", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, CacheHitRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheHitRatio_MetaData), NewProp_CacheHitRatio_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMetaHumanMetrics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_TotalProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_GPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_ActiveTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_CompletedTasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_CacheHitRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronMetaHumanMetrics",
	Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::PropPointers),
	sizeof(FAuracronMetaHumanMetrics),
	alignof(FAuracronMetaHumanMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMetaHumanMetrics *******************************************

// ********** Begin Class UAuracronMetaHumanFramework Function GetAnimationSystem ******************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics
{
	struct AuracronMetaHumanFramework_eventGetAnimationSystem_Parms
	{
		UAuracronMetaHumanAnimationSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Animation System instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Animation System instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetAnimationSystem_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetAnimationSystem", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::AuracronMetaHumanFramework_eventGetAnimationSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::AuracronMetaHumanFramework_eventGetAnimationSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetAnimationSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanAnimationSystem**)Z_Param__Result=P_THIS->GetAnimationSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetAnimationSystem ********************

// ********** Begin Class UAuracronMetaHumanFramework Function GetClothingSystem *******************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics
{
	struct AuracronMetaHumanFramework_eventGetClothingSystem_Parms
	{
		UAuracronMetaHumanClothingSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Clothing System instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Clothing System instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetClothingSystem_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetClothingSystem", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::AuracronMetaHumanFramework_eventGetClothingSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::AuracronMetaHumanFramework_eventGetClothingSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetClothingSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanClothingSystem**)Z_Param__Result=P_THIS->GetClothingSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetClothingSystem *********************

// ********** Begin Class UAuracronMetaHumanFramework Function GetDNASystem ************************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics
{
	struct AuracronMetaHumanFramework_eventGetDNASystem_Parms
	{
		UAuracronMetaHumanDNASystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get DNA System instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get DNA System instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetDNASystem_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetDNASystem", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::AuracronMetaHumanFramework_eventGetDNASystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::AuracronMetaHumanFramework_eventGetDNASystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetDNASystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanDNASystem**)Z_Param__Result=P_THIS->GetDNASystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetDNASystem **************************

// ********** Begin Class UAuracronMetaHumanFramework Function GetFrameworkConfiguration ***********
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics
{
	struct AuracronMetaHumanFramework_eventGetFrameworkConfiguration_Parms
	{
		FAuracronMetaHumanConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current framework configuration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current framework configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetFrameworkConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMetaHumanConfig, METADATA_PARAMS(0, nullptr) }; // 752160458
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetFrameworkConfiguration", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::AuracronMetaHumanFramework_eventGetFrameworkConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::AuracronMetaHumanFramework_eventGetFrameworkConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetFrameworkConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMetaHumanConfig*)Z_Param__Result=P_THIS->GetFrameworkConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetFrameworkConfiguration *************

// ********** Begin Class UAuracronMetaHumanFramework Function GetFrameworkVersion *****************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics
{
	struct AuracronMetaHumanFramework_eventGetFrameworkVersion_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get framework version\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get framework version" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetFrameworkVersion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetFrameworkVersion", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::AuracronMetaHumanFramework_eventGetFrameworkVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::AuracronMetaHumanFramework_eventGetFrameworkVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetFrameworkVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetFrameworkVersion();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetFrameworkVersion *******************

// ********** Begin Class UAuracronMetaHumanFramework Function GetPerformanceMetrics ***************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics
{
	struct AuracronMetaHumanFramework_eventGetPerformanceMetrics_Parms
	{
		FAuracronMetaHumanMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get current performance metrics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics, METADATA_PARAMS(0, nullptr) }; // 2271851725
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetPerformanceMetrics", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::AuracronMetaHumanFramework_eventGetPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::AuracronMetaHumanFramework_eventGetPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMetaHumanMetrics*)Z_Param__Result=P_THIS->GetPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetPerformanceMetrics *****************

// ********** Begin Class UAuracronMetaHumanFramework Function GetPerformanceSystem ****************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics
{
	struct AuracronMetaHumanFramework_eventGetPerformanceSystem_Parms
	{
		UAuracronMetaHumanPerformanceSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Performance System instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Performance System instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetPerformanceSystem_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetPerformanceSystem", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::AuracronMetaHumanFramework_eventGetPerformanceSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::AuracronMetaHumanFramework_eventGetPerformanceSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetPerformanceSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanPerformanceSystem**)Z_Param__Result=P_THIS->GetPerformanceSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetPerformanceSystem ******************

// ********** Begin Class UAuracronMetaHumanFramework Function GetPythonBindings *******************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics
{
	struct AuracronMetaHumanFramework_eventGetPythonBindings_Parms
	{
		UAuracronMetaHumanPythonBindings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python Bindings instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python Bindings instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetPythonBindings_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetPythonBindings", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::AuracronMetaHumanFramework_eventGetPythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::AuracronMetaHumanFramework_eventGetPythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetPythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanPythonBindings**)Z_Param__Result=P_THIS->GetPythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetPythonBindings *********************

// ********** Begin Class UAuracronMetaHumanFramework Function GetSupportedSDKVersion **************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics
{
	struct AuracronMetaHumanFramework_eventGetSupportedSDKVersion_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get supported MetaHuman SDK version\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get supported MetaHuman SDK version" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetSupportedSDKVersion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetSupportedSDKVersion", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::AuracronMetaHumanFramework_eventGetSupportedSDKVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::AuracronMetaHumanFramework_eventGetSupportedSDKVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetSupportedSDKVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSupportedSDKVersion();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetSupportedSDKVersion ****************

// ********** Begin Class UAuracronMetaHumanFramework Function GetTextureSystem ********************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics
{
	struct AuracronMetaHumanFramework_eventGetTextureSystem_Parms
	{
		UAuracronMetaHumanTextureSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Texture System instance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Texture System instance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventGetTextureSystem_Parms, ReturnValue), Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "GetTextureSystem", Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::AuracronMetaHumanFramework_eventGetTextureSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::AuracronMetaHumanFramework_eventGetTextureSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execGetTextureSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronMetaHumanTextureSystem**)Z_Param__Result=P_THIS->GetTextureSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function GetTextureSystem **********************

// ********** Begin Class UAuracronMetaHumanFramework Function InitializeFramework *****************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics
{
	struct AuracronMetaHumanFramework_eventInitializeFramework_Parms
	{
		FAuracronMetaHumanConfig Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize the MetaHuman Framework with configuration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize the MetaHuman Framework with configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventInitializeFramework_Parms, Config), Z_Construct_UScriptStruct_FAuracronMetaHumanConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 752160458
void Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanFramework_eventInitializeFramework_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanFramework_eventInitializeFramework_Parms), &Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "InitializeFramework", Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::AuracronMetaHumanFramework_eventInitializeFramework_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::AuracronMetaHumanFramework_eventInitializeFramework_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execInitializeFramework)
{
	P_GET_STRUCT_REF(FAuracronMetaHumanConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeFramework(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function InitializeFramework *******************

// ********** Begin Class UAuracronMetaHumanFramework Function IsFrameworkInitialized **************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics
{
	struct AuracronMetaHumanFramework_eventIsFrameworkInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if framework is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if framework is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanFramework_eventIsFrameworkInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanFramework_eventIsFrameworkInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "IsFrameworkInitialized", Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::AuracronMetaHumanFramework_eventIsFrameworkInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::AuracronMetaHumanFramework_eventIsFrameworkInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execIsFrameworkInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFrameworkInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function IsFrameworkInitialized ****************

// ********** Begin Class UAuracronMetaHumanFramework Function ResetPerformanceMetrics *************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reset performance metrics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "ResetPerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execResetPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function ResetPerformanceMetrics ***************

// ********** Begin Class UAuracronMetaHumanFramework Function ShutdownFramework *******************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown the MetaHuman Framework\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown the MetaHuman Framework" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "ShutdownFramework", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execShutdownFramework)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownFramework();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function ShutdownFramework *********************

// ********** Begin Class UAuracronMetaHumanFramework Function UpdateFrameworkConfiguration ********
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics
{
	struct AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms
	{
		FAuracronMetaHumanConfig NewConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update framework configuration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update framework configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms, NewConfig), Z_Construct_UScriptStruct_FAuracronMetaHumanConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 752160458
void Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms), &Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_NewConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "UpdateFrameworkConfiguration", Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::AuracronMetaHumanFramework_eventUpdateFrameworkConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execUpdateFrameworkConfiguration)
{
	P_GET_STRUCT_REF(FAuracronMetaHumanConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateFrameworkConfiguration(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function UpdateFrameworkConfiguration **********

// ********** Begin Class UAuracronMetaHumanFramework Function UpdatePerformanceMetrics ************
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update performance metrics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function UpdatePerformanceMetrics **************

// ********** Begin Class UAuracronMetaHumanFramework Function ValidateSystemRequirements **********
struct Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics
{
	struct AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms
	{
		FString OutErrorMessage;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate system requirements\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate system requirements" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrorMessage;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_OutErrorMessage = { "OutErrorMessage", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms, OutErrorMessage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms), &Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_OutErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanFramework, nullptr, "ValidateSystemRequirements", Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::AuracronMetaHumanFramework_eventValidateSystemRequirements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanFramework::execValidateSystemRequirements)
{
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutErrorMessage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSystemRequirements(Z_Param_Out_OutErrorMessage);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanFramework Function ValidateSystemRequirements ************

// ********** Begin Class UAuracronMetaHumanFramework **********************************************
void UAuracronMetaHumanFramework::StaticRegisterNativesUAuracronMetaHumanFramework()
{
	UClass* Class = UAuracronMetaHumanFramework::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetAnimationSystem", &UAuracronMetaHumanFramework::execGetAnimationSystem },
		{ "GetClothingSystem", &UAuracronMetaHumanFramework::execGetClothingSystem },
		{ "GetDNASystem", &UAuracronMetaHumanFramework::execGetDNASystem },
		{ "GetFrameworkConfiguration", &UAuracronMetaHumanFramework::execGetFrameworkConfiguration },
		{ "GetFrameworkVersion", &UAuracronMetaHumanFramework::execGetFrameworkVersion },
		{ "GetPerformanceMetrics", &UAuracronMetaHumanFramework::execGetPerformanceMetrics },
		{ "GetPerformanceSystem", &UAuracronMetaHumanFramework::execGetPerformanceSystem },
		{ "GetPythonBindings", &UAuracronMetaHumanFramework::execGetPythonBindings },
		{ "GetSupportedSDKVersion", &UAuracronMetaHumanFramework::execGetSupportedSDKVersion },
		{ "GetTextureSystem", &UAuracronMetaHumanFramework::execGetTextureSystem },
		{ "InitializeFramework", &UAuracronMetaHumanFramework::execInitializeFramework },
		{ "IsFrameworkInitialized", &UAuracronMetaHumanFramework::execIsFrameworkInitialized },
		{ "ResetPerformanceMetrics", &UAuracronMetaHumanFramework::execResetPerformanceMetrics },
		{ "ShutdownFramework", &UAuracronMetaHumanFramework::execShutdownFramework },
		{ "UpdateFrameworkConfiguration", &UAuracronMetaHumanFramework::execUpdateFrameworkConfiguration },
		{ "UpdatePerformanceMetrics", &UAuracronMetaHumanFramework::execUpdatePerformanceMetrics },
		{ "ValidateSystemRequirements", &UAuracronMetaHumanFramework::execValidateSystemRequirements },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanFramework;
UClass* UAuracronMetaHumanFramework::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanFramework;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanFramework.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanFramework"),
			Z_Registration_Info_UClass_UAuracronMetaHumanFramework.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanFramework,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanFramework.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister()
{
	return UAuracronMetaHumanFramework::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanFramework_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Main AURACRON MetaHuman Framework Class\n * Provides modular MetaHuman integration with UE 5.6 latest APIs\n */" },
#endif
		{ "DisplayName", "AURACRON MetaHuman Framework" },
		{ "IncludePath", "AuracronMetaHumanFramework.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main AURACRON MetaHuman Framework Class\nProvides modular MetaHuman integration with UE 5.6 latest APIs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DNASystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal Systems ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal Systems ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonBindings_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentConfig_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Configuration and State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Configuration and State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMetrics_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronMetaHumanFramework.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DNASystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AnimationSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextureSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PerformanceSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PythonBindings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMetrics;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetAnimationSystem, "GetAnimationSystem" }, // 1630434799
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetClothingSystem, "GetClothingSystem" }, // 2755788914
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetDNASystem, "GetDNASystem" }, // 3523232799
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkConfiguration, "GetFrameworkConfiguration" }, // 3530733387
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetFrameworkVersion, "GetFrameworkVersion" }, // 2124697667
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceMetrics, "GetPerformanceMetrics" }, // 2436614174
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPerformanceSystem, "GetPerformanceSystem" }, // 917468102
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetPythonBindings, "GetPythonBindings" }, // 2631796340
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetSupportedSDKVersion, "GetSupportedSDKVersion" }, // 3125375846
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_GetTextureSystem, "GetTextureSystem" }, // 1010310281
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_InitializeFramework, "InitializeFramework" }, // 2953762183
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_IsFrameworkInitialized, "IsFrameworkInitialized" }, // 914022273
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_ResetPerformanceMetrics, "ResetPerformanceMetrics" }, // 2129623860
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_ShutdownFramework, "ShutdownFramework" }, // 2326606177
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdateFrameworkConfiguration, "UpdateFrameworkConfiguration" }, // 1831474406
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1965985099
		{ &Z_Construct_UFunction_UAuracronMetaHumanFramework_ValidateSystemRequirements, "ValidateSystemRequirements" }, // 1371130377
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanFramework>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_DNASystem = { "DNASystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, DNASystem), Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DNASystem_MetaData), NewProp_DNASystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_AnimationSystem = { "AnimationSystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, AnimationSystem), Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSystem_MetaData), NewProp_AnimationSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_TextureSystem = { "TextureSystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, TextureSystem), Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureSystem_MetaData), NewProp_TextureSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_PerformanceSystem = { "PerformanceSystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, PerformanceSystem), Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceSystem_MetaData), NewProp_PerformanceSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_ClothingSystem = { "ClothingSystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, ClothingSystem), Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingSystem_MetaData), NewProp_ClothingSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_PythonBindings = { "PythonBindings", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, PythonBindings), Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonBindings_MetaData), NewProp_PythonBindings_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_CurrentConfig = { "CurrentConfig", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, CurrentConfig), Z_Construct_UScriptStruct_FAuracronMetaHumanConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentConfig_MetaData), NewProp_CurrentConfig_MetaData) }; // 752160458
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_CurrentMetrics = { "CurrentMetrics", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanFramework, CurrentMetrics), Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMetrics_MetaData), NewProp_CurrentMetrics_MetaData) }; // 2271851725
void Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanFramework*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanFramework), &Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_DNASystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_AnimationSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_TextureSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_PerformanceSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_ClothingSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_PythonBindings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_CurrentConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_CurrentMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::NewProp_bIsInitialized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::ClassParams = {
	&UAuracronMetaHumanFramework::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanFramework()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanFramework.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanFramework.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanFramework_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanFramework.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanFramework);
// ********** End Class UAuracronMetaHumanFramework ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronMetaHumanQuality_StaticEnum, TEXT("EAuracronMetaHumanQuality"), &Z_Registration_Info_UEnum_EAuracronMetaHumanQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2631883390U) },
		{ EAuracronMetaHumanPriority_StaticEnum, TEXT("EAuracronMetaHumanPriority"), &Z_Registration_Info_UEnum_EAuracronMetaHumanPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3618240919U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronMetaHumanConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronMetaHumanConfig_Statics::NewStructOps, TEXT("AuracronMetaHumanConfig"), &Z_Registration_Info_UScriptStruct_FAuracronMetaHumanConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMetaHumanConfig), 752160458U) },
		{ FAuracronMetaHumanMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronMetaHumanMetrics_Statics::NewStructOps, TEXT("AuracronMetaHumanMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronMetaHumanMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMetaHumanMetrics), 2271851725U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanFramework, UAuracronMetaHumanFramework::StaticClass, TEXT("UAuracronMetaHumanFramework"), &Z_Registration_Info_UClass_UAuracronMetaHumanFramework, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanFramework), 1252485105U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_128194918(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_AuracronMetaHumanFramework_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
