# AURACRON

**Revolutionary MOBA 5v5 with Dynamic Multidimensional Maps and Procedural Content Generation**

![Unreal Engine 5.6](https://img.shields.io/badge/Unreal%20Engine-5.6-blue)
![Python](https://img.shields.io/badge/Python-3.11.8-green)
![C++](https://img.shields.io/badge/C++-17-red)
![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Android%20%7C%20iOS-lightgrey)

## 🎮 Sobre o Jogo

AURACRON é um MOBA revolucionário que redefine o gênero com:

- **Sistema de Realms Dinâmicos**: 3 camadas verticais de combate (Planície Radiante, Firmamento Zephyr, Abismo Umbrio)
- **Sistema de Sígilos Auracron**: Fusão de arquétipos que permite personalização única de heróis
- **IA Adaptativa**: Jungle que aprende padrões dos jogadores e se adapta dinamicamente
- **Objetivos Procedurais**: Objetivos que se adaptam ao estado da partida
- **Geração 100% Procedural**: Todo conteúdo gerado proceduralmente usando UE5.6

## 🏗️ Arquitetura Técnica

### Engine e Tecnologias
- **Unreal Engine 5.6** - Engine principal
- **C++ Bridges** - Exposição de APIs UE5.6 para Python
- **Python 3.11.8** - Scripts de geração procedural
- **PCG Framework** - Geração de conteúdo procedural
- **MetaHuman** - Criação de personagens
- **Lumen** - Iluminação global dinâmica
- **Nanite** - Geometria virtualizada
- **World Partition** - Gerenciamento de mundo grande

### Módulos C++ Bridge
1. **AuracronPCGFramework** - Procedural Content Generation
2. **AuracronMetaHumanFramework** - Framework MetaHuman Modular (NOVO!)
3. **AuracronWorldPartitionBridge** - Gerenciamento de mundo
4. **AuracronFoliageBridge** - Sistema de vegetação
5. **AuracronLumenBridge** - Controle de iluminação
6. **AuracronAbismoUmbrioBridge** - Sistema subterrâneo
7. **AuracronAdaptiveCreaturesBridge** - Criaturas adaptativas
8. **AuracronVerticalTransitionsBridge** - Transições verticais
9. **AuracronQABridge** - Quality Assurance

## 🆕 **NOVO: AURACRON MetaHuman Framework v2.0.0**

### 🏗️ **Arquitetura Modular Refatorada**
O antigo AuracronMetaHumanBridge foi completamente refatorado em uma arquitetura modular avançada:

#### 🧬 **DNA System**
- Operações Core: Load, Save, Validate, Optimize DNA
- Operações Async: Processamento assíncrono completo
- Blend Shapes: Manipulação avançada de blend shapes
- APIs UE 5.6: Integração com MetaHuman DNA SDK

#### 🎭 **Animation System**
- Tipos: Facial, Body, Hand, Eye, Lip Sync, Emotion
- Qualidade: Draft, Preview, Production, Cinematic
- Real-time: Aplicação de animações em tempo real
- Blending: Sistema avançado de blend spaces

#### 🎨 **Texture System**
- Tipos: Diffuse, Normal, Roughness, Metallic, Subsurface
- GPU Compute: Processamento acelerado por GPU
- Skin Generation: Geração procedural de texturas de pele
- Real-time: Parâmetros dinâmicos de material

#### ⚡ **Performance System**
- Monitoramento: Frame rate, Memory, CPU, GPU em tempo real
- Otimização: LOD, Texture Streaming, Animation, Culling
- Profiling: CPU e Memory profiling integrado
- Alertas: Sistema de alertas de performance

#### 👗 **Clothing System**
- Tipos: Shirt, Pants, Dress, Jacket, Shoes, etc.
- Materiais: Cotton, Silk, Wool, Leather, Denim, etc.
- Física: Simulação avançada com Chaos Physics
- Real-time: Parâmetros de vento e interação

#### 🐍 **Python Bindings**
- APIs Completas: Acesso a todos os sistemas via Python
- Async: Execução assíncrona de scripts
- Debugging: Sistema completo de debug
- Templates: Templates de scripts pré-configurados

## 📁 Estrutura do Projeto

```
Auracron/
├── Source/                     # Código C++
│   ├── Auracron/              # Módulo principal
│   ├── AuracronPCGBridge/     # Bridge PCG
│   ├── AuracronMetaHumanBridge/
│   ├── AuracronWorldPartitionBridge/
│   ├── AuracronFoliageBridge/
│   └── AuracronLumenBridge/
├── Scripts/                   # Scripts Python
│   └── Python/
│       ├── Core/             # Sistema core
│       ├── PCG/              # Geração procedural
│       ├── MetaHuman/        # Personagens
│       ├── WorldPartition/   # Mundo
│       ├── Foliage/          # Vegetação
│       ├── Lumen/            # Iluminação
│       ├── GameSystems/      # Sistemas do jogo
│       └── Utils/            # Utilitários
├── Content/                  # Assets do jogo
│   ├── Core/                 # Assets fundamentais
│   ├── Characters/           # Personagens
│   ├── Maps/                 # Mapas
│   ├── Abilities/            # Habilidades
│   ├── UI/                   # Interface
│   ├── Audio/                # Áudio
│   ├── VFX/                  # Efeitos visuais
│   ├── Materials/            # Materiais
│   ├── PCG/                  # Assets PCG
│   └── Realms/               # Assets dos Realms
├── Config/                   # Configurações
└── Documentation/            # Documentação
```

## 🚀 Configuração do Ambiente

### Pré-requisitos
- Unreal Engine 5.6
- Visual Studio 2022 (Windows)
- Python 3.11.8 (incluído no UE5.6)
- Git

### Instalação

1. **Clone o repositório**
   ```bash
   git clone <repository-url>
   cd Auracron
   ```

2. **Gerar arquivos de projeto**
   ```bash
   # Windows
   GenerateProjectFiles.bat
   ```

3. **Compilar o projeto**
   - Abrir `Auracron.sln` no Visual Studio
   - Compilar em modo Development Editor

4. **Abrir no Unreal Editor**
   - Abrir `Auracron.uproject`
   - Aguardar compilação dos shaders

### Verificação da Instalação

Execute no console Python do UE5.6:
```python
from Core.AuracronCore import test_auracron_integration
test_auracron_integration()
```

## 🔧 Desenvolvimento

### Workflow de Desenvolvimento

1. **Identificar Tarefa** - Definir claramente o objetivo
2. **Identificar APIs** - Mapear APIs UE5.6 necessárias
3. **Buscar Documentação** - Consultar documentação oficial mais recente
4. **Implementar** - Código production-ready sem placeholders
5. **Auditar** - Verificar completude e qualidade

### Convenções de Código

#### C++
- Seguir padrões UE5.6
- Prefixos: `U` para UObject, `A` para Actor, `F` para structs
- Documentação em inglês
- Logs detalhados

#### Python
- Seguir PEP 8
- Documentação em português brasileiro
- Type hints obrigatórios
- Tratamento robusto de erros

### Plugins Necessários

- PCG (Procedural Content Generation)
- MetaHuman
- PythonScriptPlugin
- EditorScriptingUtilities
- WorldPartition
- Foliage
- GameplayAbilities
- EnhancedInput
- CommonUI

## 🎯 Sistemas do Jogo

### Sistema de Realms
- **Planície Radiante** - Camada terrestre principal
- **Firmamento Zephyr** - Camada aérea com plataformas flutuantes
- **Abismo Umbrio** - Camada subterrânea com túneis

### Sistema de Sígilos Auracron
- **Arquétipos Base**: Guerreiro, Mago, Assassino
- **Fusão Dinâmica**: Combinação de arquétipos durante partida
- **Evolução Adaptativa**: Habilidades evoluem baseado no gameplay

### IA Adaptativa
- Análise de padrões de movimento
- Adaptação de spawns de jungle
- Objetivos dinâmicos baseados no estado da partida

## 📊 Performance

### Targets de Performance
- **PC**: 60 FPS @ 1080p, 30 FPS @ 4K
- **Mobile**: 30 FPS @ 1080p, 60 FPS @ 720p
- **Latência**: < 50ms para ações críticas

### Otimizações
- Nanite para geometria complexa
- Lumen para iluminação eficiente
- World Partition para streaming otimizado
- LOD automático via PCG

## 🧪 Testes

### Executar Testes
```python
# No console Python do UE5.6
exec(open('Scripts/Python/Tests/run_all_tests.py').read())
```

### Tipos de Teste
- **Unitários**: Testes de componentes individuais
- **Integração**: Testes de comunicação entre sistemas
- **Performance**: Benchmarks de geração procedural
- **Gameplay**: Testes de mecânicas do jogo

## 📈 CI/CD

### Pipeline Automatizado
- Build automático em commits
- Testes automatizados
- Análise de código
- Deploy para ambientes de teste

### Ambientes
- **Development**: Desenvolvimento local
- **Testing**: Testes automatizados
- **Staging**: Testes de aceitação
- **Production**: Release final

## 📚 Documentação

- [Game Design Document](mapas/mapa/AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md)
- [Documentação Técnica](Documentation/Technical/)
- [Guias de Desenvolvimento](Documentation/Development/)
- [API Reference](Documentation/API/)

## 🤝 Contribuição

### Processo de Contribuição
1. Fork do repositório
2. Criar branch para feature
3. Implementar seguindo workflow
4. Testes completos
5. Pull request com documentação

### Padrões de Qualidade
- Código production-ready
- Documentação completa
- Testes abrangentes
- Performance otimizada

## 📄 Licença

Copyright (c) 2025 Auracron Studios. Todos os direitos reservados.

## 🆘 Suporte

- **Issues**: GitHub Issues
- **Documentação**: [Wiki do Projeto]
- **Contato**: <EMAIL>

---

**AURACRON** - Redefinindo o futuro dos MOBAs através da inovação tecnológica e gameplay revolucionário.
