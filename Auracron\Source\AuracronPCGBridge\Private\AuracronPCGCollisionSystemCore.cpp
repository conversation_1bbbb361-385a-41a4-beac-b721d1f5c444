// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Collision System Core Implementation
// Bridge 2.10: PCG Framework - Collision e Physics

#include "AuracronPCGCollisionSystem.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"

namespace AuracronPCGCollisionSystemUtils
{
    // =============================================================================
    // COLLISION QUERY PARAMETER CREATION
    // =============================================================================

    FCollisionQueryParams CreateCollisionQueryParams(const FAuracronPCGCollisionDescriptor& CollisionDescriptor, const AActor* IgnoreActor)
    {
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = CollisionDescriptor.bTraceComplex;
        QueryParams.bReturnPhysicalMaterial = CollisionDescriptor.bReturnPhysicalMaterial;
        
        if (IgnoreActor && CollisionDescriptor.bIgnoreSelf)
        {
            QueryParams.AddIgnoredActor(IgnoreActor);
        }

        for (AActor* ActorToIgnore : CollisionDescriptor.ActorsToIgnore)
        {
            if (ActorToIgnore)
            {
                QueryParams.AddIgnoredActor(ActorToIgnore);
            }
        }

        return QueryParams;
    }

    FCollisionObjectQueryParams CreateObjectQueryParams(const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
    {
        FCollisionObjectQueryParams ObjectQueryParams;
        
        if (CollisionDescriptor.ObjectTypes.Num() > 0)
        {
            for (const TEnumAsByte<EObjectTypeQuery>& ObjectType : CollisionDescriptor.ObjectTypes)
            {
                ObjectQueryParams.AddObjectTypesToQuery(UEngineTypes::ConvertToCollisionChannel(ObjectType));
            }
        }
        else
        {
            ObjectQueryParams = FCollisionObjectQueryParams::AllObjects;
        }

        return ObjectQueryParams;
    }

    FCollisionShape CreateCollisionShape(const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
    {
        switch (CollisionDescriptor.CollisionTestType)
        {
            case EAuracronPCGCollisionTestType::SphereTrace:
            case EAuracronPCGCollisionTestType::OverlapSphere:
                return FCollisionShape::MakeSphere(CollisionDescriptor.SphereRadius);
            case EAuracronPCGCollisionTestType::BoxTrace:
            case EAuracronPCGCollisionTestType::OverlapBox:
                return FCollisionShape::MakeBox(CollisionDescriptor.BoxExtent);
            case EAuracronPCGCollisionTestType::CapsuleTrace:
            case EAuracronPCGCollisionTestType::OverlapCapsule:
                return FCollisionShape::MakeCapsule(CollisionDescriptor.CapsuleRadius, CollisionDescriptor.CapsuleHalfHeight);
            default:
                return FCollisionShape::MakeSphere(CollisionDescriptor.SphereRadius);
        }
    }

    // =============================================================================
    // COLLISION TRACE FUNCTIONS
    // =============================================================================

    bool LineTrace(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        return World->LineTraceSingleByChannel(OutHitResult, Start, End, CollisionDescriptor.CollisionChannel, QueryParams);
    }

    bool SphereTrace(UWorld* World, const FVector& Start, const FVector& End, float Radius, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape SphereShape = FCollisionShape::MakeSphere(Radius);
        return World->SweepSingleByChannel(OutHitResult, Start, End, FQuat::Identity, CollisionDescriptor.CollisionChannel, SphereShape, QueryParams);
    }

    bool BoxTrace(UWorld* World, const FVector& Start, const FVector& End, const FVector& Extent, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape BoxShape = FCollisionShape::MakeBox(Extent);
        return World->SweepSingleByChannel(OutHitResult, Start, End, FQuat::Identity, CollisionDescriptor.CollisionChannel, BoxShape, QueryParams);
    }

    bool CapsuleTrace(UWorld* World, const FVector& Start, const FVector& End, float Radius, float HalfHeight, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape CapsuleShape = FCollisionShape::MakeCapsule(Radius, HalfHeight);
        return World->SweepSingleByChannel(OutHitResult, Start, End, FQuat::Identity, CollisionDescriptor.CollisionChannel, CapsuleShape, QueryParams);
    }

    // =============================================================================
    // COLLISION OVERLAP FUNCTIONS
    // =============================================================================

    bool SphereOverlap(UWorld* World, const FVector& Location, float Radius, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape SphereShape = FCollisionShape::MakeSphere(Radius);
        return World->OverlapMultiByChannel(OutOverlaps, Location, FQuat::Identity, CollisionDescriptor.CollisionChannel, SphereShape, QueryParams);
    }

    bool BoxOverlap(UWorld* World, const FVector& Location, const FVector& Extent, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape BoxShape = FCollisionShape::MakeBox(Extent);
        return World->OverlapMultiByChannel(OutOverlaps, Location, FQuat::Identity, CollisionDescriptor.CollisionChannel, BoxShape, QueryParams);
    }

    bool CapsuleOverlap(UWorld* World, const FVector& Location, float Radius, float HalfHeight, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps)
    {
        if (!World)
        {
            return false;
        }

        FCollisionQueryParams QueryParams = CreateCollisionQueryParams(CollisionDescriptor);
        FCollisionShape CapsuleShape = FCollisionShape::MakeCapsule(Radius, HalfHeight);
        return World->OverlapMultiByChannel(OutOverlaps, Location, FQuat::Identity, CollisionDescriptor.CollisionChannel, CapsuleShape, QueryParams);
    }

    // =============================================================================
    // PHYSICS CONFIGURATION FUNCTIONS
    // =============================================================================

    void ApplyPhysicsProperties(UPrimitiveComponent* Component, const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor)
    {
        if (!Component)
        {
            return;
        }

        // Set simulation type
        switch (PhysicsDescriptor.SimulationType)
        {
            case EAuracronPCGPhysicsSimulationType::Static:
                Component->SetMobility(EComponentMobility::Static);
                Component->SetSimulatePhysics(false);
                break;
            case EAuracronPCGPhysicsSimulationType::Kinematic:
                Component->SetMobility(EComponentMobility::Movable);
                Component->SetSimulatePhysics(false);
                break;
            case EAuracronPCGPhysicsSimulationType::Dynamic:
            case EAuracronPCGPhysicsSimulationType::Simulated:
                Component->SetMobility(EComponentMobility::Movable);
                Component->SetSimulatePhysics(true);
                break;
            default:
                break;
        }

        // Set physics properties
        if (Component->GetBodyInstance())
        {
            FBodyInstance* BodyInstance = Component->GetBodyInstance();
            
            BodyInstance->SetMassOverride(PhysicsDescriptor.Mass, true);
            BodyInstance->bEnableGravity = PhysicsDescriptor.bEnableGravity;
            BodyInstance->LinearDamping = PhysicsDescriptor.LinearDamping;
            BodyInstance->AngularDamping = PhysicsDescriptor.AngularDamping;
            BodyInstance->bStartAwake = PhysicsDescriptor.bStartAwake;
            BodyInstance->bGenerateWakeEvents = PhysicsDescriptor.bGenerateWakeEvents;
            BodyInstance->SleepThresholdMultiplier = PhysicsDescriptor.SleepThresholdMultiplier;
            BodyInstance->StabilizationThresholdMultiplier = PhysicsDescriptor.StabilizationThresholdMultiplier;

            // Set constraints
            BodyInstance->bLockXTranslation = PhysicsDescriptor.bLockXTranslation;
            BodyInstance->bLockYTranslation = PhysicsDescriptor.bLockYTranslation;
            BodyInstance->bLockZTranslation = PhysicsDescriptor.bLockZTranslation;
            BodyInstance->bLockXRotation = PhysicsDescriptor.bLockXRotation;
            BodyInstance->bLockYRotation = PhysicsDescriptor.bLockYRotation;
            BodyInstance->bLockZRotation = PhysicsDescriptor.bLockZRotation;
        }
    }

    FBodyInstance* CreateBodyInstance(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor)
    {
        FBodyInstance* BodyInstance = new FBodyInstance();
        
        BodyInstance->SetMassOverride(PhysicsDescriptor.Mass, true);
        BodyInstance->bEnableGravity = PhysicsDescriptor.bEnableGravity;
        BodyInstance->LinearDamping = PhysicsDescriptor.LinearDamping;
        BodyInstance->AngularDamping = PhysicsDescriptor.AngularDamping;
        BodyInstance->bStartAwake = PhysicsDescriptor.bStartAwake;
        BodyInstance->bGenerateWakeEvents = PhysicsDescriptor.bGenerateWakeEvents;

        // Set constraints
        BodyInstance->bLockXTranslation = PhysicsDescriptor.bLockXTranslation;
        BodyInstance->bLockYTranslation = PhysicsDescriptor.bLockYTranslation;
        BodyInstance->bLockZTranslation = PhysicsDescriptor.bLockZTranslation;
        BodyInstance->bLockXRotation = PhysicsDescriptor.bLockXRotation;
        BodyInstance->bLockYRotation = PhysicsDescriptor.bLockYRotation;
        BodyInstance->bLockZRotation = PhysicsDescriptor.bLockZRotation;

        return BodyInstance;
    }

    void ConfigureCollisionResponse(UPrimitiveComponent* Component, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
    {
        if (!Component)
        {
            return;
        }

        // Set collision response based on descriptor
        switch (CollisionDescriptor.CollisionResponse)
        {
            case EAuracronPCGCollisionResponse::Ignore:
                Component->SetCollisionResponseToAllChannels(ECR_Ignore);
                break;
            case EAuracronPCGCollisionResponse::Overlap:
                Component->SetCollisionResponseToAllChannels(ECR_Overlap);
                break;
            case EAuracronPCGCollisionResponse::Block:
                Component->SetCollisionResponseToAllChannels(ECR_Block);
                break;
            default:
                break;
        }

        // Set collision channel
        Component->SetCollisionObjectType(CollisionDescriptor.CollisionChannel);
    }

    // =============================================================================
    // STABILITY AND VALIDATION FUNCTIONS
    // =============================================================================

    TArray<FVector> GenerateStabilitySamplePoints(const FVector& Center, float Radius, int32 Samples)
    {
        TArray<FVector> SamplePoints;
        SamplePoints.Reserve(Samples);

        for (int32 i = 0; i < Samples; i++)
        {
            float Angle = (2.0f * PI * i) / Samples;
            FVector Offset = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f) * Radius;
            SamplePoints.Add(Center + Offset);
        }

        return SamplePoints;
    }

    float CalculateTerrainStability(UWorld* World, const TArray<FVector>& SamplePoints, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
    {
        if (!World || SamplePoints.Num() == 0)
        {
            return 0.0f;
        }

        int32 ValidSamples = 0;
        float TotalStability = 0.0f;

        for (const FVector& SamplePoint : SamplePoints)
        {
            FHitResult HitResult;
            FVector TraceStart = SamplePoint + FVector(0.0f, 0.0f, 100.0f);
            FVector TraceEnd = SamplePoint + FVector(0.0f, 0.0f, -100.0f);

            if (LineTrace(World, TraceStart, TraceEnd, CollisionDescriptor, HitResult))
            {
                // Calculate stability based on surface normal
                float SurfaceAngle = FMath::Acos(FVector::DotProduct(HitResult.Normal, FVector::UpVector));
                float StabilityScore = 1.0f - (SurfaceAngle / (PI * 0.5f)); // Normalize to 0-1
                TotalStability += FMath::Clamp(StabilityScore, 0.0f, 1.0f);
                ValidSamples++;
            }
        }

        return ValidSamples > 0 ? (TotalStability / ValidSamples) : 0.0f;
    }

    bool IsLocationAccessible(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
    {
        if (!World)
        {
            return false;
        }

        FHitResult HitResult;
        return !LineTrace(World, Start, End, CollisionDescriptor, HitResult);
    }

    // =============================================================================
    // ACTOR FILTERING FUNCTIONS
    // =============================================================================

    void FilterActorsByTag(TArray<AActor*>& Actors, const TArray<FName>& RequiredTags)
    {
        if (RequiredTags.Num() == 0)
        {
            return;
        }

        Actors.RemoveAll([&RequiredTags](AActor* Actor) -> bool
        {
            if (!Actor)
            {
                return true;
            }

            for (const FName& RequiredTag : RequiredTags)
            {
                if (!Actor->Tags.Contains(RequiredTag))
                {
                    return true; // Remove if doesn't have required tag
                }
            }
            return false; // Keep if has all required tags
        });
    }

    void FilterActorsByClass(TArray<AActor*>& Actors, const TArray<TSubclassOf<AActor>>& AllowedClasses)
    {
        if (AllowedClasses.Num() == 0)
        {
            return;
        }

        Actors.RemoveAll([&AllowedClasses](AActor* Actor) -> bool
        {
            if (!Actor)
            {
                return true;
            }

            for (const TSubclassOf<AActor>& AllowedClass : AllowedClasses)
            {
                if (Actor->IsA(AllowedClass))
                {
                    return false; // Keep if matches allowed class
                }
            }
            return true; // Remove if doesn't match any allowed class
        });
    }

    void SortActorsByDistance(TArray<AActor*>& Actors, const FVector& ReferenceLocation)
    {
        Actors.Sort([&ReferenceLocation](const AActor& A, const AActor& B) -> bool
        {
            float DistanceA = FVector::Dist(A.GetActorLocation(), ReferenceLocation);
            float DistanceB = FVector::Dist(B.GetActorLocation(), ReferenceLocation);
            return DistanceA < DistanceB;
        });
    }
}
