// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGLogger.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGLogger_generated_h
#error "AuracronPCGLogger.generated.h already included, missing '#pragma once' in AuracronPCGLogger.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGLogger_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronPCGLogEntry **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h_20_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGLogEntry;
// ********** End ScriptStruct FAuracronPCGLogEntry ************************************************

// ********** Begin ScriptStruct FAuracronPCGLogConfig *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGLogConfig;
// ********** End ScriptStruct FAuracronPCGLogConfig ***********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
