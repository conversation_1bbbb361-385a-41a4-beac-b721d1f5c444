// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node Examples Implementation
// Bridge 2.13: PCG Framework - Custom Node Creation

#include "AuracronPCGCustomNodeExamples.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGCustomNodeSystem.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// =============================================================================
// EXAMPLE CUSTOM POINT PROCESSOR IMPLEMENTATION
// =============================================================================

UAuracronPCGExampleCustomPointProcessorSettings::UAuracronPCGExampleCustomPointProcessorSettings()
{
    NodeMetadata.NodeName = TEXT("Example Custom Point Processor");
    NodeMetadata.NodeDescription = TEXT("Example custom node that processes points with various parameters");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Custom"));
    NodeMetadata.Tags.Add(TEXT("Example"));
    NodeMetadata.Tags.Add(TEXT("Processing"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.8f);
}

void UAuracronPCGExampleCustomPointProcessorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGExampleCustomPointProcessorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGExampleCustomPointProcessorSettings::CreateCustomNodeTemplate()
{
    UAuracronPCGCustomNodeBuilder* Builder = NewObject<UAuracronPCGCustomNodeBuilder>();
    
    return Builder->SetNodeName(TEXT("ExampleCustomPointProcessor"))
                  ->SetDisplayName(TEXT("Example Custom Point Processor"))
                  ->SetDescription(TEXT("Example custom node that processes points with various parameters"))
                  ->SetTemplateType(EAuracronPCGCustomNodeTemplateType::Modifier)
                  ->SetCategory(EAuracronPCGNodeCategory::Modifier)
                  ->AddTag(TEXT("Custom"))
                  ->AddTag(TEXT("Example"))
                  ->SetNodeColor(FLinearColor(0.8f, 0.4f, 0.8f))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateFloatParameter(TEXT("ProcessingStrength"), 1.0f, 0.0f, 10.0f))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateIntParameter(TEXT("ProcessingIterations"), 1, 1, 10))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateBoolParameter(TEXT("bEnableAdvancedProcessing"), false))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateStringParameter(TEXT("ProcessingMode"), TEXT("Default")))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateVectorParameter(TEXT("ProcessingOffset"), FVector::ZeroVector))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateColorParameter(TEXT("ProcessingColor"), FLinearColor::White))
                  ->AddInputPin(UAuracronPCGCustomNodeUtils::CreateInputPin(TEXT("Input"), EPCGDataType::Point))
                  ->AddOutputPin(UAuracronPCGCustomNodeUtils::CreateOutputPin(TEXT("Output"), EPCGDataType::Point))
                  ->SetExecutionMode(EAuracronPCGCustomNodeExecutionMode::Synchronous)
                  ->SetNativeClassName(TEXT("UAuracronPCGExampleCustomPointProcessorSettings"))
                  ->SetAuthor(TEXT("Auracron PCG Framework"))
                  ->SetVersion(TEXT("1.0"))
                  ->BuildTemplate();
}

FAuracronPCGElementResult FAuracronPCGExampleCustomPointProcessorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                       FPCGDataCollection& OutputData, 
                                                                                       const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGExampleCustomPointProcessorSettings* Settings = GetTypedSettings<UAuracronPCGExampleCustomPointProcessorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Example Custom Point Processor");
            return Result;
        }

        int32 TotalProcessed = 0;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Process each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Apply processing based on settings
                for (int32 Iteration = 0; Iteration < Settings->ProcessingIterations; Iteration++)
                {
                    // Apply processing strength
                    OutputPoint.Density *= Settings->ProcessingStrength;
                    
                    // Apply processing offset
                    FVector NewLocation = OutputPoint.Transform.GetLocation() + Settings->ProcessingOffset;
                    OutputPoint.Transform.SetLocation(NewLocation);
                    
                    // Apply processing color
                    OutputPoint.Color = FVector4(Settings->ProcessingColor.R, Settings->ProcessingColor.G, Settings->ProcessingColor.B, Settings->ProcessingColor.A);
                    
                    // Advanced processing if enabled
                    if (Settings->bEnableAdvancedProcessing)
                    {
                        // Apply advanced processing logic
                        OutputPoint.Density = FMath::Sin(OutputPoint.Density * PI) * Settings->ProcessingStrength;
                    }
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            // Add to output
            FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Pin = TEXT("Output");
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Example Custom Point Processor processed %d points"), TotalProcessed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Example Custom Point Processor error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// EXAMPLE CUSTOM GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGExampleCustomGeneratorSettings::UAuracronPCGExampleCustomGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("Example Custom Generator");
    NodeMetadata.NodeDescription = TEXT("Example custom node that generates points using various patterns");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Generator;
    NodeMetadata.Tags.Add(TEXT("Custom"));
    NodeMetadata.Tags.Add(TEXT("Example"));
    NodeMetadata.Tags.Add(TEXT("Generation"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.4f);
}

void UAuracronPCGExampleCustomGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    // Generators typically don't have input pins, but can have optional ones
    if (bUsePattern)
    {
        FPCGPinProperties& PatternPin = InputPins.Emplace_GetRef();
        PatternPin.Label = TEXT("Pattern Input");
        PatternPin.AllowedTypes = EPCGDataType::Point;
        PatternPin.bAdvancedPin = true;
    }
}

void UAuracronPCGExampleCustomGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Generated Points");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGExampleCustomGeneratorSettings::CreateCustomNodeTemplate()
{
    UAuracronPCGCustomNodeBuilder* Builder = NewObject<UAuracronPCGCustomNodeBuilder>();
    
    return Builder->SetNodeName(TEXT("ExampleCustomGenerator"))
                  ->SetDisplayName(TEXT("Example Custom Generator"))
                  ->SetDescription(TEXT("Example custom node that generates points using various patterns"))
                  ->SetTemplateType(EAuracronPCGCustomNodeTemplateType::Generator)
                  ->SetCategory(EAuracronPCGNodeCategory::Generator)
                  ->AddTag(TEXT("Custom"))
                  ->AddTag(TEXT("Example"))
                  ->SetNodeColor(FLinearColor(0.2f, 0.8f, 0.4f))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateIntParameter(TEXT("PointCount"), 100, 1, 10000))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateVectorParameter(TEXT("GenerationBounds"), FVector(1000.0f, 1000.0f, 1000.0f)))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateBoolParameter(TEXT("bUseRandomSeed"), true))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateIntParameter(TEXT("Seed"), 12345, 0, 999999))
                  ->AddParameter(UAuracronPCGCustomNodeUtils::CreateEnumParameter(TEXT("SelectedMode"), {TEXT("Random"), TEXT("Grid"), TEXT("Spiral")}, TEXT("Random")))
                  ->AddOutputPin(UAuracronPCGCustomNodeUtils::CreateOutputPin(TEXT("Generated Points"), EPCGDataType::Point))
                  ->SetExecutionMode(EAuracronPCGCustomNodeExecutionMode::Synchronous)
                  ->SetNativeClassName(TEXT("UAuracronPCGExampleCustomGeneratorSettings"))
                  ->SetAuthor(TEXT("Auracron PCG Framework"))
                  ->SetVersion(TEXT("1.0"))
                  ->BuildTemplate();
}

FAuracronPCGElementResult FAuracronPCGExampleCustomGeneratorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                  FPCGDataCollection& OutputData, 
                                                                                  const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGExampleCustomGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGExampleCustomGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Example Custom Generator");
            return Result;
        }

        // Create output point data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

        // Setup random stream
        int32 UsedSeed = Settings->bUseRandomSeed ? FMath::Rand() : Settings->Seed;
        FRandomStream RandomStream(UsedSeed);

        // Generate points based on selected mode
        if (Settings->SelectedMode == TEXT("Random"))
        {
            GenerateRandomPoints(OutputPoints, Settings, RandomStream);
        }
        else if (Settings->SelectedMode == TEXT("Grid"))
        {
            GenerateGridPoints(OutputPoints, Settings);
        }
        else if (Settings->SelectedMode == TEXT("Spiral"))
        {
            GenerateSpiralPoints(OutputPoints, Settings);
        }

        // Add to output
        FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
        OutputTaggedData.Data = OutputPointData;
        OutputTaggedData.Pin = TEXT("Generated Points");

        Result.bSuccess = true;
        Result.PointsProcessed = OutputPoints.Num();
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Example Custom Generator generated %d points"), OutputPoints.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Example Custom Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGExampleCustomGeneratorElement::GenerateRandomPoints(TArray<FPCGPoint>& OutPoints, const UAuracronPCGExampleCustomGeneratorSettings* Settings, FRandomStream& RandomStream) const
{
    OutPoints.Reserve(Settings->PointCount);

    for (int32 i = 0; i < Settings->PointCount; i++)
    {
        FPCGPoint Point;
        
        // Random position within bounds
        FVector RandomPosition;
        RandomPosition.X = RandomStream.FRandRange(-Settings->GenerationBounds.X * 0.5f, Settings->GenerationBounds.X * 0.5f);
        RandomPosition.Y = RandomStream.FRandRange(-Settings->GenerationBounds.Y * 0.5f, Settings->GenerationBounds.Y * 0.5f);
        RandomPosition.Z = RandomStream.FRandRange(-Settings->GenerationBounds.Z * 0.5f, Settings->GenerationBounds.Z * 0.5f);
        
        Point.Transform.SetLocation(RandomPosition);
        Point.Density = RandomStream.FRandRange(0.5f, 1.0f);
        Point.Color = FVector4(RandomStream.FRand(), RandomStream.FRand(), RandomStream.FRand(), 1.0f);
        
        OutPoints.Add(Point);
    }
}

void FAuracronPCGExampleCustomGeneratorElement::GenerateGridPoints(TArray<FPCGPoint>& OutPoints, const UAuracronPCGExampleCustomGeneratorSettings* Settings) const
{
    int32 GridSize = FMath::CeilToInt(FMath::Pow(Settings->PointCount, 1.0f / 3.0f));
    FVector GridSpacing = Settings->GenerationBounds / GridSize;
    FVector StartPosition = -Settings->GenerationBounds * 0.5f;

    OutPoints.Reserve(GridSize * GridSize * GridSize);

    for (int32 X = 0; X < GridSize; X++)
    {
        for (int32 Y = 0; Y < GridSize; Y++)
        {
            for (int32 Z = 0; Z < GridSize; Z++)
            {
                if (OutPoints.Num() >= Settings->PointCount)
                {
                    return;
                }

                FPCGPoint Point;
                FVector GridPosition = StartPosition + FVector(X, Y, Z) * GridSpacing;
                
                Point.Transform.SetLocation(GridPosition);
                Point.Density = 1.0f;
                Point.Color = FVector4(static_cast<float>(X) / GridSize, static_cast<float>(Y) / GridSize, static_cast<float>(Z) / GridSize, 1.0f);
                
                OutPoints.Add(Point);
            }
        }
    }
}

void FAuracronPCGExampleCustomGeneratorElement::GenerateSpiralPoints(TArray<FPCGPoint>& OutPoints, const UAuracronPCGExampleCustomGeneratorSettings* Settings) const
{
    OutPoints.Reserve(Settings->PointCount);

    float MaxRadius = FMath::Min(Settings->GenerationBounds.X, Settings->GenerationBounds.Y) * 0.5f;
    float HeightRange = Settings->GenerationBounds.Z;

    for (int32 i = 0; i < Settings->PointCount; i++)
    {
        FPCGPoint Point;
        
        float T = static_cast<float>(i) / Settings->PointCount;
        float Angle = T * 10.0f * PI; // Multiple spirals
        float Radius = T * MaxRadius;
        float Height = (T - 0.5f) * HeightRange;
        
        FVector SpiralPosition;
        SpiralPosition.X = FMath::Cos(Angle) * Radius;
        SpiralPosition.Y = FMath::Sin(Angle) * Radius;
        SpiralPosition.Z = Height;
        
        Point.Transform.SetLocation(SpiralPosition);
        Point.Density = 1.0f - T; // Density decreases along spiral
        Point.Color = FVector4(T, 1.0f - T, 0.5f, 1.0f);
        
        OutPoints.Add(Point);
    }
}
