// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Monetização Bridge Implementation

#include "AuracronMonetizationBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "OnlineSubsystem.h"
#include "Interfaces/OnlineStoreInterfaceV2.h"
#include "Interfaces/OnlinePurchaseInterface.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronMonetizationBridge::UAuracronMonetizationBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para monetização
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurar saldos iniciais de moeda
    CurrencyBalances.Add(EAuracronCurrencyType::AuraCoins, 0);
    CurrencyBalances.Add(EAuracronCurrencyType::ChronosShards, 1000); // Moeda inicial gratuita
    CurrencyBalances.Add(EAuracronCurrencyType::RealmEssence, 0);
    CurrencyBalances.Add(EAuracronCurrencyType::SigiloFragments, 0);
    CurrencyBalances.Add(EAuracronCurrencyType::SeasonTokens, 0);
    
    // Configurar Battle Pass inicial
    CurrentBattlePass.BattlePassID = TEXT("Season1");
    CurrentBattlePass.BattlePassName = FText::FromString(TEXT("Despertar dos Sígilos"));
    CurrentBattlePass.Season = 1;
    CurrentBattlePass.CurrentLevel = 1;
    CurrentBattlePass.CurrentXP = 0;
    CurrentBattlePass.XPToNextLevel = 1000;
    CurrentBattlePass.bPremiumPurchased = false;
    CurrentBattlePass.StartDate = FDateTime::Now();
    CurrentBattlePass.EndDate = FDateTime::Now() + FTimespan::FromDays(90); // 3 meses
    CurrentBattlePass.PremiumPrice = 950;
    CurrentBattlePass.bXPBoostActive = false;
    CurrentBattlePass.XPMultiplier = 1.0f;
}

void UAuracronMonetizationBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Monetização"));

    // Inicializar sistema
    bSystemInitialized = InitializeMonetizationSystem();
    
    if (bSystemInitialized)
    {
        // Carregar produtos da loja
        LoadStoreProducts();
        
        // Configurar timer para processamento
        GetWorld()->GetTimerManager().SetTimer(
            ProcessingTimer,
            [this]()
            {
                ProcessPendingTransactions(1.0f);
            },
            1.0f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Monetização inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Monetização"));
    }
}

void UAuracronMonetizationBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Processar transações pendentes antes de sair
    for (const FAuracronTransaction& Transaction : ActiveTransactions)
    {
        if (Transaction.TransactionStatus == TEXT("Pending"))
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transação pendente ao sair: %s"), *Transaction.TransactionID);
        }
    }
    
    // Limpar dados
    ActiveTransactions.Empty();
    StoreProducts.Empty();

    // Limpar timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(ProcessingTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronMonetizationBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronMonetizationBridge, CurrentBattlePass);
    DOREPLIFETIME(UAuracronMonetizationBridge, CurrencyBalances);
    DOREPLIFETIME(UAuracronMonetizationBridge, OwnedProducts);
}

void UAuracronMonetizationBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar transações pendentes
    ProcessPendingTransactions(DeltaTime);
}

// === Store Management ===

bool UAuracronMonetizationBridge::LoadStoreProducts()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    // Limpar produtos existentes
    StoreProducts.Empty();

    // Carregar produtos de exemplo (em produção, viria de servidor)
    
    // Pacotes de Aura Coins
    FAuracronStoreProduct AuraCoinsSmall;
    AuraCoinsSmall.ProductID = TEXT("aura_coins_small");
    AuraCoinsSmall.ProductName = FText::FromString(TEXT("Pacote Pequeno de Aura Coins"));
    AuraCoinsSmall.ProductDescription = FText::FromString(TEXT("500 Aura Coins + 50 de bônus"));
    AuraCoinsSmall.ProductType = EAuracronProductType::CurrencyPack;
    AuraCoinsSmall.ItemRarity = EAuracronItemRarity::Common;
    AuraCoinsSmall.RealMoneyPrice = 499; // $4.99
    AuraCoinsSmall.AcceptedCurrency = EAuracronCurrencyType::None; // Dinheiro real
    AuraCoinsSmall.bIsAvailable = true;
    AuraCoinsSmall.ProductCategory = TEXT("Currency");
    StoreProducts.Add(AuraCoinsSmall);

    // Battle Pass Premium
    FAuracronStoreProduct BattlePassPremium;
    BattlePassPremium.ProductID = TEXT("battle_pass_s1");
    BattlePassPremium.ProductName = FText::FromString(TEXT("Battle Pass: Despertar dos Sígilos"));
    BattlePassPremium.ProductDescription = FText::FromString(TEXT("Desbloqueie recompensas premium e acelere sua progressão"));
    BattlePassPremium.ProductType = EAuracronProductType::BattlePass;
    BattlePassPremium.ItemRarity = EAuracronItemRarity::Epic;
    BattlePassPremium.PremiumPrice = 950; // Aura Coins
    BattlePassPremium.AcceptedCurrency = EAuracronCurrencyType::AuraCoins;
    BattlePassPremium.bIsAvailable = true;
    BattlePassPremium.bIsTimeLimited = true;
    BattlePassPremium.ExpirationDate = CurrentBattlePass.EndDate;
    BattlePassPremium.ProductCategory = TEXT("BattlePass");
    StoreProducts.Add(BattlePassPremium);

    // Campeão Premium
    FAuracronStoreProduct ChampionPremium;
    ChampionPremium.ProductID = TEXT("champion_zara_voidweaver");
    ChampionPremium.ProductName = FText::FromString(TEXT("Zara, a Tecelã do Vazio"));
    ChampionPremium.ProductDescription = FText::FromString(TEXT("Campeã lendária com habilidades únicas de manipulação dimensional"));
    ChampionPremium.ProductType = EAuracronProductType::Champion;
    ChampionPremium.ItemRarity = EAuracronItemRarity::Legendary;
    ChampionPremium.PremiumPrice = 7800; // Aura Coins
    ChampionPremium.FreePrice = 15000; // Chronos Shards
    ChampionPremium.AcceptedCurrency = EAuracronCurrencyType::AuraCoins;
    ChampionPremium.bIsAvailable = true;
    ChampionPremium.bIsFeatured = true;
    ChampionPremium.ProductCategory = TEXT("Champions");
    StoreProducts.Add(ChampionPremium);

    // Skin Épica
    FAuracronStoreProduct SkinEpic;
    SkinEpic.ProductID = TEXT("skin_zara_cosmic_weaver");
    SkinEpic.ProductName = FText::FromString(TEXT("Zara: Tecelã Cósmica"));
    SkinEpic.ProductDescription = FText::FromString(TEXT("Skin épica com efeitos visuais únicos e animações especiais"));
    SkinEpic.ProductType = EAuracronProductType::Skin;
    SkinEpic.ItemRarity = EAuracronItemRarity::Epic;
    SkinEpic.PremiumPrice = 1350; // Aura Coins
    SkinEpic.AcceptedCurrency = EAuracronCurrencyType::AuraCoins;
    SkinEpic.bIsAvailable = true;
    SkinEpic.ProductCategory = TEXT("Skins");
    StoreProducts.Add(SkinEpic);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d produtos carregados na loja"), StoreProducts.Num());

    return true;
}

TArray<FAuracronStoreProduct> UAuracronMonetizationBridge::GetProductsByCategory(const FString& Category) const
{
    TArray<FAuracronStoreProduct> CategoryProducts;

    for (const FAuracronStoreProduct& Product : StoreProducts)
    {
        if (Product.ProductCategory == Category && Product.bIsAvailable)
        {
            CategoryProducts.Add(Product);
        }
    }

    return CategoryProducts;
}

TArray<FAuracronStoreProduct> UAuracronMonetizationBridge::GetFeaturedProducts() const
{
    TArray<FAuracronStoreProduct> FeaturedProducts;

    for (const FAuracronStoreProduct& Product : StoreProducts)
    {
        if (Product.bIsFeatured && Product.bIsAvailable)
        {
            FeaturedProducts.Add(Product);
        }
    }

    return FeaturedProducts;
}

bool UAuracronMonetizationBridge::IsProductAvailable(const FString& ProductID) const
{
    for (const FAuracronStoreProduct& Product : StoreProducts)
    {
        if (Product.ProductID == ProductID)
        {
            if (!Product.bIsAvailable)
                return false;

            // Verificar se é limitado por tempo
            if (Product.bIsTimeLimited && FDateTime::Now() > Product.ExpirationDate)
                return false;

            return true;
        }
    }

    return false;
}

int32 UAuracronMonetizationBridge::GetProductPrice(const FString& ProductID, EAuracronCurrencyType CurrencyType) const
{
    for (const FAuracronStoreProduct& Product : StoreProducts)
    {
        if (Product.ProductID == ProductID)
        {
            switch (CurrencyType)
            {
                case EAuracronCurrencyType::AuraCoins:
                    return Product.PremiumPrice;
                case EAuracronCurrencyType::ChronosShards:
                    return Product.FreePrice;
                default:
                    return Product.RealMoneyPrice;
            }
        }
    }

    return 0;
}

// === Purchase System ===

bool UAuracronMonetizationBridge::InitiatePurchase(const FString& ProductID, int32 Quantity)
{
    if (!bSystemInitialized || ProductID.IsEmpty() || Quantity <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros de compra inválidos"));
        return false;
    }

    if (!IsProductAvailable(ProductID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Produto não disponível: %s"), *ProductID);
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    // Criar transação
    FAuracronTransaction NewTransaction;
    NewTransaction.TransactionID = FGuid::NewGuid().ToString();
    NewTransaction.PlayerID = TEXT("LocalPlayer"); // Em produção, obter do sistema de autenticação
    NewTransaction.ProductID = ProductID;
    NewTransaction.Quantity = Quantity;
    NewTransaction.TransactionTime = FDateTime::Now();
    NewTransaction.TransactionStatus = TEXT("Initiated");
    NewTransaction.PurchasePlatform = UGameplayStatics::GetPlatformName();
    NewTransaction.bIsValidated = false;
    NewTransaction.bIsProcessed = false;

    // Calcular preço
    const FAuracronStoreProduct* Product = nullptr;
    for (const FAuracronStoreProduct& StoreProduct : StoreProducts)
    {
        if (StoreProduct.ProductID == ProductID)
        {
            Product = &StoreProduct;
            break;
        }
    }

    if (!Product)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Produto não encontrado: %s"), *ProductID);
        return false;
    }

    NewTransaction.CurrencyUsed = Product->AcceptedCurrency;
    
    if (Product->AcceptedCurrency == EAuracronCurrencyType::AuraCoins)
    {
        NewTransaction.PricePaid = Product->PremiumPrice * Quantity;
    }
    else if (Product->AcceptedCurrency == EAuracronCurrencyType::ChronosShards)
    {
        NewTransaction.PricePaid = Product->FreePrice * Quantity;
    }
    else
    {
        NewTransaction.PricePaid = Product->RealMoneyPrice * Quantity;
    }

    // Verificar se jogador tem saldo suficiente (para moedas do jogo)
    if (Product->AcceptedCurrency != EAuracronCurrencyType::None)
    {
        int32 CurrentBalance = GetCurrencyBalance(Product->AcceptedCurrency);
        if (CurrentBalance < NewTransaction.PricePaid)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Saldo insuficiente para compra: %s"), *ProductID);
            return false;
        }
    }

    ActiveTransactions.Add(NewTransaction);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Compra iniciada: %s (Quantidade: %d, Preço: %d)"), 
        *ProductID, Quantity, NewTransaction.PricePaid);

    // Processar compra
    return ProcessPurchase(NewTransaction);
}

bool UAuracronMonetizationBridge::ProcessPurchase(const FAuracronTransaction& Transaction)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    // Encontrar transação ativa
    FAuracronTransaction* ActiveTransaction = nullptr;
    for (FAuracronTransaction& ActiveTrans : ActiveTransactions)
    {
        if (ActiveTrans.TransactionID == Transaction.TransactionID)
        {
            ActiveTransaction = &ActiveTrans;
            break;
        }
    }

    if (!ActiveTransaction)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Transação não encontrada: %s"), *Transaction.TransactionID);
        return false;
    }

    // Simular processamento (em produção, usar APIs da plataforma)
    ActiveTransaction->TransactionStatus = TEXT("Processing");

    // Para moedas do jogo, processar imediatamente
    if (ActiveTransaction->CurrencyUsed != EAuracronCurrencyType::None)
    {
        // Debitar moeda
        if (SpendCurrency(ActiveTransaction->CurrencyUsed, ActiveTransaction->PricePaid, TEXT("Purchase")))
        {
            // Conceder produto
            OwnedProducts.Add(ActiveTransaction->ProductID);
            
            ActiveTransaction->TransactionStatus = TEXT("Completed");
            ActiveTransaction->bIsValidated = true;
            ActiveTransaction->bIsProcessed = true;

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Compra processada com sucesso: %s"), *ActiveTransaction->ProductID);

            // Broadcast evento
            OnPurchaseCompleted.Broadcast(*ActiveTransaction);

            return true;
        }
        else
        {
            ActiveTransaction->TransactionStatus = TEXT("Failed");
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao processar compra: %s"), *ActiveTransaction->ProductID);
            return false;
        }
    }

    // Para dinheiro real, aguardar validação da plataforma
    ActiveTransaction->TransactionStatus = TEXT("Pending Platform Validation");

    return true;
}

// === Currency Management ===

int32 UAuracronMonetizationBridge::GetCurrencyBalance(EAuracronCurrencyType CurrencyType) const
{
    if (const int32* Balance = CurrencyBalances.Find(CurrencyType))
    {
        return *Balance;
    }

    return 0;
}

bool UAuracronMonetizationBridge::AddCurrency(EAuracronCurrencyType CurrencyType, int32 Amount, const FString& Source)
{
    if (!bSystemInitialized || Amount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    int32 CurrentBalance = GetCurrencyBalance(CurrencyType);
    int32 NewBalance = CurrentBalance + Amount;

    CurrencyBalances.Add(CurrencyType, NewBalance);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Moeda adicionada - Tipo: %s, Quantidade: %d, Fonte: %s, Novo Saldo: %d"), 
        *UEnum::GetValueAsString(CurrencyType), Amount, *Source, NewBalance);

    return true;
}

bool UAuracronMonetizationBridge::SpendCurrency(EAuracronCurrencyType CurrencyType, int32 Amount, const FString& Purpose)
{
    if (!bSystemInitialized || Amount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    int32 CurrentBalance = GetCurrencyBalance(CurrencyType);
    
    if (CurrentBalance < Amount)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Saldo insuficiente - Tipo: %s, Necessário: %d, Disponível: %d"), 
            *UEnum::GetValueAsString(CurrencyType), Amount, CurrentBalance);
        return false;
    }

    int32 NewBalance = CurrentBalance - Amount;
    CurrencyBalances.Add(CurrencyType, NewBalance);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Moeda gasta - Tipo: %s, Quantidade: %d, Propósito: %s, Novo Saldo: %d"), 
        *UEnum::GetValueAsString(CurrencyType), Amount, *Purpose, NewBalance);

    return true;
}

// === Battle Pass ===

bool UAuracronMonetizationBridge::PurchaseBattlePass(int32 Season)
{
    if (!bSystemInitialized || CurrentBattlePass.Season != Season)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Temporada de Battle Pass inválida: %d"), Season);
        return false;
    }

    if (CurrentBattlePass.bPremiumPurchased)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Battle Pass premium já foi comprado"));
        return true;
    }

    // Verificar saldo
    if (GetCurrencyBalance(EAuracronCurrencyType::AuraCoins) < CurrentBattlePass.PremiumPrice)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Saldo insuficiente para Battle Pass premium"));
        return false;
    }

    // Processar compra
    if (SpendCurrency(EAuracronCurrencyType::AuraCoins, CurrentBattlePass.PremiumPrice, TEXT("Battle Pass Premium")))
    {
        CurrentBattlePass.bPremiumPurchased = true;
        CurrentBattlePass.bXPBoostActive = true;
        CurrentBattlePass.XPMultiplier = 1.5f; // 50% de boost

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Battle Pass premium comprado para temporada %d"), Season);

        return true;
    }

    return false;
}

bool UAuracronMonetizationBridge::AddBattlePassXP(int32 XPAmount, const FString& Source)
{
    if (!bSystemInitialized || XPAmount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    // Aplicar multiplicador se premium
    float FinalXP = XPAmount;
    if (CurrentBattlePass.bXPBoostActive)
    {
        FinalXP *= CurrentBattlePass.XPMultiplier;
    }

    int32 XPToAdd = FMath::RoundToInt(FinalXP);
    CurrentBattlePass.CurrentXP += XPToAdd;

    // Verificar level up
    while (CurrentBattlePass.CurrentXP >= CurrentBattlePass.XPToNextLevel && CurrentBattlePass.CurrentLevel < 100)
    {
        int32 OldLevel = CurrentBattlePass.CurrentLevel;
        
        CurrentBattlePass.CurrentXP -= CurrentBattlePass.XPToNextLevel;
        CurrentBattlePass.CurrentLevel++;
        
        // Calcular XP necessário para próximo nível (progressão exponencial)
        CurrentBattlePass.XPToNextLevel = FMath::RoundToInt(1000.0f * FMath::Pow(1.1f, CurrentBattlePass.CurrentLevel - 1));

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Battle Pass Level Up! %d -> %d"), OldLevel, CurrentBattlePass.CurrentLevel);

        // Broadcast evento
        OnBattlePassLevelUp.Broadcast(OldLevel, CurrentBattlePass.CurrentLevel);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: XP adicionado ao Battle Pass - Quantidade: %d, Fonte: %s, Nível: %d"), 
        XPToAdd, *Source, CurrentBattlePass.CurrentLevel);

    return true;
}

bool UAuracronMonetizationBridge::ClaimBattlePassReward(int32 Level, bool bPremiumReward)
{
    if (!bSystemInitialized || Level <= 0 || Level > CurrentBattlePass.CurrentLevel)
    {
        return false;
    }

    // Verificar se recompensa premium requer compra
    if (bPremiumReward && !CurrentBattlePass.bPremiumPurchased)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Battle Pass premium necessário para recompensa premium"));
        return false;
    }

    // Verificar se já foi coletada
    if (CurrentBattlePass.CollectedRewards.Contains(Level))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Recompensa já coletada para nível %d"), Level);
        return false;
    }

    FScopeLock Lock(&MonetizationMutex);

    // Coletar recompensa
    CurrentBattlePass.CollectedRewards.Add(Level);

    // Obter recompensa
    FString RewardID;
    if (bPremiumReward)
    {
        if (const FString* Reward = CurrentBattlePass.PremiumRewards.Find(Level))
        {
            RewardID = *Reward;
        }
    }
    else
    {
        if (const FString* Reward = CurrentBattlePass.FreeRewards.Find(Level))
        {
            RewardID = *Reward;
        }
    }

    if (!RewardID.IsEmpty())
    {
        // Conceder recompensa
        OwnedProducts.Add(RewardID);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Recompensa do Battle Pass coletada - Nível: %d, Recompensa: %s"), Level, *RewardID);
    }

    return true;
}

FAuracronBattlePass UAuracronMonetizationBridge::GetBattlePassProgress() const
{
    return CurrentBattlePass;
}

// === Gifting System ===

bool UAuracronMonetizationBridge::SendGift(const FString& RecipientID, const FString& ProductID, const FText& Message)
{
    if (!bSystemInitialized || RecipientID.IsEmpty() || ProductID.IsEmpty())
    {
        return false;
    }

    // Verificar se produto pode ser presenteado
    const FAuracronStoreProduct* Product = nullptr;
    for (const FAuracronStoreProduct& StoreProduct : StoreProducts)
    {
        if (StoreProduct.ProductID == ProductID)
        {
            Product = &StoreProduct;
            break;
        }
    }

    if (!Product || !Product->bIsAvailable)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Produto não disponível para presente: %s"), *ProductID);
        return false;
    }

    // Verificar saldo
    if (GetCurrencyBalance(Product->AcceptedCurrency) < Product->PremiumPrice)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Saldo insuficiente para presente"));
        return false;
    }

    // Processar presente
    if (SpendCurrency(Product->AcceptedCurrency, Product->PremiumPrice, TEXT("Gift")))
    {
        // Criar presente (em produção, enviar para sistema de backend)
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Presente enviado - Para: %s, Produto: %s"), *RecipientID, *ProductID);
        return true;
    }

    return false;
}

// === Analytics ===

bool UAuracronMonetizationBridge::TrackPurchase(const FAuracronTransaction& Transaction)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Implementar tracking de compras para analytics usando UE5.6 Analytics
    
    // Criar evento de analytics para a compra
    FAuracronPurchaseAnalytics PurchaseEvent;
    PurchaseEvent.TransactionID = Transaction.TransactionID;
    PurchaseEvent.ProductID = Transaction.ProductID;
    PurchaseEvent.ProductCategory = TEXT("Unknown"); // Será obtido do produto
    PurchaseEvent.PricePaid = Transaction.PricePaid;
    PurchaseEvent.CurrencyType = Transaction.CurrencyType;
    PurchaseEvent.Quantity = Transaction.Quantity;
    PurchaseEvent.PurchaseTimestamp = FDateTime::Now();
    PurchaseEvent.PlayerID = Transaction.PlayerID;
    
    // Obter informações adicionais do produto
    for (const FAuracronStoreProduct& Product : AvailableProducts)
    {
        if (Product.ProductID == Transaction.ProductID)
        {
            PurchaseEvent.ProductCategory = Product.Category;
            PurchaseEvent.ProductName = Product.DisplayName.ToString();
            break;
        }
    }
    
    // Atualizar estatísticas de monetização
    UpdateMonetizationStatistics(PurchaseEvent);
    
    // Enviar evento para sistema de analytics
    SendAnalyticsEvent(PurchaseEvent);
    
    // Log da compra para debugging
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Purchase tracked - Product: %s, Price: %.2f %s, Player: %s"), 
           *PurchaseEvent.ProductID, 
           PurchaseEvent.PricePaid, 
           *UEnum::GetValueAsString(PurchaseEvent.CurrencyType), 
           *PurchaseEvent.PlayerID);
    
    // Continuar processamento normal
    for (const FAuracronStoreProduct& Product : AvailableProducts)
    {
        if (Product.ProductID == Transaction.ProductID)
        {
            PurchaseEvent.ProductCategory = Product.Category;
            PurchaseEvent.ProductName = Product.DisplayName.ToString();
            break;
        }
    }
    
    // Calcular métricas de sessão
    float SessionDuration = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    PurchaseEvent.SessionDuration = SessionDuration;
    
    // Obter informações do jogador
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetUniqueID().ToString() == Transaction.PlayerID)
            {
                PurchaseEvent.PlayerLevel = 1; // Seria obtido do sistema de progressão
                break;
            }
        }
    }
    
    // Armazenar no histórico de compras para analytics
    PurchaseAnalyticsHistory.Add(PurchaseEvent);
    
    // Limitar histórico a 1000 entradas mais recentes
    if (PurchaseAnalyticsHistory.Num() > 1000)
    {
        PurchaseAnalyticsHistory.RemoveAt(0);
    }
    
    // Atualizar estatísticas de monetização
    UpdateMonetizationStatistics(PurchaseEvent);
    
    // Em produção, enviar para serviço de analytics externo
    // Por exemplo: Google Analytics, Firebase, ou sistema próprio
    SendAnalyticsEvent(PurchaseEvent);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Compra rastreada - Produto: %s (%s), Valor: %d %s, Jogador: %s"),
        *Transaction.ProductID, *PurchaseEvent.ProductName, Transaction.PricePaid, 
        *UEnum::GetValueAsString(Transaction.CurrencyType), *Transaction.PlayerID);

    return true;
}

TMap<FString, float> UAuracronMonetizationBridge::GetMonetizationStatistics() const
{
    TMap<FString, float> Statistics;

    // Calcular estatísticas
    Statistics.Add(TEXT("TotalTransactions"), float(ActiveTransactions.Num()));
    Statistics.Add(TEXT("OwnedProducts"), float(OwnedProducts.Num()));
    Statistics.Add(TEXT("BattlePassLevel"), float(CurrentBattlePass.CurrentLevel));
    Statistics.Add(TEXT("BattlePassProgress"), CurrentBattlePass.CurrentXP / float(CurrentBattlePass.XPToNextLevel));

    // Saldos de moeda
    for (const auto& Balance : CurrencyBalances)
    {
        FString StatName = FString::Printf(TEXT("Currency_%s"), *UEnum::GetValueAsString(Balance.Key));
        Statistics.Add(StatName, float(Balance.Value));
    }

    return Statistics;
}

// === Internal Methods ===

bool UAuracronMonetizationBridge::InitializeMonetizationSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar loja
    if (!SetupStore())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar loja"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de monetização inicializado"));

    return true;
}

bool UAuracronMonetizationBridge::SetupStore()
{
    // Configurar interface da loja
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Loja configurada"));

    return true;
}

void UAuracronMonetizationBridge::ProcessPendingTransactions(float DeltaTime)
{
    FScopeLock Lock(&MonetizationMutex);

    // Processar transações pendentes
    for (FAuracronTransaction& Transaction : ActiveTransactions)
    {
        if (Transaction.TransactionStatus == TEXT("Pending Platform Validation"))
        {
            // Simular validação da plataforma
            if (FMath::RandRange(0.0f, 1.0f) < 0.1f) // 10% chance por segundo
            {
                Transaction.TransactionStatus = TEXT("Completed");
                Transaction.bIsValidated = true;
                Transaction.bIsProcessed = true;

                // Conceder produto
                OwnedProducts.Add(Transaction.ProductID);

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Transação validada e completada: %s"), *Transaction.TransactionID);

                // Broadcast evento
                OnPurchaseCompleted.Broadcast(Transaction);
            }
        }
    }

    // Remover transações completadas antigas
    FDateTime CutoffTime = FDateTime::Now() - FTimespan::FromHours(24);
    ActiveTransactions.RemoveAll([&](const FAuracronTransaction& Transaction)
    {
        return Transaction.bIsProcessed && Transaction.TransactionTime < CutoffTime;
    });
}

bool UAuracronMonetizationBridge::ValidateProduct(const FAuracronStoreProduct& Product) const
{
    if (Product.ProductID.IsEmpty() || Product.ProductName.IsEmpty())
    {
        return false;
    }

    if (Product.PremiumPrice < 0 || Product.FreePrice < 0 || Product.RealMoneyPrice < 0)
    {
        return false;
    }

    return true;
}

void UAuracronMonetizationBridge::UpdateMonetizationStatistics(const FAuracronPurchaseAnalytics& PurchaseEvent)
{
    // Atualizar estatísticas internas de monetização
    // Em produção, isso seria persistido em banco de dados
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Estatísticas de monetização atualizadas para produto: %s"), *PurchaseEvent.ProductID);
}

void UAuracronMonetizationBridge::SendAnalyticsEvent(const FAuracronPurchaseAnalytics& PurchaseEvent)
{
    // Enviar evento para serviço de analytics externo
    // Em produção, integrar com Google Analytics, Firebase, etc.
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Evento de analytics enviado - Produto: %s, Valor: %d"), 
        *PurchaseEvent.ProductID, PurchaseEvent.PricePaid);
}
