// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Monetização Bridge Build Configuration
using UnrealBuildTool;
public class AuracronMonetizationBridge : ModuleRules
{
    public AuracronMonetizationBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine","OnlineSubsystemUtils","EOSShared","GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","CommonInput","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "KismetCompiler","BlueprintGraph",
                "Kismet",
                "ToolMenus",
                "ApplicationCore",
                "RenderCore",
                "RHI","Json","UMGEditor",
                "UMGEditor",
                "CommonUIEditor","Localization","ICU","PlatformCrypto",
                "RSA"
            }
        );
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_ONLINE_SUBSYSTEM=1");
        PublicDefinitions.Add("WITH_PURCHASE_FLOW=1");
        PublicDefinitions.Add("WITH_STORE_INTERFACE=1");
        PublicDefinitions.Add("WITH_FIREBASE=1");
        PublicDefinitions.Add("WITH_CROSS_PLATFORM_PURCHASES=1");
        PublicDefinitions.Add("WITH_BATTLE_PASS=1");
        PublicDefinitions.Add("WITH_COSMETICS=1");
        PublicDefinitions.Add("WITH_PREMIUM_CURRENCY=1");
        // Monetization features
        PublicDefinitions.Add("AURACRON_BATTLE_PASS=1");
        PublicDefinitions.Add("AURACRON_CHAMPION_PURCHASES=1");
        PublicDefinitions.Add("AURACRON_COSMETICS_STORE=1");
        PublicDefinitions.Add("AURACRON_PREMIUM_CURRENCY=1");
        PublicDefinitions.Add("AURACRON_SUBSCRIPTION_MODEL=1");
        PublicDefinitions.Add("AURACRON_SEASONAL_CONTENT=1");
        PublicDefinitions.Add("AURACRON_GIFTING_SYSTEM=1");
        PublicDefinitions.Add("AURACRON_REFERRAL_SYSTEM=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_GOOGLE_PLAY=1");
            PublicDefinitions.Add("AURACRON_GOOGLE_PLAY_BILLING=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_APP_STORE=1");
            PublicDefinitions.Add("AURACRON_APPLE_STORE_KIT=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_STEAM_STORE=1");
            PublicDefinitions.Add("AURACRON_EPIC_GAMES_STORE=1");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_MONETIZATION_DEBUG=1");
            PublicDefinitions.Add("AURACRON_PURCHASE_TESTING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MONETIZATION_DEBUG=0");
            PublicDefinitions.Add("AURACRON_PURCHASE_TESTING=0");
        }
        // Security and compliance
        PublicDefinitions.Add("AURACRON_PURCHASE_VALIDATION=1");
        PublicDefinitions.Add("AURACRON_RECEIPT_VERIFICATION=1");
        PublicDefinitions.Add("AURACRON_FRAUD_DETECTION=1");
        PublicDefinitions.Add("AURACRON_GDPR_COMPLIANCE=1");
        PublicDefinitions.Add("AURACRON_COPPA_COMPLIANCE=1");
    }
}
