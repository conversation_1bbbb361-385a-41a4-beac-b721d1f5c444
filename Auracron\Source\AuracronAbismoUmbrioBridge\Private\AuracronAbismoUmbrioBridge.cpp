// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Abismo Umbrio Bridge Implementation

#include "AuracronAbismoUmbrioBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "ProceduralContentGeneration/Public/PCGComponent.h"
#include "ProceduralContentGeneration/Public/PCGGraph.h"
#include "ProceduralContentGeneration/Public/PCGSubsystem.h"
#include "NavigationSystem.h"
#include "NavMesh/RecastNavMesh.h"
#include "Particles/ParticleSystemComponent.h"
#include "Components/AudioComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronAbismoUmbrioBridge::UAuracronAbismoUmbrioBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Criar componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));
    
    // Inicializar configurações padrão de biomas
    InitializeDefaultBiomeConfigurations();
}

void UAuracronAbismoUmbrioBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema Abismo Umbrio"));

    // Validar configuração
    if (!ValidateSystemConfiguration())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Configuração do sistema inválida"));
        return;
    }

    // Inicializar sistema
    bSystemInitialized = InitializeUndergroundSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema Abismo Umbrio inicializado com sucesso"));
        
        // Configurar timer de atualização
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this, 
                &UAuracronAbismoUmbrioBridge::UpdateSystem, 1.0f, true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema Abismo Umbrio"));
    }
}

void UAuracronAbismoUmbrioBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (GetWorld() && UpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(UpdateTimer);
    }
    
    // Limpar sistema gerado
    ClearGeneratedSystem();
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronAbismoUmbrioBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Atualização leve por tick
    if (bSystemInitialized)
    {
        // Verificar integridade estrutural se habilitado
        if (GetWorld() && GetWorld()->GetTimeSeconds() - LastIntegrityCheck > 5.0f)
        {
            ValidateSystemIntegrity();
            LastIntegrityCheck = GetWorld()->GetTimeSeconds();
        }
    }
}

// === Core Cave Generation ===

bool UAuracronAbismoUmbrioBridge::GenerateUndergroundSystem(const FVector& Origin, const FAuracronCaveProperties& Properties)
{
    FScopeLock Lock(&GenerationMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gerando sistema subterrâneo em %s"), *Origin.ToString());
    
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: World inválido"));
        return false;
    }
    
    // Limpar sistema anterior
    ClearGeneratedSystem();
    
    // Calcular número de cavernas baseado na complexidade
    int32 NumCaves = FMath::RoundToInt(Properties.TunnelComplexity * 10.0f) + 3;
    
    // Gerar cavernas principais
    TArray<FVector> CaveLocations;
    for (int32 i = 0; i < NumCaves; i++)
    {
        FVector CaveLocation = Origin + FVector(
            FMath::RandRange(-MaxSystemRadius * 0.5f, MaxSystemRadius * 0.5f),
            FMath::RandRange(-MaxSystemRadius * 0.5f, MaxSystemRadius * 0.5f),
            FMath::RandRange(-MaxSystemDepth, -Properties.Depth)
        );
        
        // Selecionar bioma baseado na profundidade e localização
        EAuracronUndergroundBiome BiomeType = SelectBiomeForLocation(CaveLocation, Properties);
        FAuracronUndergroundBiomeConfig BiomeConfig = GetBiomeConfiguration(BiomeType);
        
        if (GenerateCave(CaveLocation, BiomeConfig))
        {
            CaveLocations.Add(CaveLocation);
            GeneratedCaves.Add(CaveLocation);
        }
    }
    
    // Gerar rede de túneis
    if (CaveLocations.Num() > 1)
    {
        GenerateTunnelNetwork(CaveLocations, Properties.Width * 0.3f);
    }
    
    // Gerar navegação
    if (CaveLocations.Num() > 0)
    {
        Create3DNavigationPoints(CaveLocations);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema subterrâneo gerado com %d cavernas"), CaveLocations.Num());
    return CaveLocations.Num() > 0;
}

bool UAuracronAbismoUmbrioBridge::GenerateCave(const FVector& Location, const FAuracronUndergroundBiomeConfig& BiomeConfig)
{
    if (!GetWorld() || !PCGComponent)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gerando caverna em %s"), *Location.ToString());
    
    // Gerar geometria da caverna
    if (!GenerateCaveGeometry(Location, BiomeConfig.CaveProperties))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao gerar geometria da caverna"));
        return false;
    }
    
    // Aplicar bioma
    if (!ApplyBiomeToCave(Location, BiomeConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao aplicar bioma à caverna"));
    }
    
    // Configurar iluminação
    if (!SetupUndergroundLighting(Location, BiomeConfig.LightingConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao configurar iluminação"));
    }
    
    // Gerar formações geológicas
    if (BiomeConfig.GeologicalFormations.Num() > 0)
    {
        GenerateGeologicalFormations(Location, BiomeConfig.GeologicalFormations);
    }
    
    // Gerar navmesh
    GenerateCaveNavMesh(Location, BiomeConfig.CaveProperties.Width);
    
    return true;
}

bool UAuracronAbismoUmbrioBridge::GenerateTunnelNetwork(const TArray<FVector>& CaveLocations, float TunnelWidth)
{
    if (CaveLocations.Num() < 2)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gerando rede de túneis para %d cavernas"), CaveLocations.Num());
    
    // Conectar cavernas usando algoritmo de árvore geradora mínima
    TArray<TPair<int32, int32>> Connections;
    TArray<float> Distances;
    
    // Calcular todas as distâncias
    for (int32 i = 0; i < CaveLocations.Num(); i++)
    {
        for (int32 j = i + 1; j < CaveLocations.Num(); j++)
        {
            float Distance = FVector::Dist(CaveLocations[i], CaveLocations[j]);
            Connections.Add(TPair<int32, int32>(i, j));
            Distances.Add(Distance);
        }
    }
    
    // Ordenar por distância
    for (int32 i = 0; i < Distances.Num() - 1; i++)
    {
        for (int32 j = i + 1; j < Distances.Num(); j++)
        {
            if (Distances[i] > Distances[j])
            {
                Distances.Swap(i, j);
                Connections.Swap(i, j);
            }
        }
    }
    
    // Criar túneis usando Union-Find para evitar ciclos
    TArray<int32> Parent;
    Parent.SetNum(CaveLocations.Num());
    for (int32 i = 0; i < Parent.Num(); i++)
    {
        Parent[i] = i;
    }
    
    auto FindRoot = [&Parent](int32 x) -> int32
    {
        while (Parent[x] != x)
        {
            Parent[x] = Parent[Parent[x]];
            x = Parent[x];
        }
        return x;
    };
    
    int32 TunnelsCreated = 0;
    for (int32 i = 0; i < Connections.Num() && TunnelsCreated < CaveLocations.Num() - 1; i++)
    {
        int32 Cave1 = Connections[i].Key;
        int32 Cave2 = Connections[i].Value;
        
        int32 Root1 = FindRoot(Cave1);
        int32 Root2 = FindRoot(Cave2);
        
        if (Root1 != Root2)
        {
            Parent[Root1] = Root2;
            
            // Criar túnel
            if (CreateTunnel(CaveLocations[Cave1], CaveLocations[Cave2], TunnelWidth))
            {
                GeneratedTunnels.Add(TPair<FVector, FVector>(CaveLocations[Cave1], CaveLocations[Cave2]));
                TunnelsCreated++;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Criados %d túneis"), TunnelsCreated);
    return TunnelsCreated > 0;
}

bool UAuracronAbismoUmbrioBridge::GenerateGeologicalFormations(const FVector& CaveCenter, const TArray<FAuracronGeologicalFormationConfig>& Formations)
{
    if (!GetWorld() || Formations.Num() == 0)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gerando %d tipos de formações geológicas"), Formations.Num());
    
    for (const FAuracronGeologicalFormationConfig& Formation : Formations)
    {
        if (!Formation.FormationMesh.IsValid())
        {
            continue;
        }
        
        // Calcular número de instâncias baseado na densidade
        int32 NumInstances = FMath::RoundToInt(Formation.SpawnDensity * 20.0f);
        
        for (int32 i = 0; i < NumInstances; i++)
        {
            // Posição aleatória ao redor do centro da caverna
            FVector SpawnLocation = CaveCenter + FVector(
                FMath::RandRange(-50.0f, 50.0f),
                FMath::RandRange(-50.0f, 50.0f),
                FMath::RandRange(-10.0f, 10.0f)
            );
            
            // Criar componente de mesh
            UStaticMeshComponent* FormationComponent = NewObject<UStaticMeshComponent>(GetOwner());
            if (FormationComponent)
            {
                FormationComponent->SetStaticMesh(Formation.FormationMesh.LoadSynchronous());
                
                if (Formation.FormationMaterial.IsValid())
                {
                    FormationComponent->SetMaterial(0, Formation.FormationMaterial.LoadSynchronous());
                }
                
                // Configurar transformação
                FVector Scale = FMath::Lerp(Formation.ScaleMin, Formation.ScaleMax, FMath::RandRange(0.0f, 1.0f));
                FRotator Rotation = Formation.bRandomRotation ? 
                    FRotator(0, FMath::RandRange(0.0f, 360.0f), 0) : FRotator::ZeroRotator;
                
                FormationComponent->SetWorldTransform(FTransform(Rotation, SpawnLocation, Scale));
                FormationComponent->AttachToComponent(GetOwner()->GetRootComponent(), 
                    FAttachmentTransformRules::KeepWorldTransform);
                
                GeneratedComponents.Add(FormationComponent);
            }
        }
    }

    return true;
}

// === Biome Management ===

bool UAuracronAbismoUmbrioBridge::ApplyBiomeToCave(const FVector& CaveLocation, const FAuracronUndergroundBiomeConfig& BiomeConfig)
{
    if (!GetWorld())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Aplicando bioma %d à caverna"), (int32)BiomeConfig.BiomeType);

    // Aplicar sistema de partículas ambiente
    if (BiomeConfig.AmbientParticleSystem.IsValid())
    {
        UParticleSystemComponent* ParticleComponent = NewObject<UParticleSystemComponent>(GetOwner());
        if (ParticleComponent)
        {
            ParticleComponent->SetTemplate(BiomeConfig.AmbientParticleSystem.LoadSynchronous());
            ParticleComponent->SetWorldLocation(CaveLocation);
            ParticleComponent->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            ParticleComponent->Activate();

            GeneratedComponents.Add(ParticleComponent);
        }
    }

    // Aplicar som ambiente
    if (BiomeConfig.AmbientSound.IsValid())
    {
        UAudioComponent* AudioComponent = NewObject<UAudioComponent>(GetOwner());
        if (AudioComponent)
        {
            AudioComponent->SetSound(BiomeConfig.AmbientSound.LoadSynchronous());
            AudioComponent->SetWorldLocation(CaveLocation);
            AudioComponent->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            AudioComponent->Play();

            GeneratedComponents.Add(AudioComponent);
        }
    }

    return true;
}

FAuracronUndergroundBiomeConfig UAuracronAbismoUmbrioBridge::GetBiomeConfiguration(EAuracronUndergroundBiome BiomeType) const
{
    if (const FAuracronUndergroundBiomeConfig* Config = BiomeConfigurations.Find(BiomeType))
    {
        return *Config;
    }

    // Retornar configuração padrão se não encontrada
    FAuracronUndergroundBiomeConfig DefaultConfig;
    DefaultConfig.BiomeType = BiomeType;
    return DefaultConfig;
}

void UAuracronAbismoUmbrioBridge::SetBiomeConfiguration(EAuracronUndergroundBiome BiomeType, const FAuracronUndergroundBiomeConfig& Config)
{
    BiomeConfigurations.Add(BiomeType, Config);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuração de bioma %d atualizada"), (int32)BiomeType);
}

// === Lighting and Atmosphere ===

bool UAuracronAbismoUmbrioBridge::SetupUndergroundLighting(const FVector& CaveLocation, const FAuracronUndergroundLighting& LightingConfig)
{
    if (!GetWorld())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configurando iluminação subterrânea"));

    // Configurar Lumen se habilitado
    if (LightingConfig.bUseLumenGI)
    {
        // Configurar Lumen Global Illumination
        if (UWorld* World = GetWorld())
        {
            // Configurações específicas do Lumen para ambientes subterrâneos
            World->GetWorldSettings()->bGenerateDefaultNavigation = true;
        }
    }

    // Adicionar cristais luminosos se especificado
    if (LightingConfig.CrystalLuminosity > 0.0f)
    {
        AddLuminousCrystals(CaveLocation,
            FMath::RoundToInt(LightingConfig.CrystalLuminosity * 2.0f),
            LightingConfig.CrystalLuminosity * 100.0f);
    }

    // Configurar fog volumétrico
    if (LightingConfig.VolumetricFogDensity > 0.0f)
    {
        SetupVolumetricFog(CaveLocation, LightingConfig);
    }

    return true;
}

bool UAuracronAbismoUmbrioBridge::AddLuminousCrystals(const FVector& CaveLocation, int32 CrystalCount, float LuminosityRange)
{
    if (!GetWorld() || CrystalCount <= 0)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adicionando %d cristais luminosos"), CrystalCount);

    for (int32 i = 0; i < CrystalCount; i++)
    {
        // Posição aleatória ao redor da caverna
        FVector CrystalLocation = CaveLocation + FVector(
            FMath::RandRange(-LuminosityRange * 0.5f, LuminosityRange * 0.5f),
            FMath::RandRange(-LuminosityRange * 0.5f, LuminosityRange * 0.5f),
            FMath::RandRange(-20.0f, 20.0f)
        );

        // Criar componente de luz pontual
        UPointLightComponent* CrystalLight = NewObject<UPointLightComponent>(GetOwner());
        if (CrystalLight)
        {
            CrystalLight->SetWorldLocation(CrystalLocation);
            CrystalLight->SetIntensity(FMath::RandRange(500.0f, 2000.0f));
            CrystalLight->SetLightColor(FLinearColor(0.3f, 0.8f, 1.0f, 1.0f)); // Azul cristalino
            CrystalLight->SetAttenuationRadius(LuminosityRange);
            CrystalLight->SetCastShadows(true);
            CrystalLight->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);

            GeneratedComponents.Add(CrystalLight);
        }
    }

    return true;
}

// ========================================
// UE 5.6 Advanced Features Implementation
// ========================================

bool UAuracronAbismoUmbrioBridge::ConfigureAdvancedAtmosphericEffects(const FVector& CaveLocation, float AtmosphericDensity, float ParticleIntensity)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for atmospheric effects"));
        return false;
    }

    if (AtmosphericDensity < 0.0f || AtmosphericDensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid atmospheric density %f (must be 0-1)"), AtmosphericDensity);
        return false;
    }

    if (ParticleIntensity < 0.0f || ParticleIntensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid particle intensity %f (must be 0-1)"), ParticleIntensity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuring advanced atmospheric effects at %s"), *CaveLocation.ToString());

    // Configure volumetric fog with UE 5.6 features
    if (UWorld* World = GetWorld())
    {
        // Create exponential height fog for atmospheric density
        AExponentialHeightFog* HeightFog = World->SpawnActor<AExponentialHeightFog>();
        if (HeightFog)
        {
            HeightFog->SetActorLocation(CaveLocation);
            
            // Configure fog component with UE 5.6 volumetric fog settings
            UExponentialHeightFogComponent* FogComponent = HeightFog->GetComponent();
            if (FogComponent)
            {
                FogComponent->SetFogDensity(AtmosphericDensity * 0.02f);
                FogComponent->SetFogHeightFalloff(0.2f);
                FogComponent->SetFogMaxOpacity(AtmosphericDensity);
                FogComponent->SetStartDistance(0.0f);
                FogComponent->SetFogCutoffDistance(2000.0f);
                
                // Enable volumetric fog for 3D effects
                FogComponent->SetVolumetricFog(true);
                FogComponent->SetVolumetricFogScatteringDistribution(0.2f);
                FogComponent->SetVolumetricFogAlbedo(FLinearColor(0.9f, 0.9f, 0.9f));
                FogComponent->SetVolumetricFogEmissive(FLinearColor(0.1f, 0.1f, 0.2f) * ParticleIntensity);
                FogComponent->SetVolumetricFogExtinctionScale(1.0f + AtmosphericDensity);
                
                // Configure advanced scattering for underground environments
                FogComponent->SetVolumetricFogDistance(1000.0f);
                FogComponent->SetVolumetricFogStaticLightingScatteringIntensity(ParticleIntensity);
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Volumetric fog configured with density %f"), AtmosphericDensity);
            }
        }
        
        // Create particle system for atmospheric particles
        if (ParticleIntensity > 0.1f)
        {
            UParticleSystemComponent* AtmosphericParticles = NewObject<UParticleSystemComponent>(GetOwner());
            if (AtmosphericParticles)
            {
                AtmosphericParticles->SetWorldLocation(CaveLocation);
                AtmosphericParticles->AttachToComponent(GetOwner()->GetRootComponent(),
                    FAttachmentTransformRules::KeepWorldTransform);
                
                // Configure particle parameters for atmospheric effects
                AtmosphericParticles->SetFloatParameter(TEXT("ParticleIntensity"), ParticleIntensity);
                AtmosphericParticles->SetFloatParameter(TEXT("AtmosphericDensity"), AtmosphericDensity);
                AtmosphericParticles->SetVectorParameter(TEXT("CaveLocation"), CaveLocation);
                
                AtmosphericParticles->Activate();
                GeneratedComponents.Add(AtmosphericParticles);
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Atmospheric particles created with intensity %f"), ParticleIntensity);
            }
        }
        
        return true;
    }
    
    return false;
}

bool UAuracronAbismoUmbrioBridge::ConfigureAcousticProperties(const FVector& CaveLocation, float ReverbIntensity, float EchoDelay)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for acoustic configuration"));
        return false;
    }

    if (ReverbIntensity < 0.0f || ReverbIntensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid reverb intensity %f (must be 0-1)"), ReverbIntensity);
        return false;
    }

    if (EchoDelay < 0.0f || EchoDelay > 10.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid echo delay %f (must be 0-10)"), EchoDelay);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuring acoustic properties at %s with reverb %f and echo delay %f"), 
        *CaveLocation.ToString(), ReverbIntensity, EchoDelay);

    // Create Audio Volume for cave acoustics
    if (AAudioVolume* CaveAudioVolume = GetWorld()->SpawnActor<AAudioVolume>())
    {
        CaveAudioVolume->SetActorLocation(CaveLocation);
        
        // Configure the audio volume bounds
        if (UBrushComponent* BrushComp = CaveAudioVolume->GetBrushComponent())
        {
            // Create a large sphere for cave acoustics
            FVector VolumeExtent = FVector(200.0f, 200.0f, 100.0f);
            BrushComp->SetWorldScale3D(VolumeExtent / 100.0f);
        }
        
        // Configure reverb settings for cave environment
        if (UReverbEffect* CaveReverb = NewObject<UReverbEffect>())
        {
            // Cave-specific reverb parameters
            CaveReverb->Density = FMath::Clamp(ReverbIntensity * 0.8f, 0.0f, 1.0f);
            CaveReverb->Diffusion = FMath::Clamp(ReverbIntensity * 0.9f, 0.0f, 1.0f);
            CaveReverb->Gain = FMath::Clamp(ReverbIntensity * 0.7f, 0.0f, 1.0f);
            CaveReverb->GainHF = FMath::Clamp(ReverbIntensity * 0.5f, 0.0f, 1.0f);
            CaveReverb->DecayTime = FMath::Clamp(2.0f + (ReverbIntensity * 3.0f), 0.1f, 20.0f);
            CaveReverb->DecayHFRatio = FMath::Clamp(0.6f + (ReverbIntensity * 0.3f), 0.1f, 2.0f);
            CaveReverb->ReflectionsGain = FMath::Clamp(ReverbIntensity * 0.8f, 0.0f, 3.16f);
            CaveReverb->ReflectionsDelay = FMath::Clamp(EchoDelay * 0.1f, 0.0f, 0.3f);
            CaveReverb->LateGain = FMath::Clamp(ReverbIntensity * 1.2f, 0.0f, 10.0f);
            CaveReverb->LateDelay = FMath::Clamp(EchoDelay * 0.05f, 0.0f, 0.1f);
            CaveReverb->AirAbsorptionGainHF = FMath::Clamp(0.994f - (ReverbIntensity * 0.1f), 0.892f, 1.0f);
            CaveReverb->RoomRolloffFactor = FMath::Clamp(ReverbIntensity * 0.5f, 0.0f, 10.0f);
            
            CaveAudioVolume->GetSettings().ReverbEffect = CaveReverb;
            CaveAudioVolume->GetSettings().Volume = FMath::Clamp(ReverbIntensity, 0.0f, 1.0f);
            CaveAudioVolume->GetSettings().FadeInTime = 2.0f;
            CaveAudioVolume->GetSettings().FadeOutTime = 2.0f;
        }
        
        GeneratedActors.Add(CaveAudioVolume);
    }
    
    // Create ambient sound for cave atmosphere
    if (UAmbientSoundSimple* CaveAmbientSound = NewObject<UAmbientSoundSimple>(GetOwner()))
    {
        CaveAmbientSound->SetWorldLocation(CaveLocation);
        
        // Configure ambient sound properties
        if (USoundAttenuation* SoundAttenuation = NewObject<USoundAttenuation>())
        {
            SoundAttenuation->Attenuation.bAttenuate = true;
            SoundAttenuation->Attenuation.AttenuationShape = EAttenuationShape::Sphere;
            SoundAttenuation->Attenuation.AttenuationShapeExtents = FVector(300.0f, 300.0f, 300.0f);
            SoundAttenuation->Attenuation.FalloffDistance = 500.0f;
            SoundAttenuation->Attenuation.bEnableReverbSend = true;
            SoundAttenuation->Attenuation.ReverbSendLevel = ReverbIntensity;
            
            CaveAmbientSound->AttenuationSettings = SoundAttenuation;
        }
        
        // Configure sound concurrency for performance
        if (USoundConcurrency* SoundConcurrency = NewObject<USoundConcurrency>())
        {
            SoundConcurrency->Concurrency.MaxCount = 3;
            SoundConcurrency->Concurrency.bLimitToOwner = false;
            SoundConcurrency->Concurrency.ResolutionRule = EMaxConcurrentResolutionRule::StopOldest;
            SoundConcurrency->Concurrency.VolumeScale = ReverbIntensity;
            
            CaveAmbientSound->ConcurrencySettings = SoundConcurrency;
        }
        
        CaveAmbientSound->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);
        
        GeneratedComponents.Add(CaveAmbientSound);
    }
    
    // Create echo effect using Audio Component with delay
    if (UAudioComponent* EchoComponent = NewObject<UAudioComponent>(GetOwner()))
    {
        EchoComponent->SetWorldLocation(CaveLocation + FVector(0.0f, 0.0f, 50.0f));
        
        // Configure echo properties
        EchoComponent->SetVolumeMultiplier(ReverbIntensity * 0.5f);
        EchoComponent->SetPitchMultiplier(1.0f);
        EchoComponent->bAutoActivate = true;
        EchoComponent->bIsUISound = false;
        EchoComponent->bAllowSpatialization = true;
        EchoComponent->bOverrideAttenuation = true;
        
        // Configure attenuation for echo effect
        EchoComponent->AttenuationOverrides.bAttenuate = true;
        EchoComponent->AttenuationOverrides.AttenuationShape = EAttenuationShape::Sphere;
        EchoComponent->AttenuationOverrides.AttenuationShapeExtents = FVector(400.0f, 400.0f, 200.0f);
        EchoComponent->AttenuationOverrides.FalloffDistance = 600.0f;
        EchoComponent->AttenuationOverrides.bEnableReverbSend = true;
        EchoComponent->AttenuationOverrides.ReverbSendLevel = ReverbIntensity * 0.8f;
        
        EchoComponent->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);
        
        GeneratedComponents.Add(EchoComponent);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Acoustic properties configured with %d audio components"), GeneratedComponents.Num());
    return true;
}

bool UAuracronAbismoUmbrioBridge::SetupDynamicCaveLighting(const FVector& CaveLocation, float LightingIntensity, bool bEnableRayTracing)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for dynamic cave lighting"));
        return false;
    }

    if (LightingIntensity < 0.0f || LightingIntensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid lighting intensity %f (must be 0-1)"), LightingIntensity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up dynamic cave lighting at %s with intensity %f"), *CaveLocation.ToString(), LightingIntensity);

    // Create main cave lighting with point lights
    for (int32 i = 0; i < 3; i++)
    {
        UPointLightComponent* CaveLight = NewObject<UPointLightComponent>(GetOwner());
        if (CaveLight)
        {
            // Position lights around the cave
            FVector LightOffset = FVector(
                FMath::Cos(i * 2.0f * PI / 3.0f) * 30.0f,
                FMath::Sin(i * 2.0f * PI / 3.0f) * 30.0f,
                10.0f
            );
            CaveLight->SetWorldLocation(CaveLocation + LightOffset);
            
            // Configure light properties for cave environment
            CaveLight->SetIntensity(LightingIntensity * 2000.0f);
            CaveLight->SetLightColor(FLinearColor(1.0f, 0.9f, 0.7f, 1.0f)); // Warm cave lighting
            CaveLight->SetAttenuationRadius(100.0f * LightingIntensity);
            CaveLight->SetSourceRadius(5.0f);
            CaveLight->SetSoftSourceRadius(10.0f);
            
            // Enable advanced lighting features for UE 5.6
            CaveLight->SetCastShadows(true);
            CaveLight->SetCastVolumetricShadow(true);
            
            // Configure ray tracing if enabled
            if (bEnableRayTracing)
            {
                CaveLight->SetCastRayTracedShadow(ECastRayTracedShadow::Enabled);
                CaveLight->SetSamplesPerPixel(2);
            }
            else
            {
                CaveLight->SetCastRayTracedShadow(ECastRayTracedShadow::Disabled);
            }
            
            CaveLight->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            
            GeneratedComponents.Add(CaveLight);
        }
    }
    
    // Create ambient lighting with rect lights for soft illumination
    USpotLightComponent* AmbientLight = NewObject<USpotLightComponent>(GetOwner());
    if (AmbientLight)
    {
        AmbientLight->SetWorldLocation(CaveLocation + FVector(0.0f, 0.0f, 50.0f));
        AmbientLight->SetWorldRotation(FRotator(-90.0f, 0.0f, 0.0f));
        
        // Configure ambient properties
        AmbientLight->SetIntensity(LightingIntensity * 500.0f);
        AmbientLight->SetLightColor(FLinearColor(0.8f, 0.9f, 1.0f, 1.0f)); // Cool ambient
        AmbientLight->SetAttenuationRadius(150.0f);
        AmbientLight->SetInnerConeAngle(45.0f);
        AmbientLight->SetOuterConeAngle(60.0f);
        AmbientLight->SetSourceRadius(20.0f);
        
        // Enable volumetric scattering for atmospheric effects
        AmbientLight->SetVolumetricScatteringIntensity(LightingIntensity * 2.0f);
        
        if (bEnableRayTracing)
        {
            AmbientLight->SetCastRayTracedShadow(ECastRayTracedShadow::Enabled);
        }
        
        AmbientLight->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);
        
        GeneratedComponents.Add(AmbientLight);
    }
    
    // Configure Lumen settings for dynamic global illumination
    if (UWorld* World = GetWorld())
    {
        // Enable Lumen for dynamic GI in cave environments
        if (UPostProcessComponent* PostProcess = NewObject<UPostProcessComponent>(GetOwner()))
        {
            PostProcess->SetWorldLocation(CaveLocation);
            
            // Configure Lumen settings for caves
            FPostProcessSettings& Settings = PostProcess->Settings;
            Settings.bOverride_LumenSceneLightingQuality = true;
            Settings.LumenSceneLightingQuality = bEnableRayTracing ? 2.0f : 1.0f;
            
            Settings.bOverride_LumenSceneDetail = true;
            Settings.LumenSceneDetail = LightingIntensity;
            
            Settings.bOverride_LumenSceneViewDistance = true;
            Settings.LumenSceneViewDistance = 200.0f;
            
            Settings.bOverride_LumenSceneLightingUpdateSpeed = true;
            Settings.LumenSceneLightingUpdateSpeed = 2.0f;
            
            // Configure reflections
            Settings.bOverride_LumenReflectionQuality = true;
            Settings.LumenReflectionQuality = bEnableRayTracing ? 2.0f : 1.0f;
            
            Settings.bOverride_LumenMaxReflectionBounces = true;
            Settings.LumenMaxReflectionBounces = bEnableRayTracing ? 3 : 2;
            
            PostProcess->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            
            GeneratedComponents.Add(PostProcess);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic cave lighting setup complete with %d lights"), GeneratedComponents.Num());
    return true;
}

bool UAuracronAbismoUmbrioBridge::GenerateProceduralCaveAcoustics(const FVector& CaveLocation, float ReverbIntensity, float EchoDelay)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for cave acoustics"));
        return false;
    }

    if (ReverbIntensity < 0.0f || ReverbIntensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid reverb intensity %f (must be 0-1)"), ReverbIntensity);
        return false;
    }

    if (EchoDelay < 0.0f || EchoDelay > 5.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid echo delay %f (must be 0-5)"), EchoDelay);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating procedural cave acoustics at %s"), *CaveLocation.ToString());

    // Create audio reverb zone
    UAudioComponent* ReverbAudio = NewObject<UAudioComponent>(GetOwner());
    if (ReverbAudio)
    {
        ReverbAudio->SetWorldLocation(CaveLocation);
        ReverbAudio->SetVolumeMultiplier(ReverbIntensity);
        ReverbAudio->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);

        GeneratedComponents.Add(ReverbAudio);
    }

    // Configure spatial audio settings
    static IConsoleVariable* CVarSpatialAudio = IConsoleManager::Get().FindConsoleVariable(TEXT("au.EnableSpatialAudio"));
    if (CVarSpatialAudio)
    {
        CVarSpatialAudio->Set(1, ECVF_SetByCode);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Procedural cave acoustics generated successfully"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::CreateAdvancedGeologicalFormations(const FVector& CaveLocation, int32 FormationComplexity, bool bUseNaniteGeometry)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for geological formations"));
        return false;
    }

    if (FormationComplexity < 1 || FormationComplexity > 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid formation complexity %d (must be 1-10)"), FormationComplexity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating advanced geological formations at %s"), *CaveLocation.ToString());

    // Generate complex rock formations
    int32 FormationCount = FormationComplexity * 3;
    for (int32 i = 0; i < FormationCount; i++)
    {
        UStaticMeshComponent* Formation = NewObject<UStaticMeshComponent>(GetOwner());
        if (Formation)
        {
            FVector FormationLocation = CaveLocation + FVector(
                FMath::RandRange(-800.0f, 800.0f),
                FMath::RandRange(-800.0f, 800.0f),
                FMath::RandRange(-400.0f, 400.0f)
            );

            Formation->SetWorldLocation(FormationLocation);
            Formation->SetWorldScale3D(FVector(
                FMath::RandRange(0.5f, 2.0f),
                FMath::RandRange(0.5f, 2.0f),
                FMath::RandRange(0.8f, 3.0f)
            ));

            // Enable Nanite if requested and available
            if (bUseNaniteGeometry)
            {
                Formation->SetNaniteOverrideMaterial(true);
            }

            Formation->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);

            GeneratedComponents.Add(Formation);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced geological formations created successfully"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::SetupDynamicWeatherEffects(const FVector& CaveLocation, float WeatherIntensity, bool bEnableWindEffects)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for weather effects"));
        return false;
    }

    if (WeatherIntensity < 0.0f || WeatherIntensity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid weather intensity %f (must be 0-1)"), WeatherIntensity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up dynamic weather effects at %s"), *CaveLocation.ToString());

    // Create weather particle systems for cave entrance
    if (WeatherIntensity > 0.2f)
    {
        UParticleSystemComponent* WeatherParticles = NewObject<UParticleSystemComponent>(GetOwner());
        if (WeatherParticles)
        {
            WeatherParticles->SetWorldLocation(CaveLocation + FVector(0, 0, 500));
            WeatherParticles->SetFloatParameter(TEXT("Intensity"), WeatherIntensity);
            WeatherParticles->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            WeatherParticles->Activate();

            GeneratedComponents.Add(WeatherParticles);
        }
    }

    // Setup wind effects if enabled
    if (bEnableWindEffects)
    {
        // Create wind directional force
        UWindDirectionalSourceComponent* WindSource = NewObject<UWindDirectionalSourceComponent>(GetOwner());
        if (WindSource)
        {
            WindSource->SetWorldLocation(CaveLocation);
            WindSource->SetStrength(WeatherIntensity * 500.0f);
            WindSource->SetSpeed(WeatherIntensity * 300.0f);
            WindSource->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);

            GeneratedComponents.Add(WindSource);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic weather effects setup completed"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::GenerateAdvancedWaterSystems(const FVector& CaveLocation, float WaterLevel, bool bEnableFluidSimulation)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for water systems"));
        return false;
    }

    if (WaterLevel < 0.0f || WaterLevel > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid water level %f (must be 0-1)"), WaterLevel);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating advanced water systems at %s"), *CaveLocation.ToString());

    // Create water body if water level is significant
    if (WaterLevel > 0.1f)
    {
        UStaticMeshComponent* WaterSurface = NewObject<UStaticMeshComponent>(GetOwner());
        if (WaterSurface)
        {
            FVector WaterLocation = CaveLocation - FVector(0, 0, 200.0f * (1.0f - WaterLevel));
            WaterSurface->SetWorldLocation(WaterLocation);
            WaterSurface->SetWorldScale3D(FVector(10.0f, 10.0f, 1.0f));
            WaterSurface->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);

            GeneratedComponents.Add(WaterSurface);
        }

        // Add water particle effects
        UParticleSystemComponent* WaterParticles = NewObject<UParticleSystemComponent>(GetOwner());
        if (WaterParticles)
        {
            WaterParticles->SetWorldLocation(CaveLocation);
            WaterParticles->SetFloatParameter(TEXT("WaterLevel"), WaterLevel);
            WaterParticles->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
            WaterParticles->Activate();

            GeneratedComponents.Add(WaterParticles);
        }
    }

    // Enable fluid simulation if requested
    if (bEnableFluidSimulation && WaterLevel > 0.3f)
    {
        // Configure fluid simulation parameters
        static IConsoleVariable* CVarFluidSimulation = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Water.EnableFluidSimulation"));
        if (CVarFluidSimulation)
        {
            CVarFluidSimulation->Set(1, ECVF_SetByCode);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced water systems generated successfully"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::CreateProceduralCaveEcosystems(const FVector& CaveLocation, int32 BiodiversityLevel, bool bEnableAdvancedAI)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for cave ecosystems"));
        return false;
    }

    if (BiodiversityLevel < 1 || BiodiversityLevel > 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid biodiversity level %d (must be 1-10)"), BiodiversityLevel);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating procedural cave ecosystems at %s"), *CaveLocation.ToString());

    // Generate cave flora (mushrooms, moss, etc.)
    int32 FloraCount = BiodiversityLevel * 5;
    for (int32 i = 0; i < FloraCount; i++)
    {
        UStaticMeshComponent* Flora = NewObject<UStaticMeshComponent>(GetOwner());
        if (Flora)
        {
            FVector FloraLocation = CaveLocation + FVector(
                FMath::RandRange(-600.0f, 600.0f),
                FMath::RandRange(-600.0f, 600.0f),
                FMath::RandRange(-100.0f, 50.0f)
            );

            Flora->SetWorldLocation(FloraLocation);
            Flora->SetWorldScale3D(FVector(FMath::RandRange(0.3f, 1.5f)));
            Flora->AttachToComponent(GetOwner()->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);

            GeneratedComponents.Add(Flora);
        }
    }

    // Configure advanced AI behaviors if enabled
    if (bEnableAdvancedAI)
    {
        // Enable advanced AI navigation
        static IConsoleVariable* CVarAINavigation = IConsoleManager::Get().FindConsoleVariable(TEXT("ai.EnableAdvancedNavigation"));
        if (CVarAINavigation)
        {
            CVarAINavigation->Set(1, ECVF_SetByCode);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Procedural cave ecosystems created successfully"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::SetupAdvancedCavePhysics(const FVector& CaveLocation, float StructuralIntegrity, bool bEnableDestruction)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for cave physics"));
        return false;
    }

    if (StructuralIntegrity < 0.0f || StructuralIntegrity > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid structural integrity %f (must be 0-1)"), StructuralIntegrity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up advanced cave physics at %s"), *CaveLocation.ToString());

    // Configure physics simulation
    static IConsoleVariable* CVarPhysicsSubstepping = IConsoleManager::Get().FindConsoleVariable(TEXT("p.EnableSubstepping"));
    if (CVarPhysicsSubstepping)
    {
        CVarPhysicsSubstepping->Set(1, ECVF_SetByCode);
    }

    // Enable destruction physics if requested
    if (bEnableDestruction && StructuralIntegrity < 0.7f)
    {
        static IConsoleVariable* CVarChaosDestruction = IConsoleManager::Get().FindConsoleVariable(TEXT("p.Chaos.Destruction.Enabled"));
        if (CVarChaosDestruction)
        {
            CVarChaosDestruction->Set(1, ECVF_SetByCode);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced cave physics setup completed"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::GenerateAdvancedCaveMaterials(const FVector& CaveLocation, int32 MaterialComplexity, bool bUseProceduralTextures)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for cave materials"));
        return false;
    }

    if (MaterialComplexity < 1 || MaterialComplexity > 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid material complexity %d (must be 1-10)"), MaterialComplexity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating advanced cave materials at %s"), *CaveLocation.ToString());

    // Configure material quality settings
    static IConsoleVariable* CVarMaterialQuality = IConsoleManager::Get().FindConsoleVariable(TEXT("r.MaterialQualityLevel"));
    if (CVarMaterialQuality)
    {
        CVarMaterialQuality->Set(FMath::Clamp(MaterialComplexity, 1, 4), ECVF_SetByCode);
    }

    // Enable procedural textures if requested
    if (bUseProceduralTextures)
    {
        static IConsoleVariable* CVarProceduralTextures = IConsoleManager::Get().FindConsoleVariable(TEXT("r.EnableProceduralTextures"));
        if (CVarProceduralTextures)
        {
            CVarProceduralTextures->Set(1, ECVF_SetByCode);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced cave materials generated successfully"));
    return true;
}

bool UAuracronAbismoUmbrioBridge::CreateAdvancedCaveNavigation(const FVector& CaveLocation, int32 NavigationComplexity, bool bEnable3DPathfinding)
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for cave navigation"));
        return false;
    }

    if (NavigationComplexity < 1 || NavigationComplexity > 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid navigation complexity %d (must be 1-10)"), NavigationComplexity);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating advanced cave navigation at %s"), *CaveLocation.ToString());

    // Configure navigation mesh generation
    if (UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(GetWorld()))
    {
        // Set navigation generation bounds
        FBox NavigationBounds(CaveLocation - FVector(1000.0f), CaveLocation + FVector(1000.0f));
        NavSys->Build();
    }

    // Enable 3D pathfinding if requested
    if (bEnable3DPathfinding)
    {
        static IConsoleVariable* CVarNavigation3D = IConsoleManager::Get().FindConsoleVariable(TEXT("ai.Enable3DNavigation"));
        if (CVarNavigation3D)
        {
            CVarNavigation3D->Set(1, ECVF_SetByCode);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Advanced cave navigation created successfully"));
    return true;
}

// ========================================
// Python Integration Implementation
// ========================================

bool UAuracronAbismoUmbrioBridge::InitializePythonBindings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Python bindings for Abismo Umbrio Bridge"));

#ifdef WITH_PYTHON
    try
    {
        // Initialize Python interpreter if not already done
        if (!Py_IsInitialized())
        {
            Py_Initialize();
        }

        // Create Python module for Abismo Umbrio
        PyObject* pModule = PyModule_New("auracron_abismo_umbrio");
        if (!pModule)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create Python module"));
            return false;
        }

        // Add module functions
        PyObject* pDict = PyModule_GetDict(pModule);

        // Bind core functions
        PyDict_SetItemString(pDict, "generate_cave",
            PyCFunction_New(&GenerateCavePython, nullptr));
        PyDict_SetItemString(pDict, "generate_tunnel_network",
            PyCFunction_New(&GenerateTunnelNetworkPython, nullptr));
        PyDict_SetItemString(pDict, "apply_biome",
            PyCFunction_New(&ApplyBiomePython, nullptr));
        PyDict_SetItemString(pDict, "setup_lighting",
            PyCFunction_New(&SetupLightingPython, nullptr));
        PyDict_SetItemString(pDict, "get_system_stats",
            PyCFunction_New(&GetSystemStatsPython, nullptr));

        // Register module in Python
        PyObject* pSysModules = PyImport_GetModuleDict();
        PyDict_SetItemString(pSysModules, "auracron_abismo_umbrio", pModule);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python bindings initialized successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception initializing Python bindings: %s"),
               UTF8_TO_TCHAR(e.what()));
        return false;
    }
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Python support not compiled in"));
    return false;
#endif
}

bool UAuracronAbismoUmbrioBridge::ExecutePythonScript(const FString& ScriptPath)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executing Python script: %s"), *ScriptPath);

#ifdef WITH_PYTHON
    try
    {
        // Check if Python is initialized
        if (!Py_IsInitialized())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Python not initialized"));
            return false;
        }

        // Read script file
        FString ScriptContent;
        if (!FFileHelper::LoadFileToString(ScriptContent, *ScriptPath))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to read script file: %s"), *ScriptPath);
            return false;
        }

        // Execute Python script
        int Result = PyRun_SimpleString(TCHAR_TO_UTF8(*ScriptContent));
        if (Result != 0)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Python script execution failed"));
            return false;
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python script executed successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception executing Python script: %s"),
               UTF8_TO_TCHAR(e.what()));
        return false;
    }
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Python support not compiled in"));
    return false;
#endif
}

FString UAuracronAbismoUmbrioBridge::GetSystemDataForPython() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Getting system data for Python"));

    // Create JSON object with system data
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

    // Basic system info
    JsonObject->SetBoolField(TEXT("system_initialized"), bSystemInitialized);
    JsonObject->SetNumberField(TEXT("generated_components_count"), GeneratedComponents.Num());

    // Cave properties
    TSharedPtr<FJsonObject> CavePropsJson = MakeShareable(new FJsonObject);
    CavePropsJson->SetNumberField(TEXT("width"), DefaultCaveProperties.Width);
    CavePropsJson->SetNumberField(TEXT("height"), DefaultCaveProperties.Height);
    CavePropsJson->SetNumberField(TEXT("depth"), DefaultCaveProperties.Depth);
    CavePropsJson->SetNumberField(TEXT("complexity"), DefaultCaveProperties.Complexity);
    CavePropsJson->SetNumberField(TEXT("temperature"), DefaultCaveProperties.Temperature);
    CavePropsJson->SetBoolField(TEXT("has_underground_water"), DefaultCaveProperties.bHasUndergroundWater);
    CavePropsJson->SetBoolField(TEXT("has_luminous_crystals"), DefaultCaveProperties.bHasLuminousCrystals);
    CavePropsJson->SetNumberField(TEXT("structural_stability"), DefaultCaveProperties.StructuralStability);
    JsonObject->SetObjectField(TEXT("cave_properties"), CavePropsJson);

    // Lighting configuration
    TSharedPtr<FJsonObject> LightingJson = MakeShareable(new FJsonObject);
    LightingJson->SetNumberField(TEXT("ambient_light_intensity"), DefaultLightingConfig.AmbientLightIntensity);
    LightingJson->SetNumberField(TEXT("volumetric_fog_density"), DefaultLightingConfig.VolumetricFogDensity);
    LightingJson->SetBoolField(TEXT("use_lumen_gi"), DefaultLightingConfig.bUseLumenGI);
    LightingJson->SetNumberField(TEXT("crystal_luminosity"), DefaultLightingConfig.CrystalLuminosity);
    JsonObject->SetObjectField(TEXT("lighting_config"), LightingJson);

    // Convert to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}
