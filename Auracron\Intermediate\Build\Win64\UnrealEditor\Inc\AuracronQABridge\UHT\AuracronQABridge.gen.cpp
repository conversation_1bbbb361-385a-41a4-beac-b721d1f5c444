// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronQABridge/Public/AuracronQABridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronQABridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONQABRIDGE_API UClass* Z_Construct_UClass_UAuracronQABridge();
AURACRONQABRIDGE_API UClass* Z_Construct_UClass_UAuracronQABridge_NoRegister();
AURACRONQABRIDGE_API UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity();
AURACRONQABRIDGE_API UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult();
AURACRONQABRIDGE_API UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType();
AURACRONQABRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronQAPerformanceData();
AURACRONQABRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronQATestCase();
AURACRONQABRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronQATestExecution();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronQABridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronQATestType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronQATestType;
static UEnum* EAuracronQATestType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronQATestType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronQATestType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("EAuracronQATestType"));
	}
	return Z_Registration_Info_UEnum_EAuracronQATestType.OuterSingleton;
}
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQATestType>()
{
	return EAuracronQATestType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AssetValidation.DisplayName", "Asset Validation" },
		{ "AssetValidation.Name", "EAuracronQATestType::AssetValidation" },
		{ "BalanceVerification.DisplayName", "Balance Verification" },
		{ "BalanceVerification.Name", "EAuracronQATestType::BalanceVerification" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * QA Test Types for AURACRON champion validation\n */" },
#endif
		{ "GameplayTesting.DisplayName", "Gameplay Testing" },
		{ "GameplayTesting.Name", "EAuracronQATestType::GameplayTesting" },
		{ "IntegrationTesting.DisplayName", "Integration Testing" },
		{ "IntegrationTesting.Name", "EAuracronQATestType::IntegrationTesting" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
		{ "PerformanceTesting.DisplayName", "Performance Testing" },
		{ "PerformanceTesting.Name", "EAuracronQATestType::PerformanceTesting" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA Test Types for AURACRON champion validation" },
#endif
		{ "VisualValidation.DisplayName", "Visual Validation" },
		{ "VisualValidation.Name", "EAuracronQATestType::VisualValidation" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronQATestType::VisualValidation", (int64)EAuracronQATestType::VisualValidation },
		{ "EAuracronQATestType::GameplayTesting", (int64)EAuracronQATestType::GameplayTesting },
		{ "EAuracronQATestType::PerformanceTesting", (int64)EAuracronQATestType::PerformanceTesting },
		{ "EAuracronQATestType::BalanceVerification", (int64)EAuracronQATestType::BalanceVerification },
		{ "EAuracronQATestType::AssetValidation", (int64)EAuracronQATestType::AssetValidation },
		{ "EAuracronQATestType::IntegrationTesting", (int64)EAuracronQATestType::IntegrationTesting },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	"EAuracronQATestType",
	"EAuracronQATestType",
	Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType()
{
	if (!Z_Registration_Info_UEnum_EAuracronQATestType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronQATestType.InnerSingleton, Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronQATestType.InnerSingleton;
}
// ********** End Enum EAuracronQATestType *********************************************************

// ********** Begin Enum EAuracronQATestResult *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronQATestResult;
static UEnum* EAuracronQATestResult_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronQATestResult.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronQATestResult.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("EAuracronQATestResult"));
	}
	return Z_Registration_Info_UEnum_EAuracronQATestResult.OuterSingleton;
}
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQATestResult>()
{
	return EAuracronQATestResult_StaticEnum();
}
struct Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * QA Test Results for automation testing\n */" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronQATestResult::Error" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronQATestResult::Failed" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
		{ "Passed.DisplayName", "Passed" },
		{ "Passed.Name", "EAuracronQATestResult::Passed" },
		{ "Skipped.DisplayName", "Skipped" },
		{ "Skipped.Name", "EAuracronQATestResult::Skipped" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA Test Results for automation testing" },
#endif
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EAuracronQATestResult::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronQATestResult::Passed", (int64)EAuracronQATestResult::Passed },
		{ "EAuracronQATestResult::Failed", (int64)EAuracronQATestResult::Failed },
		{ "EAuracronQATestResult::Warning", (int64)EAuracronQATestResult::Warning },
		{ "EAuracronQATestResult::Skipped", (int64)EAuracronQATestResult::Skipped },
		{ "EAuracronQATestResult::Error", (int64)EAuracronQATestResult::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	"EAuracronQATestResult",
	"EAuracronQATestResult",
	Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult()
{
	if (!Z_Registration_Info_UEnum_EAuracronQATestResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronQATestResult.InnerSingleton, Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronQATestResult.InnerSingleton;
}
// ********** End Enum EAuracronQATestResult *******************************************************

// ********** Begin Enum EAuracronQASeverity *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronQASeverity;
static UEnum* EAuracronQASeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronQASeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronQASeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("EAuracronQASeverity"));
	}
	return Z_Registration_Info_UEnum_EAuracronQASeverity.OuterSingleton;
}
template<> AURACRONQABRIDGE_API UEnum* StaticEnum<EAuracronQASeverity>()
{
	return EAuracronQASeverity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * QA Test Severity Levels\n */" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronQASeverity::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronQASeverity::High" },
		{ "Info.DisplayName", "Info" },
		{ "Info.Name", "EAuracronQASeverity::Info" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronQASeverity::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronQASeverity::Medium" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA Test Severity Levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronQASeverity::Critical", (int64)EAuracronQASeverity::Critical },
		{ "EAuracronQASeverity::High", (int64)EAuracronQASeverity::High },
		{ "EAuracronQASeverity::Medium", (int64)EAuracronQASeverity::Medium },
		{ "EAuracronQASeverity::Low", (int64)EAuracronQASeverity::Low },
		{ "EAuracronQASeverity::Info", (int64)EAuracronQASeverity::Info },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	"EAuracronQASeverity",
	"EAuracronQASeverity",
	Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity()
{
	if (!Z_Registration_Info_UEnum_EAuracronQASeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronQASeverity.InnerSingleton, Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronQASeverity.InnerSingleton;
}
// ********** End Enum EAuracronQASeverity *********************************************************

// ********** Begin ScriptStruct FAuracronQATestCase ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronQATestCase;
class UScriptStruct* FAuracronQATestCase::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQATestCase.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronQATestCase.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronQATestCase, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("AuracronQATestCase"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQATestCase.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronQATestCase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * QA Test Case Definition Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA Test Case Definition Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestID_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unique identifier for the test case */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for the test case" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestName_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Human-readable test name */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Human-readable test name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test description */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test description" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestType_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test type classification */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test type classification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test severity level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test severity level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpectedDuration_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Expected execution time in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Expected execution time in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutDuration_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test timeout in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test timeout in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test parameters as JSON string */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test parameters as JSON string" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Prerequisites_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test prerequisites */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test prerequisites" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test tags for filtering */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test tags for filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnabled_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether test is enabled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether test is enabled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRunInEditor_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether test runs in editor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether test runs in editor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRunInGame_MetaData[] = {
		{ "Category", "Test Case" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether test runs in game */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether test runs in game" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutDuration;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Prerequisites_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Prerequisites;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static void NewProp_bRunInEditor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRunInEditor;
	static void NewProp_bRunInGame_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRunInGame;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronQATestCase>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestID = { "TestID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, TestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestID_MetaData), NewProp_TestID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, TestName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestName_MetaData), NewProp_TestName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestType = { "TestType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, TestType), Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestType_MetaData), NewProp_TestType_MetaData) }; // 724122522
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, Severity), Z_Construct_UEnum_AuracronQABridge_EAuracronQASeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 1293145061
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_ExpectedDuration = { "ExpectedDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, ExpectedDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpectedDuration_MetaData), NewProp_ExpectedDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TimeoutDuration = { "TimeoutDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, TimeoutDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutDuration_MetaData), NewProp_TimeoutDuration_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, Parameters), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Prerequisites_Inner = { "Prerequisites", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Prerequisites = { "Prerequisites", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, Prerequisites), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Prerequisites_MetaData), NewProp_Prerequisites_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestCase, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
void Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((FAuracronQATestCase*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronQATestCase), &Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnabled_MetaData), NewProp_bEnabled_MetaData) };
void Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInEditor_SetBit(void* Obj)
{
	((FAuracronQATestCase*)Obj)->bRunInEditor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInEditor = { "bRunInEditor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronQATestCase), &Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInEditor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRunInEditor_MetaData), NewProp_bRunInEditor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInGame_SetBit(void* Obj)
{
	((FAuracronQATestCase*)Obj)->bRunInGame = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInGame = { "bRunInGame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronQATestCase), &Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInGame_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRunInGame_MetaData), NewProp_bRunInGame_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_ExpectedDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_TimeoutDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Prerequisites_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Prerequisites,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInEditor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewProp_bRunInGame,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	&NewStructOps,
	"AuracronQATestCase",
	Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::PropPointers),
	sizeof(FAuracronQATestCase),
	alignof(FAuracronQATestCase),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronQATestCase()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQATestCase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronQATestCase.InnerSingleton, Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQATestCase.InnerSingleton;
}
// ********** End ScriptStruct FAuracronQATestCase *************************************************

// ********** Begin ScriptStruct FAuracronQATestExecution ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronQATestExecution;
class UScriptStruct* FAuracronQATestExecution::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronQATestExecution, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("AuracronQATestExecution"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * QA Test Execution Result Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA Test Execution Result Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestCase_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test case that was executed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test case that was executed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test execution result */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test execution message */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test execution start time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution start time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndTime_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test execution end time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution end time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActualDuration_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Actual execution duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actual execution duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputLogs_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test output logs */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test output logs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Artifacts_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Test artifacts (screenshots, files, etc.) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test artifacts (screenshots, files, etc.)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryStats_MetaData[] = {
		{ "Category", "Test Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory usage statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory usage statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestCase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Result_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActualDuration;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputLogs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutputLogs;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Artifacts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Artifacts;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_MemoryStats_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MemoryStats_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MemoryStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronQATestExecution>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_TestCase = { "TestCase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, TestCase), Z_Construct_UScriptStruct_FAuracronQATestCase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestCase_MetaData), NewProp_TestCase_MetaData) }; // 3158239933
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Result_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, Result), Z_Construct_UEnum_AuracronQABridge_EAuracronQATestResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 2838084428
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_EndTime = { "EndTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, EndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndTime_MetaData), NewProp_EndTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_ActualDuration = { "ActualDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, ActualDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActualDuration_MetaData), NewProp_ActualDuration_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_OutputLogs_Inner = { "OutputLogs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_OutputLogs = { "OutputLogs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, OutputLogs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputLogs_MetaData), NewProp_OutputLogs_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Artifacts_Inner = { "Artifacts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Artifacts = { "Artifacts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, Artifacts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Artifacts_MetaData), NewProp_Artifacts_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats_ValueProp = { "MemoryStats", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats_Key_KeyProp = { "MemoryStats_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats = { "MemoryStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQATestExecution, MemoryStats), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryStats_MetaData), NewProp_MemoryStats_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_TestCase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Result_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_EndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_ActualDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_OutputLogs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_OutputLogs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Artifacts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_Artifacts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_PerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewProp_MemoryStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	&NewStructOps,
	"AuracronQATestExecution",
	Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::PropPointers),
	sizeof(FAuracronQATestExecution),
	alignof(FAuracronQATestExecution),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronQATestExecution()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.InnerSingleton, Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQATestExecution.InnerSingleton;
}
// ********** End ScriptStruct FAuracronQATestExecution ********************************************

// ********** Begin ScriptStruct FAuracronQAPerformanceData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData;
class UScriptStruct* FAuracronQAPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronQAPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronQABridge(), TEXT("AuracronQAPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Profiling Data Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Profiling Data Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameRate_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame rate statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame rate statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsed_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory usage statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory usage statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakMemoryUsed_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageCPUUsage_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** CPU usage statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CPU usage statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakCPUUsage_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageGPUUsage_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GPU usage statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU usage statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakGPUUsage_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageDrawCalls_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Draw call statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Draw call statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakDrawCalls_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelLoadTime_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loading time statistics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading time statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetLoadTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFrameRate;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_TotalMemoryUsed;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_PeakMemoryUsed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageCPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakCPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageGPUUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakGPUUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AverageDrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PeakDrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LevelLoadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AssetLoadTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronQAPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageFrameRate = { "AverageFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, AverageFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameRate_MetaData), NewProp_AverageFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_MinFrameRate = { "MinFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, MinFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFrameRate_MetaData), NewProp_MinFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_MaxFrameRate = { "MaxFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, MaxFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameRate_MetaData), NewProp_MaxFrameRate_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_TotalMemoryUsed = { "TotalMemoryUsed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, TotalMemoryUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsed_MetaData), NewProp_TotalMemoryUsed_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakMemoryUsed = { "PeakMemoryUsed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, PeakMemoryUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakMemoryUsed_MetaData), NewProp_PeakMemoryUsed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageCPUUsage = { "AverageCPUUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, AverageCPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageCPUUsage_MetaData), NewProp_AverageCPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakCPUUsage = { "PeakCPUUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, PeakCPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakCPUUsage_MetaData), NewProp_PeakCPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageGPUUsage = { "AverageGPUUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, AverageGPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageGPUUsage_MetaData), NewProp_AverageGPUUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakGPUUsage = { "PeakGPUUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, PeakGPUUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakGPUUsage_MetaData), NewProp_PeakGPUUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageDrawCalls = { "AverageDrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, AverageDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageDrawCalls_MetaData), NewProp_AverageDrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakDrawCalls = { "PeakDrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, PeakDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakDrawCalls_MetaData), NewProp_PeakDrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_LevelLoadTime = { "LevelLoadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, LevelLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelLoadTime_MetaData), NewProp_LevelLoadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AssetLoadTime = { "AssetLoadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronQAPerformanceData, AssetLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetLoadTime_MetaData), NewProp_AssetLoadTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_MinFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_MaxFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_TotalMemoryUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakMemoryUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageCPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakCPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageGPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakGPUUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AverageDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_PeakDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_LevelLoadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewProp_AssetLoadTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronQABridge,
	nullptr,
	&NewStructOps,
	"AuracronQAPerformanceData",
	Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::PropPointers),
	sizeof(FAuracronQAPerformanceData),
	alignof(FAuracronQAPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronQAPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronQAPerformanceData ******************************************

// ********** Begin Class UAuracronQABridge Function CaptureChampionRender *************************
struct Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics
{
	struct AuracronQABridge_eventCaptureChampionRender_Parms
	{
		AActor* ChampionActor;
		FString ChampionName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Capture champion render for visual validation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Capture champion render for visual validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChampionActor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ChampionActor = { "ChampionActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureChampionRender_Parms, ChampionActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ChampionName = { "ChampionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureChampionRender_Parms, ChampionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionName_MetaData), NewProp_ChampionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureChampionRender_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ChampionActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ChampionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "CaptureChampionRender", Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::AuracronQABridge_eventCaptureChampionRender_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::AuracronQABridge_eventCaptureChampionRender_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execCaptureChampionRender)
{
	P_GET_OBJECT(AActor,Z_Param_ChampionActor);
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CaptureChampionRender(Z_Param_ChampionActor,Z_Param_ChampionName);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function CaptureChampionRender ***************************

// ********** Begin Class UAuracronQABridge Function CaptureScreenshot *****************************
struct Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics
{
	struct AuracronQABridge_eventCaptureScreenshot_Parms
	{
		FString ScreenshotName;
		int32 Width;
		int32 Height;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Capture screenshot for visual validation\n     */" },
#endif
		{ "CPP_Default_Height", "1080" },
		{ "CPP_Default_Width", "1920" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Capture screenshot for visual validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenshotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScreenshotName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_ScreenshotName = { "ScreenshotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureScreenshot_Parms, ScreenshotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenshotName_MetaData), NewProp_ScreenshotName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureScreenshot_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureScreenshot_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCaptureScreenshot_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_ScreenshotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "CaptureScreenshot", Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::AuracronQABridge_eventCaptureScreenshot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::AuracronQABridge_eventCaptureScreenshot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execCaptureScreenshot)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScreenshotName);
	P_GET_PROPERTY(FIntProperty,Z_Param_Width);
	P_GET_PROPERTY(FIntProperty,Z_Param_Height);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CaptureScreenshot(Z_Param_ScreenshotName,Z_Param_Width,Z_Param_Height);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function CaptureScreenshot *******************************

// ********** Begin Class UAuracronQABridge Function CompareScreenshots ****************************
struct Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics
{
	struct AuracronQABridge_eventCompareScreenshots_Parms
	{
		FString ReferenceImage;
		FString CurrentImage;
		float Tolerance;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Compare screenshots for visual regression testing\n     */" },
#endif
		{ "CPP_Default_Tolerance", "0.950000" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compare screenshots for visual regression testing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferenceImage_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentImage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReferenceImage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentImage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_ReferenceImage = { "ReferenceImage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCompareScreenshots_Parms, ReferenceImage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferenceImage_MetaData), NewProp_ReferenceImage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_CurrentImage = { "CurrentImage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCompareScreenshots_Parms, CurrentImage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentImage_MetaData), NewProp_CurrentImage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCompareScreenshots_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventCompareScreenshots_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_ReferenceImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_CurrentImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "CompareScreenshots", Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::AuracronQABridge_eventCompareScreenshots_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::AuracronQABridge_eventCompareScreenshots_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execCompareScreenshots)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ReferenceImage);
	P_GET_PROPERTY(FStrProperty,Z_Param_CurrentImage);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->CompareScreenshots(Z_Param_ReferenceImage,Z_Param_CurrentImage,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function CompareScreenshots ******************************

// ********** Begin Class UAuracronQABridge Function ExecutePythonTestScript ***********************
struct Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics
{
	struct AuracronQABridge_eventExecutePythonTestScript_Parms
	{
		FString ScriptPath;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python test script\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python test script" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptPath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::NewProp_ScriptPath = { "ScriptPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecutePythonTestScript_Parms, ScriptPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptPath_MetaData), NewProp_ScriptPath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecutePythonTestScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::NewProp_ScriptPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExecutePythonTestScript", Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::AuracronQABridge_eventExecutePythonTestScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::AuracronQABridge_eventExecutePythonTestScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExecutePythonTestScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->ExecutePythonTestScript(Z_Param_ScriptPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExecutePythonTestScript *************************

// ********** Begin Class UAuracronQABridge Function ExecuteTestCase *******************************
struct Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics
{
	struct AuracronQABridge_eventExecuteTestCase_Parms
	{
		FAuracronQATestCase TestCase;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute single test case\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute single test case" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestCase_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestCase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::NewProp_TestCase = { "TestCase", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestCase_Parms, TestCase), Z_Construct_UScriptStruct_FAuracronQATestCase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestCase_MetaData), NewProp_TestCase_MetaData) }; // 3158239933
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestCase_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::NewProp_TestCase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExecuteTestCase", Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::AuracronQABridge_eventExecuteTestCase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::AuracronQABridge_eventExecuteTestCase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExecuteTestCase)
{
	P_GET_STRUCT_REF(FAuracronQATestCase,Z_Param_Out_TestCase);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->ExecuteTestCase(Z_Param_Out_TestCase);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExecuteTestCase *********************************

// ********** Begin Class UAuracronQABridge Function ExecuteTestsByTag *****************************
struct Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics
{
	struct AuracronQABridge_eventExecuteTestsByTag_Parms
	{
		FString Tag;
		TArray<FAuracronQATestExecution> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute all tests with specific tag\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute all tests with specific tag" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestsByTag_Parms, Tag), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestsByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExecuteTestsByTag", Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::AuracronQABridge_eventExecuteTestsByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::AuracronQABridge_eventExecuteTestsByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExecuteTestsByTag)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestExecution>*)Z_Param__Result=P_THIS->ExecuteTestsByTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExecuteTestsByTag *******************************

// ********** Begin Class UAuracronQABridge Function ExecuteTestsByType ****************************
struct Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics
{
	struct AuracronQABridge_eventExecuteTestsByType_Parms
	{
		EAuracronQATestType TestType;
		TArray<FAuracronQATestExecution> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute all tests of specific type\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute all tests of specific type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_TestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_TestType = { "TestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestsByType_Parms, TestType), Z_Construct_UEnum_AuracronQABridge_EAuracronQATestType, METADATA_PARAMS(0, nullptr) }; // 724122522
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_TestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_TestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExecuteTestsByType", Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::AuracronQABridge_eventExecuteTestsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::AuracronQABridge_eventExecuteTestsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExecuteTestsByType)
{
	P_GET_ENUM(EAuracronQATestType,Z_Param_TestType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestExecution>*)Z_Param__Result=P_THIS->ExecuteTestsByType(EAuracronQATestType(Z_Param_TestType));
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExecuteTestsByType ******************************

// ********** Begin Class UAuracronQABridge Function ExecuteTestSuite ******************************
struct Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics
{
	struct AuracronQABridge_eventExecuteTestSuite_Parms
	{
		TArray<FAuracronQATestCase> TestCases;
		TArray<FAuracronQATestExecution> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute multiple test cases\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute multiple test cases" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestCases_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestCases_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestCases;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_TestCases_Inner = { "TestCases", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestCase, METADATA_PARAMS(0, nullptr) }; // 3158239933
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_TestCases = { "TestCases", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestSuite_Parms, TestCases), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestCases_MetaData), NewProp_TestCases_MetaData) }; // 3158239933
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExecuteTestSuite_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_TestCases_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_TestCases,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExecuteTestSuite", Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::AuracronQABridge_eventExecuteTestSuite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::AuracronQABridge_eventExecuteTestSuite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExecuteTestSuite)
{
	P_GET_TARRAY_REF(FAuracronQATestCase,Z_Param_Out_TestCases);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestExecution>*)Z_Param__Result=P_THIS->ExecuteTestSuite(Z_Param_Out_TestCases);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExecuteTestSuite ********************************

// ********** Begin Class UAuracronQABridge Function ExportQAResultsToJSON *************************
struct Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics
{
	struct AuracronQABridge_eventExportQAResultsToJSON_Parms
	{
		TArray<FAuracronQATestExecution> TestResults;
		FString OutputPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Export QA results to JSON\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Export QA results to JSON" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestResults_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestResults_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_TestResults_Inner = { "TestResults", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_TestResults = { "TestResults", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExportQAResultsToJSON_Parms, TestResults), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestResults_MetaData), NewProp_TestResults_MetaData) }; // 209021855
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_OutputPath = { "OutputPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventExportQAResultsToJSON_Parms, OutputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPath_MetaData), NewProp_OutputPath_MetaData) };
void Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronQABridge_eventExportQAResultsToJSON_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronQABridge_eventExportQAResultsToJSON_Parms), &Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_TestResults_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_TestResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_OutputPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ExportQAResultsToJSON", Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::AuracronQABridge_eventExportQAResultsToJSON_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::AuracronQABridge_eventExportQAResultsToJSON_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execExportQAResultsToJSON)
{
	P_GET_TARRAY_REF(FAuracronQATestExecution,Z_Param_Out_TestResults);
	P_GET_PROPERTY(FStrProperty,Z_Param_OutputPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportQAResultsToJSON(Z_Param_Out_TestResults,Z_Param_OutputPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ExportQAResultsToJSON ***************************

// ********** Begin Class UAuracronQABridge Function GetQADataForPython ****************************
struct Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics
{
	struct AuracronQABridge_eventGetQADataForPython_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get QA data for Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get QA data for Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventGetQADataForPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "GetQADataForPython", Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::AuracronQABridge_eventGetQADataForPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::AuracronQABridge_eventGetQADataForPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execGetQADataForPython)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetQADataForPython();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function GetQADataForPython ******************************

// ********** Begin Class UAuracronQABridge Function GetQASystemConfiguration **********************
struct Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics
{
	struct AuracronQABridge_eventGetQASystemConfiguration_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get QA system configuration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get QA system configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventGetQASystemConfiguration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "GetQASystemConfiguration", Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::AuracronQABridge_eventGetQASystemConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::AuracronQABridge_eventGetQASystemConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execGetQASystemConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetQASystemConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function GetQASystemConfiguration ************************

// ********** Begin Class UAuracronQABridge Function ImportQATestCasesFromJSON *********************
struct Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics
{
	struct AuracronQABridge_eventImportQATestCasesFromJSON_Parms
	{
		FString InputPath;
		TArray<FAuracronQATestCase> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Import QA test cases from JSON\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Import QA test cases from JSON" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InputPath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_InputPath = { "InputPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventImportQATestCasesFromJSON_Parms, InputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputPath_MetaData), NewProp_InputPath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestCase, METADATA_PARAMS(0, nullptr) }; // 3158239933
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventImportQATestCasesFromJSON_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3158239933
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_InputPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ImportQATestCasesFromJSON", Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::AuracronQABridge_eventImportQATestCasesFromJSON_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::AuracronQABridge_eventImportQATestCasesFromJSON_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execImportQATestCasesFromJSON)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InputPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestCase>*)Z_Param__Result=P_THIS->ImportQATestCasesFromJSON(Z_Param_InputPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ImportQATestCasesFromJSON ***********************

// ********** Begin Class UAuracronQABridge Function InitializeAutomationTesting *******************
struct Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics
{
	struct AuracronQABridge_eventInitializeAutomationTesting_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Framework" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize automation testing framework\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize automation testing framework" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronQABridge_eventInitializeAutomationTesting_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronQABridge_eventInitializeAutomationTesting_Parms), &Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "InitializeAutomationTesting", Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::AuracronQABridge_eventInitializeAutomationTesting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::AuracronQABridge_eventInitializeAutomationTesting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execInitializeAutomationTesting)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeAutomationTesting();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function InitializeAutomationTesting *********************

// ********** Begin Class UAuracronQABridge Function InitializePythonBindings **********************
struct Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics
{
	struct AuracronQABridge_eventInitializePythonBindings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize Python bindings\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Python bindings" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronQABridge_eventInitializePythonBindings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronQABridge_eventInitializePythonBindings_Parms), &Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "InitializePythonBindings", Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::AuracronQABridge_eventInitializePythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::AuracronQABridge_eventInitializePythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execInitializePythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function InitializePythonBindings ************************

// ********** Begin Class UAuracronQABridge Function ProfileChampionPerformance ********************
struct Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics
{
	struct AuracronQABridge_eventProfileChampionPerformance_Parms
	{
		AActor* ChampionActor;
		float TestDuration;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Profile champion performance\n     */" },
#endif
		{ "CPP_Default_TestDuration", "10.000000" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profile champion performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChampionActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TestDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_ChampionActor = { "ChampionActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventProfileChampionPerformance_Parms, ChampionActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_TestDuration = { "TestDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventProfileChampionPerformance_Parms, TestDuration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventProfileChampionPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_ChampionActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_TestDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ProfileChampionPerformance", Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::AuracronQABridge_eventProfileChampionPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::AuracronQABridge_eventProfileChampionPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execProfileChampionPerformance)
{
	P_GET_OBJECT(AActor,Z_Param_ChampionActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TestDuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->ProfileChampionPerformance(Z_Param_ChampionActor,Z_Param_TestDuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ProfileChampionPerformance **********************

// ********** Begin Class UAuracronQABridge Function StartMemoryTracking ***************************
struct Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics
{
	struct AuracronQABridge_eventStartMemoryTracking_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start memory tracking\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start memory tracking" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronQABridge_eventStartMemoryTracking_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronQABridge_eventStartMemoryTracking_Parms), &Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "StartMemoryTracking", Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::AuracronQABridge_eventStartMemoryTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::AuracronQABridge_eventStartMemoryTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execStartMemoryTracking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartMemoryTracking();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function StartMemoryTracking *****************************

// ********** Begin Class UAuracronQABridge Function StartPerformanceProfiling *********************
struct Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics
{
	struct AuracronQABridge_eventStartPerformanceProfiling_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start performance profiling\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start performance profiling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronQABridge_eventStartPerformanceProfiling_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronQABridge_eventStartPerformanceProfiling_Parms), &Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "StartPerformanceProfiling", Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::AuracronQABridge_eventStartPerformanceProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::AuracronQABridge_eventStartPerformanceProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execStartPerformanceProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartPerformanceProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function StartPerformanceProfiling ***********************

// ********** Begin Class UAuracronQABridge Function StopMemoryTracking ****************************
struct Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics
{
	struct AuracronQABridge_eventStopMemoryTracking_Parms
	{
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop memory tracking and get results\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop memory tracking and get results" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventStopMemoryTracking_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "StopMemoryTracking", Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::AuracronQABridge_eventStopMemoryTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::AuracronQABridge_eventStopMemoryTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execStopMemoryTracking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->StopMemoryTracking();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function StopMemoryTracking ******************************

// ********** Begin Class UAuracronQABridge Function StopPerformanceProfiling **********************
struct Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics
{
	struct AuracronQABridge_eventStopPerformanceProfiling_Parms
	{
		FAuracronQAPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop performance profiling and get results\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop performance profiling and get results" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventStopPerformanceProfiling_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQAPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2530139431
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "StopPerformanceProfiling", Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::AuracronQABridge_eventStopPerformanceProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::AuracronQABridge_eventStopPerformanceProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execStopPerformanceProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQAPerformanceData*)Z_Param__Result=P_THIS->StopPerformanceProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function StopPerformanceProfiling ************************

// ********** Begin Class UAuracronQABridge Function TestAssetStreamingPerformance *****************
struct Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics
{
	struct AuracronQABridge_eventTestAssetStreamingPerformance_Parms
	{
		FString LevelPath;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Asset Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Test asset streaming performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test asset streaming performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelPath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::NewProp_LevelPath = { "LevelPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventTestAssetStreamingPerformance_Parms, LevelPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelPath_MetaData), NewProp_LevelPath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventTestAssetStreamingPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::NewProp_LevelPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "TestAssetStreamingPerformance", Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::AuracronQABridge_eventTestAssetStreamingPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::AuracronQABridge_eventTestAssetStreamingPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execTestAssetStreamingPerformance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LevelPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->TestAssetStreamingPerformance(Z_Param_LevelPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function TestAssetStreamingPerformance *******************

// ********** Begin Class UAuracronQABridge Function ValidateAsset *********************************
struct Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics
{
	struct AuracronQABridge_eventValidateAsset_Parms
	{
		UObject* Asset;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate single asset\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate single asset" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAsset_Parms, Asset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAsset_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::NewProp_Asset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ValidateAsset", Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::AuracronQABridge_eventValidateAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::AuracronQABridge_eventValidateAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ValidateAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ValidateAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execValidateAsset)
{
	P_GET_OBJECT(UObject,Z_Param_Asset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->ValidateAsset(Z_Param_Asset);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ValidateAsset ***********************************

// ********** Begin Class UAuracronQABridge Function ValidateAssetLoadingPerformance ***************
struct Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics
{
	struct AuracronQABridge_eventValidateAssetLoadingPerformance_Parms
	{
		TArray<FString> AssetPaths;
		FAuracronQATestExecution ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Asset Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate asset loading performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate asset loading performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssetLoadingPerformance_Parms, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssetLoadingPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_AssetPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ValidateAssetLoadingPerformance", Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::AuracronQABridge_eventValidateAssetLoadingPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::AuracronQABridge_eventValidateAssetLoadingPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execValidateAssetLoadingPerformance)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_AssetPaths);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronQATestExecution*)Z_Param__Result=P_THIS->ValidateAssetLoadingPerformance(Z_Param_Out_AssetPaths);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ValidateAssetLoadingPerformance *****************

// ********** Begin Class UAuracronQABridge Function ValidateAssets ********************************
struct Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics
{
	struct AuracronQABridge_eventValidateAssets_Parms
	{
		TArray<UObject*> Assets;
		TArray<FAuracronQATestExecution> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate multiple assets\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate multiple assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Assets_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Assets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Assets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_Assets_Inner = { "Assets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_Assets = { "Assets", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssets_Parms, Assets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Assets_MetaData), NewProp_Assets_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssets_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_Assets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_Assets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ValidateAssets", Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::AuracronQABridge_eventValidateAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::AuracronQABridge_eventValidateAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ValidateAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ValidateAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execValidateAssets)
{
	P_GET_TARRAY_REF(UObject*,Z_Param_Out_Assets);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestExecution>*)Z_Param__Result=P_THIS->ValidateAssets(Z_Param_Out_Assets);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ValidateAssets **********************************

// ********** Begin Class UAuracronQABridge Function ValidateAssetsByPath **************************
struct Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics
{
	struct AuracronQABridge_eventValidateAssetsByPath_Parms
	{
		TArray<FString> AssetPaths;
		TArray<FAuracronQATestExecution> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON QA|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate assets by path\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate assets by path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssetsByPath_Parms, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronQATestExecution, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronQABridge_eventValidateAssetsByPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 209021855
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_AssetPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronQABridge, nullptr, "ValidateAssetsByPath", Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::AuracronQABridge_eventValidateAssetsByPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::AuracronQABridge_eventValidateAssetsByPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronQABridge::execValidateAssetsByPath)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_AssetPaths);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronQATestExecution>*)Z_Param__Result=P_THIS->ValidateAssetsByPath(Z_Param_Out_AssetPaths);
	P_NATIVE_END;
}
// ********** End Class UAuracronQABridge Function ValidateAssetsByPath ****************************

// ********** Begin Class UAuracronQABridge ********************************************************
void UAuracronQABridge::StaticRegisterNativesUAuracronQABridge()
{
	UClass* Class = UAuracronQABridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CaptureChampionRender", &UAuracronQABridge::execCaptureChampionRender },
		{ "CaptureScreenshot", &UAuracronQABridge::execCaptureScreenshot },
		{ "CompareScreenshots", &UAuracronQABridge::execCompareScreenshots },
		{ "ExecutePythonTestScript", &UAuracronQABridge::execExecutePythonTestScript },
		{ "ExecuteTestCase", &UAuracronQABridge::execExecuteTestCase },
		{ "ExecuteTestsByTag", &UAuracronQABridge::execExecuteTestsByTag },
		{ "ExecuteTestsByType", &UAuracronQABridge::execExecuteTestsByType },
		{ "ExecuteTestSuite", &UAuracronQABridge::execExecuteTestSuite },
		{ "ExportQAResultsToJSON", &UAuracronQABridge::execExportQAResultsToJSON },
		{ "GetQADataForPython", &UAuracronQABridge::execGetQADataForPython },
		{ "GetQASystemConfiguration", &UAuracronQABridge::execGetQASystemConfiguration },
		{ "ImportQATestCasesFromJSON", &UAuracronQABridge::execImportQATestCasesFromJSON },
		{ "InitializeAutomationTesting", &UAuracronQABridge::execInitializeAutomationTesting },
		{ "InitializePythonBindings", &UAuracronQABridge::execInitializePythonBindings },
		{ "ProfileChampionPerformance", &UAuracronQABridge::execProfileChampionPerformance },
		{ "StartMemoryTracking", &UAuracronQABridge::execStartMemoryTracking },
		{ "StartPerformanceProfiling", &UAuracronQABridge::execStartPerformanceProfiling },
		{ "StopMemoryTracking", &UAuracronQABridge::execStopMemoryTracking },
		{ "StopPerformanceProfiling", &UAuracronQABridge::execStopPerformanceProfiling },
		{ "TestAssetStreamingPerformance", &UAuracronQABridge::execTestAssetStreamingPerformance },
		{ "ValidateAsset", &UAuracronQABridge::execValidateAsset },
		{ "ValidateAssetLoadingPerformance", &UAuracronQABridge::execValidateAssetLoadingPerformance },
		{ "ValidateAssets", &UAuracronQABridge::execValidateAssets },
		{ "ValidateAssetsByPath", &UAuracronQABridge::execValidateAssetsByPath },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronQABridge;
UClass* UAuracronQABridge::GetPrivateStaticClass()
{
	using TClass = UAuracronQABridge;
	if (!Z_Registration_Info_UClass_UAuracronQABridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronQABridge"),
			Z_Registration_Info_UClass_UAuracronQABridge.InnerSingleton,
			StaticRegisterNativesUAuracronQABridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronQABridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronQABridge_NoRegister()
{
	return UAuracronQABridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronQABridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|QA" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Main AURACRON QA Bridge Class\n * Provides comprehensive quality assurance testing capabilities for AURACRON champions\n */" },
#endif
		{ "DisplayName", "AURACRON QA Bridge" },
		{ "IncludePath", "AuracronQABridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main AURACRON QA Bridge Class\nProvides comprehensive quality assurance testing capabilities for AURACRON champions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTestTimeout_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default test timeout in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default test timeout in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultScreenshotTolerance_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Screenshot comparison tolerance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Screenshot comparison tolerance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfilingSampleRate_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance profiling sample rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance profiling sample rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable detailed logging */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable detailed logging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceProfiling_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance profiling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance profiling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryTracking_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable memory tracking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable memory tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QAOutputDirectory_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** QA output directory */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "QA output directory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenshotDirectory_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Screenshot output directory */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronQABridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Screenshot output directory" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultTestTimeout;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultScreenshotTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProfilingSampleRate;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bEnablePerformanceProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceProfiling;
	static void NewProp_bEnableMemoryTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryTracking;
	static const UECodeGen_Private::FStrPropertyParams NewProp_QAOutputDirectory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScreenshotDirectory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronQABridge_CaptureChampionRender, "CaptureChampionRender" }, // 795910915
		{ &Z_Construct_UFunction_UAuracronQABridge_CaptureScreenshot, "CaptureScreenshot" }, // 581813267
		{ &Z_Construct_UFunction_UAuracronQABridge_CompareScreenshots, "CompareScreenshots" }, // 650193537
		{ &Z_Construct_UFunction_UAuracronQABridge_ExecutePythonTestScript, "ExecutePythonTestScript" }, // 198161993
		{ &Z_Construct_UFunction_UAuracronQABridge_ExecuteTestCase, "ExecuteTestCase" }, // 3446251674
		{ &Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByTag, "ExecuteTestsByTag" }, // 2962035439
		{ &Z_Construct_UFunction_UAuracronQABridge_ExecuteTestsByType, "ExecuteTestsByType" }, // 3636788851
		{ &Z_Construct_UFunction_UAuracronQABridge_ExecuteTestSuite, "ExecuteTestSuite" }, // 3537598734
		{ &Z_Construct_UFunction_UAuracronQABridge_ExportQAResultsToJSON, "ExportQAResultsToJSON" }, // 3831503557
		{ &Z_Construct_UFunction_UAuracronQABridge_GetQADataForPython, "GetQADataForPython" }, // 4061434375
		{ &Z_Construct_UFunction_UAuracronQABridge_GetQASystemConfiguration, "GetQASystemConfiguration" }, // 91000082
		{ &Z_Construct_UFunction_UAuracronQABridge_ImportQATestCasesFromJSON, "ImportQATestCasesFromJSON" }, // 243563360
		{ &Z_Construct_UFunction_UAuracronQABridge_InitializeAutomationTesting, "InitializeAutomationTesting" }, // 2915648107
		{ &Z_Construct_UFunction_UAuracronQABridge_InitializePythonBindings, "InitializePythonBindings" }, // 1882938219
		{ &Z_Construct_UFunction_UAuracronQABridge_ProfileChampionPerformance, "ProfileChampionPerformance" }, // 3135726561
		{ &Z_Construct_UFunction_UAuracronQABridge_StartMemoryTracking, "StartMemoryTracking" }, // 1384365111
		{ &Z_Construct_UFunction_UAuracronQABridge_StartPerformanceProfiling, "StartPerformanceProfiling" }, // 3629569582
		{ &Z_Construct_UFunction_UAuracronQABridge_StopMemoryTracking, "StopMemoryTracking" }, // 3731392681
		{ &Z_Construct_UFunction_UAuracronQABridge_StopPerformanceProfiling, "StopPerformanceProfiling" }, // 2031232930
		{ &Z_Construct_UFunction_UAuracronQABridge_TestAssetStreamingPerformance, "TestAssetStreamingPerformance" }, // 1521431712
		{ &Z_Construct_UFunction_UAuracronQABridge_ValidateAsset, "ValidateAsset" }, // 4097683525
		{ &Z_Construct_UFunction_UAuracronQABridge_ValidateAssetLoadingPerformance, "ValidateAssetLoadingPerformance" }, // 1155934614
		{ &Z_Construct_UFunction_UAuracronQABridge_ValidateAssets, "ValidateAssets" }, // 2566511499
		{ &Z_Construct_UFunction_UAuracronQABridge_ValidateAssetsByPath, "ValidateAssetsByPath" }, // 367788783
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronQABridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_DefaultTestTimeout = { "DefaultTestTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQABridge, DefaultTestTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTestTimeout_MetaData), NewProp_DefaultTestTimeout_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_DefaultScreenshotTolerance = { "DefaultScreenshotTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQABridge, DefaultScreenshotTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultScreenshotTolerance_MetaData), NewProp_DefaultScreenshotTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_ProfilingSampleRate = { "ProfilingSampleRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQABridge, ProfilingSampleRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfilingSampleRate_MetaData), NewProp_ProfilingSampleRate_MetaData) };
void Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((UAuracronQABridge*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronQABridge), &Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnablePerformanceProfiling_SetBit(void* Obj)
{
	((UAuracronQABridge*)Obj)->bEnablePerformanceProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnablePerformanceProfiling = { "bEnablePerformanceProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronQABridge), &Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnablePerformanceProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceProfiling_MetaData), NewProp_bEnablePerformanceProfiling_MetaData) };
void Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableMemoryTracking_SetBit(void* Obj)
{
	((UAuracronQABridge*)Obj)->bEnableMemoryTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableMemoryTracking = { "bEnableMemoryTracking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronQABridge), &Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableMemoryTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryTracking_MetaData), NewProp_bEnableMemoryTracking_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_QAOutputDirectory = { "QAOutputDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQABridge, QAOutputDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QAOutputDirectory_MetaData), NewProp_QAOutputDirectory_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_ScreenshotDirectory = { "ScreenshotDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQABridge, ScreenshotDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenshotDirectory_MetaData), NewProp_ScreenshotDirectory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronQABridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_DefaultTestTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_DefaultScreenshotTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_ProfilingSampleRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnablePerformanceProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_bEnableMemoryTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_QAOutputDirectory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQABridge_Statics::NewProp_ScreenshotDirectory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQABridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronQABridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronQABridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQABridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronQABridge_Statics::ClassParams = {
	&UAuracronQABridge::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronQABridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQABridge_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQABridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronQABridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronQABridge()
{
	if (!Z_Registration_Info_UClass_UAuracronQABridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronQABridge.OuterSingleton, Z_Construct_UClass_UAuracronQABridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronQABridge.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronQABridge);
UAuracronQABridge::~UAuracronQABridge() {}
// ********** End Class UAuracronQABridge **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronQATestType_StaticEnum, TEXT("EAuracronQATestType"), &Z_Registration_Info_UEnum_EAuracronQATestType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 724122522U) },
		{ EAuracronQATestResult_StaticEnum, TEXT("EAuracronQATestResult"), &Z_Registration_Info_UEnum_EAuracronQATestResult, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2838084428U) },
		{ EAuracronQASeverity_StaticEnum, TEXT("EAuracronQASeverity"), &Z_Registration_Info_UEnum_EAuracronQASeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1293145061U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronQATestCase::StaticStruct, Z_Construct_UScriptStruct_FAuracronQATestCase_Statics::NewStructOps, TEXT("AuracronQATestCase"), &Z_Registration_Info_UScriptStruct_FAuracronQATestCase, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronQATestCase), 3158239933U) },
		{ FAuracronQATestExecution::StaticStruct, Z_Construct_UScriptStruct_FAuracronQATestExecution_Statics::NewStructOps, TEXT("AuracronQATestExecution"), &Z_Registration_Info_UScriptStruct_FAuracronQATestExecution, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronQATestExecution), 209021855U) },
		{ FAuracronQAPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronQAPerformanceData_Statics::NewStructOps, TEXT("AuracronQAPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronQAPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronQAPerformanceData), 2530139431U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronQABridge, UAuracronQABridge::StaticClass, TEXT("UAuracronQABridge"), &Z_Registration_Info_UClass_UAuracronQABridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronQABridge), 3940695206U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_3898424931(TEXT("/Script/AuracronQABridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronQABridge_Public_AuracronQABridge_h__Script_AuracronQABridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
