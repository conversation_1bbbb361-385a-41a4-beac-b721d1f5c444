// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Manager Implementation
// Bridge 2.14: PCG Framework - Debugging Tools

#include "AuracronPCGDebugSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// DEBUG SYSTEM MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGDebugSystemManager* UAuracronPCGDebugSystemManager::Instance = nullptr;

UAuracronPCGDebugSystemManager* UAuracronPCGDebugSystemManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGDebugSystemManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronPCGDebugSystemManager::InitializeDebugSystem()
{
    if (bIsSystemActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Debug system is already active"));
        return;
    }

    // Initialize default descriptors
    VisualizationDescriptor = FAuracronPCGDebugVisualizationDescriptor();
    ProfilingDescriptor = FAuracronPCGDebugProfilingDescriptor();
    InspectionDescriptor = FAuracronPCGDebugInspectionDescriptor();
    ExecutionDescriptor = FAuracronPCGDebugExecutionDescriptor();

    // Register console commands
    UAuracronPCGDebugConsoleCommands::RegisterConsoleCommands();

    // Enable visualization by default
    UAuracronPCGVisualDebugger::ToggleVisualization(true);

    bIsSystemActive = true;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug System initialized successfully"));
}

void UAuracronPCGDebugSystemManager::ShutdownDebugSystem()
{
    if (!bIsSystemActive)
    {
        return;
    }

    // Stop any active debugging
    StopFullDebugging();

    // Unregister console commands
    UAuracronPCGDebugConsoleCommands::UnregisterConsoleCommands();

    // Disable visualization
    UAuracronPCGVisualDebugger::ToggleVisualization(false);

    // Clear any debug display
    if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
    {
        UAuracronPCGVisualDebugger::ClearDebugDisplay(World);
    }

    bIsSystemActive = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug System shutdown successfully"));
}

bool UAuracronPCGDebugSystemManager::IsDebugSystemActive() const
{
    return bIsSystemActive;
}

void UAuracronPCGDebugSystemManager::SetVisualizationDescriptor(const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    VisualizationDescriptor = Descriptor;
    
    // Apply visualization mode
    UAuracronPCGVisualDebugger::SetVisualizationMode(Descriptor.VisualizationMode);
    UAuracronPCGVisualDebugger::ToggleVisualization(Descriptor.VisualizationMode != EAuracronPCGDebugVisualizationMode::None);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Visualization descriptor updated"));
}

void UAuracronPCGDebugSystemManager::SetProfilingDescriptor(const FAuracronPCGDebugProfilingDescriptor& Descriptor)
{
    ProfilingDescriptor = Descriptor;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Profiling descriptor updated"));
}

void UAuracronPCGDebugSystemManager::SetInspectionDescriptor(const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    InspectionDescriptor = Descriptor;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Inspection descriptor updated"));
}

void UAuracronPCGDebugSystemManager::SetExecutionDescriptor(const FAuracronPCGDebugExecutionDescriptor& Descriptor)
{
    ExecutionDescriptor = Descriptor;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution descriptor updated"));
}

void UAuracronPCGDebugSystemManager::StartFullDebugging(const UPCGGraph* Graph)
{
    if (!bIsSystemActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Debug system is not active. Call InitializeDebugSystem() first."));
        return;
    }

    if (!Graph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot start debugging with null graph"));
        return;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Starting full debugging for PCG graph"));

    // Start profiling if enabled
    if (ProfilingDescriptor.bEnableDetailedProfiling)
    {
        UAuracronPCGPerformanceProfiler::StartProfiling(ProfilingDescriptor);
    }

    // Start step-by-step execution if enabled
    if (ExecutionDescriptor.ExecutionMode == EAuracronPCGDebugExecutionMode::StepByStep)
    {
        UAuracronPCGStepByStepExecutor::StartStepByStepExecution(Graph, ExecutionDescriptor);
    }

    // Enable visualization
    if (VisualizationDescriptor.VisualizationMode != EAuracronPCGDebugVisualizationMode::None)
    {
        UAuracronPCGVisualDebugger::ToggleVisualization(true);
        UAuracronPCGVisualDebugger::SetVisualizationMode(VisualizationDescriptor.VisualizationMode);
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Full debugging started successfully"));
}

void UAuracronPCGDebugSystemManager::StopFullDebugging()
{
    if (!bIsSystemActive)
    {
        return;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Stopping full debugging"));

    // Stop profiling
    if (UAuracronPCGPerformanceProfiler::IsProfilingActive())
    {
        UAuracronPCGPerformanceProfiler::StopProfiling();
    }

    // Stop step-by-step execution
    if (UAuracronPCGStepByStepExecutor::IsExecutionActive())
    {
        UAuracronPCGStepByStepExecutor::StopStepByStepExecution();
    }

    // Clear visualization
    if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
    {
        UAuracronPCGVisualDebugger::ClearDebugDisplay(World);
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Full debugging stopped successfully"));
}

FString UAuracronPCGDebugSystemManager::GenerateFullDebugReport(const UPCGGraph* Graph)
{
    if (!Graph)
    {
        return TEXT("Cannot generate report for null graph");
    }

    FString Report = TEXT("=== PCG Full Debug Report ===\n\n");
    Report += FString::Printf(TEXT("Generated at: %s\n\n"), *FDateTime::Now().ToString());

    // Graph inspection
    Report += TEXT("=== Graph Inspection ===\n");
    Report += UAuracronPCGDataInspector::InspectGraph(Graph, InspectionDescriptor);
    Report += TEXT("\n");

    // Performance report
    if (UAuracronPCGPerformanceProfiler::IsProfilingActive())
    {
        Report += TEXT("=== Performance Report ===\n");
        Report += UAuracronPCGPerformanceProfiler::GeneratePerformanceReport();
        Report += TEXT("\n");
    }

    // Execution history
    if (UAuracronPCGStepByStepExecutor::IsExecutionActive())
    {
        Report += TEXT("=== Execution History ===\n");
        TArray<FString> History = UAuracronPCGStepByStepExecutor::GetExecutionHistory();
        for (int32 i = 0; i < History.Num(); i++)
        {
            Report += FString::Printf(TEXT("%d. %s\n"), i + 1, *History[i]);
        }
        Report += TEXT("\n");
    }

    // Breakpoints
    TArray<FString> Breakpoints = UAuracronPCGStepByStepExecutor::GetBreakpoints();
    if (Breakpoints.Num() > 0)
    {
        Report += TEXT("=== Active Breakpoints ===\n");
        for (const FString& Breakpoint : Breakpoints)
        {
            Report += FString::Printf(TEXT("- %s\n"), *Breakpoint);
        }
        Report += TEXT("\n");
    }

    // System status
    Report += TEXT("=== Debug System Status ===\n");
    Report += FString::Printf(TEXT("System Active: %s\n"), bIsSystemActive ? TEXT("Yes") : TEXT("No"));
    Report += FString::Printf(TEXT("Visualization Enabled: %s\n"), UAuracronPCGVisualDebugger::IsVisualizationEnabled() ? TEXT("Yes") : TEXT("No"));
    Report += FString::Printf(TEXT("Profiling Active: %s\n"), UAuracronPCGPerformanceProfiler::IsProfilingActive() ? TEXT("Yes") : TEXT("No"));
    Report += FString::Printf(TEXT("Step-by-Step Active: %s\n"), UAuracronPCGStepByStepExecutor::IsExecutionActive() ? TEXT("Yes") : TEXT("No"));

    return Report;
}

void UAuracronPCGDebugSystemManager::SaveDebugConfiguration(const FString& FilePath)
{
    TSharedPtr<FJsonObject> ConfigObject = MakeShareable(new FJsonObject);

    // Serialize visualization descriptor
    TSharedPtr<FJsonObject> VisObject = MakeShareable(new FJsonObject);
    SerializeVisualizationDescriptor(VisualizationDescriptor, VisObject);
    ConfigObject->SetObjectField(TEXT("Visualization"), VisObject);

    // Serialize profiling descriptor
    TSharedPtr<FJsonObject> ProfilingObject = MakeShareable(new FJsonObject);
    SerializeProfilingDescriptor(ProfilingDescriptor, ProfilingObject);
    ConfigObject->SetObjectField(TEXT("Profiling"), ProfilingObject);

    // Serialize inspection descriptor
    TSharedPtr<FJsonObject> InspectionObject = MakeShareable(new FJsonObject);
    SerializeInspectionDescriptor(InspectionDescriptor, InspectionObject);
    ConfigObject->SetObjectField(TEXT("Inspection"), InspectionObject);

    // Serialize execution descriptor
    TSharedPtr<FJsonObject> ExecutionObject = MakeShareable(new FJsonObject);
    SerializeExecutionDescriptor(ExecutionDescriptor, ExecutionObject);
    ConfigObject->SetObjectField(TEXT("Execution"), ExecutionObject);

    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ConfigObject.ToSharedRef(), Writer);

    if (FFileHelper::SaveStringToFile(OutputString, *FilePath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug configuration saved to: %s"), *FilePath);
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save debug configuration to: %s"), *FilePath);
    }
}

void UAuracronPCGDebugSystemManager::LoadDebugConfiguration(const FString& FilePath)
{
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to load debug configuration from: %s"), *FilePath);
        return;
    }

    TSharedPtr<FJsonObject> ConfigObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);

    if (!FJsonSerializer::Deserialize(Reader, ConfigObject) || !ConfigObject.IsValid())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to parse debug configuration JSON"));
        return;
    }

    // Deserialize visualization descriptor
    if (ConfigObject->HasField(TEXT("Visualization")))
    {
        TSharedPtr<FJsonObject> VisObject = ConfigObject->GetObjectField(TEXT("Visualization"));
        DeserializeVisualizationDescriptor(VisObject, VisualizationDescriptor);
    }

    // Deserialize profiling descriptor
    if (ConfigObject->HasField(TEXT("Profiling")))
    {
        TSharedPtr<FJsonObject> ProfilingObject = ConfigObject->GetObjectField(TEXT("Profiling"));
        DeserializeProfilingDescriptor(ProfilingObject, ProfilingDescriptor);
    }

    // Deserialize inspection descriptor
    if (ConfigObject->HasField(TEXT("Inspection")))
    {
        TSharedPtr<FJsonObject> InspectionObject = ConfigObject->GetObjectField(TEXT("Inspection"));
        DeserializeInspectionDescriptor(InspectionObject, InspectionDescriptor);
    }

    // Deserialize execution descriptor
    if (ConfigObject->HasField(TEXT("Execution")))
    {
        TSharedPtr<FJsonObject> ExecutionObject = ConfigObject->GetObjectField(TEXT("Execution"));
        DeserializeExecutionDescriptor(ExecutionObject, ExecutionDescriptor);
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug configuration loaded from: %s"), *FilePath);
}

void UAuracronPCGDebugSystemManager::ResetToDefaultConfiguration()
{
    VisualizationDescriptor = FAuracronPCGDebugVisualizationDescriptor();
    ProfilingDescriptor = FAuracronPCGDebugProfilingDescriptor();
    InspectionDescriptor = FAuracronPCGDebugInspectionDescriptor();
    ExecutionDescriptor = FAuracronPCGDebugExecutionDescriptor();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug configuration reset to defaults"));
}

// Helper serialization functions
void UAuracronPCGDebugSystemManager::SerializeVisualizationDescriptor(const FAuracronPCGDebugVisualizationDescriptor& Descriptor, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetNumberField(TEXT("VisualizationMode"), static_cast<int32>(Descriptor.VisualizationMode));
    JsonObject->SetBoolField(TEXT("bShowPoints"), Descriptor.bShowPoints);
    JsonObject->SetNumberField(TEXT("PointSize"), Descriptor.PointSize);
    JsonObject->SetBoolField(TEXT("bShowConnections"), Descriptor.bShowConnections);
    JsonObject->SetNumberField(TEXT("ConnectionThickness"), Descriptor.ConnectionThickness);
    JsonObject->SetBoolField(TEXT("bShowBoundingBoxes"), Descriptor.bShowBoundingBoxes);
    JsonObject->SetBoolField(TEXT("bShowAttributes"), Descriptor.bShowAttributes);
    JsonObject->SetBoolField(TEXT("bShowPerformanceInfo"), Descriptor.bShowPerformanceInfo);
    JsonObject->SetBoolField(TEXT("bShowDataFlow"), Descriptor.bShowDataFlow);
    JsonObject->SetBoolField(TEXT("bShowErrors"), Descriptor.bShowErrors);
    JsonObject->SetBoolField(TEXT("bPersistentDisplay"), Descriptor.bPersistentDisplay);
    JsonObject->SetNumberField(TEXT("DisplayDuration"), Descriptor.DisplayDuration);
}

void UAuracronPCGDebugSystemManager::DeserializeVisualizationDescriptor(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!JsonObject.IsValid()) return;

    Descriptor.VisualizationMode = static_cast<EAuracronPCGDebugVisualizationMode>(JsonObject->GetIntegerField(TEXT("VisualizationMode")));
    Descriptor.bShowPoints = JsonObject->GetBoolField(TEXT("bShowPoints"));
    Descriptor.PointSize = JsonObject->GetNumberField(TEXT("PointSize"));
    Descriptor.bShowConnections = JsonObject->GetBoolField(TEXT("bShowConnections"));
    Descriptor.ConnectionThickness = JsonObject->GetNumberField(TEXT("ConnectionThickness"));
    Descriptor.bShowBoundingBoxes = JsonObject->GetBoolField(TEXT("bShowBoundingBoxes"));
    Descriptor.bShowAttributes = JsonObject->GetBoolField(TEXT("bShowAttributes"));
    Descriptor.bShowPerformanceInfo = JsonObject->GetBoolField(TEXT("bShowPerformanceInfo"));
    Descriptor.bShowDataFlow = JsonObject->GetBoolField(TEXT("bShowDataFlow"));
    Descriptor.bShowErrors = JsonObject->GetBoolField(TEXT("bShowErrors"));
    Descriptor.bPersistentDisplay = JsonObject->GetBoolField(TEXT("bPersistentDisplay"));
    Descriptor.DisplayDuration = JsonObject->GetNumberField(TEXT("DisplayDuration"));
}

void UAuracronPCGDebugSystemManager::SerializeProfilingDescriptor(const FAuracronPCGDebugProfilingDescriptor& Descriptor, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetBoolField(TEXT("bEnableDetailedProfiling"), Descriptor.bEnableDetailedProfiling);
    JsonObject->SetBoolField(TEXT("bProfileMemoryAllocations"), Descriptor.bProfileMemoryAllocations);
    JsonObject->SetBoolField(TEXT("bProfileGPUUsage"), Descriptor.bProfileGPUUsage);
    JsonObject->SetNumberField(TEXT("ProfilingSampleRate"), Descriptor.ProfilingSampleRate);
    JsonObject->SetBoolField(TEXT("bKeepProfilingHistory"), Descriptor.bKeepProfilingHistory);
    JsonObject->SetNumberField(TEXT("MaxHistoryEntries"), Descriptor.MaxHistoryEntries);
    JsonObject->SetBoolField(TEXT("bAutoSaveProfilingData"), Descriptor.bAutoSaveProfilingData);
    JsonObject->SetNumberField(TEXT("SlowExecutionThreshold"), Descriptor.SlowExecutionThreshold);
    JsonObject->SetNumberField(TEXT("HighMemoryThreshold"), Descriptor.HighMemoryThreshold);
    JsonObject->SetBoolField(TEXT("bAlertOnThresholdExceeded"), Descriptor.bAlertOnThresholdExceeded);
    JsonObject->SetStringField(TEXT("ExportDirectory"), Descriptor.ExportDirectory);
}

void UAuracronPCGDebugSystemManager::DeserializeProfilingDescriptor(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGDebugProfilingDescriptor& Descriptor)
{
    if (!JsonObject.IsValid()) return;

    Descriptor.bEnableDetailedProfiling = JsonObject->GetBoolField(TEXT("bEnableDetailedProfiling"));
    Descriptor.bProfileMemoryAllocations = JsonObject->GetBoolField(TEXT("bProfileMemoryAllocations"));
    Descriptor.bProfileGPUUsage = JsonObject->GetBoolField(TEXT("bProfileGPUUsage"));
    Descriptor.ProfilingSampleRate = JsonObject->GetNumberField(TEXT("ProfilingSampleRate"));
    Descriptor.bKeepProfilingHistory = JsonObject->GetBoolField(TEXT("bKeepProfilingHistory"));
    Descriptor.MaxHistoryEntries = JsonObject->GetIntegerField(TEXT("MaxHistoryEntries"));
    Descriptor.bAutoSaveProfilingData = JsonObject->GetBoolField(TEXT("bAutoSaveProfilingData"));
    Descriptor.SlowExecutionThreshold = JsonObject->GetNumberField(TEXT("SlowExecutionThreshold"));
    Descriptor.HighMemoryThreshold = JsonObject->GetNumberField(TEXT("HighMemoryThreshold"));
    Descriptor.bAlertOnThresholdExceeded = JsonObject->GetBoolField(TEXT("bAlertOnThresholdExceeded"));
    Descriptor.ExportDirectory = JsonObject->GetStringField(TEXT("ExportDirectory"));
}

void UAuracronPCGDebugSystemManager::SerializeInspectionDescriptor(const FAuracronPCGDebugInspectionDescriptor& Descriptor, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetNumberField(TEXT("InspectionLevel"), static_cast<int32>(Descriptor.InspectionLevel));
    JsonObject->SetBoolField(TEXT("bInspectPointData"), Descriptor.bInspectPointData);
    JsonObject->SetBoolField(TEXT("bInspectSpatialData"), Descriptor.bInspectSpatialData);
    JsonObject->SetBoolField(TEXT("bInspectMetadata"), Descriptor.bInspectMetadata);
    JsonObject->SetBoolField(TEXT("bInspectAttributes"), Descriptor.bInspectAttributes);
    JsonObject->SetBoolField(TEXT("bInspectGraphStructure"), Descriptor.bInspectGraphStructure);
    JsonObject->SetBoolField(TEXT("bValidateDataIntegrity"), Descriptor.bValidateDataIntegrity);
    JsonObject->SetBoolField(TEXT("bGenerateInspectionReport"), Descriptor.bGenerateInspectionReport);
    JsonObject->SetStringField(TEXT("InspectionReportPath"), Descriptor.InspectionReportPath);
}

void UAuracronPCGDebugSystemManager::DeserializeInspectionDescriptor(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!JsonObject.IsValid()) return;

    Descriptor.InspectionLevel = static_cast<EAuracronPCGDebugInspectionLevel>(JsonObject->GetIntegerField(TEXT("InspectionLevel")));
    Descriptor.bInspectPointData = JsonObject->GetBoolField(TEXT("bInspectPointData"));
    Descriptor.bInspectSpatialData = JsonObject->GetBoolField(TEXT("bInspectSpatialData"));
    Descriptor.bInspectMetadata = JsonObject->GetBoolField(TEXT("bInspectMetadata"));
    Descriptor.bInspectAttributes = JsonObject->GetBoolField(TEXT("bInspectAttributes"));
    Descriptor.bInspectGraphStructure = JsonObject->GetBoolField(TEXT("bInspectGraphStructure"));
    Descriptor.bValidateDataIntegrity = JsonObject->GetBoolField(TEXT("bValidateDataIntegrity"));
    Descriptor.bGenerateInspectionReport = JsonObject->GetBoolField(TEXT("bGenerateInspectionReport"));
    Descriptor.InspectionReportPath = JsonObject->GetStringField(TEXT("InspectionReportPath"));
}

void UAuracronPCGDebugSystemManager::SerializeExecutionDescriptor(const FAuracronPCGDebugExecutionDescriptor& Descriptor, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetNumberField(TEXT("ExecutionMode"), static_cast<int32>(Descriptor.ExecutionMode));
    JsonObject->SetBoolField(TEXT("bPauseAtEachNode"), Descriptor.bPauseAtEachNode);
    JsonObject->SetBoolField(TEXT("bRequireManualContinue"), Descriptor.bRequireManualContinue);
    JsonObject->SetNumberField(TEXT("StepDelay"), Descriptor.StepDelay);
    JsonObject->SetBoolField(TEXT("bBreakOnErrors"), Descriptor.bBreakOnErrors);
    JsonObject->SetBoolField(TEXT("bBreakOnWarnings"), Descriptor.bBreakOnWarnings);
    JsonObject->SetNumberField(TEXT("SlowMotionFactor"), Descriptor.SlowMotionFactor);
    JsonObject->SetBoolField(TEXT("bRecordExecution"), Descriptor.bRecordExecution);
    JsonObject->SetBoolField(TEXT("bAllowReplay"), Descriptor.bAllowReplay);
    JsonObject->SetNumberField(TEXT("MaxReplaySteps"), Descriptor.MaxReplaySteps);
}

void UAuracronPCGDebugSystemManager::DeserializeExecutionDescriptor(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGDebugExecutionDescriptor& Descriptor)
{
    if (!JsonObject.IsValid()) return;

    Descriptor.ExecutionMode = static_cast<EAuracronPCGDebugExecutionMode>(JsonObject->GetIntegerField(TEXT("ExecutionMode")));
    Descriptor.bPauseAtEachNode = JsonObject->GetBoolField(TEXT("bPauseAtEachNode"));
    Descriptor.bRequireManualContinue = JsonObject->GetBoolField(TEXT("bRequireManualContinue"));
    Descriptor.StepDelay = JsonObject->GetNumberField(TEXT("StepDelay"));
    Descriptor.bBreakOnErrors = JsonObject->GetBoolField(TEXT("bBreakOnErrors"));
    Descriptor.bBreakOnWarnings = JsonObject->GetBoolField(TEXT("bBreakOnWarnings"));
    Descriptor.SlowMotionFactor = JsonObject->GetNumberField(TEXT("SlowMotionFactor"));
    Descriptor.bRecordExecution = JsonObject->GetBoolField(TEXT("bRecordExecution"));
    Descriptor.bAllowReplay = JsonObject->GetBoolField(TEXT("bAllowReplay"));
    Descriptor.MaxReplaySteps = JsonObject->GetIntegerField(TEXT("MaxReplaySteps"));
}
