// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronEOSBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronEOSBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONEOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronEOSBridge();
AURACRONEOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronEOSBridge_NoRegister();
AURACRONEOSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus();
AURACRONEOSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType();
AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature();
AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature();
AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature();
AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature();
AURACRONEOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEOSAchievement();
AURACRONEOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEOSFriend();
AURACRONEOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSessionConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronEOSBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronEOSConnectionStatus **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus;
static UEnum* EAuracronEOSConnectionStatus_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus, (UObject*)Z_Construct_UPackage__Script_AuracronEOSBridge(), TEXT("EAuracronEOSConnectionStatus"));
	}
	return Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.OuterSingleton;
}
template<> AURACRONEOSBRIDGE_API UEnum* StaticEnum<EAuracronEOSConnectionStatus>()
{
	return EAuracronEOSConnectionStatus_StaticEnum();
}
struct Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Authenticated.DisplayName", "Authenticated" },
		{ "Authenticated.Name", "EAuracronEOSConnectionStatus::Authenticated" },
		{ "Authenticating.DisplayName", "Authenticating" },
		{ "Authenticating.Name", "EAuracronEOSConnectionStatus::Authenticating" },
		{ "Banned.DisplayName", "Banned" },
		{ "Banned.Name", "EAuracronEOSConnectionStatus::Banned" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para status de conex\xc3\x83\xc2\xa3o EOS\n */" },
#endif
		{ "Connected.DisplayName", "Connected" },
		{ "Connected.Name", "EAuracronEOSConnectionStatus::Connected" },
		{ "Connecting.DisplayName", "Connecting" },
		{ "Connecting.Name", "EAuracronEOSConnectionStatus::Connecting" },
		{ "Disconnected.DisplayName", "Disconnected" },
		{ "Disconnected.Name", "EAuracronEOSConnectionStatus::Disconnected" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronEOSConnectionStatus::Error" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
		{ "Suspended.DisplayName", "Suspended" },
		{ "Suspended.Name", "EAuracronEOSConnectionStatus::Suspended" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para status de conex\xc3\x83\xc2\xa3o EOS" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronEOSConnectionStatus::Disconnected", (int64)EAuracronEOSConnectionStatus::Disconnected },
		{ "EAuracronEOSConnectionStatus::Connecting", (int64)EAuracronEOSConnectionStatus::Connecting },
		{ "EAuracronEOSConnectionStatus::Connected", (int64)EAuracronEOSConnectionStatus::Connected },
		{ "EAuracronEOSConnectionStatus::Authenticating", (int64)EAuracronEOSConnectionStatus::Authenticating },
		{ "EAuracronEOSConnectionStatus::Authenticated", (int64)EAuracronEOSConnectionStatus::Authenticated },
		{ "EAuracronEOSConnectionStatus::Error", (int64)EAuracronEOSConnectionStatus::Error },
		{ "EAuracronEOSConnectionStatus::Banned", (int64)EAuracronEOSConnectionStatus::Banned },
		{ "EAuracronEOSConnectionStatus::Suspended", (int64)EAuracronEOSConnectionStatus::Suspended },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
	nullptr,
	"EAuracronEOSConnectionStatus",
	"EAuracronEOSConnectionStatus",
	Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus()
{
	if (!Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.InnerSingleton, Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus.InnerSingleton;
}
// ********** End Enum EAuracronEOSConnectionStatus ************************************************

// ********** Begin Enum EAuracronSessionType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSessionType;
static UEnum* EAuracronSessionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType, (UObject*)Z_Construct_UPackage__Script_AuracronEOSBridge(), TEXT("EAuracronSessionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton;
}
template<> AURACRONEOSBRIDGE_API UEnum* StaticEnum<EAuracronSessionType>()
{
	return EAuracronSessionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Casual.DisplayName", "Casual Match" },
		{ "Casual.Name", "EAuracronSessionType::Casual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de sess\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "Custom.DisplayName", "Custom Game" },
		{ "Custom.Name", "EAuracronSessionType::Custom" },
		{ "Event.DisplayName", "Event Match" },
		{ "Event.Name", "EAuracronSessionType::Event" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronSessionType::None" },
		{ "Ranked.DisplayName", "Ranked Match" },
		{ "Ranked.Name", "EAuracronSessionType::Ranked" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de sess\xc3\x83\xc2\xa3o" },
#endif
		{ "Tournament.DisplayName", "Tournament" },
		{ "Tournament.Name", "EAuracronSessionType::Tournament" },
		{ "Training.DisplayName", "Training" },
		{ "Training.Name", "EAuracronSessionType::Training" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSessionType::None", (int64)EAuracronSessionType::None },
		{ "EAuracronSessionType::Ranked", (int64)EAuracronSessionType::Ranked },
		{ "EAuracronSessionType::Casual", (int64)EAuracronSessionType::Casual },
		{ "EAuracronSessionType::Custom", (int64)EAuracronSessionType::Custom },
		{ "EAuracronSessionType::Training", (int64)EAuracronSessionType::Training },
		{ "EAuracronSessionType::Tournament", (int64)EAuracronSessionType::Tournament },
		{ "EAuracronSessionType::Event", (int64)EAuracronSessionType::Event },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
	nullptr,
	"EAuracronSessionType",
	"EAuracronSessionType",
	Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton, Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton;
}
// ********** End Enum EAuracronSessionType ********************************************************

// ********** Begin ScriptStruct FAuracronSessionConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration;
class UScriptStruct* FAuracronSessionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSessionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronEOSBridge(), TEXT("AuracronSessionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de sess\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionName_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionType_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "2" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de jogadores */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPrivate_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sess\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 privada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sess\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 privada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAntiCheat_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredRegion_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\x83\xc2\xa3o preferida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\x83\xc2\xa3o preferida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapName_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mapa da sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mapa da sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameMode_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Modo de jogo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modo de jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomSettings_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es customizadas (n\xc3\xa3o pode ser replicado) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es customizadas (n\xc3\xa3o pode ser replicado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionPassword_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Senha da sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Senha da sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowSpectators_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permitir espectadores */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permitir espectadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpectators_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "100" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de espectadores */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de espectadores" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static void NewProp_bIsPrivate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPrivate;
	static void NewProp_bUseAntiCheat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAntiCheat;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PreferredRegion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MapName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSettings_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomSettings;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionPassword;
	static void NewProp_bAllowSpectators_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowSpectators;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSpectators;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSessionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionName = { "SessionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, SessionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionName_MetaData), NewProp_SessionName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, SessionType), Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionType_MetaData), NewProp_SessionType_MetaData) }; // 1060124687
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bIsPrivate_SetBit(void* Obj)
{
	((FAuracronSessionConfiguration*)Obj)->bIsPrivate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bIsPrivate = { "bIsPrivate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bIsPrivate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPrivate_MetaData), NewProp_bIsPrivate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bUseAntiCheat_SetBit(void* Obj)
{
	((FAuracronSessionConfiguration*)Obj)->bUseAntiCheat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bUseAntiCheat = { "bUseAntiCheat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bUseAntiCheat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAntiCheat_MetaData), NewProp_bUseAntiCheat_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_PreferredRegion = { "PreferredRegion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, PreferredRegion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredRegion_MetaData), NewProp_PreferredRegion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MapName = { "MapName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, MapName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapName_MetaData), NewProp_MapName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_GameMode = { "GameMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, GameMode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameMode_MetaData), NewProp_GameMode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings_ValueProp = { "CustomSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings_Key_KeyProp = { "CustomSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings = { "CustomSettings", nullptr, (EPropertyFlags)0x0010000080000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, CustomSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomSettings_MetaData), NewProp_CustomSettings_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionPassword = { "SessionPassword", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, SessionPassword), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionPassword_MetaData), NewProp_SessionPassword_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bAllowSpectators_SetBit(void* Obj)
{
	((FAuracronSessionConfiguration*)Obj)->bAllowSpectators = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bAllowSpectators = { "bAllowSpectators", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bAllowSpectators_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowSpectators_MetaData), NewProp_bAllowSpectators_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MaxSpectators = { "MaxSpectators", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSessionConfiguration, MaxSpectators), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpectators_MetaData), NewProp_MaxSpectators_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bIsPrivate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bUseAntiCheat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_PreferredRegion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MapName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_GameMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_CustomSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_SessionPassword,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_bAllowSpectators,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewProp_MaxSpectators,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
	nullptr,
	&NewStructOps,
	"AuracronSessionConfiguration",
	Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::PropPointers),
	sizeof(FAuracronSessionConfiguration),
	alignof(FAuracronSessionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSessionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSessionConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronEOSFriend ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronEOSFriend;
class UScriptStruct* FAuracronEOSFriend::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronEOSFriend, (UObject*)Z_Construct_UPackage__Script_AuracronEOSBridge(), TEXT("AuracronEOSFriend"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para amigo EOS\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para amigo EOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserID_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do usu\xc3\x83\xc2\xa1rio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do usu\xc3\x83\xc2\xa1rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOnline_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status online */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status online" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PresenceStatus_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status de presen\xc3\x83\xc2\xa7""a */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status de presen\xc3\x83\xc2\xa7""a" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPlayingAuracron_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jogando Auracron */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jogando Auracron" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeInvited_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode ser convidado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode ser convidado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInMatch_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\x83\xc2\xa1 em partida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\x83\xc2\xa1 em partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSessionID_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da sess\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da sess\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastOnlineTime_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima vez online */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima vez online" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserAvatar_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Avatar do usu\xc3\x83\xc2\xa1rio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Avatar do usu\xc3\x83\xc2\xa1rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLevel_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRank_MetaData[] = {
		{ "Category", "EOS Friend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rank do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rank do jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static void NewProp_bIsOnline_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOnline;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PresenceStatus;
	static void NewProp_bIsPlayingAuracron_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPlayingAuracron;
	static void NewProp_bCanBeInvited_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeInvited;
	static void NewProp_bIsInMatch_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInMatch;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentSessionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastOnlineTime;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UserAvatar;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerRank;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronEOSFriend>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_UserID = { "UserID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, UserID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserID_MetaData), NewProp_UserID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsOnline_SetBit(void* Obj)
{
	((FAuracronEOSFriend*)Obj)->bIsOnline = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsOnline = { "bIsOnline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSFriend), &Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsOnline_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOnline_MetaData), NewProp_bIsOnline_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PresenceStatus = { "PresenceStatus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, PresenceStatus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PresenceStatus_MetaData), NewProp_PresenceStatus_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsPlayingAuracron_SetBit(void* Obj)
{
	((FAuracronEOSFriend*)Obj)->bIsPlayingAuracron = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsPlayingAuracron = { "bIsPlayingAuracron", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSFriend), &Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsPlayingAuracron_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPlayingAuracron_MetaData), NewProp_bIsPlayingAuracron_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bCanBeInvited_SetBit(void* Obj)
{
	((FAuracronEOSFriend*)Obj)->bCanBeInvited = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bCanBeInvited = { "bCanBeInvited", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSFriend), &Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bCanBeInvited_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeInvited_MetaData), NewProp_bCanBeInvited_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsInMatch_SetBit(void* Obj)
{
	((FAuracronEOSFriend*)Obj)->bIsInMatch = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsInMatch = { "bIsInMatch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSFriend), &Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsInMatch_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInMatch_MetaData), NewProp_bIsInMatch_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_CurrentSessionID = { "CurrentSessionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, CurrentSessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSessionID_MetaData), NewProp_CurrentSessionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_LastOnlineTime = { "LastOnlineTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, LastOnlineTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastOnlineTime_MetaData), NewProp_LastOnlineTime_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_UserAvatar = { "UserAvatar", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, UserAvatar), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserAvatar_MetaData), NewProp_UserAvatar_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PlayerLevel = { "PlayerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, PlayerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLevel_MetaData), NewProp_PlayerLevel_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PlayerRank = { "PlayerRank", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSFriend, PlayerRank), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRank_MetaData), NewProp_PlayerRank_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_UserID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsOnline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PresenceStatus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsPlayingAuracron,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bCanBeInvited,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_bIsInMatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_CurrentSessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_LastOnlineTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_UserAvatar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PlayerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewProp_PlayerRank,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
	nullptr,
	&NewStructOps,
	"AuracronEOSFriend",
	Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::PropPointers),
	sizeof(FAuracronEOSFriend),
	alignof(FAuracronEOSFriend),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronEOSFriend()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.InnerSingleton, Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEOSFriend.InnerSingleton;
}
// ********** End ScriptStruct FAuracronEOSFriend **************************************************

// ********** Begin ScriptStruct FAuracronEOSAchievement *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement;
class UScriptStruct* FAuracronEOSAchievement::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronEOSAchievement, (UObject*)Z_Construct_UPackage__Script_AuracronEOSBridge(), TEXT("AuracronEOSAchievement"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para achievement EOS\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para achievement EOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementID_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do achievement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementName_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do achievement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementDescription_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do achievement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Foi desbloqueado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foi desbloqueado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "Category", "EOS Achievement" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockDate_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de desbloqueio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de desbloqueio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSecret_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\xb0 achievement secreto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\xb0 achievement secreto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "Category", "EOS Achievement" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos concedidos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos concedidos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementIcon_MetaData[] = {
		{ "Category", "EOS Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone do achievement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone do achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rarity_MetaData[] = {
		{ "Category", "EOS Achievement" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade do achievement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade do achievement" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AchievementName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AchievementDescription;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockDate;
	static void NewProp_bIsSecret_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSecret;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Points;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AchievementIcon;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronEOSAchievement>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, AchievementID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementID_MetaData), NewProp_AchievementID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementName = { "AchievementName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, AchievementName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementName_MetaData), NewProp_AchievementName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementDescription = { "AchievementDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, AchievementDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementDescription_MetaData), NewProp_AchievementDescription_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FAuracronEOSAchievement*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSAchievement), &Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, Progress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_UnlockDate = { "UnlockDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, UnlockDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockDate_MetaData), NewProp_UnlockDate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsSecret_SetBit(void* Obj)
{
	((FAuracronEOSAchievement*)Obj)->bIsSecret = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsSecret = { "bIsSecret", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronEOSAchievement), &Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsSecret_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSecret_MetaData), NewProp_bIsSecret_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, Points), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementIcon = { "AchievementIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, AchievementIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementIcon_MetaData), NewProp_AchievementIcon_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEOSAchievement, Rarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rarity_MetaData), NewProp_Rarity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_UnlockDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_bIsSecret,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_AchievementIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewProp_Rarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
	nullptr,
	&NewStructOps,
	"AuracronEOSAchievement",
	Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::PropPointers),
	sizeof(FAuracronEOSAchievement),
	alignof(FAuracronEOSAchievement),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronEOSAchievement()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.InnerSingleton, Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement.InnerSingleton;
}
// ********** End ScriptStruct FAuracronEOSAchievement *********************************************

// ********** Begin Delegate FOnEOSLoginCompleted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics
{
	struct AuracronEOSBridge_eventOnEOSLoginCompleted_Parms
	{
		bool bWasSuccessful;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando login \xc3\x83\xc2\xa9 completado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando login \xc3\x83\xc2\xa9 completado" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bWasSuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasSuccessful;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_bWasSuccessful_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventOnEOSLoginCompleted_Parms*)Obj)->bWasSuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_bWasSuccessful = { "bWasSuccessful", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventOnEOSLoginCompleted_Parms), &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_bWasSuccessful_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventOnEOSLoginCompleted_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_bWasSuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "OnEOSLoginCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::AuracronEOSBridge_eventOnEOSLoginCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::AuracronEOSBridge_eventOnEOSLoginCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronEOSBridge::FOnEOSLoginCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnEOSLoginCompleted, bool bWasSuccessful, const FString& ErrorMessage)
{
	struct AuracronEOSBridge_eventOnEOSLoginCompleted_Parms
	{
		bool bWasSuccessful;
		FString ErrorMessage;
	};
	AuracronEOSBridge_eventOnEOSLoginCompleted_Parms Parms;
	Parms.bWasSuccessful=bWasSuccessful ? true : false;
	Parms.ErrorMessage=ErrorMessage;
	OnEOSLoginCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnEOSLoginCompleted ****************************************************

// ********** Begin Delegate FOnSessionCreated *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics
{
	struct AuracronEOSBridge_eventOnSessionCreated_Parms
	{
		bool bWasSuccessful;
		FString SessionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando sess\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 criada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando sess\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 criada" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bWasSuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasSuccessful;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bWasSuccessful_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventOnSessionCreated_Parms*)Obj)->bWasSuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bWasSuccessful = { "bWasSuccessful", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventOnSessionCreated_Parms), &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bWasSuccessful_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventOnSessionCreated_Parms, SessionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bWasSuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_SessionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "OnSessionCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::AuracronEOSBridge_eventOnSessionCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::AuracronEOSBridge_eventOnSessionCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronEOSBridge::FOnSessionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSessionCreated, bool bWasSuccessful, const FString& SessionID)
{
	struct AuracronEOSBridge_eventOnSessionCreated_Parms
	{
		bool bWasSuccessful;
		FString SessionID;
	};
	AuracronEOSBridge_eventOnSessionCreated_Parms Parms;
	Parms.bWasSuccessful=bWasSuccessful ? true : false;
	Parms.SessionID=SessionID;
	OnSessionCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSessionCreated *******************************************************

// ********** Begin Delegate FOnAchievementUnlocked ************************************************
struct Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics
{
	struct AuracronEOSBridge_eventOnAchievementUnlocked_Parms
	{
		FAuracronEOSAchievement Achievement;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando achievement \xc3\x83\xc2\xa9 desbloqueado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando achievement \xc3\x83\xc2\xa9 desbloqueado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Achievement;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_Achievement = { "Achievement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventOnAchievementUnlocked_Parms, Achievement), Z_Construct_UScriptStruct_FAuracronEOSAchievement, METADATA_PARAMS(0, nullptr) }; // 3927995631
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_Achievement,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "OnAchievementUnlocked__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::AuracronEOSBridge_eventOnAchievementUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::AuracronEOSBridge_eventOnAchievementUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronEOSBridge::FOnAchievementUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementUnlocked, FAuracronEOSAchievement Achievement)
{
	struct AuracronEOSBridge_eventOnAchievementUnlocked_Parms
	{
		FAuracronEOSAchievement Achievement;
	};
	AuracronEOSBridge_eventOnAchievementUnlocked_Parms Parms;
	Parms.Achievement=Achievement;
	OnAchievementUnlocked.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAchievementUnlocked **************************************************

// ********** Begin Delegate FOnFriendOnline *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics
{
	struct AuracronEOSBridge_eventOnFriendOnline_Parms
	{
		FAuracronEOSFriend Friend;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando amigo fica online */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando amigo fica online" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Friend;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::NewProp_Friend = { "Friend", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventOnFriendOnline_Parms, Friend), Z_Construct_UScriptStruct_FAuracronEOSFriend, METADATA_PARAMS(0, nullptr) }; // 4205638700
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::NewProp_Friend,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "OnFriendOnline__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::AuracronEOSBridge_eventOnFriendOnline_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::AuracronEOSBridge_eventOnFriendOnline_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronEOSBridge::FOnFriendOnline_DelegateWrapper(const FMulticastScriptDelegate& OnFriendOnline, FAuracronEOSFriend Friend)
{
	struct AuracronEOSBridge_eventOnFriendOnline_Parms
	{
		FAuracronEOSFriend Friend;
	};
	AuracronEOSBridge_eventOnFriendOnline_Parms Parms;
	Parms.Friend=Friend;
	OnFriendOnline.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFriendOnline *********************************************************

// ********** Begin Class UAuracronEOSBridge Function AddFriend ************************************
struct Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics
{
	struct AuracronEOSBridge_eventAddFriend_Parms
	{
		FString UserID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Friends" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar amigo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar amigo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_UserID = { "UserID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventAddFriend_Parms, UserID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserID_MetaData), NewProp_UserID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventAddFriend_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventAddFriend_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_UserID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "AddFriend", Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::AuracronEOSBridge_eventAddFriend_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::AuracronEOSBridge_eventAddFriend_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_AddFriend()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_AddFriend_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execAddFriend)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_UserID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddFriend(Z_Param_UserID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function AddFriend **************************************

// ********** Begin Class UAuracronEOSBridge Function CreateSession ********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics
{
	struct AuracronEOSBridge_eventCreateSession_Parms
	{
		FAuracronSessionConfiguration SessionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar sess\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventCreateSession_Parms, SessionConfig), Z_Construct_UScriptStruct_FAuracronSessionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 236244013
void Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventCreateSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventCreateSession_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_SessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "CreateSession", Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::AuracronEOSBridge_eventCreateSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::AuracronEOSBridge_eventCreateSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_CreateSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_CreateSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execCreateSession)
{
	P_GET_STRUCT_REF(FAuracronSessionConfiguration,Z_Param_Out_SessionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateSession(Z_Param_Out_SessionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function CreateSession **********************************

// ********** Begin Class UAuracronEOSBridge Function DeletePlayerData *****************************
struct Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics
{
	struct AuracronEOSBridge_eventDeletePlayerData_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|PlayerData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Deletar dados do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deletar dados do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventDeletePlayerData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventDeletePlayerData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventDeletePlayerData_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "DeletePlayerData", Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::AuracronEOSBridge_eventDeletePlayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::AuracronEOSBridge_eventDeletePlayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execDeletePlayerData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeletePlayerData(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function DeletePlayerData *******************************

// ********** Begin Class UAuracronEOSBridge Function DestroySession *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics
{
	struct AuracronEOSBridge_eventDestroySession_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Destruir sess\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destruir sess\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventDestroySession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventDestroySession_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "DestroySession", Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::AuracronEOSBridge_eventDestroySession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::AuracronEOSBridge_eventDestroySession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_DestroySession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_DestroySession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execDestroySession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroySession();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function DestroySession *********************************

// ********** Begin Class UAuracronEOSBridge Function FindSessions *********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics
{
	struct AuracronEOSBridge_eventFindSessions_Parms
	{
		EAuracronSessionType SessionType;
		FString Region;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Encontrar sess\xc3\x83\xc2\xb5""es\n     */" },
#endif
		{ "CPP_Default_Region", "" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar sess\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Region_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Region;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventFindSessions_Parms, SessionType), Z_Construct_UEnum_AuracronEOSBridge_EAuracronSessionType, METADATA_PARAMS(0, nullptr) }; // 1060124687
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_Region = { "Region", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventFindSessions_Parms, Region), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Region_MetaData), NewProp_Region_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventFindSessions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventFindSessions_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_Region,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "FindSessions", Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::AuracronEOSBridge_eventFindSessions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::AuracronEOSBridge_eventFindSessions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_FindSessions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_FindSessions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execFindSessions)
{
	P_GET_ENUM(EAuracronSessionType,Z_Param_SessionType);
	P_GET_PROPERTY(FStrProperty,Z_Param_Region);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FindSessions(EAuracronSessionType(Z_Param_SessionType),Z_Param_Region);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function FindSessions ***********************************

// ********** Begin Class UAuracronEOSBridge Function GetAchievements ******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics
{
	struct AuracronEOSBridge_eventGetAchievements_Parms
	{
		TArray<FAuracronEOSAchievement> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter achievements\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter achievements" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEOSAchievement, METADATA_PARAMS(0, nullptr) }; // 3927995631
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetAchievements_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3927995631
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetAchievements", Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::AuracronEOSBridge_eventGetAchievements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::AuracronEOSBridge_eventGetAchievements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetAchievements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronEOSAchievement>*)Z_Param__Result=P_THIS->GetAchievements();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetAchievements ********************************

// ********** Begin Class UAuracronEOSBridge Function GetAuthenticationStatus **********************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics
{
	struct AuracronEOSBridge_eventGetAuthenticationStatus_Parms
	{
		EAuracronEOSConnectionStatus ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Authentication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar status de autentica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar status de autentica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetAuthenticationStatus_Parms, ReturnValue), Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus, METADATA_PARAMS(0, nullptr) }; // 2538958531
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetAuthenticationStatus", Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::AuracronEOSBridge_eventGetAuthenticationStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::AuracronEOSBridge_eventGetAuthenticationStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetAuthenticationStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronEOSConnectionStatus*)Z_Param__Result=P_THIS->GetAuthenticationStatus();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetAuthenticationStatus ************************

// ********** Begin Class UAuracronEOSBridge Function GetDisplayName *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics
{
	struct AuracronEOSBridge_eventGetDisplayName_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Authentication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetDisplayName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetDisplayName", Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::AuracronEOSBridge_eventGetDisplayName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::AuracronEOSBridge_eventGetDisplayName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetDisplayName)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetDisplayName();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetDisplayName *********************************

// ********** Begin Class UAuracronEOSBridge Function GetFriendPresence ****************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics
{
	struct AuracronEOSBridge_eventGetFriendPresence_Parms
	{
		FString FriendID;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Presence" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter presen\xc3\x83\xc2\xa7""a de amigo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter presen\xc3\x83\xc2\xa7""a de amigo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FriendID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FriendID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::NewProp_FriendID = { "FriendID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetFriendPresence_Parms, FriendID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FriendID_MetaData), NewProp_FriendID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetFriendPresence_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::NewProp_FriendID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetFriendPresence", Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::AuracronEOSBridge_eventGetFriendPresence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::AuracronEOSBridge_eventGetFriendPresence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetFriendPresence)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FriendID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetFriendPresence(Z_Param_FriendID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetFriendPresence ******************************

// ********** Begin Class UAuracronEOSBridge Function GetFriendsList *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics
{
	struct AuracronEOSBridge_eventGetFriendsList_Parms
	{
		TArray<FAuracronEOSFriend> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Friends" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter lista de amigos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter lista de amigos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEOSFriend, METADATA_PARAMS(0, nullptr) }; // 4205638700
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetFriendsList_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4205638700
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetFriendsList", Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::AuracronEOSBridge_eventGetFriendsList_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::AuracronEOSBridge_eventGetFriendsList_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetFriendsList)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronEOSFriend>*)Z_Param__Result=P_THIS->GetFriendsList();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetFriendsList *********************************

// ********** Begin Class UAuracronEOSBridge Function GetPlayerRank ********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics
{
	struct AuracronEOSBridge_eventGetPlayerRank_Parms
	{
		FString LeaderboardID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Leaderboards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter ranking do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ranking do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::NewProp_LeaderboardID = { "LeaderboardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetPlayerRank_Parms, LeaderboardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardID_MetaData), NewProp_LeaderboardID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetPlayerRank_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::NewProp_LeaderboardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetPlayerRank", Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::AuracronEOSBridge_eventGetPlayerRank_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::AuracronEOSBridge_eventGetPlayerRank_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetPlayerRank)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayerRank(Z_Param_LeaderboardID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetPlayerRank **********************************

// ********** Begin Class UAuracronEOSBridge Function GetTopRankings *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics
{
	struct AuracronEOSBridge_eventGetTopRankings_Parms
	{
		FString LeaderboardID;
		int32 Count;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Leaderboards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter top rankings\n     */" },
#endif
		{ "CPP_Default_Count", "10" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter top rankings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_LeaderboardID = { "LeaderboardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetTopRankings_Parms, LeaderboardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardID_MetaData), NewProp_LeaderboardID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetTopRankings_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetTopRankings_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_LeaderboardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetTopRankings", Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::AuracronEOSBridge_eventGetTopRankings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::AuracronEOSBridge_eventGetTopRankings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetTopRankings)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetTopRankings(Z_Param_LeaderboardID,Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetTopRankings *********************************

// ********** Begin Class UAuracronEOSBridge Function GetUserID ************************************
struct Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics
{
	struct AuracronEOSBridge_eventGetUserID_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Authentication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter ID do usu\xc3\x83\xc2\xa1rio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ID do usu\xc3\x83\xc2\xa1rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventGetUserID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "GetUserID", Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::AuracronEOSBridge_eventGetUserID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::AuracronEOSBridge_eventGetUserID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_GetUserID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_GetUserID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execGetUserID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetUserID();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function GetUserID **************************************

// ********** Begin Class UAuracronEOSBridge Function InviteFriendToSession ************************
struct Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics
{
	struct AuracronEOSBridge_eventInviteFriendToSession_Parms
	{
		FString FriendID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Friends" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Convidar amigo para sess\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convidar amigo para sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FriendID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FriendID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_FriendID = { "FriendID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventInviteFriendToSession_Parms, FriendID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FriendID_MetaData), NewProp_FriendID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventInviteFriendToSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventInviteFriendToSession_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_FriendID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "InviteFriendToSession", Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::AuracronEOSBridge_eventInviteFriendToSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::AuracronEOSBridge_eventInviteFriendToSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execInviteFriendToSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FriendID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InviteFriendToSession(Z_Param_FriendID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function InviteFriendToSession **************************

// ********** Begin Class UAuracronEOSBridge Function IsAchievementUnlocked ************************
struct Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics
{
	struct AuracronEOSBridge_eventIsAchievementUnlocked_Parms
	{
		FString AchievementID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se achievement foi desbloqueado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se achievement foi desbloqueado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AchievementID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventIsAchievementUnlocked_Parms, AchievementID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementID_MetaData), NewProp_AchievementID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventIsAchievementUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventIsAchievementUnlocked_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "IsAchievementUnlocked", Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::AuracronEOSBridge_eventIsAchievementUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::AuracronEOSBridge_eventIsAchievementUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execIsAchievementUnlocked)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AchievementID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAchievementUnlocked(Z_Param_AchievementID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function IsAchievementUnlocked **************************

// ********** Begin Class UAuracronEOSBridge Function JoinSession **********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics
{
	struct AuracronEOSBridge_eventJoinSession_Parms
	{
		FString SessionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Entrar em sess\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entrar em sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventJoinSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventJoinSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventJoinSession_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "JoinSession", Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::AuracronEOSBridge_eventJoinSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::AuracronEOSBridge_eventJoinSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_JoinSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_JoinSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execJoinSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->JoinSession(Z_Param_SessionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function JoinSession ************************************

// ********** Begin Class UAuracronEOSBridge Function LeaveSession *********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics
{
	struct AuracronEOSBridge_eventLeaveSession_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Sair da sess\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sair da sess\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventLeaveSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventLeaveSession_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "LeaveSession", Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::AuracronEOSBridge_eventLeaveSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::AuracronEOSBridge_eventLeaveSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execLeaveSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LeaveSession();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function LeaveSession ***********************************

// ********** Begin Class UAuracronEOSBridge Function LoadFriendsList ******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics
{
	struct AuracronEOSBridge_eventLoadFriendsList_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Friends" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar lista de amigos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar lista de amigos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventLoadFriendsList_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventLoadFriendsList_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "LoadFriendsList", Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::AuracronEOSBridge_eventLoadFriendsList_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::AuracronEOSBridge_eventLoadFriendsList_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execLoadFriendsList)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadFriendsList();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function LoadFriendsList ********************************

// ********** Begin Class UAuracronEOSBridge Function LoadPlayerData *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics
{
	struct AuracronEOSBridge_eventLoadPlayerData_Parms
	{
		FString Key;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|PlayerData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar dados do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar dados do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventLoadPlayerData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventLoadPlayerData_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "LoadPlayerData", Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::AuracronEOSBridge_eventLoadPlayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::AuracronEOSBridge_eventLoadPlayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execLoadPlayerData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->LoadPlayerData(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function LoadPlayerData *********************************

// ********** Begin Class UAuracronEOSBridge Function LoginWithEOS *********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics
{
	struct AuracronEOSBridge_eventLoginWithEOS_Parms
	{
		FString LoginType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Authentication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Fazer login com EOS\n     */" },
#endif
		{ "CPP_Default_LoginType", "AccountPortal" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fazer login com EOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoginType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_LoginType = { "LoginType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventLoginWithEOS_Parms, LoginType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginType_MetaData), NewProp_LoginType_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventLoginWithEOS_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventLoginWithEOS_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_LoginType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "LoginWithEOS", Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::AuracronEOSBridge_eventLoginWithEOS_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::AuracronEOSBridge_eventLoginWithEOS_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execLoginWithEOS)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoginType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoginWithEOS(Z_Param_LoginType);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function LoginWithEOS ***********************************

// ********** Begin Class UAuracronEOSBridge Function LogoutFromEOS ********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics
{
	struct AuracronEOSBridge_eventLogoutFromEOS_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Authentication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Fazer logout\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fazer logout" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventLogoutFromEOS_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventLogoutFromEOS_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "LogoutFromEOS", Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::AuracronEOSBridge_eventLogoutFromEOS_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::AuracronEOSBridge_eventLogoutFromEOS_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execLogoutFromEOS)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LogoutFromEOS();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function LogoutFromEOS **********************************

// ********** Begin Class UAuracronEOSBridge Function OnRep_ConnectionStatus ***********************
struct Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "OnRep_ConnectionStatus", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execOnRep_ConnectionStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ConnectionStatus();
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function OnRep_ConnectionStatus *************************

// ********** Begin Class UAuracronEOSBridge Function RemoveFriend *********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics
{
	struct AuracronEOSBridge_eventRemoveFriend_Parms
	{
		FString UserID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Friends" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover amigo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover amigo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_UserID = { "UserID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventRemoveFriend_Parms, UserID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserID_MetaData), NewProp_UserID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventRemoveFriend_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventRemoveFriend_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_UserID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "RemoveFriend", Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::AuracronEOSBridge_eventRemoveFriend_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::AuracronEOSBridge_eventRemoveFriend_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execRemoveFriend)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_UserID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveFriend(Z_Param_UserID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function RemoveFriend ***********************************

// ********** Begin Class UAuracronEOSBridge Function SavePlayerData *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics
{
	struct AuracronEOSBridge_eventSavePlayerData_Parms
	{
		FString Key;
		FString Data;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|PlayerData" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Salvar dados do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar dados do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Data;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSavePlayerData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSavePlayerData_Parms, Data), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventSavePlayerData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventSavePlayerData_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "SavePlayerData", Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::AuracronEOSBridge_eventSavePlayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::AuracronEOSBridge_eventSavePlayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execSavePlayerData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_Data);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SavePlayerData(Z_Param_Key,Z_Param_Data);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function SavePlayerData *********************************

// ********** Begin Class UAuracronEOSBridge Function SendCustomMetric *****************************
struct Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics
{
	struct AuracronEOSBridge_eventSendCustomMetric_Parms
	{
		FString MetricName;
		TMap<FString,FString> Properties;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enviar m\xc3\x83\xc2\xa9trica personalizada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enviar m\xc3\x83\xc2\xa9trica personalizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Properties;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSendCustomMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties_ValueProp = { "Properties", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties_Key_KeyProp = { "Properties_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSendCustomMetric_Parms, Properties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventSendCustomMetric_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventSendCustomMetric_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "SendCustomMetric", Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::AuracronEOSBridge_eventSendCustomMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::AuracronEOSBridge_eventSendCustomMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execSendCustomMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Properties);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SendCustomMetric(Z_Param_MetricName,Z_Param_Out_Properties);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function SendCustomMetric *******************************

// ********** Begin Class UAuracronEOSBridge Function SetPresence **********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics
{
	struct AuracronEOSBridge_eventSetPresence_Parms
	{
		FString Status;
		TMap<FString,FString> Properties;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Presence" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir presen\xc3\x83\xc2\xa7""a\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir presen\xc3\x83\xc2\xa7""a" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Status;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Properties;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSetPresence_Parms, Status), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties_ValueProp = { "Properties", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties_Key_KeyProp = { "Properties_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSetPresence_Parms, Properties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventSetPresence_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventSetPresence_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "SetPresence", Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::AuracronEOSBridge_eventSetPresence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::AuracronEOSBridge_eventSetPresence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_SetPresence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_SetPresence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execSetPresence)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Status);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Properties);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetPresence(Z_Param_Status,Z_Param_Out_Properties);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function SetPresence ************************************

// ********** Begin Class UAuracronEOSBridge Function SubmitScore **********************************
struct Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics
{
	struct AuracronEOSBridge_eventSubmitScore_Parms
	{
		FString LeaderboardID;
		int32 Score;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Leaderboards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enviar score para leaderboard\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enviar score para leaderboard" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Score;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_LeaderboardID = { "LeaderboardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSubmitScore_Parms, LeaderboardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardID_MetaData), NewProp_LeaderboardID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_Score = { "Score", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventSubmitScore_Parms, Score), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventSubmitScore_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventSubmitScore_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_LeaderboardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_Score,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "SubmitScore", Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::AuracronEOSBridge_eventSubmitScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::AuracronEOSBridge_eventSubmitScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execSubmitScore)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Score);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SubmitScore(Z_Param_LeaderboardID,Z_Param_Score);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function SubmitScore ************************************

// ********** Begin Class UAuracronEOSBridge Function TrackGameEvent *******************************
struct Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics
{
	struct AuracronEOSBridge_eventTrackGameEvent_Parms
	{
		FString EventName;
		TMap<FString,float> NumericData;
		TMap<FString,FString> StringData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear evento de jogo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear evento de jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumericData_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StringData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NumericData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NumericData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NumericData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StringData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StringData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StringData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_EventName = { "EventName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventTrackGameEvent_Parms, EventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventName_MetaData), NewProp_EventName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData_ValueProp = { "NumericData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData_Key_KeyProp = { "NumericData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData = { "NumericData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventTrackGameEvent_Parms, NumericData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumericData_MetaData), NewProp_NumericData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData_ValueProp = { "StringData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData_Key_KeyProp = { "StringData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData = { "StringData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventTrackGameEvent_Parms, StringData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StringData_MetaData), NewProp_StringData_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventTrackGameEvent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventTrackGameEvent_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_EventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_NumericData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_StringData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "TrackGameEvent", Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::AuracronEOSBridge_eventTrackGameEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::AuracronEOSBridge_eventTrackGameEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execTrackGameEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventName);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_NumericData);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_StringData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackGameEvent(Z_Param_EventName,Z_Param_Out_NumericData,Z_Param_Out_StringData);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function TrackGameEvent *********************************

// ********** Begin Class UAuracronEOSBridge Function UnlockAchievement ****************************
struct Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics
{
	struct AuracronEOSBridge_eventUnlockAchievement_Parms
	{
		FString AchievementID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desbloquear achievement\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desbloquear achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AchievementID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventUnlockAchievement_Parms, AchievementID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementID_MetaData), NewProp_AchievementID_MetaData) };
void Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventUnlockAchievement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventUnlockAchievement_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "UnlockAchievement", Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::AuracronEOSBridge_eventUnlockAchievement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::AuracronEOSBridge_eventUnlockAchievement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execUnlockAchievement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AchievementID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnlockAchievement(Z_Param_AchievementID);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function UnlockAchievement ******************************

// ********** Begin Class UAuracronEOSBridge Function UpdateAchievementProgress ********************
struct Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics
{
	struct AuracronEOSBridge_eventUpdateAchievementProgress_Parms
	{
		FString AchievementID;
		float Progress;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON EOS|Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar progresso de achievement\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar progresso de achievement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventUpdateAchievementProgress_Parms, AchievementID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementID_MetaData), NewProp_AchievementID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronEOSBridge_eventUpdateAchievementProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronEOSBridge_eventUpdateAchievementProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronEOSBridge_eventUpdateAchievementProgress_Parms), &Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronEOSBridge, nullptr, "UpdateAchievementProgress", Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::AuracronEOSBridge_eventUpdateAchievementProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::AuracronEOSBridge_eventUpdateAchievementProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronEOSBridge::execUpdateAchievementProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AchievementID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateAchievementProgress(Z_Param_AchievementID,Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UAuracronEOSBridge Function UpdateAchievementProgress **********************

// ********** Begin Class UAuracronEOSBridge *******************************************************
void UAuracronEOSBridge::StaticRegisterNativesUAuracronEOSBridge()
{
	UClass* Class = UAuracronEOSBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddFriend", &UAuracronEOSBridge::execAddFriend },
		{ "CreateSession", &UAuracronEOSBridge::execCreateSession },
		{ "DeletePlayerData", &UAuracronEOSBridge::execDeletePlayerData },
		{ "DestroySession", &UAuracronEOSBridge::execDestroySession },
		{ "FindSessions", &UAuracronEOSBridge::execFindSessions },
		{ "GetAchievements", &UAuracronEOSBridge::execGetAchievements },
		{ "GetAuthenticationStatus", &UAuracronEOSBridge::execGetAuthenticationStatus },
		{ "GetDisplayName", &UAuracronEOSBridge::execGetDisplayName },
		{ "GetFriendPresence", &UAuracronEOSBridge::execGetFriendPresence },
		{ "GetFriendsList", &UAuracronEOSBridge::execGetFriendsList },
		{ "GetPlayerRank", &UAuracronEOSBridge::execGetPlayerRank },
		{ "GetTopRankings", &UAuracronEOSBridge::execGetTopRankings },
		{ "GetUserID", &UAuracronEOSBridge::execGetUserID },
		{ "InviteFriendToSession", &UAuracronEOSBridge::execInviteFriendToSession },
		{ "IsAchievementUnlocked", &UAuracronEOSBridge::execIsAchievementUnlocked },
		{ "JoinSession", &UAuracronEOSBridge::execJoinSession },
		{ "LeaveSession", &UAuracronEOSBridge::execLeaveSession },
		{ "LoadFriendsList", &UAuracronEOSBridge::execLoadFriendsList },
		{ "LoadPlayerData", &UAuracronEOSBridge::execLoadPlayerData },
		{ "LoginWithEOS", &UAuracronEOSBridge::execLoginWithEOS },
		{ "LogoutFromEOS", &UAuracronEOSBridge::execLogoutFromEOS },
		{ "OnRep_ConnectionStatus", &UAuracronEOSBridge::execOnRep_ConnectionStatus },
		{ "RemoveFriend", &UAuracronEOSBridge::execRemoveFriend },
		{ "SavePlayerData", &UAuracronEOSBridge::execSavePlayerData },
		{ "SendCustomMetric", &UAuracronEOSBridge::execSendCustomMetric },
		{ "SetPresence", &UAuracronEOSBridge::execSetPresence },
		{ "SubmitScore", &UAuracronEOSBridge::execSubmitScore },
		{ "TrackGameEvent", &UAuracronEOSBridge::execTrackGameEvent },
		{ "UnlockAchievement", &UAuracronEOSBridge::execUnlockAchievement },
		{ "UpdateAchievementProgress", &UAuracronEOSBridge::execUpdateAchievementProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronEOSBridge;
UClass* UAuracronEOSBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronEOSBridge;
	if (!Z_Registration_Info_UClass_UAuracronEOSBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronEOSBridge"),
			Z_Registration_Info_UClass_UAuracronEOSBridge.InnerSingleton,
			StaticRegisterNativesUAuracronEOSBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronEOSBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronEOSBridge_NoRegister()
{
	return UAuracronEOSBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronEOSBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|EOS" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Epic Online Services\n * Respons\xc3\x83\xc2\xa1vel pela integra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o completa com EOS\n */" },
#endif
		{ "DisplayName", "AURACRON EOS Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronEOSBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Epic Online Services\nRespons\xc3\x83\xc2\xa1vel pela integra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o completa com EOS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentConnectionStatus_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status de conex\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status de conex\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentUserID_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do usu\xc3\x83\xc2\xa1rio atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do usu\xc3\x83\xc2\xa1rio atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDisplayName_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome de exibi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FriendsList_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lista de amigos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de amigos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerAchievements_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Achievements do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Achievements do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSession_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sess\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sess\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInSession_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\x83\xc2\xa1 em sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\x83\xc2\xa1 em sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEOSLoginCompleted_MetaData[] = {
		{ "Category", "AURACRON EOS|Events" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSessionCreated_MetaData[] = {
		{ "Category", "AURACRON EOS|Events" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAchievementUnlocked_MetaData[] = {
		{ "Category", "AURACRON EOS|Events" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFriendOnline_MetaData[] = {
		{ "Category", "AURACRON EOS|Events" },
		{ "ModuleRelativePath", "Public/AuracronEOSBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentConnectionStatus_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentConnectionStatus;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentUserID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentDisplayName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FriendsList_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FriendsList;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerAchievements_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayerAchievements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentSession;
	static void NewProp_bIsInSession_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInSession;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEOSLoginCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSessionCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAchievementUnlocked;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFriendOnline;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronEOSBridge_AddFriend, "AddFriend" }, // 3386267527
		{ &Z_Construct_UFunction_UAuracronEOSBridge_CreateSession, "CreateSession" }, // 3042582060
		{ &Z_Construct_UFunction_UAuracronEOSBridge_DeletePlayerData, "DeletePlayerData" }, // 3304746692
		{ &Z_Construct_UFunction_UAuracronEOSBridge_DestroySession, "DestroySession" }, // 612841002
		{ &Z_Construct_UFunction_UAuracronEOSBridge_FindSessions, "FindSessions" }, // 1952402868
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetAchievements, "GetAchievements" }, // 2209603368
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetAuthenticationStatus, "GetAuthenticationStatus" }, // 3050078184
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetDisplayName, "GetDisplayName" }, // 489063636
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetFriendPresence, "GetFriendPresence" }, // 4284335788
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetFriendsList, "GetFriendsList" }, // 502103573
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetPlayerRank, "GetPlayerRank" }, // 3116235497
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetTopRankings, "GetTopRankings" }, // 1383855740
		{ &Z_Construct_UFunction_UAuracronEOSBridge_GetUserID, "GetUserID" }, // 595956670
		{ &Z_Construct_UFunction_UAuracronEOSBridge_InviteFriendToSession, "InviteFriendToSession" }, // 1251663535
		{ &Z_Construct_UFunction_UAuracronEOSBridge_IsAchievementUnlocked, "IsAchievementUnlocked" }, // 1327440888
		{ &Z_Construct_UFunction_UAuracronEOSBridge_JoinSession, "JoinSession" }, // 2010727448
		{ &Z_Construct_UFunction_UAuracronEOSBridge_LeaveSession, "LeaveSession" }, // 1007996799
		{ &Z_Construct_UFunction_UAuracronEOSBridge_LoadFriendsList, "LoadFriendsList" }, // 3959061429
		{ &Z_Construct_UFunction_UAuracronEOSBridge_LoadPlayerData, "LoadPlayerData" }, // 3314820504
		{ &Z_Construct_UFunction_UAuracronEOSBridge_LoginWithEOS, "LoginWithEOS" }, // 1322472598
		{ &Z_Construct_UFunction_UAuracronEOSBridge_LogoutFromEOS, "LogoutFromEOS" }, // 3865878487
		{ &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature, "OnAchievementUnlocked__DelegateSignature" }, // 1722048380
		{ &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature, "OnEOSLoginCompleted__DelegateSignature" }, // 3274517256
		{ &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature, "OnFriendOnline__DelegateSignature" }, // 4000796176
		{ &Z_Construct_UFunction_UAuracronEOSBridge_OnRep_ConnectionStatus, "OnRep_ConnectionStatus" }, // 3580847065
		{ &Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature, "OnSessionCreated__DelegateSignature" }, // 258286536
		{ &Z_Construct_UFunction_UAuracronEOSBridge_RemoveFriend, "RemoveFriend" }, // 526276173
		{ &Z_Construct_UFunction_UAuracronEOSBridge_SavePlayerData, "SavePlayerData" }, // 403657686
		{ &Z_Construct_UFunction_UAuracronEOSBridge_SendCustomMetric, "SendCustomMetric" }, // 2944444420
		{ &Z_Construct_UFunction_UAuracronEOSBridge_SetPresence, "SetPresence" }, // 468809381
		{ &Z_Construct_UFunction_UAuracronEOSBridge_SubmitScore, "SubmitScore" }, // 1240522589
		{ &Z_Construct_UFunction_UAuracronEOSBridge_TrackGameEvent, "TrackGameEvent" }, // 2563850470
		{ &Z_Construct_UFunction_UAuracronEOSBridge_UnlockAchievement, "UnlockAchievement" }, // 2191241068
		{ &Z_Construct_UFunction_UAuracronEOSBridge_UpdateAchievementProgress, "UpdateAchievementProgress" }, // 3641611975
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronEOSBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentConnectionStatus_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentConnectionStatus = { "CurrentConnectionStatus", "OnRep_ConnectionStatus", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, CurrentConnectionStatus), Z_Construct_UEnum_AuracronEOSBridge_EAuracronEOSConnectionStatus, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentConnectionStatus_MetaData), NewProp_CurrentConnectionStatus_MetaData) }; // 2538958531
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentUserID = { "CurrentUserID", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, CurrentUserID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentUserID_MetaData), NewProp_CurrentUserID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentDisplayName = { "CurrentDisplayName", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, CurrentDisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDisplayName_MetaData), NewProp_CurrentDisplayName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_FriendsList_Inner = { "FriendsList", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEOSFriend, METADATA_PARAMS(0, nullptr) }; // 4205638700
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_FriendsList = { "FriendsList", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, FriendsList), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FriendsList_MetaData), NewProp_FriendsList_MetaData) }; // 4205638700
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_PlayerAchievements_Inner = { "PlayerAchievements", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronEOSAchievement, METADATA_PARAMS(0, nullptr) }; // 3927995631
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_PlayerAchievements = { "PlayerAchievements", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, PlayerAchievements), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerAchievements_MetaData), NewProp_PlayerAchievements_MetaData) }; // 3927995631
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentSession = { "CurrentSession", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, CurrentSession), Z_Construct_UScriptStruct_FAuracronSessionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSession_MetaData), NewProp_CurrentSession_MetaData) }; // 236244013
void Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_bIsInSession_SetBit(void* Obj)
{
	((UAuracronEOSBridge*)Obj)->bIsInSession = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_bIsInSession = { "bIsInSession", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronEOSBridge), &Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_bIsInSession_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInSession_MetaData), NewProp_bIsInSession_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnEOSLoginCompleted = { "OnEOSLoginCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, OnEOSLoginCompleted), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEOSLoginCompleted_MetaData), NewProp_OnEOSLoginCompleted_MetaData) }; // 3274517256
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnSessionCreated = { "OnSessionCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, OnSessionCreated), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSessionCreated_MetaData), NewProp_OnSessionCreated_MetaData) }; // 258286536
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnAchievementUnlocked = { "OnAchievementUnlocked", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, OnAchievementUnlocked), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAchievementUnlocked_MetaData), NewProp_OnAchievementUnlocked_MetaData) }; // 1722048380
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnFriendOnline = { "OnFriendOnline", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEOSBridge, OnFriendOnline), Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFriendOnline_MetaData), NewProp_OnFriendOnline_MetaData) }; // 4000796176
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronEOSBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentConnectionStatus_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentConnectionStatus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentUserID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentDisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_FriendsList_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_FriendsList,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_PlayerAchievements_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_PlayerAchievements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_CurrentSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_bIsInSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnEOSLoginCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnSessionCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnAchievementUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEOSBridge_Statics::NewProp_OnFriendOnline,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEOSBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronEOSBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronEOSBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEOSBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronEOSBridge_Statics::ClassParams = {
	&UAuracronEOSBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronEOSBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEOSBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEOSBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronEOSBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronEOSBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronEOSBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronEOSBridge.OuterSingleton, Z_Construct_UClass_UAuracronEOSBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronEOSBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronEOSBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentConnectionStatus(TEXT("CurrentConnectionStatus"));
	static FName Name_CurrentUserID(TEXT("CurrentUserID"));
	static FName Name_CurrentDisplayName(TEXT("CurrentDisplayName"));
	static FName Name_FriendsList(TEXT("FriendsList"));
	static FName Name_PlayerAchievements(TEXT("PlayerAchievements"));
	static FName Name_CurrentSession(TEXT("CurrentSession"));
	static FName Name_bIsInSession(TEXT("bIsInSession"));
	const bool bIsValid = true
		&& Name_CurrentConnectionStatus == ClassReps[(int32)ENetFields_Private::CurrentConnectionStatus].Property->GetFName()
		&& Name_CurrentUserID == ClassReps[(int32)ENetFields_Private::CurrentUserID].Property->GetFName()
		&& Name_CurrentDisplayName == ClassReps[(int32)ENetFields_Private::CurrentDisplayName].Property->GetFName()
		&& Name_FriendsList == ClassReps[(int32)ENetFields_Private::FriendsList].Property->GetFName()
		&& Name_PlayerAchievements == ClassReps[(int32)ENetFields_Private::PlayerAchievements].Property->GetFName()
		&& Name_CurrentSession == ClassReps[(int32)ENetFields_Private::CurrentSession].Property->GetFName()
		&& Name_bIsInSession == ClassReps[(int32)ENetFields_Private::bIsInSession].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronEOSBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronEOSBridge);
UAuracronEOSBridge::~UAuracronEOSBridge() {}
// ********** End Class UAuracronEOSBridge *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronEOSConnectionStatus_StaticEnum, TEXT("EAuracronEOSConnectionStatus"), &Z_Registration_Info_UEnum_EAuracronEOSConnectionStatus, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2538958531U) },
		{ EAuracronSessionType_StaticEnum, TEXT("EAuracronSessionType"), &Z_Registration_Info_UEnum_EAuracronSessionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1060124687U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronSessionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronSessionConfiguration_Statics::NewStructOps, TEXT("AuracronSessionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronSessionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSessionConfiguration), 236244013U) },
		{ FAuracronEOSFriend::StaticStruct, Z_Construct_UScriptStruct_FAuracronEOSFriend_Statics::NewStructOps, TEXT("AuracronEOSFriend"), &Z_Registration_Info_UScriptStruct_FAuracronEOSFriend, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronEOSFriend), 4205638700U) },
		{ FAuracronEOSAchievement::StaticStruct, Z_Construct_UScriptStruct_FAuracronEOSAchievement_Statics::NewStructOps, TEXT("AuracronEOSAchievement"), &Z_Registration_Info_UScriptStruct_FAuracronEOSAchievement, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronEOSAchievement), 3927995631U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronEOSBridge, UAuracronEOSBridge::StaticClass, TEXT("UAuracronEOSBridge"), &Z_Registration_Info_UClass_UAuracronEOSBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronEOSBridge), 2851126340U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_1973136243(TEXT("/Script/AuracronEOSBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronEOSBridge_Public_AuracronEOSBridge_h__Script_AuracronEOSBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
