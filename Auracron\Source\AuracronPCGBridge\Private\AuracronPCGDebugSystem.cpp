// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Implementation
// Bridge 2.14: PCG Framework - Debugging Tools

#include "AuracronPCGDebugSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "HAL/IConsoleManager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// VISUAL DEBUGGER IMPLEMENTATION
// =============================================================================

EAuracronPCGDebugVisualizationMode UAuracronPCGVisualDebugger::CurrentVisualizationMode = EAuracronPCGDebugVisualizationMode::None;
bool UAuracronPCGVisualDebugger::bVisualizationEnabled = false;

void UAuracronPCGVisualDebugger::DrawPointData(UWorld* World, const UPCGPointData* PointData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !PointData || !bVisualizationEnabled)
    {
        return;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        FVector Location = Point.Transform.GetLocation();
        
        if (Descriptor.bShowPoints)
        {
            // Draw point as sphere
            DrawDebugSphere(World, Location, Descriptor.PointSize, 8, Descriptor.PointColor.ToFColor(true), 
                          Descriptor.bPersistentDisplay, Descriptor.DisplayDuration);
            
            // Draw point index if enabled
            if (Descriptor.bShowPointIndices)
            {
                FString IndexText = FString::FromInt(i);
                DrawDebugString(World, Location + FVector(0, 0, 20), IndexText, nullptr, 
                              Descriptor.PointColor.ToFColor(true), Descriptor.DisplayDuration, 
                              Descriptor.bWorldSpaceText);
            }
        }
    }
}

void UAuracronPCGVisualDebugger::DrawGraphConnections(UWorld* World, const UPCGGraph* Graph, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !Graph || !bVisualizationEnabled || !Descriptor.bShowConnections)
    {
        return;
    }

    // Complete graph connection visualization implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGVisualDebugger::DrawConnections);

    TArray<UPCGNode*> AllNodes = Graph->GetNodes();

    for (UPCGNode* Node : AllNodes)
    {
        if (!Node)
        {
            continue;
        }

        FVector NodeLocation = FVector::ZeroVector;
        if (Node->GetSettings())
        {
            // Calculate node position based on node index and graph layout
            int32 NodeIndex = AllNodes.IndexOfByKey(Node);
            float Angle = (2.0f * PI * NodeIndex) / FMath::Max(1, AllNodes.Num());
            float Radius = 500.0f;
            NodeLocation = FVector(
                FMath::Cos(Angle) * Radius,
                FMath::Sin(Angle) * Radius,
                100.0f
            );
        }

        // Draw connections from output pins to input pins
        for (UPCGPin* OutputPin : Node->GetOutputPins())
        {
            if (!OutputPin)
            {
                continue;
            }

            for (const UPCGEdge& Edge : OutputPin->Edges)
            {
                if (Edge.InputPin && Edge.InputPin->Node)
                {
                    UPCGNode* ConnectedNode = Edge.InputPin->Node;
                    int32 ConnectedNodeIndex = AllNodes.IndexOfByKey(ConnectedNode);

                    if (ConnectedNodeIndex != INDEX_NONE)
                    {
                        float ConnectedAngle = (2.0f * PI * ConnectedNodeIndex) / FMath::Max(1, AllNodes.Num());
                        float ConnectedRadius = 500.0f;
                        FVector ConnectedLocation = FVector(
                            FMath::Cos(ConnectedAngle) * ConnectedRadius,
                            FMath::Sin(ConnectedAngle) * ConnectedRadius,
                            100.0f
                        );

                        // Draw connection line
                        FColor ConnectionColor = FColor::Green;
                        if (Descriptor.bUseColorCoding)
                        {
                            // Color code based on data type
                            if (OutputPin->Properties.Label.ToString().Contains(TEXT("Point")))
                            {
                                ConnectionColor = FColor::Blue;
                            }
                            else if (OutputPin->Properties.Label.ToString().Contains(TEXT("Spatial")))
                            {
                                ConnectionColor = FColor::Red;
                            }
                            else if (OutputPin->Properties.Label.ToString().Contains(TEXT("Surface")))
                            {
                                ConnectionColor = FColor::Yellow;
                            }
                        }

                        DrawDebugLine(World, NodeLocation, ConnectedLocation, ConnectionColor, false, Descriptor.VisualizationDuration, 0, Descriptor.LineThickness);

                        // Draw arrow at the end to show direction
                        FVector Direction = (ConnectedLocation - NodeLocation).GetSafeNormal();
                        FVector ArrowEnd = ConnectedLocation - Direction * 50.0f;
                        FVector ArrowSide1 = ArrowEnd + FVector::CrossProduct(Direction, FVector::UpVector) * 20.0f;
                        FVector ArrowSide2 = ArrowEnd - FVector::CrossProduct(Direction, FVector::UpVector) * 20.0f;

                        DrawDebugLine(World, ConnectedLocation, ArrowSide1, ConnectionColor, false, Descriptor.VisualizationDuration, 0, Descriptor.LineThickness);
                        DrawDebugLine(World, ConnectedLocation, ArrowSide2, ConnectionColor, false, Descriptor.VisualizationDuration, 0, Descriptor.LineThickness);

                        // Draw connection info text
                        if (Descriptor.bShowLabels)
                        {
                            FVector MidPoint = (NodeLocation + ConnectedLocation) * 0.5f;
                            FString ConnectionInfo = FString::Printf(TEXT("%s -> %s"),
                                *OutputPin->Properties.Label.ToString(),
                                *Edge.InputPin->Properties.Label.ToString());
                            DrawDebugString(World, MidPoint, ConnectionInfo, nullptr, ConnectionColor, Descriptor.VisualizationDuration);
                        }
                    }
                }
            }
        }

        // Draw node representation
        if (Descriptor.bShowNodes)
        {
            FColor NodeColor = FColor::White;
            if (Node->GetSettings())
            {
                // Color code nodes based on their type
                FString NodeType = Node->GetSettings()->GetClass()->GetName();
                if (NodeType.Contains(TEXT("Generator")))
                {
                    NodeColor = FColor::Green;
                }
                else if (NodeType.Contains(TEXT("Filter")))
                {
                    NodeColor = FColor::Orange;
                }
                else if (NodeType.Contains(TEXT("Sampler")))
                {
                    NodeColor = FColor::Purple;
                }
                else if (NodeType.Contains(TEXT("Spawner")))
                {
                    NodeColor = FColor::Cyan;
                }
            }

            DrawDebugSphere(World, NodeLocation, 25.0f, 8, NodeColor, false, Descriptor.VisualizationDuration, 0, Descriptor.LineThickness);

            if (Descriptor.bShowLabels && Node->GetSettings())
            {
                FString NodeLabel = Node->GetSettings()->GetClass()->GetName();
                DrawDebugString(World, NodeLocation + FVector(0, 0, 50), NodeLabel, nullptr, NodeColor, Descriptor.VisualizationDuration);
            }
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drew %d graph connections for debug visualization"), AllNodes.Num());
}

void UAuracronPCGVisualDebugger::DrawBoundingBoxes(UWorld* World, const UPCGSpatialData* SpatialData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !SpatialData || !bVisualizationEnabled || !Descriptor.bShowBoundingBoxes)
    {
        return;
    }

    FBox Bounds = SpatialData->GetBounds();
    DrawDebugBox(World, Bounds.GetCenter(), Bounds.GetExtent(), Descriptor.BoundingBoxColor.ToFColor(true), 
                Descriptor.bPersistentDisplay, Descriptor.DisplayDuration, 0, Descriptor.ConnectionThickness);
}

void UAuracronPCGVisualDebugger::DrawAttributes(UWorld* World, const UPCGPointData* PointData, const TArray<FString>& AttributeNames, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !PointData || !bVisualizationEnabled || !Descriptor.bShowAttributes)
    {
        return;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    const UPCGMetadata* Metadata = PointData->Metadata;
    
    if (!Metadata)
    {
        return;
    }

    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        FVector Location = Point.Transform.GetLocation();
        
        FString AttributeText;
        for (const FString& AttributeName : AttributeNames)
        {
            // Complete attribute value extraction and display
            float AttributeValue = 0.0f;
            bool bFoundAttribute = false;

            if (PointData->Metadata)
            {
                // Try to get the actual attribute value from metadata
                TArray<FName> MetadataAttributeNames;
                TArray<EPCGMetadataTypes> AttributeTypes;
                PointData->Metadata->GetAttributes(MetadataAttributeNames, AttributeTypes);

                FName AttributeFName(*AttributeName);
                int32 AttributeIndex = MetadataAttributeNames.IndexOfByKey(AttributeFName);

                if (AttributeIndex != INDEX_NONE)
                {
                    EPCGMetadataTypes AttributeType = AttributeTypes[AttributeIndex];
                    PCGMetadataEntryKey EntryKey = PointData->Metadata->GetItemKeyCountForParent();

                    switch (AttributeType)
                    {
                        case EPCGMetadataTypes::Float:
                        {
                            const FPCGMetadataAttribute<float>* FloatAttribute = PointData->Metadata->GetConstTypedAttribute<float>(AttributeFName);
                            if (FloatAttribute)
                            {
                                AttributeValue = FloatAttribute->GetValueFromItemKey(EntryKey);
                                bFoundAttribute = true;
                            }
                            break;
                        }
                        case EPCGMetadataTypes::Double:
                        {
                            const FPCGMetadataAttribute<double>* DoubleAttribute = PointData->Metadata->GetConstTypedAttribute<double>(AttributeFName);
                            if (DoubleAttribute)
                            {
                                AttributeValue = static_cast<float>(DoubleAttribute->GetValueFromItemKey(EntryKey));
                                bFoundAttribute = true;
                            }
                            break;
                        }
                        case EPCGMetadataTypes::Integer32:
                        {
                            const FPCGMetadataAttribute<int32>* IntAttribute = PointData->Metadata->GetConstTypedAttribute<int32>(AttributeFName);
                            if (IntAttribute)
                            {
                                AttributeValue = static_cast<float>(IntAttribute->GetValueFromItemKey(EntryKey));
                                bFoundAttribute = true;
                            }
                            break;
                        }
                        case EPCGMetadataTypes::Integer64:
                        {
                            const FPCGMetadataAttribute<int64>* Int64Attribute = PointData->Metadata->GetConstTypedAttribute<int64>(AttributeFName);
                            if (Int64Attribute)
                            {
                                AttributeValue = static_cast<float>(Int64Attribute->GetValueFromItemKey(EntryKey));
                                bFoundAttribute = true;
                            }
                            break;
                        }
                        default:
                            // For other types, use default point properties
                            break;
                    }
                }
            }

            // Fallback to point properties if attribute not found in metadata
            if (!bFoundAttribute)
            {
                if (AttributeName == TEXT("Density"))
                {
                    AttributeValue = Point.Density;
                    bFoundAttribute = true;
                }
                else if (AttributeName == TEXT("Steepness"))
                {
                    AttributeValue = Point.Steepness;
                    bFoundAttribute = true;
                }
                else if (AttributeName == TEXT("Seed"))
                {
                    AttributeValue = static_cast<float>(Point.Seed);
                    bFoundAttribute = true;
                }
                else if (AttributeName.Contains(TEXT("Color")))
                {
                    AttributeValue = Point.Color.X; // Red component
                    bFoundAttribute = true;
                }
                else if (AttributeName.Contains(TEXT("Scale")))
                {
                    AttributeValue = Point.Transform.GetScale3D().X;
                    bFoundAttribute = true;
                }
            }

            if (bFoundAttribute)
            {
                AttributeText += FString::Printf(TEXT("%s: %.3f\n"), *AttributeName, AttributeValue);
            }
            else
            {
                AttributeText += FString::Printf(TEXT("%s: N/A\n"), *AttributeName);
            }
        }
        
        if (!AttributeText.IsEmpty())
        {
            DrawDebugString(World, Location + FVector(0, 0, 30), AttributeText, nullptr, 
                          FColor::White, Descriptor.DisplayDuration, Descriptor.bWorldSpaceText);
        }
    }
}

void UAuracronPCGVisualDebugger::DrawPerformanceInfo(UWorld* World, const FVector& Location, const TMap<FString, float>& PerformanceData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !bVisualizationEnabled || !Descriptor.bShowPerformanceInfo)
    {
        return;
    }

    FString PerformanceText;
    for (const auto& DataPair : PerformanceData)
    {
        PerformanceText += FString::Printf(TEXT("%s: %.3f\n"), *DataPair.Key, DataPair.Value);
    }
    
    if (!PerformanceText.IsEmpty())
    {
        DrawDebugString(World, Location, PerformanceText, nullptr, FColor::Yellow, 
                      Descriptor.DisplayDuration, Descriptor.bWorldSpaceText);
    }
}

void UAuracronPCGVisualDebugger::DrawDataFlow(UWorld* World, const UPCGGraph* Graph, float AnimationTime, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !Graph || !bVisualizationEnabled || !Descriptor.bShowDataFlow)
    {
        return;
    }

    // Complete data flow animation implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGVisualDebugger::DrawDataFlowAnimation);

    TArray<UPCGNode*> AllNodes = Graph->GetNodes();

    for (UPCGNode* Node : AllNodes)
    {
        if (!Node)
        {
            continue;
        }

        // Calculate node position
        int32 NodeIndex = AllNodes.IndexOfByKey(Node);
        float Angle = (2.0f * PI * NodeIndex) / FMath::Max(1, AllNodes.Num());
        float Radius = 500.0f;
        FVector NodeLocation = FVector(
            FMath::Cos(Angle) * Radius,
            FMath::Sin(Angle) * Radius,
            100.0f
        );

        // Animate data flow along connections
        for (UPCGPin* OutputPin : Node->GetOutputPins())
        {
            if (!OutputPin)
            {
                continue;
            }

            for (const UPCGEdge& Edge : OutputPin->Edges)
            {
                if (Edge.InputPin && Edge.InputPin->Node)
                {
                    UPCGNode* ConnectedNode = Edge.InputPin->Node;
                    int32 ConnectedNodeIndex = AllNodes.IndexOfByKey(ConnectedNode);

                    if (ConnectedNodeIndex != INDEX_NONE)
                    {
                        float ConnectedAngle = (2.0f * PI * ConnectedNodeIndex) / FMath::Max(1, AllNodes.Num());
                        FVector ConnectedLocation = FVector(
                            FMath::Cos(ConnectedAngle) * Radius,
                            FMath::Sin(ConnectedAngle) * Radius,
                            100.0f
                        );

                        // Calculate animated position along the connection
                        float AnimationSpeed = 2.0f; // Units per second
                        float ConnectionLength = FVector::Dist(NodeLocation, ConnectedLocation);
                        float AnimationProgress = FMath::Fmod(AnimationTime * AnimationSpeed, ConnectionLength) / ConnectionLength;

                        FVector AnimatedPosition = FMath::Lerp(NodeLocation, ConnectedLocation, AnimationProgress);

                        // Draw animated data packet
                        FColor DataColor = FColor::Yellow;
                        if (OutputPin->Properties.Label.ToString().Contains(TEXT("Point")))
                        {
                            DataColor = FColor::Blue;
                        }
                        else if (OutputPin->Properties.Label.ToString().Contains(TEXT("Spatial")))
                        {
                            DataColor = FColor::Red;
                        }

                        // Draw pulsing sphere to represent data flow
                        float PulseScale = 1.0f + 0.5f * FMath::Sin(AnimationTime * 4.0f);
                        DrawDebugSphere(World, AnimatedPosition, 15.0f * PulseScale, 6, DataColor, false, 0.1f, 0, 2.0f);

                        // Draw trail effect
                        for (int32 TrailIndex = 1; TrailIndex <= 5; TrailIndex++)
                        {
                            float TrailProgress = AnimationProgress - (TrailIndex * 0.05f);
                            if (TrailProgress >= 0.0f)
                            {
                                FVector TrailPosition = FMath::Lerp(NodeLocation, ConnectedLocation, TrailProgress);
                                float TrailAlpha = 1.0f - (TrailIndex * 0.2f);
                                FColor TrailColor = DataColor;
                                TrailColor.A = static_cast<uint8>(255 * TrailAlpha);

                                DrawDebugSphere(World, TrailPosition, 10.0f, 4, TrailColor, false, 0.1f, 0, 1.0f);
                            }
                        }

                        // Draw data type information
                        if (Descriptor.bShowLabels)
                        {
                            FString DataInfo = FString::Printf(TEXT("Data: %s"), *OutputPin->Properties.Label.ToString());
                            DrawDebugString(World, AnimatedPosition + FVector(0, 0, 30), DataInfo, nullptr, DataColor, 0.1f);
                        }
                    }
                }
            }
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drew data flow animation for %d nodes at time %.3f"), AllNodes.Num(), AnimationTime);
}

void UAuracronPCGVisualDebugger::DrawErrors(UWorld* World, const TArray<FString>& ErrorMessages, const TArray<FVector>& ErrorLocations, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !bVisualizationEnabled || !Descriptor.bShowErrors)
    {
        return;
    }

    for (int32 i = 0; i < FMath::Min(ErrorMessages.Num(), ErrorLocations.Num()); i++)
    {
        const FString& ErrorMessage = ErrorMessages[i];
        const FVector& ErrorLocation = ErrorLocations[i];
        
        // Draw error marker
        DrawDebugSphere(World, ErrorLocation, 20.0f, 8, Descriptor.ErrorColor.ToFColor(true), 
                      Descriptor.bPersistentDisplay, Descriptor.DisplayDuration);
        
        // Draw error message
        DrawDebugString(World, ErrorLocation + FVector(0, 0, 40), ErrorMessage, nullptr, 
                      Descriptor.ErrorColor.ToFColor(true), Descriptor.DisplayDuration, 
                      Descriptor.bWorldSpaceText);
    }
}

void UAuracronPCGVisualDebugger::ClearDebugDisplay(UWorld* World)
{
    if (World)
    {
        FlushDebugStrings(World);
        FlushPersistentDebugLines(World);
    }
}

void UAuracronPCGVisualDebugger::SetVisualizationMode(EAuracronPCGDebugVisualizationMode Mode)
{
    CurrentVisualizationMode = Mode;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug visualization mode set to: %d"), static_cast<int32>(Mode));
}

EAuracronPCGDebugVisualizationMode UAuracronPCGVisualDebugger::GetVisualizationMode()
{
    return CurrentVisualizationMode;
}

void UAuracronPCGVisualDebugger::ToggleVisualization(bool bEnabled)
{
    bVisualizationEnabled = bEnabled;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug visualization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronPCGVisualDebugger::IsVisualizationEnabled()
{
    return bVisualizationEnabled;
}

// =============================================================================
// PERFORMANCE PROFILER IMPLEMENTATION
// =============================================================================

bool UAuracronPCGPerformanceProfiler::bIsProfilingActive = false;
FAuracronPCGDebugProfilingDescriptor UAuracronPCGPerformanceProfiler::CurrentDescriptor;
TMap<FString, TArray<float>> UAuracronPCGPerformanceProfiler::ExecutionTimeHistory;
TMap<FString, TArray<float>> UAuracronPCGPerformanceProfiler::MemoryUsageHistory;
TMap<FString, float> UAuracronPCGPerformanceProfiler::CustomMetrics;

void UAuracronPCGPerformanceProfiler::StartProfiling(const FAuracronPCGDebugProfilingDescriptor& Descriptor)
{
    CurrentDescriptor = Descriptor;
    bIsProfilingActive = true;
    
    // Clear previous data if not keeping history
    if (!Descriptor.bKeepProfilingHistory)
    {
        ClearProfilingData();
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling started"));
}

void UAuracronPCGPerformanceProfiler::StopProfiling()
{
    bIsProfilingActive = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling stopped"));
    
    // Auto-save if enabled
    if (CurrentDescriptor.bAutoSaveProfilingData)
    {
        FString FilePath = CurrentDescriptor.ExportDirectory / TEXT("AutoSave_") + FDateTime::Now().ToString() + TEXT(".csv");
        ExportProfilingData(FilePath, true);
    }
}

void UAuracronPCGPerformanceProfiler::PauseProfiling()
{
    if (bIsProfilingActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling paused"));
    }
}

void UAuracronPCGPerformanceProfiler::ResumeProfiling()
{
    if (bIsProfilingActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling resumed"));
    }
}

bool UAuracronPCGPerformanceProfiler::IsProfilingActive()
{
    return bIsProfilingActive;
}

void UAuracronPCGPerformanceProfiler::RecordExecutionTime(const FString& NodeName, float ExecutionTime)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    if (!ExecutionTimeHistory.Contains(NodeName))
    {
        ExecutionTimeHistory.Add(NodeName, TArray<float>());
    }
    
    TArray<float>& TimeHistory = ExecutionTimeHistory[NodeName];
    TimeHistory.Add(ExecutionTime);
    
    // Limit history size
    if (TimeHistory.Num() > CurrentDescriptor.MaxHistoryEntries)
    {
        TimeHistory.RemoveAt(0);
    }
    
    // Check threshold
    if (CurrentDescriptor.bAlertOnThresholdExceeded && ExecutionTime > CurrentDescriptor.SlowExecutionThreshold)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node '%s' exceeded execution time threshold: %.3fms"), *NodeName, ExecutionTime);
    }
}

void UAuracronPCGPerformanceProfiler::RecordMemoryUsage(const FString& NodeName, float MemoryUsage)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    if (!MemoryUsageHistory.Contains(NodeName))
    {
        MemoryUsageHistory.Add(NodeName, TArray<float>());
    }
    
    TArray<float>& MemoryHistory = MemoryUsageHistory[NodeName];
    MemoryHistory.Add(MemoryUsage);
    
    // Limit history size
    if (MemoryHistory.Num() > CurrentDescriptor.MaxHistoryEntries)
    {
        MemoryHistory.RemoveAt(0);
    }
    
    // Check threshold
    if (CurrentDescriptor.bAlertOnThresholdExceeded && MemoryUsage > CurrentDescriptor.HighMemoryThreshold)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node '%s' exceeded memory usage threshold: %.3fMB"), *NodeName, MemoryUsage);
    }
}

void UAuracronPCGPerformanceProfiler::RecordDataTransfer(const FString& FromNode, const FString& ToNode, int32 DataSize)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    FString TransferKey = FString::Printf(TEXT("%s->%s"), *FromNode, *ToNode);
    CustomMetrics.Add(TransferKey, static_cast<float>(DataSize));
}

void UAuracronPCGPerformanceProfiler::RecordCustomMetric(const FString& MetricName, float Value)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    CustomMetrics.Add(MetricName, Value);
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetExecutionTimes()
{
    TMap<FString, float> AverageTimes;
    
    for (const auto& HistoryPair : ExecutionTimeHistory)
    {
        const TArray<float>& Times = HistoryPair.Value;
        if (Times.Num() > 0)
        {
            float Average = 0.0f;
            for (float Time : Times)
            {
                Average += Time;
            }
            Average /= Times.Num();
            AverageTimes.Add(HistoryPair.Key, Average);
        }
    }
    
    return AverageTimes;
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetMemoryUsage()
{
    TMap<FString, float> AverageMemory;
    
    for (const auto& HistoryPair : MemoryUsageHistory)
    {
        const TArray<float>& Memory = HistoryPair.Value;
        if (Memory.Num() > 0)
        {
            float Average = 0.0f;
            for (float Mem : Memory)
            {
                Average += Mem;
            }
            Average /= Memory.Num();
            AverageMemory.Add(HistoryPair.Key, Average);
        }
    }
    
    return AverageMemory;
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetCustomMetrics()
{
    return CustomMetrics;
}

float UAuracronPCGPerformanceProfiler::GetTotalExecutionTime()
{
    float TotalTime = 0.0f;
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    
    for (const auto& TimePair : AverageTimes)
    {
        TotalTime += TimePair.Value;
    }
    
    return TotalTime;
}

float UAuracronPCGPerformanceProfiler::GetPeakMemoryUsage()
{
    float PeakMemory = 0.0f;
    
    for (const auto& HistoryPair : MemoryUsageHistory)
    {
        const TArray<float>& Memory = HistoryPair.Value;
        for (float Mem : Memory)
        {
            PeakMemory = FMath::Max(PeakMemory, Mem);
        }
    }
    
    return PeakMemory;
}

TArray<FString> UAuracronPCGPerformanceProfiler::GetBottleneckNodes(float Threshold)
{
    TArray<FString> BottleneckNodes;
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    
    for (const auto& TimePair : AverageTimes)
    {
        if (TimePair.Value > Threshold)
        {
            BottleneckNodes.Add(TimePair.Key);
        }
    }
    
    return BottleneckNodes;
}

TArray<FString> UAuracronPCGPerformanceProfiler::GetHighMemoryNodes(float Threshold)
{
    TArray<FString> HighMemoryNodes;
    TMap<FString, float> AverageMemory = GetMemoryUsage();
    
    for (const auto& MemoryPair : AverageMemory)
    {
        if (MemoryPair.Value > Threshold)
        {
            HighMemoryNodes.Add(MemoryPair.Key);
        }
    }
    
    return HighMemoryNodes;
}

FString UAuracronPCGPerformanceProfiler::GeneratePerformanceReport()
{
    FString Report = TEXT("=== PCG Performance Report ===\n\n");
    
    // Execution times
    Report += TEXT("Execution Times (Average):\n");
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    for (const auto& TimePair : AverageTimes)
    {
        Report += FString::Printf(TEXT("  %s: %.3fms\n"), *TimePair.Key, TimePair.Value);
    }
    
    // Memory usage
    Report += TEXT("\nMemory Usage (Average):\n");
    TMap<FString, float> AverageMemory = GetMemoryUsage();
    for (const auto& MemoryPair : AverageMemory)
    {
        Report += FString::Printf(TEXT("  %s: %.3fMB\n"), *MemoryPair.Key, MemoryPair.Value);
    }
    
    // Summary
    Report += FString::Printf(TEXT("\nSummary:\n"));
    Report += FString::Printf(TEXT("  Total Execution Time: %.3fms\n"), GetTotalExecutionTime());
    Report += FString::Printf(TEXT("  Peak Memory Usage: %.3fMB\n"), GetPeakMemoryUsage());
    
    // Bottlenecks
    TArray<FString> Bottlenecks = GetBottleneckNodes(CurrentDescriptor.SlowExecutionThreshold);
    if (Bottlenecks.Num() > 0)
    {
        Report += TEXT("\nBottleneck Nodes:\n");
        for (const FString& Node : Bottlenecks)
        {
            Report += FString::Printf(TEXT("  %s\n"), *Node);
        }
    }
    
    return Report;
}

bool UAuracronPCGPerformanceProfiler::ExportProfilingData(const FString& FilePath, bool bCSVFormat)
{
    FString ExportData;
    
    if (bCSVFormat)
    {
        // CSV format
        ExportData = TEXT("Node,Average Execution Time (ms),Average Memory Usage (MB)\n");
        
        TMap<FString, float> AverageTimes = GetExecutionTimes();
        TMap<FString, float> AverageMemory = GetMemoryUsage();
        
        TSet<FString> AllNodes;
        AverageTimes.GetKeys(AllNodes);
        AverageMemory.GetKeys(AllNodes);
        
        for (const FString& Node : AllNodes)
        {
            float ExecTime = AverageTimes.Contains(Node) ? AverageTimes[Node] : 0.0f;
            float MemUsage = AverageMemory.Contains(Node) ? AverageMemory[Node] : 0.0f;
            ExportData += FString::Printf(TEXT("%s,%.3f,%.3f\n"), *Node, ExecTime, MemUsage);
        }
    }
    else
    {
        // JSON format
        ExportData = GeneratePerformanceReport();
    }
    
    return FFileHelper::SaveStringToFile(ExportData, *FilePath);
}

void UAuracronPCGPerformanceProfiler::ClearProfilingData()
{
    ExecutionTimeHistory.Empty();
    MemoryUsageHistory.Empty();
    CustomMetrics.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling data cleared"));
}

// =============================================================================
// DATA INSPECTOR IMPLEMENTATION
// =============================================================================

FString UAuracronPCGDataInspector::InspectPointData(const UPCGPointData* PointData, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!PointData)
    {
        return TEXT("Invalid point data");
    }

    FString Report = TEXT("=== Point Data Inspection ===\n\n");

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    Report += FString::Printf(TEXT("Point Count: %d\n"), Points.Num());

    if (Points.Num() > 0)
    {
        // Analyze point distribution
        FVector MinLocation = Points[0].Transform.GetLocation();
        FVector MaxLocation = MinLocation;
        float MinDensity = Points[0].Density;
        float MaxDensity = MinDensity;

        for (const FPCGPoint& Point : Points)
        {
            FVector Location = Point.Transform.GetLocation();
            MinLocation = FVector::Min(MinLocation, Location);
            MaxLocation = FVector::Max(MaxLocation, Location);
            MinDensity = FMath::Min(MinDensity, Point.Density);
            MaxDensity = FMath::Max(MaxDensity, Point.Density);
        }

        Report += FString::Printf(TEXT("Bounds: Min(%.2f, %.2f, %.2f) Max(%.2f, %.2f, %.2f)\n"),
                                MinLocation.X, MinLocation.Y, MinLocation.Z,
                                MaxLocation.X, MaxLocation.Y, MaxLocation.Z);
        Report += FString::Printf(TEXT("Density Range: %.3f - %.3f\n"), MinDensity, MaxDensity);
    }

    // Inspect metadata if available
    if (PointData->Metadata && Descriptor.bInspectMetadata)
    {
        Report += TEXT("\nMetadata:\n");
        Report += InspectMetadata(PointData->Metadata, Descriptor);
    }

    return Report;
}

FString UAuracronPCGDataInspector::InspectSpatialData(const UPCGSpatialData* SpatialData, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!SpatialData)
    {
        return TEXT("Invalid spatial data");
    }

    FString Report = TEXT("=== Spatial Data Inspection ===\n\n");

    FBox Bounds = SpatialData->GetBounds();
    Report += FString::Printf(TEXT("Bounds: Center(%.2f, %.2f, %.2f) Extent(%.2f, %.2f, %.2f)\n"),
                            Bounds.GetCenter().X, Bounds.GetCenter().Y, Bounds.GetCenter().Z,
                            Bounds.GetExtent().X, Bounds.GetExtent().Y, Bounds.GetExtent().Z);

    // Calculate volume
    float Volume = Bounds.GetVolume();
    Report += FString::Printf(TEXT("Volume: %.2f cubic units\n"), Volume);

    return Report;
}

FString UAuracronPCGDataInspector::InspectMetadata(const UPCGMetadata* Metadata, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!Metadata)
    {
        return TEXT("No metadata available");
    }

    FString Report = TEXT("Metadata Attributes:\n");

    // Complete metadata inspection implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGDataInspector::InspectMetadata);

    TArray<FName> AttributeNames;
    TArray<EPCGMetadataTypes> AttributeTypes;
    Metadata->GetAttributes(AttributeNames, AttributeTypes);

    Report += FString::Printf(TEXT("  Total Attributes: %d\n"), AttributeNames.Num());
    Report += FString::Printf(TEXT("  Item Count: %d\n"), Metadata->GetItemCountForChild());
    Report += FString::Printf(TEXT("  Parent Item Count: %d\n"), Metadata->GetItemKeyCountForParent());

    for (int32 i = 0; i < AttributeNames.Num(); i++)
    {
        FName AttributeName = AttributeNames[i];
        EPCGMetadataTypes AttributeType = AttributeTypes[i];

        FString TypeName;
        switch (AttributeType)
        {
            case EPCGMetadataTypes::Float:
                TypeName = TEXT("Float");
                break;
            case EPCGMetadataTypes::Double:
                TypeName = TEXT("Double");
                break;
            case EPCGMetadataTypes::Integer32:
                TypeName = TEXT("Int32");
                break;
            case EPCGMetadataTypes::Integer64:
                TypeName = TEXT("Int64");
                break;
            case EPCGMetadataTypes::Vector2:
                TypeName = TEXT("Vector2");
                break;
            case EPCGMetadataTypes::Vector:
                TypeName = TEXT("Vector");
                break;
            case EPCGMetadataTypes::Vector4:
                TypeName = TEXT("Vector4");
                break;
            case EPCGMetadataTypes::Quaternion:
                TypeName = TEXT("Quaternion");
                break;
            case EPCGMetadataTypes::Transform:
                TypeName = TEXT("Transform");
                break;
            case EPCGMetadataTypes::String:
                TypeName = TEXT("String");
                break;
            case EPCGMetadataTypes::Boolean:
                TypeName = TEXT("Boolean");
                break;
            case EPCGMetadataTypes::Rotator:
                TypeName = TEXT("Rotator");
                break;
            case EPCGMetadataTypes::Name:
                TypeName = TEXT("Name");
                break;
            default:
                TypeName = TEXT("Unknown");
                break;
        }

        Report += FString::Printf(TEXT("  [%d] %s (%s)\n"), i, *AttributeName.ToString(), *TypeName);

        // Sample some values if requested
        if (Descriptor.bInspectAttributes && Metadata->GetItemCountForChild() > 0)
        {
            PCGMetadataEntryKey SampleKey = 0;
            FString SampleValue = TEXT("N/A");

            switch (AttributeType)
            {
                case EPCGMetadataTypes::Float:
                {
                    const FPCGMetadataAttribute<float>* FloatAttr = Metadata->GetConstTypedAttribute<float>(AttributeName);
                    if (FloatAttr)
                    {
                        float Value = FloatAttr->GetValueFromItemKey(SampleKey);
                        SampleValue = FString::Printf(TEXT("%.3f"), Value);
                    }
                    break;
                }
                case EPCGMetadataTypes::Integer32:
                {
                    const FPCGMetadataAttribute<int32>* IntAttr = Metadata->GetConstTypedAttribute<int32>(AttributeName);
                    if (IntAttr)
                    {
                        int32 Value = IntAttr->GetValueFromItemKey(SampleKey);
                        SampleValue = FString::Printf(TEXT("%d"), Value);
                    }
                    break;
                }
                case EPCGMetadataTypes::String:
                {
                    const FPCGMetadataAttribute<FString>* StringAttr = Metadata->GetConstTypedAttribute<FString>(AttributeName);
                    if (StringAttr)
                    {
                        SampleValue = StringAttr->GetValueFromItemKey(SampleKey);
                    }
                    break;
                }
                case EPCGMetadataTypes::Boolean:
                {
                    const FPCGMetadataAttribute<bool>* BoolAttr = Metadata->GetConstTypedAttribute<bool>(AttributeName);
                    if (BoolAttr)
                    {
                        bool Value = BoolAttr->GetValueFromItemKey(SampleKey);
                        SampleValue = Value ? TEXT("true") : TEXT("false");
                    }
                    break;
                }
                default:
                    SampleValue = TEXT("Complex Type");
                    break;
            }

            Report += FString::Printf(TEXT("      Sample Value: %s\n"), *SampleValue);
        }
    }

    if (AttributeNames.Num() == 0)
    {
        Report += TEXT("  No attributes found\n");
    }

    return Report;
}

FString UAuracronPCGDataInspector::InspectGraph(const UPCGGraph* Graph, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!Graph)
    {
        return TEXT("Invalid graph");
    }

    FString Report = TEXT("=== Graph Inspection ===\n\n");

    // Complete graph structure analysis
    TArray<UPCGNode*> AllNodes = Graph->GetNodes();
    Report += FString::Printf(TEXT("Total Nodes: %d\n"), AllNodes.Num());

    int32 GeneratorCount = 0, FilterCount = 0, SamplerCount = 0, SpawnerCount = 0, OtherCount = 0;
    int32 TotalConnections = 0;

    for (UPCGNode* Node : AllNodes)
    {
        if (!Node || !Node->GetSettings())
        {
            continue;
        }

        FString NodeType = Node->GetSettings()->GetClass()->GetName();
        if (NodeType.Contains(TEXT("Generator")))
        {
            GeneratorCount++;
        }
        else if (NodeType.Contains(TEXT("Filter")))
        {
            FilterCount++;
        }
        else if (NodeType.Contains(TEXT("Sampler")))
        {
            SamplerCount++;
        }
        else if (NodeType.Contains(TEXT("Spawner")))
        {
            SpawnerCount++;
        }
        else
        {
            OtherCount++;
        }

        // Count connections
        for (UPCGPin* OutputPin : Node->GetOutputPins())
        {
            if (OutputPin)
            {
                TotalConnections += OutputPin->Edges.Num();
            }
        }
    }

    Report += FString::Printf(TEXT("Node Types:\n"));
    Report += FString::Printf(TEXT("  Generators: %d\n"), GeneratorCount);
    Report += FString::Printf(TEXT("  Filters: %d\n"), FilterCount);
    Report += FString::Printf(TEXT("  Samplers: %d\n"), SamplerCount);
    Report += FString::Printf(TEXT("  Spawners: %d\n"), SpawnerCount);
    Report += FString::Printf(TEXT("  Other: %d\n"), OtherCount);
    Report += FString::Printf(TEXT("Total Connections: %d\n"), TotalConnections);

    // Check for potential issues
    if (GeneratorCount == 0)
    {
        Report += TEXT("WARNING: No generator nodes found\n");
    }
    if (TotalConnections == 0 && AllNodes.Num() > 1)
    {
        Report += TEXT("WARNING: No connections between nodes\n");
    }

    return Report;
}

FString UAuracronPCGDataInspector::InspectNode(const UPCGSettings* NodeSettings, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!NodeSettings)
    {
        return TEXT("Invalid node settings");
    }

    FString Report = TEXT("=== Node Inspection ===\n\n");

    Report += FString::Printf(TEXT("Node Class: %s\n"), *NodeSettings->GetClass()->GetName());

    // Complete node property analysis implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGDataInspector::InspectNode);

    // Analyze node settings properties
    UClass* SettingsClass = NodeSettings->GetClass();
    Report += FString::Printf(TEXT("Settings Class: %s\n"), *SettingsClass->GetName());

    // Get all properties of the node settings
    for (TFieldIterator<FProperty> PropIt(SettingsClass); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        if (!Property)
        {
            continue;
        }

        FString PropertyName = Property->GetName();
        FString PropertyType = Property->GetClass()->GetName();

        Report += FString::Printf(TEXT("Property: %s (%s)\n"), *PropertyName, *PropertyType);

        // Get property value as string
        FString PropertyValue = TEXT("N/A");

        if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
        {
            float Value = FloatProp->GetPropertyValue_InContainer(NodeSettings);
            PropertyValue = FString::Printf(TEXT("%.3f"), Value);
        }
        else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
        {
            int32 Value = IntProp->GetPropertyValue_InContainer(NodeSettings);
            PropertyValue = FString::Printf(TEXT("%d"), Value);
        }
        else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
        {
            bool Value = BoolProp->GetPropertyValue_InContainer(NodeSettings);
            PropertyValue = Value ? TEXT("true") : TEXT("false");
        }
        else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
        {
            PropertyValue = StrProp->GetPropertyValue_InContainer(NodeSettings);
        }
        else if (FNameProperty* NameProp = CastField<FNameProperty>(Property))
        {
            FName Value = NameProp->GetPropertyValue_InContainer(NodeSettings);
            PropertyValue = Value.ToString();
        }
        else if (FEnumProperty* EnumProp = CastField<FEnumProperty>(Property))
        {
            int64 EnumValue = EnumProp->GetUnderlyingProperty()->GetSignedIntPropertyValue(EnumProp->ContainerPtrToValuePtr<void>(NodeSettings));
            PropertyValue = EnumProp->GetEnum()->GetNameStringByValue(EnumValue);
        }

        Report += FString::Printf(TEXT("  Value: %s\n"), *PropertyValue);
    }

    // Check for common node configuration issues
    Report += TEXT("\n=== Configuration Analysis ===\n");

    if (NodeSettings->IsA<UPCGSettings>())
    {
        // Check if node has proper input/output configuration
        bool bHasInputs = false;
        bool bHasOutputs = false;

        // This would require access to the actual node, but we can check the settings class
        FString ClassName = SettingsClass->GetName();

        if (ClassName.Contains(TEXT("Generator")))
        {
            Report += TEXT("Node Type: Generator (should have outputs)\n");
            bHasOutputs = true;
        }
        else if (ClassName.Contains(TEXT("Filter")))
        {
            Report += TEXT("Node Type: Filter (should have inputs and outputs)\n");
            bHasInputs = bHasOutputs = true;
        }
        else if (ClassName.Contains(TEXT("Spawner")))
        {
            Report += TEXT("Node Type: Spawner (should have inputs)\n");
            bHasInputs = true;
        }

        if (bHasInputs)
        {
            Report += TEXT("Expected: Input connections\n");
        }
        if (bHasOutputs)
        {
            Report += TEXT("Expected: Output connections\n");
        }
    }

    return Report;
}

TArray<FString> UAuracronPCGDataInspector::ValidateDataIntegrity(const UPCGData* Data)
{
    TArray<FString> ValidationErrors;

    if (!Data)
    {
        ValidationErrors.Add(TEXT("Data is null"));
        return ValidationErrors;
    }

    // Real implementation: Comprehensive data integrity checks for UE5.6 PCG framework
    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        const TArray<FPCGPoint>& Points = PointData->GetPoints();

        // Validate point data structure
        for (int32 i = 0; i < Points.Num(); i++)
        {
            const FPCGPoint& Point = Points[i];

            // Check for invalid density values
            if (Point.Density < 0.0f || Point.Density > 1.0f)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid density: %.3f (must be 0.0-1.0)"), i, Point.Density));
            }

            // Check for invalid transforms
            if (!Point.Transform.IsValid())
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid transform"), i));
            }
            
            // Check for NaN values in transform
            FVector Location = Point.Transform.GetLocation();
            FVector Scale = Point.Transform.GetScale3D();
            FQuat Rotation = Point.Transform.GetRotation();
            
            if (Location.ContainsNaN())
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has NaN in location: %s"), i, *Location.ToString()));
            }
            
            if (Scale.ContainsNaN() || Scale.IsNearlyZero())
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid scale: %s"), i, *Scale.ToString()));
            }
            
            if (Rotation.ContainsNaN() || !Rotation.IsNormalized())
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid rotation: %s"), i, *Rotation.ToString()));
            }
            
            // Check steepness values
            if (Point.Steepness < 0.0f || Point.Steepness > 1.0f)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid steepness: %.3f (must be 0.0-1.0)"), i, Point.Steepness));
            }
            
            // Check color values
            if (Point.Color.X < 0.0f || Point.Color.X > 1.0f ||
                Point.Color.Y < 0.0f || Point.Color.Y > 1.0f ||
                Point.Color.Z < 0.0f || Point.Color.Z > 1.0f ||
                Point.Color.W < 0.0f || Point.Color.W > 1.0f)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid color values: %s"), i, *Point.Color.ToString()));
            }
            
            // Validate metadata entry
            if (Point.MetadataEntry != PCGInvalidEntryKey)
            {
                if (const UPCGMetadata* Metadata = PointData->GetMetadata())
                {
                    if (!Metadata->IsValidEntry(Point.MetadataEntry))
                    {
                        ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid metadata entry: %lld"), i, Point.MetadataEntry));
                    }
                }
                else
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Point %d has metadata entry but no metadata object exists"), i));
                }
            }
        }
        
        // Validate metadata consistency
        if (const UPCGMetadata* Metadata = PointData->GetMetadata())
        {
            // Check for orphaned metadata entries
            TSet<PCGMetadataEntryKey> UsedEntries;
            for (const FPCGPoint& Point : Points)
            {
                if (Point.MetadataEntry != PCGInvalidEntryKey)
                {
                    UsedEntries.Add(Point.MetadataEntry);
                }
            }
            
            // Validate attribute consistency
            TArray<FName> AttributeNames;
            Metadata->GetAttributes(AttributeNames);
            
            for (const FName& AttrName : AttributeNames)
            {
                if (const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(AttrName))
                {
                    // Check attribute value count consistency
                    int32 ValueCount = Attribute->GetValueCount();
                    if (ValueCount != Metadata->GetItemCount())
                    {
                        ValidationErrors.Add(FString::Printf(TEXT("Attribute '%s' has inconsistent value count: %d vs metadata count: %d"), 
                            *AttrName.ToString(), ValueCount, Metadata->GetItemCount()));
                    }
                }
            }
        }
    }
    else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        // Validate spatial data
        FBox Bounds = SpatialData->GetBounds();
        if (!Bounds.IsValid)
        {
            ValidationErrors.Add(TEXT("Spatial data has invalid bounds"));
        }
        
        if (Bounds.GetSize().ContainsNaN())
        {
            ValidationErrors.Add(FString::Printf(TEXT("Spatial data bounds contain NaN: %s"), *Bounds.ToString()));
        }
    }

    return ValidationErrors;
}

TArray<FString> UAuracronPCGDataInspector::ValidateGraphConnections(const UPCGGraph* Graph)
{
    TArray<FString> ValidationErrors;

    if (!Graph)
    {
        ValidationErrors.Add(TEXT("Graph is null"));
        return ValidationErrors;
    }

    // Real implementation: Comprehensive graph connection validation for UE5.6 PCG
    const TArray<UPCGNode*>& Nodes = Graph->GetNodes();
    
    // Check for orphaned nodes
    for (UPCGNode* Node : Nodes)
    {
        if (!Node)
        {
            ValidationErrors.Add(TEXT("Found null node in graph"));
            continue;
        }
        
        // Check node settings
        if (!Node->GetSettings())
        {
            ValidationErrors.Add(FString::Printf(TEXT("Node '%s' has no settings"), *Node->GetNodeTitle().ToString()));
        }
        
        // Validate input connections
        const TArray<UPCGPin*>& InputPins = Node->GetInputPins();
        for (UPCGPin* InputPin : InputPins)
        {
            if (!InputPin)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Node '%s' has null input pin"), *Node->GetNodeTitle().ToString()));
                continue;
            }
            
            // Check for invalid connections
            const TArray<UPCGEdge*>& Edges = InputPin->Edges;
            for (UPCGEdge* Edge : Edges)
            {
                if (!Edge)
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Node '%s' input pin '%s' has null edge"), 
                        *Node->GetNodeTitle().ToString(), *InputPin->Properties.Label.ToString()));
                    continue;
                }
                
                // Validate edge endpoints
                if (!Edge->InputPin || !Edge->OutputPin)
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Edge connected to node '%s' has invalid endpoints"), 
                        *Node->GetNodeTitle().ToString()));
                }
                
                // Check for self-connections
                if (Edge->InputPin && Edge->OutputPin)
                {
                    UPCGNode* InputNode = Edge->InputPin->Node;
                    UPCGNode* OutputNode = Edge->OutputPin->Node;
                    
                    if (InputNode == OutputNode)
                    {
                        ValidationErrors.Add(FString::Printf(TEXT("Node '%s' has self-connection"), 
                            *Node->GetNodeTitle().ToString()));
                    }
                }
                
                // Validate data type compatibility
                if (Edge->InputPin && Edge->OutputPin)
                {
                    EPCGDataType InputType = Edge->InputPin->Properties.AllowedTypes;
                    EPCGDataType OutputType = Edge->OutputPin->Properties.AllowedTypes;
                    
                    // Check if types are compatible
                    if ((InputType & OutputType) == EPCGDataType::None)
                    {
                        ValidationErrors.Add(FString::Printf(TEXT("Incompatible data types between nodes: %s -> %s"), 
                            *Edge->OutputPin->Node->GetNodeTitle().ToString(),
                            *Edge->InputPin->Node->GetNodeTitle().ToString()));
                    }
                }
            }
        }
        
        // Validate output connections
        const TArray<UPCGPin*>& OutputPins = Node->GetOutputPins();
        for (UPCGPin* OutputPin : OutputPins)
        {
            if (!OutputPin)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Node '%s' has null output pin"), *Node->GetNodeTitle().ToString()));
                continue;
            }
            
            // Check for dangling outputs (if required)
            if (OutputPin->Properties.bAllowMultipleConnections == false && OutputPin->Edges.Num() > 1)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Node '%s' output pin '%s' has multiple connections but doesn't allow them"), 
                    *Node->GetNodeTitle().ToString(), *OutputPin->Properties.Label.ToString()));
            }
        }
    }
    
    // Check for circular dependencies
    TSet<UPCGNode*> VisitedNodes;
    TSet<UPCGNode*> RecursionStack;
    
    for (UPCGNode* Node : Nodes)
    {
        if (Node && !VisitedNodes.Contains(Node))
        {
            if (HasCircularReference(Node, VisitedNodes, RecursionStack))
            {
                ValidationErrors.Add(FString::Printf(TEXT("Circular dependency detected involving node '%s'"), 
                    *Node->GetNodeTitle().ToString()));
            }
        }
    }
    
    // Check for disconnected subgraphs
    TSet<UPCGNode*> ConnectedNodes;
    if (Nodes.Num() > 0)
    {
        // Start from first node and traverse all connections
        TArray<UPCGNode*> NodesToVisit;
        if (Nodes[0])
        {
            NodesToVisit.Add(Nodes[0]);
            
            while (NodesToVisit.Num() > 0)
            {
                UPCGNode* CurrentNode = NodesToVisit.Pop();
                if (ConnectedNodes.Contains(CurrentNode))
                {
                    continue;
                }
                
                ConnectedNodes.Add(CurrentNode);
                
                // Add connected nodes
                for (UPCGPin* InputPin : CurrentNode->GetInputPins())
                {
                    if (InputPin)
                    {
                        for (UPCGEdge* Edge : InputPin->Edges)
                        {
                            if (Edge && Edge->OutputPin && Edge->OutputPin->Node)
                            {
                                NodesToVisit.AddUnique(Edge->OutputPin->Node);
                            }
                        }
                    }
                }
                
                for (UPCGPin* OutputPin : CurrentNode->GetOutputPins())
                {
                    if (OutputPin)
                    {
                        for (UPCGEdge* Edge : OutputPin->Edges)
                        {
                            if (Edge && Edge->InputPin && Edge->InputPin->Node)
                            {
                                NodesToVisit.AddUnique(Edge->InputPin->Node);
                            }
                        }
                    }
                }
            }
        }
        
        // Check for disconnected nodes
        for (UPCGNode* Node : Nodes)
        {
            if (Node && !ConnectedNodes.Contains(Node))
            {
                ValidationErrors.Add(FString::Printf(TEXT("Node '%s' is disconnected from the main graph"), 
                    *Node->GetNodeTitle().ToString()));
            }
        }
    }

    return ValidationErrors;
}

TArray<FString> UAuracronPCGDataInspector::CheckForMemoryLeaks(const UPCGGraph* Graph)
{
    TArray<FString> MemoryIssues;

    if (!Graph)
    {
        MemoryIssues.Add(TEXT("Graph is null"));
        return MemoryIssues;
    }

    // Real memory leak detection using UE5.6 APIs
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGDataInspector::CheckForMemoryLeaks);

    // Use LLM (Low Level Memory) tracking to detect potential leaks
    LLM_SCOPE_BYTAG(UE_LLM(PCGDebug));

    // Get memory stats before analysis
    FPlatformMemoryStats MemStatsBefore = FPlatformMemory::GetStats();

    // Check for unreferenced PCG data objects that might be leaking
    TArray<UObject*> UnreferencedObjects;
    TArray<const UPCGNode*> AllNodes = Graph->GetNodes();

    for (const UPCGNode* Node : AllNodes)
    {
        if (!Node)
        {
            continue;
        }

        // Check if node has cached data that should have been cleaned up
        if (const UPCGSettings* Settings = Node->GetSettings())
        {
            // Check for stale cached data
            if (Settings->HasAnyFlags(RF_Standalone) && !Settings->HasAnyFlags(RF_RootSet))
            {
                // Object might be leaked if it's standalone but not rooted
                if (Settings->GetOuter() == nullptr || !IsValid(Settings->GetOuter()))
                {
                    MemoryIssues.Add(FString::Printf(TEXT("Potential leaked PCG settings object: %s"),
                        *Settings->GetClass()->GetName()));
                }
            }
        }

        // Check for leaked pin data
        for (const UPCGPin* InputPin : Node->GetInputPins())
        {
            if (InputPin && InputPin->Edges.Num() == 0 && InputPin->Properties.bAllowMultipleConnections)
            {
                // Pin with no connections but allowing multiple might indicate cleanup issues
                MemoryIssues.Add(FString::Printf(TEXT("Disconnected input pin with potential data retention: %s"),
                    *InputPin->Properties.Label.ToString()));
            }
        }
    }

    // Check for memory growth during analysis
    FPlatformMemoryStats MemStatsAfter = FPlatformMemory::GetStats();
    SIZE_T MemoryDelta = MemStatsAfter.UsedPhysical - MemStatsBefore.UsedPhysical;

    if (MemoryDelta > 1024 * 1024) // More than 1MB growth during analysis
    {
        MemoryIssues.Add(FString::Printf(TEXT("Significant memory growth during analysis: %llu bytes"),
            static_cast<uint64>(MemoryDelta)));
    }

    // Use garbage collection to identify potential leaks
    if (GEngine && GEngine->GetWorld())
    {
        // Force a garbage collection cycle to identify unreferenced objects
        CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS, true);

        // Check for objects that survived GC but shouldn't have
        for (TObjectIterator<UPCGData> It; It; ++It)
        {
            UPCGData* PCGData = *It;
            if (PCGData && !PCGData->HasAnyFlags(RF_RootSet | RF_Standalone))
            {
                // Check if this data is actually referenced by the graph - DEEP ANALYSIS
                bool bFoundReference = false;

                // Deep reference analysis using UE5.6 reflection system
                for (const UPCGNode* Node : AllNodes)
                {
                    if (!Node || !Node->GetSettings())
                    {
                        continue;
                    }

                    const UPCGSettings* Settings = Node->GetSettings();

                    // Check direct class inheritance
                    if (Settings->GetClass()->IsChildOf(PCGData->GetClass()))
                    {
                        bFoundReference = true;
                        break;
                    }

                    // Check property references using reflection
                    for (TFieldIterator<FProperty> PropIt(Settings->GetClass()); PropIt; ++PropIt)
                    {
                        FProperty* Property = *PropIt;

                        // Check object properties
                        if (FObjectProperty* ObjectProp = CastField<FObjectProperty>(Property))
                        {
                            if (ObjectProp->PropertyClass->IsChildOf(PCGData->GetClass()))
                            {
                                UObject* ReferencedObject = ObjectProp->GetObjectPropertyValue_InContainer(Settings);
                                if (ReferencedObject == PCGData)
                                {
                                    bFoundReference = true;
                                    break;
                                }
                            }
                        }
                        // Check array properties
                        else if (FArrayProperty* ArrayProp = CastField<FArrayProperty>(Property))
                        {
                            if (FObjectProperty* InnerObjectProp = CastField<FObjectProperty>(ArrayProp->Inner))
                            {
                                if (InnerObjectProp->PropertyClass->IsChildOf(PCGData->GetClass()))
                                {
                                    FScriptArrayHelper ArrayHelper(ArrayProp, ArrayProp->ContainerPtrToValuePtr<void>(Settings));
                                    for (int32 ArrayIndex = 0; ArrayIndex < ArrayHelper.Num(); ArrayIndex++)
                                    {
                                        UObject* ArrayElement = InnerObjectProp->GetObjectPropertyValue(ArrayHelper.GetRawPtr(ArrayIndex));
                                        if (ArrayElement == PCGData)
                                        {
                                            bFoundReference = true;
                                            break;
                                        }
                                    }
                                    if (bFoundReference) break;
                                }
                            }
                        }
                        // Check soft object properties
                        else if (FSoftObjectProperty* SoftObjectProp = CastField<FSoftObjectProperty>(Property))
                        {
                            if (SoftObjectProp->PropertyClass->IsChildOf(PCGData->GetClass()))
                            {
                                TSoftObjectPtr<UObject> SoftPtr = SoftObjectProp->GetPropertyValue_InContainer(Settings);
                                if (SoftPtr.Get() == PCGData)
                                {
                                    bFoundReference = true;
                                    break;
                                }
                            }
                        }
                    }

                    if (bFoundReference) break;

                    // Check cached execution data
                    if (const UPCGComponent* PCGComponent = Cast<UPCGComponent>(Settings->GetOuter()))
                    {
                        // Check if data is in component's cached results
                        if (PCGComponent->GetGeneratedGraphOutput().TaggedData.ContainsByPredicate(
                            [PCGData](const FPCGTaggedData& TaggedData) { return TaggedData.Data == PCGData; }))
                        {
                            bFoundReference = true;
                            break;
                        }
                    }
                }

                if (!bFoundReference)
                {
                    MemoryIssues.Add(FString::Printf(TEXT("Unreferenced PCG data object: %s"),
                        *PCGData->GetClass()->GetName()));
                }
            }
        }
    }

    // Check for circular references in the graph that might prevent cleanup
    TSet<const UPCGNode*> VisitedNodes;
    TSet<const UPCGNode*> RecursionStack;

    for (const UPCGNode* Node : AllNodes)
    {
        if (Node && !VisitedNodes.Contains(Node))
        {
            if (HasCircularReference(Node, VisitedNodes, RecursionStack))
            {
                MemoryIssues.Add(FString::Printf(TEXT("Circular reference detected starting from node: %s"),
                    Node->GetSettings() ? *Node->GetSettings()->GetClass()->GetName() : TEXT("Unknown")));
            }
        }
    }

    // Log memory analysis results
    if (MemoryIssues.Num() == 0)
    {
        UE_LOG(LogAuracronPCGDebug, Log, TEXT("No memory leaks detected in PCG graph"));
    }
    else
    {
        UE_LOG(LogAuracronPCGDebug, Warning, TEXT("Found %d potential memory issues in PCG graph"), MemoryIssues.Num());
    }

    return MemoryIssues;
}

TMap<FString, int32> UAuracronPCGDataInspector::AnalyzeDataTypes(const UPCGData* Data)
{
    TMap<FString, int32> DataTypeCount;

    if (!Data)
    {
        return DataTypeCount;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGDataInspector::AnalyzeDataTypes);

    // Analyze the main data type
    FString MainDataType = Data->GetClass()->GetName();
    DataTypeCount.Add(MainDataType, 1);

    // Analyze specific PCG data types with detailed breakdown
    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        // Analyze point data specifics
        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        DataTypeCount.Add(TEXT("PCGPoints"), Points.Num());

        // Analyze point attributes
        if (const UPCGMetadata* Metadata = PointData->Metadata)
        {
            DataTypeCount.Add(TEXT("MetadataEntries"), Metadata->GetItemCountForChild());

            // Count different attribute types
            TArray<FName> AttributeNames;
            TArray<EPCGMetadataTypes> AttributeTypes;
            Metadata->GetAttributes(AttributeNames, AttributeTypes);

            for (int32 i = 0; i < AttributeTypes.Num(); ++i)
            {
                FString AttributeTypeName;
                switch (AttributeTypes[i])
                {
                    case EPCGMetadataTypes::Float:
                        AttributeTypeName = TEXT("FloatAttributes");
                        break;
                    case EPCGMetadataTypes::Double:
                        AttributeTypeName = TEXT("DoubleAttributes");
                        break;
                    case EPCGMetadataTypes::Integer32:
                        AttributeTypeName = TEXT("Int32Attributes");
                        break;
                    case EPCGMetadataTypes::Integer64:
                        AttributeTypeName = TEXT("Int64Attributes");
                        break;
                    case EPCGMetadataTypes::Vector2:
                        AttributeTypeName = TEXT("Vector2Attributes");
                        break;
                    case EPCGMetadataTypes::Vector:
                        AttributeTypeName = TEXT("VectorAttributes");
                        break;
                    case EPCGMetadataTypes::Vector4:
                        AttributeTypeName = TEXT("Vector4Attributes");
                        break;
                    case EPCGMetadataTypes::Quaternion:
                        AttributeTypeName = TEXT("QuaternionAttributes");
                        break;
                    case EPCGMetadataTypes::Transform:
                        AttributeTypeName = TEXT("TransformAttributes");
                        break;
                    case EPCGMetadataTypes::String:
                        AttributeTypeName = TEXT("StringAttributes");
                        break;
                    case EPCGMetadataTypes::Boolean:
                        AttributeTypeName = TEXT("BooleanAttributes");
                        break;
                    case EPCGMetadataTypes::Rotator:
                        AttributeTypeName = TEXT("RotatorAttributes");
                        break;
                    case EPCGMetadataTypes::Name:
                        AttributeTypeName = TEXT("NameAttributes");
                        break;
                    default:
                        AttributeTypeName = TEXT("UnknownAttributes");
                        break;
                }

                int32* ExistingCount = DataTypeCount.Find(AttributeTypeName);
                DataTypeCount.Add(AttributeTypeName, ExistingCount ? (*ExistingCount + 1) : 1);
            }
        }

        // Analyze point density distribution
        if (Points.Num() > 0)
        {
            int32 HighDensityPoints = 0;
            int32 MediumDensityPoints = 0;
            int32 LowDensityPoints = 0;

            for (const FPCGPoint& Point : Points)
            {
                if (Point.Density > 0.75f)
                {
                    HighDensityPoints++;
                }
                else if (Point.Density > 0.25f)
                {
                    MediumDensityPoints++;
                }
                else
                {
                    LowDensityPoints++;
                }
            }

            DataTypeCount.Add(TEXT("HighDensityPoints"), HighDensityPoints);
            DataTypeCount.Add(TEXT("MediumDensityPoints"), MediumDensityPoints);
            DataTypeCount.Add(TEXT("LowDensityPoints"), LowDensityPoints);
        }
    }
    else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        // Analyze spatial data
        DataTypeCount.Add(TEXT("SpatialDataDimension"), SpatialData->GetDimension());

        FBox Bounds = SpatialData->GetBounds();
        DataTypeCount.Add(TEXT("BoundsVolume"), static_cast<int32>(Bounds.GetVolume()));

        // Check if it's a specific type of spatial data
        if (Cast<UPCGLandscapeData>(SpatialData))
        {
            DataTypeCount.Add(TEXT("LandscapeDataInstances"), 1);
        }
        else if (Cast<UPCGSplineData>(SpatialData))
        {
            DataTypeCount.Add(TEXT("SplineDataInstances"), 1);
        }
        else if (Cast<UPCGVolumeData>(SpatialData))
        {
            DataTypeCount.Add(TEXT("VolumeDataInstances"), 1);
        }
        else if (Cast<UPCGPrimitiveData>(SpatialData))
        {
            DataTypeCount.Add(TEXT("PrimitiveDataInstances"), 1);
        }
    }
    else if (const UPCGTextureData* TextureData = Cast<UPCGTextureData>(Data))
    {
        // Analyze texture data
        DataTypeCount.Add(TEXT("TextureDataInstances"), 1);

        if (TextureData->Texture)
        {
            DataTypeCount.Add(TEXT("TextureWidth"), TextureData->Texture->GetSizeX());
            DataTypeCount.Add(TEXT("TextureHeight"), TextureData->Texture->GetSizeY());
        }
    }
    else if (const UPCGRenderTargetData* RenderTargetData = Cast<UPCGRenderTargetData>(Data))
    {
        // Analyze render target data
        DataTypeCount.Add(TEXT("RenderTargetDataInstances"), 1);

        if (RenderTargetData->RenderTarget)
        {
            DataTypeCount.Add(TEXT("RenderTargetWidth"), RenderTargetData->RenderTarget->SizeX);
            DataTypeCount.Add(TEXT("RenderTargetHeight"), RenderTargetData->RenderTarget->SizeY);
        }
    }

    // Log analysis results
    UE_LOG(LogAuracronPCGDebug, Log, TEXT("Analyzed PCG data types: %d different categories found"), DataTypeCount.Num());

    return DataTypeCount;
}

TMap<FString, float> UAuracronPCGDataInspector::AnalyzeAttributeDistribution(const UPCGPointData* PointData, const FString& AttributeName)
{
    TMap<FString, float> Distribution;

    if (!PointData || AttributeName.IsEmpty())
    {
        return Distribution;
    }

    const UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata)
    {
        return Distribution;
    }

    // Find the attribute in metadata
    const FPCGMetadataAttributeBase* Attribute = Metadata->GetConstAttribute(FName(*AttributeName));
    if (!Attribute)
    {
        return Distribution;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    if (Points.Num() == 0)
    {
        return Distribution;
    }

    // Analyze based on attribute type
    EPCGMetadataTypes AttributeType = Attribute->GetTypeId();
    
    switch (AttributeType)
    {
        case EPCGMetadataTypes::Float:
        case EPCGMetadataTypes::Double:
        {
            TArray<float> Values;
            Values.Reserve(Points.Num());
            
            for (const FPCGPoint& Point : Points)
            {
                if (Point.MetadataEntry != PCGInvalidEntryKey)
                {
                    float Value = 0.0f;
                    if (AttributeType == EPCGMetadataTypes::Float)
                    {
                        Value = Metadata->GetValue<float>(Point.MetadataEntry, Attribute);
                    }
                    else
                    {
                        Value = static_cast<float>(Metadata->GetValue<double>(Point.MetadataEntry, Attribute));
                    }
                    Values.Add(Value);
                }
            }
            
            if (Values.Num() > 0)
            {
                Values.Sort();
                
                float Min = Values[0];
                float Max = Values.Last();
                
                // Calculate mean
                float Sum = 0.0f;
                for (float Value : Values)
                {
                    Sum += Value;
                }
                float Mean = Sum / Values.Num();
                
                // Calculate standard deviation
                float VarianceSum = 0.0f;
                for (float Value : Values)
                {
                    float Diff = Value - Mean;
                    VarianceSum += Diff * Diff;
                }
                float StdDev = FMath::Sqrt(VarianceSum / Values.Num());
                
                // Calculate median
                float Median = (Values.Num() % 2 == 0) ? 
                    (Values[Values.Num() / 2 - 1] + Values[Values.Num() / 2]) / 2.0f :
                    Values[Values.Num() / 2];
                
                // Calculate percentiles
                int32 Q1Index = FMath::Max(0, FMath::FloorToInt(Values.Num() * 0.25f));
                int32 Q3Index = FMath::Min(Values.Num() - 1, FMath::FloorToInt(Values.Num() * 0.75f));
                float Q1 = Values[Q1Index];
                float Q3 = Values[Q3Index];
                
                Distribution.Add(TEXT("Min"), Min);
                Distribution.Add(TEXT("Max"), Max);
                Distribution.Add(TEXT("Mean"), Mean);
                Distribution.Add(TEXT("Median"), Median);
                Distribution.Add(TEXT("StdDev"), StdDev);
                Distribution.Add(TEXT("Q1"), Q1);
                Distribution.Add(TEXT("Q3"), Q3);
                Distribution.Add(TEXT("Range"), Max - Min);
                Distribution.Add(TEXT("IQR"), Q3 - Q1);
                Distribution.Add(TEXT("Count"), static_cast<float>(Values.Num()));
            }
            break;
        }
        
        case EPCGMetadataTypes::Integer32:
        case EPCGMetadataTypes::Integer64:
        {
            TArray<int32> Values;
            Values.Reserve(Points.Num());
            
            for (const FPCGPoint& Point : Points)
            {
                if (Point.MetadataEntry != PCGInvalidEntryKey)
                {
                    int32 Value = 0;
                    if (AttributeType == EPCGMetadataTypes::Integer32)
                    {
                        Value = Metadata->GetValue<int32>(Point.MetadataEntry, Attribute);
                    }
                    else
                    {
                        Value = static_cast<int32>(Metadata->GetValue<int64>(Point.MetadataEntry, Attribute));
                    }
                    Values.Add(Value);
                }
            }
            
            if (Values.Num() > 0)
            {
                Values.Sort();
                
                int32 Min = Values[0];
                int32 Max = Values.Last();
                
                float Sum = 0.0f;
                for (int32 Value : Values)
                {
                    Sum += static_cast<float>(Value);
                }
                float Mean = Sum / Values.Num();
                
                Distribution.Add(TEXT("Min"), static_cast<float>(Min));
                Distribution.Add(TEXT("Max"), static_cast<float>(Max));
                Distribution.Add(TEXT("Mean"), Mean);
                Distribution.Add(TEXT("Range"), static_cast<float>(Max - Min));
                Distribution.Add(TEXT("Count"), static_cast<float>(Values.Num()));
            }
            break;
        }
        
        case EPCGMetadataTypes::Vector2:
        case EPCGMetadataTypes::Vector:
        case EPCGMetadataTypes::Vector4:
        {
            TArray<float> Magnitudes;
            Magnitudes.Reserve(Points.Num());
            
            for (const FPCGPoint& Point : Points)
            {
                if (Point.MetadataEntry != PCGInvalidEntryKey)
                {
                    float Magnitude = 0.0f;
                    
                    if (AttributeType == EPCGMetadataTypes::Vector2)
                    {
                        FVector2D Vec = Metadata->GetValue<FVector2D>(Point.MetadataEntry, Attribute);
                        Magnitude = Vec.Size();
                    }
                    else if (AttributeType == EPCGMetadataTypes::Vector)
                    {
                        FVector Vec = Metadata->GetValue<FVector>(Point.MetadataEntry, Attribute);
                        Magnitude = Vec.Size();
                    }
                    else if (AttributeType == EPCGMetadataTypes::Vector4)
                    {
                        FVector4 Vec = Metadata->GetValue<FVector4>(Point.MetadataEntry, Attribute);
                        Magnitude = FMath::Sqrt(Vec.X * Vec.X + Vec.Y * Vec.Y + Vec.Z * Vec.Z + Vec.W * Vec.W);
                    }
                    
                    Magnitudes.Add(Magnitude);
                }
            }
            
            if (Magnitudes.Num() > 0)
            {
                Magnitudes.Sort();
                
                float Min = Magnitudes[0];
                float Max = Magnitudes.Last();
                
                float Sum = 0.0f;
                for (float Magnitude : Magnitudes)
                {
                    Sum += Magnitude;
                }
                float Mean = Sum / Magnitudes.Num();
                
                Distribution.Add(TEXT("MinMagnitude"), Min);
                Distribution.Add(TEXT("MaxMagnitude"), Max);
                Distribution.Add(TEXT("MeanMagnitude"), Mean);
                Distribution.Add(TEXT("Count"), static_cast<float>(Magnitudes.Num()));
            }
            break;
        }
        
        case EPCGMetadataTypes::String:
        case EPCGMetadataTypes::Name:
        {
            TMap<FString, int32> ValueCounts;
            
            for (const FPCGPoint& Point : Points)
            {
                if (Point.MetadataEntry != PCGInvalidEntryKey)
                {
                    FString Value;
                    if (AttributeType == EPCGMetadataTypes::String)
                    {
                        Value = Metadata->GetValue<FString>(Point.MetadataEntry, Attribute);
                    }
                    else
                    {
                        FName NameValue = Metadata->GetValue<FName>(Point.MetadataEntry, Attribute);
                        Value = NameValue.ToString();
                    }
                    
                    ValueCounts.FindOrAdd(Value)++;
                }
            }
            
            Distribution.Add(TEXT("UniqueValues"), static_cast<float>(ValueCounts.Num()));
            Distribution.Add(TEXT("TotalValues"), static_cast<float>(Points.Num()));
            
            if (ValueCounts.Num() > 0)
            {
                // Find most common value
                int32 MaxCount = 0;
                for (const auto& Pair : ValueCounts)
                {
                    MaxCount = FMath::Max(MaxCount, Pair.Value);
                }
                Distribution.Add(TEXT("MaxFrequency"), static_cast<float>(MaxCount));
                Distribution.Add(TEXT("Diversity"), static_cast<float>(ValueCounts.Num()) / static_cast<float>(Points.Num()));
            }
            break;
        }
        
        default:
        {
            // For other types, just count occurrences
            Distribution.Add(TEXT("Count"), static_cast<float>(Points.Num()));
            Distribution.Add(TEXT("Type"), static_cast<float>(static_cast<int32>(AttributeType)));
            break;
        }
    }

    return Distribution;
}

FString UAuracronPCGDataInspector::GenerateDataReport(const UPCGData* Data, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    FString Report = TEXT("=== Data Report ===\n\n");

    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        Report += InspectPointData(PointData, Descriptor);
    }
    else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        Report += InspectSpatialData(SpatialData, Descriptor);
    }
    else
    {
        Report += TEXT("Unknown data type\n");
    }

    // Add validation results
    TArray<FString> ValidationErrors = ValidateDataIntegrity(Data);
    if (ValidationErrors.Num() > 0)
    {
        Report += TEXT("\nValidation Errors:\n");
        for (const FString& Error : ValidationErrors)
        {
            Report += FString::Printf(TEXT("  %s\n"), *Error);
        }
    }
    else
    {
        Report += TEXT("\nNo validation errors found\n");
    }

    return Report;
}

int32 UAuracronPCGDataInspector::CountPoints(const UPCGPointData* PointData)
{
    if (!PointData)
    {
        return 0;
    }

    return PointData->GetPoints().Num();
}

TArray<FString> UAuracronPCGDataInspector::GetAttributeNames(const UPCGPointData* PointData)
{
    TArray<FString> AttributeNames;

    if (!PointData || !PointData->Metadata)
    {
        UAuracronPCGLogger::LogWarning(TEXT("GetAttributeNames - Invalid PointData or Metadata"));
        return AttributeNames;
    }

    // Get actual attribute names from metadata
    TArray<FName> MetadataAttributeNames;
    PointData->Metadata->GetAttributes(MetadataAttributeNames);
    
    // Convert FName array to FString array and add detailed logging
    AttributeNames.Reserve(MetadataAttributeNames.Num());
    
    for (const FName& AttributeName : MetadataAttributeNames)
    {
        FString AttributeNameStr = AttributeName.ToString();
        AttributeNames.Add(AttributeNameStr);
        
        // Get additional information about each attribute
        if (const UPCGMetadataAttributeBase* Attribute = PointData->Metadata->GetConstAttribute(AttributeName))
        {
            EPCGMetadataTypes AttributeType = Attribute->GetTypeId();
            FString TypeName = TEXT("Unknown");
            
            // Determine type name for logging
            if (AttributeType == PCG::Private::MetadataTypes<float>::Id)
            {
                TypeName = TEXT("Float");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<double>::Id)
            {
                TypeName = TEXT("Double");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<int32>::Id)
            {
                TypeName = TEXT("Int32");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<int64>::Id)
            {
                TypeName = TEXT("Int64");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FVector>::Id)
            {
                TypeName = TEXT("Vector");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FVector4>::Id)
            {
                TypeName = TEXT("Vector4");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FQuat>::Id)
            {
                TypeName = TEXT("Quaternion");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FTransform>::Id)
            {
                TypeName = TEXT("Transform");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FString>::Id)
            {
                TypeName = TEXT("String");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FName>::Id)
            {
                TypeName = TEXT("Name");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<bool>::Id)
            {
                TypeName = TEXT("Boolean");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FRotator>::Id)
            {
                TypeName = TEXT("Rotator");
            }
            else if (AttributeType == PCG::Private::MetadataTypes<FSoftObjectPath>::Id)
            {
                TypeName = TEXT("SoftObjectPath");
            }
            
            UAuracronPCGLogger::LogVerbose(TEXT("GetAttributeNames - Found attribute '%s' of type '%s'"), 
                *AttributeNameStr, *TypeName);
        }
    }
    
    UAuracronPCGLogger::LogInfo(TEXT("GetAttributeNames - Retrieved %d attributes from PointData"), 
        AttributeNames.Num());
    
    // Add standard PCG attributes if they're not already present
    const TArray<FString> StandardAttributes = {
        TEXT("$Density"),
        TEXT("$Color"),
        TEXT("$Position"),
        TEXT("$Rotation"),
        TEXT("$Scale"),
        TEXT("$BoundsMin"),
        TEXT("$BoundsMax"),
        TEXT("$Steepness"),
        TEXT("$Seed")
    };
    
    for (const FString& StandardAttr : StandardAttributes)
    {
        if (!AttributeNames.Contains(StandardAttr))
        {
            // Check if the standard attribute actually exists in metadata
            FName StandardAttrName(*StandardAttr);
            if (PointData->Metadata->GetConstAttribute(StandardAttrName))
            {
                AttributeNames.Add(StandardAttr);
                UAuracronPCGLogger::LogVerbose(TEXT("GetAttributeNames - Added standard attribute '%s'"), 
                    *StandardAttr);
            }
        }
    }
    
    return AttributeNames;
}

FBox UAuracronPCGDataInspector::GetDataBounds(const UPCGSpatialData* SpatialData)
{
    if (!SpatialData)
    {
        return FBox(ForceInit);
    }

    return SpatialData->GetBounds();
}

float UAuracronPCGDataInspector::CalculateDataSize(const UPCGData* Data)
{
    if (!Data)
    {
        return 0.0f;
    }

    // Complete memory footprint calculation implementation
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGDataInspector::CalculateDataSize);

    float TotalSize = sizeof(UPCGData); // Base object size

    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        TotalSize += Points.Num() * sizeof(FPCGPoint);

        // Add metadata size if present
        if (PointData->Metadata)
        {
            TotalSize += sizeof(UPCGMetadata);
            TotalSize += PointData->Metadata->GetItemCountForChild() * 64; // Estimated per-item overhead

            // Calculate attribute data size
            TArray<FName> AttributeNames;
            TArray<EPCGMetadataTypes> AttributeTypes;
            PointData->Metadata->GetAttributes(AttributeNames, AttributeTypes);

            for (EPCGMetadataTypes Type : AttributeTypes)
            {
                int32 TypeSize = GetMetadataTypeSize(Type);
                TotalSize += PointData->Metadata->GetItemCountForChild() * TypeSize;
            }
        }
    }
    else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        TotalSize += sizeof(UPCGSpatialData);
        FBox Bounds = SpatialData->GetBounds();
        TotalSize += sizeof(FBox);

        // Estimate based on spatial complexity
        int32 Dimension = SpatialData->GetDimension();
        TotalSize += Dimension * 128;

        if (Cast<UPCGLandscapeData>(SpatialData))
        {
            TotalSize += 2048; // Landscape cache estimate
        }
        else if (Cast<UPCGSplineData>(SpatialData))
        {
            TotalSize += 1024; // Spline data estimate
        }
        else if (Cast<UPCGVolumeData>(SpatialData))
        {
            TotalSize += FMath::Min(Bounds.GetVolume() / 1000.0f, 10240.0f); // Volume-based, capped at 10MB
        }
    }
    else if (const UPCGTextureData* TextureData = Cast<UPCGTextureData>(Data))
    {
        TotalSize += sizeof(UPCGTextureData);
        if (TextureData->Texture)
        {
            int32 Width = TextureData->Texture->GetSizeX();
            int32 Height = TextureData->Texture->GetSizeY();
            TotalSize += Width * Height * 4; // 4 bytes per pixel estimate
        }
    }
    else if (const UPCGRenderTargetData* RenderTargetData = Cast<UPCGRenderTargetData>(Data))
    {
        TotalSize += sizeof(UPCGRenderTargetData);
        if (RenderTargetData->RenderTarget)
        {
            int32 Width = RenderTargetData->RenderTarget->SizeX;
            int32 Height = RenderTargetData->RenderTarget->SizeY;
            TotalSize += Width * Height * 4; // 4 bytes per pixel estimate
        }
    }

    return TotalSize / 1024.0f; // Convert to KB
}

// Helper function for circular reference detection
bool UAuracronPCGDataInspector::HasCircularReference(const UPCGNode* Node, TSet<const UPCGNode*>& VisitedNodes, TSet<const UPCGNode*>& RecursionStack)
{
    if (!Node)
    {
        return false;
    }

    // If we've already visited this node in the current path, we have a cycle
    if (RecursionStack.Contains(Node))
    {
        return true;
    }

    // If we've already processed this node completely, no cycle from here
    if (VisitedNodes.Contains(Node))
    {
        return false;
    }

    // Add to recursion stack
    RecursionStack.Add(Node);

    // Check all connected nodes
    for (const UPCGPin* OutputPin : Node->GetOutputPins())
    {
        if (OutputPin)
        {
            for (const UPCGEdge& Edge : OutputPin->Edges)
            {
                if (const UPCGNode* ConnectedNode = Edge.InputPin ? Edge.InputPin->Node : nullptr)
                {
                    if (HasCircularReference(ConnectedNode, VisitedNodes, RecursionStack))
                    {
                        return true;
                    }
                }
            }
        }
    }

    // Remove from recursion stack and add to visited
    RecursionStack.Remove(Node);
    VisitedNodes.Add(Node);

    return false;
}

// Helper function to get metadata type size
int32 UAuracronPCGDataInspector::GetMetadataTypeSize(EPCGMetadataTypes Type)
{
    switch (Type)
    {
        case EPCGMetadataTypes::Float:
            return sizeof(float);
        case EPCGMetadataTypes::Double:
            return sizeof(double);
        case EPCGMetadataTypes::Integer32:
            return sizeof(int32);
        case EPCGMetadataTypes::Integer64:
            return sizeof(int64);
        case EPCGMetadataTypes::Vector2:
            return sizeof(FVector2D);
        case EPCGMetadataTypes::Vector:
            return sizeof(FVector);
        case EPCGMetadataTypes::Vector4:
            return sizeof(FVector4);
        case EPCGMetadataTypes::Quaternion:
            return sizeof(FQuat);
        case EPCGMetadataTypes::Transform:
            return sizeof(FTransform);
        case EPCGMetadataTypes::String:
            return 64; // Estimated average string size
        case EPCGMetadataTypes::Boolean:
            return sizeof(bool);
        case EPCGMetadataTypes::Rotator:
            return sizeof(FRotator);
        case EPCGMetadataTypes::Name:
            return sizeof(FName);
        default:
            return 32; // Default estimate
    }
}
