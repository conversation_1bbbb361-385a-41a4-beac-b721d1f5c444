using UnrealBuildTool;
public class AuracronAdaptiveCreaturesBridge : ModuleRules
{
    public AuracronAdaptiveCreaturesBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine","GameplayTasks",
            "NavigationSystem",
            "MassEntity",
            "MassEntityTestSuite",
            "MassEntity",
            "MassMovement",
            "MassNavigation",
            "MassReplication",
            "MassSimulation",
            "MassSpawner",
            "StateTreeModule",
            "StateTreeEditorModule",
            "GameplayStateTreeModule",
            "StructUtils",
            "SmartObjectsModule",
            "ZoneGraph",
            "ZoneGraphAnnotations","UMG",
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin"
        });
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "RenderCore",
            "RHI","ChaosCore",
            "PhysicsCore","NiagaraCore",
            "NiagaraShader",
            "CinematicCamera","MovieScene",
            "MovieSceneTracks",
            "AnimGraphRuntime",
            "AnimationCore",
            "AnimationBlueprintLibrary"
        });
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronAdaptiveCreaturesBridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronAdaptiveCreaturesBridge/Private"
        });
    }
}
