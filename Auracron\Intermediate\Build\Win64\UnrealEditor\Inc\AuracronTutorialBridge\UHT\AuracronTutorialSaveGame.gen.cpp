// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronTutorialSaveGame.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronTutorialSaveGame() {}

// ********** Begin Cross Module References ********************************************************
AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialSaveGame();
AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialSaveGame_NoRegister();
AURACRONTUTORIALBRIDGE_API UEnum* Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState();
AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature();
AURACRONTUTORIALBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialProgressData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_USaveGame();
UPackage* Z_Construct_UPackage__Script_AuracronTutorialBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronTutorialProgressData *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData;
class UScriptStruct* FAuracronTutorialProgressData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTutorialProgressData, (UObject*)Z_Construct_UPackage__Script_AuracronTutorialBridge(), TEXT("AuracronTutorialProgressData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para dados de progresso de um tutorial espec\xc3\xad""fico\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de progresso de um tutorial espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialState_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStepIndex_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x8dndice do passo atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x8dndice do passo atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressPercentage_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso percentual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso percentual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdated_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedSteps_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Passos completados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Passos completados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalTimeSpent_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo total gasto no tutorial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo total gasto no tutorial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttemptCount_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de tentativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de tentativas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TutorialState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TutorialState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentStepIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProgressPercentage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedSteps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalTimeSpent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttemptCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTutorialProgressData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialState = { "TutorialState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, TutorialState), Z_Construct_UEnum_AuracronTutorialBridge_EAuracronTutorialState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialState_MetaData), NewProp_TutorialState_MetaData) }; // 102943273
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CurrentStepIndex = { "CurrentStepIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, CurrentStepIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStepIndex_MetaData), NewProp_CurrentStepIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_ProgressPercentage = { "ProgressPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, ProgressPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressPercentage_MetaData), NewProp_ProgressPercentage_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_LastUpdated = { "LastUpdated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, LastUpdated), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdated_MetaData), NewProp_LastUpdated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CompletedSteps_Inner = { "CompletedSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CompletedSteps = { "CompletedSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, CompletedSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedSteps_MetaData), NewProp_CompletedSteps_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TotalTimeSpent = { "TotalTimeSpent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, TotalTimeSpent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalTimeSpent_MetaData), NewProp_TotalTimeSpent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_AttemptCount = { "AttemptCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTutorialProgressData, AttemptCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttemptCount_MetaData), NewProp_AttemptCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TutorialState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CurrentStepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_ProgressPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_LastUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CompletedSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_CompletedSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_TotalTimeSpent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewProp_AttemptCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
	nullptr,
	&NewStructOps,
	"AuracronTutorialProgressData",
	Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::PropPointers),
	sizeof(FAuracronTutorialProgressData),
	alignof(FAuracronTutorialProgressData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTutorialProgressData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTutorialProgressData ***************************************

// ********** Begin Delegate FOnAllProgressReset ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics
{
	struct AuracronTutorialSaveGame_eventOnAllProgressReset_Parms
	{
		int32 TutorialsInProgress;
		int32 CompletedTutorials;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate para notificar quando todo o progresso for resetado\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate para notificar quando todo o progresso for resetado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TutorialsInProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedTutorials;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::NewProp_TutorialsInProgress = { "TutorialsInProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventOnAllProgressReset_Parms, TutorialsInProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::NewProp_CompletedTutorials = { "CompletedTutorials", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventOnAllProgressReset_Parms, CompletedTutorials), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::NewProp_TutorialsInProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::NewProp_CompletedTutorials,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "OnAllProgressReset__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::AuracronTutorialSaveGame_eventOnAllProgressReset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::AuracronTutorialSaveGame_eventOnAllProgressReset_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronTutorialSaveGame::FOnAllProgressReset_DelegateWrapper(const FMulticastScriptDelegate& OnAllProgressReset, int32 TutorialsInProgress, int32 CompletedTutorials)
{
	struct AuracronTutorialSaveGame_eventOnAllProgressReset_Parms
	{
		int32 TutorialsInProgress;
		int32 CompletedTutorials;
	};
	AuracronTutorialSaveGame_eventOnAllProgressReset_Parms Parms;
	Parms.TutorialsInProgress=TutorialsInProgress;
	Parms.CompletedTutorials=CompletedTutorials;
	OnAllProgressReset.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAllProgressReset *****************************************************

// ********** Begin Class UAuracronTutorialSaveGame Function GetTutorialProgress *******************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics
{
	struct AuracronTutorialSaveGame_eventGetTutorialProgress_Parms
	{
		FString TutorialID;
		FAuracronTutorialProgressData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m o progresso de um tutorial espec\xc3\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o progresso de um tutorial espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventGetTutorialProgress_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventGetTutorialProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTutorialProgressData, METADATA_PARAMS(0, nullptr) }; // 4224676143
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "GetTutorialProgress", Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::AuracronTutorialSaveGame_eventGetTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::AuracronTutorialSaveGame_eventGetTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execGetTutorialProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTutorialProgressData*)Z_Param__Result=P_THIS->GetTutorialProgress(Z_Param_TutorialID);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function GetTutorialProgress *********************

// ********** Begin Class UAuracronTutorialSaveGame Function IsTutorialCompleted *******************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics
{
	struct AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms
	{
		FString TutorialID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se um tutorial foi completado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se um tutorial foi completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
void Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms), &Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "IsTutorialCompleted", Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::AuracronTutorialSaveGame_eventIsTutorialCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execIsTutorialCompleted)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTutorialCompleted(Z_Param_TutorialID);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function IsTutorialCompleted *********************

// ********** Begin Class UAuracronTutorialSaveGame Function MarkTutorialCompleted *****************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics
{
	struct AuracronTutorialSaveGame_eventMarkTutorialCompleted_Parms
	{
		FString TutorialID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Marca um tutorial como completado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Marca um tutorial como completado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventMarkTutorialCompleted_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::NewProp_TutorialID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "MarkTutorialCompleted", Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::AuracronTutorialSaveGame_eventMarkTutorialCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::AuracronTutorialSaveGame_eventMarkTutorialCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execMarkTutorialCompleted)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MarkTutorialCompleted(Z_Param_TutorialID);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function MarkTutorialCompleted *******************

// ********** Begin Class UAuracronTutorialSaveGame Function ResetAllProgress **********************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reseta todo o progresso dos tutoriais\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reseta todo o progresso dos tutoriais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "ResetAllProgress", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execResetAllProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAllProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function ResetAllProgress ************************

// ********** Begin Class UAuracronTutorialSaveGame Function ResetTutorialProgress *****************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics
{
	struct AuracronTutorialSaveGame_eventResetTutorialProgress_Parms
	{
		FString TutorialID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reseta o progresso de um tutorial espec\xc3\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reseta o progresso de um tutorial espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventResetTutorialProgress_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::NewProp_TutorialID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "ResetTutorialProgress", Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::AuracronTutorialSaveGame_eventResetTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::AuracronTutorialSaveGame_eventResetTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execResetTutorialProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetTutorialProgress(Z_Param_TutorialID);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function ResetTutorialProgress *******************

// ********** Begin Class UAuracronTutorialSaveGame Function UpdateTutorialProgress ****************
struct Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics
{
	struct AuracronTutorialSaveGame_eventUpdateTutorialProgress_Parms
	{
		FString TutorialID;
		FAuracronTutorialProgressData ProgressData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualiza o progresso de um tutorial espec\xc3\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualiza o progresso de um tutorial espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProgressData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProgressData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::NewProp_TutorialID = { "TutorialID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventUpdateTutorialProgress_Parms, TutorialID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialID_MetaData), NewProp_TutorialID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::NewProp_ProgressData = { "ProgressData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronTutorialSaveGame_eventUpdateTutorialProgress_Parms, ProgressData), Z_Construct_UScriptStruct_FAuracronTutorialProgressData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProgressData_MetaData), NewProp_ProgressData_MetaData) }; // 4224676143
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::NewProp_TutorialID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::NewProp_ProgressData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronTutorialSaveGame, nullptr, "UpdateTutorialProgress", Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::AuracronTutorialSaveGame_eventUpdateTutorialProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::AuracronTutorialSaveGame_eventUpdateTutorialProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronTutorialSaveGame::execUpdateTutorialProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TutorialID);
	P_GET_STRUCT_REF(FAuracronTutorialProgressData,Z_Param_Out_ProgressData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTutorialProgress(Z_Param_TutorialID,Z_Param_Out_ProgressData);
	P_NATIVE_END;
}
// ********** End Class UAuracronTutorialSaveGame Function UpdateTutorialProgress ******************

// ********** Begin Class UAuracronTutorialSaveGame ************************************************
void UAuracronTutorialSaveGame::StaticRegisterNativesUAuracronTutorialSaveGame()
{
	UClass* Class = UAuracronTutorialSaveGame::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetTutorialProgress", &UAuracronTutorialSaveGame::execGetTutorialProgress },
		{ "IsTutorialCompleted", &UAuracronTutorialSaveGame::execIsTutorialCompleted },
		{ "MarkTutorialCompleted", &UAuracronTutorialSaveGame::execMarkTutorialCompleted },
		{ "ResetAllProgress", &UAuracronTutorialSaveGame::execResetAllProgress },
		{ "ResetTutorialProgress", &UAuracronTutorialSaveGame::execResetTutorialProgress },
		{ "UpdateTutorialProgress", &UAuracronTutorialSaveGame::execUpdateTutorialProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronTutorialSaveGame;
UClass* UAuracronTutorialSaveGame::GetPrivateStaticClass()
{
	using TClass = UAuracronTutorialSaveGame;
	if (!Z_Registration_Info_UClass_UAuracronTutorialSaveGame.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronTutorialSaveGame"),
			Z_Registration_Info_UClass_UAuracronTutorialSaveGame.InnerSingleton,
			StaticRegisterNativesUAuracronTutorialSaveGame,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronTutorialSaveGame.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronTutorialSaveGame_NoRegister()
{
	return UAuracronTutorialSaveGame::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronTutorialSaveGame_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe de Save Game para armazenar progresso dos tutoriais\n */" },
#endif
		{ "IncludePath", "AuracronTutorialSaveGame.h" },
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe de Save Game para armazenar progresso dos tutoriais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveSlotName_MetaData[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do slot de salvamento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do slot de salvamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserIndex_MetaData[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x8dndice do usu\xc3\xa1rio (usando int32 para compatibilidade com Blueprint) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x8dndice do usu\xc3\xa1rio (usando int32 para compatibilidade com Blueprint)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveGameVersion_MetaData[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vers\xc3\xa3o do save game */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vers\xc3\xa3o do save game" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TutorialProgressMap_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progresso de todos os tutoriais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progresso de todos os tutoriais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedTutorials_MetaData[] = {
		{ "Category", "Tutorial Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lista de tutoriais completados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de tutoriais completados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAIMentorEnabled_MetaData[] = {
		{ "Category", "AI Mentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es do AI Mentor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do AI Mentor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIMentorVolume_MetaData[] = {
		{ "Category", "AI Mentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume do AI Mentor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume do AI Mentor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIMentorSpeechRate_MetaData[] = {
		{ "Category", "AI Mentor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de fala do AI Mentor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de fala do AI Mentor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da cria\xc3\xa7\xc3\xa3o do save */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da cria\xc3\xa7\xc3\xa3o do save" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModified_MetaData[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da \xc3\xbaltima modifica\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da \xc3\xbaltima modifica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAllProgressReset_MetaData[] = {
		{ "Category", "Tutorial Events" },
		{ "ModuleRelativePath", "Public/AuracronTutorialSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SaveSlotName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UserIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SaveGameVersion;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TutorialProgressMap_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TutorialProgressMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TutorialProgressMap;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletedTutorials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedTutorials;
	static void NewProp_bAIMentorEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAIMentorEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AIMentorVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AIMentorSpeechRate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModified;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAllProgressReset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_GetTutorialProgress, "GetTutorialProgress" }, // 759088059
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_IsTutorialCompleted, "IsTutorialCompleted" }, // 54095548
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_MarkTutorialCompleted, "MarkTutorialCompleted" }, // 143577585
		{ &Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature, "OnAllProgressReset__DelegateSignature" }, // 2958574084
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetAllProgress, "ResetAllProgress" }, // 1751711654
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_ResetTutorialProgress, "ResetTutorialProgress" }, // 3078622985
		{ &Z_Construct_UFunction_UAuracronTutorialSaveGame_UpdateTutorialProgress, "UpdateTutorialProgress" }, // 664387322
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronTutorialSaveGame>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_SaveSlotName = { "SaveSlotName", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, SaveSlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveSlotName_MetaData), NewProp_SaveSlotName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_UserIndex = { "UserIndex", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, UserIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserIndex_MetaData), NewProp_UserIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_SaveGameVersion = { "SaveGameVersion", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, SaveGameVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveGameVersion_MetaData), NewProp_SaveGameVersion_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap_ValueProp = { "TutorialProgressMap", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronTutorialProgressData, METADATA_PARAMS(0, nullptr) }; // 4224676143
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap_Key_KeyProp = { "TutorialProgressMap_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap = { "TutorialProgressMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, TutorialProgressMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TutorialProgressMap_MetaData), NewProp_TutorialProgressMap_MetaData) }; // 4224676143
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CompletedTutorials_Inner = { "CompletedTutorials", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CompletedTutorials = { "CompletedTutorials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, CompletedTutorials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedTutorials_MetaData), NewProp_CompletedTutorials_MetaData) };
void Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_bAIMentorEnabled_SetBit(void* Obj)
{
	((UAuracronTutorialSaveGame*)Obj)->bAIMentorEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_bAIMentorEnabled = { "bAIMentorEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronTutorialSaveGame), &Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_bAIMentorEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAIMentorEnabled_MetaData), NewProp_bAIMentorEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_AIMentorVolume = { "AIMentorVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, AIMentorVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIMentorVolume_MetaData), NewProp_AIMentorVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_AIMentorSpeechRate = { "AIMentorSpeechRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, AIMentorSpeechRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIMentorSpeechRate_MetaData), NewProp_AIMentorSpeechRate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_LastModified = { "LastModified", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, LastModified), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModified_MetaData), NewProp_LastModified_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_OnAllProgressReset = { "OnAllProgressReset", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTutorialSaveGame, OnAllProgressReset), Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAllProgressReset_MetaData), NewProp_OnAllProgressReset_MetaData) }; // 2958574084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_SaveSlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_UserIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_SaveGameVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_TutorialProgressMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CompletedTutorials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CompletedTutorials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_bAIMentorEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_AIMentorVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_AIMentorSpeechRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_LastModified,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::NewProp_OnAllProgressReset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USaveGame,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronTutorialBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::ClassParams = {
	&UAuracronTutorialSaveGame::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronTutorialSaveGame()
{
	if (!Z_Registration_Info_UClass_UAuracronTutorialSaveGame.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronTutorialSaveGame.OuterSingleton, Z_Construct_UClass_UAuracronTutorialSaveGame_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronTutorialSaveGame.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronTutorialSaveGame);
UAuracronTutorialSaveGame::~UAuracronTutorialSaveGame() {}
// ********** End Class UAuracronTutorialSaveGame **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTutorialProgressData::StaticStruct, Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics::NewStructOps, TEXT("AuracronTutorialProgressData"), &Z_Registration_Info_UScriptStruct_FAuracronTutorialProgressData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTutorialProgressData), 4224676143U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronTutorialSaveGame, UAuracronTutorialSaveGame::StaticClass, TEXT("UAuracronTutorialSaveGame"), &Z_Registration_Info_UClass_UAuracronTutorialSaveGame, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronTutorialSaveGame), 1233513583U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_3163509317(TEXT("/Script/AuracronTutorialBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h__Script_AuracronTutorialBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
