// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanPythonBindings.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanPythonBindings() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonAPIDoc();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonResult();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonScriptConfig();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPythonAPICategory ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPythonAPICategory;
static UEnum* EAuracronPythonAPICategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonAPICategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPythonAPICategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronPythonAPICategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronPythonAPICategory.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronPythonAPICategory>()
{
	return EAuracronPythonAPICategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All APIs" },
		{ "All.Name", "EAuracronPythonAPICategory::All" },
		{ "Animation.DisplayName", "Animation System" },
		{ "Animation.Name", "EAuracronPythonAPICategory::Animation" },
		{ "BlueprintType", "true" },
		{ "Clothing.DisplayName", "Clothing System" },
		{ "Clothing.Name", "EAuracronPythonAPICategory::Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python API Categories\n */" },
#endif
		{ "Core.DisplayName", "Core Framework" },
		{ "Core.Name", "EAuracronPythonAPICategory::Core" },
		{ "DNA.DisplayName", "DNA System" },
		{ "DNA.Name", "EAuracronPythonAPICategory::DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
		{ "Performance.DisplayName", "Performance System" },
		{ "Performance.Name", "EAuracronPythonAPICategory::Performance" },
		{ "Texture.DisplayName", "Texture System" },
		{ "Texture.Name", "EAuracronPythonAPICategory::Texture" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python API Categories" },
#endif
		{ "Utility.DisplayName", "Utility Functions" },
		{ "Utility.Name", "EAuracronPythonAPICategory::Utility" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPythonAPICategory::Core", (int64)EAuracronPythonAPICategory::Core },
		{ "EAuracronPythonAPICategory::DNA", (int64)EAuracronPythonAPICategory::DNA },
		{ "EAuracronPythonAPICategory::Animation", (int64)EAuracronPythonAPICategory::Animation },
		{ "EAuracronPythonAPICategory::Texture", (int64)EAuracronPythonAPICategory::Texture },
		{ "EAuracronPythonAPICategory::Performance", (int64)EAuracronPythonAPICategory::Performance },
		{ "EAuracronPythonAPICategory::Clothing", (int64)EAuracronPythonAPICategory::Clothing },
		{ "EAuracronPythonAPICategory::Utility", (int64)EAuracronPythonAPICategory::Utility },
		{ "EAuracronPythonAPICategory::All", (int64)EAuracronPythonAPICategory::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronPythonAPICategory",
	"EAuracronPythonAPICategory",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonAPICategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPythonAPICategory.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPythonAPICategory.InnerSingleton;
}
// ********** End Enum EAuracronPythonAPICategory **************************************************

// ********** Begin ScriptStruct FAuracronPythonResult *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonResult;
class UScriptStruct* FAuracronPythonResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronPythonResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Script Execution Result\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Script Execution Result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether the execution was successful */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the execution was successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Result message */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Result message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Output_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Python output */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python output" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorOutput_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Python error output */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python error output" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimeMS_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Execution time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execution time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Return value as JSON string */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return value as JSON string" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Output;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorOutput;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTimeMS;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPythonResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonResult), &Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_Output = { "Output", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonResult, Output), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Output_MetaData), NewProp_Output_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ErrorOutput = { "ErrorOutput", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonResult, ErrorOutput), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorOutput_MetaData), NewProp_ErrorOutput_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ExecutionTimeMS = { "ExecutionTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonResult, ExecutionTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimeMS_MetaData), NewProp_ExecutionTimeMS_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonResult, ReturnValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_Output,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ErrorOutput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ExecutionTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronPythonResult",
	Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::PropPointers),
	sizeof(FAuracronPythonResult),
	alignof(FAuracronPythonResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonResult ***********************************************

// ********** Begin ScriptStruct FAuracronPythonScriptConfig ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig;
class UScriptStruct* FAuracronPythonScriptConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonScriptConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronPythonScriptConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Script Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Script Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptName_MetaData[] = {
		{ "Category", "Script" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Script name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "Category", "Script" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Script content */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script content" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptFilePath_MetaData[] = {
		{ "Category", "Script" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Script file path (alternative to ScriptContent) */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script file path (alternative to ScriptContent)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Arguments_MetaData[] = {
		{ "Category", "Script" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Script arguments */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script arguments" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugOutput_MetaData[] = {
		{ "Category", "Script" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable debug output */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable debug output" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Script" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Script timeout in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script timeout in seconds" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptFilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Arguments_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Arguments_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Arguments;
	static void NewProp_bEnableDebugOutput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugOutput;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonScriptConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptName = { "ScriptName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonScriptConfig, ScriptName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptName_MetaData), NewProp_ScriptName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonScriptConfig, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptFilePath = { "ScriptFilePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonScriptConfig, ScriptFilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptFilePath_MetaData), NewProp_ScriptFilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments_ValueProp = { "Arguments", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments_Key_KeyProp = { "Arguments_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments = { "Arguments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonScriptConfig, Arguments), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Arguments_MetaData), NewProp_Arguments_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_bEnableDebugOutput_SetBit(void* Obj)
{
	((FAuracronPythonScriptConfig*)Obj)->bEnableDebugOutput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_bEnableDebugOutput = { "bEnableDebugOutput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonScriptConfig), &Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_bEnableDebugOutput_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugOutput_MetaData), NewProp_bEnableDebugOutput_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonScriptConfig, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_ScriptFilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_Arguments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_bEnableDebugOutput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewProp_TimeoutSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronPythonScriptConfig",
	Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::PropPointers),
	sizeof(FAuracronPythonScriptConfig),
	alignof(FAuracronPythonScriptConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonScriptConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonScriptConfig *****************************************

// ********** Begin ScriptStruct FAuracronPythonAPIDoc *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc;
class UScriptStruct* FAuracronPythonAPIDoc::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonAPIDoc, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronPythonAPIDoc"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python API Documentation Entry\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python API Documentation Entry" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Function name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Function description */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function description" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Signature_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Function signature */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function signature" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parameters documentation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameters documentation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Return value documentation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return value documentation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Examples_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usage examples */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usage examples" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Documentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** API category */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "API category" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Signature;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Examples_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Examples;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonAPIDoc>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Signature = { "Signature", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, Signature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Signature_MetaData), NewProp_Signature_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Parameters_Inner = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, Parameters), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, ReturnValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Examples_Inner = { "Examples", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Examples = { "Examples", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, Examples), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Examples_MetaData), NewProp_Examples_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonAPIDoc, Category), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 3553965557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Signature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Parameters_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_ReturnValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Examples_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Examples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronPythonAPIDoc",
	Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::PropPointers),
	sizeof(FAuracronPythonAPIDoc),
	alignof(FAuracronPythonAPIDoc),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonAPIDoc()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonAPIDoc ***********************************************

// ********** Begin Delegate FAuracronPythonScriptComplete *****************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms
	{
		FAuracronPythonResult Result;
		FString ScriptID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 3748611557
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::NewProp_ScriptID = { "ScriptID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms, ScriptID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptID_MetaData), NewProp_ScriptID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::NewProp_ScriptID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronPythonScriptComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronPythonScriptComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronPythonScriptComplete, FAuracronPythonResult const& Result, const FString& ScriptID)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms
	{
		FAuracronPythonResult Result;
		FString ScriptID;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronPythonScriptComplete_Parms Parms;
	Parms.Result=Result;
	Parms.ScriptID=ScriptID;
	AuracronPythonScriptComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronPythonScriptComplete *******************************************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ConvertPythonObjectToUE ********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics
{
	struct AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms
	{
		FString PythonVariableName;
		UClass* TargetClass;
		UObject* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Convert Python object to UE\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert Python object to UE" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonVariableName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonVariableName;
	static const UECodeGen_Private::FClassPropertyParams NewProp_TargetClass;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_PythonVariableName = { "PythonVariableName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms, PythonVariableName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonVariableName_MetaData), NewProp_PythonVariableName_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_TargetClass = { "TargetClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms, TargetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms, ReturnValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_PythonVariableName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_TargetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ConvertPythonObjectToUE", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::AuracronMetaHumanPythonBindings_eventConvertPythonObjectToUE_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execConvertPythonObjectToUE)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonVariableName);
	P_GET_OBJECT(UClass,Z_Param_TargetClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UObject**)Z_Param__Result=P_THIS->ConvertPythonObjectToUE(Z_Param_PythonVariableName,Z_Param_TargetClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ConvertPythonObjectToUE **********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ConvertUEObjectToPython ********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics
{
	struct AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms
	{
		UObject* Object;
		FString PythonVariableName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Convert UE object to Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert UE object to Python" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonVariableName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonVariableName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms, Object), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_PythonVariableName = { "PythonVariableName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms, PythonVariableName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonVariableName_MetaData), NewProp_PythonVariableName_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_Object,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_PythonVariableName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ConvertUEObjectToPython", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::AuracronMetaHumanPythonBindings_eventConvertUEObjectToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execConvertUEObjectToPython)
{
	P_GET_OBJECT(UObject,Z_Param_Object);
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonVariableName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConvertUEObjectToPython(Z_Param_Object,Z_Param_PythonVariableName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ConvertUEObjectToPython **********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function EnablePythonDebugging **********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics
{
	struct AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable Python debugging\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Python debugging" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "EnablePythonDebugging", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::AuracronMetaHumanPythonBindings_eventEnablePythonDebugging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execEnablePythonDebugging)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnablePythonDebugging(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function EnablePythonDebugging ************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function EvaluatePythonExpression *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics
{
	struct AuracronMetaHumanPythonBindings_eventEvaluatePythonExpression_Parms
	{
		FString Expression;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Evaluate Python expression\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evaluate Python expression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Expression_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Expression;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::NewProp_Expression = { "Expression", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventEvaluatePythonExpression_Parms, Expression), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Expression_MetaData), NewProp_Expression_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventEvaluatePythonExpression_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::NewProp_Expression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "EvaluatePythonExpression", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::AuracronMetaHumanPythonBindings_eventEvaluatePythonExpression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::AuracronMetaHumanPythonBindings_eventEvaluatePythonExpression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execEvaluatePythonExpression)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Expression);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->EvaluatePythonExpression(Z_Param_Expression);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function EvaluatePythonExpression *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ExecutePythonCode **************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics
{
	struct AuracronMetaHumanPythonBindings_eventExecutePythonCode_Parms
	{
		FString PythonCode;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python code string\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python code string" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonCode_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonCode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::NewProp_PythonCode = { "PythonCode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonCode_Parms, PythonCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonCode_MetaData), NewProp_PythonCode_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonCode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::NewProp_PythonCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ExecutePythonCode", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonCode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonCode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execExecutePythonCode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonCode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->ExecutePythonCode(Z_Param_PythonCode);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ExecutePythonCode ****************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ExecutePythonCodeAsync *********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics
{
	struct AuracronMetaHumanPythonBindings_eventExecutePythonCodeAsync_Parms
	{
		FString PythonCode;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python code asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python code asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonCode_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::NewProp_PythonCode = { "PythonCode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonCodeAsync_Parms, PythonCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonCode_MetaData), NewProp_PythonCode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonCodeAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::NewProp_PythonCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ExecutePythonCodeAsync", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonCodeAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonCodeAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execExecutePythonCodeAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonCode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecutePythonCodeAsync(Z_Param_PythonCode);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ExecutePythonCodeAsync ***********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ExecutePythonFile **************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics
{
	struct AuracronMetaHumanPythonBindings_eventExecutePythonFile_Parms
	{
		FString FilePath;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python file\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python file" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ExecutePythonFile", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execExecutePythonFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->ExecutePythonFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ExecutePythonFile ****************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ExecutePythonScript ************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics
{
	struct AuracronMetaHumanPythonBindings_eventExecutePythonScript_Parms
	{
		FAuracronPythonScriptConfig Config;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python script\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python script" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonScript_Parms, Config), Z_Construct_UScriptStruct_FAuracronPythonScriptConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 3346775910
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ExecutePythonScript", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execExecutePythonScript)
{
	P_GET_STRUCT_REF(FAuracronPythonScriptConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->ExecutePythonScript(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ExecutePythonScript **************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ExecutePythonScriptAsync *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics
{
	struct AuracronMetaHumanPythonBindings_eventExecutePythonScriptAsync_Parms
	{
		FAuracronPythonScriptConfig Config;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Execute Python script asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute Python script asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonScriptAsync_Parms, Config), Z_Construct_UScriptStruct_FAuracronPythonScriptConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 3346775910
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventExecutePythonScriptAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ExecutePythonScriptAsync", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonScriptAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::AuracronMetaHumanPythonBindings_eventExecutePythonScriptAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execExecutePythonScriptAsync)
{
	P_GET_STRUCT_REF(FAuracronPythonScriptConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecutePythonScriptAsync(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ExecutePythonScriptAsync *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetAPIDocumentation ************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetAPIDocumentation_Parms
	{
		EAuracronPythonAPICategory Category;
		TArray<FAuracronPythonAPIDoc> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Bindings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get API documentation\n     */" },
#endif
		{ "CPP_Default_Category", "All" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get API documentation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAPIDocumentation_Parms, Category), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory, METADATA_PARAMS(0, nullptr) }; // 3553965557
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPythonAPIDoc, METADATA_PARAMS(0, nullptr) }; // 2051204464
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAPIDocumentation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2051204464
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetAPIDocumentation", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::AuracronMetaHumanPythonBindings_eventGetAPIDocumentation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::AuracronMetaHumanPythonBindings_eventGetAPIDocumentation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetAPIDocumentation)
{
	P_GET_ENUM(EAuracronPythonAPICategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPythonAPIDoc>*)Z_Param__Result=P_THIS->GetAPIDocumentation(EAuracronPythonAPICategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetAPIDocumentation **************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetAsyncExecutionResult ********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetAsyncExecutionResult_Parms
	{
		FString ScriptID;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get async execution result\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get async execution result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::NewProp_ScriptID = { "ScriptID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAsyncExecutionResult_Parms, ScriptID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptID_MetaData), NewProp_ScriptID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAsyncExecutionResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::NewProp_ScriptID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetAsyncExecutionResult", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::AuracronMetaHumanPythonBindings_eventGetAsyncExecutionResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::AuracronMetaHumanPythonBindings_eventGetAsyncExecutionResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetAsyncExecutionResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->GetAsyncExecutionResult(Z_Param_ScriptID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetAsyncExecutionResult **********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetAvailableAPIFunctions *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetAvailableAPIFunctions_Parms
	{
		EAuracronPythonAPICategory Category;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Bindings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get available API functions\n     */" },
#endif
		{ "CPP_Default_Category", "All" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get available API functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAvailableAPIFunctions_Parms, Category), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory, METADATA_PARAMS(0, nullptr) }; // 3553965557
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetAvailableAPIFunctions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetAvailableAPIFunctions", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::AuracronMetaHumanPythonBindings_eventGetAvailableAPIFunctions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::AuracronMetaHumanPythonBindings_eventGetAvailableAPIFunctions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetAvailableAPIFunctions)
{
	P_GET_ENUM(EAuracronPythonAPICategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAvailableAPIFunctions(EAuracronPythonAPICategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetAvailableAPIFunctions *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetPythonExecutionStatistics ***
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetPythonExecutionStatistics_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python execution statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python execution statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonExecutionStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetPythonExecutionStatistics", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::AuracronMetaHumanPythonBindings_eventGetPythonExecutionStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::AuracronMetaHumanPythonBindings_eventGetPythonExecutionStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetPythonExecutionStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPythonExecutionStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetPythonExecutionStatistics *****

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetPythonScriptTemplates *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetPythonScriptTemplates_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Scripts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python script templates\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python script templates" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonScriptTemplates_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetPythonScriptTemplates", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::AuracronMetaHumanPythonBindings_eventGetPythonScriptTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::AuracronMetaHumanPythonBindings_eventGetPythonScriptTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetPythonScriptTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetPythonScriptTemplates();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetPythonScriptTemplates *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetPythonSystemInfo ************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetPythonSystemInfo_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python system information\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python system information" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonSystemInfo_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetPythonSystemInfo", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::AuracronMetaHumanPythonBindings_eventGetPythonSystemInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::AuracronMetaHumanPythonBindings_eventGetPythonSystemInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetPythonSystemInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetPythonSystemInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetPythonSystemInfo **************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetPythonVariable **************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetPythonVariable_Parms
	{
		FString VariableName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python variable\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python variable" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariableName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariableName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::NewProp_VariableName = { "VariableName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonVariable_Parms, VariableName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariableName_MetaData), NewProp_VariableName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonVariable_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::NewProp_VariableName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetPythonVariable", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::AuracronMetaHumanPythonBindings_eventGetPythonVariable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::AuracronMetaHumanPythonBindings_eventGetPythonVariable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetPythonVariable)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VariableName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPythonVariable(Z_Param_VariableName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetPythonVariable ****************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function GetPythonVersion ***************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics
{
	struct AuracronMetaHumanPythonBindings_eventGetPythonVersion_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get Python version\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Python version" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventGetPythonVersion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "GetPythonVersion", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::AuracronMetaHumanPythonBindings_eventGetPythonVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::AuracronMetaHumanPythonBindings_eventGetPythonVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execGetPythonVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPythonVersion();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function GetPythonVersion *****************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ImportPythonModule *************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics
{
	struct AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Import Python module\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Import Python module" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ImportPythonModule", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::AuracronMetaHumanPythonBindings_eventImportPythonModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execImportPythonModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportPythonModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ImportPythonModule ***************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function Initialize *********************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics
{
	struct AuracronMetaHumanPythonBindings_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize Python bindings\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Python bindings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::AuracronMetaHumanPythonBindings_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::AuracronMetaHumanPythonBindings_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function Initialize ***********************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function InitializePythonEnvironment ****
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics
{
	struct AuracronMetaHumanPythonBindings_eventInitializePythonEnvironment_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize Python environment\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Python environment" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventInitializePythonEnvironment_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventInitializePythonEnvironment_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "InitializePythonEnvironment", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::AuracronMetaHumanPythonBindings_eventInitializePythonEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::AuracronMetaHumanPythonBindings_eventInitializePythonEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execInitializePythonEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function InitializePythonEnvironment ******

// ********** Begin Class UAuracronMetaHumanPythonBindings Function InstallPythonPackage ***********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics
{
	struct AuracronMetaHumanPythonBindings_eventInstallPythonPackage_Parms
	{
		FString PackageName;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Install Python package\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Install Python package" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PackageName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PackageName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::NewProp_PackageName = { "PackageName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventInstallPythonPackage_Parms, PackageName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PackageName_MetaData), NewProp_PackageName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventInstallPythonPackage_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::NewProp_PackageName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "InstallPythonPackage", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::AuracronMetaHumanPythonBindings_eventInstallPythonPackage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::AuracronMetaHumanPythonBindings_eventInstallPythonPackage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execInstallPythonPackage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PackageName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->InstallPythonPackage(Z_Param_PackageName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function InstallPythonPackage *************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function IsAsyncExecutionComplete *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics
{
	struct AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms
	{
		FString ScriptID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if async execution is complete\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if async execution is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ScriptID = { "ScriptID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms, ScriptID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptID_MetaData), NewProp_ScriptID_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ScriptID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "IsAsyncExecutionComplete", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::AuracronMetaHumanPythonBindings_eventIsAsyncExecutionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execIsAsyncExecutionComplete)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncExecutionComplete(Z_Param_ScriptID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function IsAsyncExecutionComplete *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics
{
	struct AuracronMetaHumanPythonBindings_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if Python bindings are initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if Python bindings are initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::AuracronMetaHumanPythonBindings_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::AuracronMetaHumanPythonBindings_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function IsInitialized ********************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ListInstalledPythonPackages ****
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics
{
	struct AuracronMetaHumanPythonBindings_eventListInstalledPythonPackages_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * List installed Python packages\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "List installed Python packages" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventListInstalledPythonPackages_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ListInstalledPythonPackages", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::AuracronMetaHumanPythonBindings_eventListInstalledPythonPackages_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::AuracronMetaHumanPythonBindings_eventListInstalledPythonPackages_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execListInstalledPythonPackages)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->ListInstalledPythonPackages();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ListInstalledPythonPackages ******

// ********** Begin Class UAuracronMetaHumanPythonBindings Function LoadPythonScriptFromFile *******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics
{
	struct AuracronMetaHumanPythonBindings_eventLoadPythonScriptFromFile_Parms
	{
		FString FilePath;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Scripts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Load Python script from file\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Load Python script from file" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventLoadPythonScriptFromFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventLoadPythonScriptFromFile_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "LoadPythonScriptFromFile", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::AuracronMetaHumanPythonBindings_eventLoadPythonScriptFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::AuracronMetaHumanPythonBindings_eventLoadPythonScriptFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execLoadPythonScriptFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->LoadPythonScriptFromFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function LoadPythonScriptFromFile *********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ProfilePythonScript ************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics
{
	struct AuracronMetaHumanPythonBindings_eventProfilePythonScript_Parms
	{
		FAuracronPythonScriptConfig Config;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Profile Python script execution\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profile Python script execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventProfilePythonScript_Parms, Config), Z_Construct_UScriptStruct_FAuracronPythonScriptConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 3346775910
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventProfilePythonScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ProfilePythonScript", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::AuracronMetaHumanPythonBindings_eventProfilePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::AuracronMetaHumanPythonBindings_eventProfilePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execProfilePythonScript)
{
	P_GET_STRUCT_REF(FAuracronPythonScriptConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->ProfilePythonScript(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ProfilePythonScript **************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function RegisterMetaHumanAPIs **********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics
{
	struct AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms
	{
		EAuracronPythonAPICategory Category;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Bindings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Register MetaHuman Framework APIs with Python\n     */" },
#endif
		{ "CPP_Default_Category", "All" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Register MetaHuman Framework APIs with Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms, Category), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronPythonAPICategory, METADATA_PARAMS(0, nullptr) }; // 3553965557
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "RegisterMetaHumanAPIs", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::AuracronMetaHumanPythonBindings_eventRegisterMetaHumanAPIs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execRegisterMetaHumanAPIs)
{
	P_GET_ENUM(EAuracronPythonAPICategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterMetaHumanAPIs(EAuracronPythonAPICategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function RegisterMetaHumanAPIs ************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function SavePythonScriptToFile *********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics
{
	struct AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms
	{
		FString ScriptContent;
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Scripts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Save Python script to file\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save Python script to file" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "SavePythonScriptToFile", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::AuracronMetaHumanPythonBindings_eventSavePythonScriptToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execSavePythonScriptToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SavePythonScriptToFile(Z_Param_ScriptContent,Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function SavePythonScriptToFile ***********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function SetPythonBreakpoint ************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics
{
	struct AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms
	{
		FString FilePath;
		int32 LineNumber;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Python breakpoint\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Python breakpoint" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LineNumber;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_LineNumber = { "LineNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms, LineNumber), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_LineNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "SetPythonBreakpoint", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::AuracronMetaHumanPythonBindings_eventSetPythonBreakpoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execSetPythonBreakpoint)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_GET_PROPERTY(FIntProperty,Z_Param_LineNumber);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetPythonBreakpoint(Z_Param_FilePath,Z_Param_LineNumber);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function SetPythonBreakpoint **************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function SetPythonVariable **************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics
{
	struct AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms
	{
		FString VariableName;
		FString Value;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set Python variable\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set Python variable" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariableName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariableName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Value;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_VariableName = { "VariableName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms, VariableName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariableName_MetaData), NewProp_VariableName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms), &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_VariableName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "SetPythonVariable", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::AuracronMetaHumanPythonBindings_eventSetPythonVariable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execSetPythonVariable)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VariableName);
	P_GET_PROPERTY(FStrProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetPythonVariable(Z_Param_VariableName,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function SetPythonVariable ****************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function Shutdown ***********************
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown Python bindings\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown Python bindings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function Shutdown *************************

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ShutdownPythonEnvironment ******
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown Python environment\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown Python environment" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ShutdownPythonEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execShutdownPythonEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPythonEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ShutdownPythonEnvironment ********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function UnregisterMetaHumanAPIs ********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Bindings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Unregister MetaHuman Framework APIs from Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unregister MetaHuman Framework APIs from Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "UnregisterMetaHumanAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execUnregisterMetaHumanAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterMetaHumanAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function UnregisterMetaHumanAPIs **********

// ********** Begin Class UAuracronMetaHumanPythonBindings Function ValidatePythonScript ***********
struct Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics
{
	struct AuracronMetaHumanPythonBindings_eventValidatePythonScript_Parms
	{
		FString ScriptContent;
		FAuracronPythonResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Python|Scripts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate Python script syntax\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate Python script syntax" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventValidatePythonScript_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanPythonBindings_eventValidatePythonScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonResult, METADATA_PARAMS(0, nullptr) }; // 3748611557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanPythonBindings, nullptr, "ValidatePythonScript", Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::AuracronMetaHumanPythonBindings_eventValidatePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::AuracronMetaHumanPythonBindings_eventValidatePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanPythonBindings::execValidatePythonScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonResult*)Z_Param__Result=P_THIS->ValidatePythonScript(Z_Param_ScriptContent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanPythonBindings Function ValidatePythonScript *************

// ********** Begin Class UAuracronMetaHumanPythonBindings *****************************************
void UAuracronMetaHumanPythonBindings::StaticRegisterNativesUAuracronMetaHumanPythonBindings()
{
	UClass* Class = UAuracronMetaHumanPythonBindings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ConvertPythonObjectToUE", &UAuracronMetaHumanPythonBindings::execConvertPythonObjectToUE },
		{ "ConvertUEObjectToPython", &UAuracronMetaHumanPythonBindings::execConvertUEObjectToPython },
		{ "EnablePythonDebugging", &UAuracronMetaHumanPythonBindings::execEnablePythonDebugging },
		{ "EvaluatePythonExpression", &UAuracronMetaHumanPythonBindings::execEvaluatePythonExpression },
		{ "ExecutePythonCode", &UAuracronMetaHumanPythonBindings::execExecutePythonCode },
		{ "ExecutePythonCodeAsync", &UAuracronMetaHumanPythonBindings::execExecutePythonCodeAsync },
		{ "ExecutePythonFile", &UAuracronMetaHumanPythonBindings::execExecutePythonFile },
		{ "ExecutePythonScript", &UAuracronMetaHumanPythonBindings::execExecutePythonScript },
		{ "ExecutePythonScriptAsync", &UAuracronMetaHumanPythonBindings::execExecutePythonScriptAsync },
		{ "GetAPIDocumentation", &UAuracronMetaHumanPythonBindings::execGetAPIDocumentation },
		{ "GetAsyncExecutionResult", &UAuracronMetaHumanPythonBindings::execGetAsyncExecutionResult },
		{ "GetAvailableAPIFunctions", &UAuracronMetaHumanPythonBindings::execGetAvailableAPIFunctions },
		{ "GetPythonExecutionStatistics", &UAuracronMetaHumanPythonBindings::execGetPythonExecutionStatistics },
		{ "GetPythonScriptTemplates", &UAuracronMetaHumanPythonBindings::execGetPythonScriptTemplates },
		{ "GetPythonSystemInfo", &UAuracronMetaHumanPythonBindings::execGetPythonSystemInfo },
		{ "GetPythonVariable", &UAuracronMetaHumanPythonBindings::execGetPythonVariable },
		{ "GetPythonVersion", &UAuracronMetaHumanPythonBindings::execGetPythonVersion },
		{ "ImportPythonModule", &UAuracronMetaHumanPythonBindings::execImportPythonModule },
		{ "Initialize", &UAuracronMetaHumanPythonBindings::execInitialize },
		{ "InitializePythonEnvironment", &UAuracronMetaHumanPythonBindings::execInitializePythonEnvironment },
		{ "InstallPythonPackage", &UAuracronMetaHumanPythonBindings::execInstallPythonPackage },
		{ "IsAsyncExecutionComplete", &UAuracronMetaHumanPythonBindings::execIsAsyncExecutionComplete },
		{ "IsInitialized", &UAuracronMetaHumanPythonBindings::execIsInitialized },
		{ "ListInstalledPythonPackages", &UAuracronMetaHumanPythonBindings::execListInstalledPythonPackages },
		{ "LoadPythonScriptFromFile", &UAuracronMetaHumanPythonBindings::execLoadPythonScriptFromFile },
		{ "ProfilePythonScript", &UAuracronMetaHumanPythonBindings::execProfilePythonScript },
		{ "RegisterMetaHumanAPIs", &UAuracronMetaHumanPythonBindings::execRegisterMetaHumanAPIs },
		{ "SavePythonScriptToFile", &UAuracronMetaHumanPythonBindings::execSavePythonScriptToFile },
		{ "SetPythonBreakpoint", &UAuracronMetaHumanPythonBindings::execSetPythonBreakpoint },
		{ "SetPythonVariable", &UAuracronMetaHumanPythonBindings::execSetPythonVariable },
		{ "Shutdown", &UAuracronMetaHumanPythonBindings::execShutdown },
		{ "ShutdownPythonEnvironment", &UAuracronMetaHumanPythonBindings::execShutdownPythonEnvironment },
		{ "UnregisterMetaHumanAPIs", &UAuracronMetaHumanPythonBindings::execUnregisterMetaHumanAPIs },
		{ "ValidatePythonScript", &UAuracronMetaHumanPythonBindings::execValidatePythonScript },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings;
UClass* UAuracronMetaHumanPythonBindings::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanPythonBindings;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanPythonBindings"),
			Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanPythonBindings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister()
{
	return UAuracronMetaHumanPythonBindings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman Python Bindings\n * Comprehensive Python integration for MetaHuman Framework\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanPythonBindings.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman Python Bindings\nComprehensive Python integration for MetaHuman Framework" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPythonScriptComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Python|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when an async Python script execution completes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when an async Python script execution completes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPythonEnvironmentInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPythonScriptComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bPythonEnvironmentInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPythonEnvironmentInitialized;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertPythonObjectToUE, "ConvertPythonObjectToUE" }, // 822514025
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ConvertUEObjectToPython, "ConvertUEObjectToPython" }, // 2666408504
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EnablePythonDebugging, "EnablePythonDebugging" }, // 1024121766
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_EvaluatePythonExpression, "EvaluatePythonExpression" }, // 2552314805
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCode, "ExecutePythonCode" }, // 3248225437
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonCodeAsync, "ExecutePythonCodeAsync" }, // 3127167980
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonFile, "ExecutePythonFile" }, // 817350152
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScript, "ExecutePythonScript" }, // 2910333472
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ExecutePythonScriptAsync, "ExecutePythonScriptAsync" }, // 2742584637
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAPIDocumentation, "GetAPIDocumentation" }, // 1438745725
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAsyncExecutionResult, "GetAsyncExecutionResult" }, // 764920369
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetAvailableAPIFunctions, "GetAvailableAPIFunctions" }, // 1463543282
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonExecutionStatistics, "GetPythonExecutionStatistics" }, // 1476925049
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonScriptTemplates, "GetPythonScriptTemplates" }, // 3721054935
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonSystemInfo, "GetPythonSystemInfo" }, // 1792739770
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVariable, "GetPythonVariable" }, // 3957130044
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_GetPythonVersion, "GetPythonVersion" }, // 3418211562
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ImportPythonModule, "ImportPythonModule" }, // 2161790472
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Initialize, "Initialize" }, // 3931335304
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InitializePythonEnvironment, "InitializePythonEnvironment" }, // 1651928064
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_InstallPythonPackage, "InstallPythonPackage" }, // 756512540
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsAsyncExecutionComplete, "IsAsyncExecutionComplete" }, // 988489651
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_IsInitialized, "IsInitialized" }, // 3855257753
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ListInstalledPythonPackages, "ListInstalledPythonPackages" }, // 1131357426
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_LoadPythonScriptFromFile, "LoadPythonScriptFromFile" }, // 1688482701
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ProfilePythonScript, "ProfilePythonScript" }, // 3500655990
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_RegisterMetaHumanAPIs, "RegisterMetaHumanAPIs" }, // 4194480748
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SavePythonScriptToFile, "SavePythonScriptToFile" }, // 174124714
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonBreakpoint, "SetPythonBreakpoint" }, // 489235449
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_SetPythonVariable, "SetPythonVariable" }, // 2787933793
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_Shutdown, "Shutdown" }, // 3019771481
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ShutdownPythonEnvironment, "ShutdownPythonEnvironment" }, // 660766255
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_UnregisterMetaHumanAPIs, "UnregisterMetaHumanAPIs" }, // 1465755904
		{ &Z_Construct_UFunction_UAuracronMetaHumanPythonBindings_ValidatePythonScript, "ValidatePythonScript" }, // 972479471
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanPythonBindings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_OnPythonScriptComplete = { "OnPythonScriptComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPythonBindings, OnPythonScriptComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronPythonScriptComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPythonScriptComplete_MetaData), NewProp_OnPythonScriptComplete_MetaData) }; // 2165348922
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPythonBindings, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanPythonBindings*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanPythonBindings), &Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bPythonEnvironmentInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanPythonBindings*)Obj)->bPythonEnvironmentInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bPythonEnvironmentInitialized = { "bPythonEnvironmentInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanPythonBindings), &Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bPythonEnvironmentInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPythonEnvironmentInitialized_MetaData), NewProp_bPythonEnvironmentInitialized_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanPythonBindings, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_OnPythonScriptComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_bPythonEnvironmentInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::ClassParams = {
	&UAuracronMetaHumanPythonBindings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanPythonBindings);
// ********** End Class UAuracronMetaHumanPythonBindings *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPythonAPICategory_StaticEnum, TEXT("EAuracronPythonAPICategory"), &Z_Registration_Info_UEnum_EAuracronPythonAPICategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3553965557U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPythonResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonResult_Statics::NewStructOps, TEXT("AuracronPythonResult"), &Z_Registration_Info_UScriptStruct_FAuracronPythonResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonResult), 3748611557U) },
		{ FAuracronPythonScriptConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics::NewStructOps, TEXT("AuracronPythonScriptConfig"), &Z_Registration_Info_UScriptStruct_FAuracronPythonScriptConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonScriptConfig), 3346775910U) },
		{ FAuracronPythonAPIDoc::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics::NewStructOps, TEXT("AuracronPythonAPIDoc"), &Z_Registration_Info_UScriptStruct_FAuracronPythonAPIDoc, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonAPIDoc), 2051204464U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanPythonBindings, UAuracronMetaHumanPythonBindings::StaticClass, TEXT("UAuracronMetaHumanPythonBindings"), &Z_Registration_Info_UClass_UAuracronMetaHumanPythonBindings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanPythonBindings), 3298764764U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_3528761089(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
