// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Comunicação por Voz Bridge Implementation

#include "AuracronVoiceBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Voice.h"
#include "VoiceChat.h"
#include "OnlineSubsystem.h"
#include "Interfaces/VoiceInterface.h"
#include "AudioMixerBlueprintLibrary.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronVoiceBridge::UAuracronVoiceBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para voice chat responsivo
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de voz
    VoiceConfiguration.bUseVoiceChat = true;
    VoiceConfiguration.VoiceQuality = EAuracronVoiceQuality::High;
    VoiceConfiguration.InputVolume = 1.0f;
    VoiceConfiguration.OutputVolume = 1.0f;
    VoiceConfiguration.bUsePushToTalk = false;
    VoiceConfiguration.bUseVoiceActivityDetection = true;
    VoiceConfiguration.VoiceActivityThreshold = 0.3f;
    VoiceConfiguration.bUseNoiseSuppression = true;
    VoiceConfiguration.NoiseSuppressionIntensity = 0.7f;
    VoiceConfiguration.bUseEchoCancellation = true;
    VoiceConfiguration.bUseAudioCompression = true;
    VoiceConfiguration.BitRate = 64;
    VoiceConfiguration.bUse3DVoice = true;
    VoiceConfiguration.ProximityDistance = 1500.0f;
    VoiceConfiguration.bUseDistanceAttenuation = true;
    VoiceConfiguration.bUseVoiceFilters = true;
    VoiceConfiguration.bUseVoiceModulation = false;
    VoiceConfiguration.VoiceModulationType = TEXT("None");
    VoiceConfiguration.bUseVoiceRecording = false;
    VoiceConfiguration.MaxRecordingDuration = 30.0f;
    
    CurrentVoiceState = EAuracronVoiceState::Disconnected;
}

void UAuracronVoiceBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Voice Chat"));

    // Inicializar sistema
    bSystemInitialized = InitializeVoiceSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timer para atualizações
        GetWorld()->GetTimerManager().SetTimer(
            VoiceUpdateTimer,
            [this]()
            {
                ProcessVoiceData(0.1f);
                UpdateParticipants(0.1f);
            },
            0.1f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Voice Chat inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Voice Chat"));
    }
}

void UAuracronVoiceBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Desconectar do voice chat
    DisconnectFromVoiceChat();
    
    // Limpar canais
    ActiveVoiceChannels.Empty();
    
    // Limpar participantes
    ConnectedParticipants.Empty();

    // Limpar timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(VoiceUpdateTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronVoiceBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronVoiceBridge, VoiceConfiguration);
    DOREPLIFETIME(UAuracronVoiceBridge, ActiveVoiceChannels);
    DOREPLIFETIME(UAuracronVoiceBridge, ConnectedParticipants);
    DOREPLIFETIME(UAuracronVoiceBridge, CurrentVoiceState);
}

void UAuracronVoiceBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar dados de voz
    ProcessVoiceData(DeltaTime);
    
    // Atualizar participantes
    UpdateParticipants(DeltaTime);
}

// === Core Voice Management ===

bool UAuracronVoiceBridge::InitializeVoiceChat()
{
    if (!VoiceConfiguration.bUseVoiceChat)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Voice chat desabilitado na configuração"));
        return false;
    }

    // Obter interface de voice chat
    if (IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get())
    {
        IVoiceInterface* VoiceInterface = OnlineSubsystem->GetVoiceInterface();
        if (VoiceInterface)
        {
            // Inicializar voice interface
            if (VoiceInterface->Init())
            {
                CurrentVoiceState = EAuracronVoiceState::Disconnected;
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Voice interface inicializada"));
                return true;
            }
        }
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar voice interface"));
    CurrentVoiceState = EAuracronVoiceState::Error;
    return false;
}

bool UAuracronVoiceBridge::ConnectToVoiceChat()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (CurrentVoiceState == EAuracronVoiceState::Connected || CurrentVoiceState == EAuracronVoiceState::Connecting)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Já conectado ou conectando ao voice chat"));
        return true;
    }

    CurrentVoiceState = EAuracronVoiceState::Connecting;

    // Simular conexão (em implementação real, usar Vivox ou EOS Voice)
    GetWorld()->GetTimerManager().SetTimer(
        FTimerHandle(),
        [this]()
        {
            CurrentVoiceState = EAuracronVoiceState::Connected;
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Conectado ao voice chat"));
        },
        1.0f,
        false
    );

    return true;
}

bool UAuracronVoiceBridge::DisconnectFromVoiceChat()
{
    if (CurrentVoiceState == EAuracronVoiceState::Disconnected)
    {
        return true;
    }

    // Sair de todos os canais
    for (const FAuracronVoiceChannel& Channel : ActiveVoiceChannels)
    {
        LeaveVoiceChannel(Channel.ChannelID);
    }

    CurrentVoiceState = EAuracronVoiceState::Disconnected;
    ConnectedParticipants.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Desconectado do voice chat"));

    return true;
}

bool UAuracronVoiceBridge::MuteMicrophone(bool bMute)
{
    if (!bSystemInitialized || CurrentVoiceState != EAuracronVoiceState::Connected)
    {
        return false;
    }

    // Implementar mute do microfone
    if (IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get())
    {
        IVoiceInterface* VoiceInterface = OnlineSubsystem->GetVoiceInterface();
        if (VoiceInterface)
        {
            VoiceInterface->MuteRemoteTalker(0, bMute); // Implementação simplificada
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Microfone %s"), bMute ? TEXT("mutado") : TEXT("desmutado"));

    return true;
}

bool UAuracronVoiceBridge::DeafenAudio(bool bDeafen)
{
    if (!bSystemInitialized || CurrentVoiceState != EAuracronVoiceState::Connected)
    {
        return false;
    }

    // Implementar deafen
    float TargetVolume = bDeafen ? 0.0f : VoiceConfiguration.OutputVolume;
    
    for (FAuracronVoiceParticipant& Participant : ConnectedParticipants)
    {
        Participant.bIsDeafened = bDeafen;
        if (bDeafen)
        {
            SetParticipantVolume(Participant.PlayerID, 0.0f);
        }
        else
        {
            SetParticipantVolume(Participant.PlayerID, Participant.ParticipantVolume);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Áudio %s"), bDeafen ? TEXT("ensurdecido") : TEXT("restaurado"));

    return true;
}

// === Channel Management ===

bool UAuracronVoiceBridge::CreateVoiceChannel(const FAuracronVoiceChannel& ChannelConfig)
{
    if (!bSystemInitialized || ChannelConfig.ChannelID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&VoiceMutex);

    // Verificar se canal já existe
    for (const FAuracronVoiceChannel& ExistingChannel : ActiveVoiceChannels)
    {
        if (ExistingChannel.ChannelID == ChannelConfig.ChannelID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Canal de voz já existe: %s"), *ChannelConfig.ChannelID);
            return false;
        }
    }

    // Criar novo canal
    FAuracronVoiceChannel NewChannel = ChannelConfig;
    NewChannel.bIsActive = true;
    NewChannel.CreationTime = FDateTime::Now();
    NewChannel.LastActivity = FDateTime::Now();

    ActiveVoiceChannels.Add(NewChannel);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Canal de voz criado: %s (%s)"), *NewChannel.ChannelName, *UEnum::GetValueAsString(NewChannel.ChannelType));

    return true;
}

bool UAuracronVoiceBridge::JoinVoiceChannel(const FString& ChannelID)
{
    if (!bSystemInitialized || ChannelID.IsEmpty() || CurrentVoiceState != EAuracronVoiceState::Connected)
    {
        return false;
    }

    FScopeLock Lock(&VoiceMutex);

    // Encontrar canal
    FAuracronVoiceChannel* Channel = nullptr;
    for (FAuracronVoiceChannel& ExistingChannel : ActiveVoiceChannels)
    {
        if (ExistingChannel.ChannelID == ChannelID)
        {
            Channel = &ExistingChannel;
            break;
        }
    }

    if (!Channel)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Canal de voz não encontrado: %s"), *ChannelID);
        return false;
    }

    // Verificar permissões
    if (Channel->bRequiresPermission)
    {
        // Verificar se o jogador tem permissão para entrar no canal
        if (APlayerController* PlayerController = GetWorld()->GetFirstPlayerController())
        {
            // Verificar permissões baseadas no tipo de canal
            switch (Channel->ChannelType)
            {
                case EAuracronVoiceChannelType::Team:
                    // Verificar se está na mesma equipe
                    if (!IsPlayerInSameTeam(PlayerController))
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não está na equipe para canal: %s"), *ChannelID);
                        return false;
                    }
                    break;
                    
                case EAuracronVoiceChannelType::Guild:
                    // Verificar se está na mesma guilda
                    if (!IsPlayerInSameGuild(PlayerController))
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não está na guilda para canal: %s"), *ChannelID);
                        return false;
                    }
                    break;
                    
                case EAuracronVoiceChannelType::Private:
                    // Verificar se foi convidado para canal privado
                    if (!IsPlayerInvitedToChannel(PlayerController, ChannelID))
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não foi convidado para canal privado: %s"), *ChannelID);
                        return false;
                    }
                    break;
                    
                case EAuracronVoiceChannelType::Public:
                default:
                    // Canal público - sem restrições adicionais
                    break;
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: PlayerController não encontrado para verificação de permissões"));
            return false;
        }
    }

    // Adicionar jogador ao canal
    FString PlayerID = TEXT("LocalPlayer"); // Em produção, obter do sistema de autenticação
    if (!Channel->PlayersInChannel.Contains(PlayerID))
    {
        Channel->PlayersInChannel.Add(PlayerID);
        Channel->LastActivity = FDateTime::Now();

        // Criar participante
        FAuracronVoiceParticipant NewParticipant;
        NewParticipant.PlayerID = PlayerID;
        NewParticipant.PlayerName = TEXT("Local Player");
        NewParticipant.VoiceState = EAuracronVoiceState::Connected;
        NewParticipant.ConnectedTime = FDateTime::Now();
        NewParticipant.ActiveChannels.Add(ChannelID);

        ConnectedParticipants.Add(NewParticipant);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Entrou no canal de voz: %s"), *Channel->ChannelName);

        // Broadcast evento
        OnParticipantJoined.Broadcast(ChannelID, NewParticipant);

        return true;
    }

    return false;
}

bool UAuracronVoiceBridge::LeaveVoiceChannel(const FString& ChannelID)
{
    if (!bSystemInitialized || ChannelID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&VoiceMutex);

    // Encontrar canal
    FAuracronVoiceChannel* Channel = nullptr;
    for (FAuracronVoiceChannel& ExistingChannel : ActiveVoiceChannels)
    {
        if (ExistingChannel.ChannelID == ChannelID)
        {
            Channel = &ExistingChannel;
            break;
        }
    }

    if (!Channel)
    {
        return false;
    }

    FString PlayerID = TEXT("LocalPlayer");

    // Remover jogador do canal
    Channel->PlayersInChannel.Remove(PlayerID);

    // Remover participante
    ConnectedParticipants.RemoveAll([&](const FAuracronVoiceParticipant& Participant)
    {
        return Participant.PlayerID == PlayerID && Participant.ActiveChannels.Contains(ChannelID);
    });

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Saiu do canal de voz: %s"), *Channel->ChannelName);

    // Broadcast evento
    OnParticipantLeft.Broadcast(ChannelID, PlayerID);

    return true;
}

// === Internal Methods ===

bool UAuracronVoiceBridge::InitializeVoiceSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar voice chat
    if (!SetupVoiceChat())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar voice chat"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de voz inicializado"));

    return true;
}

bool UAuracronVoiceBridge::SetupVoiceChat()
{
    if (!VoiceConfiguration.bUseVoiceChat)
    {
        return true;
    }

    // Configurar interface de voice chat
    // Em produção, usar Vivox ou EOS Voice
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Voice chat configurado"));

    return true;
}

void UAuracronVoiceBridge::ProcessVoiceData(float DeltaTime)
{
    // Processar dados de voz em tempo real
    for (FAuracronVoiceParticipant& Participant : ConnectedParticipants)
    {
        // Simular detecção de fala
        if (FMath::RandRange(0.0f, 1.0f) < 0.1f) // 10% chance por frame
        {
            if (!Participant.bIsSpeaking)
            {
                Participant.bIsSpeaking = true;
                Participant.LastSpokeTime = FDateTime::Now();
                OnParticipantStartedSpeaking.Broadcast(Participant.PlayerID);
            }
        }
        else if (Participant.bIsSpeaking)
        {
            Participant.bIsSpeaking = false;
            OnParticipantStoppedSpeaking.Broadcast(Participant.PlayerID);
        }

        // Atualizar qualidade da conexão
        Participant.ConnectionQuality = FMath::RandRange(0.8f, 1.0f);
        Participant.VoiceLatency = FMath::RandRange(20.0f, 100.0f);
    }
}

void UAuracronVoiceBridge::UpdateParticipants(float DeltaTime)
{
    FScopeLock Lock(&VoiceMutex);

    // Atualizar posições 3D para proximity chat
    if (VoiceConfiguration.bUse3DVoice)
    {
        for (FAuracronVoiceParticipant& Participant : ConnectedParticipants)
        {
            if (Participant.bUse3DPosition)
            {
                // Atualizar posição baseada no pawn do jogador
                // Em produção, obter posição real do jogador
                Participant.Position3D = FVector::ZeroVector;
            }
        }
    }
}

bool UAuracronVoiceBridge::ValidateVoiceConfiguration(const FAuracronVoiceConfiguration& Config) const
{
    if (Config.InputVolume < 0.0f || Config.OutputVolume < 0.0f)
    {
        return false;
    }

    if (Config.VoiceActivityThreshold < 0.0f || Config.VoiceActivityThreshold > 1.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronVoiceBridge::IsPlayerInSameTeam(APlayerController* PlayerController) const
{
    if (!PlayerController)
    {
        return false;
    }
    
    // Em produção, verificar através do sistema de equipes/partidas
    // Por enquanto, implementação simplificada
    if (APawn* PlayerPawn = PlayerController->GetPawn())
    {
        // Verificar se o pawn tem um componente de equipe ou tag específica
        if (PlayerPawn->Tags.Contains(TEXT("Team1")) || PlayerPawn->Tags.Contains(TEXT("Team2")))
        {
            return true;
        }
    }
    
    return false;
}

bool UAuracronVoiceBridge::IsPlayerInSameGuild(APlayerController* PlayerController) const
{
    if (!PlayerController)
    {
        return false;
    }
    
    // Em produção, verificar através do sistema de guildas/clãs
    // Por enquanto, implementação simplificada
    if (APawn* PlayerPawn = PlayerController->GetPawn())
    {
        // Verificar se o pawn tem uma tag de guilda
        for (const FName& Tag : PlayerPawn->Tags)
        {
            if (Tag.ToString().StartsWith(TEXT("Guild_")))
            {
                return true;
            }
        }
    }
    
    return false;
}

bool UAuracronVoiceBridge::IsPlayerInvitedToChannel(APlayerController* PlayerController, const FString& ChannelID) const
{
    if (!PlayerController || ChannelID.IsEmpty())
    {
        return false;
    }
    
    // Em produção, verificar através de sistema de convites
    // Por enquanto, implementação simplificada que verifica se o jogador está em uma lista de convidados
    FString PlayerID = PlayerController->GetName();
    
    // Buscar o canal para verificar lista de convidados
    for (const FAuracronVoiceChannel& Channel : ActiveVoiceChannels)
    {
        if (Channel.ChannelID == ChannelID)
        {
            // Verificar se o jogador está na lista de participantes permitidos
             for (const FAuracronVoiceParticipant& Participant : ConnectedParticipants)
             {
                 if (Participant.PlayerID == PlayerID && Participant.ActiveChannels.Contains(ChannelID))
                 {
                     return true;
                 }
             }
            break;
        }
    }
    
    return false;
}
