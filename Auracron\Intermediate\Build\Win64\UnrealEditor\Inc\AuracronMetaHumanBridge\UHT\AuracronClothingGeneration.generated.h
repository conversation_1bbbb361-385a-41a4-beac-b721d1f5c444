// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronClothingGeneration.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronClothingGeneration_generated_h
#error "AuracronClothingGeneration.generated.h already included, missing '#pragma once' in AuracronClothingGeneration.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronClothingGeneration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FClothMeshData ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_52_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothMeshData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothMeshData;
// ********** End ScriptStruct FClothMeshData ******************************************************

// ********** Begin ScriptStruct FClothPhysicsData *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_86_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothPhysicsData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothPhysicsData;
// ********** End ScriptStruct FClothPhysicsData ***************************************************

// ********** Begin ScriptStruct FClothSimulationData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_203_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothSimulationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothSimulationData;
// ********** End ScriptStruct FClothSimulationData ************************************************

// ********** Begin ScriptStruct FClothMaterialData ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_256_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothMaterialData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothMaterialData;
// ********** End ScriptStruct FClothMaterialData **************************************************

// ********** Begin ScriptStruct FClothLODLevel ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_317_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothLODLevel_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothLODLevel;
// ********** End ScriptStruct FClothLODLevel ******************************************************

// ********** Begin ScriptStruct FClothLODData *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_346_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothLODData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothLODData;
// ********** End ScriptStruct FClothLODData *******************************************************

// ********** Begin ScriptStruct FClothingGenerationParameters *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h_370_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingGenerationParameters;
// ********** End ScriptStruct FClothingGenerationParameters ***************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronClothingGeneration_h

// ********** Begin Enum EClothType ****************************************************************
#define FOREACH_ENUM_ECLOTHTYPE(op) \
	op(EClothType::None) \
	op(EClothType::Shirt) \
	op(EClothType::Pants) \
	op(EClothType::Dress) \
	op(EClothType::Skirt) \
	op(EClothType::Cape) \
	op(EClothType::Custom) 

enum class EClothType : uint8;
template<> struct TIsUEnumClass<EClothType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothType>();
// ********** End Enum EClothType ******************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
