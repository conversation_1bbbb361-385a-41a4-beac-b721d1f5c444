// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGCachingSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGCachingSystem_generated_h
#error "AuracronPCGCachingSystem.generated.h already included, missing '#pragma once' in AuracronPCGCachingSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGCachingSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronPCGCacheManager;
class UPCGData;
class UPCGGraph;
class UPCGPointData;
class UPCGSettings;
class UPCGSpatialData;
enum class EAuracronPCGCacheInvalidationStrategy : uint8;
struct FAuracronPCGCacheConfiguration;
struct FAuracronPCGCacheEntry;
struct FAuracronPCGCacheKey;
struct FAuracronPCGCacheStatistics;

// ********** Begin ScriptStruct FAuracronPCGCacheConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_110_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCacheConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCacheConfiguration;
// ********** End ScriptStruct FAuracronPCGCacheConfiguration **************************************

// ********** Begin ScriptStruct FAuracronPCGCacheKey **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_220_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCacheKey_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCacheKey;
// ********** End ScriptStruct FAuracronPCGCacheKey ************************************************

// ********** Begin ScriptStruct FAuracronPCGCacheEntry ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_269_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCacheEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCacheEntry;
// ********** End ScriptStruct FAuracronPCGCacheEntry **********************************************

// ********** Begin ScriptStruct FAuracronPCGCacheStatistics ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_344_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCacheStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCacheStatistics;
// ********** End ScriptStruct FAuracronPCGCacheStatistics *****************************************

// ********** Begin Class UAuracronPCGDependencyTracker ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLoadDependencyGraph); \
	DECLARE_FUNCTION(execSaveDependencyGraph); \
	DECLARE_FUNCTION(execClearDependencyGraph); \
	DECLARE_FUNCTION(execUpdateDependencyGraph); \
	DECLARE_FUNCTION(execBuildDependencyGraph); \
	DECLARE_FUNCTION(execGetDependencyDepth); \
	DECLARE_FUNCTION(execGetTopologicalOrder); \
	DECLARE_FUNCTION(execDetectCircularDependencies); \
	DECLARE_FUNCTION(execHasCircularDependency); \
	DECLARE_FUNCTION(execHasDependency); \
	DECLARE_FUNCTION(execGetAllDependents); \
	DECLARE_FUNCTION(execGetAllDependencies); \
	DECLARE_FUNCTION(execGetDependents); \
	DECLARE_FUNCTION(execGetDependencies); \
	DECLARE_FUNCTION(execRemoveAllDependencies); \
	DECLARE_FUNCTION(execRemoveDependency); \
	DECLARE_FUNCTION(execAddDependency);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDependencyTracker_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDependencyTracker(); \
	friend struct Z_Construct_UClass_UAuracronPCGDependencyTracker_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDependencyTracker_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDependencyTracker, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDependencyTracker_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDependencyTracker)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGDependencyTracker(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDependencyTracker(UAuracronPCGDependencyTracker&&) = delete; \
	UAuracronPCGDependencyTracker(const UAuracronPCGDependencyTracker&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDependencyTracker); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDependencyTracker); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGDependencyTracker) \
	NO_API virtual ~UAuracronPCGDependencyTracker();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_422_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_425_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDependencyTracker;

// ********** End Class UAuracronPCGDependencyTracker **********************************************

// ********** Begin Class UAuracronPCGCacheInvalidator *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetInvalidationStatistics); \
	DECLARE_FUNCTION(execGetInvalidationStatistics); \
	DECLARE_FUNCTION(execGetInvalidationCount); \
	DECLARE_FUNCTION(execNotifyGraphChanged); \
	DECLARE_FUNCTION(execNotifyNodeChanged); \
	DECLARE_FUNCTION(execUnregisterChangeListener); \
	DECLARE_FUNCTION(execRegisterChangeListener); \
	DECLARE_FUNCTION(execProcessScheduledInvalidations); \
	DECLARE_FUNCTION(execCancelScheduledInvalidation); \
	DECLARE_FUNCTION(execScheduleInvalidation); \
	DECLARE_FUNCTION(execGetInvalidationStrategy); \
	DECLARE_FUNCTION(execSetInvalidationStrategy); \
	DECLARE_FUNCTION(execInvalidateAll); \
	DECLARE_FUNCTION(execInvalidateByPattern); \
	DECLARE_FUNCTION(execInvalidateExpiredEntries); \
	DECLARE_FUNCTION(execInvalidateDependents); \
	DECLARE_FUNCTION(execInvalidateGraph); \
	DECLARE_FUNCTION(execInvalidateNode); \
	DECLARE_FUNCTION(execInvalidateEntry);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheInvalidator_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCacheInvalidator(); \
	friend struct Z_Construct_UClass_UAuracronPCGCacheInvalidator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheInvalidator_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCacheInvalidator, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCacheInvalidator_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCacheInvalidator)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCacheInvalidator(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCacheInvalidator(UAuracronPCGCacheInvalidator&&) = delete; \
	UAuracronPCGCacheInvalidator(const UAuracronPCGCacheInvalidator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCacheInvalidator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCacheInvalidator); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCacheInvalidator) \
	NO_API virtual ~UAuracronPCGCacheInvalidator();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_504_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_507_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCacheInvalidator;

// ********** End Class UAuracronPCGCacheInvalidator ***********************************************

// ********** Begin Class UAuracronPCGPersistentStorage ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsEncryptionEnabled); \
	DECLARE_FUNCTION(execSetEncryptionEnabled); \
	DECLARE_FUNCTION(execIsCompressionEnabled); \
	DECLARE_FUNCTION(execSetCompressionEnabled); \
	DECLARE_FUNCTION(execCompactStorage); \
	DECLARE_FUNCTION(execCleanupStorage); \
	DECLARE_FUNCTION(execGetEntryCount); \
	DECLARE_FUNCTION(execGetStorageSize); \
	DECLARE_FUNCTION(execGetStorageDirectory); \
	DECLARE_FUNCTION(execSetStorageDirectory); \
	DECLARE_FUNCTION(execDeleteEntries); \
	DECLARE_FUNCTION(execLoadEntries); \
	DECLARE_FUNCTION(execSaveEntries); \
	DECLARE_FUNCTION(execEntryExists); \
	DECLARE_FUNCTION(execDeleteEntry); \
	DECLARE_FUNCTION(execLoadEntry); \
	DECLARE_FUNCTION(execSaveEntry);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPersistentStorage_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPersistentStorage(); \
	friend struct Z_Construct_UClass_UAuracronPCGPersistentStorage_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPersistentStorage_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPersistentStorage, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPersistentStorage_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPersistentStorage)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPersistentStorage(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPersistentStorage(UAuracronPCGPersistentStorage&&) = delete; \
	UAuracronPCGPersistentStorage(const UAuracronPCGPersistentStorage&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPersistentStorage); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPersistentStorage); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPersistentStorage) \
	NO_API virtual ~UAuracronPCGPersistentStorage();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_601_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_604_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPersistentStorage;

// ********** End Class UAuracronPCGPersistentStorage **********************************************

// ********** Begin Delegate FOnCacheHit ***********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_802_DELEGATE \
static void FOnCacheHit_DelegateWrapper(const FMulticastScriptDelegate& OnCacheHit, FAuracronPCGCacheKey Key, float AccessTime);


// ********** End Delegate FOnCacheHit *************************************************************

// ********** Begin Delegate FOnCacheMiss **********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_803_DELEGATE \
static void FOnCacheMiss_DelegateWrapper(const FMulticastScriptDelegate& OnCacheMiss, FAuracronPCGCacheKey Key, float ComputationTime);


// ********** End Delegate FOnCacheMiss ************************************************************

// ********** Begin Delegate FOnCacheEviction ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_804_DELEGATE \
static void FOnCacheEviction_DelegateWrapper(const FMulticastScriptDelegate& OnCacheEviction, FAuracronPCGCacheKey Key, const FString& Reason);


// ********** End Delegate FOnCacheEviction ********************************************************

// ********** Begin Class UAuracronPCGCacheManager *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetDependencies); \
	DECLARE_FUNCTION(execRemoveDependency); \
	DECLARE_FUNCTION(execAddDependency); \
	DECLARE_FUNCTION(execSetPrefetchingEnabled); \
	DECLARE_FUNCTION(execPrefetchCache); \
	DECLARE_FUNCTION(execPreloadCache); \
	DECLARE_FUNCTION(execSetMaxEntries); \
	DECLARE_FUNCTION(execSetMaxMemorySize); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execGetCacheSize); \
	DECLARE_FUNCTION(execGetHitRatio); \
	DECLARE_FUNCTION(execResetCacheStatistics); \
	DECLARE_FUNCTION(execGetCacheStatistics); \
	DECLARE_FUNCTION(execCleanupExpiredEntries); \
	DECLARE_FUNCTION(execClearCache); \
	DECLARE_FUNCTION(execInvalidateGraphCache); \
	DECLARE_FUNCTION(execInvalidateNodeCache); \
	DECLARE_FUNCTION(execInvalidateCache); \
	DECLARE_FUNCTION(execSetCachedSpatialData); \
	DECLARE_FUNCTION(execGetCachedSpatialData); \
	DECLARE_FUNCTION(execSetCachedPointData); \
	DECLARE_FUNCTION(execGetCachedPointData); \
	DECLARE_FUNCTION(execRemoveCachedResult); \
	DECLARE_FUNCTION(execHasCachedResult); \
	DECLARE_FUNCTION(execSetCachedResult); \
	DECLARE_FUNCTION(execGetCachedResult); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCacheManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGCacheManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCacheManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCacheManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCacheManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCacheManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCacheManager(UAuracronPCGCacheManager&&) = delete; \
	UAuracronPCGCacheManager(const UAuracronPCGCacheManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCacheManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCacheManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCacheManager) \
	NO_API virtual ~UAuracronPCGCacheManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_690_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_693_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCacheManager;

// ********** End Class UAuracronPCGCacheManager ***************************************************

// ********** Begin Class UAuracronPCGCacheUtils ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCacheStatisticsToString); \
	DECLARE_FUNCTION(execCacheEntryToString); \
	DECLARE_FUNCTION(execCacheKeyToString); \
	DECLARE_FUNCTION(execShouldCacheResult); \
	DECLARE_FUNCTION(execEstimateMemoryUsage); \
	DECLARE_FUNCTION(execEstimateCacheValue); \
	DECLARE_FUNCTION(execIsDataCompatible); \
	DECLARE_FUNCTION(execValidateCacheKey); \
	DECLARE_FUNCTION(execValidateCacheEntry); \
	DECLARE_FUNCTION(execDeserializeSpatialData); \
	DECLARE_FUNCTION(execSerializeSpatialData); \
	DECLARE_FUNCTION(execDeserializePointData); \
	DECLARE_FUNCTION(execSerializePointData); \
	DECLARE_FUNCTION(execGenerateHashForSettings); \
	DECLARE_FUNCTION(execGenerateHashForData); \
	DECLARE_FUNCTION(execGenerateKeyForGraph); \
	DECLARE_FUNCTION(execGenerateKeyForNode);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCacheUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGCacheUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCacheUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCacheUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCacheUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCacheUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCacheUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCacheUtils(UAuracronPCGCacheUtils&&) = delete; \
	UAuracronPCGCacheUtils(const UAuracronPCGCacheUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCacheUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCacheUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCacheUtils) \
	NO_API virtual ~UAuracronPCGCacheUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_867_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h_870_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCacheUtils;

// ********** End Class UAuracronPCGCacheUtils *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCachingSystem_h

// ********** Begin Enum EAuracronPCGCacheEntryType ************************************************
#define FOREACH_ENUM_EAURACRONPCGCACHEENTRYTYPE(op) \
	op(EAuracronPCGCacheEntryType::NodeResult) \
	op(EAuracronPCGCacheEntryType::GraphResult) \
	op(EAuracronPCGCacheEntryType::PointData) \
	op(EAuracronPCGCacheEntryType::SpatialData) \
	op(EAuracronPCGCacheEntryType::Metadata) \
	op(EAuracronPCGCacheEntryType::Settings) \
	op(EAuracronPCGCacheEntryType::Custom) 

enum class EAuracronPCGCacheEntryType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCacheEntryType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCacheEntryType>();
// ********** End Enum EAuracronPCGCacheEntryType **************************************************

// ********** Begin Enum EAuracronPCGCacheInvalidationStrategy *************************************
#define FOREACH_ENUM_EAURACRONPCGCACHEINVALIDATIONSTRATEGY(op) \
	op(EAuracronPCGCacheInvalidationStrategy::Immediate) \
	op(EAuracronPCGCacheInvalidationStrategy::Deferred) \
	op(EAuracronPCGCacheInvalidationStrategy::OnAccess) \
	op(EAuracronPCGCacheInvalidationStrategy::TimeBasedLRU) \
	op(EAuracronPCGCacheInvalidationStrategy::SizeBasedLRU) \
	op(EAuracronPCGCacheInvalidationStrategy::DependencyBased) \
	op(EAuracronPCGCacheInvalidationStrategy::Custom) 

enum class EAuracronPCGCacheInvalidationStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCacheInvalidationStrategy> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCacheInvalidationStrategy>();
// ********** End Enum EAuracronPCGCacheInvalidationStrategy ***************************************

// ********** Begin Enum EAuracronPCGCacheStorageType **********************************************
#define FOREACH_ENUM_EAURACRONPCGCACHESTORAGETYPE(op) \
	op(EAuracronPCGCacheStorageType::Memory) \
	op(EAuracronPCGCacheStorageType::Disk) \
	op(EAuracronPCGCacheStorageType::Hybrid) \
	op(EAuracronPCGCacheStorageType::Distributed) \
	op(EAuracronPCGCacheStorageType::Compressed) \
	op(EAuracronPCGCacheStorageType::Encrypted) 

enum class EAuracronPCGCacheStorageType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCacheStorageType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCacheStorageType>();
// ********** End Enum EAuracronPCGCacheStorageType ************************************************

// ********** Begin Enum EAuracronPCGCacheAccessPattern ********************************************
#define FOREACH_ENUM_EAURACRONPCGCACHEACCESSPATTERN(op) \
	op(EAuracronPCGCacheAccessPattern::ReadOnly) \
	op(EAuracronPCGCacheAccessPattern::WriteOnly) \
	op(EAuracronPCGCacheAccessPattern::ReadWrite) \
	op(EAuracronPCGCacheAccessPattern::WriteThrough) \
	op(EAuracronPCGCacheAccessPattern::WriteBack) \
	op(EAuracronPCGCacheAccessPattern::ReadThrough) 

enum class EAuracronPCGCacheAccessPattern : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCacheAccessPattern> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCacheAccessPattern>();
// ********** End Enum EAuracronPCGCacheAccessPattern **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
