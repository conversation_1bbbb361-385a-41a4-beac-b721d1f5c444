// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionLandscape.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLandscape_generated_h
#error "AuracronWorldPartitionLandscape.generated.h already included, missing '#pragma once' in AuracronWorldPartitionLandscape.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLandscape_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionLandscapeManager;
class UWorld;
enum class EAuracronLandscapeLODState : uint8;
enum class EAuracronLandscapeStreamingState : uint8;
struct FAuracronHeightmapData;
struct FAuracronLandscapeConfiguration;
struct FAuracronLandscapeDescriptor;
struct FAuracronLandscapeStatistics;

// ********** Begin ScriptStruct FAuracronLandscapeConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_106_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLandscapeConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLandscapeConfiguration;
// ********** End ScriptStruct FAuracronLandscapeConfiguration *************************************

// ********** Begin ScriptStruct FAuracronLandscapeDescriptor **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_200_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLandscapeDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLandscapeDescriptor;
// ********** End ScriptStruct FAuracronLandscapeDescriptor ****************************************

// ********** Begin ScriptStruct FAuracronHeightmapData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_289_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronHeightmapData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronHeightmapData;
// ********** End ScriptStruct FAuracronHeightmapData **********************************************

// ********** Begin ScriptStruct FAuracronLandscapeStatistics **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_346_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLandscapeStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLandscapeStatistics;
// ********** End ScriptStruct FAuracronLandscapeStatistics ****************************************

// ********** Begin Delegate FOnLandscapeLoaded ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_565_DELEGATE \
static void FOnLandscapeLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeLoaded, const FString& LandscapeId, bool bSuccess);


// ********** End Delegate FOnLandscapeLoaded ******************************************************

// ********** Begin Delegate FOnLandscapeUnloaded **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_566_DELEGATE \
static void FOnLandscapeUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeUnloaded, const FString& LandscapeId);


// ********** End Delegate FOnLandscapeUnloaded ****************************************************

// ********** Begin Delegate FOnLandscapeLODChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_567_DELEGATE \
static void FOnLandscapeLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLandscapeLODChanged, const FString& LandscapeId, EAuracronLandscapeLODState NewLOD);


// ********** End Delegate FOnLandscapeLODChanged **************************************************

// ********** Begin Delegate FOnHeightmapUpdated ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_568_DELEGATE \
static void FOnHeightmapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnHeightmapUpdated, const FString& LandscapeId, bool bSuccess);


// ********** End Delegate FOnHeightmapUpdated *****************************************************

// ********** Begin Class UAuracronWorldPartitionLandscapeManager **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugLandscapeInfo); \
	DECLARE_FUNCTION(execLogLandscapeState); \
	DECLARE_FUNCTION(execIsLandscapeDebugEnabled); \
	DECLARE_FUNCTION(execEnableLandscapeDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetLoadedLandscapeCount); \
	DECLARE_FUNCTION(execGetTotalLandscapeCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetLandscapeStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execUpdateCollisionMesh); \
	DECLARE_FUNCTION(execIsLandscapeCollisionEnabled); \
	DECLARE_FUNCTION(execEnableLandscapeCollision); \
	DECLARE_FUNCTION(execMoveLandscapeToCell); \
	DECLARE_FUNCTION(execGetLandscapeCell); \
	DECLARE_FUNCTION(execGetLandscapesInCell); \
	DECLARE_FUNCTION(execCalculateLODForDistance); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLODs); \
	DECLARE_FUNCTION(execGetLandscapeLOD); \
	DECLARE_FUNCTION(execSetLandscapeLOD); \
	DECLARE_FUNCTION(execGetLandscapeMaterials); \
	DECLARE_FUNCTION(execSetLandscapeMaterial); \
	DECLARE_FUNCTION(execUnloadLandscapeMaterials); \
	DECLARE_FUNCTION(execLoadLandscapeMaterials); \
	DECLARE_FUNCTION(execGetHeightAtLocation); \
	DECLARE_FUNCTION(execUpdateHeightmap); \
	DECLARE_FUNCTION(execGetHeightmapData); \
	DECLARE_FUNCTION(execUnloadHeightmap); \
	DECLARE_FUNCTION(execLoadHeightmap); \
	DECLARE_FUNCTION(execGetStreamingLandscapes); \
	DECLARE_FUNCTION(execGetLoadedLandscapes); \
	DECLARE_FUNCTION(execGetLandscapeStreamingState); \
	DECLARE_FUNCTION(execUnloadLandscape); \
	DECLARE_FUNCTION(execLoadLandscape); \
	DECLARE_FUNCTION(execDoesLandscapeExist); \
	DECLARE_FUNCTION(execGetLandscapeIds); \
	DECLARE_FUNCTION(execGetAllLandscapes); \
	DECLARE_FUNCTION(execGetLandscapeDescriptor); \
	DECLARE_FUNCTION(execRemoveLandscape); \
	DECLARE_FUNCTION(execCreateLandscape); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionLandscapeManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionLandscapeManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionLandscapeManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionLandscapeManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionLandscapeManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionLandscapeManager(UAuracronWorldPartitionLandscapeManager&&) = delete; \
	UAuracronWorldPartitionLandscapeManager(const UAuracronWorldPartitionLandscapeManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionLandscapeManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionLandscapeManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionLandscapeManager) \
	NO_API virtual ~UAuracronWorldPartitionLandscapeManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_408_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h_411_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionLandscapeManager;

// ********** End Class UAuracronWorldPartitionLandscapeManager ************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLandscape_h

// ********** Begin Enum EAuracronLandscapeStreamingState ******************************************
#define FOREACH_ENUM_EAURACRONLANDSCAPESTREAMINGSTATE(op) \
	op(EAuracronLandscapeStreamingState::Unloaded) \
	op(EAuracronLandscapeStreamingState::Loading) \
	op(EAuracronLandscapeStreamingState::Loaded) \
	op(EAuracronLandscapeStreamingState::Unloading) \
	op(EAuracronLandscapeStreamingState::Failed) 

enum class EAuracronLandscapeStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronLandscapeStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLandscapeStreamingState>();
// ********** End Enum EAuracronLandscapeStreamingState ********************************************

// ********** Begin Enum EAuracronLandscapeLODState ************************************************
#define FOREACH_ENUM_EAURACRONLANDSCAPELODSTATE(op) \
	op(EAuracronLandscapeLODState::LOD0) \
	op(EAuracronLandscapeLODState::LOD1) \
	op(EAuracronLandscapeLODState::LOD2) \
	op(EAuracronLandscapeLODState::LOD3) \
	op(EAuracronLandscapeLODState::LOD4) \
	op(EAuracronLandscapeLODState::LOD5) \
	op(EAuracronLandscapeLODState::LOD6) \
	op(EAuracronLandscapeLODState::LOD7) 

enum class EAuracronLandscapeLODState : uint8;
template<> struct TIsUEnumClass<EAuracronLandscapeLODState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLandscapeLODState>();
// ********** End Enum EAuracronLandscapeLODState **************************************************

// ********** Begin Enum EAuracronHeightmapQuality *************************************************
#define FOREACH_ENUM_EAURACRONHEIGHTMAPQUALITY(op) \
	op(EAuracronHeightmapQuality::Low) \
	op(EAuracronHeightmapQuality::Medium) \
	op(EAuracronHeightmapQuality::High) \
	op(EAuracronHeightmapQuality::Ultra) 

enum class EAuracronHeightmapQuality : uint8;
template<> struct TIsUEnumClass<EAuracronHeightmapQuality> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronHeightmapQuality>();
// ********** End Enum EAuracronHeightmapQuality ***************************************************

// ********** Begin Enum EAuracronMaterialStreamingType ********************************************
#define FOREACH_ENUM_EAURACRONMATERIALSTREAMINGTYPE(op) \
	op(EAuracronMaterialStreamingType::Static) \
	op(EAuracronMaterialStreamingType::Dynamic) \
	op(EAuracronMaterialStreamingType::Adaptive) \
	op(EAuracronMaterialStreamingType::OnDemand) 

enum class EAuracronMaterialStreamingType : uint8;
template<> struct TIsUEnumClass<EAuracronMaterialStreamingType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronMaterialStreamingType>();
// ********** End Enum EAuracronMaterialStreamingType **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
