// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronNetworkingBridge_init() {}
	AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature();
	AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature();
	AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature();
	AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature();
	AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronNetworkingBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronNetworkingBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronNetworkingBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronNetworkingBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x39061E61,
				0xBB04CE0D,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronNetworkingBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronNetworkingBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronNetworkingBridge(Z_Construct_UPackage__Script_AuracronNetworkingBridge, TEXT("/Script/AuracronNetworkingBridge"), Z_Registration_Info_UPackage__Script_AuracronNetworkingBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x39061E61, 0xBB04CE0D));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
