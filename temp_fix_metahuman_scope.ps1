# Script para comentar temporariamente USTRUCT/UENUM em escopo incorreto

$filePath = "Auracron\Source\AuracronMetaHumanBridge\Public\AuracronMetaHumanBridge.h"

Write-Host "Comentando temporariamente USTRUCT/UENUM em escopo incorreto..."

# Ler o arquivo
$content = Get-Content $filePath -Raw

# Lista de linhas problemáticas identificadas
$problemLines = @(
    1087, 1097, 1106, 1116, 1125, 1134, 1144, 1184, 1224, 1262,
    1863, 1874, 1884, 1898, 1909, 1919, 1929, 1966, 2012, 2061, 2110,
    2343, 2357, 2373, 2389, 2405, 2421, 2437, 2455, 2471, 2484,
    2537, 2604, 2676, 2743, 2810, 2872, 2925,
    3236, 3246, 3256, 3270, 3284, 3295, 3306, 3316, 3326,
    3362, 3402, 3443, 3494, 3545, 3595, 3636,
    3944, 3962, 3973, 3984, 3996, 4008, 4019, 4029, 4039, 4049,
    4150, 4210, 4294, 4350, 4396,
    4598, 4610, 4622, 4634, 4646, 4658, 4669, 4680, 4706,
    4757, 4814, 4872, 4918, 4965,
    6007, 6019, 6032, 6044, 6056, 6069, 6120, 6166, 6207, 6248, 6289,
    6340, 6351, 6367, 6379, 6393, 6455
)

# Dividir em linhas
$lines = $content -split "`r?`n"

# Comentar as linhas problemáticas
foreach ($lineNum in $problemLines) {
    $index = $lineNum - 1  # Arrays são 0-based
    if ($index -lt $lines.Length) {
        $line = $lines[$index]
        if ($line -match "^\s*(USTRUCT|UENUM)") {
            $lines[$index] = "    // TEMPORARILY COMMENTED FOR COMPILATION: " + $line.Trim()
            Write-Host "Comentada linha $lineNum`: $($line.Trim())"
        }
    }
}

# Salvar o arquivo modificado
$newContent = $lines -join "`n"
Set-Content -Path $filePath -Value $newContent -NoNewline

Write-Host "Arquivo modificado com sucesso!"
Write-Host "NOTA: Esta é uma correção temporária. Os USTRUCT/UENUM precisam ser movidos para escopo global posteriormente."
