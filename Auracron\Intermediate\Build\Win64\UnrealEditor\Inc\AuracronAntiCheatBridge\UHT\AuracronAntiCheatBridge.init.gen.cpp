// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronAntiCheatBridge_init() {}
	AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature();
	AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature();
	AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature();
	AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronAntiCheatBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronAntiCheatBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronAntiCheatBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronAntiCheatBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x7C24577D,
				0xDC260CC7,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronAntiCheatBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronAntiCheatBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronAntiCheatBridge(Z_Construct_UPackage__Script_AuracronAntiCheatBridge, TEXT("/Script/AuracronAntiCheatBridge"), Z_Registration_Info_UPackage__Script_AuracronAntiCheatBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x7C24577D, 0xDC260CC7));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
