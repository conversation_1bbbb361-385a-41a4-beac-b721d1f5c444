// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGPythonBindings.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGPythonBindings() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UEnum();
COREUOBJECT_API UClass* Z_Construct_UClass_UFunction();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UScriptStruct();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGPythonBindingCategory *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory;
static UEnum* EAuracronPCGPythonBindingCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPythonBindingCategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonBindingCategory>()
{
	return EAuracronPCGPythonBindingCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All" },
		{ "All.Name", "EAuracronPCGPythonBindingCategory::All" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python binding categories\n" },
#endif
		{ "Core.DisplayName", "Core" },
		{ "Core.Name", "EAuracronPCGPythonBindingCategory::Core" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGPythonBindingCategory::Custom" },
		{ "Data.DisplayName", "Data" },
		{ "Data.Name", "EAuracronPCGPythonBindingCategory::Data" },
		{ "Debug.DisplayName", "Debug" },
		{ "Debug.Name", "EAuracronPCGPythonBindingCategory::Debug" },
		{ "Execution.DisplayName", "Execution" },
		{ "Execution.Name", "EAuracronPCGPythonBindingCategory::Execution" },
		{ "Graph.DisplayName", "Graph" },
		{ "Graph.Name", "EAuracronPCGPythonBindingCategory::Graph" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
		{ "Nodes.DisplayName", "Nodes" },
		{ "Nodes.Name", "EAuracronPCGPythonBindingCategory::Nodes" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python binding categories" },
#endif
		{ "Utilities.DisplayName", "Utilities" },
		{ "Utilities.Name", "EAuracronPCGPythonBindingCategory::Utilities" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPythonBindingCategory::Core", (int64)EAuracronPCGPythonBindingCategory::Core },
		{ "EAuracronPCGPythonBindingCategory::Nodes", (int64)EAuracronPCGPythonBindingCategory::Nodes },
		{ "EAuracronPCGPythonBindingCategory::Data", (int64)EAuracronPCGPythonBindingCategory::Data },
		{ "EAuracronPCGPythonBindingCategory::Graph", (int64)EAuracronPCGPythonBindingCategory::Graph },
		{ "EAuracronPCGPythonBindingCategory::Execution", (int64)EAuracronPCGPythonBindingCategory::Execution },
		{ "EAuracronPCGPythonBindingCategory::Debug", (int64)EAuracronPCGPythonBindingCategory::Debug },
		{ "EAuracronPCGPythonBindingCategory::Custom", (int64)EAuracronPCGPythonBindingCategory::Custom },
		{ "EAuracronPCGPythonBindingCategory::Utilities", (int64)EAuracronPCGPythonBindingCategory::Utilities },
		{ "EAuracronPCGPythonBindingCategory::All", (int64)EAuracronPCGPythonBindingCategory::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPythonBindingCategory",
	"EAuracronPCGPythonBindingCategory",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory.InnerSingleton;
}
// ********** End Enum EAuracronPCGPythonBindingCategory *******************************************

// ********** Begin Enum EAuracronPCGPythonBindingMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode;
static UEnum* EAuracronPCGPythonBindingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPythonBindingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonBindingMode>()
{
	return EAuracronPCGPythonBindingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python binding modes\n" },
#endif
		{ "ExecuteOnly.DisplayName", "Execute Only" },
		{ "ExecuteOnly.Name", "EAuracronPCGPythonBindingMode::ExecuteOnly" },
		{ "Full.DisplayName", "Full Access" },
		{ "Full.Name", "EAuracronPCGPythonBindingMode::Full" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
		{ "ReadOnly.DisplayName", "Read Only" },
		{ "ReadOnly.Name", "EAuracronPCGPythonBindingMode::ReadOnly" },
		{ "ReadWrite.DisplayName", "Read Write" },
		{ "ReadWrite.Name", "EAuracronPCGPythonBindingMode::ReadWrite" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python binding modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPythonBindingMode::ReadOnly", (int64)EAuracronPCGPythonBindingMode::ReadOnly },
		{ "EAuracronPCGPythonBindingMode::ReadWrite", (int64)EAuracronPCGPythonBindingMode::ReadWrite },
		{ "EAuracronPCGPythonBindingMode::ExecuteOnly", (int64)EAuracronPCGPythonBindingMode::ExecuteOnly },
		{ "EAuracronPCGPythonBindingMode::Full", (int64)EAuracronPCGPythonBindingMode::Full },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPythonBindingMode",
	"EAuracronPCGPythonBindingMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGPythonBindingMode ***********************************************

// ********** Begin Enum EAuracronPCGPythonExecutionContext ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext;
static UEnum* EAuracronPCGPythonExecutionContext_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGPythonExecutionContext"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPythonExecutionContext>()
{
	return EAuracronPCGPythonExecutionContext_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Any.DisplayName", "Any" },
		{ "Any.Name", "EAuracronPCGPythonExecutionContext::Any" },
		{ "BlueprintType", "true" },
		{ "Client.DisplayName", "Client" },
		{ "Client.Name", "EAuracronPCGPythonExecutionContext::Client" },
		{ "Commandlet.DisplayName", "Commandlet" },
		{ "Commandlet.Name", "EAuracronPCGPythonExecutionContext::Commandlet" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python execution contexts\n" },
#endif
		{ "Editor.DisplayName", "Editor" },
		{ "Editor.Name", "EAuracronPCGPythonExecutionContext::Editor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
		{ "Runtime.DisplayName", "Runtime" },
		{ "Runtime.Name", "EAuracronPCGPythonExecutionContext::Runtime" },
		{ "Server.DisplayName", "Server" },
		{ "Server.Name", "EAuracronPCGPythonExecutionContext::Server" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python execution contexts" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGPythonExecutionContext::Editor", (int64)EAuracronPCGPythonExecutionContext::Editor },
		{ "EAuracronPCGPythonExecutionContext::Runtime", (int64)EAuracronPCGPythonExecutionContext::Runtime },
		{ "EAuracronPCGPythonExecutionContext::Commandlet", (int64)EAuracronPCGPythonExecutionContext::Commandlet },
		{ "EAuracronPCGPythonExecutionContext::Server", (int64)EAuracronPCGPythonExecutionContext::Server },
		{ "EAuracronPCGPythonExecutionContext::Client", (int64)EAuracronPCGPythonExecutionContext::Client },
		{ "EAuracronPCGPythonExecutionContext::Any", (int64)EAuracronPCGPythonExecutionContext::Any },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGPythonExecutionContext",
	"EAuracronPCGPythonExecutionContext",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext.InnerSingleton;
}
// ********** End Enum EAuracronPCGPythonExecutionContext ******************************************

// ********** Begin ScriptStruct FAuracronPCGPythonBindingDescriptor *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor;
class UScriptStruct* FAuracronPCGPythonBindingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPythonBindingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Binding Descriptor\n * Describes configuration for Python bindings\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Binding Descriptor\nDescribes configuration for Python bindings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Binding" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mode_MetaData[] = {
		{ "Category", "Binding" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "Category", "Binding" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "Category", "Module" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleDescription_MetaData[] = {
		{ "Category", "Module" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleVersion_MetaData[] = {
		{ "Category", "Module" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassesToBind_MetaData[] = {
		{ "Category", "Classes" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassesToExclude_MetaData[] = {
		{ "Category", "Classes" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionsToBind_MetaData[] = {
		{ "Category", "Functions" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionsToExclude_MetaData[] = {
		{ "Category", "Functions" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBindProperties_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBindReadOnlyProperties_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBindWriteOnlyProperties_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBindEvents_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBindDelegates_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutoCompletion_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDocstrings_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTypeHints_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableErrorHandling_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCaching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLazyLoading_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCacheSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Context_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Context;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleVersion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassesToBind_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ClassesToBind;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassesToExclude_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ClassesToExclude;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionsToBind_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FunctionsToBind;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionsToExclude_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FunctionsToExclude;
	static void NewProp_bBindProperties_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBindProperties;
	static void NewProp_bBindReadOnlyProperties_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBindReadOnlyProperties;
	static void NewProp_bBindWriteOnlyProperties_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBindWriteOnlyProperties;
	static void NewProp_bBindEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBindEvents;
	static void NewProp_bBindDelegates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBindDelegates;
	static void NewProp_bEnableAutoCompletion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutoCompletion;
	static void NewProp_bEnableDocstrings_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDocstrings;
	static void NewProp_bEnableTypeHints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTypeHints;
	static void NewProp_bEnableErrorHandling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableErrorHandling;
	static void NewProp_bEnableCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCaching;
	static void NewProp_bEnableLazyLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLazyLoading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCacheSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPythonBindingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 557218132
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, Mode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonBindingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mode_MetaData), NewProp_Mode_MetaData) }; // 3224485988
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Context_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, Context), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGPythonExecutionContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 4253872810
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleDescription = { "ModuleDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, ModuleDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleDescription_MetaData), NewProp_ModuleDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleVersion = { "ModuleVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, ModuleVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleVersion_MetaData), NewProp_ModuleVersion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToBind_Inner = { "ClassesToBind", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToBind = { "ClassesToBind", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, ClassesToBind), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassesToBind_MetaData), NewProp_ClassesToBind_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToExclude_Inner = { "ClassesToExclude", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToExclude = { "ClassesToExclude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, ClassesToExclude), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassesToExclude_MetaData), NewProp_ClassesToExclude_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToBind_Inner = { "FunctionsToBind", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToBind = { "FunctionsToBind", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, FunctionsToBind), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionsToBind_MetaData), NewProp_FunctionsToBind_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToExclude_Inner = { "FunctionsToExclude", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToExclude = { "FunctionsToExclude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, FunctionsToExclude), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionsToExclude_MetaData), NewProp_FunctionsToExclude_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindProperties_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bBindProperties = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindProperties = { "bBindProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindProperties_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBindProperties_MetaData), NewProp_bBindProperties_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindReadOnlyProperties_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bBindReadOnlyProperties = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindReadOnlyProperties = { "bBindReadOnlyProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindReadOnlyProperties_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBindReadOnlyProperties_MetaData), NewProp_bBindReadOnlyProperties_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindWriteOnlyProperties_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bBindWriteOnlyProperties = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindWriteOnlyProperties = { "bBindWriteOnlyProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindWriteOnlyProperties_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBindWriteOnlyProperties_MetaData), NewProp_bBindWriteOnlyProperties_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindEvents_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bBindEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindEvents = { "bBindEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBindEvents_MetaData), NewProp_bBindEvents_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindDelegates_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bBindDelegates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindDelegates = { "bBindDelegates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindDelegates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBindDelegates_MetaData), NewProp_bBindDelegates_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableAutoCompletion_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableAutoCompletion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableAutoCompletion = { "bEnableAutoCompletion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableAutoCompletion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutoCompletion_MetaData), NewProp_bEnableAutoCompletion_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableDocstrings_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableDocstrings = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableDocstrings = { "bEnableDocstrings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableDocstrings_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDocstrings_MetaData), NewProp_bEnableDocstrings_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableTypeHints_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableTypeHints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableTypeHints = { "bEnableTypeHints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableTypeHints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTypeHints_MetaData), NewProp_bEnableTypeHints_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableErrorHandling_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableErrorHandling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableErrorHandling = { "bEnableErrorHandling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableErrorHandling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableErrorHandling_MetaData), NewProp_bEnableErrorHandling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableCaching_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableCaching = { "bEnableCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCaching_MetaData), NewProp_bEnableCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableLazyLoading_SetBit(void* Obj)
{
	((FAuracronPCGPythonBindingDescriptor*)Obj)->bEnableLazyLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableLazyLoading = { "bEnableLazyLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonBindingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableLazyLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLazyLoading_MetaData), NewProp_bEnableLazyLoading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_MaxCacheSize = { "MaxCacheSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonBindingDescriptor, MaxCacheSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCacheSize_MetaData), NewProp_MaxCacheSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Mode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Context_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ModuleVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToBind_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToBind,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToExclude_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_ClassesToExclude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToBind_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToBind,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToExclude_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_FunctionsToExclude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindReadOnlyProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindWriteOnlyProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bBindDelegates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableAutoCompletion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableDocstrings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableTypeHints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableErrorHandling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_bEnableLazyLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewProp_MaxCacheSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPythonBindingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGPythonBindingDescriptor),
	alignof(FAuracronPCGPythonBindingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPythonBindingDescriptor *********************************

// ********** Begin ScriptStruct FAuracronPCGPythonExecutionResult *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult;
class UScriptStruct* FAuracronPCGPythonExecutionResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGPythonExecutionResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Execution Result\n * Result of Python code execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Execution Result\nResult of Python code execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Output_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinesExecuted_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Variables_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Output;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LinesExecuted;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Variables;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGPythonExecutionResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPCGPythonExecutionResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGPythonExecutionResult), &Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Output = { "Output", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, Output), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Output_MetaData), NewProp_Output_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_ExecutionTime = { "ExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, ExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTime_MetaData), NewProp_ExecutionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_LinesExecuted = { "LinesExecuted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, LinesExecuted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinesExecuted_MetaData), NewProp_LinesExecuted_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables_ValueProp = { "Variables", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables_Key_KeyProp = { "Variables_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables = { "Variables", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, Variables), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Variables_MetaData), NewProp_Variables_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGPythonExecutionResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Output,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_ExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_LinesExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Variables,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewProp_Warnings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGPythonExecutionResult",
	Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::PropPointers),
	sizeof(FAuracronPCGPythonExecutionResult),
	alignof(FAuracronPCGPythonExecutionResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGPythonExecutionResult ***********************************

// ********** Begin Class UAuracronPCGPythonBindingManager Function AddToPythonPath ****************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics
{
	struct AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms
	{
		FString Path;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Path;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms, Path), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Path_MetaData), NewProp_Path_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_Path,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "AddToPythonPath", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::AuracronPCGPythonBindingManager_eventAddToPythonPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execAddToPythonPath)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Path);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddToPythonPath(Z_Param_Path);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function AddToPythonPath ******************

// ********** Begin Class UAuracronPCGPythonBindingManager Function BindClass **********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics
{
	struct AuracronPCGPythonBindingManager_eventBindClass_Parms
	{
		FString ClassName;
		UClass* Class;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Binding operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Binding operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassName;
	static const UECodeGen_Private::FClassPropertyParams NewProp_Class;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ClassName = { "ClassName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindClass_Parms, ClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassName_MetaData), NewProp_ClassName_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_Class = { "Class", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindClass_Parms, Class), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventBindClass_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventBindClass_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_Class,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "BindClass", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::AuracronPCGPythonBindingManager_eventBindClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::AuracronPCGPythonBindingManager_eventBindClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execBindClass)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ClassName);
	P_GET_OBJECT(UClass,Z_Param_Class);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BindClass(Z_Param_ClassName,Z_Param_Class);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function BindClass ************************

// ********** Begin Class UAuracronPCGPythonBindingManager Function BindEnum ***********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics
{
	struct AuracronPCGPythonBindingManager_eventBindEnum_Parms
	{
		FString EnumName;
		UEnum* Enum;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnumName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnumName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enum;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_EnumName = { "EnumName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindEnum_Parms, EnumName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnumName_MetaData), NewProp_EnumName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_Enum = { "Enum", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindEnum_Parms, Enum), Z_Construct_UClass_UEnum, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventBindEnum_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventBindEnum_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_EnumName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_Enum,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "BindEnum", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::AuracronPCGPythonBindingManager_eventBindEnum_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::AuracronPCGPythonBindingManager_eventBindEnum_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execBindEnum)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EnumName);
	P_GET_OBJECT(UEnum,Z_Param_Enum);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BindEnum(Z_Param_EnumName,Z_Param_Enum);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function BindEnum *************************

// ********** Begin Class UAuracronPCGPythonBindingManager Function BindFunction *******************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics
{
	struct AuracronPCGPythonBindingManager_eventBindFunction_Parms
	{
		FString FunctionName;
		UFunction* Function;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Function;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindFunction_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_Function = { "Function", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindFunction_Parms, Function), Z_Construct_UClass_UFunction, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventBindFunction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventBindFunction_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_Function,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "BindFunction", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::AuracronPCGPythonBindingManager_eventBindFunction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::AuracronPCGPythonBindingManager_eventBindFunction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execBindFunction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_OBJECT(UFunction,Z_Param_Function);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BindFunction(Z_Param_FunctionName,Z_Param_Function);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function BindFunction *********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function BindStruct *********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics
{
	struct AuracronPCGPythonBindingManager_eventBindStruct_Parms
	{
		FString StructName;
		UScriptStruct* Struct;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StructName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Struct;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_StructName = { "StructName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindStruct_Parms, StructName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructName_MetaData), NewProp_StructName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_Struct = { "Struct", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventBindStruct_Parms, Struct), Z_Construct_UClass_UScriptStruct, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventBindStruct_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventBindStruct_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_StructName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_Struct,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "BindStruct", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::AuracronPCGPythonBindingManager_eventBindStruct_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::AuracronPCGPythonBindingManager_eventBindStruct_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execBindStruct)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_StructName);
	P_GET_OBJECT(UScriptStruct,Z_Param_Struct);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BindStruct(Z_Param_StructName,Z_Param_Struct);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function BindStruct ***********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ClearErrors ********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ClearErrors", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execClearErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearErrors();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ClearErrors **********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ExecutePythonCode **************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics
{
	struct AuracronPCGPythonBindingManager_eventExecutePythonCode_Parms
	{
		FString Code;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Execution functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execution functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Code_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Code;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonCode_Parms, Code), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Code_MetaData), NewProp_Code_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonCode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ExecutePythonCode", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::AuracronPCGPythonBindingManager_eventExecutePythonCode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::AuracronPCGPythonBindingManager_eventExecutePythonCode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execExecutePythonCode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Code);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=P_THIS->ExecutePythonCode(Z_Param_Code);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ExecutePythonCode ****************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ExecutePythonFile **************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics
{
	struct AuracronPCGPythonBindingManager_eventExecutePythonFile_Parms
	{
		FString FilePath;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ExecutePythonFile", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::AuracronPCGPythonBindingManager_eventExecutePythonFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::AuracronPCGPythonBindingManager_eventExecutePythonFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execExecutePythonFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=P_THIS->ExecutePythonFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ExecutePythonFile ****************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ExecutePythonFunction **********
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics
{
	struct AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms
	{
		FString ModuleName;
		FString FunctionName;
		TArray<FString> Arguments;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Arguments_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Arguments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Arguments;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_Arguments_Inner = { "Arguments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_Arguments = { "Arguments", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms, Arguments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Arguments_MetaData), NewProp_Arguments_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_Arguments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_Arguments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ExecutePythonFunction", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::AuracronPCGPythonBindingManager_eventExecutePythonFunction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execExecutePythonFunction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_TARRAY_REF(FString,Z_Param_Out_Arguments);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=P_THIS->ExecutePythonFunction(Z_Param_ModuleName,Z_Param_FunctionName,Z_Param_Out_Arguments);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ExecutePythonFunction ************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetBindingDescriptor ***********
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetBindingDescriptor_Parms
	{
		FAuracronPCGPythonBindingDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetBindingDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(0, nullptr) }; // 1888713989
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetBindingDescriptor", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::AuracronPCGPythonBindingManager_eventGetBindingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::AuracronPCGPythonBindingManager_eventGetBindingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetBindingDescriptor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonBindingDescriptor*)Z_Param__Result=P_THIS->GetBindingDescriptor();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetBindingDescriptor *************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetInstance ********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetInstance_Parms
	{
		UAuracronPCGPythonBindingManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::AuracronPCGPythonBindingManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::AuracronPCGPythonBindingManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGPythonBindingManager**)Z_Param__Result=UAuracronPCGPythonBindingManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetInstance **********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetLastErrors ******************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetLastErrors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error handling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error handling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetLastErrors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetLastErrors", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::AuracronPCGPythonBindingManager_eventGetLastErrors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::AuracronPCGPythonBindingManager_eventGetLastErrors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetLastErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLastErrors();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetLastErrors ********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetPythonPath ******************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetPythonPath_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetPythonPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetPythonPath", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::AuracronPCGPythonBindingManager_eventGetPythonPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::AuracronPCGPythonBindingManager_eventGetPythonPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetPythonPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPythonPath();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetPythonPath ********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetPythonVersion ***************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetPythonVersion_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetPythonVersion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetPythonVersion", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::AuracronPCGPythonBindingManager_eventGetPythonVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::AuracronPCGPythonBindingManager_eventGetPythonVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetPythonVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPythonVersion();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetPythonVersion *****************

// ********** Begin Class UAuracronPCGPythonBindingManager Function GetRegisteredModules ***********
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics
{
	struct AuracronPCGPythonBindingManager_eventGetRegisteredModules_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventGetRegisteredModules_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "GetRegisteredModules", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::AuracronPCGPythonBindingManager_eventGetRegisteredModules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::AuracronPCGPythonBindingManager_eventGetRegisteredModules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execGetRegisteredModules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetRegisteredModules();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function GetRegisteredModules *************

// ********** Begin Class UAuracronPCGPythonBindingManager Function HasErrors **********************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics
{
	struct AuracronPCGPythonBindingManager_eventHasErrors_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventHasErrors_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventHasErrors_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "HasErrors", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::AuracronPCGPythonBindingManager_eventHasErrors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::AuracronPCGPythonBindingManager_eventHasErrors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execHasErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasErrors();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function HasErrors ************************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ImportPythonModule *************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics
{
	struct AuracronPCGPythonBindingManager_eventImportPythonModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventImportPythonModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventImportPythonModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventImportPythonModule_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ImportPythonModule", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::AuracronPCGPythonBindingManager_eventImportPythonModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::AuracronPCGPythonBindingManager_eventImportPythonModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execImportPythonModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportPythonModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ImportPythonModule ***************

// ********** Begin Class UAuracronPCGPythonBindingManager Function InitializePythonBindings *******
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics
{
	struct AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms
	{
		FAuracronPCGPythonBindingDescriptor Descriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initialization and shutdown\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization and shutdown" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 1888713989
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "InitializePythonBindings", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::AuracronPCGPythonBindingManager_eventInitializePythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execInitializePythonBindings)
{
	P_GET_STRUCT_REF(FAuracronPCGPythonBindingDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonBindings(Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function InitializePythonBindings *********

// ********** Begin Class UAuracronPCGPythonBindingManager Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics
{
	struct AuracronPCGPythonBindingManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::AuracronPCGPythonBindingManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::AuracronPCGPythonBindingManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function IsInitialized ********************

// ********** Begin Class UAuracronPCGPythonBindingManager Function IsModuleRegistered *************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics
{
	struct AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "IsModuleRegistered", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::AuracronPCGPythonBindingManager_eventIsModuleRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execIsModuleRegistered)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsModuleRegistered(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function IsModuleRegistered ***************

// ********** Begin Class UAuracronPCGPythonBindingManager Function RegisterModule *****************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics
{
	struct AuracronPCGPythonBindingManager_eventRegisterModule_Parms
	{
		FString ModuleName;
		FAuracronPCGPythonBindingDescriptor Descriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Module management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Module management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventRegisterModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventRegisterModule_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 1888713989
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventRegisterModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventRegisterModule_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_Descriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "RegisterModule", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::AuracronPCGPythonBindingManager_eventRegisterModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::AuracronPCGPythonBindingManager_eventRegisterModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execRegisterModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_GET_STRUCT_REF(FAuracronPCGPythonBindingDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterModule(Z_Param_ModuleName,Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function RegisterModule *******************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ReloadPythonModule *************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics
{
	struct AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ReloadPythonModule", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::AuracronPCGPythonBindingManager_eventReloadPythonModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execReloadPythonModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReloadPythonModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ReloadPythonModule ***************

// ********** Begin Class UAuracronPCGPythonBindingManager Function SetBindingDescriptor ***********
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics
{
	struct AuracronPCGPythonBindingManager_eventSetBindingDescriptor_Parms
	{
		FAuracronPCGPythonBindingDescriptor Descriptor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Descriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Descriptor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::NewProp_Descriptor = { "Descriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventSetBindingDescriptor_Parms, Descriptor), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Descriptor_MetaData), NewProp_Descriptor_MetaData) }; // 1888713989
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::NewProp_Descriptor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "SetBindingDescriptor", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::AuracronPCGPythonBindingManager_eventSetBindingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::AuracronPCGPythonBindingManager_eventSetBindingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execSetBindingDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGPythonBindingDescriptor,Z_Param_Out_Descriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBindingDescriptor(Z_Param_Out_Descriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function SetBindingDescriptor *************

// ********** Begin Class UAuracronPCGPythonBindingManager Function ShutdownPythonBindings *********
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "ShutdownPythonBindings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execShutdownPythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function ShutdownPythonBindings ***********

// ********** Begin Class UAuracronPCGPythonBindingManager Function UnregisterModule ***************
struct Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics
{
	struct AuracronPCGPythonBindingManager_eventUnregisterModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Binding Manager" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonBindingManager_eventUnregisterModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonBindingManager_eventUnregisterModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonBindingManager_eventUnregisterModule_Parms), &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonBindingManager, nullptr, "UnregisterModule", Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::AuracronPCGPythonBindingManager_eventUnregisterModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::AuracronPCGPythonBindingManager_eventUnregisterModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonBindingManager::execUnregisterModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonBindingManager Function UnregisterModule *****************

// ********** Begin Class UAuracronPCGPythonBindingManager *****************************************
void UAuracronPCGPythonBindingManager::StaticRegisterNativesUAuracronPCGPythonBindingManager()
{
	UClass* Class = UAuracronPCGPythonBindingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddToPythonPath", &UAuracronPCGPythonBindingManager::execAddToPythonPath },
		{ "BindClass", &UAuracronPCGPythonBindingManager::execBindClass },
		{ "BindEnum", &UAuracronPCGPythonBindingManager::execBindEnum },
		{ "BindFunction", &UAuracronPCGPythonBindingManager::execBindFunction },
		{ "BindStruct", &UAuracronPCGPythonBindingManager::execBindStruct },
		{ "ClearErrors", &UAuracronPCGPythonBindingManager::execClearErrors },
		{ "ExecutePythonCode", &UAuracronPCGPythonBindingManager::execExecutePythonCode },
		{ "ExecutePythonFile", &UAuracronPCGPythonBindingManager::execExecutePythonFile },
		{ "ExecutePythonFunction", &UAuracronPCGPythonBindingManager::execExecutePythonFunction },
		{ "GetBindingDescriptor", &UAuracronPCGPythonBindingManager::execGetBindingDescriptor },
		{ "GetInstance", &UAuracronPCGPythonBindingManager::execGetInstance },
		{ "GetLastErrors", &UAuracronPCGPythonBindingManager::execGetLastErrors },
		{ "GetPythonPath", &UAuracronPCGPythonBindingManager::execGetPythonPath },
		{ "GetPythonVersion", &UAuracronPCGPythonBindingManager::execGetPythonVersion },
		{ "GetRegisteredModules", &UAuracronPCGPythonBindingManager::execGetRegisteredModules },
		{ "HasErrors", &UAuracronPCGPythonBindingManager::execHasErrors },
		{ "ImportPythonModule", &UAuracronPCGPythonBindingManager::execImportPythonModule },
		{ "InitializePythonBindings", &UAuracronPCGPythonBindingManager::execInitializePythonBindings },
		{ "IsInitialized", &UAuracronPCGPythonBindingManager::execIsInitialized },
		{ "IsModuleRegistered", &UAuracronPCGPythonBindingManager::execIsModuleRegistered },
		{ "RegisterModule", &UAuracronPCGPythonBindingManager::execRegisterModule },
		{ "ReloadPythonModule", &UAuracronPCGPythonBindingManager::execReloadPythonModule },
		{ "SetBindingDescriptor", &UAuracronPCGPythonBindingManager::execSetBindingDescriptor },
		{ "ShutdownPythonBindings", &UAuracronPCGPythonBindingManager::execShutdownPythonBindings },
		{ "UnregisterModule", &UAuracronPCGPythonBindingManager::execUnregisterModule },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager;
UClass* UAuracronPCGPythonBindingManager::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPythonBindingManager;
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPythonBindingManager"),
			Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPythonBindingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager_NoRegister()
{
	return UAuracronPCGPythonBindingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Binding Manager\n * Manages Python bindings for PCG Framework\n */" },
#endif
		{ "IncludePath", "AuracronPCGPythonBindings.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Binding Manager\nManages Python bindings for PCG Framework" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDescriptor_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredModules_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrors_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredModules_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegisteredModules_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredModules;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LastErrors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_AddToPythonPath, "AddToPythonPath" }, // 4134812153
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindClass, "BindClass" }, // 2608249895
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindEnum, "BindEnum" }, // 624351601
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindFunction, "BindFunction" }, // 2788468276
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_BindStruct, "BindStruct" }, // 1924444137
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ClearErrors, "ClearErrors" }, // 2037025791
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonCode, "ExecutePythonCode" }, // 3393640330
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFile, "ExecutePythonFile" }, // 3829028988
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ExecutePythonFunction, "ExecutePythonFunction" }, // 1673640035
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetBindingDescriptor, "GetBindingDescriptor" }, // 1928821678
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetInstance, "GetInstance" }, // 1737388451
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetLastErrors, "GetLastErrors" }, // 135979693
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonPath, "GetPythonPath" }, // 1871456364
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetPythonVersion, "GetPythonVersion" }, // 3467815566
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_GetRegisteredModules, "GetRegisteredModules" }, // 2210376710
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_HasErrors, "HasErrors" }, // 1221589664
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ImportPythonModule, "ImportPythonModule" }, // 3659524951
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_InitializePythonBindings, "InitializePythonBindings" }, // 137345597
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsInitialized, "IsInitialized" }, // 1351553819
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_IsModuleRegistered, "IsModuleRegistered" }, // 3696449075
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_RegisterModule, "RegisterModule" }, // 2809933294
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ReloadPythonModule, "ReloadPythonModule" }, // 2299359942
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_SetBindingDescriptor, "SetBindingDescriptor" }, // 2006857881
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_ShutdownPythonBindings, "ShutdownPythonBindings" }, // 1738451181
		{ &Z_Construct_UFunction_UAuracronPCGPythonBindingManager_UnregisterModule, "UnregisterModule" }, // 2584303004
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPythonBindingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_CurrentDescriptor = { "CurrentDescriptor", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPythonBindingManager, CurrentDescriptor), Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDescriptor_MetaData), NewProp_CurrentDescriptor_MetaData) }; // 1888713989
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules_ValueProp = { "RegisteredModules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor, METADATA_PARAMS(0, nullptr) }; // 1888713989
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules_Key_KeyProp = { "RegisteredModules_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules = { "RegisteredModules", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPythonBindingManager, RegisteredModules), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredModules_MetaData), NewProp_RegisteredModules_MetaData) }; // 1888713989
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_LastErrors_Inner = { "LastErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_LastErrors = { "LastErrors", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPythonBindingManager, LastErrors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrors_MetaData), NewProp_LastErrors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_CurrentDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_RegisteredModules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_LastErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::NewProp_LastErrors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::ClassParams = {
	&UAuracronPCGPythonBindingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPythonBindingManager()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.OuterSingleton, Z_Construct_UClass_UAuracronPCGPythonBindingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager.OuterSingleton;
}
UAuracronPCGPythonBindingManager::UAuracronPCGPythonBindingManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPythonBindingManager);
UAuracronPCGPythonBindingManager::~UAuracronPCGPythonBindingManager() {}
// ********** End Class UAuracronPCGPythonBindingManager *******************************************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScript ******************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms
	{
		FString ScriptContent;
		TMap<FString,FString> Variables;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Script execution\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Variables_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Variables;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables_ValueProp = { "Variables", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables_Key_KeyProp = { "Variables_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables = { "Variables", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms, Variables), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Variables_MetaData), NewProp_Variables_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_Variables,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScript", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Variables);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ExecuteScript(Z_Param_ScriptContent,Z_Param_Out_Variables);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScript ********************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptAsync *************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptAsync_Parms
	{
		FString ScriptContent;
		FString CallbackFunction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Async execution\n" },
#endif
		{ "CPP_Default_CallbackFunction", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackFunction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackFunction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptAsync_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::NewProp_CallbackFunction = { "CallbackFunction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptAsync_Parms, CallbackFunction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackFunction_MetaData), NewProp_CallbackFunction_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::NewProp_CallbackFunction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptAsync", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackFunction);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGPythonScriptExecutor::ExecuteScriptAsync(Z_Param_ScriptContent,Z_Param_CallbackFunction);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptAsync ***************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFile **************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms
	{
		FString FilePath;
		TMap<FString,FString> Variables;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Variables_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Variables;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables_ValueProp = { "Variables", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables_Key_KeyProp = { "Variables_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables = { "Variables", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms, Variables), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Variables_MetaData), NewProp_Variables_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_Variables,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptFile", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Variables);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ExecuteScriptFile(Z_Param_FilePath,Z_Param_Out_Variables);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFile ****************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFileAsync *********
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptFileAsync_Parms
	{
		FString FilePath;
		FString CallbackFunction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "CPP_Default_CallbackFunction", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackFunction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackFunction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFileAsync_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::NewProp_CallbackFunction = { "CallbackFunction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFileAsync_Parms, CallbackFunction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackFunction_MetaData), NewProp_CallbackFunction_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::NewProp_CallbackFunction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptFileAsync", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFileAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFileAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptFileAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackFunction);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGPythonScriptExecutor::ExecuteScriptFileAsync(Z_Param_FilePath,Z_Param_CallbackFunction);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFileAsync ***********

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFileSimple ********
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptFileSimple_Parms
	{
		FString FilePath;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFileSimple_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptFileSimple_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptFileSimple", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFileSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptFileSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptFileSimple)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ExecuteScriptFileSimple(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptFileSimple **********

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptSimple ************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptSimple_Parms
	{
		FString ScriptContent;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptSimple_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptSimple_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptSimple", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptSimple)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ExecuteScriptSimple(Z_Param_ScriptContent);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptSimple **************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptWithGraph *********
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms
	{
		FString ScriptContent;
		UPCGGraph* Graph;
		TMap<FString,FString> Variables;
		FAuracronPCGPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Variables_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variables_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Variables;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables_ValueProp = { "Variables", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables_Key_KeyProp = { "Variables_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables = { "Variables", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms, Variables), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Variables_MetaData), NewProp_Variables_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 2021421145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Graph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_Variables,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ExecuteScriptWithGraph", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::AuracronPCGPythonScriptExecutor_eventExecuteScriptWithGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execExecuteScriptWithGraph)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Variables);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGPythonExecutionResult*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ExecuteScriptWithGraph(Z_Param_ScriptContent,Z_Param_Graph,Z_Param_Out_Variables);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ExecuteScriptWithGraph ***********

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function FormatScript *******************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventFormatScript_Parms
	{
		FString ScriptContent;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Script utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventFormatScript_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventFormatScript_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "FormatScript", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::AuracronPCGPythonScriptExecutor_eventFormatScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::AuracronPCGPythonScriptExecutor_eventFormatScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execFormatScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::FormatScript(Z_Param_ScriptContent);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function FormatScript *********************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function GetScriptDependencies **********
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventGetScriptDependencies_Parms
	{
		FString ScriptContent;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventGetScriptDependencies_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventGetScriptDependencies_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "GetScriptDependencies", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::AuracronPCGPythonScriptExecutor_eventGetScriptDependencies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::AuracronPCGPythonScriptExecutor_eventGetScriptDependencies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execGetScriptDependencies)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::GetScriptDependencies(Z_Param_ScriptContent);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function GetScriptDependencies ************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function HasScriptFunction **************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms
	{
		FString ScriptContent;
		FString FunctionName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms), &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "HasScriptFunction", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::AuracronPCGPythonScriptExecutor_eventHasScriptFunction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execHasScriptFunction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::HasScriptFunction(Z_Param_ScriptContent,Z_Param_FunctionName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function HasScriptFunction ****************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ValidateScript *****************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventValidateScript_Parms
	{
		FString ScriptContent;
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Script validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Script validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptContent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptContent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ScriptContent = { "ScriptContent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventValidateScript_Parms, ScriptContent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptContent_MetaData), NewProp_ScriptContent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventValidateScript_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonScriptExecutor_eventValidateScript_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonScriptExecutor_eventValidateScript_Parms), &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ScriptContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ValidateScript", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::AuracronPCGPythonScriptExecutor_eventValidateScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::AuracronPCGPythonScriptExecutor_eventValidateScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execValidateScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptContent);
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ValidateScript(Z_Param_ScriptContent,Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ValidateScript *******************

// ********** Begin Class UAuracronPCGPythonScriptExecutor Function ValidateScriptFile *************
struct Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics
{
	struct AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms
	{
		FString FilePath;
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Script Executor" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms), &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, nullptr, "ValidateScriptFile", Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::AuracronPCGPythonScriptExecutor_eventValidateScriptFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonScriptExecutor::execValidateScriptFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPythonScriptExecutor::ValidateScriptFile(Z_Param_FilePath,Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonScriptExecutor Function ValidateScriptFile ***************

// ********** Begin Class UAuracronPCGPythonScriptExecutor *****************************************
void UAuracronPCGPythonScriptExecutor::StaticRegisterNativesUAuracronPCGPythonScriptExecutor()
{
	UClass* Class = UAuracronPCGPythonScriptExecutor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ExecuteScript", &UAuracronPCGPythonScriptExecutor::execExecuteScript },
		{ "ExecuteScriptAsync", &UAuracronPCGPythonScriptExecutor::execExecuteScriptAsync },
		{ "ExecuteScriptFile", &UAuracronPCGPythonScriptExecutor::execExecuteScriptFile },
		{ "ExecuteScriptFileAsync", &UAuracronPCGPythonScriptExecutor::execExecuteScriptFileAsync },
		{ "ExecuteScriptFileSimple", &UAuracronPCGPythonScriptExecutor::execExecuteScriptFileSimple },
		{ "ExecuteScriptSimple", &UAuracronPCGPythonScriptExecutor::execExecuteScriptSimple },
		{ "ExecuteScriptWithGraph", &UAuracronPCGPythonScriptExecutor::execExecuteScriptWithGraph },
		{ "FormatScript", &UAuracronPCGPythonScriptExecutor::execFormatScript },
		{ "GetScriptDependencies", &UAuracronPCGPythonScriptExecutor::execGetScriptDependencies },
		{ "HasScriptFunction", &UAuracronPCGPythonScriptExecutor::execHasScriptFunction },
		{ "ValidateScript", &UAuracronPCGPythonScriptExecutor::execValidateScript },
		{ "ValidateScriptFile", &UAuracronPCGPythonScriptExecutor::execValidateScriptFile },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor;
UClass* UAuracronPCGPythonScriptExecutor::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPythonScriptExecutor;
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPythonScriptExecutor"),
			Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPythonScriptExecutor,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_NoRegister()
{
	return UAuracronPCGPythonScriptExecutor::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Script Executor\n * Executes Python scripts with PCG context\n */" },
#endif
		{ "IncludePath", "AuracronPCGPythonBindings.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Script Executor\nExecutes Python scripts with PCG context" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScript, "ExecuteScript" }, // 4264961477
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptAsync, "ExecuteScriptAsync" }, // 1896622588
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFile, "ExecuteScriptFile" }, // 2600587690
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileAsync, "ExecuteScriptFileAsync" }, // 681709350
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptFileSimple, "ExecuteScriptFileSimple" }, // 2135454991
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptSimple, "ExecuteScriptSimple" }, // 4159697966
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ExecuteScriptWithGraph, "ExecuteScriptWithGraph" }, // 948453629
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_FormatScript, "FormatScript" }, // 332089396
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_GetScriptDependencies, "GetScriptDependencies" }, // 1444393652
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_HasScriptFunction, "HasScriptFunction" }, // 1273738673
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScript, "ValidateScript" }, // 72270188
		{ &Z_Construct_UFunction_UAuracronPCGPythonScriptExecutor_ValidateScriptFile, "ValidateScriptFile" }, // 1155034506
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPythonScriptExecutor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::ClassParams = {
	&UAuracronPCGPythonScriptExecutor::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPythonScriptExecutor()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.OuterSingleton, Z_Construct_UClass_UAuracronPCGPythonScriptExecutor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor.OuterSingleton;
}
UAuracronPCGPythonScriptExecutor::UAuracronPCGPythonScriptExecutor(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPythonScriptExecutor);
UAuracronPCGPythonScriptExecutor::~UAuracronPCGPythonScriptExecutor() {}
// ********** End Class UAuracronPCGPythonScriptExecutor *******************************************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertArrayToPython *********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertArrayToPython_Parms
	{
		TArray<FString> Array;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Array conversion utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Array conversion utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Array_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Array_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Array;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_Array_Inner = { "Array", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_Array = { "Array", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertArrayToPython_Parms, Array), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Array_MetaData), NewProp_Array_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertArrayToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_Array_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_Array,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertArrayToPython", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertArrayToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertArrayToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertArrayToPython)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_Array);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertArrayToPython(Z_Param_Out_Array);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertArrayToPython ***********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertMapToPython ***********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertMapToPython_Parms
	{
		TMap<FString,FString> Map;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Map conversion utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Map conversion utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Map_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Map_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Map_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Map;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map_ValueProp = { "Map", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map_Key_KeyProp = { "Map_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map = { "Map", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertMapToPython_Parms, Map), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Map_MetaData), NewProp_Map_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertMapToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_Map,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertMapToPython", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertMapToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertMapToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertMapToPython)
{
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Map);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertMapToPython(Z_Param_Out_Map);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertMapToPython *************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToArray *********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertPythonToArray_Parms
	{
		FString PythonArray;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonArray_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonArray;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_PythonArray = { "PythonArray", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToArray_Parms, PythonArray), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonArray_MetaData), NewProp_PythonArray_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToArray_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_PythonArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertPythonToArray", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToArray_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToArray_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertPythonToArray)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonArray);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertPythonToArray(Z_Param_PythonArray);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToArray ***********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToMap ***********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertPythonToMap_Parms
	{
		FString PythonMap;
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonMap_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonMap;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_PythonMap = { "PythonMap", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToMap_Parms, PythonMap), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonMap_MetaData), NewProp_PythonMap_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToMap_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_PythonMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertPythonToMap", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToMap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToMap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertPythonToMap)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonMap);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertPythonToMap(Z_Param_PythonMap);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToMap *************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToUObject *******
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms
	{
		FString PythonObject;
		UClass* TargetClass;
		UObject* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonObject_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonObject;
	static const UECodeGen_Private::FClassPropertyParams NewProp_TargetClass;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_PythonObject = { "PythonObject", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms, PythonObject), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonObject_MetaData), NewProp_PythonObject_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_TargetClass = { "TargetClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms, TargetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms, ReturnValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_PythonObject,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_TargetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertPythonToUObject", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::AuracronPCGPythonIntegrationUtils_eventConvertPythonToUObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertPythonToUObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PythonObject);
	P_GET_OBJECT(UClass,Z_Param_TargetClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UObject**)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertPythonToUObject(Z_Param_PythonObject,Z_Param_TargetClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertPythonToUObject *********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function ConvertUObjectToPython *******
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventConvertUObjectToPython_Parms
	{
		UObject* Object;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Type conversion utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type conversion utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertUObjectToPython_Parms, Object), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventConvertUObjectToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::NewProp_Object,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "ConvertUObjectToPython", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertUObjectToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::AuracronPCGPythonIntegrationUtils_eventConvertUObjectToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execConvertUObjectToPython)
{
	P_GET_OBJECT(UObject,Z_Param_Object);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::ConvertUObjectToPython(Z_Param_Object);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function ConvertUObjectToPython *********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function EnablePythonDebugging ********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventEnablePythonDebugging_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debugging utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debugging utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronPCGPythonIntegrationUtils_eventEnablePythonDebugging_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonIntegrationUtils_eventEnablePythonDebugging_Parms), &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "EnablePythonDebugging", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::AuracronPCGPythonIntegrationUtils_eventEnablePythonDebugging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::AuracronPCGPythonIntegrationUtils_eventEnablePythonDebugging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execEnablePythonDebugging)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGPythonIntegrationUtils::EnablePythonDebugging(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function EnablePythonDebugging **********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GenerateClassDocumentation ***
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGenerateClassDocumentation_Parms
	{
		UClass* Class;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Documentation generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Documentation generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_Class;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::NewProp_Class = { "Class", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateClassDocumentation_Parms, Class), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateClassDocumentation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::NewProp_Class,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GenerateClassDocumentation", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateClassDocumentation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateClassDocumentation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGenerateClassDocumentation)
{
	P_GET_OBJECT(UClass,Z_Param_Class);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GenerateClassDocumentation(Z_Param_Class);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GenerateClassDocumentation *****

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GenerateFunctionDocumentation 
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGenerateFunctionDocumentation_Parms
	{
		UFunction* Function;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Function;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::NewProp_Function = { "Function", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateFunctionDocumentation_Parms, Function), Z_Construct_UClass_UFunction, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateFunctionDocumentation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::NewProp_Function,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GenerateFunctionDocumentation", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateFunctionDocumentation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateFunctionDocumentation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGenerateFunctionDocumentation)
{
	P_GET_OBJECT(UFunction,Z_Param_Function);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GenerateFunctionDocumentation(Z_Param_Function);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GenerateFunctionDocumentation **

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GenerateModuleDocumentation **
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGenerateModuleDocumentation_Parms
	{
		FString ModuleName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateModuleDocumentation_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGenerateModuleDocumentation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GenerateModuleDocumentation", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateModuleDocumentation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::AuracronPCGPythonIntegrationUtils_eventGenerateModuleDocumentation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGenerateModuleDocumentation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GenerateModuleDocumentation(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GenerateModuleDocumentation ****

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GeneratePythonStub ***********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGeneratePythonStub_Parms
	{
		UClass* Class;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Code generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Code generation utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_Class;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::NewProp_Class = { "Class", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGeneratePythonStub_Parms, Class), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGeneratePythonStub_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::NewProp_Class,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GeneratePythonStub", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::AuracronPCGPythonIntegrationUtils_eventGeneratePythonStub_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::AuracronPCGPythonIntegrationUtils_eventGeneratePythonStub_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGeneratePythonStub)
{
	P_GET_OBJECT(UClass,Z_Param_Class);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GeneratePythonStub(Z_Param_Class);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GeneratePythonStub *************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GeneratePythonWrapper ********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGeneratePythonWrapper_Parms
	{
		UClass* Class;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_Class;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::NewProp_Class = { "Class", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGeneratePythonWrapper_Parms, Class), Z_Construct_UClass_UClass, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGeneratePythonWrapper_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::NewProp_Class,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GeneratePythonWrapper", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::AuracronPCGPythonIntegrationUtils_eventGeneratePythonWrapper_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::AuracronPCGPythonIntegrationUtils_eventGeneratePythonWrapper_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGeneratePythonWrapper)
{
	P_GET_OBJECT(UClass,Z_Param_Class);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GeneratePythonWrapper(Z_Param_Class);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GeneratePythonWrapper **********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GetPythonCallStack ***********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGetPythonCallStack_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGetPythonCallStack_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GetPythonCallStack", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::AuracronPCGPythonIntegrationUtils_eventGetPythonCallStack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::AuracronPCGPythonIntegrationUtils_eventGetPythonCallStack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGetPythonCallStack)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GetPythonCallStack();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GetPythonCallStack *************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function GetPythonMemoryUsage *********
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventGetPythonMemoryUsage_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventGetPythonMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "GetPythonMemoryUsage", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::AuracronPCGPythonIntegrationUtils_eventGetPythonMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::AuracronPCGPythonIntegrationUtils_eventGetPythonMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execGetPythonMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::GetPythonMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function GetPythonMemoryUsage ***********

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function IsPythonDebuggingEnabled *****
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventIsPythonDebuggingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonIntegrationUtils_eventIsPythonDebuggingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonIntegrationUtils_eventIsPythonDebuggingEnabled_Parms), &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "IsPythonDebuggingEnabled", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::AuracronPCGPythonIntegrationUtils_eventIsPythonDebuggingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::AuracronPCGPythonIntegrationUtils_eventIsPythonDebuggingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execIsPythonDebuggingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::IsPythonDebuggingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function IsPythonDebuggingEnabled *******

// ********** Begin Class UAuracronPCGPythonIntegrationUtils Function SavePythonStubs **************
struct Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics
{
	struct AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms
	{
		FString OutputDirectory;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Integration Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputDirectory_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputDirectory;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_OutputDirectory = { "OutputDirectory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms, OutputDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputDirectory_MetaData), NewProp_OutputDirectory_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms), &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_OutputDirectory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, nullptr, "SavePythonStubs", Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::AuracronPCGPythonIntegrationUtils_eventSavePythonStubs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPythonIntegrationUtils::execSavePythonStubs)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OutputDirectory);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPythonIntegrationUtils::SavePythonStubs(Z_Param_OutputDirectory);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPythonIntegrationUtils Function SavePythonStubs ****************

// ********** Begin Class UAuracronPCGPythonIntegrationUtils ***************************************
void UAuracronPCGPythonIntegrationUtils::StaticRegisterNativesUAuracronPCGPythonIntegrationUtils()
{
	UClass* Class = UAuracronPCGPythonIntegrationUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ConvertArrayToPython", &UAuracronPCGPythonIntegrationUtils::execConvertArrayToPython },
		{ "ConvertMapToPython", &UAuracronPCGPythonIntegrationUtils::execConvertMapToPython },
		{ "ConvertPythonToArray", &UAuracronPCGPythonIntegrationUtils::execConvertPythonToArray },
		{ "ConvertPythonToMap", &UAuracronPCGPythonIntegrationUtils::execConvertPythonToMap },
		{ "ConvertPythonToUObject", &UAuracronPCGPythonIntegrationUtils::execConvertPythonToUObject },
		{ "ConvertUObjectToPython", &UAuracronPCGPythonIntegrationUtils::execConvertUObjectToPython },
		{ "EnablePythonDebugging", &UAuracronPCGPythonIntegrationUtils::execEnablePythonDebugging },
		{ "GenerateClassDocumentation", &UAuracronPCGPythonIntegrationUtils::execGenerateClassDocumentation },
		{ "GenerateFunctionDocumentation", &UAuracronPCGPythonIntegrationUtils::execGenerateFunctionDocumentation },
		{ "GenerateModuleDocumentation", &UAuracronPCGPythonIntegrationUtils::execGenerateModuleDocumentation },
		{ "GeneratePythonStub", &UAuracronPCGPythonIntegrationUtils::execGeneratePythonStub },
		{ "GeneratePythonWrapper", &UAuracronPCGPythonIntegrationUtils::execGeneratePythonWrapper },
		{ "GetPythonCallStack", &UAuracronPCGPythonIntegrationUtils::execGetPythonCallStack },
		{ "GetPythonMemoryUsage", &UAuracronPCGPythonIntegrationUtils::execGetPythonMemoryUsage },
		{ "IsPythonDebuggingEnabled", &UAuracronPCGPythonIntegrationUtils::execIsPythonDebuggingEnabled },
		{ "SavePythonStubs", &UAuracronPCGPythonIntegrationUtils::execSavePythonStubs },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils;
UClass* UAuracronPCGPythonIntegrationUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPythonIntegrationUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPythonIntegrationUtils"),
			Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPythonIntegrationUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_NoRegister()
{
	return UAuracronPCGPythonIntegrationUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Integration Utilities\n * Utility functions for Python integration\n */" },
#endif
		{ "IncludePath", "AuracronPCGPythonBindings.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPythonBindings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Integration Utilities\nUtility functions for Python integration" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertArrayToPython, "ConvertArrayToPython" }, // 2026692475
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertMapToPython, "ConvertMapToPython" }, // 192010478
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToArray, "ConvertPythonToArray" }, // 2412085516
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToMap, "ConvertPythonToMap" }, // 79573641
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertPythonToUObject, "ConvertPythonToUObject" }, // 3829424250
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_ConvertUObjectToPython, "ConvertUObjectToPython" }, // 1135133792
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_EnablePythonDebugging, "EnablePythonDebugging" }, // 370384900
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateClassDocumentation, "GenerateClassDocumentation" }, // 1292142048
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateFunctionDocumentation, "GenerateFunctionDocumentation" }, // 1850975465
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GenerateModuleDocumentation, "GenerateModuleDocumentation" }, // 1391891178
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonStub, "GeneratePythonStub" }, // 1189387169
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GeneratePythonWrapper, "GeneratePythonWrapper" }, // 1535842792
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonCallStack, "GetPythonCallStack" }, // 2353746102
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_GetPythonMemoryUsage, "GetPythonMemoryUsage" }, // 2730892909
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_IsPythonDebuggingEnabled, "IsPythonDebuggingEnabled" }, // 1480141795
		{ &Z_Construct_UFunction_UAuracronPCGPythonIntegrationUtils_SavePythonStubs, "SavePythonStubs" }, // 2327508327
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPythonIntegrationUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::ClassParams = {
	&UAuracronPCGPythonIntegrationUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils.OuterSingleton;
}
UAuracronPCGPythonIntegrationUtils::UAuracronPCGPythonIntegrationUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPythonIntegrationUtils);
UAuracronPCGPythonIntegrationUtils::~UAuracronPCGPythonIntegrationUtils() {}
// ********** End Class UAuracronPCGPythonIntegrationUtils *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGPythonBindingCategory_StaticEnum, TEXT("EAuracronPCGPythonBindingCategory"), &Z_Registration_Info_UEnum_EAuracronPCGPythonBindingCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 557218132U) },
		{ EAuracronPCGPythonBindingMode_StaticEnum, TEXT("EAuracronPCGPythonBindingMode"), &Z_Registration_Info_UEnum_EAuracronPCGPythonBindingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3224485988U) },
		{ EAuracronPCGPythonExecutionContext_StaticEnum, TEXT("EAuracronPCGPythonExecutionContext"), &Z_Registration_Info_UEnum_EAuracronPCGPythonExecutionContext, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4253872810U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGPythonBindingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPythonBindingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGPythonBindingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPythonBindingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPythonBindingDescriptor), 1888713989U) },
		{ FAuracronPCGPythonExecutionResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGPythonExecutionResult_Statics::NewStructOps, TEXT("AuracronPCGPythonExecutionResult"), &Z_Registration_Info_UScriptStruct_FAuracronPCGPythonExecutionResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGPythonExecutionResult), 2021421145U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGPythonBindingManager, UAuracronPCGPythonBindingManager::StaticClass, TEXT("UAuracronPCGPythonBindingManager"), &Z_Registration_Info_UClass_UAuracronPCGPythonBindingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPythonBindingManager), 1352787385U) },
		{ Z_Construct_UClass_UAuracronPCGPythonScriptExecutor, UAuracronPCGPythonScriptExecutor::StaticClass, TEXT("UAuracronPCGPythonScriptExecutor"), &Z_Registration_Info_UClass_UAuracronPCGPythonScriptExecutor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPythonScriptExecutor), 909529173U) },
		{ Z_Construct_UClass_UAuracronPCGPythonIntegrationUtils, UAuracronPCGPythonIntegrationUtils::StaticClass, TEXT("UAuracronPCGPythonIntegrationUtils"), &Z_Registration_Info_UClass_UAuracronPCGPythonIntegrationUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPythonIntegrationUtils), 1814197524U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_1235309035(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPythonBindings_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
