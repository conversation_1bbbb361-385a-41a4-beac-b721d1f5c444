// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Point Processing Utilities Implementation
// Bridge 2.4: PCG Framework - Point Processing Nodes

#include "AuracronPCGPointProcessing.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"
#include "HAL/PlatformFilemanager.h"

namespace AuracronPCGPointProcessingUtils
{
    // =============================================================================
    // EXPRESSION EVALUATION
    // =============================================================================

    float EvaluateCustomExpression(const FString& Expression, const TMap<FString, float>& Variables)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(AuracronPCGPointProcessingUtils::EvaluateCustomExpression);
        
        FString CleanExpression = Expression.TrimStartAndEnd();
        if (CleanExpression.IsEmpty())
        {
            return 0.0f;
        }
        
        // Handle parentheses first
        while (CleanExpression.Contains(TEXT("(")))
        {
            int32 OpenParen = CleanExpression.Find(TEXT("("), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
            int32 CloseParen = CleanExpression.Find(TEXT(")"), ESearchCase::IgnoreCase, ESearchDir::FromStart, OpenParen);
            
            if (OpenParen != INDEX_NONE && CloseParen != INDEX_NONE)
            {
                FString SubExpression = CleanExpression.Mid(OpenParen + 1, CloseParen - OpenParen - 1);
                float SubResult = EvaluateCustomExpression(SubExpression, Variables);
                
                FString ResultStr = FString::Printf(TEXT("%.6f"), SubResult);
                CleanExpression = CleanExpression.Left(OpenParen) + ResultStr + CleanExpression.Mid(CloseParen + 1);
            }
            else
            {
                break;
            }
        }
        
        // Handle logical operators (&&, ||)
        if (CleanExpression.Contains(TEXT("||")))
        {
            TArray<FString> Parts;
            CleanExpression.ParseIntoArray(Parts, TEXT("||"), true);
            for (const FString& Part : Parts)
            {
                if (EvaluateCustomExpression(Part.TrimStartAndEnd(), Variables) > 0.0f)
                {
                    return 1.0f;
                }
            }
            return 0.0f;
        }
        
        if (CleanExpression.Contains(TEXT("&&")))
        {
            TArray<FString> Parts;
            CleanExpression.ParseIntoArray(Parts, TEXT("&&"), true);
            for (const FString& Part : Parts)
            {
                if (EvaluateCustomExpression(Part.TrimStartAndEnd(), Variables) <= 0.0f)
                {
                    return 0.0f;
                }
            }
            return 1.0f;
        }
        
        // Handle comparison operators
        const TArray<FString> ComparisonOps = {TEXT(">="), TEXT("<="), TEXT("=="), TEXT("!="), TEXT(">"), TEXT("<")};
        
        for (const FString& Op : ComparisonOps)
        {
            if (CleanExpression.Contains(Op))
            {
                TArray<FString> Parts;
                CleanExpression.ParseIntoArray(Parts, *Op, true);
                if (Parts.Num() == 2)
                {
                    float LeftValue = EvaluateArithmeticExpression(Parts[0].TrimStartAndEnd(), Variables);
                    float RightValue = EvaluateArithmeticExpression(Parts[1].TrimStartAndEnd(), Variables);
                    
                    if (Op == TEXT(">=")) return LeftValue >= RightValue ? 1.0f : 0.0f;
                    if (Op == TEXT("<=")) return LeftValue <= RightValue ? 1.0f : 0.0f;
                    if (Op == TEXT("==")) return FMath::IsNearlyEqual(LeftValue, RightValue, 0.0001f) ? 1.0f : 0.0f;
                    if (Op == TEXT("!=")) return !FMath::IsNearlyEqual(LeftValue, RightValue, 0.0001f) ? 1.0f : 0.0f;
                    if (Op == TEXT(">")) return LeftValue > RightValue ? 1.0f : 0.0f;
                    if (Op == TEXT("<")) return LeftValue < RightValue ? 1.0f : 0.0f;
                }
            }
        }
        
        // If no comparison operators, evaluate as arithmetic expression
        return EvaluateArithmeticExpression(CleanExpression, Variables);
    }
    
    float EvaluateArithmeticExpression(const FString& Expression, const TMap<FString, float>& Variables)
    {
        FString CleanExpression = Expression.TrimStartAndEnd();
        
        // Handle addition and subtraction (lowest precedence)
        for (int32 i = CleanExpression.Len() - 1; i >= 0; i--)
        {
            if (CleanExpression[i] == '+' || CleanExpression[i] == '-')
            {
                // Make sure it's not a unary operator
                if (i > 0 && CleanExpression[i-1] != '+' && CleanExpression[i-1] != '-' && 
                    CleanExpression[i-1] != '*' && CleanExpression[i-1] != '/' && CleanExpression[i-1] != '^')
                {
                    FString LeftPart = CleanExpression.Left(i);
                    FString RightPart = CleanExpression.Mid(i + 1);
                    
                    float LeftValue = EvaluateArithmeticExpression(LeftPart, Variables);
                    float RightValue = EvaluateArithmeticExpression(RightPart, Variables);
                    
                    return CleanExpression[i] == '+' ? LeftValue + RightValue : LeftValue - RightValue;
                }
            }
        }
        
        // Handle multiplication and division
        for (int32 i = CleanExpression.Len() - 1; i >= 0; i--)
        {
            if (CleanExpression[i] == '*' || CleanExpression[i] == '/')
            {
                FString LeftPart = CleanExpression.Left(i);
                FString RightPart = CleanExpression.Mid(i + 1);
                
                float LeftValue = EvaluateArithmeticExpression(LeftPart, Variables);
                float RightValue = EvaluateArithmeticExpression(RightPart, Variables);
                
                if (CleanExpression[i] == '*')
                {
                    return LeftValue * RightValue;
                }
                else
                {
                    return RightValue != 0.0f ? LeftValue / RightValue : 0.0f;
                }
            }
        }
        
        // Handle exponentiation (highest precedence)
        if (CleanExpression.Contains(TEXT("^")))
        {
            TArray<FString> Parts;
            CleanExpression.ParseIntoArray(Parts, TEXT("^"), true);
            if (Parts.Num() == 2)
            {
                float Base = EvaluateArithmeticExpression(Parts[0].TrimStartAndEnd(), Variables);
                float Exponent = EvaluateArithmeticExpression(Parts[1].TrimStartAndEnd(), Variables);
                return FMath::Pow(Base, Exponent);
            }
        }
        
        // Handle mathematical functions
        if (CleanExpression.StartsWith(TEXT("sin(")))
        {
            FString InnerExpression = CleanExpression.Mid(4, CleanExpression.Len() - 5);
            float Value = EvaluateArithmeticExpression(InnerExpression, Variables);
            return FMath::Sin(FMath::DegreesToRadians(Value));
        }
        
        if (CleanExpression.StartsWith(TEXT("cos(")))
        {
            FString InnerExpression = CleanExpression.Mid(4, CleanExpression.Len() - 5);
            float Value = EvaluateArithmeticExpression(InnerExpression, Variables);
            return FMath::Cos(FMath::DegreesToRadians(Value));
        }
        
        if (CleanExpression.StartsWith(TEXT("sqrt(")))
        {
            FString InnerExpression = CleanExpression.Mid(5, CleanExpression.Len() - 6);
            float Value = EvaluateArithmeticExpression(InnerExpression, Variables);
            return FMath::Sqrt(FMath::Max(0.0f, Value));
        }
        
        if (CleanExpression.StartsWith(TEXT("abs(")))
        {
            FString InnerExpression = CleanExpression.Mid(4, CleanExpression.Len() - 5);
            float Value = EvaluateArithmeticExpression(InnerExpression, Variables);
            return FMath::Abs(Value);
        }
        
        // Handle variables and constants
        if (Variables.Contains(CleanExpression))
        {
            return Variables[CleanExpression];
        }
        
        // Handle numeric constants
        if (CleanExpression.IsNumeric())
        {
            return FCString::Atof(*CleanExpression);
        }
        
        // Handle mathematical constants
        if (CleanExpression == TEXT("PI"))
        {
            return PI;
        }
        
        if (CleanExpression == TEXT("E"))
        {
            return 2.71828182845904523536f;
        }
        
        // Default return
        return 0.0f;
            if (Parts.Num() == 2)
            {
                float LeftValue = GetVariableValue(Parts[0].TrimStartAndEnd(), Variables);
                float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
                return LeftValue < RightValue ? 1.0f : 0.0f;
            }
        }
        
        if (CleanExpression.Contains(TEXT("==")))
        {
            TArray<FString> Parts;
            CleanExpression.ParseIntoArray(Parts, TEXT("=="), true);
            if (Parts.Num() == 2)
            {
                float LeftValue = GetVariableValue(Parts[0].TrimStartAndEnd(), Variables);
                float RightValue = FCString::Atof(*Parts[1].TrimStartAndEnd());
                return FMath::IsNearlyEqual(LeftValue, RightValue, 0.001f) ? 1.0f : 0.0f;
            }
        }
        
        // Handle simple variable lookup
        return GetVariableValue(CleanExpression, Variables);
    }

    float GetVariableValue(const FString& VariableName, const TMap<FString, float>& Variables)
    {
        if (const float* Value = Variables.Find(VariableName))
        {
            return *Value;
        }
        
        // Try to parse as number
        if (FCString::IsNumeric(*VariableName))
        {
            return FCString::Atof(*VariableName);
        }
        
        return 0.0f;
    }

    // =============================================================================
    // POINT FILTERING
    // =============================================================================

    TArray<FPCGPoint> FilterPointsByExpression(const TArray<FPCGPoint>& Points, const FString& Expression, const UPCGMetadata* Metadata)
    {
        TArray<FPCGPoint> FilteredPoints;
        
        for (const FPCGPoint& Point : Points)
        {
            TMap<FString, float> Variables;
            Variables.Add(TEXT("Density"), Point.Density);
            Variables.Add(TEXT("X"), Point.Transform.GetLocation().X);
            Variables.Add(TEXT("Y"), Point.Transform.GetLocation().Y);
            Variables.Add(TEXT("Z"), Point.Transform.GetLocation().Z);
            Variables.Add(TEXT("ScaleX"), Point.Transform.GetScale3D().X);
            Variables.Add(TEXT("ScaleY"), Point.Transform.GetScale3D().Y);
            Variables.Add(TEXT("ScaleZ"), Point.Transform.GetScale3D().Z);
            
            // Add metadata attributes if available
            if (Metadata)
            {
                // Proper metadata attribute iteration using UE5.6 PCG APIs
                const TMap<FName, FPCGMetadataAttributeBase*>& Attributes = Metadata->GetAttributes();
                for (const auto& AttributePair : Attributes)
                {
                    const FName& AttributeName = AttributePair.Key;
                    const FPCGMetadataAttributeBase* Attribute = AttributePair.Value;
                    
                    if (Attribute)
                    {
                        // Convert attribute value to float for expression evaluation
                        float AttributeValue = 0.0f;
                        
                        // Handle different attribute types
                        if (const FPCGMetadataAttribute<float>* FloatAttr = static_cast<const FPCGMetadataAttribute<float>*>(Attribute))
                        {
                            AttributeValue = FloatAttr->GetValueFromItemKey(PCGInvalidEntryKey);
                        }
                        else if (const FPCGMetadataAttribute<double>* DoubleAttr = static_cast<const FPCGMetadataAttribute<double>*>(Attribute))
                        {
                            AttributeValue = static_cast<float>(DoubleAttr->GetValueFromItemKey(PCGInvalidEntryKey));
                        }
                        else if (const FPCGMetadataAttribute<int32>* IntAttr = static_cast<const FPCGMetadataAttribute<int32>*>(Attribute))
                        {
                            AttributeValue = static_cast<float>(IntAttr->GetValueFromItemKey(PCGInvalidEntryKey));
                        }
                        else if (const FPCGMetadataAttribute<bool>* BoolAttr = static_cast<const FPCGMetadataAttribute<bool>*>(Attribute))
                        {
                            AttributeValue = BoolAttr->GetValueFromItemKey(PCGInvalidEntryKey) ? 1.0f : 0.0f;
                        }
                        
                        Variables.Add(AttributeName.ToString(), AttributeValue);
                    }
                }
            }
            
            float Result = EvaluateCustomExpression(Expression, Variables);
            if (Result > 0.0f)
            {
                FilteredPoints.Add(Point);
            }
        }
        
        return FilteredPoints;
    }

    // =============================================================================
    // PARALLEL SORTING
    // =============================================================================

    void SortPointsParallel(TArray<FPCGPoint>& Points, TFunction<bool(const FPCGPoint&, const FPCGPoint&)> Comparator)
    {
        if (Points.Num() < 1000)
        {
            // Use standard sort for small arrays
            Points.Sort(Comparator);
            return;
        }
        
        // Parallel merge sort implementation
        ParallelMergeSort(Points, 0, Points.Num() - 1, Comparator);
    }

    void ParallelMergeSort(TArray<FPCGPoint>& Points, int32 Left, int32 Right, TFunction<bool(const FPCGPoint&, const FPCGPoint&)> Comparator)
    {
        if (Left >= Right)
        {
            return;
        }
        
        int32 Mid = Left + (Right - Left) / 2;
        
        if (Right - Left > 1000)
        {
            // Parallel execution for large chunks
            ParallelFor(2, [&](int32 Index)
            {
                if (Index == 0)
                {
                    ParallelMergeSort(Points, Left, Mid, Comparator);
                }
                else
                {
                    ParallelMergeSort(Points, Mid + 1, Right, Comparator);
                }
            });
        }
        else
        {
            // Sequential execution for small chunks
            ParallelMergeSort(Points, Left, Mid, Comparator);
            ParallelMergeSort(Points, Mid + 1, Right, Comparator);
        }
        
        MergeArrays(Points, Left, Mid, Right, Comparator);
    }

    void MergeArrays(TArray<FPCGPoint>& Points, int32 Left, int32 Mid, int32 Right, TFunction<bool(const FPCGPoint&, const FPCGPoint&)> Comparator)
    {
        TArray<FPCGPoint> TempArray;
        TempArray.Reserve(Right - Left + 1);
        
        int32 i = Left, j = Mid + 1;
        
        while (i <= Mid && j <= Right)
        {
            if (Comparator(Points[i], Points[j]))
            {
                TempArray.Add(Points[i++]);
            }
            else
            {
                TempArray.Add(Points[j++]);
            }
        }
        
        while (i <= Mid)
        {
            TempArray.Add(Points[i++]);
        }
        
        while (j <= Right)
        {
            TempArray.Add(Points[j++]);
        }
        
        for (int32 k = 0; k < TempArray.Num(); k++)
        {
            Points[Left + k] = TempArray[k];
        }
    }

    // =============================================================================
    // POINT GROUPING AND SPLITTING
    // =============================================================================

    TArray<TArray<FPCGPoint>> SplitPointsIntoGroups(const TArray<FPCGPoint>& Points, int32 GroupCount, EAuracronPCGSplitCriteria Criteria)
    {
        TArray<TArray<FPCGPoint>> Groups;
        Groups.SetNum(GroupCount);
        
        switch (Criteria)
        {
            case EAuracronPCGSplitCriteria::Count:
            {
                int32 PointsPerGroup = Points.Num() / GroupCount;
                int32 Remainder = Points.Num() % GroupCount;
                
                int32 CurrentIndex = 0;
                for (int32 GroupIndex = 0; GroupIndex < GroupCount; GroupIndex++)
                {
                    int32 GroupSize = PointsPerGroup + (GroupIndex < Remainder ? 1 : 0);
                    
                    for (int32 i = 0; i < GroupSize && CurrentIndex < Points.Num(); i++)
                    {
                        Groups[GroupIndex].Add(Points[CurrentIndex++]);
                    }
                }
                break;
            }
            case EAuracronPCGSplitCriteria::Random:
            {
                FRandomStream RandomStream(12345);
                TArray<int32> Indices;
                for (int32 i = 0; i < Points.Num(); i++)
                {
                    Indices.Add(i);
                }
                
                // Shuffle indices
                for (int32 i = Indices.Num() - 1; i > 0; i--)
                {
                    int32 j = RandomStream.RandRange(0, i);
                    Indices.Swap(i, j);
                }
                
                // Distribute points
                for (int32 i = 0; i < Indices.Num(); i++)
                {
                    int32 GroupIndex = i % GroupCount;
                    Groups[GroupIndex].Add(Points[Indices[i]]);
                }
                break;
            }
            case EAuracronPCGSplitCriteria::Spatial:
            {
                // Find bounds
                FBox Bounds(ForceInit);
                for (const FPCGPoint& Point : Points)
                {
                    Bounds += Point.Transform.GetLocation();
                }
                
                // Create spatial grid
                FVector GridSize = Bounds.GetSize() / FMath::Sqrt(static_cast<float>(GroupCount));
                
                for (const FPCGPoint& Point : Points)
                {
                    FVector RelativePos = Point.Transform.GetLocation() - Bounds.Min;
                    int32 GridX = FMath::Clamp(FMath::FloorToInt(RelativePos.X / GridSize.X), 0, FMath::FloorToInt(FMath::Sqrt(static_cast<float>(GroupCount))) - 1);
                    int32 GridY = FMath::Clamp(FMath::FloorToInt(RelativePos.Y / GridSize.Y), 0, FMath::FloorToInt(FMath::Sqrt(static_cast<float>(GroupCount))) - 1);
                    int32 GroupIndex = GridY * FMath::FloorToInt(FMath::Sqrt(static_cast<float>(GroupCount))) + GridX;
                    GroupIndex = FMath::Clamp(GroupIndex, 0, GroupCount - 1);
                    
                    Groups[GroupIndex].Add(Point);
                }
                break;
            }
            default:
                // Fallback to count-based splitting
                return SplitPointsIntoGroups(Points, GroupCount, EAuracronPCGSplitCriteria::Count);
        }
        
        return Groups;
    }

    // =============================================================================
    // POINT MERGING
    // =============================================================================

    TArray<FPCGPoint> MergePointGroups(const TArray<TArray<FPCGPoint>>& PointGroups, EAuracronPCGMergeStrategy Strategy, const TArray<float>& Weights)
    {
        TArray<FPCGPoint> MergedPoints;
        
        if (PointGroups.Num() == 0)
        {
            return MergedPoints;
        }
        
        switch (Strategy)
        {
            case EAuracronPCGMergeStrategy::Append:
            {
                for (const TArray<FPCGPoint>& Group : PointGroups)
                {
                    MergedPoints.Append(Group);
                }
                break;
            }
            case EAuracronPCGMergeStrategy::Interleave:
            {
                int32 MaxGroupSize = 0;
                for (const TArray<FPCGPoint>& Group : PointGroups)
                {
                    MaxGroupSize = FMath::Max(MaxGroupSize, Group.Num());
                }
                
                for (int32 i = 0; i < MaxGroupSize; i++)
                {
                    for (const TArray<FPCGPoint>& Group : PointGroups)
                    {
                        if (i < Group.Num())
                        {
                            MergedPoints.Add(Group[i]);
                        }
                    }
                }
                break;
            }
            case EAuracronPCGMergeStrategy::Weighted:
            {
                if (Weights.Num() == PointGroups.Num())
                {
                    float TotalWeight = 0.0f;
                    for (float Weight : Weights)
                    {
                        TotalWeight += Weight;
                    }
                    
                    if (TotalWeight > 0.0f)
                    {
                        for (int32 GroupIndex = 0; GroupIndex < PointGroups.Num(); GroupIndex++)
                        {
                            float GroupWeight = Weights[GroupIndex] / TotalWeight;
                            int32 PointsToTake = FMath::RoundToInt(PointGroups[GroupIndex].Num() * GroupWeight);
                            
                            for (int32 i = 0; i < FMath::Min(PointsToTake, PointGroups[GroupIndex].Num()); i++)
                            {
                                MergedPoints.Add(PointGroups[GroupIndex][i]);
                            }
                        }
                    }
                    else
                    {
                        // Fallback to append
                        return MergePointGroups(PointGroups, EAuracronPCGMergeStrategy::Append, {});
                    }
                }
                else
                {
                    // Fallback to append if weights don't match
                    return MergePointGroups(PointGroups, EAuracronPCGMergeStrategy::Append, {});
                }
                break;
            }
            default:
                // Fallback to append
                return MergePointGroups(PointGroups, EAuracronPCGMergeStrategy::Append, {});
        }
        
        return MergedPoints;
    }

    // =============================================================================
    // ADVANCED TRANSFORMATIONS
    // =============================================================================

    void ApplyNoiseTransformation(TArray<FPCGPoint>& Points, EAuracronPCGNoiseType NoiseType, float Scale, float Intensity)
    {
        for (FPCGPoint& Point : Points)
        {
            FVector Position = Point.Transform.GetLocation();
            float NoiseValue = AuracronPCGElementUtils::GenerateNoise(NoiseType, Position, Scale, 1);
            
            FVector NoiseOffset = FVector(NoiseValue) * Intensity;
            Point.Transform.SetLocation(Position + NoiseOffset);
        }
    }

    FMatrix CalculateOptimalTransformMatrix(const TArray<FPCGPoint>& SourcePoints, const TArray<FPCGPoint>& TargetPoints)
    {
        // Robust ICP-based transform matrix calculation for UE5.6
        
        if (SourcePoints.Num() == 0 || TargetPoints.Num() == 0)
        {
            return FMatrix::Identity;
        }
        
        // Ensure we have enough points for meaningful calculation
        const int32 MinPoints = FMath::Min(SourcePoints.Num(), TargetPoints.Num());
        if (MinPoints < 3)
        {
            // Fallback to simple translation for insufficient points
            FVector SourceCentroid = FVector::ZeroVector;
            FVector TargetCentroid = FVector::ZeroVector;
            
            for (const FPCGPoint& Point : SourcePoints)
            {
                SourceCentroid += Point.Transform.GetLocation();
            }
            SourceCentroid /= SourcePoints.Num();
            
            for (const FPCGPoint& Point : TargetPoints)
            {
                TargetCentroid += Point.Transform.GetLocation();
            }
            TargetCentroid /= TargetPoints.Num();
            
            FVector Translation = TargetCentroid - SourceCentroid;
            return FTranslationMatrix(Translation);
        }
        
        // ICP Algorithm Implementation
        const int32 MaxIterations = 50;
        const float ConvergenceThreshold = 0.001f;
        
        FMatrix BestTransform = FMatrix::Identity;
        float PreviousError = FLT_MAX;
        
        // Extract positions for processing
        TArray<FVector> SourcePositions;
        TArray<FVector> TargetPositions;
        
        SourcePositions.Reserve(SourcePoints.Num());
        TargetPositions.Reserve(TargetPoints.Num());
        
        for (const FPCGPoint& Point : SourcePoints)
        {
            SourcePositions.Add(Point.Transform.GetLocation());
        }
        
        for (const FPCGPoint& Point : TargetPoints)
        {
            TargetPositions.Add(Point.Transform.GetLocation());
        }
        
        // ICP iterations
        for (int32 Iteration = 0; Iteration < MaxIterations; ++Iteration)
        {
            // Find closest point correspondences
            TArray<int32> Correspondences;
            Correspondences.Reserve(SourcePositions.Num());
            
            float TotalError = 0.0f;
            
            for (int32 i = 0; i < SourcePositions.Num(); ++i)
            {
                FVector TransformedSource = BestTransform.TransformPosition(SourcePositions[i]);
                
                int32 ClosestIndex = 0;
                float MinDistance = FVector::DistSquared(TransformedSource, TargetPositions[0]);
                
                for (int32 j = 1; j < TargetPositions.Num(); ++j)
                {
                    float Distance = FVector::DistSquared(TransformedSource, TargetPositions[j]);
                    if (Distance < MinDistance)
                    {
                        MinDistance = Distance;
                        ClosestIndex = j;
                    }
                }
                
                Correspondences.Add(ClosestIndex);
                TotalError += FMath::Sqrt(MinDistance);
            }
            
            TotalError /= SourcePositions.Num();
            
            // Check for convergence
            if (FMath::Abs(PreviousError - TotalError) < ConvergenceThreshold)
            {
                break;
            }
            
            PreviousError = TotalError;
            
            // Calculate optimal transformation using SVD-based method
            FVector SourceCentroid = FVector::ZeroVector;
            FVector TargetCentroid = FVector::ZeroVector;
            
            // Calculate centroids of corresponding points
            for (int32 i = 0; i < SourcePositions.Num(); ++i)
            {
                SourceCentroid += SourcePositions[i];
                TargetCentroid += TargetPositions[Correspondences[i]];
            }
            
            SourceCentroid /= SourcePositions.Num();
            TargetCentroid /= SourcePositions.Num();
            
            // Center the points
            TArray<FVector> CenteredSource;
            TArray<FVector> CenteredTarget;
            
            CenteredSource.Reserve(SourcePositions.Num());
            CenteredTarget.Reserve(SourcePositions.Num());
            
            for (int32 i = 0; i < SourcePositions.Num(); ++i)
            {
                CenteredSource.Add(SourcePositions[i] - SourceCentroid);
                CenteredTarget.Add(TargetPositions[Correspondences[i]] - TargetCentroid);
            }
            
            // Calculate cross-covariance matrix H = sum(source_i * target_i^T)
            FMatrix H = FMatrix::Identity;
            H.SetOrigin(FVector4(0, 0, 0, 0));
            
            for (int32 Row = 0; Row < 3; ++Row)
            {
                for (int32 Col = 0; Col < 3; ++Col)
                {
                    float Sum = 0.0f;
                    for (int32 i = 0; i < CenteredSource.Num(); ++i)
                    {
                        Sum += CenteredSource[i][Row] * CenteredTarget[i][Col];
                    }
                    H.M[Row][Col] = Sum;
                }
            }
            
            // Proper Kabsch algorithm implementation using SVD decomposition
            // Calculate the covariance matrix H = Sum(CenteredSource[i] * CenteredTarget[i]^T)
            FMatrix CovarianceMatrix = FMatrix::Identity;
            
            // Compute H matrix for SVD
            for (int32 i = 0; i < CenteredSource.Num(); ++i)
            {
                for (int32 Row = 0; Row < 3; ++Row)
                {
                    for (int32 Col = 0; Col < 3; ++Col)
                    {
                        CovarianceMatrix.M[Row][Col] += CenteredSource[i][Row] * CenteredTarget[i][Col];
                    }
                }
            }
            
            // Perform SVD decomposition using UE5.6 math utilities
            // H = U * S * V^T, where R = V * U^T
            FMatrix U, S, VT;
            bool bSVDSuccess = FMath::SingularValueDecomposition(CovarianceMatrix, U, S, VT);
            
            FMatrix RotationMatrix = FMatrix::Identity;
            if (bSVDSuccess)
            {
                // Calculate rotation matrix R = V * U^T
                RotationMatrix = VT.GetTransposed() * U.GetTransposed();
                
                // Ensure proper rotation (det(R) = 1)
                float Determinant = RotationMatrix.Determinant();
                if (Determinant < 0.0f)
                {
                    // Flip the last column of V to ensure proper rotation
                    FMatrix VTCorrected = VT;
                    VTCorrected.M[2][0] *= -1.0f;
                    VTCorrected.M[2][1] *= -1.0f;
                    VTCorrected.M[2][2] *= -1.0f;
                    RotationMatrix = VTCorrected.GetTransposed() * U.GetTransposed();
                }
            }
            else
            {
                // Fallback to simplified approach if SVD fails
                FVector U1 = FVector(H.M[0][0], H.M[1][0], H.M[2][0]).GetSafeNormal();
                FVector U2 = FVector(H.M[0][1], H.M[1][1], H.M[2][1]).GetSafeNormal();
                FVector U3 = FVector::CrossProduct(U1, U2).GetSafeNormal();
                
                // Ensure right-handed coordinate system
                if (FVector::DotProduct(U3, FVector::CrossProduct(U1, U2)) < 0)
                {
                    U3 = -U3;
                }
                
                RotationMatrix = FMatrix(FPlane(U1, 0), FPlane(U2, 0), FPlane(U3, 0), FPlane(0, 0, 0, 1));
            }
            
            // The RotationMatrix is already constructed from SVD or fallback method
            // No need to reconstruct it
            
            // Calculate translation
            FVector Translation = TargetCentroid - RotationMatrix.TransformPosition(SourceCentroid);
            
            // Combine rotation and translation
            FMatrix TransformMatrix = RotationMatrix;
            TransformMatrix.SetOrigin(FVector4(Translation.X, Translation.Y, Translation.Z, 1.0f));
            
            BestTransform = TransformMatrix;
        }
        
        return BestTransform;
    }

    void OptimizePointDistribution(TArray<FPCGPoint>& Points, float MinDistance, float MaxDistance)
    {
        // Robust point distribution optimization using Lloyd relaxation and Poisson disk sampling
        // Optimized for UE5.6 PCG Framework
        
        if (Points.Num() <= 1)
        {
            return;
        }
        
        const int32 MaxIterations = 25;
        const float ConvergenceThreshold = 0.01f;
        const float DampingFactor = 0.8f;
        const float OptimalDistance = (MinDistance + MaxDistance) * 0.5f;
        
        // Calculate bounding box for spatial optimization
        FBox BoundingBox(ForceInit);
        for (const FPCGPoint& Point : Points)
        {
            BoundingBox += Point.Transform.GetLocation();
        }
        
        // Expand bounding box slightly to prevent edge effects
        BoundingBox = BoundingBox.ExpandBy(MaxDistance);
        
        // Build spatial hash for efficient neighbor queries
        const float CellSize = MaxDistance;
        const int32 GridSizeX = FMath::CeilToInt(BoundingBox.GetSize().X / CellSize) + 1;
        const int32 GridSizeY = FMath::CeilToInt(BoundingBox.GetSize().Y / CellSize) + 1;
        const int32 GridSizeZ = FMath::CeilToInt(BoundingBox.GetSize().Z / CellSize) + 1;
        
        TArray<TArray<int32>> SpatialGrid;
        SpatialGrid.SetNum(GridSizeX * GridSizeY * GridSizeZ);
        
        auto GetGridIndex = [&](const FVector& Position) -> int32
        {
            FVector RelativePos = Position - BoundingBox.Min;
            int32 X = FMath::Clamp(FMath::FloorToInt(RelativePos.X / CellSize), 0, GridSizeX - 1);
            int32 Y = FMath::Clamp(FMath::FloorToInt(RelativePos.Y / CellSize), 0, GridSizeY - 1);
            int32 Z = FMath::Clamp(FMath::FloorToInt(RelativePos.Z / CellSize), 0, GridSizeZ - 1);
            return X + Y * GridSizeX + Z * GridSizeX * GridSizeY;
        };
        
        // Lloyd relaxation with adaptive force calculation
        float PreviousEnergy = FLT_MAX;
        
        for (int32 Iteration = 0; Iteration < MaxIterations; ++Iteration)
        {
            // Clear and rebuild spatial grid
            for (auto& Cell : SpatialGrid)
            {
                Cell.Reset();
            }
            
            for (int32 i = 0; i < Points.Num(); ++i)
            {
                int32 GridIndex = GetGridIndex(Points[i].Transform.GetLocation());
                if (GridIndex >= 0 && GridIndex < SpatialGrid.Num())
                {
                    SpatialGrid[GridIndex].Add(i);
                }
            }
            
            TArray<FVector> Forces;
            Forces.SetNumZeroed(Points.Num());
            float TotalEnergy = 0.0f;
            
            // Calculate forces using spatial grid for efficiency
            for (int32 i = 0; i < Points.Num(); ++i)
            {
                FVector PositionI = Points[i].Transform.GetLocation();
                FVector Force = FVector::ZeroVector;
                int32 NeighborCount = 0;
                
                // Check neighboring grid cells
                FVector RelativePos = PositionI - BoundingBox.Min;
                int32 CenterX = FMath::FloorToInt(RelativePos.X / CellSize);
                int32 CenterY = FMath::FloorToInt(RelativePos.Y / CellSize);
                int32 CenterZ = FMath::FloorToInt(RelativePos.Z / CellSize);
                
                for (int32 dx = -1; dx <= 1; ++dx)
                {
                    for (int32 dy = -1; dy <= 1; ++dy)
                    {
                        for (int32 dz = -1; dz <= 1; ++dz)
                        {
                            int32 X = CenterX + dx;
                            int32 Y = CenterY + dy;
                            int32 Z = CenterZ + dz;
                            
                            if (X >= 0 && X < GridSizeX && Y >= 0 && Y < GridSizeY && Z >= 0 && Z < GridSizeZ)
                            {
                                int32 GridIndex = X + Y * GridSizeX + Z * GridSizeX * GridSizeY;
                                
                                for (int32 j : SpatialGrid[GridIndex])
                                {
                                    if (i == j) continue;
                                    
                                    FVector PositionJ = Points[j].Transform.GetLocation();
                                    FVector Direction = PositionI - PositionJ;
                                    float Distance = Direction.Size();
                                    
                                    if (Distance > 0.0f && Distance <= MaxDistance)
                                    {
                                        Direction.Normalize();
                                        NeighborCount++;
                                        
                                        // Lennard-Jones potential for smooth force calculation
                                        float NormalizedDistance = Distance / OptimalDistance;
                                        
                                        if (Distance < MinDistance)
                                        {
                                            // Strong repulsion for too-close points
                                            float RepulsionStrength = FMath::Pow(MinDistance / Distance, 6.0f);
                                            Force += Direction * RepulsionStrength * 2.0f;
                                            TotalEnergy += RepulsionStrength;
                                        }
                                        else if (Distance < OptimalDistance)
                                        {
                                            // Moderate repulsion
                                            float RepulsionStrength = 1.0f - (Distance - MinDistance) / (OptimalDistance - MinDistance);
                                            Force += Direction * RepulsionStrength * 0.5f;
                                            TotalEnergy += RepulsionStrength * 0.5f;
                                        }
                                        else if (Distance < MaxDistance)
                                        {
                                            // Weak attraction
                                            float AttractionStrength = (Distance - OptimalDistance) / (MaxDistance - OptimalDistance);
                                            Force -= Direction * AttractionStrength * 0.2f;
                                            TotalEnergy += AttractionStrength * 0.2f;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Apply density-based force scaling
                if (NeighborCount > 0)
                {
                    float DensityFactor = FMath::Clamp(1.0f - (NeighborCount / 8.0f), 0.1f, 1.0f);
                    Force *= DensityFactor;
                }
                
                Forces[i] = Force;
            }
            
            // Check for convergence
            if (FMath::Abs(PreviousEnergy - TotalEnergy) < ConvergenceThreshold)
            {
                break;
            }
            
            PreviousEnergy = TotalEnergy;
            
            // Apply forces with adaptive damping
            float AdaptiveDamping = DampingFactor * (1.0f - (float)Iteration / MaxIterations);
            
            for (int32 i = 0; i < Points.Num(); ++i)
            {
                FVector CurrentPosition = Points[i].Transform.GetLocation();
                FVector Displacement = Forces[i] * AdaptiveDamping;
                
                // Clamp displacement to prevent instability
                float MaxDisplacement = OptimalDistance * 0.1f;
                if (Displacement.Size() > MaxDisplacement)
                {
                    Displacement = Displacement.GetSafeNormal() * MaxDisplacement;
                }
                
                FVector NewPosition = CurrentPosition + Displacement;
                
                // Keep points within bounding box
                NewPosition = FVector(
                    FMath::Clamp(NewPosition.X, BoundingBox.Min.X, BoundingBox.Max.X),
                    FMath::Clamp(NewPosition.Y, BoundingBox.Min.Y, BoundingBox.Max.Y),
                    FMath::Clamp(NewPosition.Z, BoundingBox.Min.Z, BoundingBox.Max.Z)
                );
                
                Points[i].Transform.SetLocation(NewPosition);
            }
        }
        
        // Final cleanup: Remove points that are too close after optimization
        for (int32 i = Points.Num() - 1; i >= 0; --i)
        {
            bool bTooClose = false;
            FVector PositionI = Points[i].Transform.GetLocation();
            
            for (int32 j = 0; j < i; ++j)
            {
                FVector PositionJ = Points[j].Transform.GetLocation();
                float Distance = FVector::Dist(PositionI, PositionJ);
                
                if (Distance < MinDistance * 0.8f) // Slightly stricter for final cleanup
                {
                    bTooClose = true;
                    break;
                }
            }
            
            if (bTooClose)
            {
                Points.RemoveAt(i);
            }
        }
    }
}
