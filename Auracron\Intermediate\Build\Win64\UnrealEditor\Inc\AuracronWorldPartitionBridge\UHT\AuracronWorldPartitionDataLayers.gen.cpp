// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionDataLayers.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionDataLayers() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronDataLayerManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronDataLayerManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerCondition();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerInfo();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerStatistics();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UDataLayerSubsystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronDataLayerState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDataLayerState;
static UEnum* EAuracronDataLayerState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDataLayerState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDataLayerState"));
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerState>()
{
	return EAuracronDataLayerState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronDataLayerState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronDataLayerState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronDataLayerState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronDataLayerState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronDataLayerState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDataLayerState::Unloaded", (int64)EAuracronDataLayerState::Unloaded },
		{ "EAuracronDataLayerState::Loading", (int64)EAuracronDataLayerState::Loading },
		{ "EAuracronDataLayerState::Loaded", (int64)EAuracronDataLayerState::Loaded },
		{ "EAuracronDataLayerState::Unloading", (int64)EAuracronDataLayerState::Unloading },
		{ "EAuracronDataLayerState::Failed", (int64)EAuracronDataLayerState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDataLayerState",
	"EAuracronDataLayerState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDataLayerState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerState.InnerSingleton;
}
// ********** End Enum EAuracronDataLayerState *****************************************************

// ********** Begin Enum EAuracronDataLayerType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDataLayerType;
static UEnum* EAuracronDataLayerType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDataLayerType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDataLayerType"));
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerType>()
{
	return EAuracronDataLayerType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer types\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronDataLayerType::Dynamic" },
		{ "Editor.DisplayName", "Editor" },
		{ "Editor.Name", "EAuracronDataLayerType::Editor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
		{ "Runtime.DisplayName", "Runtime" },
		{ "Runtime.Name", "EAuracronDataLayerType::Runtime" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronDataLayerType::Static" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronDataLayerType::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDataLayerType::Runtime", (int64)EAuracronDataLayerType::Runtime },
		{ "EAuracronDataLayerType::Editor", (int64)EAuracronDataLayerType::Editor },
		{ "EAuracronDataLayerType::Streaming", (int64)EAuracronDataLayerType::Streaming },
		{ "EAuracronDataLayerType::Static", (int64)EAuracronDataLayerType::Static },
		{ "EAuracronDataLayerType::Dynamic", (int64)EAuracronDataLayerType::Dynamic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDataLayerType",
	"EAuracronDataLayerType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDataLayerType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerType.InnerSingleton;
}
// ********** End Enum EAuracronDataLayerType ******************************************************

// ********** Begin Enum EAuracronDataLayerVisibility **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDataLayerVisibility;
static UEnum* EAuracronDataLayerVisibility_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDataLayerVisibility"));
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerVisibility>()
{
	return EAuracronDataLayerVisibility_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer visibility\n" },
#endif
		{ "Hidden.DisplayName", "Hidden" },
		{ "Hidden.Name", "EAuracronDataLayerVisibility::Hidden" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
		{ "PartiallyVisible.DisplayName", "Partially Visible" },
		{ "PartiallyVisible.Name", "EAuracronDataLayerVisibility::PartiallyVisible" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer visibility" },
#endif
		{ "Transitioning.DisplayName", "Transitioning" },
		{ "Transitioning.Name", "EAuracronDataLayerVisibility::Transitioning" },
		{ "Visible.DisplayName", "Visible" },
		{ "Visible.Name", "EAuracronDataLayerVisibility::Visible" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDataLayerVisibility::Hidden", (int64)EAuracronDataLayerVisibility::Hidden },
		{ "EAuracronDataLayerVisibility::Visible", (int64)EAuracronDataLayerVisibility::Visible },
		{ "EAuracronDataLayerVisibility::PartiallyVisible", (int64)EAuracronDataLayerVisibility::PartiallyVisible },
		{ "EAuracronDataLayerVisibility::Transitioning", (int64)EAuracronDataLayerVisibility::Transitioning },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDataLayerVisibility",
	"EAuracronDataLayerVisibility",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerVisibility.InnerSingleton;
}
// ********** End Enum EAuracronDataLayerVisibility ************************************************

// ********** Begin Enum EAuracronDataLayerPriority ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDataLayerPriority;
static UEnum* EAuracronDataLayerPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDataLayerPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDataLayerPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerPriority.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerPriority>()
{
	return EAuracronDataLayerPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer priority\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronDataLayerPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronDataLayerPriority::High" },
		{ "Highest.DisplayName", "Highest" },
		{ "Highest.Name", "EAuracronDataLayerPriority::Highest" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronDataLayerPriority::Low" },
		{ "Lowest.DisplayName", "Lowest" },
		{ "Lowest.Name", "EAuracronDataLayerPriority::Lowest" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronDataLayerPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer priority" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDataLayerPriority::Lowest", (int64)EAuracronDataLayerPriority::Lowest },
		{ "EAuracronDataLayerPriority::Low", (int64)EAuracronDataLayerPriority::Low },
		{ "EAuracronDataLayerPriority::Normal", (int64)EAuracronDataLayerPriority::Normal },
		{ "EAuracronDataLayerPriority::High", (int64)EAuracronDataLayerPriority::High },
		{ "EAuracronDataLayerPriority::Highest", (int64)EAuracronDataLayerPriority::Highest },
		{ "EAuracronDataLayerPriority::Critical", (int64)EAuracronDataLayerPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDataLayerPriority",
	"EAuracronDataLayerPriority",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronDataLayerPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDataLayerPriority.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDataLayerPriority.InnerSingleton;
}
// ********** End Enum EAuracronDataLayerPriority **************************************************

// ********** Begin ScriptStruct FAuracronDataLayerConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration;
class UScriptStruct* FAuracronDataLayerConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDataLayerConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Layer Configuration\n * Configuration settings for data layer management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer Configuration\nConfiguration settings for data layer management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDataLayers_MetaData[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRuntimeSwitching_MetaData[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableConditionalLoading_MetaData[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLayerOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerTransitionTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLayerBlending_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLayerMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLayerCaching_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLayerDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogLayerOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDataLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDataLayers;
	static void NewProp_bEnableRuntimeSwitching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRuntimeSwitching;
	static void NewProp_bEnableConditionalLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableConditionalLoading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLayerOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerTransitionTime;
	static void NewProp_bEnableLayerBlending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLayerBlending;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLayerMemoryUsageMB;
	static void NewProp_bEnableLayerCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLayerCaching;
	static void NewProp_bEnableLayerDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLayerDebug;
	static void NewProp_bLogLayerOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogLayerOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDataLayerConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableDataLayers_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableDataLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableDataLayers = { "bEnableDataLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableDataLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDataLayers_MetaData), NewProp_bEnableDataLayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableRuntimeSwitching_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableRuntimeSwitching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableRuntimeSwitching = { "bEnableRuntimeSwitching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableRuntimeSwitching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRuntimeSwitching_MetaData), NewProp_bEnableRuntimeSwitching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableConditionalLoading_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableConditionalLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableConditionalLoading = { "bEnableConditionalLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableConditionalLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableConditionalLoading_MetaData), NewProp_bEnableConditionalLoading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_MaxConcurrentLayerOperations = { "MaxConcurrentLayerOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerConfiguration, MaxConcurrentLayerOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLayerOperations_MetaData), NewProp_MaxConcurrentLayerOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_LayerTransitionTime = { "LayerTransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerConfiguration, LayerTransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerTransitionTime_MetaData), NewProp_LayerTransitionTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerBlending_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableLayerBlending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerBlending = { "bEnableLayerBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerBlending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLayerBlending_MetaData), NewProp_bEnableLayerBlending_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_MaxLayerMemoryUsageMB = { "MaxLayerMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerConfiguration, MaxLayerMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLayerMemoryUsageMB_MetaData), NewProp_MaxLayerMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerCaching_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableLayerCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerCaching = { "bEnableLayerCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLayerCaching_MetaData), NewProp_bEnableLayerCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerDebug_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bEnableLayerDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerDebug = { "bEnableLayerDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLayerDebug_MetaData), NewProp_bEnableLayerDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bLogLayerOperations_SetBit(void* Obj)
{
	((FAuracronDataLayerConfiguration*)Obj)->bLogLayerOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bLogLayerOperations = { "bLogLayerOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerConfiguration), &Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bLogLayerOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogLayerOperations_MetaData), NewProp_bLogLayerOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableDataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableRuntimeSwitching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableConditionalLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_MaxConcurrentLayerOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_LayerTransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerBlending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_MaxLayerMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bEnableLayerDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewProp_bLogLayerOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDataLayerConfiguration",
	Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::PropPointers),
	sizeof(FAuracronDataLayerConfiguration),
	alignof(FAuracronDataLayerConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDataLayerConfiguration *************************************

// ********** Begin ScriptStruct FAuracronDataLayerInfo ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo;
class UScriptStruct* FAuracronDataLayerInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDataLayerInfo, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDataLayerInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Layer Info\n * Information about a data layer\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer Info\nInformation about a data layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerLabel_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerType_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Visibility_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRuntimeToggleable_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitiallyLoaded_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorCount_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dependencies_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModifiedTime_MetaData[] = {
		{ "Category", "Layer Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerLabel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Visibility_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Visibility;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bIsRuntimeToggleable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRuntimeToggleable;
	static void NewProp_bIsInitiallyLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitiallyLoaded;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorCount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Dependencies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Dependencies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModifiedTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDataLayerInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerLabel = { "LayerLabel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, LayerLabel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerLabel_MetaData), NewProp_LayerLabel_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerType = { "LayerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, LayerType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerType_MetaData), NewProp_LayerType_MetaData) }; // 189093178
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, State), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 2619166922
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Visibility_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Visibility = { "Visibility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, Visibility), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Visibility_MetaData), NewProp_Visibility_MetaData) }; // 3787628279
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, Priority), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 3380752196
void Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsRuntimeToggleable_SetBit(void* Obj)
{
	((FAuracronDataLayerInfo*)Obj)->bIsRuntimeToggleable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsRuntimeToggleable = { "bIsRuntimeToggleable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerInfo), &Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsRuntimeToggleable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRuntimeToggleable_MetaData), NewProp_bIsRuntimeToggleable_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsInitiallyLoaded_SetBit(void* Obj)
{
	((FAuracronDataLayerInfo*)Obj)->bIsInitiallyLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsInitiallyLoaded = { "bIsInitiallyLoaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerInfo), &Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsInitiallyLoaded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitiallyLoaded_MetaData), NewProp_bIsInitiallyLoaded_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_ActorCount = { "ActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, ActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorCount_MetaData), NewProp_ActorCount_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Dependencies_Inner = { "Dependencies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Dependencies = { "Dependencies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, Dependencies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dependencies_MetaData), NewProp_Dependencies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LastModifiedTime = { "LastModifiedTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerInfo, LastModifiedTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModifiedTime_MetaData), NewProp_LastModifiedTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerLabel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LayerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Visibility_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Visibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsRuntimeToggleable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_bIsInitiallyLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_ActorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Dependencies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Dependencies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewProp_LastModifiedTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDataLayerInfo",
	Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::PropPointers),
	sizeof(FAuracronDataLayerInfo),
	alignof(FAuracronDataLayerInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDataLayerInfo **********************************************

// ********** Begin ScriptStruct FAuracronDataLayerCondition ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition;
class UScriptStruct* FAuracronDataLayerCondition::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDataLayerCondition, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDataLayerCondition"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Layer Condition\n * Condition for conditional layer loading\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer Condition\nCondition for conditional layer loading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionName_MetaData[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionExpression_MetaData[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvaluationInterval_MetaData[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionExpression;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvaluationInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDataLayerCondition>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_ConditionName = { "ConditionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerCondition, ConditionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionName_MetaData), NewProp_ConditionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_ConditionExpression = { "ConditionExpression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerCondition, ConditionExpression), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionExpression_MetaData), NewProp_ConditionExpression_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerCondition, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronDataLayerCondition*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDataLayerCondition), &Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_EvaluationInterval = { "EvaluationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerCondition, EvaluationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvaluationInterval_MetaData), NewProp_EvaluationInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_ConditionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_ConditionExpression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewProp_EvaluationInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDataLayerCondition",
	Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::PropPointers),
	sizeof(FAuracronDataLayerCondition),
	alignof(FAuracronDataLayerCondition),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerCondition()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDataLayerCondition *****************************************

// ********** Begin ScriptStruct FAuracronDataLayerStatistics **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics;
class UScriptStruct* FAuracronDataLayerStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDataLayerStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDataLayerStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Layer Statistics\n * Performance and usage statistics for data layers\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer Statistics\nPerformance and usage statistics for data layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLayers_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedLayers_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibleLayers_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerSwitches_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLayers;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedLayers;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VisibleLayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerSwitches;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDataLayerStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_TotalLayers = { "TotalLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, TotalLayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLayers_MetaData), NewProp_TotalLayers_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LoadedLayers = { "LoadedLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, LoadedLayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedLayers_MetaData), NewProp_LoadedLayers_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_VisibleLayers = { "VisibleLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, VisibleLayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibleLayers_MetaData), NewProp_VisibleLayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LayerSwitches = { "LayerSwitches", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, LayerSwitches), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerSwitches_MetaData), NewProp_LayerSwitches_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LayerEfficiency = { "LayerEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, LayerEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerEfficiency_MetaData), NewProp_LayerEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDataLayerStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_TotalLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LoadedLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_VisibleLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LayerSwitches,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LayerEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDataLayerStatistics",
	Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::PropPointers),
	sizeof(FAuracronDataLayerStatistics),
	alignof(FAuracronDataLayerStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDataLayerStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDataLayerStatistics ****************************************

// ********** Begin Delegate FOnDataLayerStateChanged **********************************************
struct Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics
{
	struct AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms
	{
		FString LayerId;
		EAuracronDataLayerState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms, LayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState, METADATA_PARAMS(0, nullptr) }; // 2619166922
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "OnDataLayerStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronDataLayerManager::FOnDataLayerStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerStateChanged, const FString& LayerId, EAuracronDataLayerState NewState)
{
	struct AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms
	{
		FString LayerId;
		EAuracronDataLayerState NewState;
	};
	AuracronDataLayerManager_eventOnDataLayerStateChanged_Parms Parms;
	Parms.LayerId=LayerId;
	Parms.NewState=NewState;
	OnDataLayerStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDataLayerStateChanged ************************************************

// ********** Begin Delegate FOnDataLayerVisibilityChanged *****************************************
struct Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics
{
	struct AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms
	{
		FString LayerId;
		EAuracronDataLayerVisibility NewVisibility;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewVisibility_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewVisibility;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms, LayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_NewVisibility_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_NewVisibility = { "NewVisibility", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms, NewVisibility), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility, METADATA_PARAMS(0, nullptr) }; // 3787628279
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_NewVisibility_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::NewProp_NewVisibility,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "OnDataLayerVisibilityChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronDataLayerManager::FOnDataLayerVisibilityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerVisibilityChanged, const FString& LayerId, EAuracronDataLayerVisibility NewVisibility)
{
	struct AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms
	{
		FString LayerId;
		EAuracronDataLayerVisibility NewVisibility;
	};
	AuracronDataLayerManager_eventOnDataLayerVisibilityChanged_Parms Parms;
	Parms.LayerId=LayerId;
	Parms.NewVisibility=NewVisibility;
	OnDataLayerVisibilityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDataLayerVisibilityChanged *******************************************

// ********** Begin Delegate FOnDataLayerTransitionStarted *****************************************
struct Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics
{
	struct AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms
	{
		FString FromLayerId;
		FString ToLayerId;
		float TransitionTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FromLayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ToLayerId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_FromLayerId = { "FromLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms, FromLayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_ToLayerId = { "ToLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms, ToLayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms, TransitionTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_FromLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_ToLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::NewProp_TransitionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "OnDataLayerTransitionStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronDataLayerManager::FOnDataLayerTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerTransitionStarted, const FString& FromLayerId, const FString& ToLayerId, float TransitionTime)
{
	struct AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms
	{
		FString FromLayerId;
		FString ToLayerId;
		float TransitionTime;
	};
	AuracronDataLayerManager_eventOnDataLayerTransitionStarted_Parms Parms;
	Parms.FromLayerId=FromLayerId;
	Parms.ToLayerId=ToLayerId;
	Parms.TransitionTime=TransitionTime;
	OnDataLayerTransitionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDataLayerTransitionStarted *******************************************

// ********** Begin Delegate FOnDataLayerTransitionCompleted ***************************************
struct Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics
{
	struct AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms
	{
		FString FromLayerId;
		FString ToLayerId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FromLayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ToLayerId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::NewProp_FromLayerId = { "FromLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms, FromLayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::NewProp_ToLayerId = { "ToLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms, ToLayerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::NewProp_FromLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::NewProp_ToLayerId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "OnDataLayerTransitionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronDataLayerManager::FOnDataLayerTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerTransitionCompleted, const FString& FromLayerId, const FString& ToLayerId)
{
	struct AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms
	{
		FString FromLayerId;
		FString ToLayerId;
	};
	AuracronDataLayerManager_eventOnDataLayerTransitionCompleted_Parms Parms;
	Parms.FromLayerId=FromLayerId;
	Parms.ToLayerId=ToLayerId;
	OnDataLayerTransitionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDataLayerTransitionCompleted *****************************************

// ********** Begin Class UAuracronDataLayerManager Function AddLayerCondition *********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics
{
	struct AuracronDataLayerManager_eventAddLayerCondition_Parms
	{
		FString LayerId;
		FAuracronDataLayerCondition Condition;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Conditional loading\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conditional loading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Condition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Condition;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventAddLayerCondition_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_Condition = { "Condition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventAddLayerCondition_Parms, Condition), Z_Construct_UScriptStruct_FAuracronDataLayerCondition, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Condition_MetaData), NewProp_Condition_MetaData) }; // 931011943
void Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventAddLayerCondition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventAddLayerCondition_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_Condition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "AddLayerCondition", Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::AuracronDataLayerManager_eventAddLayerCondition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::AuracronDataLayerManager_eventAddLayerCondition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execAddLayerCondition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_STRUCT_REF(FAuracronDataLayerCondition,Z_Param_Out_Condition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddLayerCondition(Z_Param_LayerId,Z_Param_Out_Condition);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function AddLayerCondition ***********************

// ********** Begin Class UAuracronDataLayerManager Function AddLayerDependency ********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics
{
	struct AuracronDataLayerManager_eventAddLayerDependency_Parms
	{
		FString LayerId;
		FString DependencyLayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer dependencies\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer dependencies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DependencyLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DependencyLayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventAddLayerDependency_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_DependencyLayerId = { "DependencyLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventAddLayerDependency_Parms, DependencyLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DependencyLayerId_MetaData), NewProp_DependencyLayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventAddLayerDependency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventAddLayerDependency_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_DependencyLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "AddLayerDependency", Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::AuracronDataLayerManager_eventAddLayerDependency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::AuracronDataLayerManager_eventAddLayerDependency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execAddLayerDependency)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_PROPERTY(FStrProperty,Z_Param_DependencyLayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddLayerDependency(Z_Param_LayerId,Z_Param_DependencyLayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function AddLayerDependency **********************

// ********** Begin Class UAuracronDataLayerManager Function BlendDataLayers ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics
{
	struct AuracronDataLayerManager_eventBlendDataLayers_Parms
	{
		TArray<FString> LayerIds;
		TArray<float> BlendWeights;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIds_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerIds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendWeights;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_LayerIds_Inner = { "LayerIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_LayerIds = { "LayerIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventBlendDataLayers_Parms, LayerIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIds_MetaData), NewProp_LayerIds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_BlendWeights_Inner = { "BlendWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_BlendWeights = { "BlendWeights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventBlendDataLayers_Parms, BlendWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeights_MetaData), NewProp_BlendWeights_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventBlendDataLayers_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventBlendDataLayers_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_LayerIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_LayerIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_BlendWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_BlendWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "BlendDataLayers", Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::AuracronDataLayerManager_eventBlendDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::AuracronDataLayerManager_eventBlendDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execBlendDataLayers)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_LayerIds);
	P_GET_TARRAY_REF(float,Z_Param_Out_BlendWeights);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BlendDataLayers(Z_Param_Out_LayerIds,Z_Param_Out_BlendWeights);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function BlendDataLayers *************************

// ********** Begin Class UAuracronDataLayerManager Function CreateDataLayer ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics
{
	struct AuracronDataLayerManager_eventCreateDataLayer_Parms
	{
		FString LayerName;
		EAuracronDataLayerType LayerType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer creation and management\n" },
#endif
		{ "CPP_Default_LayerType", "Runtime" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventCreateDataLayer_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerType = { "LayerType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventCreateDataLayer_Parms, LayerType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType, METADATA_PARAMS(0, nullptr) }; // 189093178
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventCreateDataLayer_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_LayerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "CreateDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::AuracronDataLayerManager_eventCreateDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::AuracronDataLayerManager_eventCreateDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execCreateDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_GET_ENUM(EAuracronDataLayerType,Z_Param_LayerType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateDataLayer(Z_Param_LayerName,EAuracronDataLayerType(Z_Param_LayerType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function CreateDataLayer *************************

// ********** Begin Class UAuracronDataLayerManager Function DoesDataLayerExist ********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics
{
	struct AuracronDataLayerManager_eventDoesDataLayerExist_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventDoesDataLayerExist_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventDoesDataLayerExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventDoesDataLayerExist_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "DoesDataLayerExist", Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::AuracronDataLayerManager_eventDoesDataLayerExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::AuracronDataLayerManager_eventDoesDataLayerExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execDoesDataLayerExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesDataLayerExist(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function DoesDataLayerExist **********************

// ********** Begin Class UAuracronDataLayerManager Function DrawDebugLayerInfo ********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics
{
	struct AuracronDataLayerManager_eventDrawDebugLayerInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventDrawDebugLayerInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "DrawDebugLayerInfo", Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::AuracronDataLayerManager_eventDrawDebugLayerInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::AuracronDataLayerManager_eventDrawDebugLayerInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execDrawDebugLayerInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLayerInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function DrawDebugLayerInfo **********************

// ********** Begin Class UAuracronDataLayerManager Function EnableLayerDebug **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics
{
	struct AuracronDataLayerManager_eventEnableLayerDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventEnableLayerDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventEnableLayerDebug_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "EnableLayerDebug", Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::AuracronDataLayerManager_eventEnableLayerDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::AuracronDataLayerManager_eventEnableLayerDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execEnableLayerDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLayerDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function EnableLayerDebug ************************

// ********** Begin Class UAuracronDataLayerManager Function EvaluateAllConditions *****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "EvaluateAllConditions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execEvaluateAllConditions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EvaluateAllConditions();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function EvaluateAllConditions *******************

// ********** Begin Class UAuracronDataLayerManager Function GetAllDataLayers **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics
{
	struct AuracronDataLayerManager_eventGetAllDataLayers_Parms
	{
		TArray<FAuracronDataLayerInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDataLayerInfo, METADATA_PARAMS(0, nullptr) }; // 586382557
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetAllDataLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 586382557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetAllDataLayers", Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::AuracronDataLayerManager_eventGetAllDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::AuracronDataLayerManager_eventGetAllDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetAllDataLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDataLayerInfo>*)Z_Param__Result=P_THIS->GetAllDataLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetAllDataLayers ************************

// ********** Begin Class UAuracronDataLayerManager Function GetConfiguration **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics
{
	struct AuracronDataLayerManager_eventGetConfiguration_Parms
	{
		FAuracronDataLayerConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration, METADATA_PARAMS(0, nullptr) }; // 2828586530
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::AuracronDataLayerManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::AuracronDataLayerManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDataLayerConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetConfiguration ************************

// ********** Begin Class UAuracronDataLayerManager Function GetDataLayerInfo **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics
{
	struct AuracronDataLayerManager_eventGetDataLayerInfo_Parms
	{
		FString LayerId;
		FAuracronDataLayerInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerInfo_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDataLayerInfo, METADATA_PARAMS(0, nullptr) }; // 586382557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetDataLayerInfo", Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::AuracronDataLayerManager_eventGetDataLayerInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::AuracronDataLayerManager_eventGetDataLayerInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetDataLayerInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDataLayerInfo*)Z_Param__Result=P_THIS->GetDataLayerInfo(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetDataLayerInfo ************************

// ********** Begin Class UAuracronDataLayerManager Function GetDataLayerNames *********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics
{
	struct AuracronDataLayerManager_eventGetDataLayerNames_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerNames_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetDataLayerNames", Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::AuracronDataLayerManager_eventGetDataLayerNames_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::AuracronDataLayerManager_eventGetDataLayerNames_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetDataLayerNames)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetDataLayerNames();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetDataLayerNames ***********************

// ********** Begin Class UAuracronDataLayerManager Function GetDataLayerState *********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics
{
	struct AuracronDataLayerManager_eventGetDataLayerState_Parms
	{
		FString LayerId;
		EAuracronDataLayerState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerState_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState, METADATA_PARAMS(0, nullptr) }; // 2619166922
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetDataLayerState", Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::AuracronDataLayerManager_eventGetDataLayerState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::AuracronDataLayerManager_eventGetDataLayerState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetDataLayerState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronDataLayerState*)Z_Param__Result=P_THIS->GetDataLayerState(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetDataLayerState ***********************

// ********** Begin Class UAuracronDataLayerManager Function GetDataLayerStatistics ****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics
{
	struct AuracronDataLayerManager_eventGetDataLayerStatistics_Parms
	{
		FAuracronDataLayerStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDataLayerStatistics, METADATA_PARAMS(0, nullptr) }; // 2058732843
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetDataLayerStatistics", Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::AuracronDataLayerManager_eventGetDataLayerStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::AuracronDataLayerManager_eventGetDataLayerStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetDataLayerStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDataLayerStatistics*)Z_Param__Result=P_THIS->GetDataLayerStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetDataLayerStatistics ******************

// ********** Begin Class UAuracronDataLayerManager Function GetDataLayerVisibility ****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics
{
	struct AuracronDataLayerManager_eventGetDataLayerVisibility_Parms
	{
		FString LayerId;
		EAuracronDataLayerVisibility ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerVisibility_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetDataLayerVisibility_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility, METADATA_PARAMS(0, nullptr) }; // 3787628279
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetDataLayerVisibility", Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::AuracronDataLayerManager_eventGetDataLayerVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::AuracronDataLayerManager_eventGetDataLayerVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetDataLayerVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronDataLayerVisibility*)Z_Param__Result=P_THIS->GetDataLayerVisibility(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetDataLayerVisibility ******************

// ********** Begin Class UAuracronDataLayerManager Function GetInstance ***************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics
{
	struct AuracronDataLayerManager_eventGetInstance_Parms
	{
		UAuracronDataLayerManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronDataLayerManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::AuracronDataLayerManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::AuracronDataLayerManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronDataLayerManager**)Z_Param__Result=UAuracronDataLayerManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetInstance *****************************

// ********** Begin Class UAuracronDataLayerManager Function GetLayerConditions ********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics
{
	struct AuracronDataLayerManager_eventGetLayerConditions_Parms
	{
		FString LayerId;
		TArray<FAuracronDataLayerCondition> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayerConditions_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDataLayerCondition, METADATA_PARAMS(0, nullptr) }; // 931011943
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayerConditions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 931011943
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLayerConditions", Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::AuracronDataLayerManager_eventGetLayerConditions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::AuracronDataLayerManager_eventGetLayerConditions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLayerConditions)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDataLayerCondition>*)Z_Param__Result=P_THIS->GetLayerConditions(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLayerConditions **********************

// ********** Begin Class UAuracronDataLayerManager Function GetLayerDependencies ******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics
{
	struct AuracronDataLayerManager_eventGetLayerDependencies_Parms
	{
		FString LayerId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayerDependencies_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayerDependencies_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLayerDependencies", Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::AuracronDataLayerManager_eventGetLayerDependencies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::AuracronDataLayerManager_eventGetLayerDependencies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLayerDependencies)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLayerDependencies(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLayerDependencies ********************

// ********** Begin Class UAuracronDataLayerManager Function GetLayersByTag ************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics
{
	struct AuracronDataLayerManager_eventGetLayersByTag_Parms
	{
		FString Tag;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayersByTag_Parms, Tag), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayersByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLayersByTag", Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::AuracronDataLayerManager_eventGetLayersByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::AuracronDataLayerManager_eventGetLayersByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLayersByTag)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLayersByTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLayersByTag **************************

// ********** Begin Class UAuracronDataLayerManager Function GetLayersByType ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics
{
	struct AuracronDataLayerManager_eventGetLayersByType_Parms
	{
		EAuracronDataLayerType LayerType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_LayerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_LayerType = { "LayerType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayersByType_Parms, LayerType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerType, METADATA_PARAMS(0, nullptr) }; // 189093178
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLayersByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_LayerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_LayerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLayersByType", Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::AuracronDataLayerManager_eventGetLayersByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::AuracronDataLayerManager_eventGetLayersByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLayersByType)
{
	P_GET_ENUM(EAuracronDataLayerType,Z_Param_LayerType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLayersByType(EAuracronDataLayerType(Z_Param_LayerType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLayersByType *************************

// ********** Begin Class UAuracronDataLayerManager Function GetLoadedLayerCount *******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics
{
	struct AuracronDataLayerManager_eventGetLoadedLayerCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLoadedLayerCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLoadedLayerCount", Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::AuracronDataLayerManager_eventGetLoadedLayerCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::AuracronDataLayerManager_eventGetLoadedLayerCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLoadedLayerCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedLayerCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLoadedLayerCount *********************

// ********** Begin Class UAuracronDataLayerManager Function GetLoadedLayers ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics
{
	struct AuracronDataLayerManager_eventGetLoadedLayers_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetLoadedLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetLoadedLayers", Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::AuracronDataLayerManager_eventGetLoadedLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::AuracronDataLayerManager_eventGetLoadedLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetLoadedLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetLoadedLayers *************************

// ********** Begin Class UAuracronDataLayerManager Function GetTotalLayerCount ********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics
{
	struct AuracronDataLayerManager_eventGetTotalLayerCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetTotalLayerCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetTotalLayerCount", Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::AuracronDataLayerManager_eventGetTotalLayerCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::AuracronDataLayerManager_eventGetTotalLayerCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetTotalLayerCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalLayerCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetTotalLayerCount **********************

// ********** Begin Class UAuracronDataLayerManager Function GetTotalMemoryUsage *******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics
{
	struct AuracronDataLayerManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::AuracronDataLayerManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::AuracronDataLayerManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetTotalMemoryUsage *********************

// ********** Begin Class UAuracronDataLayerManager Function GetVisibleLayers **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics
{
	struct AuracronDataLayerManager_eventGetVisibleLayers_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventGetVisibleLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "GetVisibleLayers", Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::AuracronDataLayerManager_eventGetVisibleLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::AuracronDataLayerManager_eventGetVisibleLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execGetVisibleLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetVisibleLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function GetVisibleLayers ************************

// ********** Begin Class UAuracronDataLayerManager Function HideDataLayer *************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics
{
	struct AuracronDataLayerManager_eventHideDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventHideDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventHideDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventHideDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "HideDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::AuracronDataLayerManager_eventHideDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::AuracronDataLayerManager_eventHideDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execHideDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HideDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function HideDataLayer ***************************

// ********** Begin Class UAuracronDataLayerManager Function Initialize ****************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics
{
	struct AuracronDataLayerManager_eventInitialize_Parms
	{
		FAuracronDataLayerConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2828586530
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::AuracronDataLayerManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::AuracronDataLayerManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronDataLayerConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function Initialize ******************************

// ********** Begin Class UAuracronDataLayerManager Function IsInitialized *************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics
{
	struct AuracronDataLayerManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::AuracronDataLayerManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::AuracronDataLayerManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function IsInitialized ***************************

// ********** Begin Class UAuracronDataLayerManager Function IsLayerDebugEnabled *******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics
{
	struct AuracronDataLayerManager_eventIsLayerDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventIsLayerDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventIsLayerDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "IsLayerDebugEnabled", Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::AuracronDataLayerManager_eventIsLayerDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::AuracronDataLayerManager_eventIsLayerDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execIsLayerDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLayerDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function IsLayerDebugEnabled *********************

// ********** Begin Class UAuracronDataLayerManager Function LoadDataLayer *************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics
{
	struct AuracronDataLayerManager_eventLoadDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventLoadDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventLoadDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventLoadDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "LoadDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::AuracronDataLayerManager_eventLoadDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::AuracronDataLayerManager_eventLoadDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execLoadDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function LoadDataLayer ***************************

// ********** Begin Class UAuracronDataLayerManager Function LogLayerState *************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "LogLayerState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execLogLayerState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLayerState();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function LogLayerState ***************************

// ********** Begin Class UAuracronDataLayerManager Function RemoveDataLayer ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics
{
	struct AuracronDataLayerManager_eventRemoveDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventRemoveDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventRemoveDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventRemoveDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "RemoveDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::AuracronDataLayerManager_eventRemoveDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::AuracronDataLayerManager_eventRemoveDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execRemoveDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function RemoveDataLayer *************************

// ********** Begin Class UAuracronDataLayerManager Function RemoveLayerCondition ******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics
{
	struct AuracronDataLayerManager_eventRemoveLayerCondition_Parms
	{
		FString LayerId;
		FString ConditionName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventRemoveLayerCondition_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ConditionName = { "ConditionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventRemoveLayerCondition_Parms, ConditionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionName_MetaData), NewProp_ConditionName_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventRemoveLayerCondition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventRemoveLayerCondition_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ConditionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "RemoveLayerCondition", Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::AuracronDataLayerManager_eventRemoveLayerCondition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::AuracronDataLayerManager_eventRemoveLayerCondition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execRemoveLayerCondition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ConditionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLayerCondition(Z_Param_LayerId,Z_Param_ConditionName);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function RemoveLayerCondition ********************

// ********** Begin Class UAuracronDataLayerManager Function RemoveLayerDependency *****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics
{
	struct AuracronDataLayerManager_eventRemoveLayerDependency_Parms
	{
		FString LayerId;
		FString DependencyLayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DependencyLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DependencyLayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventRemoveLayerDependency_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_DependencyLayerId = { "DependencyLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventRemoveLayerDependency_Parms, DependencyLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DependencyLayerId_MetaData), NewProp_DependencyLayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventRemoveLayerDependency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventRemoveLayerDependency_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_DependencyLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "RemoveLayerDependency", Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::AuracronDataLayerManager_eventRemoveLayerDependency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::AuracronDataLayerManager_eventRemoveLayerDependency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execRemoveLayerDependency)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_PROPERTY(FStrProperty,Z_Param_DependencyLayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLayerDependency(Z_Param_LayerId,Z_Param_DependencyLayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function RemoveLayerDependency *******************

// ********** Begin Class UAuracronDataLayerManager Function ResetStatistics ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function ResetStatistics *************************

// ********** Begin Class UAuracronDataLayerManager Function SetConfiguration **********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics
{
	struct AuracronDataLayerManager_eventSetConfiguration_Parms
	{
		FAuracronDataLayerConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2828586530
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::AuracronDataLayerManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::AuracronDataLayerManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronDataLayerConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function SetConfiguration ************************

// ********** Begin Class UAuracronDataLayerManager Function SetDataLayerState *********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics
{
	struct AuracronDataLayerManager_eventSetDataLayerState_Parms
	{
		FString LayerId;
		EAuracronDataLayerState NewState;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer state management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer state management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSetDataLayerState_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSetDataLayerState_Parms, NewState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerState, METADATA_PARAMS(0, nullptr) }; // 2619166922
void Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventSetDataLayerState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventSetDataLayerState_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_NewState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "SetDataLayerState", Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::AuracronDataLayerManager_eventSetDataLayerState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::AuracronDataLayerManager_eventSetDataLayerState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execSetDataLayerState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_ENUM(EAuracronDataLayerState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetDataLayerState(Z_Param_LayerId,EAuracronDataLayerState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function SetDataLayerState ***********************

// ********** Begin Class UAuracronDataLayerManager Function SetDataLayerVisibility ****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics
{
	struct AuracronDataLayerManager_eventSetDataLayerVisibility_Parms
	{
		FString LayerId;
		EAuracronDataLayerVisibility Visibility;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer visibility management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer visibility management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Visibility_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Visibility;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSetDataLayerVisibility_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_Visibility_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_Visibility = { "Visibility", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSetDataLayerVisibility_Parms, Visibility), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDataLayerVisibility, METADATA_PARAMS(0, nullptr) }; // 3787628279
void Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventSetDataLayerVisibility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventSetDataLayerVisibility_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_Visibility_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_Visibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "SetDataLayerVisibility", Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::AuracronDataLayerManager_eventSetDataLayerVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::AuracronDataLayerManager_eventSetDataLayerVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execSetDataLayerVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_GET_ENUM(EAuracronDataLayerVisibility,Z_Param_Visibility);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetDataLayerVisibility(Z_Param_LayerId,EAuracronDataLayerVisibility(Z_Param_Visibility));
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function SetDataLayerVisibility ******************

// ********** Begin Class UAuracronDataLayerManager Function ShowDataLayer *************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics
{
	struct AuracronDataLayerManager_eventShowDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventShowDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventShowDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventShowDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "ShowDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::AuracronDataLayerManager_eventShowDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::AuracronDataLayerManager_eventShowDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execShowDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShowDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function ShowDataLayer ***************************

// ********** Begin Class UAuracronDataLayerManager Function Shutdown ******************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function Shutdown ********************************

// ********** Begin Class UAuracronDataLayerManager Function SwitchToDataLayerSet ******************
struct Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics
{
	struct AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms
	{
		TArray<FString> LayerIds;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Runtime switching\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Runtime switching" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIds_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerIds;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_LayerIds_Inner = { "LayerIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_LayerIds = { "LayerIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms, LayerIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIds_MetaData), NewProp_LayerIds_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_LayerIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_LayerIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "SwitchToDataLayerSet", Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::AuracronDataLayerManager_eventSwitchToDataLayerSet_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execSwitchToDataLayerSet)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_LayerIds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SwitchToDataLayerSet(Z_Param_Out_LayerIds);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function SwitchToDataLayerSet ********************

// ********** Begin Class UAuracronDataLayerManager Function Tick **********************************
struct Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics
{
	struct AuracronDataLayerManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::AuracronDataLayerManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::AuracronDataLayerManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function Tick ************************************

// ********** Begin Class UAuracronDataLayerManager Function ToggleDataLayer ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics
{
	struct AuracronDataLayerManager_eventToggleDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventToggleDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventToggleDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventToggleDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "ToggleDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::AuracronDataLayerManager_eventToggleDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::AuracronDataLayerManager_eventToggleDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execToggleDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ToggleDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function ToggleDataLayer *************************

// ********** Begin Class UAuracronDataLayerManager Function TransitionToDataLayer *****************
struct Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics
{
	struct AuracronDataLayerManager_eventTransitionToDataLayer_Parms
	{
		FString FromLayerId;
		FString ToLayerId;
		float TransitionTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "CPP_Default_TransitionTime", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FromLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToLayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FromLayerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ToLayerId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_FromLayerId = { "FromLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventTransitionToDataLayer_Parms, FromLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FromLayerId_MetaData), NewProp_FromLayerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ToLayerId = { "ToLayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventTransitionToDataLayer_Parms, ToLayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToLayerId_MetaData), NewProp_ToLayerId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventTransitionToDataLayer_Parms, TransitionTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventTransitionToDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventTransitionToDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_FromLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ToLayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "TransitionToDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::AuracronDataLayerManager_eventTransitionToDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::AuracronDataLayerManager_eventTransitionToDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execTransitionToDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FromLayerId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ToLayerId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TransitionToDataLayer(Z_Param_FromLayerId,Z_Param_ToLayerId,Z_Param_TransitionTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function TransitionToDataLayer *******************

// ********** Begin Class UAuracronDataLayerManager Function UnloadDataLayer ***********************
struct Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics
{
	struct AuracronDataLayerManager_eventUnloadDataLayer_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventUnloadDataLayer_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventUnloadDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventUnloadDataLayer_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "UnloadDataLayer", Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::AuracronDataLayerManager_eventUnloadDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::AuracronDataLayerManager_eventUnloadDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execUnloadDataLayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadDataLayer(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function UnloadDataLayer *************************

// ********** Begin Class UAuracronDataLayerManager Function ValidateLayerDependencies *************
struct Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics
{
	struct AuracronDataLayerManager_eventValidateLayerDependencies_Parms
	{
		FString LayerId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layer Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_LayerId = { "LayerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDataLayerManager_eventValidateLayerDependencies_Parms, LayerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerId_MetaData), NewProp_LayerId_MetaData) };
void Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDataLayerManager_eventValidateLayerDependencies_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDataLayerManager_eventValidateLayerDependencies_Parms), &Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_LayerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDataLayerManager, nullptr, "ValidateLayerDependencies", Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::AuracronDataLayerManager_eventValidateLayerDependencies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::AuracronDataLayerManager_eventValidateLayerDependencies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDataLayerManager::execValidateLayerDependencies)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateLayerDependencies(Z_Param_LayerId);
	P_NATIVE_END;
}
// ********** End Class UAuracronDataLayerManager Function ValidateLayerDependencies ***************

// ********** Begin Class UAuracronDataLayerManager ************************************************
void UAuracronDataLayerManager::StaticRegisterNativesUAuracronDataLayerManager()
{
	UClass* Class = UAuracronDataLayerManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddLayerCondition", &UAuracronDataLayerManager::execAddLayerCondition },
		{ "AddLayerDependency", &UAuracronDataLayerManager::execAddLayerDependency },
		{ "BlendDataLayers", &UAuracronDataLayerManager::execBlendDataLayers },
		{ "CreateDataLayer", &UAuracronDataLayerManager::execCreateDataLayer },
		{ "DoesDataLayerExist", &UAuracronDataLayerManager::execDoesDataLayerExist },
		{ "DrawDebugLayerInfo", &UAuracronDataLayerManager::execDrawDebugLayerInfo },
		{ "EnableLayerDebug", &UAuracronDataLayerManager::execEnableLayerDebug },
		{ "EvaluateAllConditions", &UAuracronDataLayerManager::execEvaluateAllConditions },
		{ "GetAllDataLayers", &UAuracronDataLayerManager::execGetAllDataLayers },
		{ "GetConfiguration", &UAuracronDataLayerManager::execGetConfiguration },
		{ "GetDataLayerInfo", &UAuracronDataLayerManager::execGetDataLayerInfo },
		{ "GetDataLayerNames", &UAuracronDataLayerManager::execGetDataLayerNames },
		{ "GetDataLayerState", &UAuracronDataLayerManager::execGetDataLayerState },
		{ "GetDataLayerStatistics", &UAuracronDataLayerManager::execGetDataLayerStatistics },
		{ "GetDataLayerVisibility", &UAuracronDataLayerManager::execGetDataLayerVisibility },
		{ "GetInstance", &UAuracronDataLayerManager::execGetInstance },
		{ "GetLayerConditions", &UAuracronDataLayerManager::execGetLayerConditions },
		{ "GetLayerDependencies", &UAuracronDataLayerManager::execGetLayerDependencies },
		{ "GetLayersByTag", &UAuracronDataLayerManager::execGetLayersByTag },
		{ "GetLayersByType", &UAuracronDataLayerManager::execGetLayersByType },
		{ "GetLoadedLayerCount", &UAuracronDataLayerManager::execGetLoadedLayerCount },
		{ "GetLoadedLayers", &UAuracronDataLayerManager::execGetLoadedLayers },
		{ "GetTotalLayerCount", &UAuracronDataLayerManager::execGetTotalLayerCount },
		{ "GetTotalMemoryUsage", &UAuracronDataLayerManager::execGetTotalMemoryUsage },
		{ "GetVisibleLayers", &UAuracronDataLayerManager::execGetVisibleLayers },
		{ "HideDataLayer", &UAuracronDataLayerManager::execHideDataLayer },
		{ "Initialize", &UAuracronDataLayerManager::execInitialize },
		{ "IsInitialized", &UAuracronDataLayerManager::execIsInitialized },
		{ "IsLayerDebugEnabled", &UAuracronDataLayerManager::execIsLayerDebugEnabled },
		{ "LoadDataLayer", &UAuracronDataLayerManager::execLoadDataLayer },
		{ "LogLayerState", &UAuracronDataLayerManager::execLogLayerState },
		{ "RemoveDataLayer", &UAuracronDataLayerManager::execRemoveDataLayer },
		{ "RemoveLayerCondition", &UAuracronDataLayerManager::execRemoveLayerCondition },
		{ "RemoveLayerDependency", &UAuracronDataLayerManager::execRemoveLayerDependency },
		{ "ResetStatistics", &UAuracronDataLayerManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronDataLayerManager::execSetConfiguration },
		{ "SetDataLayerState", &UAuracronDataLayerManager::execSetDataLayerState },
		{ "SetDataLayerVisibility", &UAuracronDataLayerManager::execSetDataLayerVisibility },
		{ "ShowDataLayer", &UAuracronDataLayerManager::execShowDataLayer },
		{ "Shutdown", &UAuracronDataLayerManager::execShutdown },
		{ "SwitchToDataLayerSet", &UAuracronDataLayerManager::execSwitchToDataLayerSet },
		{ "Tick", &UAuracronDataLayerManager::execTick },
		{ "ToggleDataLayer", &UAuracronDataLayerManager::execToggleDataLayer },
		{ "TransitionToDataLayer", &UAuracronDataLayerManager::execTransitionToDataLayer },
		{ "UnloadDataLayer", &UAuracronDataLayerManager::execUnloadDataLayer },
		{ "ValidateLayerDependencies", &UAuracronDataLayerManager::execValidateLayerDependencies },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronDataLayerManager;
UClass* UAuracronDataLayerManager::GetPrivateStaticClass()
{
	using TClass = UAuracronDataLayerManager;
	if (!Z_Registration_Info_UClass_UAuracronDataLayerManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronDataLayerManager"),
			Z_Registration_Info_UClass_UAuracronDataLayerManager.InnerSingleton,
			StaticRegisterNativesUAuracronDataLayerManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronDataLayerManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronDataLayerManager_NoRegister()
{
	return UAuracronDataLayerManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronDataLayerManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Layer Manager\n * Central manager for data layer operations\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionDataLayers.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Layer Manager\nCentral manager for data layer operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDataLayerStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDataLayerVisibilityChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDataLayerTransitionStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDataLayerTransitionCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerSubsystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDataLayers.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDataLayerStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDataLayerVisibilityChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDataLayerTransitionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDataLayerTransitionCompleted;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_DataLayerSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerCondition, "AddLayerCondition" }, // 2533252252
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_AddLayerDependency, "AddLayerDependency" }, // 2934873173
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_BlendDataLayers, "BlendDataLayers" }, // 801056634
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_CreateDataLayer, "CreateDataLayer" }, // 4080915122
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_DoesDataLayerExist, "DoesDataLayerExist" }, // 3727394087
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_DrawDebugLayerInfo, "DrawDebugLayerInfo" }, // 1885420027
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_EnableLayerDebug, "EnableLayerDebug" }, // 2966881870
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_EvaluateAllConditions, "EvaluateAllConditions" }, // 716293669
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetAllDataLayers, "GetAllDataLayers" }, // 3037449675
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetConfiguration, "GetConfiguration" }, // 342274656
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerInfo, "GetDataLayerInfo" }, // 3557360517
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerNames, "GetDataLayerNames" }, // 3281790590
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerState, "GetDataLayerState" }, // 1407339446
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerStatistics, "GetDataLayerStatistics" }, // 3555836695
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetDataLayerVisibility, "GetDataLayerVisibility" }, // 1732962163
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetInstance, "GetInstance" }, // 1862512019
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerConditions, "GetLayerConditions" }, // 3135087958
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLayerDependencies, "GetLayerDependencies" }, // 495791166
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByTag, "GetLayersByTag" }, // 3449850526
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLayersByType, "GetLayersByType" }, // 110366552
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayerCount, "GetLoadedLayerCount" }, // 2895937494
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetLoadedLayers, "GetLoadedLayers" }, // 1098667437
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalLayerCount, "GetTotalLayerCount" }, // 1899723281
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 1355998443
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_GetVisibleLayers, "GetVisibleLayers" }, // 2876450707
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_HideDataLayer, "HideDataLayer" }, // 2143751130
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_Initialize, "Initialize" }, // 4257399952
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_IsInitialized, "IsInitialized" }, // 2226857765
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_IsLayerDebugEnabled, "IsLayerDebugEnabled" }, // 1815359228
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_LoadDataLayer, "LoadDataLayer" }, // 3702116352
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_LogLayerState, "LogLayerState" }, // 855588749
		{ &Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature, "OnDataLayerStateChanged__DelegateSignature" }, // 115822844
		{ &Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature, "OnDataLayerTransitionCompleted__DelegateSignature" }, // 2843661336
		{ &Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature, "OnDataLayerTransitionStarted__DelegateSignature" }, // 348065453
		{ &Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature, "OnDataLayerVisibilityChanged__DelegateSignature" }, // 4221635096
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveDataLayer, "RemoveDataLayer" }, // 3260490625
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerCondition, "RemoveLayerCondition" }, // 3387718609
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_RemoveLayerDependency, "RemoveLayerDependency" }, // 1457674915
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_ResetStatistics, "ResetStatistics" }, // 1020100261
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_SetConfiguration, "SetConfiguration" }, // 3275888840
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerState, "SetDataLayerState" }, // 2355280292
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_SetDataLayerVisibility, "SetDataLayerVisibility" }, // 3630912192
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_ShowDataLayer, "ShowDataLayer" }, // 508851559
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_Shutdown, "Shutdown" }, // 2502424243
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_SwitchToDataLayerSet, "SwitchToDataLayerSet" }, // 3529486660
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_Tick, "Tick" }, // 1902584074
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_ToggleDataLayer, "ToggleDataLayer" }, // 146650863
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_TransitionToDataLayer, "TransitionToDataLayer" }, // 1386020838
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_UnloadDataLayer, "UnloadDataLayer" }, // 1780310255
		{ &Z_Construct_UFunction_UAuracronDataLayerManager_ValidateLayerDependencies, "ValidateLayerDependencies" }, // 228691168
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronDataLayerManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerStateChanged = { "OnDataLayerStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, OnDataLayerStateChanged), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDataLayerStateChanged_MetaData), NewProp_OnDataLayerStateChanged_MetaData) }; // 115822844
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerVisibilityChanged = { "OnDataLayerVisibilityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, OnDataLayerVisibilityChanged), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDataLayerVisibilityChanged_MetaData), NewProp_OnDataLayerVisibilityChanged_MetaData) }; // 4221635096
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerTransitionStarted = { "OnDataLayerTransitionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, OnDataLayerTransitionStarted), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDataLayerTransitionStarted_MetaData), NewProp_OnDataLayerTransitionStarted_MetaData) }; // 348065453
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerTransitionCompleted = { "OnDataLayerTransitionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, OnDataLayerTransitionCompleted), Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDataLayerTransitionCompleted_MetaData), NewProp_OnDataLayerTransitionCompleted_MetaData) }; // 2843661336
void Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronDataLayerManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDataLayerManager), &Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, Configuration), Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2828586530
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_DataLayerSubsystem = { "DataLayerSubsystem", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDataLayerManager, DataLayerSubsystem), Z_Construct_UClass_UDataLayerSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerSubsystem_MetaData), NewProp_DataLayerSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronDataLayerManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerVisibilityChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerTransitionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_OnDataLayerTransitionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDataLayerManager_Statics::NewProp_DataLayerSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDataLayerManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronDataLayerManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDataLayerManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronDataLayerManager_Statics::ClassParams = {
	&UAuracronDataLayerManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronDataLayerManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDataLayerManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDataLayerManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronDataLayerManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronDataLayerManager()
{
	if (!Z_Registration_Info_UClass_UAuracronDataLayerManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronDataLayerManager.OuterSingleton, Z_Construct_UClass_UAuracronDataLayerManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronDataLayerManager.OuterSingleton;
}
UAuracronDataLayerManager::UAuracronDataLayerManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronDataLayerManager);
UAuracronDataLayerManager::~UAuracronDataLayerManager() {}
// ********** End Class UAuracronDataLayerManager **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronDataLayerState_StaticEnum, TEXT("EAuracronDataLayerState"), &Z_Registration_Info_UEnum_EAuracronDataLayerState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2619166922U) },
		{ EAuracronDataLayerType_StaticEnum, TEXT("EAuracronDataLayerType"), &Z_Registration_Info_UEnum_EAuracronDataLayerType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 189093178U) },
		{ EAuracronDataLayerVisibility_StaticEnum, TEXT("EAuracronDataLayerVisibility"), &Z_Registration_Info_UEnum_EAuracronDataLayerVisibility, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3787628279U) },
		{ EAuracronDataLayerPriority_StaticEnum, TEXT("EAuracronDataLayerPriority"), &Z_Registration_Info_UEnum_EAuracronDataLayerPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3380752196U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDataLayerConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics::NewStructOps, TEXT("AuracronDataLayerConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDataLayerConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDataLayerConfiguration), 2828586530U) },
		{ FAuracronDataLayerInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics::NewStructOps, TEXT("AuracronDataLayerInfo"), &Z_Registration_Info_UScriptStruct_FAuracronDataLayerInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDataLayerInfo), 586382557U) },
		{ FAuracronDataLayerCondition::StaticStruct, Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics::NewStructOps, TEXT("AuracronDataLayerCondition"), &Z_Registration_Info_UScriptStruct_FAuracronDataLayerCondition, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDataLayerCondition), 931011943U) },
		{ FAuracronDataLayerStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics::NewStructOps, TEXT("AuracronDataLayerStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronDataLayerStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDataLayerStatistics), 2058732843U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronDataLayerManager, UAuracronDataLayerManager::StaticClass, TEXT("UAuracronDataLayerManager"), &Z_Registration_Info_UClass_UAuracronDataLayerManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronDataLayerManager), 1541502494U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_2067084954(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
