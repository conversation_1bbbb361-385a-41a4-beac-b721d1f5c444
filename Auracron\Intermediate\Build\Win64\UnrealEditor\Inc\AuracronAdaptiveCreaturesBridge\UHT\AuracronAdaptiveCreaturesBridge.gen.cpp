// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAdaptiveCreaturesBridge/Public/AuracronAdaptiveCreaturesBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdaptiveCreaturesBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONADAPTIVECREATURESBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge();
AURACRONADAPTIVECREATURESBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_NoRegister();
AURACRONADAPTIVECREATURESBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType();
AURACRONADAPTIVECREATURESBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType();
AURACRONADAPTIVECREATURESBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState();
AURACRONADAPTIVECREATURESBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAdaptationData();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FCreatureProperties();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FCreatureSpawnData();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMassCreatureFragment();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FMassCreatureTag();
AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UAnimBlueprint_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
MASSENTITY_API UClass* Z_Construct_UClass_UMassEntitySubsystem_NoRegister();
MASSENTITY_API UScriptStruct* Z_Construct_UScriptStruct_FMassFragment();
MASSENTITY_API UScriptStruct* Z_Construct_UScriptStruct_FMassTag();
STATETREEMODULE_API UClass* Z_Construct_UClass_UStateTree_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronAdaptiveRealmType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType;
static UEnum* EAuracronAdaptiveRealmType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("EAuracronAdaptiveRealmType"));
	}
	return Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.OuterSingleton;
}
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EAuracronAdaptiveRealmType>()
{
	return EAuracronAdaptiveRealmType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbismoUmbrio.DisplayName", "Abismo Umbrio" },
		{ "AbismoUmbrio.Name", "EAuracronAdaptiveRealmType::AbismoUmbrio" },
		{ "BlueprintType", "true" },
		{ "FirmamentoZephyr.DisplayName", "Firmamento Zephyr" },
		{ "FirmamentoZephyr.Name", "EAuracronAdaptiveRealmType::FirmamentoZephyr" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
		{ "PlanicieRadiante.DisplayName", "Plan\xc3\xad""cie Radiante" },
		{ "PlanicieRadiante.Name", "EAuracronAdaptiveRealmType::PlanicieRadiante" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAdaptiveRealmType::PlanicieRadiante", (int64)EAuracronAdaptiveRealmType::PlanicieRadiante },
		{ "EAuracronAdaptiveRealmType::FirmamentoZephyr", (int64)EAuracronAdaptiveRealmType::FirmamentoZephyr },
		{ "EAuracronAdaptiveRealmType::AbismoUmbrio", (int64)EAuracronAdaptiveRealmType::AbismoUmbrio },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	"EAuracronAdaptiveRealmType",
	"EAuracronAdaptiveRealmType",
	Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType()
{
	if (!Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.InnerSingleton, Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType.InnerSingleton;
}
// ********** End Enum EAuracronAdaptiveRealmType **************************************************

// ********** Begin Enum ECreatureType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECreatureType;
static UEnum* ECreatureType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECreatureType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECreatureType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("ECreatureType"));
	}
	return Z_Registration_Info_UEnum_ECreatureType.OuterSingleton;
}
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<ECreatureType>()
{
	return ECreatureType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CacadorAgil.DisplayName", "Ca\xc3\xa7""ador \xc3\x81gil" },
		{ "CacadorAgil.Name", "ECreatureType::CacadorAgil" },
		{ "ColetorMistico.DisplayName", "Coletor M\xc3\xadstico" },
		{ "ColetorMistico.Name", "ECreatureType::ColetorMistico" },
		{ "Cristalizador.DisplayName", "Cristalizador" },
		{ "Cristalizador.Name", "ECreatureType::Cristalizador" },
		{ "EcoSombrio.DisplayName", "Eco Sombrio" },
		{ "EcoSombrio.Name", "ECreatureType::EcoSombrio" },
		{ "GuardiaoTerrestre.Comment", "// Plan\xc3\xad""cie Radiante\n" },
		{ "GuardiaoTerrestre.DisplayName", "Guardi\xc3\xa3o Terrestre" },
		{ "GuardiaoTerrestre.Name", "ECreatureType::GuardiaoTerrestre" },
		{ "GuardiaoTerrestre.ToolTip", "Plan\xc3\xad""cie Radiante" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
		{ "NavegadorEtereo.DisplayName", "Navegador Et\xc3\xa9reo" },
		{ "NavegadorEtereo.Name", "ECreatureType::NavegadorEtereo" },
		{ "SentinelaVoadora.Comment", "// Firmamento Zephyr\n" },
		{ "SentinelaVoadora.DisplayName", "Sentinela Voadora" },
		{ "SentinelaVoadora.Name", "ECreatureType::SentinelaVoadora" },
		{ "SentinelaVoadora.ToolTip", "Firmamento Zephyr" },
		{ "SombraAdaptativa.Comment", "// Abismo Umbrio\n" },
		{ "SombraAdaptativa.DisplayName", "Sombra Adaptativa" },
		{ "SombraAdaptativa.Name", "ECreatureType::SombraAdaptativa" },
		{ "SombraAdaptativa.ToolTip", "Abismo Umbrio" },
		{ "TempestadeViva.DisplayName", "Tempestade Viva" },
		{ "TempestadeViva.Name", "ECreatureType::TempestadeViva" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECreatureType::GuardiaoTerrestre", (int64)ECreatureType::GuardiaoTerrestre },
		{ "ECreatureType::CacadorAgil", (int64)ECreatureType::CacadorAgil },
		{ "ECreatureType::ColetorMistico", (int64)ECreatureType::ColetorMistico },
		{ "ECreatureType::SentinelaVoadora", (int64)ECreatureType::SentinelaVoadora },
		{ "ECreatureType::TempestadeViva", (int64)ECreatureType::TempestadeViva },
		{ "ECreatureType::NavegadorEtereo", (int64)ECreatureType::NavegadorEtereo },
		{ "ECreatureType::SombraAdaptativa", (int64)ECreatureType::SombraAdaptativa },
		{ "ECreatureType::Cristalizador", (int64)ECreatureType::Cristalizador },
		{ "ECreatureType::EcoSombrio", (int64)ECreatureType::EcoSombrio },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	"ECreatureType",
	"ECreatureType",
	Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType()
{
	if (!Z_Registration_Info_UEnum_ECreatureType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECreatureType.InnerSingleton, Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECreatureType.InnerSingleton;
}
// ********** End Enum ECreatureType ***************************************************************

// ********** Begin Enum EBehaviorState ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBehaviorState;
static UEnum* EBehaviorState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBehaviorState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBehaviorState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("EBehaviorState"));
	}
	return Z_Registration_Info_UEnum_EBehaviorState.OuterSingleton;
}
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EBehaviorState>()
{
	return EBehaviorState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EBehaviorState::Aggressive" },
		{ "Alert.DisplayName", "Alert" },
		{ "Alert.Name", "EBehaviorState::Alert" },
		{ "BlueprintType", "true" },
		{ "Cooperative.DisplayName", "Cooperative" },
		{ "Cooperative.Name", "EBehaviorState::Cooperative" },
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "EBehaviorState::Defensive" },
		{ "Fleeing.DisplayName", "Fleeing" },
		{ "Fleeing.Name", "EBehaviorState::Fleeing" },
		{ "Hunting.DisplayName", "Hunting" },
		{ "Hunting.Name", "EBehaviorState::Hunting" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
		{ "Passive.DisplayName", "Passive" },
		{ "Passive.Name", "EBehaviorState::Passive" },
		{ "Territorial.DisplayName", "Territorial" },
		{ "Territorial.Name", "EBehaviorState::Territorial" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBehaviorState::Passive", (int64)EBehaviorState::Passive },
		{ "EBehaviorState::Alert", (int64)EBehaviorState::Alert },
		{ "EBehaviorState::Aggressive", (int64)EBehaviorState::Aggressive },
		{ "EBehaviorState::Defensive", (int64)EBehaviorState::Defensive },
		{ "EBehaviorState::Hunting", (int64)EBehaviorState::Hunting },
		{ "EBehaviorState::Fleeing", (int64)EBehaviorState::Fleeing },
		{ "EBehaviorState::Territorial", (int64)EBehaviorState::Territorial },
		{ "EBehaviorState::Cooperative", (int64)EBehaviorState::Cooperative },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	"EBehaviorState",
	"EBehaviorState",
	Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState()
{
	if (!Z_Registration_Info_UEnum_EBehaviorState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBehaviorState.InnerSingleton, Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EBehaviorState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBehaviorState.InnerSingleton;
}
// ********** End Enum EBehaviorState **************************************************************

// ********** Begin Enum EAdaptationType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAdaptationType;
static UEnum* EAdaptationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAdaptationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAdaptationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("EAdaptationType"));
	}
	return Z_Registration_Info_UEnum_EAdaptationType.OuterSingleton;
}
template<> AURACRONADAPTIVECREATURESBRIDGE_API UEnum* StaticEnum<EAdaptationType>()
{
	return EAdaptationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AggressionScaling.DisplayName", "Aggression Scaling" },
		{ "AggressionScaling.Name", "EAdaptationType::AggressionScaling" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
		{ "PackCoordination.DisplayName", "Pack Coordination" },
		{ "PackCoordination.Name", "EAdaptationType::PackCoordination" },
		{ "PatrolOptimization.DisplayName", "Patrol Optimization" },
		{ "PatrolOptimization.Name", "EAdaptationType::PatrolOptimization" },
		{ "ResourceCompetition.DisplayName", "Resource Competition" },
		{ "ResourceCompetition.Name", "EAdaptationType::ResourceCompetition" },
		{ "StealthAdaptation.DisplayName", "Stealth Adaptation" },
		{ "StealthAdaptation.Name", "EAdaptationType::StealthAdaptation" },
		{ "TerritoryShifting.DisplayName", "Territory Shifting" },
		{ "TerritoryShifting.Name", "EAdaptationType::TerritoryShifting" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAdaptationType::AggressionScaling", (int64)EAdaptationType::AggressionScaling },
		{ "EAdaptationType::TerritoryShifting", (int64)EAdaptationType::TerritoryShifting },
		{ "EAdaptationType::PackCoordination", (int64)EAdaptationType::PackCoordination },
		{ "EAdaptationType::ResourceCompetition", (int64)EAdaptationType::ResourceCompetition },
		{ "EAdaptationType::StealthAdaptation", (int64)EAdaptationType::StealthAdaptation },
		{ "EAdaptationType::PatrolOptimization", (int64)EAdaptationType::PatrolOptimization },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	"EAdaptationType",
	"EAdaptationType",
	Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType()
{
	if (!Z_Registration_Info_UEnum_EAdaptationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAdaptationType.InnerSingleton, Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAdaptationType.InnerSingleton;
}
// ********** End Enum EAdaptationType *************************************************************

// ********** Begin ScriptStruct FCreatureProperties ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FCreatureProperties;
class UScriptStruct* FCreatureProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FCreatureProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FCreatureProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCreatureProperties, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("CreatureProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FCreatureProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FCreatureProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damage_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Speed_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRange_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerritoryRadius_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationRate_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanFly_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsNocturnal_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPackHunter_MetaData[] = {
		{ "Category", "Creature Properties" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerritoryRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationRate;
	static void NewProp_bCanFly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanFly;
	static void NewProp_bIsNocturnal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNocturnal;
	static void NewProp_bPackHunter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPackHunter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCreatureProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, Damage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damage_MetaData), NewProp_Damage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, Speed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Speed_MetaData), NewProp_Speed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_DetectionRange = { "DetectionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, DetectionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRange_MetaData), NewProp_DetectionRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_TerritoryRadius = { "TerritoryRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, TerritoryRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerritoryRadius_MetaData), NewProp_TerritoryRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_AdaptationRate = { "AdaptationRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureProperties, AdaptationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationRate_MetaData), NewProp_AdaptationRate_MetaData) };
void Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bCanFly_SetBit(void* Obj)
{
	((FCreatureProperties*)Obj)->bCanFly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bCanFly = { "bCanFly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCreatureProperties), &Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bCanFly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanFly_MetaData), NewProp_bCanFly_MetaData) };
void Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bIsNocturnal_SetBit(void* Obj)
{
	((FCreatureProperties*)Obj)->bIsNocturnal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bIsNocturnal = { "bIsNocturnal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCreatureProperties), &Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bIsNocturnal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsNocturnal_MetaData), NewProp_bIsNocturnal_MetaData) };
void Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bPackHunter_SetBit(void* Obj)
{
	((FCreatureProperties*)Obj)->bPackHunter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bPackHunter = { "bPackHunter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCreatureProperties), &Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bPackHunter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPackHunter_MetaData), NewProp_bPackHunter_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCreatureProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_Speed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_DetectionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_TerritoryRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_AdaptationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bCanFly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bIsNocturnal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewProp_bPackHunter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCreatureProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	&NewStructOps,
	"CreatureProperties",
	Z_Construct_UScriptStruct_FCreatureProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureProperties_Statics::PropPointers),
	sizeof(FCreatureProperties),
	alignof(FCreatureProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCreatureProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCreatureProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FCreatureProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FCreatureProperties.InnerSingleton, Z_Construct_UScriptStruct_FCreatureProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FCreatureProperties.InnerSingleton;
}
// ********** End ScriptStruct FCreatureProperties *************************************************

// ********** Begin ScriptStruct FAdaptationData ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAdaptationData;
class UScriptStruct* FAdaptationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAdaptationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAdaptationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAdaptationData, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("AdaptationData"));
	}
	return Z_Registration_Info_UScriptStruct_FAdaptationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAdaptationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationType_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationStrength_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerThreshold_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DecayRate_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPermanentAdaptation_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAdaptationTime_MetaData[] = {
		{ "Category", "Adaptation" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TriggerThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DecayRate;
	static void NewProp_bPermanentAdaptation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPermanentAdaptation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAdaptationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAdaptationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationType = { "AdaptationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationData, AdaptationType), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAdaptationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationType_MetaData), NewProp_AdaptationType_MetaData) }; // 3713552408
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationStrength = { "AdaptationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationData, AdaptationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationStrength_MetaData), NewProp_AdaptationStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_TriggerThreshold = { "TriggerThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationData, TriggerThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerThreshold_MetaData), NewProp_TriggerThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_DecayRate = { "DecayRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationData, DecayRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DecayRate_MetaData), NewProp_DecayRate_MetaData) };
void Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_bPermanentAdaptation_SetBit(void* Obj)
{
	((FAdaptationData*)Obj)->bPermanentAdaptation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_bPermanentAdaptation = { "bPermanentAdaptation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAdaptationData), &Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_bPermanentAdaptation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPermanentAdaptation_MetaData), NewProp_bPermanentAdaptation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_LastAdaptationTime = { "LastAdaptationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationData, LastAdaptationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAdaptationTime_MetaData), NewProp_LastAdaptationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAdaptationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_AdaptationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_TriggerThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_DecayRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_bPermanentAdaptation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationData_Statics::NewProp_LastAdaptationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAdaptationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	&NewStructOps,
	"AdaptationData",
	Z_Construct_UScriptStruct_FAdaptationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationData_Statics::PropPointers),
	sizeof(FAdaptationData),
	alignof(FAdaptationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAdaptationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAdaptationData()
{
	if (!Z_Registration_Info_UScriptStruct_FAdaptationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAdaptationData.InnerSingleton, Z_Construct_UScriptStruct_FAdaptationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAdaptationData.InnerSingleton;
}
// ********** End ScriptStruct FAdaptationData *****************************************************

// ********** Begin ScriptStruct FCreatureSpawnData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FCreatureSpawnData;
class UScriptStruct* FCreatureSpawnData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FCreatureSpawnData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FCreatureSpawnData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCreatureSpawnData, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("CreatureSpawnData"));
	}
	return Z_Registration_Info_UScriptStruct_FCreatureSpawnData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FCreatureSpawnData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureType_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmType_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnLocation_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnRotation_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialAdaptations_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureMesh_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationBlueprint_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorStateTree_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PackSize_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnRadius_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CreatureType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CreatureType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InitialAdaptations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InitialAdaptations;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CreatureMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AnimationBlueprint;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BehaviorStateTree;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PackSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCreatureSpawnData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureType = { "CreatureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, CreatureType), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureType_MetaData), NewProp_CreatureType_MetaData) }; // 1374601931
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, RealmType), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmType_MetaData), NewProp_RealmType_MetaData) }; // 3474500431
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnLocation = { "SpawnLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, SpawnLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnLocation_MetaData), NewProp_SpawnLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnRotation = { "SpawnRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, SpawnRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnRotation_MetaData), NewProp_SpawnRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, Properties), Z_Construct_UScriptStruct_FCreatureProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 3353594624
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_InitialAdaptations_Inner = { "InitialAdaptations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAdaptationData, METADATA_PARAMS(0, nullptr) }; // 3671449128
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_InitialAdaptations = { "InitialAdaptations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, InitialAdaptations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialAdaptations_MetaData), NewProp_InitialAdaptations_MetaData) }; // 3671449128
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureMesh = { "CreatureMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, CreatureMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureMesh_MetaData), NewProp_CreatureMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_AnimationBlueprint = { "AnimationBlueprint", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, AnimationBlueprint), Z_Construct_UClass_UAnimBlueprint_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationBlueprint_MetaData), NewProp_AnimationBlueprint_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_BehaviorStateTree = { "BehaviorStateTree", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, BehaviorStateTree), Z_Construct_UClass_UStateTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorStateTree_MetaData), NewProp_BehaviorStateTree_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_PackSize = { "PackSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, PackSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PackSize_MetaData), NewProp_PackSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnRadius = { "SpawnRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCreatureSpawnData, SpawnRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnRadius_MetaData), NewProp_SpawnRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_InitialAdaptations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_InitialAdaptations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_CreatureMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_AnimationBlueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_BehaviorStateTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_PackSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewProp_SpawnRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	&NewStructOps,
	"CreatureSpawnData",
	Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::PropPointers),
	sizeof(FCreatureSpawnData),
	alignof(FCreatureSpawnData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCreatureSpawnData()
{
	if (!Z_Registration_Info_UScriptStruct_FCreatureSpawnData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FCreatureSpawnData.InnerSingleton, Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FCreatureSpawnData.InnerSingleton;
}
// ********** End ScriptStruct FCreatureSpawnData **************************************************

// ********** Begin ScriptStruct FMassCreatureFragment *********************************************
static_assert(std::is_polymorphic<FMassCreatureFragment>() == std::is_polymorphic<FMassFragment>(), "USTRUCT FMassCreatureFragment cannot be polymorphic unless super FMassFragment is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMassCreatureFragment;
class UScriptStruct* FMassCreatureFragment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMassCreatureFragment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMassCreatureFragment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMassCreatureFragment, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("MassCreatureFragment"));
	}
	return Z_Registration_Info_UScriptStruct_FMassCreatureFragment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMassCreatureFragment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMassCreatureFragment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMassCreatureFragment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	Z_Construct_UScriptStruct_FMassFragment,
	&NewStructOps,
	"MassCreatureFragment",
	nullptr,
	0,
	sizeof(FMassCreatureFragment),
	alignof(FMassCreatureFragment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMassCreatureFragment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMassCreatureFragment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMassCreatureFragment()
{
	if (!Z_Registration_Info_UScriptStruct_FMassCreatureFragment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMassCreatureFragment.InnerSingleton, Z_Construct_UScriptStruct_FMassCreatureFragment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMassCreatureFragment.InnerSingleton;
}
// ********** End ScriptStruct FMassCreatureFragment ***********************************************

// ********** Begin ScriptStruct FMassCreatureTag **************************************************
static_assert(std::is_polymorphic<FMassCreatureTag>() == std::is_polymorphic<FMassTag>(), "USTRUCT FMassCreatureTag cannot be polymorphic unless super FMassTag is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMassCreatureTag;
class UScriptStruct* FMassCreatureTag::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMassCreatureTag.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMassCreatureTag.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMassCreatureTag, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("MassCreatureTag"));
	}
	return Z_Registration_Info_UScriptStruct_FMassCreatureTag.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMassCreatureTag_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMassCreatureTag>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMassCreatureTag_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	Z_Construct_UScriptStruct_FMassTag,
	&NewStructOps,
	"MassCreatureTag",
	nullptr,
	0,
	sizeof(FMassCreatureTag),
	alignof(FMassCreatureTag),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMassCreatureTag_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMassCreatureTag_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMassCreatureTag()
{
	if (!Z_Registration_Info_UScriptStruct_FMassCreatureTag.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMassCreatureTag.InnerSingleton, Z_Construct_UScriptStruct_FMassCreatureTag_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMassCreatureTag.InnerSingleton;
}
// ********** End ScriptStruct FMassCreatureTag ****************************************************

// ********** Begin ScriptStruct FAdaptationDataArrayWrapper ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper;
class UScriptStruct* FAdaptationDataArrayWrapper::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper, (UObject*)Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge(), TEXT("AdaptationDataArrayWrapper"));
	}
	return Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para TArray<FAdaptationData> em TMap\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para TArray<FAdaptationData> em TMap" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationData_MetaData[] = {
		{ "Category", "Adaptation Data" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdaptationData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdaptationData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAdaptationDataArrayWrapper>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::NewProp_AdaptationData_Inner = { "AdaptationData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAdaptationData, METADATA_PARAMS(0, nullptr) }; // 3671449128
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::NewProp_AdaptationData = { "AdaptationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAdaptationDataArrayWrapper, AdaptationData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationData_MetaData), NewProp_AdaptationData_MetaData) }; // 3671449128
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::NewProp_AdaptationData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::NewProp_AdaptationData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
	nullptr,
	&NewStructOps,
	"AdaptationDataArrayWrapper",
	Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::PropPointers),
	sizeof(FAdaptationDataArrayWrapper),
	alignof(FAdaptationDataArrayWrapper),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper()
{
	if (!Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.InnerSingleton, Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper.InnerSingleton;
}
// ********** End ScriptStruct FAdaptationDataArrayWrapper *****************************************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ApplyRealmEnvironmentalEffects *
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventApplyRealmEnvironmentalEffects_Parms
	{
		EAuracronAdaptiveRealmType RealmType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeitos ambientais do realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos ambientais do realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventApplyRealmEnvironmentalEffects_Parms, RealmType), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType, METADATA_PARAMS(0, nullptr) }; // 3474500431
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::NewProp_RealmType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ApplyRealmEnvironmentalEffects", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::AuracronAdaptiveCreaturesBridge_eventApplyRealmEnvironmentalEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::AuracronAdaptiveCreaturesBridge_eventApplyRealmEnvironmentalEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execApplyRealmEnvironmentalEffects)
{
	P_GET_ENUM(EAuracronAdaptiveRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyRealmEnvironmentalEffects(EAuracronAdaptiveRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ApplyRealmEnvironmentalEffects ***

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ConfigureRealmSpecificBehaviors 
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms
	{
		EAuracronAdaptiveRealmType RealmType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar comportamentos espec\xc3\xad""ficos do realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar comportamentos espec\xc3\xad""ficos do realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_RealmType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_RealmType = { "RealmType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms, RealmType), Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_EAuracronAdaptiveRealmType, METADATA_PARAMS(0, nullptr) }; // 3474500431
void Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms), &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_RealmType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_RealmType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ConfigureRealmSpecificBehaviors", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::AuracronAdaptiveCreaturesBridge_eventConfigureRealmSpecificBehaviors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execConfigureRealmSpecificBehaviors)
{
	P_GET_ENUM(EAuracronAdaptiveRealmType,Z_Param_RealmType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureRealmSpecificBehaviors(EAuracronAdaptiveRealmType(Z_Param_RealmType));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ConfigureRealmSpecificBehaviors **

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ExecutePythonScript ************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms
	{
		FString ScriptPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar script Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar script Python" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ScriptPath = { "ScriptPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms, ScriptPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptPath_MetaData), NewProp_ScriptPath_MetaData) };
void Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms), &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ScriptPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ExecutePythonScript", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::AuracronAdaptiveCreaturesBridge_eventExecutePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execExecutePythonScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecutePythonScript(Z_Param_ScriptPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ExecutePythonScript **************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function GetCreatureDataForPython *******
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventGetCreatureDataForPython_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter dados de criaturas para Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter dados de criaturas para Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventGetCreatureDataForPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "GetCreatureDataForPython", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::AuracronAdaptiveCreaturesBridge_eventGetCreatureDataForPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::AuracronAdaptiveCreaturesBridge_eventGetCreatureDataForPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execGetCreatureDataForPython)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCreatureDataForPython();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function GetCreatureDataForPython *********

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function GetSystemStatistics ************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventGetSystemStatistics_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\xadsticas do sistema\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas do sistema" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventGetSystemStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "GetSystemStatistics", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::AuracronAdaptiveCreaturesBridge_eventGetSystemStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::AuracronAdaptiveCreaturesBridge_eventGetSystemStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execGetSystemStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSystemStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function GetSystemStatistics **************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function InitializePythonBindings *******
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventInitializePythonBindings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicializar bindings Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar bindings Python" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveCreaturesBridge_eventInitializePythonBindings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveCreaturesBridge_eventInitializePythonBindings_Parms), &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "InitializePythonBindings", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::AuracronAdaptiveCreaturesBridge_eventInitializePythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::AuracronAdaptiveCreaturesBridge_eventInitializePythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execInitializePythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function InitializePythonBindings *********

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ProcessPackBehaviors ***********
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar comportamentos de pack\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar comportamentos de pack" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ProcessPackBehaviors", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execProcessPackBehaviors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessPackBehaviors();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ProcessPackBehaviors *************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ProcessPlayerBehaviorAdaptations 
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventProcessPlayerBehaviorAdaptations_Parms
	{
		FVector PlayerLocation;
		float PlayerThreatLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar adapta\xc3\xa7\xc3\xb5""es baseadas no comportamento do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar adapta\xc3\xa7\xc3\xb5""es baseadas no comportamento do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerThreatLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventProcessPlayerBehaviorAdaptations_Parms, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::NewProp_PlayerThreatLevel = { "PlayerThreatLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventProcessPlayerBehaviorAdaptations_Parms, PlayerThreatLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::NewProp_PlayerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::NewProp_PlayerThreatLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ProcessPlayerBehaviorAdaptations", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::AuracronAdaptiveCreaturesBridge_eventProcessPlayerBehaviorAdaptations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::AuracronAdaptiveCreaturesBridge_eventProcessPlayerBehaviorAdaptations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execProcessPlayerBehaviorAdaptations)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PlayerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PlayerThreatLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessPlayerBehaviorAdaptations(Z_Param_Out_PlayerLocation,Z_Param_PlayerThreatLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ProcessPlayerBehaviorAdaptations *

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function RemoveAllCreatures *************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover todas as criaturas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover todas as criaturas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "RemoveAllCreatures", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execRemoveAllCreatures)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveAllCreatures();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function RemoveAllCreatures ***************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ResetAllAdaptations ************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Resetar todas as adapta\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resetar todas as adapta\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ResetAllAdaptations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execResetAllAdaptations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAllAdaptations();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ResetAllAdaptations **************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function SpawnCreatures *****************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms
	{
		TArray<FCreatureSpawnData> SpawnData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar criaturas usando Mass Entity System\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar criaturas usando Mass Entity System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_SpawnData_Inner = { "SpawnData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FCreatureSpawnData, METADATA_PARAMS(0, nullptr) }; // 3670453542
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_SpawnData = { "SpawnData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms, SpawnData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnData_MetaData), NewProp_SpawnData_MetaData) }; // 3670453542
void Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms), &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_SpawnData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_SpawnData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "SpawnCreatures", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::AuracronAdaptiveCreaturesBridge_eventSpawnCreatures_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execSpawnCreatures)
{
	P_GET_TARRAY_REF(FCreatureSpawnData,Z_Param_Out_SpawnData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpawnCreatures(Z_Param_Out_SpawnData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function SpawnCreatures *******************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function Update3DNavigation *************
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar navega\xc3\xa7\xc3\xa3o 3D para criaturas voadoras\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar navega\xc3\xa7\xc3\xa3o 3D para criaturas voadoras" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "Update3DNavigation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execUpdate3DNavigation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Update3DNavigation();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function Update3DNavigation ***************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function UpdateAllAdaptations ***********
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventUpdateAllAdaptations_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar adapta\xc3\xa7\xc3\xb5""es de todas as criaturas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar adapta\xc3\xa7\xc3\xb5""es de todas as criaturas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveCreaturesBridge_eventUpdateAllAdaptations_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "UpdateAllAdaptations", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::AuracronAdaptiveCreaturesBridge_eventUpdateAllAdaptations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::AuracronAdaptiveCreaturesBridge_eventUpdateAllAdaptations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execUpdateAllAdaptations)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAllAdaptations(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function UpdateAllAdaptations *************

// ********** Begin Class UAuracronAdaptiveCreaturesBridge Function ValidateSystemIntegrity ********
struct Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics
{
	struct AuracronAdaptiveCreaturesBridge_eventValidateSystemIntegrity_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Adaptive Creatures|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar integridade do sistema\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar integridade do sistema" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveCreaturesBridge_eventValidateSystemIntegrity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveCreaturesBridge_eventValidateSystemIntegrity_Parms), &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, nullptr, "ValidateSystemIntegrity", Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::AuracronAdaptiveCreaturesBridge_eventValidateSystemIntegrity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::AuracronAdaptiveCreaturesBridge_eventValidateSystemIntegrity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdaptiveCreaturesBridge::execValidateSystemIntegrity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSystemIntegrity();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdaptiveCreaturesBridge Function ValidateSystemIntegrity **********

// ********** Begin Class UAuracronAdaptiveCreaturesBridge *****************************************
void UAuracronAdaptiveCreaturesBridge::StaticRegisterNativesUAuracronAdaptiveCreaturesBridge()
{
	UClass* Class = UAuracronAdaptiveCreaturesBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyRealmEnvironmentalEffects", &UAuracronAdaptiveCreaturesBridge::execApplyRealmEnvironmentalEffects },
		{ "ConfigureRealmSpecificBehaviors", &UAuracronAdaptiveCreaturesBridge::execConfigureRealmSpecificBehaviors },
		{ "ExecutePythonScript", &UAuracronAdaptiveCreaturesBridge::execExecutePythonScript },
		{ "GetCreatureDataForPython", &UAuracronAdaptiveCreaturesBridge::execGetCreatureDataForPython },
		{ "GetSystemStatistics", &UAuracronAdaptiveCreaturesBridge::execGetSystemStatistics },
		{ "InitializePythonBindings", &UAuracronAdaptiveCreaturesBridge::execInitializePythonBindings },
		{ "ProcessPackBehaviors", &UAuracronAdaptiveCreaturesBridge::execProcessPackBehaviors },
		{ "ProcessPlayerBehaviorAdaptations", &UAuracronAdaptiveCreaturesBridge::execProcessPlayerBehaviorAdaptations },
		{ "RemoveAllCreatures", &UAuracronAdaptiveCreaturesBridge::execRemoveAllCreatures },
		{ "ResetAllAdaptations", &UAuracronAdaptiveCreaturesBridge::execResetAllAdaptations },
		{ "SpawnCreatures", &UAuracronAdaptiveCreaturesBridge::execSpawnCreatures },
		{ "Update3DNavigation", &UAuracronAdaptiveCreaturesBridge::execUpdate3DNavigation },
		{ "UpdateAllAdaptations", &UAuracronAdaptiveCreaturesBridge::execUpdateAllAdaptations },
		{ "ValidateSystemIntegrity", &UAuracronAdaptiveCreaturesBridge::execValidateSystemIntegrity },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge;
UClass* UAuracronAdaptiveCreaturesBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronAdaptiveCreaturesBridge;
	if (!Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdaptiveCreaturesBridge"),
			Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.InnerSingleton,
			StaticRegisterNativesUAuracronAdaptiveCreaturesBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_NoRegister()
{
	return UAuracronAdaptiveCreaturesBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Adaptive Creatures" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Criaturas Neutras Adaptativas\n * Integra Mass Entity System, State Trees e AI para criaturas que se adaptam ao comportamento do jogador\n */" },
#endif
		{ "DisplayName", "AURACRON Adaptive Creatures Bridge" },
		{ "IncludePath", "AuracronAdaptiveCreaturesBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Criaturas Neutras Adaptativas\nIntegra Mass Entity System, State Trees e AI para criaturas que se adaptam ao comportamento do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MassEntitySubsystem_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Subsistema Mass Entity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subsistema Mass Entity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureTypeConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de spawn por tipo de criatura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de spawn por tipo de criatura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmAdaptationConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de adapta\xc3\xa7\xc3\xa3o por realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de adapta\xc3\xa7\xc3\xa3o por realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSimultaneousCreatures_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa1ximo de criaturas simult\xc3\xa2neas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa1ximo de criaturas simult\xc3\xa2neas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultiThreading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar multi-threading para processamento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar multi-threading para processamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de atualiza\xc3\xa7\xc3\xa3o de adapta\xc3\xa7\xc3\xb5""es (segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o de adapta\xc3\xa7\xc3\xb5""es (segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerDetectionRadius_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de detec\xc3\xa7\xc3\xa3o do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de detec\xc3\xa7\xc3\xa3o do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "999999" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveCreaturesBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MassEntitySubsystem;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreatureTypeConfigurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CreatureTypeConfigurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CreatureTypeConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatureTypeConfigurations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmAdaptationConfigurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmAdaptationConfigurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmAdaptationConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RealmAdaptationConfigurations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSimultaneousCreatures;
	static void NewProp_bUseMultiThreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultiThreading;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerDetectionRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GenerationSeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ApplyRealmEnvironmentalEffects, "ApplyRealmEnvironmentalEffects" }, // 685478739
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ConfigureRealmSpecificBehaviors, "ConfigureRealmSpecificBehaviors" }, // 3295242355
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ExecutePythonScript, "ExecutePythonScript" }, // 1972116817
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetCreatureDataForPython, "GetCreatureDataForPython" }, // 4029337594
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_GetSystemStatistics, "GetSystemStatistics" }, // 3410858974
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_InitializePythonBindings, "InitializePythonBindings" }, // 423044494
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPackBehaviors, "ProcessPackBehaviors" }, // 564101621
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ProcessPlayerBehaviorAdaptations, "ProcessPlayerBehaviorAdaptations" }, // 2500203202
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_RemoveAllCreatures, "RemoveAllCreatures" }, // 1571049200
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ResetAllAdaptations, "ResetAllAdaptations" }, // 623045198
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_SpawnCreatures, "SpawnCreatures" }, // 4184931044
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_Update3DNavigation, "Update3DNavigation" }, // 1902573427
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_UpdateAllAdaptations, "UpdateAllAdaptations" }, // 884899198
		{ &Z_Construct_UFunction_UAuracronAdaptiveCreaturesBridge_ValidateSystemIntegrity, "ValidateSystemIntegrity" }, // 3542848784
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAdaptiveCreaturesBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_MassEntitySubsystem = { "MassEntitySubsystem", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, MassEntitySubsystem), Z_Construct_UClass_UMassEntitySubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MassEntitySubsystem_MetaData), NewProp_MassEntitySubsystem_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_ValueProp = { "CreatureTypeConfigurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FCreatureSpawnData, METADATA_PARAMS(0, nullptr) }; // 3670453542
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_Key_KeyProp = { "CreatureTypeConfigurations_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronAdaptiveCreaturesBridge_ECreatureType, METADATA_PARAMS(0, nullptr) }; // 1374601931
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations = { "CreatureTypeConfigurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, CreatureTypeConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureTypeConfigurations_MetaData), NewProp_CreatureTypeConfigurations_MetaData) }; // 1374601931 3670453542
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_ValueProp = { "RealmAdaptationConfigurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper, METADATA_PARAMS(0, nullptr) }; // 134997227
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_Key_KeyProp = { "RealmAdaptationConfigurations_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, METADATA_PARAMS(0, nullptr) }; // 2881442322
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations = { "RealmAdaptationConfigurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, RealmAdaptationConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmAdaptationConfigurations_MetaData), NewProp_RealmAdaptationConfigurations_MetaData) }; // 2881442322 134997227
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_MaxSimultaneousCreatures = { "MaxSimultaneousCreatures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, MaxSimultaneousCreatures), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSimultaneousCreatures_MetaData), NewProp_MaxSimultaneousCreatures_MetaData) };
void Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_bUseMultiThreading_SetBit(void* Obj)
{
	((UAuracronAdaptiveCreaturesBridge*)Obj)->bUseMultiThreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_bUseMultiThreading = { "bUseMultiThreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdaptiveCreaturesBridge), &Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_bUseMultiThreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultiThreading_MetaData), NewProp_bUseMultiThreading_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_AdaptationUpdateInterval = { "AdaptationUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, AdaptationUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationUpdateInterval_MetaData), NewProp_AdaptationUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_PlayerDetectionRadius = { "PlayerDetectionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, PlayerDetectionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerDetectionRadius_MetaData), NewProp_PlayerDetectionRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdaptiveCreaturesBridge, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_MassEntitySubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_CreatureTypeConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_RealmAdaptationConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_MaxSimultaneousCreatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_bUseMultiThreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_AdaptationUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_PlayerDetectionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::NewProp_GenerationSeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::ClassParams = {
	&UAuracronAdaptiveCreaturesBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.OuterSingleton, Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAdaptiveCreaturesBridge);
UAuracronAdaptiveCreaturesBridge::~UAuracronAdaptiveCreaturesBridge() {}
// ********** End Class UAuracronAdaptiveCreaturesBridge *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronAdaptiveRealmType_StaticEnum, TEXT("EAuracronAdaptiveRealmType"), &Z_Registration_Info_UEnum_EAuracronAdaptiveRealmType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3474500431U) },
		{ ECreatureType_StaticEnum, TEXT("ECreatureType"), &Z_Registration_Info_UEnum_ECreatureType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1374601931U) },
		{ EBehaviorState_StaticEnum, TEXT("EBehaviorState"), &Z_Registration_Info_UEnum_EBehaviorState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 804139241U) },
		{ EAdaptationType_StaticEnum, TEXT("EAdaptationType"), &Z_Registration_Info_UEnum_EAdaptationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3713552408U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FCreatureProperties::StaticStruct, Z_Construct_UScriptStruct_FCreatureProperties_Statics::NewStructOps, TEXT("CreatureProperties"), &Z_Registration_Info_UScriptStruct_FCreatureProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCreatureProperties), 3353594624U) },
		{ FAdaptationData::StaticStruct, Z_Construct_UScriptStruct_FAdaptationData_Statics::NewStructOps, TEXT("AdaptationData"), &Z_Registration_Info_UScriptStruct_FAdaptationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAdaptationData), 3671449128U) },
		{ FCreatureSpawnData::StaticStruct, Z_Construct_UScriptStruct_FCreatureSpawnData_Statics::NewStructOps, TEXT("CreatureSpawnData"), &Z_Registration_Info_UScriptStruct_FCreatureSpawnData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCreatureSpawnData), 3670453542U) },
		{ FMassCreatureFragment::StaticStruct, Z_Construct_UScriptStruct_FMassCreatureFragment_Statics::NewStructOps, TEXT("MassCreatureFragment"), &Z_Registration_Info_UScriptStruct_FMassCreatureFragment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMassCreatureFragment), 2971372956U) },
		{ FMassCreatureTag::StaticStruct, Z_Construct_UScriptStruct_FMassCreatureTag_Statics::NewStructOps, TEXT("MassCreatureTag"), &Z_Registration_Info_UScriptStruct_FMassCreatureTag, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMassCreatureTag), 3722796548U) },
		{ FAdaptationDataArrayWrapper::StaticStruct, Z_Construct_UScriptStruct_FAdaptationDataArrayWrapper_Statics::NewStructOps, TEXT("AdaptationDataArrayWrapper"), &Z_Registration_Info_UScriptStruct_FAdaptationDataArrayWrapper, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAdaptationDataArrayWrapper), 134997227U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAdaptiveCreaturesBridge, UAuracronAdaptiveCreaturesBridge::StaticClass, TEXT("UAuracronAdaptiveCreaturesBridge"), &Z_Registration_Info_UClass_UAuracronAdaptiveCreaturesBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAdaptiveCreaturesBridge), 279815774U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_3064265846(TEXT("/Script/AuracronAdaptiveCreaturesBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAdaptiveCreaturesBridge_Public_AuracronAdaptiveCreaturesBridge_h__Script_AuracronAdaptiveCreaturesBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
