// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronErrorHandling.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronErrorHandling_generated_h
#error "AuracronErrorHandling.generated.h already included, missing '#pragma once' in AuracronErrorHandling.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronErrorHandling_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FErrorInfo ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h_70_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FErrorInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FErrorInfo;
// ********** End ScriptStruct FErrorInfo **********************************************************

// ********** Begin ScriptStruct FDNAValidationResult **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h_105_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDNAValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FDNAValidationResult;
// ********** End ScriptStruct FDNAValidationResult ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronErrorHandling_h

// ********** Begin Enum EErrorSeverity ************************************************************
#define FOREACH_ENUM_EERRORSEVERITY(op) \
	op(EErrorSeverity::Info) \
	op(EErrorSeverity::Warning) \
	op(EErrorSeverity::Error) \
	op(EErrorSeverity::Critical) \
	op(EErrorSeverity::Fatal) 

enum class EErrorSeverity : uint8;
template<> struct TIsUEnumClass<EErrorSeverity> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EErrorSeverity>();
// ********** End Enum EErrorSeverity **************************************************************

// ********** Begin Enum EErrorCategory ************************************************************
#define FOREACH_ENUM_EERRORCATEGORY(op) \
	op(EErrorCategory::General) \
	op(EErrorCategory::DNA) \
	op(EErrorCategory::Mesh) \
	op(EErrorCategory::Animation) \
	op(EErrorCategory::Texture) \
	op(EErrorCategory::Hair) \
	op(EErrorCategory::Clothing) \
	op(EErrorCategory::Physics) \
	op(EErrorCategory::Performance) \
	op(EErrorCategory::Memory) \
	op(EErrorCategory::IO) \
	op(EErrorCategory::Network) \
	op(EErrorCategory::Validation) 

enum class EErrorCategory : uint8;
template<> struct TIsUEnumClass<EErrorCategory> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EErrorCategory>();
// ********** End Enum EErrorCategory **************************************************************

// ********** Begin Enum EDNAValidationType ********************************************************
#define FOREACH_ENUM_EDNAVALIDATIONTYPE(op) \
	op(EDNAValidationType::Basic) \
	op(EDNAValidationType::Comprehensive) \
	op(EDNAValidationType::Integrity) \
	op(EDNAValidationType::Performance) \
	op(EDNAValidationType::Compatibility) 

enum class EDNAValidationType : uint8;
template<> struct TIsUEnumClass<EDNAValidationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EDNAValidationType>();
// ********** End Enum EDNAValidationType **********************************************************

// ********** Begin Enum ERecoveryAction ***********************************************************
#define FOREACH_ENUM_ERECOVERYACTION(op) \
	op(ERecoveryAction::None) \
	op(ERecoveryAction::Retry) \
	op(ERecoveryAction::Reset) \
	op(ERecoveryAction::Reload) \
	op(ERecoveryAction::Fallback) \
	op(ERecoveryAction::Abort) 

enum class ERecoveryAction : uint8;
template<> struct TIsUEnumClass<ERecoveryAction> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERecoveryAction>();
// ********** End Enum ERecoveryAction *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
