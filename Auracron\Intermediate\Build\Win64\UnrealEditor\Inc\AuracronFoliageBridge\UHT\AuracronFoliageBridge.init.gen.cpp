// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronFoliageBridge_init() {}
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageError__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceAdded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceModified__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageOperationCompleted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliagePerformanceUpdated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageStateChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageTypeRegistered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageTypeUnregistered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature();
	AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronFoliageBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronFoliageBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceModified__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageInstanceRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageOperationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliagePerformanceUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageTypeRegistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronFoliageBridge_AuracronFoliageTypeUnregistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeRegistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnBiomeUnregistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnDisturbanceEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageBiomeManager_OnSpeciesRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageBendingStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnFoliageRecoveryCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionEnded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageInteractionManager_OnPlayerInteractionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnColorVariationApplied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnMaterialInstanceRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageMaterialVariationManager_OnTextureBlendingCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageChunkUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageMemoryPressure__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageStreamingManager_OnFoliageStreamingStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronFoliageBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xBFC08485,
				0x45AEE621,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronFoliageBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronFoliageBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronFoliageBridge(Z_Construct_UPackage__Script_AuracronFoliageBridge, TEXT("/Script/AuracronFoliageBridge"), Z_Registration_Info_UPackage__Script_AuracronFoliageBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xBFC08485, 0x45AEE621));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
