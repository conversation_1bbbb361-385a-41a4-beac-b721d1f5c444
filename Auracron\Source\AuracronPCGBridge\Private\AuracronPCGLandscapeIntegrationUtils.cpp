// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Landscape Integration Utilities Implementation
// Bridge 2.7: PCG Framework - Landscape Integration

#include "AuracronPCGLandscapeIntegration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeProxy.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeDataAccess.h"
#include "LandscapeEdit.h"
#include "LandscapeHeightfieldCollisionComponent.h"
#include "LandscapeStreamingProxy.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// LANDSCAPE INTEGRATION UTILITIES IMPLEMENTATION
// =============================================================================

ALandscape* UAuracronPCGLandscapeIntegrationUtils::FindLandscapeInWorld(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return nullptr;
    }

    // Find all landscapes in the world
    TArray<ALandscape*> Landscapes = GetAllLandscapesInWorld(World);
    
    if (Landscapes.Num() == 0)
    {
        return nullptr;
    }

    // If location is zero, return the first landscape
    if (Location.IsZero())
    {
        return Landscapes[0];
    }

    // Find the landscape that contains the location
    for (ALandscape* Landscape : Landscapes)
    {
        if (IsPointOnLandscape(Landscape, Location))
        {
            return Landscape;
        }
    }

    // Return the closest landscape if none contains the point
    ALandscape* ClosestLandscape = nullptr;
    float ClosestDistance = FLT_MAX;

    for (ALandscape* Landscape : Landscapes)
    {
        FBox LandscapeBounds = GetLandscapeBounds(Landscape);
        float Distance = FVector::Dist(Location, LandscapeBounds.GetCenter());
        
        if (Distance < ClosestDistance)
        {
            ClosestDistance = Distance;
            ClosestLandscape = Landscape;
        }
    }

    return ClosestLandscape;
}

TArray<ALandscape*> UAuracronPCGLandscapeIntegrationUtils::GetAllLandscapesInWorld(UWorld* World)
{
    TArray<ALandscape*> Landscapes;
    
    if (!World)
    {
        return Landscapes;
    }

    // Iterate through all actors in the world
    for (TActorIterator<ALandscape> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        ALandscape* Landscape = *ActorIterator;
        if (Landscape && IsValid(Landscape))
        {
            Landscapes.Add(Landscape);
        }
    }

    return Landscapes;
}

bool UAuracronPCGLandscapeIntegrationUtils::IsPointOnLandscape(ALandscape* Landscape, const FVector& WorldLocation)
{
    if (!Landscape)
    {
        return false;
    }

    FBox LandscapeBounds = GetLandscapeBounds(Landscape);
    return LandscapeBounds.IsInside(WorldLocation);
}

float UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(ALandscape* Landscape, const FVector& WorldLocation, bool bUseInterpolation)
{
    if (!Landscape)
    {
        return 0.0f;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return 0.0f;
    }

    // Convert world location to landscape coordinates
    FIntPoint LandscapeCoord = WorldLocationToLandscapeCoordinate(Landscape, WorldLocation);
    
    // Sample height using landscape data access
    FLandscapeDataAccess DataAccess(LandscapeInfo);
    
    if (bUseInterpolation)
    {
        // Use interpolated sampling for higher quality
        float Height = 0.0f;
        if (DataAccess.GetHeightAtLocation(WorldLocation.X, WorldLocation.Y, Height))
        {
            return Height;
        }
    }
    else
    {
        // Use direct sampling for performance
        uint16 HeightValue = 0;
        if (DataAccess.GetHeightData(LandscapeCoord.X, LandscapeCoord.Y, HeightValue))
        {
            // Convert from uint16 to world height
            return LandscapeDataAccess::GetLocalHeight(HeightValue);
        }
    }

    return 0.0f;
}

FVector UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeNormal(ALandscape* Landscape, const FVector& WorldLocation)
{
    if (!Landscape)
    {
        return FVector::UpVector;
    }

    // Sample height at multiple points to calculate normal
    const float SampleDistance = 10.0f;
    
    float HeightCenter = SampleLandscapeHeight(Landscape, WorldLocation);
    float HeightRight = SampleLandscapeHeight(Landscape, WorldLocation + FVector(SampleDistance, 0.0f, 0.0f));
    float HeightForward = SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, SampleDistance, 0.0f));
    
    // Calculate tangent vectors
    FVector TangentX = FVector(SampleDistance, 0.0f, HeightRight - HeightCenter).GetSafeNormal();
    FVector TangentY = FVector(0.0f, SampleDistance, HeightForward - HeightCenter).GetSafeNormal();
    
    // Calculate normal using cross product
    FVector Normal = FVector::CrossProduct(TangentY, TangentX).GetSafeNormal();
    
    return Normal;
}

float UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeSlope(ALandscape* Landscape, const FVector& WorldLocation)
{
    if (!Landscape)
    {
        return 0.0f;
    }

    FVector Normal = SampleLandscapeNormal(Landscape, WorldLocation);
    
    // Calculate slope angle from normal
    float DotProduct = FVector::DotProduct(Normal, FVector::UpVector);
    float SlopeRadians = FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
    float SlopeDegrees = FMath::RadiansToDegrees(SlopeRadians);
    
    return SlopeDegrees;
}

float UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeLayer(ALandscape* Landscape, const FString& LayerName, const FVector& WorldLocation)
{
    if (!Landscape)
    {
        return 0.0f;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return 0.0f;
    }

    // Find layer info object
    ULandscapeLayerInfoObject* LayerInfo = nullptr;
    for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
    {
        if (LayerSettings.LayerInfoObj && LayerSettings.LayerName.ToString() == LayerName)
        {
            LayerInfo = LayerSettings.LayerInfoObj;
            break;
        }
    }

    if (!LayerInfo)
    {
        return 0.0f;
    }

    // Convert world location to landscape coordinates
    FIntPoint LandscapeCoord = WorldLocationToLandscapeCoordinate(Landscape, WorldLocation);
    
    // Sample layer weight
    FLandscapeDataAccess DataAccess(LandscapeInfo);
    uint8 WeightValue = 0;
    
    if (DataAccess.GetWeightData(LayerInfo, LandscapeCoord.X, LandscapeCoord.Y, WeightValue))
    {
        // Convert from uint8 to normalized weight (0-1)
        return static_cast<float>(WeightValue) / 255.0f;
    }

    return 0.0f;
}

TMap<FString, float> UAuracronPCGLandscapeIntegrationUtils::SampleAllLandscapeLayers(ALandscape* Landscape, const FVector& WorldLocation)
{
    TMap<FString, float> LayerValues;
    
    if (!Landscape)
    {
        return LayerValues;
    }

    TArray<FString> LayerNames = GetLandscapeLayerNames(Landscape);
    
    for (const FString& LayerName : LayerNames)
    {
        float LayerValue = SampleLandscapeLayer(Landscape, LayerName, WorldLocation);
        LayerValues.Add(LayerName, LayerValue);
    }

    return LayerValues;
}

bool UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(ALandscape* Landscape, const FVector& WorldLocation, float Height, float Radius, EAuracronPCGHeightModificationMode Mode)
{
    if (!Landscape)
    {
        return false;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return false;
    }

    // Calculate affected area
    FBox AffectedArea(WorldLocation - FVector(Radius), WorldLocation + FVector(Radius));
    
    // Get height data for the area
    TArray<float> HeightData;
    FIntPoint DataSize;
    if (!AuracronPCGLandscapeIntegrationUtils::GetLandscapeHeightData(Landscape, AffectedArea, HeightData, DataSize))
    {
        return false;
    }

    // Modify height data based on mode
    for (int32 Y = 0; Y < DataSize.Y; Y++)
    {
        for (int32 X = 0; X < DataSize.X; X++)
        {
            int32 Index = Y * DataSize.X + X;
            
            // Calculate world position for this data point
            FVector DataWorldPos = AffectedArea.Min + FVector(
                (static_cast<float>(X) / (DataSize.X - 1)) * AffectedArea.GetSize().X,
                (static_cast<float>(Y) / (DataSize.Y - 1)) * AffectedArea.GetSize().Y,
                0.0f
            );
            
            // Calculate distance from center
            float Distance = FVector::Dist2D(DataWorldPos, WorldLocation);
            if (Distance > Radius)
            {
                continue;
            }

            // Calculate brush strength based on distance
            float BrushStrength = 1.0f - (Distance / Radius);
            BrushStrength = FMath::Clamp(BrushStrength, 0.0f, 1.0f);

            // Apply height modification
            float& CurrentHeight = HeightData[Index];
            switch (Mode)
            {
                case EAuracronPCGHeightModificationMode::Absolute:
                    CurrentHeight = FMath::Lerp(CurrentHeight, Height, BrushStrength);
                    break;
                case EAuracronPCGHeightModificationMode::Additive:
                    CurrentHeight += Height * BrushStrength;
                    break;
                case EAuracronPCGHeightModificationMode::Subtractive:
                    CurrentHeight -= Height * BrushStrength;
                    break;
                default:
                    CurrentHeight = FMath::Lerp(CurrentHeight, Height, BrushStrength);
                    break;
            }
        }
    }

    // Set modified height data back to landscape
    return AuracronPCGLandscapeIntegrationUtils::SetLandscapeHeightData(Landscape, AffectedArea, HeightData, DataSize);
}

bool UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeightBatch(ALandscape* Landscape, const TArray<FVector>& Locations, const TArray<float>& Heights, float Radius, EAuracronPCGHeightModificationMode Mode)
{
    if (!Landscape || Locations.Num() != Heights.Num())
    {
        return false;
    }

    bool bSuccess = true;
    for (int32 i = 0; i < Locations.Num(); i++)
    {
        if (!ModifyLandscapeHeight(Landscape, Locations[i], Heights[i], Radius, Mode))
        {
            bSuccess = false;
        }
    }

    return bSuccess;
}

FIntPoint UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(ALandscape* Landscape, const FVector& WorldLocation)
{
    if (!Landscape)
    {
        return FIntPoint::ZeroValue;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return FIntPoint::ZeroValue;
    }

    // Transform world location to landscape space
    FTransform LandscapeTransform = Landscape->GetActorTransform();
    FVector LocalLocation = LandscapeTransform.InverseTransformPosition(WorldLocation);
    
    // Convert to landscape coordinates
    FIntPoint LandscapeCoord;
    LandscapeCoord.X = FMath::RoundToInt(LocalLocation.X);
    LandscapeCoord.Y = FMath::RoundToInt(LocalLocation.Y);
    
    return LandscapeCoord;
}

FVector UAuracronPCGLandscapeIntegrationUtils::LandscapeCoordinateToWorldLocation(ALandscape* Landscape, const FIntPoint& LandscapeCoordinate)
{
    if (!Landscape)
    {
        return FVector::ZeroVector;
    }

    // Convert landscape coordinates to local space
    FVector LocalLocation(static_cast<float>(LandscapeCoordinate.X), static_cast<float>(LandscapeCoordinate.Y), 0.0f);
    
    // Transform to world space
    FTransform LandscapeTransform = Landscape->GetActorTransform();
    return LandscapeTransform.TransformPosition(LocalLocation);
}

FBox UAuracronPCGLandscapeIntegrationUtils::GetLandscapeBounds(ALandscape* Landscape)
{
    if (!Landscape)
    {
        return FBox(ForceInit);
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return FBox(ForceInit);
    }

    // Get landscape bounds from info
    FIntRect LandscapeRect;
    if (LandscapeInfo->GetLandscapeExtent(LandscapeRect.Min.X, LandscapeRect.Min.Y, LandscapeRect.Max.X, LandscapeRect.Max.Y))
    {
        // Convert landscape coordinates to world coordinates
        FVector MinWorld = LandscapeCoordinateToWorldLocation(Landscape, LandscapeRect.Min);
        FVector MaxWorld = LandscapeCoordinateToWorldLocation(Landscape, LandscapeRect.Max);
        
        return FBox(MinWorld, MaxWorld);
    }

    return FBox(ForceInit);
}

TArray<FString> UAuracronPCGLandscapeIntegrationUtils::GetLandscapeLayerNames(ALandscape* Landscape)
{
    TArray<FString> LayerNames;
    
    if (!Landscape)
    {
        return LayerNames;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return LayerNames;
    }

    // Get all layer names from landscape info
    for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
    {
        if (LayerSettings.LayerInfoObj)
        {
            LayerNames.Add(LayerSettings.LayerName.ToString());
        }
    }

    return LayerNames;
}
