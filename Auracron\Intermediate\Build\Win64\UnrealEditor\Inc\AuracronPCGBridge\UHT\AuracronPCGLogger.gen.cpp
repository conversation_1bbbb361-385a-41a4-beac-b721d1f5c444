// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGLogger.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGLogger() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLogConfig();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLogEntry();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronPCGLogEntry **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry;
class UScriptStruct* FAuracronPCGLogEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGLogEntry, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGLogEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Log entry structure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log entry structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadId_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineNumber_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceFile_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LogLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Context;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ThreadId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LineNumber;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceFile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGLogEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_LogLevel = { "LogLevel", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, LogLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogLevel_MetaData), NewProp_LogLevel_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, Context), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_ThreadId = { "ThreadId", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, ThreadId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadId_MetaData), NewProp_ThreadId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_LineNumber = { "LineNumber", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, LineNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineNumber_MetaData), NewProp_LineNumber_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_SourceFile = { "SourceFile", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogEntry, SourceFile), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceFile_MetaData), NewProp_SourceFile_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_LogLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_ThreadId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_LineNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewProp_SourceFile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGLogEntry",
	Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::PropPointers),
	sizeof(FAuracronPCGLogEntry),
	alignof(FAuracronPCGLogEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLogEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGLogEntry ************************************************

// ********** Begin ScriptStruct FAuracronPCGLogConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig;
class UScriptStruct* FAuracronPCGLogConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGLogConfig, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGLogConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Log configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFileLogging_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableConsoleLogging_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceLogging_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogFilePath_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLogFileSize_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLogFiles_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 10MB\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "10MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryEntries_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoFlush_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlushIntervalSeconds_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPCGLogger.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableFileLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFileLogging;
	static void NewProp_bEnableConsoleLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableConsoleLogging;
	static void NewProp_bEnablePerformanceLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceLogging;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LogFilePath;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLogFileSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLogFiles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryEntries;
	static void NewProp_bAutoFlush_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoFlush;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlushIntervalSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGLogConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableFileLogging_SetBit(void* Obj)
{
	((FAuracronPCGLogConfig*)Obj)->bEnableFileLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableFileLogging = { "bEnableFileLogging", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLogConfig), &Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableFileLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFileLogging_MetaData), NewProp_bEnableFileLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableConsoleLogging_SetBit(void* Obj)
{
	((FAuracronPCGLogConfig*)Obj)->bEnableConsoleLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableConsoleLogging = { "bEnableConsoleLogging", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLogConfig), &Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableConsoleLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableConsoleLogging_MetaData), NewProp_bEnableConsoleLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnablePerformanceLogging_SetBit(void* Obj)
{
	((FAuracronPCGLogConfig*)Obj)->bEnablePerformanceLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnablePerformanceLogging = { "bEnablePerformanceLogging", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLogConfig), &Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnablePerformanceLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceLogging_MetaData), NewProp_bEnablePerformanceLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((FAuracronPCGLogConfig*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLogConfig), &Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_LogFilePath = { "LogFilePath", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogConfig, LogFilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogFilePath_MetaData), NewProp_LogFilePath_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxLogFileSize = { "MaxLogFileSize", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogConfig, MaxLogFileSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLogFileSize_MetaData), NewProp_MaxLogFileSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxLogFiles = { "MaxLogFiles", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogConfig, MaxLogFiles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLogFiles_MetaData), NewProp_MaxLogFiles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxMemoryEntries = { "MaxMemoryEntries", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogConfig, MaxMemoryEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryEntries_MetaData), NewProp_MaxMemoryEntries_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bAutoFlush_SetBit(void* Obj)
{
	((FAuracronPCGLogConfig*)Obj)->bAutoFlush = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bAutoFlush = { "bAutoFlush", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGLogConfig), &Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bAutoFlush_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoFlush_MetaData), NewProp_bAutoFlush_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_FlushIntervalSeconds = { "FlushIntervalSeconds", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGLogConfig, FlushIntervalSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlushIntervalSeconds_MetaData), NewProp_FlushIntervalSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableFileLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableConsoleLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnablePerformanceLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_LogFilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxLogFileSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxLogFiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_MaxMemoryEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_bAutoFlush,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewProp_FlushIntervalSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGLogConfig",
	Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::PropPointers),
	sizeof(FAuracronPCGLogConfig),
	alignof(FAuracronPCGLogConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGLogConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGLogConfig ***********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGLogEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGLogEntry_Statics::NewStructOps, TEXT("AuracronPCGLogEntry"), &Z_Registration_Info_UScriptStruct_FAuracronPCGLogEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGLogEntry), 412310153U) },
		{ FAuracronPCGLogConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGLogConfig_Statics::NewStructOps, TEXT("AuracronPCGLogConfig"), &Z_Registration_Info_UScriptStruct_FAuracronPCGLogConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGLogConfig), 2218838205U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h__Script_AuracronPCGBridge_362274444(TEXT("/Script/AuracronPCGBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLogger_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
