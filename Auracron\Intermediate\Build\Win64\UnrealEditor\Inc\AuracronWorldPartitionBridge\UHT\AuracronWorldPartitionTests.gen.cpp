// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionTests.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionTests() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestResult();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestSuiteResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronTestCategory *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTestCategory;
static UEnum* EAuracronTestCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTestCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronTestCategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronTestCategory.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestCategory>()
{
	return EAuracronTestCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test categories\n" },
#endif
		{ "Integration.DisplayName", "Integration Tests" },
		{ "Integration.Name", "EAuracronTestCategory::Integration" },
		{ "LargeWorld.DisplayName", "Large World Tests" },
		{ "LargeWorld.Name", "EAuracronTestCategory::LargeWorld" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
		{ "Performance.DisplayName", "Performance Tests" },
		{ "Performance.Name", "EAuracronTestCategory::Performance" },
		{ "Regression.DisplayName", "Regression Tests" },
		{ "Regression.Name", "EAuracronTestCategory::Regression" },
		{ "Streaming.DisplayName", "Streaming Tests" },
		{ "Streaming.Name", "EAuracronTestCategory::Streaming" },
		{ "Stress.DisplayName", "Stress Tests" },
		{ "Stress.Name", "EAuracronTestCategory::Stress" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test categories" },
#endif
		{ "Unit.DisplayName", "Unit Tests" },
		{ "Unit.Name", "EAuracronTestCategory::Unit" },
		{ "Validation.DisplayName", "Validation Tests" },
		{ "Validation.Name", "EAuracronTestCategory::Validation" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTestCategory::Unit", (int64)EAuracronTestCategory::Unit },
		{ "EAuracronTestCategory::Integration", (int64)EAuracronTestCategory::Integration },
		{ "EAuracronTestCategory::Performance", (int64)EAuracronTestCategory::Performance },
		{ "EAuracronTestCategory::Streaming", (int64)EAuracronTestCategory::Streaming },
		{ "EAuracronTestCategory::LargeWorld", (int64)EAuracronTestCategory::LargeWorld },
		{ "EAuracronTestCategory::Stress", (int64)EAuracronTestCategory::Stress },
		{ "EAuracronTestCategory::Regression", (int64)EAuracronTestCategory::Regression },
		{ "EAuracronTestCategory::Validation", (int64)EAuracronTestCategory::Validation },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronTestCategory",
	"EAuracronTestCategory",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTestCategory.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTestCategory.InnerSingleton;
}
// ********** End Enum EAuracronTestCategory *******************************************************

// ********** Begin Enum EAuracronTestSeverity *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTestSeverity;
static UEnum* EAuracronTestSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTestSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronTestSeverity"));
	}
	return Z_Registration_Info_UEnum_EAuracronTestSeverity.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestSeverity>()
{
	return EAuracronTestSeverity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test severity levels\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronTestSeverity::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronTestSeverity::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronTestSeverity::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronTestSeverity::Medium" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test severity levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTestSeverity::Low", (int64)EAuracronTestSeverity::Low },
		{ "EAuracronTestSeverity::Medium", (int64)EAuracronTestSeverity::Medium },
		{ "EAuracronTestSeverity::High", (int64)EAuracronTestSeverity::High },
		{ "EAuracronTestSeverity::Critical", (int64)EAuracronTestSeverity::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronTestSeverity",
	"EAuracronTestSeverity",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTestSeverity.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTestSeverity.InnerSingleton;
}
// ********** End Enum EAuracronTestSeverity *******************************************************

// ********** Begin Enum EAuracronTestExecutionMode ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTestExecutionMode;
static UEnum* EAuracronTestExecutionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestExecutionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTestExecutionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronTestExecutionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronTestExecutionMode.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronTestExecutionMode>()
{
	return EAuracronTestExecutionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test execution modes\n" },
#endif
		{ "Continuous.DisplayName", "Continuous" },
		{ "Continuous.Name", "EAuracronTestExecutionMode::Continuous" },
		{ "Isolated.DisplayName", "Isolated" },
		{ "Isolated.Name", "EAuracronTestExecutionMode::Isolated" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
		{ "Parallel.DisplayName", "Parallel" },
		{ "Parallel.Name", "EAuracronTestExecutionMode::Parallel" },
		{ "Sequential.DisplayName", "Sequential" },
		{ "Sequential.Name", "EAuracronTestExecutionMode::Sequential" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTestExecutionMode::Sequential", (int64)EAuracronTestExecutionMode::Sequential },
		{ "EAuracronTestExecutionMode::Parallel", (int64)EAuracronTestExecutionMode::Parallel },
		{ "EAuracronTestExecutionMode::Isolated", (int64)EAuracronTestExecutionMode::Isolated },
		{ "EAuracronTestExecutionMode::Continuous", (int64)EAuracronTestExecutionMode::Continuous },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronTestExecutionMode",
	"EAuracronTestExecutionMode",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronTestExecutionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTestExecutionMode.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTestExecutionMode.InnerSingleton;
}
// ********** End Enum EAuracronTestExecutionMode **************************************************

// ********** Begin ScriptStruct FAuracronTestConfiguration ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration;
class UScriptStruct* FAuracronTestConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTestConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronTestConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Test Configuration\n * Configuration settings for World Partition testing\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Configuration\nConfiguration settings for World Partition testing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableUnitTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableIntegrationTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreamingTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLargeWorldTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStressTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionMode_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestTimeout_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentTests_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateDetailedReports_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCapturePerformanceMetrics_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateMemoryUsage_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceThresholdMs_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryThresholdMB_MetaData[] = {
		{ "Category", "Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 60 FPS\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "60 FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LargeWorldCellCount_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StressTestDuration_MetaData[] = {
		{ "Category", "Test Configuration" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestOutputDirectory_MetaData[] = {
		{ "Category", "Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "seconds" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableUnitTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableUnitTests;
	static void NewProp_bEnableIntegrationTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableIntegrationTests;
	static void NewProp_bEnablePerformanceTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceTests;
	static void NewProp_bEnableStreamingTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreamingTests;
	static void NewProp_bEnableLargeWorldTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLargeWorldTests;
	static void NewProp_bEnableStressTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStressTests;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TestTimeout;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentTests;
	static void NewProp_bGenerateDetailedReports_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateDetailedReports;
	static void NewProp_bCapturePerformanceMetrics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCapturePerformanceMetrics;
	static void NewProp_bValidateMemoryUsage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateMemoryUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceThresholdMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryThresholdMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LargeWorldCellCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StressTestDuration;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestOutputDirectory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTestConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableUnitTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnableUnitTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableUnitTests = { "bEnableUnitTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableUnitTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableUnitTests_MetaData), NewProp_bEnableUnitTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableIntegrationTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnableIntegrationTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableIntegrationTests = { "bEnableIntegrationTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableIntegrationTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableIntegrationTests_MetaData), NewProp_bEnableIntegrationTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnablePerformanceTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnablePerformanceTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnablePerformanceTests = { "bEnablePerformanceTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnablePerformanceTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceTests_MetaData), NewProp_bEnablePerformanceTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStreamingTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnableStreamingTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStreamingTests = { "bEnableStreamingTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStreamingTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreamingTests_MetaData), NewProp_bEnableStreamingTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableLargeWorldTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnableLargeWorldTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableLargeWorldTests = { "bEnableLargeWorldTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableLargeWorldTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLargeWorldTests_MetaData), NewProp_bEnableLargeWorldTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStressTests_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bEnableStressTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStressTests = { "bEnableStressTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStressTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStressTests_MetaData), NewProp_bEnableStressTests_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, ExecutionMode), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestExecutionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionMode_MetaData), NewProp_ExecutionMode_MetaData) }; // 3399416300
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_TestTimeout = { "TestTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, TestTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestTimeout_MetaData), NewProp_TestTimeout_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_MaxConcurrentTests = { "MaxConcurrentTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, MaxConcurrentTests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentTests_MetaData), NewProp_MaxConcurrentTests_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bGenerateDetailedReports_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bGenerateDetailedReports = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bGenerateDetailedReports = { "bGenerateDetailedReports", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bGenerateDetailedReports_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateDetailedReports_MetaData), NewProp_bGenerateDetailedReports_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bCapturePerformanceMetrics_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bCapturePerformanceMetrics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bCapturePerformanceMetrics = { "bCapturePerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bCapturePerformanceMetrics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCapturePerformanceMetrics_MetaData), NewProp_bCapturePerformanceMetrics_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bValidateMemoryUsage_SetBit(void* Obj)
{
	((FAuracronTestConfiguration*)Obj)->bValidateMemoryUsage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bValidateMemoryUsage = { "bValidateMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestConfiguration), &Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bValidateMemoryUsage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateMemoryUsage_MetaData), NewProp_bValidateMemoryUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_PerformanceThresholdMs = { "PerformanceThresholdMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, PerformanceThresholdMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceThresholdMs_MetaData), NewProp_PerformanceThresholdMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_MemoryThresholdMB = { "MemoryThresholdMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, MemoryThresholdMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryThresholdMB_MetaData), NewProp_MemoryThresholdMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_LargeWorldCellCount = { "LargeWorldCellCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, LargeWorldCellCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LargeWorldCellCount_MetaData), NewProp_LargeWorldCellCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_StressTestDuration = { "StressTestDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, StressTestDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StressTestDuration_MetaData), NewProp_StressTestDuration_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_TestOutputDirectory = { "TestOutputDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestConfiguration, TestOutputDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestOutputDirectory_MetaData), NewProp_TestOutputDirectory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableUnitTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableIntegrationTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnablePerformanceTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStreamingTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableLargeWorldTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bEnableStressTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_TestTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_MaxConcurrentTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bGenerateDetailedReports,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bCapturePerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_bValidateMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_PerformanceThresholdMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_MemoryThresholdMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_LargeWorldCellCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_StressTestDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewProp_TestOutputDirectory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronTestConfiguration",
	Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::PropPointers),
	sizeof(FAuracronTestConfiguration),
	alignof(FAuracronTestConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTestConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronTestResult ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTestResult;
class UScriptStruct* FAuracronTestResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTestResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTestResult, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronTestResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTestResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Test Result\n * Result data from a single test execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Result\nResult data from a single test execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestName_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestCategory_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPassed_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTime_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimestamp_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalData_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Warnings_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestCategory_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestCategory;
	static void NewProp_bPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPassed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExecutionTimestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AdditionalData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Warnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Warnings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTestResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, TestName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestName_MetaData), NewProp_TestName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestCategory_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestCategory = { "TestCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, TestCategory), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestCategory_MetaData), NewProp_TestCategory_MetaData) }; // 2406710211
void Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_bPassed_SetBit(void* Obj)
{
	((FAuracronTestResult*)Obj)->bPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_bPassed = { "bPassed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTestResult), &Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_bPassed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPassed_MetaData), NewProp_bPassed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ExecutionTime = { "ExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, ExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTime_MetaData), NewProp_ExecutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ExecutionTimestamp = { "ExecutionTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, ExecutionTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimestamp_MetaData), NewProp_ExecutionTimestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData_ValueProp = { "AdditionalData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData_Key_KeyProp = { "AdditionalData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData = { "AdditionalData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, AdditionalData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalData_MetaData), NewProp_AdditionalData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_Warnings_Inner = { "Warnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_Warnings = { "Warnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestResult, Warnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Warnings_MetaData), NewProp_Warnings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTestResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestCategory_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_TestCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_bPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_ExecutionTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_AdditionalData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_Warnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewProp_Warnings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTestResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronTestResult",
	Z_Construct_UScriptStruct_FAuracronTestResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestResult_Statics::PropPointers),
	sizeof(FAuracronTestResult),
	alignof(FAuracronTestResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTestResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTestResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTestResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTestResult *************************************************

// ********** Begin ScriptStruct FAuracronTestSuiteResult ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult;
class UScriptStruct* FAuracronTestSuiteResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTestSuiteResult, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronTestSuiteResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Test Suite Result\n * Aggregated results from a test suite execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Suite Result\nAggregated results from a test suite execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuiteName_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalTests_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PassedTests_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedTests_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkippedTests_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalExecutionTime_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageExecutionTime_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakMemoryUsageMB_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimestamp_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestResults_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Test Suite Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuiteName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalTests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PassedTests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedTests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SkippedTests;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalExecutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageExecutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeakMemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExecutionTimestamp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestResults_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestResults;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTestSuiteResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_SuiteName = { "SuiteName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, SuiteName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuiteName_MetaData), NewProp_SuiteName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TotalTests = { "TotalTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, TotalTests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalTests_MetaData), NewProp_TotalTests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PassedTests = { "PassedTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, PassedTests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PassedTests_MetaData), NewProp_PassedTests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_FailedTests = { "FailedTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, FailedTests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedTests_MetaData), NewProp_FailedTests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_SkippedTests = { "SkippedTests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, SkippedTests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkippedTests_MetaData), NewProp_SkippedTests_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TotalExecutionTime = { "TotalExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, TotalExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalExecutionTime_MetaData), NewProp_TotalExecutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_AverageExecutionTime = { "AverageExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, AverageExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageExecutionTime_MetaData), NewProp_AverageExecutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PeakMemoryUsageMB = { "PeakMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, PeakMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakMemoryUsageMB_MetaData), NewProp_PeakMemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_ExecutionTimestamp = { "ExecutionTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, ExecutionTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimestamp_MetaData), NewProp_ExecutionTimestamp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TestResults_Inner = { "TestResults", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TestResults = { "TestResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, TestResults), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestResults_MetaData), NewProp_TestResults_MetaData) }; // 737110479
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTestSuiteResult, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_SuiteName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TotalTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PassedTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_FailedTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_SkippedTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TotalExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_AverageExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PeakMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_ExecutionTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TestResults_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_TestResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewProp_PerformanceMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronTestSuiteResult",
	Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::PropPointers),
	sizeof(FAuracronTestSuiteResult),
	alignof(FAuracronTestSuiteResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTestSuiteResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTestSuiteResult ********************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms
	{
		FString TestName;
		FAuracronTestResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms, TestName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms, Result), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "OnTestCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionTestManager::FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, const FString& TestName, FAuracronTestResult Result)
{
	struct AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms
	{
		FString TestName;
		FAuracronTestResult Result;
	};
	AuracronWorldPartitionTestManager_eventOnTestCompleted_Parms Parms;
	Parms.TestName=TestName;
	Parms.Result=Result;
	OnTestCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnTestSuiteCompleted *************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms
	{
		FString SuiteName;
		FAuracronTestSuiteResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuiteName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::NewProp_SuiteName = { "SuiteName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms, SuiteName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms, Result), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::NewProp_SuiteName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "OnTestSuiteCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionTestManager::FOnTestSuiteCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestSuiteCompleted, const FString& SuiteName, FAuracronTestSuiteResult Result)
{
	struct AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms
	{
		FString SuiteName;
		FAuracronTestSuiteResult Result;
	};
	AuracronWorldPartitionTestManager_eventOnTestSuiteCompleted_Parms Parms;
	Parms.SuiteName=SuiteName;
	Parms.Result=Result;
	OnTestSuiteCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestSuiteCompleted ***************************************************

// ********** Begin Delegate FOnTestFailed *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics
{
	struct AuracronWorldPartitionTestManager_eventOnTestFailed_Parms
	{
		FString TestName;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestFailed_Parms, TestName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventOnTestFailed_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "OnTestFailed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::AuracronWorldPartitionTestManager_eventOnTestFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionTestManager::FOnTestFailed_DelegateWrapper(const FMulticastScriptDelegate& OnTestFailed, const FString& TestName, const FString& ErrorMessage)
{
	struct AuracronWorldPartitionTestManager_eventOnTestFailed_Parms
	{
		FString TestName;
		FString ErrorMessage;
	};
	AuracronWorldPartitionTestManager_eventOnTestFailed_Parms Parms;
	Parms.TestName=TestName;
	Parms.ErrorMessage=ErrorMessage;
	OnTestFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestFailed ***********************************************************

// ********** Begin Class UAuracronWorldPartitionTestManager Function CancelRunningTests ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "CancelRunningTests", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execCancelRunningTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelRunningTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function CancelRunningTests *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function ClearTestHistory *************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "ClearTestHistory", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execClearTestHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearTestHistory();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function ClearTestHistory ***************

// ********** Begin Class UAuracronWorldPartitionTestManager Function GenerateTestReport ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics
{
	struct AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms
	{
		FAuracronTestSuiteResult SuiteResult;
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test reporting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuiteResult_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SuiteResult;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_SuiteResult = { "SuiteResult", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms, SuiteResult), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuiteResult_MetaData), NewProp_SuiteResult_MetaData) }; // 4016154621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_SuiteResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "GenerateTestReport", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::AuracronWorldPartitionTestManager_eventGenerateTestReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execGenerateTestReport)
{
	P_GET_STRUCT_REF(FAuracronTestSuiteResult,Z_Param_Out_SuiteResult);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateTestReport(Z_Param_Out_SuiteResult,Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function GenerateTestReport *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function GenerateTestSummary **********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics
{
	struct AuracronWorldPartitionTestManager_eventGenerateTestSummary_Parms
	{
		FAuracronTestSuiteResult SuiteResult;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuiteResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SuiteResult;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::NewProp_SuiteResult = { "SuiteResult", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGenerateTestSummary_Parms, SuiteResult), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuiteResult_MetaData), NewProp_SuiteResult_MetaData) }; // 4016154621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGenerateTestSummary_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::NewProp_SuiteResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "GenerateTestSummary", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::AuracronWorldPartitionTestManager_eventGenerateTestSummary_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::AuracronWorldPartitionTestManager_eventGenerateTestSummary_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execGenerateTestSummary)
{
	P_GET_STRUCT_REF(FAuracronTestSuiteResult,Z_Param_Out_SuiteResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateTestSummary(Z_Param_Out_SuiteResult);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function GenerateTestSummary ************

// ********** Begin Class UAuracronWorldPartitionTestManager Function GetConfiguration *************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionTestManager_eventGetConfiguration_Parms
	{
		FAuracronTestConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestConfiguration, METADATA_PARAMS(0, nullptr) }; // 3535147630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::AuracronWorldPartitionTestManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::AuracronWorldPartitionTestManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function GetConfiguration ***************

// ********** Begin Class UAuracronWorldPartitionTestManager Function GetInstance ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics
{
	struct AuracronWorldPartitionTestManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionTestManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::AuracronWorldPartitionTestManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::AuracronWorldPartitionTestManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionTestManager**)Z_Param__Result=UAuracronWorldPartitionTestManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function GetInstance ********************

// ********** Begin Class UAuracronWorldPartitionTestManager Function GetTestHistory ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics
{
	struct AuracronWorldPartitionTestManager_eventGetTestHistory_Parms
	{
		TArray<FAuracronTestSuiteResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventGetTestHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "GetTestHistory", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::AuracronWorldPartitionTestManager_eventGetTestHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::AuracronWorldPartitionTestManager_eventGetTestHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execGetTestHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronTestSuiteResult>*)Z_Param__Result=P_THIS->GetTestHistory();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function GetTestHistory *****************

// ********** Begin Class UAuracronWorldPartitionTestManager Function Initialize *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics
{
	struct AuracronWorldPartitionTestManager_eventInitialize_Parms
	{
		FAuracronTestConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronTestConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3535147630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::AuracronWorldPartitionTestManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::AuracronWorldPartitionTestManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronTestConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function Initialize *********************

// ********** Begin Class UAuracronWorldPartitionTestManager Function IsInitialized ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionTestManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionTestManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionTestManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::AuracronWorldPartitionTestManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::AuracronWorldPartitionTestManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function IsInitialized ******************

// ********** Begin Class UAuracronWorldPartitionTestManager Function IsTestRunning ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics
{
	struct AuracronWorldPartitionTestManager_eventIsTestRunning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionTestManager_eventIsTestRunning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionTestManager_eventIsTestRunning_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "IsTestRunning", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::AuracronWorldPartitionTestManager_eventIsTestRunning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::AuracronWorldPartitionTestManager_eventIsTestRunning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execIsTestRunning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTestRunning();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function IsTestRunning ******************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunAllTests ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunAllTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test execution\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunAllTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunAllTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::AuracronWorldPartitionTestManager_eventRunAllTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::AuracronWorldPartitionTestManager_eventRunAllTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunAllTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunAllTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunAllTests ********************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunIntegrationTests **********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunIntegrationTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunIntegrationTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunIntegrationTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::AuracronWorldPartitionTestManager_eventRunIntegrationTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::AuracronWorldPartitionTestManager_eventRunIntegrationTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunIntegrationTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunIntegrationTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunIntegrationTests ************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunLargeWorldTests ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunLargeWorldTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Large world tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Large world tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunLargeWorldTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunLargeWorldTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::AuracronWorldPartitionTestManager_eventRunLargeWorldTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::AuracronWorldPartitionTestManager_eventRunLargeWorldTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunLargeWorldTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunLargeWorldTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunLargeWorldTests *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunPerformanceTests **********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunPerformanceTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunPerformanceTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunPerformanceTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::AuracronWorldPartitionTestManager_eventRunPerformanceTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::AuracronWorldPartitionTestManager_eventRunPerformanceTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunPerformanceTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunPerformanceTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunPerformanceTests ************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunSingleTest ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunSingleTest_Parms
	{
		FString TestName;
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunSingleTest_Parms, TestName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestName_MetaData), NewProp_TestName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunSingleTest_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunSingleTest", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::AuracronWorldPartitionTestManager_eventRunSingleTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::AuracronWorldPartitionTestManager_eventRunSingleTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunSingleTest)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TestName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->RunSingleTest(Z_Param_TestName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunSingleTest ******************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunStreamingTests ************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunStreamingTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunStreamingTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunStreamingTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::AuracronWorldPartitionTestManager_eventRunStreamingTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::AuracronWorldPartitionTestManager_eventRunStreamingTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunStreamingTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunStreamingTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunStreamingTests **************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunStressTests ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunStressTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Stress tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stress tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunStressTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunStressTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::AuracronWorldPartitionTestManager_eventRunStressTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::AuracronWorldPartitionTestManager_eventRunStressTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunStressTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunStressTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunStressTests *****************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunTestCategory **************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunTestCategory_Parms
	{
		EAuracronTestCategory Category;
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunTestCategory_Parms, Category), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory, METADATA_PARAMS(0, nullptr) }; // 2406710211
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunTestCategory_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunTestCategory", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::AuracronWorldPartitionTestManager_eventRunTestCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::AuracronWorldPartitionTestManager_eventRunTestCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunTestCategory)
{
	P_GET_ENUM(EAuracronTestCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunTestCategory(EAuracronTestCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunTestCategory ****************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunTestsAsync ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunTestsAsync_Parms
	{
		EAuracronTestCategory Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunTestsAsync_Parms, Category), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronTestCategory, METADATA_PARAMS(0, nullptr) }; // 2406710211
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunTestsAsync", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::AuracronWorldPartitionTestManager_eventRunTestsAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::AuracronWorldPartitionTestManager_eventRunTestsAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunTestsAsync)
{
	P_GET_ENUM(EAuracronTestCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunTestsAsync(EAuracronTestCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunTestsAsync ******************

// ********** Begin Class UAuracronWorldPartitionTestManager Function RunUnitTests *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics
{
	struct AuracronWorldPartitionTestManager_eventRunUnitTests_Parms
	{
		FAuracronTestSuiteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unit tests\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit tests" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventRunUnitTests_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestSuiteResult, METADATA_PARAMS(0, nullptr) }; // 4016154621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "RunUnitTests", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::AuracronWorldPartitionTestManager_eventRunUnitTests_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::AuracronWorldPartitionTestManager_eventRunUnitTests_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execRunUnitTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestSuiteResult*)Z_Param__Result=P_THIS->RunUnitTests();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function RunUnitTests *******************

// ********** Begin Class UAuracronWorldPartitionTestManager Function SetConfiguration *************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionTestManager_eventSetConfiguration_Parms
	{
		FAuracronTestConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronTestConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3535147630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::AuracronWorldPartitionTestManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::AuracronWorldPartitionTestManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronTestConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function SetConfiguration ***************

// ********** Begin Class UAuracronWorldPartitionTestManager Function Shutdown *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function Shutdown ***********************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestCellLoadingUnloading *****
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestCellLoadingUnloading_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestCellLoadingUnloading_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestCellLoadingUnloading", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::AuracronWorldPartitionTestManager_eventTestCellLoadingUnloading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::AuracronWorldPartitionTestManager_eventTestCellLoadingUnloading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestCellLoadingUnloading)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestCellLoadingUnloading();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestCellLoadingUnloading *******

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestCellManagement ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestCellManagement_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestCellManagement_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestCellManagement", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::AuracronWorldPartitionTestManager_eventTestCellManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::AuracronWorldPartitionTestManager_eventTestCellManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestCellManagement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestCellManagement();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestCellManagement *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestContinuousStreaming ******
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestContinuousStreaming_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestContinuousStreaming_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestContinuousStreaming", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::AuracronWorldPartitionTestManager_eventTestContinuousStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::AuracronWorldPartitionTestManager_eventTestContinuousStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestContinuousStreaming)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestContinuousStreaming();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestContinuousStreaming ********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestCPUPerformance ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestCPUPerformance_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestCPUPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestCPUPerformance", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::AuracronWorldPartitionTestManager_eventTestCPUPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::AuracronWorldPartitionTestManager_eventTestCPUPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestCPUPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestCPUPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestCPUPerformance *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestDebugSystem **************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestDebugSystem_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestDebugSystem_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestDebugSystem", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::AuracronWorldPartitionTestManager_eventTestDebugSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::AuracronWorldPartitionTestManager_eventTestDebugSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestDebugSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestDebugSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestDebugSystem ****************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestGridSystemCreation *******
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestGridSystemCreation_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestGridSystemCreation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestGridSystemCreation", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::AuracronWorldPartitionTestManager_eventTestGridSystemCreation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::AuracronWorldPartitionTestManager_eventTestGridSystemCreation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestGridSystemCreation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestGridSystemCreation();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestGridSystemCreation *********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestLargeWorldCreation *******
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestLargeWorldCreation_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestLargeWorldCreation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestLargeWorldCreation", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::AuracronWorldPartitionTestManager_eventTestLargeWorldCreation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::AuracronWorldPartitionTestManager_eventTestLargeWorldCreation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestLargeWorldCreation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestLargeWorldCreation();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestLargeWorldCreation *********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestLumenIntegration *********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestLumenIntegration_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestLumenIntegration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestLumenIntegration", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::AuracronWorldPartitionTestManager_eventTestLumenIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::AuracronWorldPartitionTestManager_eventTestLumenIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestLumenIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestLumenIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestLumenIntegration ***********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestMassiveStreamingLoad *****
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestMassiveStreamingLoad_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestMassiveStreamingLoad_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestMassiveStreamingLoad", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::AuracronWorldPartitionTestManager_eventTestMassiveStreamingLoad_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::AuracronWorldPartitionTestManager_eventTestMassiveStreamingLoad_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestMassiveStreamingLoad)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestMassiveStreamingLoad();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestMassiveStreamingLoad *******

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestMemoryStress *************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestMemoryStress_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestMemoryStress_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestMemoryStress", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::AuracronWorldPartitionTestManager_eventTestMemoryStress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::AuracronWorldPartitionTestManager_eventTestMemoryStress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestMemoryStress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestMemoryStress();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestMemoryStress ***************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestMemoryUsage **************
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestMemoryUsage_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestMemoryUsage_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::AuracronWorldPartitionTestManager_eventTestMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::AuracronWorldPartitionTestManager_eventTestMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestMemoryUsage ****************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestPCGIntegration ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestPCGIntegration_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestPCGIntegration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestPCGIntegration", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::AuracronWorldPartitionTestManager_eventTestPCGIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::AuracronWorldPartitionTestManager_eventTestPCGIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestPCGIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestPCGIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestPCGIntegration *************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestPerformanceMonitoring ****
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestPerformanceMonitoring_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestPerformanceMonitoring_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestPerformanceMonitoring", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::AuracronWorldPartitionTestManager_eventTestPerformanceMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::AuracronWorldPartitionTestManager_eventTestPerformanceMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestPerformanceMonitoring ******

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestStreamingPerformance *****
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestStreamingPerformance_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestStreamingPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestStreamingPerformance", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::AuracronWorldPartitionTestManager_eventTestStreamingPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::AuracronWorldPartitionTestManager_eventTestStreamingPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestStreamingPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestStreamingPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestStreamingPerformance *******

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestStreamingPriority ********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestStreamingPriority_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestStreamingPriority_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestStreamingPriority", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::AuracronWorldPartitionTestManager_eventTestStreamingPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::AuracronWorldPartitionTestManager_eventTestStreamingPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestStreamingPriority)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestStreamingPriority();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestStreamingPriority **********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestStreamingStability *******
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestStreamingStability_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestStreamingStability_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestStreamingStability", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::AuracronWorldPartitionTestManager_eventTestStreamingStability_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::AuracronWorldPartitionTestManager_eventTestStreamingStability_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestStreamingStability)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestStreamingStability();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestStreamingStability *********

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestStreamingSystem **********
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestStreamingSystem_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestStreamingSystem_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestStreamingSystem", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::AuracronWorldPartitionTestManager_eventTestStreamingSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::AuracronWorldPartitionTestManager_eventTestStreamingSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestStreamingSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestStreamingSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestStreamingSystem ************

// ********** Begin Class UAuracronWorldPartitionTestManager Function TestWorldPartitionIntegration 
struct Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics
{
	struct AuracronWorldPartitionTestManager_eventTestWorldPartitionIntegration_Parms
	{
		FAuracronTestResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Test Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionTestManager_eventTestWorldPartitionIntegration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTestResult, METADATA_PARAMS(0, nullptr) }; // 737110479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionTestManager, nullptr, "TestWorldPartitionIntegration", Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::AuracronWorldPartitionTestManager_eventTestWorldPartitionIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::AuracronWorldPartitionTestManager_eventTestWorldPartitionIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionTestManager::execTestWorldPartitionIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTestResult*)Z_Param__Result=P_THIS->TestWorldPartitionIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionTestManager Function TestWorldPartitionIntegration **

// ********** Begin Class UAuracronWorldPartitionTestManager ***************************************
void UAuracronWorldPartitionTestManager::StaticRegisterNativesUAuracronWorldPartitionTestManager()
{
	UClass* Class = UAuracronWorldPartitionTestManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelRunningTests", &UAuracronWorldPartitionTestManager::execCancelRunningTests },
		{ "ClearTestHistory", &UAuracronWorldPartitionTestManager::execClearTestHistory },
		{ "GenerateTestReport", &UAuracronWorldPartitionTestManager::execGenerateTestReport },
		{ "GenerateTestSummary", &UAuracronWorldPartitionTestManager::execGenerateTestSummary },
		{ "GetConfiguration", &UAuracronWorldPartitionTestManager::execGetConfiguration },
		{ "GetInstance", &UAuracronWorldPartitionTestManager::execGetInstance },
		{ "GetTestHistory", &UAuracronWorldPartitionTestManager::execGetTestHistory },
		{ "Initialize", &UAuracronWorldPartitionTestManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionTestManager::execIsInitialized },
		{ "IsTestRunning", &UAuracronWorldPartitionTestManager::execIsTestRunning },
		{ "RunAllTests", &UAuracronWorldPartitionTestManager::execRunAllTests },
		{ "RunIntegrationTests", &UAuracronWorldPartitionTestManager::execRunIntegrationTests },
		{ "RunLargeWorldTests", &UAuracronWorldPartitionTestManager::execRunLargeWorldTests },
		{ "RunPerformanceTests", &UAuracronWorldPartitionTestManager::execRunPerformanceTests },
		{ "RunSingleTest", &UAuracronWorldPartitionTestManager::execRunSingleTest },
		{ "RunStreamingTests", &UAuracronWorldPartitionTestManager::execRunStreamingTests },
		{ "RunStressTests", &UAuracronWorldPartitionTestManager::execRunStressTests },
		{ "RunTestCategory", &UAuracronWorldPartitionTestManager::execRunTestCategory },
		{ "RunTestsAsync", &UAuracronWorldPartitionTestManager::execRunTestsAsync },
		{ "RunUnitTests", &UAuracronWorldPartitionTestManager::execRunUnitTests },
		{ "SetConfiguration", &UAuracronWorldPartitionTestManager::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionTestManager::execShutdown },
		{ "TestCellLoadingUnloading", &UAuracronWorldPartitionTestManager::execTestCellLoadingUnloading },
		{ "TestCellManagement", &UAuracronWorldPartitionTestManager::execTestCellManagement },
		{ "TestContinuousStreaming", &UAuracronWorldPartitionTestManager::execTestContinuousStreaming },
		{ "TestCPUPerformance", &UAuracronWorldPartitionTestManager::execTestCPUPerformance },
		{ "TestDebugSystem", &UAuracronWorldPartitionTestManager::execTestDebugSystem },
		{ "TestGridSystemCreation", &UAuracronWorldPartitionTestManager::execTestGridSystemCreation },
		{ "TestLargeWorldCreation", &UAuracronWorldPartitionTestManager::execTestLargeWorldCreation },
		{ "TestLumenIntegration", &UAuracronWorldPartitionTestManager::execTestLumenIntegration },
		{ "TestMassiveStreamingLoad", &UAuracronWorldPartitionTestManager::execTestMassiveStreamingLoad },
		{ "TestMemoryStress", &UAuracronWorldPartitionTestManager::execTestMemoryStress },
		{ "TestMemoryUsage", &UAuracronWorldPartitionTestManager::execTestMemoryUsage },
		{ "TestPCGIntegration", &UAuracronWorldPartitionTestManager::execTestPCGIntegration },
		{ "TestPerformanceMonitoring", &UAuracronWorldPartitionTestManager::execTestPerformanceMonitoring },
		{ "TestStreamingPerformance", &UAuracronWorldPartitionTestManager::execTestStreamingPerformance },
		{ "TestStreamingPriority", &UAuracronWorldPartitionTestManager::execTestStreamingPriority },
		{ "TestStreamingStability", &UAuracronWorldPartitionTestManager::execTestStreamingStability },
		{ "TestStreamingSystem", &UAuracronWorldPartitionTestManager::execTestStreamingSystem },
		{ "TestWorldPartitionIntegration", &UAuracronWorldPartitionTestManager::execTestWorldPartitionIntegration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager;
UClass* UAuracronWorldPartitionTestManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionTestManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionTestManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionTestManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager_NoRegister()
{
	return UAuracronWorldPartitionTestManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Test Manager\n * Central manager for World Partition testing suite\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionTests.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Test Manager\nCentral manager for World Partition testing suite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestSuiteCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionTests.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestSuiteCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestFailed;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_CancelRunningTests, "CancelRunningTests" }, // 2456921102
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_ClearTestHistory, "ClearTestHistory" }, // 2900939506
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestReport, "GenerateTestReport" }, // 693137720
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GenerateTestSummary, "GenerateTestSummary" }, // 832120100
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetConfiguration, "GetConfiguration" }, // 2037071887
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetInstance, "GetInstance" }, // 2078133011
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_GetTestHistory, "GetTestHistory" }, // 926581614
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Initialize, "Initialize" }, // 2079474905
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsInitialized, "IsInitialized" }, // 2151973775
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_IsTestRunning, "IsTestRunning" }, // 3523019142
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature, "OnTestCompleted__DelegateSignature" }, // 3829180559
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature, "OnTestFailed__DelegateSignature" }, // 516952631
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature, "OnTestSuiteCompleted__DelegateSignature" }, // 662397802
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunAllTests, "RunAllTests" }, // 2371583342
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunIntegrationTests, "RunIntegrationTests" }, // 1482596578
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunLargeWorldTests, "RunLargeWorldTests" }, // 891363069
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunPerformanceTests, "RunPerformanceTests" }, // 2518898906
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunSingleTest, "RunSingleTest" }, // 271374630
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStreamingTests, "RunStreamingTests" }, // 3925551753
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunStressTests, "RunStressTests" }, // 2105307050
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestCategory, "RunTestCategory" }, // 1567850811
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunTestsAsync, "RunTestsAsync" }, // 506195640
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_RunUnitTests, "RunUnitTests" }, // 1501130403
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_SetConfiguration, "SetConfiguration" }, // 344244703
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_Shutdown, "Shutdown" }, // 1269187100
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellLoadingUnloading, "TestCellLoadingUnloading" }, // 810089293
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCellManagement, "TestCellManagement" }, // 3516119506
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestContinuousStreaming, "TestContinuousStreaming" }, // 2682560219
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestCPUPerformance, "TestCPUPerformance" }, // 3511958978
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestDebugSystem, "TestDebugSystem" }, // 1978606882
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestGridSystemCreation, "TestGridSystemCreation" }, // 2630366186
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLargeWorldCreation, "TestLargeWorldCreation" }, // 3059260709
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestLumenIntegration, "TestLumenIntegration" }, // 2197199651
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMassiveStreamingLoad, "TestMassiveStreamingLoad" }, // 3731237128
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryStress, "TestMemoryStress" }, // 330498509
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestMemoryUsage, "TestMemoryUsage" }, // 1688054653
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPCGIntegration, "TestPCGIntegration" }, // 3948765819
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestPerformanceMonitoring, "TestPerformanceMonitoring" }, // 1968856414
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPerformance, "TestStreamingPerformance" }, // 289671517
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingPriority, "TestStreamingPriority" }, // 2585201830
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingStability, "TestStreamingStability" }, // 501576561
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestStreamingSystem, "TestStreamingSystem" }, // 2838836407
		{ &Z_Construct_UFunction_UAuracronWorldPartitionTestManager_TestWorldPartitionIntegration, "TestWorldPartitionIntegration" }, // 2702026834
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionTestManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestCompleted = { "OnTestCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionTestManager, OnTestCompleted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestCompleted_MetaData), NewProp_OnTestCompleted_MetaData) }; // 3829180559
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestSuiteCompleted = { "OnTestSuiteCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionTestManager, OnTestSuiteCompleted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestSuiteCompleted_MetaData), NewProp_OnTestSuiteCompleted_MetaData) }; // 662397802
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestFailed = { "OnTestFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionTestManager, OnTestFailed), Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestFailed_MetaData), NewProp_OnTestFailed_MetaData) }; // 516952631
void Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionTestManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionTestManager), &Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionTestManager, Configuration), Z_Construct_UScriptStruct_FAuracronTestConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3535147630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestSuiteCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_OnTestFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::ClassParams = {
	&UAuracronWorldPartitionTestManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionTestManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionTestManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager.OuterSingleton;
}
UAuracronWorldPartitionTestManager::UAuracronWorldPartitionTestManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionTestManager);
UAuracronWorldPartitionTestManager::~UAuracronWorldPartitionTestManager() {}
// ********** End Class UAuracronWorldPartitionTestManager *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronTestCategory_StaticEnum, TEXT("EAuracronTestCategory"), &Z_Registration_Info_UEnum_EAuracronTestCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2406710211U) },
		{ EAuracronTestSeverity_StaticEnum, TEXT("EAuracronTestSeverity"), &Z_Registration_Info_UEnum_EAuracronTestSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3298447594U) },
		{ EAuracronTestExecutionMode_StaticEnum, TEXT("EAuracronTestExecutionMode"), &Z_Registration_Info_UEnum_EAuracronTestExecutionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3399416300U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTestConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronTestConfiguration_Statics::NewStructOps, TEXT("AuracronTestConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronTestConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTestConfiguration), 3535147630U) },
		{ FAuracronTestResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronTestResult_Statics::NewStructOps, TEXT("AuracronTestResult"), &Z_Registration_Info_UScriptStruct_FAuracronTestResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTestResult), 737110479U) },
		{ FAuracronTestSuiteResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronTestSuiteResult_Statics::NewStructOps, TEXT("AuracronTestSuiteResult"), &Z_Registration_Info_UScriptStruct_FAuracronTestSuiteResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTestSuiteResult), 4016154621U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionTestManager, UAuracronWorldPartitionTestManager::StaticClass, TEXT("UAuracronWorldPartitionTestManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionTestManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionTestManager), 4247144671U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_3873935715(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionTests_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
