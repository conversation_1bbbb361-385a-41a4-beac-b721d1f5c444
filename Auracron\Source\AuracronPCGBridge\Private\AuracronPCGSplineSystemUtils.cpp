// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Utilities Implementation
// Bridge 2.8: PCG Framework - Spline System

#include "AuracronPCGSplineSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SplineActor.h"
#include "SplineMeshActor.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// SPLINE SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

USplineComponent* UAuracronPCGSplineSystemUtils::CreateSplineFromPoints(const TArray<FVector>& Points, bool bClosedSpline, EAuracronPCGSplineTangentMode TangentMode)
{
    if (Points.Num() < 2)
    {
        return nullptr;
    }

    USplineComponent* SplineComponent = NewObject<USplineComponent>();
    if (!SplineComponent)
    {
        return nullptr;
    }

    // Clear existing points
    SplineComponent->ClearSplinePoints();

    // Add points to spline
    for (int32 i = 0; i < Points.Num(); i++)
    {
        SplineComponent->AddSplinePoint(Points[i], ESplineCoordinateSpace::World);
    }

    // Set closed loop
    SplineComponent->SetClosedLoop(bClosedSpline);

    // Configure tangent modes
    ESplineTangentMode UnrealTangentMode = ConvertTangentMode(TangentMode);
    for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); i++)
    {
        SplineComponent->SetTangentAtSplinePoint(i, FVector::ZeroVector, ESplineCoordinateSpace::Local);
        SplineComponent->SetSplinePointType(i, UnrealTangentMode);
    }

    // Update spline
    SplineComponent->UpdateSpline();

    return SplineComponent;
}

USplineComponent* UAuracronPCGSplineSystemUtils::CreateSplineFromPointData(const UPCGPointData* PointData, bool bClosedSpline)
{
    if (!PointData)
    {
        return nullptr;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    TArray<FVector> Locations;
    Locations.Reserve(Points.Num());

    for (const FPCGPoint& Point : Points)
    {
        Locations.Add(Point.Transform.GetLocation());
    }

    return CreateSplineFromPoints(Locations, bClosedSpline);
}

bool UAuracronPCGSplineSystemUtils::ValidateSplinePoints(const TArray<FVector>& Points, float MinDistance)
{
    if (Points.Num() < 2)
    {
        return false;
    }

    // Check for minimum distance between consecutive points
    for (int32 i = 1; i < Points.Num(); i++)
    {
        float Distance = FVector::Dist(Points[i-1], Points[i]);
        if (Distance < MinDistance)
        {
            return false;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::DistributePointsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, EAuracronPCGSplineDistributionMode DistributionMode)
{
    TArray<FVector> DistributedPoints;
    
    if (!SplineComponent || PointCount <= 0)
    {
        return DistributedPoints;
    }

    float SplineLength = CalculateSplineLength(SplineComponent);
    DistributedPoints.Reserve(PointCount);

    switch (DistributionMode)
    {
        case EAuracronPCGSplineDistributionMode::Uniform:
        {
            for (int32 i = 0; i < PointCount; i++)
            {
                float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
                float Distance = T * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
        case EAuracronPCGSplineDistributionMode::Random:
        {
            FRandomStream RandomStream(12345);
            for (int32 i = 0; i < PointCount; i++)
            {
                float RandomT = RandomStream.FRand();
                float Distance = RandomT * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
        default:
        {
            // Default to uniform distribution
            for (int32 i = 0; i < PointCount; i++)
            {
                float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
                float Distance = T * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
    }

    return DistributedPoints;
}

TArray<FTransform> UAuracronPCGSplineSystemUtils::DistributeTransformsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, bool bAlignToSpline)
{
    TArray<FTransform> DistributedTransforms;
    
    if (!SplineComponent || PointCount <= 0)
    {
        return DistributedTransforms;
    }

    float SplineLength = CalculateSplineLength(SplineComponent);
    DistributedTransforms.Reserve(PointCount);

    for (int32 i = 0; i < PointCount; i++)
    {
        float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
        float Distance = T * SplineLength;
        
        FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FRotator Rotation = FRotator::ZeroRotator;
        
        if (bAlignToSpline)
        {
            FVector Direction = SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            Rotation = FRotationMatrix::MakeFromX(Direction).Rotator();
        }
        
        FVector Scale = SplineComponent->GetScaleAtDistanceAlongSpline(Distance);
        
        DistributedTransforms.Add(FTransform(Rotation, Location, Scale));
    }

    return DistributedTransforms;
}

float UAuracronPCGSplineSystemUtils::CalculateSplineLength(USplineComponent* SplineComponent)
{
    if (!SplineComponent)
    {
        return 0.0f;
    }

    return SplineComponent->GetSplineLength();
}

USplineMeshComponent* UAuracronPCGSplineSystemUtils::CreateSplineMeshComponent(USplineComponent* SplineComponent, UStaticMesh* StaticMesh)
{
    if (!SplineComponent || !StaticMesh)
    {
        return nullptr;
    }

    USplineMeshComponent* SplineMeshComponent = NewObject<USplineMeshComponent>();
    if (!SplineMeshComponent)
    {
        return nullptr;
    }

    // Configure spline mesh component
    SplineMeshComponent->SetStaticMesh(StaticMesh);
    SplineMeshComponent->SetSplineUpDir(FVector::UpVector);

    // Set spline mesh parameters based on spline component
    FVector StartPos = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::Local);
    FVector StartTangent = SplineComponent->GetTangentAtSplinePoint(0, ESplineCoordinateSpace::Local);
    FVector EndPos = SplineComponent->GetLocationAtSplinePoint(1, ESplineCoordinateSpace::Local);
    FVector EndTangent = SplineComponent->GetTangentAtSplinePoint(1, ESplineCoordinateSpace::Local);

    SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);

    return SplineMeshComponent;
}

bool UAuracronPCGSplineSystemUtils::GenerateSplineMeshes(USplineComponent* SplineComponent, const TArray<FAuracronPCGSplineMeshDescriptor>& MeshDescriptors)
{
    if (!SplineComponent || MeshDescriptors.Num() == 0)
    {
        return false;
    }

    // Generate meshes for each descriptor
    for (const FAuracronPCGSplineMeshDescriptor& MeshDescriptor : MeshDescriptors)
    {
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (!StaticMesh)
        {
            continue;
        }

        switch (MeshDescriptor.MeshMode)
        {
            case EAuracronPCGSplineMeshMode::SplineMeshComponent:
                GenerateSplineMeshComponents(SplineComponent, MeshDescriptor);
                break;
            case EAuracronPCGSplineMeshMode::InstancedMesh:
                GenerateInstancedMeshes(SplineComponent, MeshDescriptor);
                break;
            default:
                GenerateSplineMeshComponents(SplineComponent, MeshDescriptor);
                break;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::FindPath(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
{
    TArray<FVector> Path;

    switch (PathFindingDescriptor.PathFindingMode)
    {
        case EAuracronPCGPathFindingMode::AStar:
            AuracronPCGSplineSystemUtils::PerformAStarPathFinding(Start, End, PathFindingDescriptor, Path);
            break;
        case EAuracronPCGPathFindingMode::Dijkstra:
            AuracronPCGSplineSystemUtils::PerformDijkstraPathFinding(Start, End, PathFindingDescriptor, Path);
            break;
        default:
            // Simple direct path
            Path.Add(Start);
            Path.Add(End);
            break;
    }

    // Smooth path if requested
    if (PathFindingDescriptor.bSmoothPath && Path.Num() > 2)
    {
        Path = SmoothPath(Path, PathFindingDescriptor.SmoothingStrength, PathFindingDescriptor.SmoothingIterations);
    }

    return Path;
}

bool UAuracronPCGSplineSystemUtils::IsPathValid(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
{
    if (Path.Num() < 2)
    {
        return false;
    }

    // Check path constraints
    for (int32 i = 1; i < Path.Num(); i++)
    {
        FVector Segment = Path[i] - Path[i-1];
        float SegmentLength = Segment.Size();
        
        if (SegmentLength < PathFindingDescriptor.MinWidth)
        {
            return false;
        }

        // Check slope constraint
        float Slope = FMath::Abs(FMath::Atan2(Segment.Z, FVector2D(Segment.X, Segment.Y).Size()));
        float SlopeDegrees = FMath::RadiansToDegrees(Slope);
        
        if (SlopeDegrees > PathFindingDescriptor.MaxSlope)
        {
            return false;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::SmoothPath(const TArray<FVector>& Path, float SmoothingStrength, int32 Iterations)
{
    if (Path.Num() < 3)
    {
        return Path;
    }

    TArray<FVector> SmoothedPath = Path;

    for (int32 Iter = 0; Iter < Iterations; Iter++)
    {
        TArray<FVector> NewPath = SmoothedPath;

        // Apply smoothing to interior points
        for (int32 i = 1; i < SmoothedPath.Num() - 1; i++)
        {
            FVector PrevPoint = SmoothedPath[i-1];
            FVector CurrentPoint = SmoothedPath[i];
            FVector NextPoint = SmoothedPath[i+1];

            FVector SmoothedPoint = (PrevPoint + CurrentPoint + NextPoint) / 3.0f;
            NewPath[i] = FMath::Lerp(CurrentPoint, SmoothedPoint, SmoothingStrength);
        }

        SmoothedPath = NewPath;
    }

    return SmoothedPath;
}

float UAuracronPCGSplineSystemUtils::CalculateSplineCurvature(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return 0.0f;
    }

    // Sample points around the distance to calculate curvature
    float SampleDistance = 10.0f;
    FVector P1 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance - SampleDistance, ESplineCoordinateSpace::World);
    FVector P2 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
    FVector P3 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance + SampleDistance, ESplineCoordinateSpace::World);

    // Calculate curvature using three points
    FVector V1 = (P2 - P1).GetSafeNormal();
    FVector V2 = (P3 - P2).GetSafeNormal();
    
    float DotProduct = FVector::DotProduct(V1, V2);
    float Angle = FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
    
    return Angle / SampleDistance;
}

FVector UAuracronPCGSplineSystemUtils::GetSplineDirectionAtDistance(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return FVector::ForwardVector;
    }

    return SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
}

float UAuracronPCGSplineSystemUtils::GetSplineWidthAtDistance(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return 100.0f;
    }

    // Get scale at distance and use X component as width
    FVector Scale = SplineComponent->GetScaleAtDistanceAlongSpline(Distance);
    return Scale.X * 100.0f; // Convert to reasonable width units
}

bool UAuracronPCGSplineSystemUtils::OptimizeSpline(USplineComponent* SplineComponent, float Tolerance)
{
    if (!SplineComponent)
    {
        return false;
    }

    // Optimize spline tangents
    AuracronPCGSplineSystemUtils::OptimizeSplineTangents(SplineComponent, 0.5f);

    return true;
}

bool UAuracronPCGSplineSystemUtils::SimplifySpline(USplineComponent* SplineComponent, float Tolerance)
{
    if (!SplineComponent)
    {
        return false;
    }

    // Get current spline points
    TArray<FVector> Points;
    int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
    
    for (int32 i = 0; i < NumPoints; i++)
    {
        Points.Add(SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World));
    }

    // Optimize using Douglas-Peucker algorithm
    TArray<FVector> OptimizedPoints;
    SimplifyPointArray(Points, OptimizedPoints, Tolerance);

    // Rebuild spline with optimized points
    if (OptimizedPoints.Num() >= 2 && OptimizedPoints.Num() < Points.Num())
    {
        SplineComponent->ClearSplinePoints();
        for (const FVector& Point : OptimizedPoints)
        {
            SplineComponent->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        SplineComponent->UpdateSpline();
        return true;
    }

    return false;
}

int32 UAuracronPCGSplineSystemUtils::GetOptimalPointCount(float SplineLength, float TargetSpacing)
{
    if (TargetSpacing <= 0.0f)
    {
        return 2;
    }

    return FMath::Max(2, FMath::CeilToInt(SplineLength / TargetSpacing));
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

ESplineTangentMode UAuracronPCGSplineSystemUtils::ConvertTangentMode(EAuracronPCGSplineTangentMode TangentMode)
{
    switch (TangentMode)
    {
        case EAuracronPCGSplineTangentMode::Auto:
            return ESplineTangentMode::Auto;
        case EAuracronPCGSplineTangentMode::User:
            return ESplineTangentMode::User;
        case EAuracronPCGSplineTangentMode::Break:
            return ESplineTangentMode::Break;
        case EAuracronPCGSplineTangentMode::Linear:
            return ESplineTangentMode::Linear;
        case EAuracronPCGSplineTangentMode::Constant:
            return ESplineTangentMode::Constant;
        default:
            return ESplineTangentMode::Auto;
    }
}

void UAuracronPCGSplineSystemUtils::GenerateSplineMeshComponents(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
{
    // Generate spline mesh components for each segment
    int32 NumSegments = SplineComponent->GetNumberOfSplineSegments();
    
    for (int32 i = 0; i < NumSegments; i++)
    {
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (StaticMesh)
        {
            USplineMeshComponent* SplineMeshComponent = CreateSplineMeshComponent(SplineComponent, StaticMesh);
            if (SplineMeshComponent)
            {
                // Configure segment-specific parameters
                FVector StartPos = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);
                FVector StartTangent = SplineComponent->GetTangentAtSplinePoint(i, ESplineCoordinateSpace::Local);
                FVector EndPos = SplineComponent->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);
                FVector EndTangent = SplineComponent->GetTangentAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);

                SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);
            }
        }
    }
}

void UAuracronPCGSplineSystemUtils::GenerateInstancedMeshes(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGSplineSystemUtils::GenerateInstancedMeshes);

    if (!SplineComponent || !MeshDescriptor.StaticMesh.IsValid())
    {
        return;
    }

    // Real instanced mesh generation along spline using UE5.6 APIs
    UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
    if (!StaticMesh)
    {
        return;
    }

    // Create hierarchical instanced static mesh component
    AActor* Owner = SplineComponent->GetOwner();
    if (!Owner)
    {
        return;
    }

    UHierarchicalInstancedStaticMeshComponent* HISMComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(Owner);
    HISMComponent->SetStaticMesh(StaticMesh);
    HISMComponent->AttachToComponent(SplineComponent, FAttachmentTransformRules::KeepWorldTransform);

    // Configure HISM component settings
    HISMComponent->SetCullDistances(0, 5000); // Cull at 5000 units
    HISMComponent->bUseAsOccluder = true;
    HISMComponent->bCastShadow = true;
    HISMComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

    // Apply material overrides if specified
    if (MeshDescriptor.MaterialOverrides.Num() > 0)
    {
        for (int32 i = 0; i < MeshDescriptor.MaterialOverrides.Num(); i++)
        {
            if (MeshDescriptor.MaterialOverrides[i].IsValid())
            {
                UMaterialInterface* Material = MeshDescriptor.MaterialOverrides[i].LoadSynchronous();
                if (Material)
                {
                    HISMComponent->SetMaterial(i, Material);
                }
            }
        }
    }

    // Calculate instance transforms along spline
    float SplineLength = SplineComponent->GetSplineLength();
    float InstanceSpacing = MeshDescriptor.bTileMesh ? MeshDescriptor.TileLength : (SplineLength / 10.0f);
    int32 NumInstances = FMath::Max(1, FMath::FloorToInt(SplineLength / InstanceSpacing));

    // Generate instances along spline
    for (int32 i = 0; i < NumInstances; i++)
    {
        float Distance = i * InstanceSpacing;
        if (Distance > SplineLength)
        {
            break;
        }

        // Get transform at distance
        FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FVector Direction = SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FVector UpVector = SplineComponent->GetUpVectorAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);

        // Calculate rotation from direction and up vectors
        FRotator Rotation = FRotationMatrix::MakeFromXZ(Direction, UpVector).Rotator();

        // Apply custom scaling if specified
        FVector Scale = FVector::OneVector;
        if (MeshDescriptor.bUseCustomScale && MeshDescriptor.ScaleCurve.IsValid())
        {
            UCurveVector* ScaleCurve = MeshDescriptor.ScaleCurve.LoadSynchronous();
            if (ScaleCurve)
            {
                float Alpha = Distance / SplineLength;
                Scale = ScaleCurve->GetVectorValue(Alpha);
            }
        }

        // Create instance transform
        FTransform InstanceTransform(Rotation, Location, Scale);

        // Add instance to HISM component
        HISMComponent->AddInstance(InstanceTransform);
    }

    // Register component with owner
    Owner->AddInstanceComponent(HISMComponent);
    HISMComponent->RegisterComponent();

    UE_LOG(LogAuracronPCGSpline, Log, TEXT("Generated %d instanced meshes along spline: %s"),
           NumInstances, *SplineComponent->GetName());
}

void UAuracronPCGSplineSystemUtils::SimplifyPointArray(const TArray<FVector>& InputPoints, TArray<FVector>& OutputPoints, float Tolerance)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGSplineSystemUtils::SimplifyPointArray);

    OutputPoints.Empty();

    if (InputPoints.Num() < 3)
    {
        OutputPoints = InputPoints;
        return;
    }

    // Real Douglas-Peucker algorithm implementation
    TArray<bool> KeepPoints;
    KeepPoints.SetNumZeroed(InputPoints.Num());

    // Always keep first and last points
    KeepPoints[0] = true;
    KeepPoints[InputPoints.Num() - 1] = true;

    // Recursively simplify the line
    DouglasPeuckerRecursive(InputPoints, KeepPoints, 0, InputPoints.Num() - 1, Tolerance);

    // Build output array with kept points
    for (int32 i = 0; i < InputPoints.Num(); i++)
    {
        if (KeepPoints[i])
        {
            OutputPoints.Add(InputPoints[i]);
        }
    }

    UE_LOG(LogAuracronPCGSpline, Log, TEXT("Douglas-Peucker optimized %d points to %d points (%.1f%% reduction)"),
           InputPoints.Num(), OutputPoints.Num(),
           (1.0f - static_cast<float>(OutputPoints.Num()) / InputPoints.Num()) * 100.0f);
}

void UAuracronPCGSplineSystemUtils::DouglasPeuckerRecursive(const TArray<FVector>& Points, TArray<bool>& KeepPoints, int32 StartIndex, int32 EndIndex, float Tolerance)
{
    if (EndIndex <= StartIndex + 1)
    {
        return;
    }

    // Find the point with maximum distance from the line segment
    float MaxDistance = 0.0f;
    int32 MaxIndex = -1;

    FVector LineStart = Points[StartIndex];
    FVector LineEnd = Points[EndIndex];

    for (int32 i = StartIndex + 1; i < EndIndex; i++)
    {
        float Distance = CalculatePointToLineDistance(Points[i], LineStart, LineEnd);

        if (Distance > MaxDistance)
        {
            MaxDistance = Distance;
            MaxIndex = i;
        }
    }

    // If the maximum distance is greater than tolerance, keep the point and recurse
    if (MaxDistance > Tolerance && MaxIndex != -1)
    {
        KeepPoints[MaxIndex] = true;

        // Recursively simplify the two segments
        DouglasPeuckerRecursive(Points, KeepPoints, StartIndex, MaxIndex, Tolerance);
        DouglasPeuckerRecursive(Points, KeepPoints, MaxIndex, EndIndex, Tolerance);
    }
}

float UAuracronPCGSplineSystemUtils::CalculatePointToLineDistance(const FVector& Point, const FVector& LineStart, const FVector& LineEnd)
{
    // Calculate perpendicular distance from point to line segment
    FVector LineVector = LineEnd - LineStart;
    FVector PointVector = Point - LineStart;

    float LineLength = LineVector.Size();

    if (LineLength < SMALL_NUMBER)
    {
        // Line segment is essentially a point
        return FVector::Dist(Point, LineStart);
    }

    // Project point onto line
    float ProjectionLength = FVector::DotProduct(PointVector, LineVector) / LineLength;

    // Clamp projection to line segment
    ProjectionLength = FMath::Clamp(ProjectionLength, 0.0f, LineLength);

    // Find closest point on line segment
    FVector ClosestPoint = LineStart + (LineVector.GetSafeNormal() * ProjectionLength);

    // Return distance from point to closest point on line
    return FVector::Dist(Point, ClosestPoint);
}
