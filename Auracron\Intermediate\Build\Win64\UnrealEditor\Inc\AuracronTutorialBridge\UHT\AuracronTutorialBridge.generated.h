// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronTutorialBridge.h"

#ifdef AURACRONTUTORIALBRIDGE_AuracronTutorialBridge_generated_h
#error "AuracronTutorialBridge.generated.h already included, missing '#pragma once' in AuracronTutorialBridge.h"
#endif
#define AURACRONTUTORIALBRIDGE_AuracronTutorialBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
struct FAuracronTutorialConfiguration;
struct FAuracronTutorialStep;

// ********** Begin ScriptStruct FAuracronTutorialStep *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_82_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTutorialStep_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTutorialStep;
// ********** End ScriptStruct FAuracronTutorialStep ***********************************************

// ********** Begin ScriptStruct FAuracronTutorialConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTutorialConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTutorialConfiguration;
// ********** End ScriptStruct FAuracronTutorialConfiguration **************************************

// ********** Begin Delegate FOnTutorialStarted ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_517_DELEGATE \
static void FOnTutorialStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialStarted, FAuracronTutorialConfiguration Tutorial);


// ********** End Delegate FOnTutorialStarted ******************************************************

// ********** Begin Delegate FOnTutorialStepCompleted **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_522_DELEGATE \
static void FOnTutorialStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialStepCompleted, int32 StepIndex, FAuracronTutorialStep Step);


// ********** End Delegate FOnTutorialStepCompleted ************************************************

// ********** Begin Delegate FOnTutorialCompleted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_527_DELEGATE \
static void FOnTutorialCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTutorialCompleted, const FString& TutorialID);


// ********** End Delegate FOnTutorialCompleted ****************************************************

// ********** Begin Class UAuracronTutorialBridge **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_CurrentStep); \
	DECLARE_FUNCTION(execOnRep_TutorialState); \
	DECLARE_FUNCTION(execPersonalizeTutorialContent); \
	DECLARE_FUNCTION(execAdjustTutorialDifficulty); \
	DECLARE_FUNCTION(execAdaptTutorialToPlayer); \
	DECLARE_FUNCTION(execIsTutorialCompleted); \
	DECLARE_FUNCTION(execGetTutorialProgress); \
	DECLARE_FUNCTION(execLoadTutorialProgress); \
	DECLARE_FUNCTION(execSaveTutorialProgress); \
	DECLARE_FUNCTION(execAIMentorDemonstrate); \
	DECLARE_FUNCTION(execAIMentorSpeak); \
	DECLARE_FUNCTION(execDeactivateAIMentor); \
	DECLARE_FUNCTION(execActivateAIMentor); \
	DECLARE_FUNCTION(execSkipCurrentStep); \
	DECLARE_FUNCTION(execCompleteCurrentStep); \
	DECLARE_FUNCTION(execGoToTutorialStep); \
	DECLARE_FUNCTION(execPreviousTutorialStep); \
	DECLARE_FUNCTION(execNextTutorialStep); \
	DECLARE_FUNCTION(execRestartTutorial); \
	DECLARE_FUNCTION(execSkipTutorial); \
	DECLARE_FUNCTION(execStopTutorial); \
	DECLARE_FUNCTION(execResumeTutorial); \
	DECLARE_FUNCTION(execPauseTutorial); \
	DECLARE_FUNCTION(execStartTutorial);


AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronTutorialBridge(); \
	friend struct Z_Construct_UClass_UAuracronTutorialBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronTutorialBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronTutorialBridge"), Z_Construct_UClass_UAuracronTutorialBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronTutorialBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentTutorial=NETFIELD_REP_START, \
		CurrentTutorialState, \
		CurrentStepIndex, \
		TutorialProgress, \
		bAIMentorActive, \
		NETFIELD_REP_END=bAIMentorActive	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronTutorialBridge(UAuracronTutorialBridge&&) = delete; \
	UAuracronTutorialBridge(const UAuracronTutorialBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronTutorialBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronTutorialBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronTutorialBridge) \
	NO_API virtual ~UAuracronTutorialBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_270_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h_273_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronTutorialBridge;

// ********** End Class UAuracronTutorialBridge ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialBridge_h

// ********** Begin Enum EAuracronTutorialType *****************************************************
#define FOREACH_ENUM_EAURACRONTUTORIALTYPE(op) \
	op(EAuracronTutorialType::None) \
	op(EAuracronTutorialType::Onboarding) \
	op(EAuracronTutorialType::BasicMovement) \
	op(EAuracronTutorialType::CombatBasics) \
	op(EAuracronTutorialType::AbilityUsage) \
	op(EAuracronTutorialType::SigiloSystem) \
	op(EAuracronTutorialType::RealmNavigation) \
	op(EAuracronTutorialType::TeamPlay) \
	op(EAuracronTutorialType::Objectives) \
	op(EAuracronTutorialType::Advanced) \
	op(EAuracronTutorialType::Reboarding) 

enum class EAuracronTutorialType : uint8;
template<> struct TIsUEnumClass<EAuracronTutorialType> { enum { Value = true }; };
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialType>();
// ********** End Enum EAuracronTutorialType *******************************************************

// ********** Begin Enum EAuracronTutorialState ****************************************************
#define FOREACH_ENUM_EAURACRONTUTORIALSTATE(op) \
	op(EAuracronTutorialState::NotStarted) \
	op(EAuracronTutorialState::InProgress) \
	op(EAuracronTutorialState::Completed) \
	op(EAuracronTutorialState::Skipped) \
	op(EAuracronTutorialState::Failed) \
	op(EAuracronTutorialState::Paused) 

enum class EAuracronTutorialState : uint8;
template<> struct TIsUEnumClass<EAuracronTutorialState> { enum { Value = true }; };
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialState>();
// ********** End Enum EAuracronTutorialState ******************************************************

// ********** Begin Enum EAuracronTutorialStepType *************************************************
#define FOREACH_ENUM_EAURACRONTUTORIALSTEPTYPE(op) \
	op(EAuracronTutorialStepType::Information) \
	op(EAuracronTutorialStepType::Action) \
	op(EAuracronTutorialStepType::Demonstration) \
	op(EAuracronTutorialStepType::Practice) \
	op(EAuracronTutorialStepType::Quiz) \
	op(EAuracronTutorialStepType::Checkpoint) \
	op(EAuracronTutorialStepType::Reward) \
	op(EAuracronTutorialStepType::Transition) 

enum class EAuracronTutorialStepType : uint8;
template<> struct TIsUEnumClass<EAuracronTutorialStepType> { enum { Value = true }; };
template<> AURACRONTUTORIALBRIDGE_API UEnum* StaticEnum<EAuracronTutorialStepType>();
// ********** End Enum EAuracronTutorialStepType ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
