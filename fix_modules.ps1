# Script para corrigir módulos inexistentes nos bridges do Auracron
# Para UE 5.6

$bridgeFiles = Get-ChildItem "Auracron\Source\*Bridge\*.Build.cs", "Auracron\Source\*Framework\*.Build.cs"

foreach ($file in $bridgeFiles) {
    Write-Host "Corrigindo $($file.Name)..."
    
    $content = Get-Content $file.FullName -Raw
    
    # Substituições de módulos
    $content = $content -replace '"KismetDeveloper"', '"Kismet"'
    $content = $content -replace '"Telemetry"', '"StudioTelemetry"'
    $content = $content -replace '"UnrealInsights"', '"TraceInsights"'
    $content = $content -replace '"MemoryProfiler2"', '"TraceInsights"'
    $content = $content -replace '"NotificationManager"', '"ToolMenus"'
    $content = $content -replace '"EditorNotifications"', '"EditorWidgets"'
    $content = $content -replace '"Framework"', '"ApplicationCore"'
    $content = $content -replace '"GroomEditor"', '"HairStrandsEditor"'
    $content = $content -replace '"AudioMixerEditor"', '"AudioEditor"'
    $content = $content -replace '"Nanite"', '"Renderer"'
    $content = $content -replace '"Lumen"', '"Renderer"'
    $content = $content -replace '"LumenRuntime"', '"Renderer"'
    $content = $content -replace '"GlobalIllumination"', '"Renderer"'
    $content = $content -replace '"RayTracing"', '"Renderer"'
    $content = $content -replace '"PathTracing"', '"Renderer"'
    $content = $content -replace '"VirtualShadowMaps"', '"Renderer"'
    $content = $content -replace '"TemporalUpsampling"', '"Renderer"'
    $content = $content -replace '"ScreenSpaceReflections"', '"Renderer"'
    $content = $content -replace '"MeshPaintMode"', '"MeshPaint"'
    $content = $content -replace '"WidgetBlueprintEditor"', '"UMGEditor"'
    $content = $content -replace '"ReplaySubsystem"', '"NetworkReplayStreaming"'
    $content = $content -replace '"WorldPartition"', '"Engine"'
    $content = $content -replace '"ProceduralFoliage"', '"Foliage"'
    $content = $content -replace '"JsonObjectConverter"', '"Json"'
    $content = $content -replace '"TextureUtilities"', '"TextureUtilitiesCommon"'
    $content = $content -replace '"WorldBrowserEditor"', '"WorldBrowser"'
    $content = $content -replace '"MassGameplay"', '"MassEntity"'

    # Remover módulos que não existem no UE 5.6
    $content = $content -replace '[\s]*"Firebase[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"CascadeParticles[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"CascadeParticlesEditor",?\s*', ''
    $content = $content -replace '[\s]*"ChaosEditor",?\s*', ''
    $content = $content -replace '[\s]*"FieldSystemEditor",?\s*', ''
    $content = $content -replace '[\s]*"GeometryCollectionEditor",?\s*', ''
    $content = $content -replace '[\s]*"FractureEditor",?\s*', ''
    $content = $content -replace '[\s]*"APEX",?\s*', ''
    $content = $content -replace '[\s]*"ApexDestruction",?\s*', ''
    $content = $content -replace '[\s]*"PhysX[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"NvCloth",?\s*', ''
    $content = $content -replace '[\s]*"PxShared",?\s*', ''
    $content = $content -replace '[\s]*"SimplygonMeshReduction",?\s*', ''
    $content = $content -replace '[\s]*"QuadricMeshReduction",?\s*', ''
    $content = $content -replace '[\s]*"VoiceChatEditor",?\s*', ''
    $content = $content -replace '[\s]*"LibOpus",?\s*', ''
    $content = $content -replace '[\s]*"Ogg",?\s*', ''
    $content = $content -replace '[\s]*"Vorbis[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"UEOgg",?\s*', ''
    $content = $content -replace '[\s]*"WebRTC",?\s*', ''
    $content = $content -replace '[\s]*"AudioMixer[A-Z][^"]*",?\s*', ''
    $content = $content -replace '[\s]*"AudioCapture[^"]*",?\s*', ''
    
    # Remover módulos incluídos no Core
    $content = $content -replace '[\s]*"Internationalization",?\s*', ''
    $content = $content -replace '[\s]*"HAL",?\s*', ''
    $content = $content -replace '[\s]*"Async",?\s*', ''
    $content = $content -replace '[\s]*"TaskGraph",?\s*', ''
    $content = $content -replace '[\s]*"ProfilingDebugging",?\s*', ''
    $content = $content -replace '[\s]*"Stats",?\s*', ''
    $content = $content -replace '[\s]*"CommonGame",?\s*', ''
    $content = $content -replace '[\s]*"BlackboardEditor",?\s*', ''
    $content = $content -replace '[\s]*"BehaviorTreeEditor",?\s*', ''
    $content = $content -replace '[\s]*"TextAsset",?\s*', ''
    $content = $content -replace '[\s]*"LocalizationService",?\s*', ''
    $content = $content -replace '[\s]*"TranslationEditor",?\s*', ''
    $content = $content -replace '[\s]*"LocalizationDashboard",?\s*', ''
    $content = $content -replace '[\s]*"PurchaseFlow",?\s*', ''
    $content = $content -replace '[\s]*"StoreInterface",?\s*', ''
    $content = $content -replace '[\s]*"GooglePlay[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"IOSCloudKit",?\s*', ''
    $content = $content -replace '[\s]*"AppleStoreKit",?\s*', ''
    $content = $content -replace '[\s]*"SteamShared",?\s*', ''
    $content = $content -replace '[\s]*"SteamSockets",?\s*', ''
    $content = $content -replace '[\s]*"Steamworks",?\s*', ''
    $content = $content -replace '[\s]*"NetDriverEOS",?\s*', ''
    $content = $content -replace '[\s]*"SocketSubsystemEOS",?\s*', ''
    $content = $content -replace '[\s]*"OnlineSubsystemEOSPlus",?\s*', ''
    $content = $content -replace '[\s]*"MetaHumanSDK",?\s*', ''
    $content = $content -replace '[\s]*"ProceduralContentGeneration",?\s*', ''
    $content = $content -replace '[\s]*"PCG[^"]*",?\s*', ''
    $content = $content -replace '[\s]*"InstancedFoliage",?\s*', ''
    $content = $content -replace '[\s]*"FoliageEdit",?\s*', ''
    $content = $content -replace '[\s]*"AudioEngine",?\s*', ''
    $content = $content -replace '[\s]*"RuntimeMeshComponent",?\s*', ''
    $content = $content -replace '[\s]*"ProceduralMeshComponent",?\s*', ''
    $content = $content -replace '[\s]*"LightingBuildQualitySettings",?\s*', ''
    $content = $content -replace '[\s]*"LightmassCore",?\s*', ''
    $content = $content -replace '[\s]*"Lightmass",?\s*', ''
    $content = $content -replace '[\s]*"VirtualTexturing",?\s*', ''
    $content = $content -replace '[\s]*"ShaderCore",?\s*', ''
    $content = $content -replace '[\s]*"ShaderCompiler",?\s*', ''
    $content = $content -replace '[\s]*"WorldPartitionHLODs",?\s*', ''
    $content = $content -replace '[\s]*"WorldPartitionEditor",?\s*', ''
    $content = $content -replace '[\s]*"LevelInstance",?\s*', ''
    $content = $content -replace '[\s]*"Lighting",?\s*', ''
    $content = $content -replace '[\s]*"LightingBuildQuality",?\s*', ''
    $content = $content -replace '[\s]*"SubsystemBrowserPlugin",?\s*', ''
    $content = $content -replace '[\s]*"DesktopTargetPlatform",?\s*', ''
    $content = $content -replace '[\s]*"TargetPlatform",?\s*', ''
    $content = $content -replace '[\s]*"Perception",?\s*', ''
    $content = $content -replace '[\s]*"BehaviorTreeModule",?\s*', ''
    $content = $content -replace '[\s]*"BlackboardModule",?\s*', ''
    $content = $content -replace '[\s]*"MetaHumanMeshTracker",?\s*', ''
    $content = $content -replace '[\s]*"MetaHumanIdentity",?\s*', ''
    $content = $content -replace '[\s]*"MetaHumanCore",?\s*', ''
    $content = $content -replace '[\s]*"DNAReader",?\s*', ''
    $content = $content -replace '[\s]*"DNACalibration",?\s*', ''
    $content = $content -replace '[\s]*"RigLogic",?\s*', ''
    $content = $content -replace '[\s]*"DNACommon",?\s*', ''
    $content = $content -replace '[\s]*"DNAUtils",?\s*', ''
    $content = $content -replace '[\s]*"VCamInput",?\s*', ''
    $content = $content -replace '[\s]*"VCamCore",?\s*', ''

    # Remover módulos carregados dinamicamente
    $content = $content -replace '[\s]*"Analytics",?\s*', ''
    $content = $content -replace '[\s]*"AnalyticsET",?\s*', ''
    $content = $content -replace '[\s]*"OnlineSubsystemEOS",?\s*', ''
    $content = $content -replace '[\s]*"OnlineSubsystem",?\s*', ''
    $content = $content -replace '[\s]*"CryptoKeys",?\s*', ''
    $content = $content -replace '[\s]*"SSL",?\s*', ''
    $content = $content -replace '[\s]*"Voice",?\s*', ''
    $content = $content -replace '[\s]*"VoiceChat",?\s*', ''
    $content = $content -replace '[\s]*"Media",?\s*', ''
    $content = $content -replace '[\s]*"MetasoundEngine",?\s*', ''
    $content = $content -replace '[\s]*"AnimationModifiers",?\s*', ''
    $content = $content -replace '[\s]*"GameplayAbilitiesEditor",?\s*', ''
    $content = $content -replace '[\s]*"StudioTelemetry",?\s*', ''
    $content = $content -replace '[\s]*"HTTP",?\s*', ''
    $content = $content -replace '[\s]*"JsonUtilities",?\s*', ''
    $content = $content -replace '[\s]*"EOSSDK",?\s*', ''
    $content = $content -replace '[\s]*"CommonUI",?\s*', ''
    $content = $content -replace '[\s]*"LevelSequence",?\s*', ''
    $content = $content -replace '[\s]*"NaniteBuilder",?\s*', ''
    $content = $content -replace '[\s]*"Chaos",?\s*', ''
    $content = $content -replace '[\s]*"FieldSystemEngine",?\s*', ''
    $content = $content -replace '[\s]*"GeometryCollectionEngine",?\s*', ''
    $content = $content -replace '[\s]*"WebBrowser",?\s*', ''
    $content = $content -replace '[\s]*"Niagara",?\s*', ''
    $content = $content -replace '[\s]*"NiagaraEditor",?\s*', ''
    $content = $content -replace '[\s]*"AIModule",?\s*', ''
    $content = $content -replace '[\s]*"Renderer",?\s*', ''
    
    # Limpar linhas vazias duplas
    $content = $content -replace '\r?\n\s*\r?\n', "`r`n"
    
    # Salvar o arquivo corrigido
    Set-Content $file.FullName -Value $content -NoNewline
    
    Write-Host "Corrigido: $($file.Name)"
}

Write-Host "Correção concluída para todos os bridges!"
