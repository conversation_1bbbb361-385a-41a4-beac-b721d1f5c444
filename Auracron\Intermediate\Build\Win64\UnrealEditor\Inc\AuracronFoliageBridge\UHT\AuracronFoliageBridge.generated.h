// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageBridge.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageBridge_generated_h
#error "AuracronFoliageBridge.generated.h already included, missing '#pragma once' in AuracronFoliageBridge.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AInteractiveFoliageActor;
class ALandscape;
class AProceduralFoliageVolume;
class UFoliageType;
class UFoliageType_Actor;
class UFoliageType_InstancedStaticMesh;
class ULandscapeGrassType;
class UMaterialInterface;
class UObject;
class UProceduralFoliageSpawner;
class USplineComponent;
class UStaticMesh;
class UWorld;
enum class EAuracronFoliageBiomeType : uint8;
enum class EAuracronFoliageCollisionMode : uint8;
enum class EAuracronFoliageSeasonMode : uint8;
enum class EAuracronFoliageState : uint8;
enum class EAuracronFoliageWindMode : uint8;
struct FAuracronFoliageInstanceInfo;
struct FAuracronFoliagePerformanceInfo;
struct FAuracronFoliagePlacementInfo;
struct FAuracronFoliageTypeInfo;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronFoliageInstanceInfo **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_324_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageInstanceInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageInstanceInfo;
// ********** End ScriptStruct FAuracronFoliageInstanceInfo ****************************************

// ********** Begin ScriptStruct FAuracronFoliageTypeInfo ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_373_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageTypeInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageTypeInfo;
// ********** End ScriptStruct FAuracronFoliageTypeInfo ********************************************

// ********** Begin ScriptStruct FAuracronFoliagePlacementInfo *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_443_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliagePlacementInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliagePlacementInfo;
// ********** End ScriptStruct FAuracronFoliagePlacementInfo ***************************************

// ********** Begin ScriptStruct FAuracronFoliagePerformanceInfo ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_503_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliagePerformanceInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliagePerformanceInfo;
// ********** End ScriptStruct FAuracronFoliagePerformanceInfo *************************************

// ********** Begin Delegate FAuracronFoliageStateChanged ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_554_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageStateChanged_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageStateChanged, EAuracronFoliageState NewState);


// ********** End Delegate FAuracronFoliageStateChanged ********************************************

// ********** Begin Delegate FAuracronFoliageInstanceAdded *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_555_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageInstanceAdded_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageInstanceAdded, FAuracronFoliageInstanceInfo const& InstanceInfo, const FString& FoliageTypeName);


// ********** End Delegate FAuracronFoliageInstanceAdded *******************************************

// ********** Begin Delegate FAuracronFoliageInstanceRemoved ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_556_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageInstanceRemoved, int32 InstanceIndex, const FString& FoliageTypeName);


// ********** End Delegate FAuracronFoliageInstanceRemoved *****************************************

// ********** Begin Delegate FAuracronFoliageInstanceModified **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_557_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageInstanceModified_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageInstanceModified, int32 InstanceIndex, FAuracronFoliageInstanceInfo const& NewInstanceInfo, const FString& FoliageTypeName);


// ********** End Delegate FAuracronFoliageInstanceModified ****************************************

// ********** Begin Delegate FAuracronFoliageTypeRegistered ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_558_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageTypeRegistered_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageTypeRegistered, FAuracronFoliageTypeInfo const& TypeInfo);


// ********** End Delegate FAuracronFoliageTypeRegistered ******************************************

// ********** Begin Delegate FAuracronFoliageTypeUnregistered **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_559_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageTypeUnregistered_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageTypeUnregistered, const FString& TypeName);


// ********** End Delegate FAuracronFoliageTypeUnregistered ****************************************

// ********** Begin Delegate FAuracronFoliagePerformanceUpdated ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_560_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliagePerformanceUpdated_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliagePerformanceUpdated, FAuracronFoliagePerformanceInfo const& PerformanceInfo);


// ********** End Delegate FAuracronFoliagePerformanceUpdated **************************************

// ********** Begin Delegate FAuracronFoliageOperationCompleted ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_561_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageOperationCompleted_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageOperationCompleted, const FString& OperationName, bool bSuccess);


// ********** End Delegate FAuracronFoliageOperationCompleted **************************************

// ********** Begin Delegate FAuracronFoliageError *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_562_DELEGATE \
AURACRONFOLIAGEBRIDGE_API void FAuracronFoliageError_DelegateWrapper(const FMulticastScriptDelegate& AuracronFoliageError, const FString& ErrorMessage, int32 ErrorCode);


// ********** End Delegate FAuracronFoliageError ***************************************************

// ********** Begin Class UAuracronFoliageBridgeAPI ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRestoreFoliageData); \
	DECLARE_FUNCTION(execBackupFoliageData); \
	DECLARE_FUNCTION(execGetSupportedImportFormats); \
	DECLARE_FUNCTION(execGetSupportedExportFormats); \
	DECLARE_FUNCTION(execImportFoliageData); \
	DECLARE_FUNCTION(execExportFoliageData); \
	DECLARE_FUNCTION(execLoadFoliageConfiguration); \
	DECLARE_FUNCTION(execSaveFoliageConfiguration); \
	DECLARE_FUNCTION(execValidateFoliageIntegrity); \
	DECLARE_FUNCTION(execGetFoliageDebugString); \
	DECLARE_FUNCTION(execDrawFoliageDebugInfo); \
	DECLARE_FUNCTION(execEnableFoliageDebugVisualization); \
	DECLARE_FUNCTION(execAssignFoliageToDataLayer); \
	DECLARE_FUNCTION(execSetFoliageStreamingDistance); \
	DECLARE_FUNCTION(execIsWorldPartitionEnabled); \
	DECLARE_FUNCTION(execEnableWorldPartitionSupport); \
	DECLARE_FUNCTION(execEnableFoliageShadowCasting); \
	DECLARE_FUNCTION(execSetFoliageInstanceColor); \
	DECLARE_FUNCTION(execSetFoliageInstanceCustomData); \
	DECLARE_FUNCTION(execSetFoliageMaterial); \
	DECLARE_FUNCTION(execGetFoliageInstancesInBox); \
	DECLARE_FUNCTION(execGetFoliageInstancesInRadius); \
	DECLARE_FUNCTION(execEnableFoliagePhysics); \
	DECLARE_FUNCTION(execSetFoliageCollisionSettings); \
	DECLARE_FUNCTION(execApplyBiomeToArea); \
	DECLARE_FUNCTION(execCreateFoliageBiome); \
	DECLARE_FUNCTION(execTransitionFoliageToSeason); \
	DECLARE_FUNCTION(execSetFoliageSeasonMode); \
	DECLARE_FUNCTION(execAnimateFoliageInstances); \
	DECLARE_FUNCTION(execCreateWindZone); \
	DECLARE_FUNCTION(execEnableGlobalWindForFoliage); \
	DECLARE_FUNCTION(execSetFoliageWindSettings); \
	DECLARE_FUNCTION(execEnableFoliageInteraction); \
	DECLARE_FUNCTION(execGetInteractiveFoliageActors); \
	DECLARE_FUNCTION(execConfigureInteractiveFoliage); \
	DECLARE_FUNCTION(execCreateInteractiveFoliageActor); \
	DECLARE_FUNCTION(execApplyGrassToLandscapeLayer); \
	DECLARE_FUNCTION(execGetLandscapeGrassType); \
	DECLARE_FUNCTION(execCreateLandscapeGrassType); \
	DECLARE_FUNCTION(execGetAttachedLandscapes); \
	DECLARE_FUNCTION(execDetachFoliageFromLandscape); \
	DECLARE_FUNCTION(execAttachFoliageToLandscape); \
	DECLARE_FUNCTION(execUpdateFoliagePerformanceMetrics); \
	DECLARE_FUNCTION(execOptimizeFoliageInstances); \
	DECLARE_FUNCTION(execEnableFoliageInstancing); \
	DECLARE_FUNCTION(execSetFoliageLODDistance); \
	DECLARE_FUNCTION(execSetFoliageCullingDistance); \
	DECLARE_FUNCTION(execGetFoliageDensityScale); \
	DECLARE_FUNCTION(execSetFoliageDensityScale); \
	DECLARE_FUNCTION(execGetPerformanceInfo); \
	DECLARE_FUNCTION(execSetProceduralFoliageRadius); \
	DECLARE_FUNCTION(execSetProceduralFoliageDensity); \
	DECLARE_FUNCTION(execGetProceduralFoliageVolumes); \
	DECLARE_FUNCTION(execClearProceduralFoliage); \
	DECLARE_FUNCTION(execGenerateProceduralFoliage); \
	DECLARE_FUNCTION(execCreateProceduralFoliageVolume); \
	DECLARE_FUNCTION(execConfigureProceduralFoliageSpawner); \
	DECLARE_FUNCTION(execCreateProceduralFoliageSpawner); \
	DECLARE_FUNCTION(execPlaceFoliageOnLandscapeLayer); \
	DECLARE_FUNCTION(execPlaceFoliageOnSpline); \
	DECLARE_FUNCTION(execFillAreaWithFoliage); \
	DECLARE_FUNCTION(execPlaceSingleFoliageInstance); \
	DECLARE_FUNCTION(execReapplyFoliageSettings); \
	DECLARE_FUNCTION(execSelectFoliageAtLocation); \
	DECLARE_FUNCTION(execEraseFoliageAtLocation); \
	DECLARE_FUNCTION(execPaintFoliageAtLocation); \
	DECLARE_FUNCTION(execClearAllFoliageInstances); \
	DECLARE_FUNCTION(execRemoveFoliageInstances); \
	DECLARE_FUNCTION(execAddFoliageInstances); \
	DECLARE_FUNCTION(execAddFoliageInstance); \
	DECLARE_FUNCTION(execRemoveFoliageInstance); \
	DECLARE_FUNCTION(execUpdateFoliageInstance); \
	DECLARE_FUNCTION(execGetFoliageInstanceInfo); \
	DECLARE_FUNCTION(execGetFoliageInstanceCount); \
	DECLARE_FUNCTION(execGetFoliageInstances); \
	DECLARE_FUNCTION(execCreateActorFoliageType); \
	DECLARE_FUNCTION(execCreateStaticMeshFoliageType); \
	DECLARE_FUNCTION(execGetUE5FoliageType); \
	DECLARE_FUNCTION(execUpdateFoliageTypeInfo); \
	DECLARE_FUNCTION(execGetFoliageTypeInfo); \
	DECLARE_FUNCTION(execGetRegisteredFoliageTypes); \
	DECLARE_FUNCTION(execUnregisterFoliageType); \
	DECLARE_FUNCTION(execRegisterFoliageType); \
	DECLARE_FUNCTION(execSetTargetWorld); \
	DECLARE_FUNCTION(execGetTargetWorld); \
	DECLARE_FUNCTION(execGetCurrentState); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdownFoliageBridge); \
	DECLARE_FUNCTION(execInitializeFoliageBridge);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBridgeAPI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageBridgeAPI(); \
	friend struct Z_Construct_UClass_UAuracronFoliageBridgeAPI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBridgeAPI_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageBridgeAPI, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageBridgeAPI_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageBridgeAPI(UAuracronFoliageBridgeAPI&&) = delete; \
	UAuracronFoliageBridgeAPI(const UAuracronFoliageBridgeAPI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageBridgeAPI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageBridgeAPI); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronFoliageBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_568_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h_571_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageBridgeAPI;

// ********** End Class UAuracronFoliageBridgeAPI **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageBridge_h

// ********** Begin Enum EAuracronFoliageState *****************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGESTATE(op) \
	op(EAuracronFoliageState::Uninitialized) \
	op(EAuracronFoliageState::Initializing) \
	op(EAuracronFoliageState::Ready) \
	op(EAuracronFoliageState::Processing) \
	op(EAuracronFoliageState::Error) \
	op(EAuracronFoliageState::Shutdown) 

enum class EAuracronFoliageState : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageState> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageState>();
// ********** End Enum EAuracronFoliageState *******************************************************

// ********** Begin Enum EAuracronFoliageType ******************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGETYPE(op) \
	op(EAuracronFoliageType::StaticMesh) \
	op(EAuracronFoliageType::Actor) \
	op(EAuracronFoliageType::Grass) \
	op(EAuracronFoliageType::Interactive) \
	op(EAuracronFoliageType::Procedural) 

enum class EAuracronFoliageType : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageType>();
// ********** End Enum EAuracronFoliageType ********************************************************

// ********** Begin Enum EAuracronFoliageBridgePlacementMode ***************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEBRIDGEPLACEMENTMODE(op) \
	op(EAuracronFoliageBridgePlacementMode::Paint) \
	op(EAuracronFoliageBridgePlacementMode::Erase) \
	op(EAuracronFoliageBridgePlacementMode::Select) \
	op(EAuracronFoliageBridgePlacementMode::Reapply) \
	op(EAuracronFoliageBridgePlacementMode::Single) \
	op(EAuracronFoliageBridgePlacementMode::Fill) \
	op(EAuracronFoliageBridgePlacementMode::Procedural) 

enum class EAuracronFoliageBridgePlacementMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageBridgePlacementMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageBridgePlacementMode>();
// ********** End Enum EAuracronFoliageBridgePlacementMode *****************************************

// ********** Begin Enum EAuracronFoliageDensityLevel **********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEDENSITYLEVEL(op) \
	op(EAuracronFoliageDensityLevel::VeryLow) \
	op(EAuracronFoliageDensityLevel::Low) \
	op(EAuracronFoliageDensityLevel::Medium) \
	op(EAuracronFoliageDensityLevel::High) \
	op(EAuracronFoliageDensityLevel::VeryHigh) \
	op(EAuracronFoliageDensityLevel::Ultra) \
	op(EAuracronFoliageDensityLevel::Custom) 

enum class EAuracronFoliageDensityLevel : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageDensityLevel> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageDensityLevel>();
// ********** End Enum EAuracronFoliageDensityLevel ************************************************

// ********** Begin Enum EAuracronFoliageCullingMode ***********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGECULLINGMODE(op) \
	op(EAuracronFoliageCullingMode::None) \
	op(EAuracronFoliageCullingMode::Distance) \
	op(EAuracronFoliageCullingMode::Frustum) \
	op(EAuracronFoliageCullingMode::Occlusion) \
	op(EAuracronFoliageCullingMode::Combined) 

enum class EAuracronFoliageCullingMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageCullingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageCullingMode>();
// ********** End Enum EAuracronFoliageCullingMode *************************************************

// ********** Begin Enum EAuracronFoliageBridgeLODMode *********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEBRIDGELODMODE(op) \
	op(EAuracronFoliageBridgeLODMode::Disabled) \
	op(EAuracronFoliageBridgeLODMode::Simple) \
	op(EAuracronFoliageBridgeLODMode::Advanced) \
	op(EAuracronFoliageBridgeLODMode::Hierarchical) \
	op(EAuracronFoliageBridgeLODMode::Adaptive) 

enum class EAuracronFoliageBridgeLODMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageBridgeLODMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageBridgeLODMode>();
// ********** End Enum EAuracronFoliageBridgeLODMode ***********************************************

// ********** Begin Enum EAuracronFoliageCollisionMode *********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGECOLLISIONMODE(op) \
	op(EAuracronFoliageCollisionMode::None) \
	op(EAuracronFoliageCollisionMode::Simple) \
	op(EAuracronFoliageCollisionMode::Complex) \
	op(EAuracronFoliageCollisionMode::Custom) 

enum class EAuracronFoliageCollisionMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageCollisionMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageCollisionMode>();
// ********** End Enum EAuracronFoliageCollisionMode ***********************************************

// ********** Begin Enum EAuracronFoliageWindMode **************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEWINDMODE(op) \
	op(EAuracronFoliageWindMode::None) \
	op(EAuracronFoliageWindMode::Simple) \
	op(EAuracronFoliageWindMode::Advanced) \
	op(EAuracronFoliageWindMode::Procedural) \
	op(EAuracronFoliageWindMode::Interactive) 

enum class EAuracronFoliageWindMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageWindMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageWindMode>();
// ********** End Enum EAuracronFoliageWindMode ****************************************************

// ********** Begin Enum EAuracronFoliageSeasonMode ************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGESEASONMODE(op) \
	op(EAuracronFoliageSeasonMode::None) \
	op(EAuracronFoliageSeasonMode::Spring) \
	op(EAuracronFoliageSeasonMode::Summer) \
	op(EAuracronFoliageSeasonMode::Autumn) \
	op(EAuracronFoliageSeasonMode::Winter) \
	op(EAuracronFoliageSeasonMode::Dynamic) 

enum class EAuracronFoliageSeasonMode : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageSeasonMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageSeasonMode>();
// ********** End Enum EAuracronFoliageSeasonMode **************************************************

// ********** Begin Enum EAuracronFoliageBiomeType *************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEBIOMETYPE(op) \
	op(EAuracronFoliageBiomeType::Temperate) \
	op(EAuracronFoliageBiomeType::Tropical) \
	op(EAuracronFoliageBiomeType::Desert) \
	op(EAuracronFoliageBiomeType::Tundra) \
	op(EAuracronFoliageBiomeType::Grassland) \
	op(EAuracronFoliageBiomeType::Wetland) \
	op(EAuracronFoliageBiomeType::Mountain) \
	op(EAuracronFoliageBiomeType::Coastal) \
	op(EAuracronFoliageBiomeType::Urban) \
	op(EAuracronFoliageBiomeType::Custom) 

enum class EAuracronFoliageBiomeType : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageBiomeType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageBiomeType>();
// ********** End Enum EAuracronFoliageBiomeType ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
