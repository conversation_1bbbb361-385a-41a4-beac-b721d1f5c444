// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronTutorialSaveGame.h"

#ifdef AURACRONTUTORIALBRIDGE_AuracronTutorialSaveGame_generated_h
#error "AuracronTutorialSaveGame.generated.h already included, missing '#pragma once' in AuracronTutorialSaveGame.h"
#endif
#define AURACRONTUTORIALBRIDGE_AuracronTutorialSaveGame_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FAuracronTutorialProgressData;

// ********** Begin ScriptStruct FAuracronTutorialProgressData *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_18_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTutorialProgressData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTutorialProgressData;
// ********** End ScriptStruct FAuracronTutorialProgressData ***************************************

// ********** Begin Delegate FOnAllProgressReset ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_147_DELEGATE \
static void FOnAllProgressReset_DelegateWrapper(const FMulticastScriptDelegate& OnAllProgressReset, int32 TutorialsInProgress, int32 CompletedTutorials);


// ********** End Delegate FOnAllProgressReset *****************************************************

// ********** Begin Class UAuracronTutorialSaveGame ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetAllProgress); \
	DECLARE_FUNCTION(execResetTutorialProgress); \
	DECLARE_FUNCTION(execMarkTutorialCompleted); \
	DECLARE_FUNCTION(execIsTutorialCompleted); \
	DECLARE_FUNCTION(execGetTutorialProgress); \
	DECLARE_FUNCTION(execUpdateTutorialProgress);


AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialSaveGame_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronTutorialSaveGame(); \
	friend struct Z_Construct_UClass_UAuracronTutorialSaveGame_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONTUTORIALBRIDGE_API UClass* Z_Construct_UClass_UAuracronTutorialSaveGame_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronTutorialSaveGame, USaveGame, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronTutorialBridge"), Z_Construct_UClass_UAuracronTutorialSaveGame_NoRegister) \
	DECLARE_SERIALIZER(UAuracronTutorialSaveGame)


#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronTutorialSaveGame(UAuracronTutorialSaveGame&&) = delete; \
	UAuracronTutorialSaveGame(const UAuracronTutorialSaveGame&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronTutorialSaveGame); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronTutorialSaveGame); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronTutorialSaveGame) \
	NO_API virtual ~UAuracronTutorialSaveGame();


#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_61_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h_64_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronTutorialSaveGame;

// ********** End Class UAuracronTutorialSaveGame **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronTutorialBridge_Public_AuracronTutorialSaveGame_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
