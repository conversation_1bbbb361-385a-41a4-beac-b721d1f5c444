// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGTestingFramework.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGTestingFramework_generated_h
#error "AuracronPCGTestingFramework.generated.h already included, missing '#pragma once' in AuracronPCGTestingFramework.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGTestingFramework_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronPCGTestRunner;
class UObject;
class UPCGData;
class UPCGGraph;
class UPCGMetadata;
class UPCGPointData;
class UPCGSettings;
class UPCGSpatialData;
enum class EAuracronPCGTestType : uint8;
struct FAuracronPCGPerformanceMetrics;
struct FAuracronPCGTestCase;
struct FAuracronPCGTestConfiguration;
struct FAuracronPCGTestingPerformanceMetrics;
struct FAuracronPCGTestResult;
struct FAuracronPCGTestSuite;

// ********** Begin ScriptStruct FAuracronPCGTestConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_94_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGTestConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGTestConfiguration;
// ********** End ScriptStruct FAuracronPCGTestConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronPCGTestCase **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_192_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGTestCase_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGTestCase;
// ********** End ScriptStruct FAuracronPCGTestCase ************************************************

// ********** Begin ScriptStruct FAuracronPCGTestResult ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_244_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGTestResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGTestResult;
// ********** End ScriptStruct FAuracronPCGTestResult **********************************************

// ********** Begin ScriptStruct FAuracronPCGTestSuite *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_297_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGTestSuite_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGTestSuite;
// ********** End ScriptStruct FAuracronPCGTestSuite ***********************************************

// ********** Begin ScriptStruct FAuracronPCGTestingPerformanceMetrics *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_331_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGTestingPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGTestingPerformanceMetrics;
// ********** End ScriptStruct FAuracronPCGTestingPerformanceMetrics *******************************

// ********** Begin Delegate FOnTestStarted ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_487_DELEGATE \
static void FOnTestStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTestStarted, const FString& TestName, EAuracronPCGTestType TestType);


// ********** End Delegate FOnTestStarted **********************************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_488_DELEGATE \
static void FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, const FString& TestName, FAuracronPCGTestResult Result);


// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnTestSuiteCompleted *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_489_DELEGATE \
static void FOnTestSuiteCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestSuiteCompleted, const FString& SuiteName, const TArray<FAuracronPCGTestResult>& Results);


// ********** End Delegate FOnTestSuiteCompleted ***************************************************

// ********** Begin Class UAuracronPCGTestRunner ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGenerateCSVReport); \
	DECLARE_FUNCTION(execGenerateJUnitXMLReport); \
	DECLARE_FUNCTION(execGenerateHTMLReport); \
	DECLARE_FUNCTION(execGenerateReport); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetTotalExecutionTime); \
	DECLARE_FUNCTION(execGetSuccessRate); \
	DECLARE_FUNCTION(execGetFailedTestCount); \
	DECLARE_FUNCTION(execGetPassedTestCount); \
	DECLARE_FUNCTION(execGetTotalTestCount); \
	DECLARE_FUNCTION(execGetPassedTests); \
	DECLARE_FUNCTION(execGetFailedTests); \
	DECLARE_FUNCTION(execGetTestResult); \
	DECLARE_FUNCTION(execGetTestResults); \
	DECLARE_FUNCTION(execClearAllTestSuites); \
	DECLARE_FUNCTION(execUnregisterTestSuite); \
	DECLARE_FUNCTION(execRegisterTestSuite); \
	DECLARE_FUNCTION(execRunTestsByTag); \
	DECLARE_FUNCTION(execRunTestsByCategory); \
	DECLARE_FUNCTION(execRunTestsByType); \
	DECLARE_FUNCTION(execRunAllTests); \
	DECLARE_FUNCTION(execRunTestCase); \
	DECLARE_FUNCTION(execRunTestSuite); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGTestRunner_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGTestRunner(); \
	friend struct Z_Construct_UClass_UAuracronPCGTestRunner_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGTestRunner_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGTestRunner, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGTestRunner_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGTestRunner)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGTestRunner(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGTestRunner(UAuracronPCGTestRunner&&) = delete; \
	UAuracronPCGTestRunner(const UAuracronPCGTestRunner&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGTestRunner); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGTestRunner); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGTestRunner) \
	NO_API virtual ~UAuracronPCGTestRunner();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_398_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_401_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGTestRunner;

// ********** End Class UAuracronPCGTestRunner *****************************************************

// ********** Begin Class UAuracronPCGPerformanceBenchmark *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLoadPerformanceBaseline); \
	DECLARE_FUNCTION(execSavePerformanceBaseline); \
	DECLARE_FUNCTION(execComparePerformance); \
	DECLARE_FUNCTION(execStressTestGraph); \
	DECLARE_FUNCTION(execStressTestNode); \
	DECLARE_FUNCTION(execGetAllProfiledTimes); \
	DECLARE_FUNCTION(execGetProfiledTime); \
	DECLARE_FUNCTION(execStopPerformanceProfiling); \
	DECLARE_FUNCTION(execStartPerformanceProfiling); \
	DECLARE_FUNCTION(execGetCurrentMemoryUsage); \
	DECLARE_FUNCTION(execStopMemoryProfiling); \
	DECLARE_FUNCTION(execStartMemoryProfiling); \
	DECLARE_FUNCTION(execBenchmarkDataProcessing); \
	DECLARE_FUNCTION(execBenchmarkPointGeneration); \
	DECLARE_FUNCTION(execBenchmarkGraph); \
	DECLARE_FUNCTION(execBenchmarkNode);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceBenchmark_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPerformanceBenchmark(); \
	friend struct Z_Construct_UClass_UAuracronPCGPerformanceBenchmark_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceBenchmark_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPerformanceBenchmark, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPerformanceBenchmark_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPerformanceBenchmark)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGPerformanceBenchmark(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPerformanceBenchmark(UAuracronPCGPerformanceBenchmark&&) = delete; \
	UAuracronPCGPerformanceBenchmark(const UAuracronPCGPerformanceBenchmark&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPerformanceBenchmark); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPerformanceBenchmark); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGPerformanceBenchmark) \
	NO_API virtual ~UAuracronPCGPerformanceBenchmark();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_530_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_533_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPerformanceBenchmark;

// ********** End Class UAuracronPCGPerformanceBenchmark *******************************************

// ********** Begin Class UAuracronPCGValidationSuite **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSaveValidationReport); \
	DECLARE_FUNCTION(execGenerateValidationReport); \
	DECLARE_FUNCTION(execLoadBaseline); \
	DECLARE_FUNCTION(execSaveResultAsBaseline); \
	DECLARE_FUNCTION(execCompareWithBaseline); \
	DECLARE_FUNCTION(execValidateExecutionTime); \
	DECLARE_FUNCTION(execValidateMemoryUsage); \
	DECLARE_FUNCTION(execValidatePerformanceThresholds); \
	DECLARE_FUNCTION(execTestInvalidInputs); \
	DECLARE_FUNCTION(execTestEdgeCases); \
	DECLARE_FUNCTION(execTestBoundaryConditions); \
	DECLARE_FUNCTION(execValidateResultConsistency); \
	DECLARE_FUNCTION(execValidateDataConsistency); \
	DECLARE_FUNCTION(execCheckMemoryIntegrity); \
	DECLARE_FUNCTION(execCheckDataIntegrity); \
	DECLARE_FUNCTION(execValidateNodeSettings); \
	DECLARE_FUNCTION(execValidateGraph); \
	DECLARE_FUNCTION(execValidateMetadata); \
	DECLARE_FUNCTION(execValidateSpatialData); \
	DECLARE_FUNCTION(execValidatePointData);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGValidationSuite_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGValidationSuite(); \
	friend struct Z_Construct_UClass_UAuracronPCGValidationSuite_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGValidationSuite_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGValidationSuite, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGValidationSuite_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGValidationSuite)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGValidationSuite(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGValidationSuite(UAuracronPCGValidationSuite&&) = delete; \
	UAuracronPCGValidationSuite(const UAuracronPCGValidationSuite&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGValidationSuite); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGValidationSuite); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGValidationSuite) \
	NO_API virtual ~UAuracronPCGValidationSuite();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_616_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_619_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGValidationSuite;

// ********** End Class UAuracronPCGValidationSuite ************************************************

// ********** Begin Class UAuracronPCGTestUtils ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetElapsedTime); \
	DECLARE_FUNCTION(execStopTimer); \
	DECLARE_FUNCTION(execStartTimer); \
	DECLARE_FUNCTION(execCreateMockPCGGraph); \
	DECLARE_FUNCTION(execCreateMockPCGData); \
	DECLARE_FUNCTION(execCreateMockNodeSettings); \
	DECLARE_FUNCTION(execTeardownTestEnvironment); \
	DECLARE_FUNCTION(execSetupTestEnvironment); \
	DECLARE_FUNCTION(execCleanupTestData); \
	DECLARE_FUNCTION(execGetTestOutputDirectory); \
	DECLARE_FUNCTION(execGetTestDataDirectory); \
	DECLARE_FUNCTION(execAssertFalse); \
	DECLARE_FUNCTION(execAssertTrue); \
	DECLARE_FUNCTION(execAssertArraySizeEqual); \
	DECLARE_FUNCTION(execAssertNotNull); \
	DECLARE_FUNCTION(execAssertVectorEqual); \
	DECLARE_FUNCTION(execAssertEqual); \
	DECLARE_FUNCTION(execCreateTestNodeSettings); \
	DECLARE_FUNCTION(execCreateTestGraph); \
	DECLARE_FUNCTION(execGenerateTestSpatialData); \
	DECLARE_FUNCTION(execGenerateTestPointData);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGTestUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGTestUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGTestUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGTestUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGTestUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGTestUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGTestUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGTestUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGTestUtils(UAuracronPCGTestUtils&&) = delete; \
	UAuracronPCGTestUtils(const UAuracronPCGTestUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGTestUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGTestUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGTestUtils) \
	NO_API virtual ~UAuracronPCGTestUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_740_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h_743_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGTestUtils;

// ********** End Class UAuracronPCGTestUtils ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGTestingFramework_h

// ********** Begin Enum EAuracronPCGTestType ******************************************************
#define FOREACH_ENUM_EAURACRONPCGTESTTYPE(op) \
	op(EAuracronPCGTestType::UnitTest) \
	op(EAuracronPCGTestType::IntegrationTest) \
	op(EAuracronPCGTestType::PerformanceTest) \
	op(EAuracronPCGTestType::ValidationTest) \
	op(EAuracronPCGTestType::RegressionTest) \
	op(EAuracronPCGTestType::StressTest) \
	op(EAuracronPCGTestType::FunctionalTest) \
	op(EAuracronPCGTestType::EndToEndTest) 

enum class EAuracronPCGTestType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTestType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTestType>();
// ********** End Enum EAuracronPCGTestType ********************************************************

// ********** Begin Enum EAuracronPCGTestResult ****************************************************
#define FOREACH_ENUM_EAURACRONPCGTESTRESULT(op) \
	op(EAuracronPCGTestResult::NotRun) \
	op(EAuracronPCGTestResult::Running) \
	op(EAuracronPCGTestResult::Passed) \
	op(EAuracronPCGTestResult::Failed) \
	op(EAuracronPCGTestResult::Skipped) \
	op(EAuracronPCGTestResult::Timeout) \
	op(EAuracronPCGTestResult::Error) 

enum class EAuracronPCGTestResult : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTestResult> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTestResult>();
// ********** End Enum EAuracronPCGTestResult ******************************************************

// ********** Begin Enum EAuracronPCGTestPriority **************************************************
#define FOREACH_ENUM_EAURACRONPCGTESTPRIORITY(op) \
	op(EAuracronPCGTestPriority::Low) \
	op(EAuracronPCGTestPriority::Normal) \
	op(EAuracronPCGTestPriority::High) \
	op(EAuracronPCGTestPriority::Critical) \
	op(EAuracronPCGTestPriority::Blocker) 

enum class EAuracronPCGTestPriority : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTestPriority> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTestPriority>();
// ********** End Enum EAuracronPCGTestPriority ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
