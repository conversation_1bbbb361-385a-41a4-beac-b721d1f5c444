#include "AuracronPCGBridge.h"
#include "Core.h"
#include "Modules/ModuleManager.h"
#include "Interfaces/IPluginManager.h"
#include "PCGSubsystem.h"
#include "PCGWorldActor.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "PCGSettings.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "PCGPoint.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "UObject/Package.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Landscape/Landscape.h"
#include "Components/SplineComponent.h"
#include "Engine/StaticMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMeshActor.h"

// UE 5.6 Advanced PCG includes
#include "PCGContext.h"
#include "PCGExecutionContext.h"
#include "PCGCustomVersion.h"
#include "PCGBlueprintElement.h"
#include "PCGParamData.h"
#include "PCGMetadataAccessor.h"
#include "Elements/PCGExecuteBlueprint.h"
#include "Elements/PCGDebugElement.h"
#include "Elements/PCGAttributeFilter.h"
#include "Elements/PCGBoundsModifier.h"
#include "Elements/PCGCopyPoints.h"
#include "Elements/PCGCreateSpline.h"
#include "Elements/PCGDataFromActor.h"
#include "Elements/PCGDifferenceElement.h"
#include "Elements/PCGGather.h"
#include "Elements/PCGIntersectionElement.h"
#include "Elements/PCGLoopElement.h"
#include "Elements/PCGMergeElement.h"
#include "Elements/PCGNormalToDensity.h"
#include "Elements/PCGPointExtents.h"
#include "Elements/PCGPointFilter.h"
#include "Elements/PCGPointMatchAndSet.h"
#include "Elements/PCGPointNeighborhood.h"
#include "Elements/PCGPointProcessingElementBase.h"
#include "Elements/PCGProjectionElement.h"
#include "Elements/PCGPropertyToParamData.h"
#include "Elements/PCGSelfPruning.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGStaticMeshSpawnerContext.h"
#include "Elements/PCGUnionElement.h"

// Async and Performance includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Stats/Stats.h"
#include "HAL/IConsoleManager.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Queue.h"

// PCG Element includes
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGTransformPoints.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Elements/PCGLandscapeData.h"
#include "Elements/PCGSplineData.h"
#include "Elements/PCGDensityFilter.h"
#include "Elements/PCGAttributeFilter.h"
#include "Elements/PCGMath.h"
#include "Elements/PCGCreateAttribute.h"
#include "Elements/PCGCopyPoints.h"
#include "Elements/PCGUnionData.h"
#include "Elements/PCGIntersectionData.h"
#include "Elements/PCGProjectionData.h"
#include "Elements/PCGBoundsModifier.h"
#include "Elements/PCGNormalToDensity.h"
#include "Elements/PCGSelfPruning.h"
#include "Elements/PCGDistance.h"
#include "Elements/PCGVolumeSampler.h"
#include "Elements/PCGPointFilter.h"
#include "Elements/PCGAttributeNoise.h"
#include "Elements/PCGAttributeSelectElement.h"
#include "Elements/PCGMetadataElement.h"

// Additional UE5.6 includes
#include "PCGPin.h"
#include "PCGEdge.h"
#include "Data/PCGCollisionShapeData.h"
#include "Data/PCGLandscapeData.h"
#include "Data/PCGSplineData.h"
#include "Data/PCGVolumeData.h"
#include "Data/PCGSurfaceData.h"
#include "Data/PCGTextureData.h"
#include "Data/PCGRenderTargetData.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGActorHelpers.h"
#include "Helpers/PCGBlueprintHelpers.h"
#include "Helpers/PCGSettingsHelpers.h"
#include "Metadata/PCGMetadataAccessor.h"
#include "Metadata/PCGMetadataAttribute.h"
#include "Metadata/PCGMetadataAttributeTraits.h"
#include "Metadata/PCGMetadataCommon.h"
#include "Metadata/PCGMetadataEntryKey.h"
#include "Metadata/PCGMetadataTypes.h"

DEFINE_LOG_CATEGORY(LogAuracronPCGBridge);

#define LOCTEXT_NAMESPACE "FAuracronPCGBridgeModule"

// ========================================
// Forward Declarations for Async Processing
// ========================================

/**
 * Async task for PCG generation
 */
class FPCGAsyncTask
{
public:
    FPCGAsyncTask(UPCGComponent* InComponent, const FPCGAsyncGenerationParams& InParams)
        : Component(InComponent)
        , Params(InParams)
        , bIsComplete(false)
        , Progress(0.0f)
        , bIsCancelled(false)
    {
        StartTime = FPlatformTime::Seconds();
    }

    bool IsComplete() const { return bIsComplete; }
    float GetProgress() const { return Progress; }
    bool IsCancelled() const { return bIsCancelled; }
    void Cancel() { bIsCancelled = true; }

    void Execute()
    {
        if (!Component.IsValid() || bIsCancelled)
        {
            bIsComplete = true;
            return;
        }

        // Execute PCG generation asynchronously
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
        {
            SCOPE_CYCLE_COUNTER(STAT_AuracronPCGAsyncProcessingTime);

            if (Component.IsValid() && !bIsCancelled)
            {
                Progress = 0.1f;

                // Generate PCG data
                Component->Generate();
                Progress = 0.8f;

                if (!bIsCancelled)
                {
                    Progress = 1.0f;
                    bIsComplete = true;

                    // Update performance stats
                    double EndTime = FPlatformTime::Seconds();
                    GenerationTime = EndTime - StartTime;

                    INC_DWORD_STAT(STAT_AuracronPCGAsyncTasks);
                }
            }
        });
    }

private:
    TWeakObjectPtr<UPCGComponent> Component;
    FPCGAsyncGenerationParams Params;
    FThreadSafeBool bIsComplete;
    FThreadSafeBool bIsCancelled;
    TAtomic<float> Progress;
    double StartTime;
    double GenerationTime;
};

// ========================================
// Static Variable Definitions
// ========================================

TMap<UPCGComponent*, TSharedPtr<FPCGAsyncTask>> UAuracronPCGBridgeAPI::AsyncTasks;
FCriticalSection UAuracronPCGBridgeAPI::AsyncTasksMutex;
TMap<UPCGComponent*, FPCGPerformanceData> UAuracronPCGBridgeAPI::PerformanceDataCache;
bool UAuracronPCGBridgeAPI::bProfilingEnabled = false;

void FAuracronPCGBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronPCGBridge, Warning, TEXT("AuracronPCGBridge module starting up"));
    
    // Initialize Python bindings
    InitializePythonBindings();
    
    // Register PCG elements
    RegisterPCGElements();
    
    UE_LOG(LogAuracronPCGBridge, Warning, TEXT("AuracronPCGBridge module started successfully"));
}

void FAuracronPCGBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronPCGBridge, Warning, TEXT("AuracronPCGBridge module shutting down"));
    
    // Cleanup Python bindings
    CleanupPythonBindings();
    
    // Unregister PCG elements
    UnregisterPCGElements();
    
    UE_LOG(LogAuracronPCGBridge, Warning, TEXT("AuracronPCGBridge module shut down successfully"));
}

void FAuracronPCGBridgeModule::InitializePythonBindings()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Initializing Python bindings for PCG Bridge"));
    
#if WITH_PYTHON
    // Register PCG Bridge API with Python using UE5.6 Python integration
    if (FPythonScriptPlugin::Get()->IsPythonAvailable())
    {
        // Expose UAuracronPCGBridgeAPI to Python
        FPyObjectPtr PCGBridgeModule = FPyWrapperTypeRegistry::Get().GenerateWrappedType(UAuracronPCGBridgeAPI::StaticClass());
        if (PCGBridgeModule)
        {
            PyDict_SetItemString(PyImport_GetModuleDict(), "auracron_pcg_bridge", PCGBridgeModule);
            
            // Register core PCG types
            FPyWrapperTypeRegistry::Get().GenerateWrappedType(UPCGGraph::StaticClass());
            FPyWrapperTypeRegistry::Get().GenerateWrappedType(UPCGComponent::StaticClass());
            FPyWrapperTypeRegistry::Get().GenerateWrappedType(UPCGNode::StaticClass());
            FPyWrapperTypeRegistry::Get().GenerateWrappedType(UPCGPointData::StaticClass());
            
            UE_LOG(LogAuracronPCGBridge, Log, TEXT("Python bindings initialized successfully"));
        }
        else
        {
            UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create Python wrapper for PCG Bridge API"));
        }
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Python not available - skipping Python bindings"));
    }
#else
    UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Python support not compiled - skipping Python bindings"));
#endif
}

void FAuracronPCGBridgeModule::CleanupPythonBindings()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Cleaning up Python bindings for PCG Bridge"));
    
    // Cleanup Python bindings
}

void FAuracronPCGBridgeModule::RegisterPCGElements()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Registering custom PCG elements"));
    
    // Register custom PCG elements here if needed
}

void FAuracronPCGBridgeModule::UnregisterPCGElements()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Unregistering custom PCG elements"));
    
    // Unregister custom PCG elements here if needed
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronPCGBridgeModule, AuracronPCGBridge)

// UAuracronPCGBridgeAPI Implementation

UAuracronPCGBridgeAPI::UAuracronPCGBridgeAPI()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("AuracronPCGBridgeAPI constructor called"));
}

// Static variables for bridge state
static bool bIsPCGBridgeInitialized = false;
static FString PCGBridgeVersion = TEXT("1.0.0");

bool UAuracronPCGBridgeAPI::InitializePCGBridge()
{
    if (bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is already initialized"));
        return true;
    }

    // Initialize PCG subsystem
    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        if (UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>())
        {
            // PCG subsystem is available
            bIsPCGBridgeInitialized = true;
            UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Bridge initialized successfully"));
            return true;
        }
    }

    // Try to get PCG subsystem from editor world if play world is not available
    if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        if (UPCGSubsystem* PCGSubsystem = GEditor->GetEditorWorldContext().World()->GetSubsystem<UPCGSubsystem>())
        {
            bIsPCGBridgeInitialized = true;
            UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Bridge initialized successfully (Editor mode)"));
            return true;
        }
    }

    UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to initialize PCG Bridge - PCG Subsystem not available"));
    return false;
}

void UAuracronPCGBridgeAPI::ShutdownPCGBridge()
{
    if (!bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is not initialized"));
        return;
    }

    // Cleanup any resources if needed
    bIsPCGBridgeInitialized = false;
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Bridge shutdown successfully"));
}

bool UAuracronPCGBridgeAPI::IsPCGBridgeReady()
{
    return bIsPCGBridgeInitialized;
}

FString UAuracronPCGBridgeAPI::GetPCGBridgeVersion()
{
    return PCGBridgeVersion;
}

UPCGGraph* UAuracronPCGBridgeAPI::CreatePCGGraph(const FString& GraphName)
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Creating PCG Graph: %s"), *GraphName);
    
    if (GraphName.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Graph name cannot be empty"));
        return nullptr;
    }
    
    // Create a new PCG Graph
    UPCGGraph* NewGraph = NewObject<UPCGGraph>();
    if (NewGraph)
    {
        NewGraph->SetFlags(RF_Public | RF_Standalone);
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully created PCG Graph: %s"), *GraphName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create PCG Graph: %s"), *GraphName);
    }
    
    return NewGraph;
}

bool UAuracronPCGBridgeAPI::SavePCGGraph(UPCGGraph* Graph, const FString& PackagePath)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot save null PCG Graph"));
        return false;
    }
    
    if (PackagePath.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Package path cannot be empty"));
        return false;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Saving PCG Graph to: %s"), *PackagePath);
    
    // Create package and save the graph
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create package: %s"), *PackagePath);
        return false;
    }
    
    Graph->Rename(nullptr, Package);
    Package->SetDirtyFlag(true);
    
    // Mark package for save
    FAssetRegistryModule::AssetCreated(Graph);
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully saved PCG Graph to: %s"), *PackagePath);
    return true;
}

UPCGGraph* UAuracronPCGBridgeAPI::LoadPCGGraph(const FString& PackagePath)
{
    if (PackagePath.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Package path cannot be empty"));
        return nullptr;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Loading PCG Graph from: %s"), *PackagePath);
    
    UPCGGraph* LoadedGraph = LoadObject<UPCGGraph>(nullptr, *PackagePath);
    if (LoadedGraph)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully loaded PCG Graph from: %s"), *PackagePath);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to load PCG Graph from: %s"), *PackagePath);
    }
    
    return LoadedGraph;
}

UPCGComponent* UAuracronPCGBridgeAPI::CreatePCGComponent(AActor* Owner)
{
    if (!Owner)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot create PCG Component without owner actor"));
        return nullptr;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Creating PCG Component for actor: %s"), *Owner->GetName());
    
    UPCGComponent* PCGComponent = NewObject<UPCGComponent>(Owner);
    if (PCGComponent)
    {
        Owner->AddInstanceComponent(PCGComponent);
        PCGComponent->AttachToComponent(Owner->GetRootComponent(), 
                                       FAttachmentTransformRules::KeepWorldTransform);
        PCGComponent->RegisterComponent();
        
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully created PCG Component for actor: %s"), *Owner->GetName());
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create PCG Component for actor: %s"), *Owner->GetName());
    }
    
    return PCGComponent;
}

bool UAuracronPCGBridgeAPI::SetPCGGraph(UPCGComponent* Component, UPCGGraph* Graph)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set graph on null PCG Component"));
        return false;
    }
    
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set null graph on PCG Component"));
        return false;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Setting PCG Graph on component"));
    
    Component->SetGraph(Graph);
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully set PCG Graph on component"));
    return true;
}

bool UAuracronPCGBridgeAPI::GeneratePCG(UPCGComponent* Component, bool bForce)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot generate on null PCG Component"));
        return false;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Generating PCG content (Force: %s)"), bForce ? TEXT("true") : TEXT("false"));
    
    if (bForce)
    {
        Component->Generate();
    }
    else
    {
        Component->GenerateLocal(/*bForce=*/false);
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG generation initiated successfully"));
    return true;
}

bool UAuracronPCGBridgeAPI::CleanupPCG(UPCGComponent* Component)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot cleanup null PCG Component"));
        return false;
    }
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Cleaning up PCG component"));
    
    Component->CleanupLocal(/*bRemoveComponents=*/true);
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG cleanup completed successfully"));
    return true;
}

UPCGNode* UAuracronPCGBridgeAPI::CreatePCGNode(UPCGGraph* Graph, const FString& NodeType, const FString& NodeName)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Graph is null"));
        return nullptr;
    }

    UClass* SettingsClass = nullptr;
    FString DefaultNodeName;

    // Map node type strings to their corresponding settings classes
    if (NodeType == TEXT("PCGInputNode") || NodeType == TEXT("Input"))
    {
        // For input nodes, we'll use a basic settings class or create a simple node
        SettingsClass = UPCGSettings::StaticClass();
        DefaultNodeName = TEXT("Input");
    }
    else if (NodeType == TEXT("PCGOutputNode") || NodeType == TEXT("Output"))
    {
        // For output nodes
        SettingsClass = UPCGSettings::StaticClass();
        DefaultNodeName = TEXT("Output");
    }
    else if (NodeType == TEXT("PCGTransformNode") || NodeType == TEXT("Transform"))
    {
        SettingsClass = UPCGTransformPointsSettings::StaticClass();
        DefaultNodeName = TEXT("Transform Points");
    }
    else if (NodeType == TEXT("SurfaceSampler"))
    {
        SettingsClass = UPCGSurfaceSamplerSettings::StaticClass();
        DefaultNodeName = TEXT("Surface Sampler");
    }
    else if (NodeType == TEXT("StaticMeshSpawner"))
    {
        SettingsClass = UPCGStaticMeshSpawnerSettings::StaticClass();
        DefaultNodeName = TEXT("Static Mesh Spawner");
    }
    else
    {
        // Default to basic settings for unknown types
        SettingsClass = UPCGSettings::StaticClass();
        DefaultNodeName = NodeType;
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Unknown node type '%s', using default settings"), *NodeType);
    }

    if (!SettingsClass)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to find settings class for node type '%s'"), *NodeType);
        return nullptr;
    }

    UPCGNode* NewNode = Graph->AddNode(SettingsClass);
    if (NewNode)
    {
        FString FinalNodeName = NodeName.IsEmpty() ? DefaultNodeName : NodeName;
        NewNode->SetNodeTitle(FText::FromString(FinalNodeName));
        
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG node '%s' of type '%s' created successfully"), *FinalNodeName, *NodeType);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create PCG node of type '%s'"), *NodeType);
    }
    
    return NewNode;
}

bool UAuracronPCGBridgeAPI::ExecutePCGGraph(UPCGGraph* Graph)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Graph is null"));
        return false;
    }

    if (!bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is not initialized"));
        return false;
    }

    // Get PCG subsystem
    UWorld* World = nullptr;
    if (GEngine->GetCurrentPlayWorld())
    {
        World = GEngine->GetCurrentPlayWorld();
    }
    else if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        World = GEditor->GetEditorWorldContext().World();
    }

    if (!World)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("No valid world found for PCG execution"));
        return false;
    }

    UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
    if (!PCGSubsystem)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("PCG Subsystem not available"));
        return false;
    }

    // Find or create a PCG World Actor to execute the graph
    APCGWorldActor* PCGWorldActor = PCGSubsystem->GetPCGWorldActor();
    if (!PCGWorldActor)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("No PCG World Actor found"));
        return false;
    }

    // Get the PCG component from the world actor
    UPCGComponent* PCGComponent = PCGWorldActor->GetPCGComponent();
    if (!PCGComponent)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("No PCG Component found on PCG World Actor"));
        return false;
    }

    // Set the graph on the component
    PCGComponent->SetGraph(Graph);
    
    // Execute the graph
    PCGComponent->Generate();
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Graph '%s' execution initiated successfully"), *Graph->GetName());
    return true;
}

bool UAuracronPCGBridgeAPI::IsPCGGraphExecuting(UPCGGraph* Graph)
{
    if (!Graph)
    {
        return false;
    }

    // Get the current world and PCG subsystem
    UWorld* World = nullptr;
    if (GEngine->GetCurrentPlayWorld())
    {
        World = GEngine->GetCurrentPlayWorld();
    }
    else if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        World = GEditor->GetEditorWorldContext().World();
    }

    if (!World)
    {
        return false;
    }

    UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
    if (!PCGSubsystem)
    {
        return false;
    }

    // Check if there are any active PCG tasks for this graph
    APCGWorldActor* PCGWorldActor = PCGSubsystem->GetPCGWorldActor();
    if (!PCGWorldActor)
    {
        return false;
    }

    UPCGComponent* PCGComponent = PCGWorldActor->GetPCGComponent();
    if (!PCGComponent)
    {
        return false;
    }

    // Check if the component is currently generating and using our graph
    return PCGComponent->GetGraph() == Graph && PCGComponent->IsGenerating();
}

UPCGPointData* UAuracronPCGBridgeAPI::CreatePointData()
{
    if (!bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is not initialized"));
        return nullptr;
    }

    UPCGPointData* PointData = NewObject<UPCGPointData>();
    if (PointData)
    {
        PointData->InitializeFromData(MakeShared<FPCGPointData>());
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Point data created successfully"));
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create point data"));
    }

    return PointData;
}

bool UAuracronPCGBridgeAPI::SetPointDataPoints(UPCGPointData* PointData, const TArray<FPCGPoint>& Points)
{
    if (!PointData)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PointData is null"));
        return false;
    }

    if (!bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is not initialized"));
        return false;
    }

    // Get the mutable point data
    FPCGPointData& MutablePointData = PointData->GetMutablePointData();
    
    // Set the points
    MutablePointData.GetMutablePoints() = Points;
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Set %d points in point data"), Points.Num());
    return true;
}

TArray<FPCGPoint> UAuracronPCGBridgeAPI::GetPointDataPoints(UPCGPointData* PointData)
{
    TArray<FPCGPoint> EmptyArray;
    
    if (!PointData)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PointData is null"));
        return EmptyArray;
    }

    if (!bIsPCGBridgeInitialized)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("PCG Bridge is not initialized"));
        return EmptyArray;
    }

    const FPCGPointData& PointDataRef = PointData->GetPointData();
    const TArray<FPCGPoint>& Points = PointDataRef.GetPoints();
    
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Retrieved %d points from point data"), Points.Num());
    return Points;
}

UPCGNode* UAuracronPCGBridgeAPI::AddSurfaceSamplerNode(UPCGGraph* Graph, const FString& NodeName)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add Surface Sampler node to null graph"));
        return nullptr;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Adding Surface Sampler node: %s"), *NodeName);

    // Create Surface Sampler settings
    UPCGSurfaceSamplerSettings* SamplerSettings = NewObject<UPCGSurfaceSamplerSettings>();
    if (!SamplerSettings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create Surface Sampler settings"));
        return nullptr;
    }

    // Configure default settings
    SamplerSettings->PointsPerSquaredMeter = 0.1f;
    SamplerSettings->PointExtents = FVector(100.0f, 100.0f, 100.0f);
    SamplerSettings->Looseness = 1.0f;
    SamplerSettings->bUnbounded = false;

    // Create and add node to graph
    UPCGNode* NewNode = Graph->AddNode(SamplerSettings);
    if (NewNode)
    {
        NewNode->SetSettingsInterface(SamplerSettings);
        if (!NodeName.IsEmpty())
        {
            NewNode->GetSettings()->SetNodeTitle(FText::FromString(NodeName));
        }

        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully added Surface Sampler node: %s"), *NodeName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to add Surface Sampler node: %s"), *NodeName);
    }

    return NewNode;
}

UPCGNode* UAuracronPCGBridgeAPI::AddTransformPointsNode(UPCGGraph* Graph, const FString& NodeName)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add Transform Points node to null graph"));
        return nullptr;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Adding Transform Points node: %s"), *NodeName);

    // Create Transform Points settings
    UPCGTransformPointsSettings* TransformSettings = NewObject<UPCGTransformPointsSettings>();
    if (!TransformSettings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create Transform Points settings"));
        return nullptr;
    }

    // Configure default settings
    TransformSettings->OffsetMin = FVector::ZeroVector;
    TransformSettings->OffsetMax = FVector::ZeroVector;
    TransformSettings->RotationMin = FVector::ZeroVector;
    TransformSettings->RotationMax = FVector(0.0f, 0.0f, 360.0f);
    TransformSettings->ScaleMin = FVector(0.8f, 0.8f, 0.8f);
    TransformSettings->ScaleMax = FVector(1.2f, 1.2f, 1.2f);
    TransformSettings->bAbsoluteOffset = false;
    TransformSettings->bAbsoluteRotation = true;
    TransformSettings->bAbsoluteScale = false;

    // Create and add node to graph
    UPCGNode* NewNode = Graph->AddNode(TransformSettings);
    if (NewNode)
    {
        NewNode->SetSettingsInterface(TransformSettings);
        if (!NodeName.IsEmpty())
        {
            NewNode->GetSettings()->SetNodeTitle(FText::FromString(NodeName));
        }

        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully added Transform Points node: %s"), *NodeName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to add Transform Points node: %s"), *NodeName);
    }

    return NewNode;
}

UPCGNode* UAuracronPCGBridgeAPI::AddStaticMeshSpawnerNode(UPCGGraph* Graph, const FString& NodeName)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add Static Mesh Spawner node to null graph"));
        return nullptr;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Adding Static Mesh Spawner node: %s"), *NodeName);

    // Create Static Mesh Spawner settings
    UPCGStaticMeshSpawnerSettings* SpawnerSettings = NewObject<UPCGStaticMeshSpawnerSettings>();
    if (!SpawnerSettings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create Static Mesh Spawner settings"));
        return nullptr;
    }

    // Configure default settings
    SpawnerSettings->MeshEntries.Empty();
    SpawnerSettings->bPartitionActors = true;
    SpawnerSettings->bInheritActorTags = false;

    // Create and add node to graph
    UPCGNode* NewNode = Graph->AddNode(SpawnerSettings);
    if (NewNode)
    {
        NewNode->SetSettingsInterface(SpawnerSettings);
        if (!NodeName.IsEmpty())
        {
            NewNode->GetSettings()->SetNodeTitle(FText::FromString(NodeName));
        }

        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully added Static Mesh Spawner node: %s"), *NodeName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to add Static Mesh Spawner node: %s"), *NodeName);
    }

    return NewNode;
}

UPCGNode* UAuracronPCGBridgeAPI::AddLandscapeDataNode(UPCGGraph* Graph, const FString& NodeName)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add Landscape Data node to null graph"));
        return nullptr;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Adding Landscape Data node: %s"), *NodeName);

    // Create Landscape Data settings
    UPCGLandscapeDataSettings* LandscapeSettings = NewObject<UPCGLandscapeDataSettings>();
    if (!LandscapeSettings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create Landscape Data settings"));
        return nullptr;
    }

    // Configure default settings
    LandscapeSettings->bGetHeightOnly = false;
    LandscapeSettings->bGetLayerWeights = true;
    LandscapeSettings->bUseMetadata = true;

    // Create and add node to graph
    UPCGNode* NewNode = Graph->AddNode(LandscapeSettings);
    if (NewNode)
    {
        NewNode->SetSettingsInterface(LandscapeSettings);
        if (!NodeName.IsEmpty())
        {
            NewNode->GetSettings()->SetNodeTitle(FText::FromString(NodeName));
        }

        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully added Landscape Data node: %s"), *NodeName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to add Landscape Data node: %s"), *NodeName);
    }

    return NewNode;
}

bool UAuracronPCGBridgeAPI::ConnectPCGNodes(UPCGNode* OutputNode, UPCGNode* InputNode, const FString& OutputPinName, const FString& InputPinName)
{
    if (!OutputNode || !InputNode)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot connect null PCG nodes"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Connecting PCG nodes: %s -> %s"),
           OutputNode->GetSettings() ? *OutputNode->GetSettings()->GetNodeTitle().ToString() : TEXT("Unknown"),
           InputNode->GetSettings() ? *InputNode->GetSettings()->GetNodeTitle().ToString() : TEXT("Unknown"));

    // Get output pin
    UPCGPin* OutputPin = nullptr;
    FString ActualOutputPinName = OutputPinName.IsEmpty() ? TEXT("Output") : OutputPinName;

    for (UPCGPin* Pin : OutputNode->GetOutputPins())
    {
        if (Pin && Pin->Properties.Label.ToString() == ActualOutputPinName)
        {
            OutputPin = Pin;
            break;
        }
    }

    if (!OutputPin)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Output pin '%s' not found"), *ActualOutputPinName);
        return false;
    }

    // Get input pin
    UPCGPin* InputPin = nullptr;
    FString ActualInputPinName = InputPinName.IsEmpty() ? TEXT("Input") : InputPinName;

    for (UPCGPin* Pin : InputNode->GetInputPins())
    {
        if (Pin && Pin->Properties.Label.ToString() == ActualInputPinName)
        {
            InputPin = Pin;
            break;
        }
    }

    if (!InputPin)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Input pin '%s' not found"), *ActualInputPinName);
        return false;
    }

    // Create edge connection
    UPCGGraph* Graph = OutputNode->GetGraph();
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get graph from output node"));
        return false;
    }

    UPCGEdge* NewEdge = Graph->AddEdge(OutputPin, InputPin);
    if (NewEdge)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully connected PCG nodes"));
        return true;
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create edge between nodes"));
        return false;
    }
}

bool UAuracronPCGBridgeAPI::SetSurfaceSamplerSettings(UPCGNode* Node, float PointsPerSquaredMeter, const FVector& PointExtents, float Looseness)
{
    if (!Node)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set settings on null node"));
        return false;
    }

    UPCGSurfaceSamplerSettings* Settings = Cast<UPCGSurfaceSamplerSettings>(Node->GetSettings());
    if (!Settings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Node is not a Surface Sampler node"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Setting Surface Sampler settings: Points/m²=%.3f, Extents=(%.1f,%.1f,%.1f), Looseness=%.3f"),
           PointsPerSquaredMeter, PointExtents.X, PointExtents.Y, PointExtents.Z, Looseness);

    Settings->PointsPerSquaredMeter = PointsPerSquaredMeter;
    Settings->PointExtents = PointExtents;
    Settings->Looseness = Looseness;

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully updated Surface Sampler settings"));
    return true;
}

bool UAuracronPCGBridgeAPI::SetTransformPointsSettings(UPCGNode* Node, const FVector& OffsetMin, const FVector& OffsetMax,
                                                       const FVector& RotationMin, const FVector& RotationMax,
                                                       const FVector& ScaleMin, const FVector& ScaleMax,
                                                       bool bAbsoluteRotation)
{
    if (!Node)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set settings on null node"));
        return false;
    }

    UPCGTransformPointsSettings* Settings = Cast<UPCGTransformPointsSettings>(Node->GetSettings());
    if (!Settings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Node is not a Transform Points node"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Setting Transform Points settings"));

    Settings->OffsetMin = OffsetMin;
    Settings->OffsetMax = OffsetMax;
    Settings->RotationMin = RotationMin;
    Settings->RotationMax = RotationMax;
    Settings->ScaleMin = ScaleMin;
    Settings->ScaleMax = ScaleMax;
    Settings->bAbsoluteRotation = bAbsoluteRotation;

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully updated Transform Points settings"));
    return true;
}

bool UAuracronPCGBridgeAPI::AddStaticMeshToSpawner(UPCGNode* Node, UStaticMesh* StaticMesh, float Weight)
{
    if (!Node)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add mesh to null node"));
        return false;
    }

    if (!StaticMesh)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot add null static mesh"));
        return false;
    }

    UPCGStaticMeshSpawnerSettings* Settings = Cast<UPCGStaticMeshSpawnerSettings>(Node->GetSettings());
    if (!Settings)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Node is not a Static Mesh Spawner node"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Adding static mesh to spawner: %s (Weight: %.3f)"),
           *StaticMesh->GetName(), Weight);

    // Create mesh entry
    FPCGStaticMeshSpawnerEntry MeshEntry;
    MeshEntry.Descriptor.StaticMesh = StaticMesh;
    MeshEntry.Weight = Weight;
    MeshEntry.bOverrideCollisionProfile = false;
    MeshEntry.bOverrideMaterials = false;

    Settings->MeshEntries.Add(MeshEntry);

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully added static mesh to spawner"));
    return true;
}

TArray<FVector> UAuracronPCGBridgeAPI::GetGeneratedPoints(UPCGComponent* Component)
{
    TArray<FVector> Points;

    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get points from null component"));
        return Points;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Getting generated points from PCG component"));

    // Get generated data from component
    const FPCGDataCollection& GeneratedData = Component->GetGeneratedGraphOutput();

    for (const FPCGTaggedData& TaggedData : GeneratedData.TaggedData)
    {
        if (const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data))
        {
            const TArray<FPCGPoint>& PCGPoints = PointData->GetPoints();

            for (const FPCGPoint& Point : PCGPoints)
            {
                Points.Add(Point.Transform.GetLocation());
            }
        }
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Retrieved %d points from PCG component"), Points.Num());
    return Points;
}

TArray<FTransform> UAuracronPCGBridgeAPI::GetGeneratedTransforms(UPCGComponent* Component)
{
    TArray<FTransform> Transforms;

    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get transforms from null component"));
        return Transforms;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Getting generated transforms from PCG component"));

    // Get generated data from component
    const FPCGDataCollection& GeneratedData = Component->GetGeneratedGraphOutput();

    for (const FPCGTaggedData& TaggedData : GeneratedData.TaggedData)
    {
        if (const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data))
        {
            const TArray<FPCGPoint>& PCGPoints = PointData->GetPoints();

            for (const FPCGPoint& Point : PCGPoints)
            {
                Transforms.Add(Point.Transform);
            }
        }
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Retrieved %d transforms from PCG component"), Transforms.Num());
    return Transforms;
}

int32 UAuracronPCGBridgeAPI::GetGeneratedPointCount(UPCGComponent* Component)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get point count from null component"));
        return 0;
    }

    int32 TotalPoints = 0;

    // Get generated data from component
    const FPCGDataCollection& GeneratedData = Component->GetGeneratedGraphOutput();

    for (const FPCGTaggedData& TaggedData : GeneratedData.TaggedData)
    {
        if (const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data))
        {
            TotalPoints += PointData->GetPoints().Num();
        }
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG component has %d total points"), TotalPoints);
    return TotalPoints;
}

bool UAuracronPCGBridgeAPI::SetPCGGraphParameter(UPCGGraph* Graph, const FString& ParameterName, float Value)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set parameter on null graph"));
        return false;
    }

    if (ParameterName.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Parameter name cannot be empty"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Setting graph parameter '%s' to %.3f"), *ParameterName, Value);

    // Get graph parameters
    FPCGGraphParametersInterface* ParametersInterface = Graph->GetParametersInterface();
    if (!ParametersInterface)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Graph has no parameters interface"));
        return false;
    }

    // Try to set the parameter value
    bool bSuccess = false;

    // Check if parameter exists and set it
    const TArray<FPCGGraphParameter>& Parameters = ParametersInterface->GetParameters();
    for (const FPCGGraphParameter& Parameter : Parameters)
    {
        if (Parameter.Name.ToString() == ParameterName)
        {
            // Set parameter value based on type
            if (Parameter.PropertyType == EPCGMetadataTypes::Float)
            {
                ParametersInterface->SetFloatParameter(FName(*ParameterName), Value);
                bSuccess = true;
            }
            else if (Parameter.PropertyType == EPCGMetadataTypes::Double)
            {
                ParametersInterface->SetDoubleParameter(FName(*ParameterName), static_cast<double>(Value));
                bSuccess = true;
            }
            else if (Parameter.PropertyType == EPCGMetadataTypes::Integer32)
            {
                ParametersInterface->SetIntParameter(FName(*ParameterName), static_cast<int32>(Value));
                bSuccess = true;
            }
            break;
        }
    }

    if (bSuccess)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully set graph parameter '%s'"), *ParameterName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to set graph parameter '%s' - parameter not found or type mismatch"), *ParameterName);
    }

    return bSuccess;
}

bool UAuracronPCGBridgeAPI::SetPCGGraphParameterVector(UPCGGraph* Graph, const FString& ParameterName, const FVector& Value)
{
    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set parameter on null graph"));
        return false;
    }

    if (ParameterName.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Parameter name cannot be empty"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Setting graph vector parameter '%s' to (%.3f, %.3f, %.3f)"),
           *ParameterName, Value.X, Value.Y, Value.Z);

    // Get graph parameters
    FPCGGraphParametersInterface* ParametersInterface = Graph->GetParametersInterface();
    if (!ParametersInterface)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Graph has no parameters interface"));
        return false;
    }

    // Try to set the vector parameter
    bool bSuccess = false;

    const TArray<FPCGGraphParameter>& Parameters = ParametersInterface->GetParameters();
    for (const FPCGGraphParameter& Parameter : Parameters)
    {
        if (Parameter.Name.ToString() == ParameterName)
        {
            if (Parameter.PropertyType == EPCGMetadataTypes::Vector)
            {
                ParametersInterface->SetVectorParameter(FName(*ParameterName), Value);
                bSuccess = true;
            }
            break;
        }
    }

    if (bSuccess)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully set graph vector parameter '%s'"), *ParameterName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to set graph vector parameter '%s' - parameter not found or type mismatch"), *ParameterName);
    }

    return bSuccess;
}

TArray<FString> UAuracronPCGBridgeAPI::GetPCGGraphParameters(UPCGGraph* Graph)
{
    TArray<FString> ParameterNames;

    if (!Graph)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get parameters from null graph"));
        return ParameterNames;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Getting PCG graph parameters"));

    // Get graph parameters
    FPCGGraphParametersInterface* ParametersInterface = Graph->GetParametersInterface();
    if (!ParametersInterface)
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Graph has no parameters interface"));
        return ParameterNames;
    }

    const TArray<FPCGGraphParameter>& Parameters = ParametersInterface->GetParameters();
    for (const FPCGGraphParameter& Parameter : Parameters)
    {
        ParameterNames.Add(Parameter.Name.ToString());
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Found %d parameters in graph"), ParameterNames.Num());
    return ParameterNames;
}

bool UAuracronPCGBridgeAPI::CreatePCGAttribute(UPCGPointData* PointData, const FString& AttributeName, const FString& AttributeType)
{
    if (!PointData)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot create attribute on null point data"));
        return false;
    }

    if (AttributeName.IsEmpty())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Attribute name cannot be empty"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Creating PCG attribute '%s' of type '%s'"), *AttributeName, *AttributeType);

    UPCGMetadata* Metadata = PointData->MutableMetadata();
    if (!Metadata)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Point data has no metadata"));
        return false;
    }

    // Determine attribute type
    EPCGMetadataTypes MetadataType = EPCGMetadataTypes::Float;

    if (AttributeType.Equals(TEXT("Float"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::Float;
    }
    else if (AttributeType.Equals(TEXT("Double"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::Double;
    }
    else if (AttributeType.Equals(TEXT("Integer"), ESearchCase::IgnoreCase) || AttributeType.Equals(TEXT("Int"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::Integer32;
    }
    else if (AttributeType.Equals(TEXT("Vector"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::Vector;
    }
    else if (AttributeType.Equals(TEXT("String"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::String;
    }
    else if (AttributeType.Equals(TEXT("Boolean"), ESearchCase::IgnoreCase) || AttributeType.Equals(TEXT("Bool"), ESearchCase::IgnoreCase))
    {
        MetadataType = EPCGMetadataTypes::Boolean;
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Unknown attribute type '%s', defaulting to Float"), *AttributeType);
    }

    // Create attribute
    FPCGMetadataAttributeBase* NewAttribute = Metadata->CreateAttribute(FName(*AttributeName), MetadataType, false, false);

    if (NewAttribute)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Successfully created attribute '%s'"), *AttributeName);
        return true;
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Failed to create attribute '%s'"), *AttributeName);
        return false;
    }
}

bool UAuracronPCGBridgeAPI::SetPointAttribute(UPCGPointData* PointData, int32 PointIndex, const FString& AttributeName, float Value)
{
    if (!PointData)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot set attribute on null point data"));
        return false;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    if (PointIndex < 0 || PointIndex >= Points.Num())
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Point index %d is out of range (0-%d)"), PointIndex, Points.Num() - 1);
        return false;
    }

    UPCGMetadata* Metadata = PointData->MutableMetadata();
    if (!Metadata)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Point data has no metadata"));
        return false;
    }

    // Get attribute
    FPCGMetadataAttributeBase* Attribute = Metadata->GetMutableAttribute(FName(*AttributeName));
    if (!Attribute)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Attribute '%s' not found"), *AttributeName);
        return false;
    }

    // Set attribute value based on type
    PCGMetadataEntryKey EntryKey = Points[PointIndex].MetadataEntry;

    if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        static_cast<FPCGMetadataAttribute<float>*>(Attribute)->SetValue(EntryKey, Value);
    }
    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<double>::Id)
    {
        static_cast<FPCGMetadataAttribute<double>*>(Attribute)->SetValue(EntryKey, static_cast<double>(Value));
    }
    else if (Attribute->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
    {
        static_cast<FPCGMetadataAttribute<int32>*>(Attribute)->SetValue(EntryKey, static_cast<int32>(Value));
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Unsupported attribute type for float value"));
        return false;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Set attribute '%s' on point %d to %.3f"), *AttributeName, PointIndex, Value);
    return true;
}

TArray<AActor*> UAuracronPCGBridgeAPI::GetGeneratedActors(UPCGComponent* Component)
{
    TArray<AActor*> GeneratedActors;

    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot get actors from null component"));
        return GeneratedActors;
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Getting generated actors from PCG component"));

    // Get all generated actors from the component
    const TSet<TObjectPtr<AActor>>& ManagedActors = Component->GetGeneratedActors();

    for (const TObjectPtr<AActor>& Actor : ManagedActors)
    {
        if (Actor.IsValid())
        {
            GeneratedActors.Add(Actor.Get());
        }
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Retrieved %d generated actors"), GeneratedActors.Num());
    return GeneratedActors;
}

bool UAuracronPCGBridgeAPI::IsPCGGenerationComplete(UPCGComponent* Component)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot check generation status on null component"));
        return false;
    }

    bool bIsComplete = !Component->IsGenerating();

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG generation complete: %s"), bIsComplete ? TEXT("true") : TEXT("false"));
    return bIsComplete;
}

FString UAuracronPCGBridgeAPI::GetPCGComponentStatus(UPCGComponent* Component)
{
    if (!Component)
    {
        return TEXT("Invalid Component");
    }

    if (Component->IsGenerating())
    {
        return TEXT("Generating");
    }
    else if (Component->GetGeneratedGraphOutput().TaggedData.Num() > 0)
    {
        return TEXT("Generated");
    }
    else
    {
        return TEXT("Not Generated");
    }
}

// ========================================
// UE 5.6 Advanced PCG Features Implementation
// ========================================

bool UAuracronPCGBridgeAPI::GeneratePCGAsync(UPCGComponent* Component, const FPCGAsyncGenerationParams& Params)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGBridge, Error, TEXT("Cannot generate async on null component"));
        return false;
    }

    SCOPE_CYCLE_COUNTER(STAT_AuracronPCGAsyncProcessingTime);

    FScopeLock Lock(&AsyncTasksMutex);

    // Cancel existing task if any
    if (AsyncTasks.Contains(Component))
    {
        AsyncTasks[Component]->Cancel();
        AsyncTasks.Remove(Component);
    }

    // Create new async task
    TSharedPtr<FPCGAsyncTask> NewTask = MakeShared<FPCGAsyncTask>(Component, Params);
    AsyncTasks.Add(Component, NewTask);

    // Execute the task
    NewTask->Execute();

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Started async PCG generation for component"));
    return true;
}

bool UAuracronPCGBridgeAPI::IsAsyncGenerationComplete(UPCGComponent* Component)
{
    if (!Component)
    {
        return false;
    }

    FScopeLock Lock(&AsyncTasksMutex);

    if (AsyncTasks.Contains(Component))
    {
        return AsyncTasks[Component]->IsComplete();
    }

    return true; // No task means it's complete
}

float UAuracronPCGBridgeAPI::GetAsyncGenerationProgress(UPCGComponent* Component)
{
    if (!Component)
    {
        return 0.0f;
    }

    FScopeLock Lock(&AsyncTasksMutex);

    if (AsyncTasks.Contains(Component))
    {
        return AsyncTasks[Component]->GetProgress();
    }

    return 1.0f; // No task means it's complete
}

bool UAuracronPCGBridgeAPI::CancelAsyncGeneration(UPCGComponent* Component)
{
    if (!Component)
    {
        return false;
    }

    FScopeLock Lock(&AsyncTasksMutex);

    if (AsyncTasks.Contains(Component))
    {
        AsyncTasks[Component]->Cancel();
        AsyncTasks.Remove(Component);
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Cancelled async PCG generation"));
        return true;
    }

    return false;
}

FPCGPerformanceData UAuracronPCGBridgeAPI::GetPCGPerformanceData(UPCGComponent* Component)
{
    FPCGPerformanceData PerformanceData;

    if (!Component)
    {
        return PerformanceData;
    }

    if (PerformanceDataCache.Contains(Component))
    {
        return PerformanceDataCache[Component];
    }

    // Calculate performance data
    const FPCGDataCollection& GeneratedData = Component->GetGeneratedGraphOutput();

    for (const FPCGTaggedData& TaggedData : GeneratedData.TaggedData)
    {
        if (const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data))
        {
            PerformanceData.PointsGenerated += PointData->GetPoints().Num();
        }
    }

    // Estimate memory usage
    PerformanceData.MemoryUsage = PerformanceData.PointsGenerated * sizeof(FPCGPoint);

    // Cache the data
    PerformanceDataCache.Add(Component, PerformanceData);

    return PerformanceData;
}

bool UAuracronPCGBridgeAPI::EnablePCGProfiling(bool bEnable)
{
    bProfilingEnabled = bEnable;

    if (bEnable)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Profiling enabled"));
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG Profiling disabled"));
    }

    return true;
}

TArray<FString> UAuracronPCGBridgeAPI::GetPCGPerformanceReport()
{
    TArray<FString> Report;

    Report.Add(TEXT("=== Auracron PCG Bridge Performance Report ==="));
    Report.Add(FString::Printf(TEXT("Total Components Tracked: %d"), PerformanceDataCache.Num()));
    Report.Add(FString::Printf(TEXT("Active Async Tasks: %d"), AsyncTasks.Num()));

    int32 TotalPoints = 0;
    int32 TotalMemory = 0;

    for (const auto& Pair : PerformanceDataCache)
    {
        const FPCGPerformanceData& Data = Pair.Value;
        TotalPoints += Data.PointsGenerated;
        TotalMemory += Data.MemoryUsage;
    }

    Report.Add(FString::Printf(TEXT("Total Points Generated: %d"), TotalPoints));
    Report.Add(FString::Printf(TEXT("Total Memory Usage: %d bytes (%.2f MB)"), TotalMemory, TotalMemory / (1024.0f * 1024.0f)));

    return Report;
}

bool UAuracronPCGBridgeAPI::OptimizePCGMemoryUsage(UPCGComponent* Component)
{
    if (!Component)
    {
        return false;
    }

    SCOPE_CYCLE_COUNTER(STAT_AuracronPCGMemoryOptimizationTime);

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Optimizing PCG memory usage for component"));

    // Force garbage collection on PCG data
    Component->CleanupLocalImmediate(/*bRemoveComponents=*/false);

    // Clear performance cache for this component
    PerformanceDataCache.Remove(Component);

    return true;
}
