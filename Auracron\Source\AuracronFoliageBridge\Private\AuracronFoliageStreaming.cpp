// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Streaming Integration Implementation
// Bridge 4.10: Foliage - Streaming Integration

#include "AuracronFoliageStreaming.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronWorldPartitionStreaming.h"
#include "AuracronWorldPartitionDataLayers.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Streaming includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Foliage streaming includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"
#include "ProceduralFoliageComponent.h"

// Memory management includes
#include "HAL/PlatformMemory.h"
#include "HAL/MemoryBase.h"
#include "Containers/LockFreeList.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "Engine/StreamableManager.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Box.h"

// =============================================================================
// FOLIAGE STREAMING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageStreamingManager* UAuracronFoliageStreamingManager::Instance = nullptr;

UAuracronFoliageStreamingManager* UAuracronFoliageStreamingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageStreamingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageStreamingManager::Initialize(const FAuracronFoliageStreamingConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage Streaming Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    FoliageChunks.Empty();
    StreamingRequests.Empty();
    MemoryPools.Empty();

    // Clear queues
    while (!PendingLoadRequests.IsEmpty())
    {
        FString DummyRequest;
        PendingLoadRequests.Dequeue(DummyRequest);
    }
    while (!PendingUnloadRequests.IsEmpty())
    {
        FString DummyRequest;
        PendingUnloadRequests.Dequeue(DummyRequest);
    }
    ActiveAsyncOperations.Empty();

    // Initialize performance data
    PerformanceData = FAuracronFoliageStreamingPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastStreamingUpdate = 0.0f;
    LastMemoryUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    // Initialize memory pools if enabled
    if (Configuration.bEnableInstancePooling)
    {
        InitializeMemoryPools();
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming Manager initialized with strategy: %s, max memory: %.1f MB"), 
                              *UEnum::GetValueAsString(Configuration.StreamingStrategy),
                              Configuration.MaxFoliageMemoryMB);
}

void UAuracronFoliageStreamingManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel all pending operations
    while (!PendingLoadRequests.IsEmpty())
    {
        FString RequestId;
        PendingLoadRequests.Dequeue(RequestId);
    }
    while (!PendingUnloadRequests.IsEmpty())
    {
        FString RequestId;
        PendingUnloadRequests.Dequeue(RequestId);
    }

    // Clear all collections
    FoliageChunks.Empty();
    StreamingRequests.Empty();
    MemoryPools.Empty();
    ActiveAsyncOperations.Empty();

    // Reset references
    ManagedWorld.Reset();
    WorldPartitionManager.Reset();
    DataLayerManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming Manager shutdown completed"));
}

bool UAuracronFoliageStreamingManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageStreamingManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update streaming operations
    LastStreamingUpdate += DeltaTime;
    if (LastStreamingUpdate >= Configuration.StreamingUpdateInterval)
    {
        UpdateStreamingInternal(DeltaTime);
        LastStreamingUpdate = 0.0f;
    }

    // Process streaming requests
    ProcessStreamingRequests();

    // Update async loading operations
    if (Configuration.bEnableAsyncFoliageLoading)
    {
        UpdateAsyncLoadingOperations(DeltaTime);
    }

    // Update memory management
    LastMemoryUpdate += DeltaTime;
    if (LastMemoryUpdate >= 1.0f) // Update every second
    {
        UpdateMemoryManagement();
        LastMemoryUpdate = 0.0f;
    }

    // Update data layer based streaming
    if (Configuration.bEnableDataLayerIntegration)
    {
        UpdateDataLayerBasedStreaming();
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageStreamingManager::SetConfiguration(const FAuracronFoliageStreamingConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming configuration updated"));
}

FAuracronFoliageStreamingConfiguration UAuracronFoliageStreamingManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageStreamingManager::IntegrateWithWorldPartition(UAuracronWorldPartitionStreamingManager* InWorldPartitionManager)
{
    if (!InWorldPartitionManager)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid World Partition Manager"));
        return;
    }

    WorldPartitionManager = InWorldPartitionManager;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming integrated with World Partition"));
}

void UAuracronFoliageStreamingManager::IntegrateWithDataLayers(UAuracronDataLayerManager* InDataLayerManager)
{
    if (!InDataLayerManager)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid Data Layer Manager"));
        return;
    }

    DataLayerManager = InDataLayerManager;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming integrated with Data Layers"));
}

FString UAuracronFoliageStreamingManager::CreateFoliageChunk(const FBox& ChunkBounds, const FString& CellId, const FString& DataLayerId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    FString ChunkId = GenerateChunkId();

    FAuracronFoliageChunkData NewChunkData;
    NewChunkData.ChunkId = ChunkId;
    NewChunkData.CellId = CellId;
    NewChunkData.DataLayerId = DataLayerId;
    NewChunkData.ChunkBounds = ChunkBounds;
    NewChunkData.StreamingState = EAuracronFoliageStreamingState::Unloaded;
    NewChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
    NewChunkData.LastAccessTime = FPlatformTime::Seconds();

    FoliageChunks.Add(ChunkId, NewChunkData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk created: %s (Cell: %s, DataLayer: %s)"), 
                              *ChunkId, *CellId, *DataLayerId);

    return ChunkId;
}

void UAuracronFoliageStreamingManager::ValidateConfiguration()
{
    // Validate streaming distances
    Configuration.FoliageStreamingDistance = FMath::Max(1000.0f, Configuration.FoliageStreamingDistance);
    Configuration.FoliageUnloadingDistance = FMath::Max(Configuration.FoliageStreamingDistance + 500.0f, Configuration.FoliageUnloadingDistance);
    Configuration.FoliagePreloadDistance = FMath::Max(Configuration.FoliageUnloadingDistance + 500.0f, Configuration.FoliagePreloadDistance);

    // Validate cell streaming
    Configuration.CellStreamingBuffer = FMath::Max(100.0f, Configuration.CellStreamingBuffer);

    // Validate memory settings
    Configuration.MaxFoliageMemoryMB = FMath::Max(64.0f, Configuration.MaxFoliageMemoryMB);
    Configuration.MemoryPressureThreshold = FMath::Clamp(Configuration.MemoryPressureThreshold, 0.5f, 0.95f);
    Configuration.InstancePoolSize = FMath::Max(1000, Configuration.InstancePoolSize);

    // Validate async settings
    Configuration.MaxConcurrentFoliageLoads = FMath::Max(1, Configuration.MaxConcurrentFoliageLoads);
    Configuration.FoliageLoadingTimeSliceMs = FMath::Max(1.0f, Configuration.FoliageLoadingTimeSliceMs);
    Configuration.AsyncWorkerThreads = FMath::Max(1, Configuration.AsyncWorkerThreads);

    // Validate performance settings
    Configuration.MaxFoliageInstancesPerFrame = FMath::Max(100, Configuration.MaxFoliageInstancesPerFrame);
    Configuration.StreamingUpdateInterval = FMath::Max(0.01f, Configuration.StreamingUpdateInterval);

    // Validate caching settings
    Configuration.CacheRetentionTime = FMath::Max(5.0f, Configuration.CacheRetentionTime);
    Configuration.MaxCachedFoliageChunks = FMath::Max(1, Configuration.MaxCachedFoliageChunks);
}

FString UAuracronFoliageStreamingManager::GenerateChunkId() const
{
    return FString::Printf(TEXT("FoliageChunk_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageStreamingManager::GenerateRequestId() const
{
    return FString::Printf(TEXT("StreamingRequest_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageStreamingManager::GeneratePoolId() const
{
    return FString::Printf(TEXT("MemoryPool_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageStreamingManager::UpdateStreamingInternal(float DeltaTime)
{
    if (!ManagedWorld.IsValid())
    {
        return;
    }

    // Get streaming sources (players, cameras, etc.)
    TArray<FVector> StreamingSources;

    // Add player locations as streaming sources
    UWorld* World = ManagedWorld.Get();
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        if (APlayerController* PC = Iterator->Get())
        {
            if (APawn* Pawn = PC->GetPawn())
            {
                StreamingSources.Add(Pawn->GetActorLocation());
            }
        }
    }

    // Update distance-based streaming
    if (Configuration.StreamingStrategy == EAuracronFoliageStreamingStrategy::DistanceBased ||
        Configuration.StreamingStrategy == EAuracronFoliageStreamingStrategy::Hybrid)
    {
        UpdateDistanceBasedStreaming(StreamingSources);
    }

    // Update chunk priorities
    UpdateChunkPriorities(StreamingSources);
}

void UAuracronFoliageStreamingManager::UpdateDistanceBasedStreaming(const TArray<FVector>& StreamingSources)
{
    FScopeLock Lock(&StreamingLock);

    for (auto& ChunkPair : FoliageChunks)
    {
        FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        // Check if chunk should be loaded
        if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloaded)
        {
            if (ShouldLoadChunk(ChunkData, StreamingSources))
            {
                RequestFoliageChunkLoading(ChunkData.ChunkId, ChunkData.LoadingPriority);
            }
        }
        // Check if chunk should be unloaded
        else if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded)
        {
            if (ShouldUnloadChunk(ChunkData, StreamingSources))
            {
                RequestFoliageChunkUnloading(ChunkData.ChunkId);
            }
        }
    }
}

void UAuracronFoliageStreamingManager::UpdateChunkPriorities(const TArray<FVector>& StreamingSources)
{
    FScopeLock Lock(&StreamingLock);

    for (auto& ChunkPair : FoliageChunks)
    {
        FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        // Calculate priority based on distance to nearest streaming source
        float MinDistance = FLT_MAX;
        for (const FVector& Source : StreamingSources)
        {
            float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
            MinDistance = FMath::Min(MinDistance, Distance);
        }

        // Update loading priority based on distance
        if (MinDistance < Configuration.FoliageStreamingDistance * 0.5f)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::VeryHigh;
        }
        else if (MinDistance < Configuration.FoliageStreamingDistance * 0.75f)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::High;
        }
        else if (MinDistance < Configuration.FoliageStreamingDistance)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
        }
        else if (MinDistance < Configuration.FoliagePreloadDistance)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Low;
        }
        else
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::VeryLow;
        }
    }
}

bool UAuracronFoliageStreamingManager::ShouldLoadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const
{
    // Check distance to streaming sources
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
        if (Distance <= Configuration.FoliageStreamingDistance)
        {
            return true;
        }
    }

    return false;
}

bool UAuracronFoliageStreamingManager::ShouldUnloadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const
{
    // Check distance to streaming sources
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
        if (Distance <= Configuration.FoliageUnloadingDistance)
        {
            return false; // Don't unload if within unloading distance
        }
    }

    return true; // Unload if all sources are beyond unloading distance
}

FString UAuracronFoliageStreamingManager::RequestFoliageChunkLoading(const FString& ChunkId, EAuracronFoliageLoadingPriority Priority)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    // Check if chunk exists
    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return FString();
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Check if already loaded or loading
    if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded ||
        ChunkData.StreamingState == EAuracronFoliageStreamingState::Loading)
    {
        return FString(); // Already loaded or loading
    }

    FString RequestId = GenerateRequestId();

    FAuracronFoliageStreamingRequest NewRequest;
    NewRequest.RequestId = RequestId;
    NewRequest.ChunkId = ChunkId;
    NewRequest.bIsLoadRequest = true;
    NewRequest.Priority = Priority;
    NewRequest.RequestTime = FPlatformTime::Seconds();

    StreamingRequests.Add(RequestId, NewRequest);
    PendingLoadRequests.Enqueue(RequestId);

    // Update chunk state
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Loading;
    ChunkData.LoadingPriority = Priority;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk loading requested: %s (Priority: %s)"),
                              *ChunkId,
                              *UEnum::GetValueAsString(Priority));

    return RequestId;
}

FString UAuracronFoliageStreamingManager::RequestFoliageChunkUnloading(const FString& ChunkId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    // Check if chunk exists
    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return FString();
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Check if already unloaded or unloading
    if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloaded ||
        ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloading)
    {
        return FString(); // Already unloaded or unloading
    }

    FString RequestId = GenerateRequestId();

    FAuracronFoliageStreamingRequest NewRequest;
    NewRequest.RequestId = RequestId;
    NewRequest.ChunkId = ChunkId;
    NewRequest.bIsLoadRequest = false;
    NewRequest.Priority = EAuracronFoliageLoadingPriority::Normal;
    NewRequest.RequestTime = FPlatformTime::Seconds();

    StreamingRequests.Add(RequestId, NewRequest);
    PendingUnloadRequests.Enqueue(RequestId);

    // Update chunk state
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Unloading;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk unloading requested: %s"), *ChunkId);

    return RequestId;
}
