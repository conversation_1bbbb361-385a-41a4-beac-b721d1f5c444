// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronUIBridge.h"

#ifdef AURACRONUIBRIDGE_AuracronUIBridge_generated_h
#error "AuracronUIBridge.generated.h already included, missing '#pragma once' in AuracronUIBridge.h"
#endif
#define AURACRONUIBRIDGE_AuracronUIBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UCommonUserWidget;
enum class EAuracronInputPlatform : uint8;
enum class EAuracronUIState : uint8;
enum class EAuracronUIType : uint8;
struct FAuracronUIConfiguration;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronUIStateEntry *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_122_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronUIStateEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronUIStateEntry;
// ********** End ScriptStruct FAuracronUIStateEntry ***********************************************

// ********** Begin ScriptStruct FAuracronUIConfiguration ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_148_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronUIConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronUIConfiguration;
// ********** End ScriptStruct FAuracronUIConfiguration ********************************************

// ********** Begin ScriptStruct FAuracronMinimapConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_241_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMinimapConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMinimapConfiguration;
// ********** End ScriptStruct FAuracronMinimapConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronCombatHUDConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_318_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCombatHUDConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCombatHUDConfiguration;
// ********** End ScriptStruct FAuracronCombatHUDConfiguration *************************************

// ********** Begin ScriptStruct FAuracronCrossPlatformInputConfiguration **************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_427_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCrossPlatformInputConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCrossPlatformInputConfiguration;
// ********** End ScriptStruct FAuracronCrossPlatformInputConfiguration ****************************

// ********** Begin Delegate FOnUIShown ************************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_780_DELEGATE \
static void FOnUIShown_DelegateWrapper(const FMulticastScriptDelegate& OnUIShown, EAuracronUIType UIType);


// ********** End Delegate FOnUIShown **************************************************************

// ********** Begin Delegate FOnUIHidden ***********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_785_DELEGATE \
static void FOnUIHidden_DelegateWrapper(const FMulticastScriptDelegate& OnUIHidden, EAuracronUIType UIType);


// ********** End Delegate FOnUIHidden *************************************************************

// ********** Begin Delegate FOnInputPlatformChanged ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_790_DELEGATE \
static void FOnInputPlatformChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInputPlatformChanged, EAuracronInputPlatform OldPlatform, EAuracronInputPlatform NewPlatform);


// ********** End Delegate FOnInputPlatformChanged *************************************************

// ********** Begin Delegate FOnUIConfigurationUpdated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_795_DELEGATE \
static void FOnUIConfigurationUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnUIConfigurationUpdated, FAuracronUIConfiguration NewConfiguration);


// ********** End Delegate FOnUIConfigurationUpdated ***********************************************

// ********** Begin Delegate FOnMinimapUpdated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_800_DELEGATE \
static void FOnMinimapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMinimapUpdated);


// ********** End Delegate FOnMinimapUpdated *******************************************************

// ********** Begin Class UAuracronUIBridge ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_InputPlatform); \
	DECLARE_FUNCTION(execOnRep_UIStates); \
	DECLARE_FUNCTION(execResetToDefaultSettings); \
	DECLARE_FUNCTION(execLoadUISettings); \
	DECLARE_FUNCTION(execSaveUISettings); \
	DECLARE_FUNCTION(execApplyUIConfiguration); \
	DECLARE_FUNCTION(execToggleTouchControls); \
	DECLARE_FUNCTION(execSetupTouchControls); \
	DECLARE_FUNCTION(execProcessUIInput); \
	DECLARE_FUNCTION(execDetectInputPlatform); \
	DECLARE_FUNCTION(execSetupPlatformInput); \
	DECLARE_FUNCTION(execToggleMinimapRealm); \
	DECLARE_FUNCTION(execSetMinimapZoom); \
	DECLARE_FUNCTION(execRemoveMinimapMarker); \
	DECLARE_FUNCTION(execAddMinimapMarker); \
	DECLARE_FUNCTION(execUpdateMinimap3D); \
	DECLARE_FUNCTION(execUpdateKDA); \
	DECLARE_FUNCTION(execUpdateGold); \
	DECLARE_FUNCTION(execUpdateExperienceBar); \
	DECLARE_FUNCTION(execShowDamageIndicator); \
	DECLARE_FUNCTION(execUpdateAbilityCooldown); \
	DECLARE_FUNCTION(execUpdateManaBar); \
	DECLARE_FUNCTION(execUpdateHealthBar); \
	DECLARE_FUNCTION(execGetUIWidget); \
	DECLARE_FUNCTION(execSetUIWidget); \
	DECLARE_FUNCTION(execGetUIState); \
	DECLARE_FUNCTION(execIsUIVisible); \
	DECLARE_FUNCTION(execToggleUI); \
	DECLARE_FUNCTION(execHideUI); \
	DECLARE_FUNCTION(execShowUI);


AURACRONUIBRIDGE_API UClass* Z_Construct_UClass_UAuracronUIBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronUIBridge(); \
	friend struct Z_Construct_UClass_UAuracronUIBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONUIBRIDGE_API UClass* Z_Construct_UClass_UAuracronUIBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronUIBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronUIBridge"), Z_Construct_UClass_UAuracronUIBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronUIBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		UIConfiguration=NETFIELD_REP_START, \
		UIStates, \
		CurrentInputPlatform, \
		NETFIELD_REP_END=CurrentInputPlatform	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronUIBridge(UAuracronUIBridge&&) = delete; \
	UAuracronUIBridge(const UAuracronUIBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronUIBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronUIBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronUIBridge) \
	NO_API virtual ~UAuracronUIBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_490_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h_493_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronUIBridge;

// ********** End Class UAuracronUIBridge **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronUIBridge_Public_AuracronUIBridge_h

// ********** Begin Enum EAuracronUIType ***********************************************************
#define FOREACH_ENUM_EAURACRONUITYPE(op) \
	op(EAuracronUIType::None) \
	op(EAuracronUIType::MainMenu) \
	op(EAuracronUIType::InGameHUD) \
	op(EAuracronUIType::ChampionSelect) \
	op(EAuracronUIType::SigilosMenu) \
	op(EAuracronUIType::ProgressionMenu) \
	op(EAuracronUIType::SettingsMenu) \
	op(EAuracronUIType::ScoreBoard) \
	op(EAuracronUIType::Minimap) \
	op(EAuracronUIType::AbilityBar) \
	op(EAuracronUIType::InventoryMenu) \
	op(EAuracronUIType::ShopMenu) \
	op(EAuracronUIType::Tutorial) \
	op(EAuracronUIType::LoadingScreen) \
	op(EAuracronUIType::ErrorDialog) \
	op(EAuracronUIType::ConfirmDialog) 

enum class EAuracronUIType : uint8;
template<> struct TIsUEnumClass<EAuracronUIType> { enum { Value = true }; };
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronUIType>();
// ********** End Enum EAuracronUIType *************************************************************

// ********** Begin Enum EAuracronInputPlatform ****************************************************
#define FOREACH_ENUM_EAURACRONINPUTPLATFORM(op) \
	op(EAuracronInputPlatform::PC) \
	op(EAuracronInputPlatform::Console) \
	op(EAuracronInputPlatform::Mobile) \
	op(EAuracronInputPlatform::VR) \
	op(EAuracronInputPlatform::Auto) 

enum class EAuracronInputPlatform : uint8;
template<> struct TIsUEnumClass<EAuracronInputPlatform> { enum { Value = true }; };
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronInputPlatform>();
// ********** End Enum EAuracronInputPlatform ******************************************************

// ********** Begin Enum EAuracronUIState **********************************************************
#define FOREACH_ENUM_EAURACRONUISTATE(op) \
	op(EAuracronUIState::Hidden) \
	op(EAuracronUIState::Visible) \
	op(EAuracronUIState::Transitioning) \
	op(EAuracronUIState::Loading) \
	op(EAuracronUIState::Error) \
	op(EAuracronUIState::Disabled) 

enum class EAuracronUIState : uint8;
template<> struct TIsUEnumClass<EAuracronUIState> { enum { Value = true }; };
template<> AURACRONUIBRIDGE_API UEnum* StaticEnum<EAuracronUIState>();
// ********** End Enum EAuracronUIState ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
