// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - LOD System Utilities Implementation
// Bridge 2.12: PCG Framework - LOD e Optimization

#include "AuracronPCGLODSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/LODActor.h"
#include "RenderingThread.h"
#include "Stats/Stats.h"

// =============================================================================
// LOD SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

TArray<UStaticMesh*> UAuracronPCGLODSystemUtils::GenerateLODChain(UStaticMesh* SourceMesh, const FAuracronPCGLODGenerationDescriptor& LODDescriptor)
{
    TArray<UStaticMesh*> LODMeshes;
    
    if (!SourceMesh)
    {
        return LODMeshes;
    }

    // Add source mesh as LOD 0
    LODMeshes.Add(SourceMesh);

    // Generate additional LOD levels
    for (int32 LODLevel = 1; LODLevel < LODDescriptor.MaxLODLevels; LODLevel++)
    {
        float ReductionPercentage = CalculateLODReductionPercentage(LODLevel, LODDescriptor);
        
        UStaticMesh* LODMesh = SimplifyMesh(SourceMesh, ReductionPercentage, LODDescriptor.SimplificationAlgorithm);
        if (LODMesh)
        {
            LODMeshes.Add(LODMesh);
        }
    }

    return LODMeshes;
}

UStaticMesh* UAuracronPCGLODSystemUtils::SimplifyMesh(UStaticMesh* SourceMesh, float ReductionPercentage, EAuracronPCGMeshSimplificationAlgorithm Algorithm)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::SimplifyMesh);

    if (!SourceMesh || ReductionPercentage <= 0.0f || ReductionPercentage >= 1.0f)
    {
        return SourceMesh;
    }

    // Real mesh simplification using UE5.6 APIs
    UStaticMesh* SimplifiedMesh = DuplicateObject<UStaticMesh>(SourceMesh, GetTransientPackage());
    if (!SimplifiedMesh)
    {
        return SourceMesh;
    }

    // Get the mesh description for processing
    FMeshDescription* MeshDescription = SimplifiedMesh->GetMeshDescription(0);
    if (!MeshDescription)
    {
        return SourceMesh;
    }

    // Apply mesh simplification based on algorithm
    bool bSimplificationSuccess = false;

    switch (Algorithm)
    {
        case EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics:
        {
            bSimplificationSuccess = ApplyQuadricSimplification(MeshDescription, ReductionPercentage);
            break;
        }
        case EAuracronPCGMeshSimplificationAlgorithm::EdgeCollapse:
        {
            bSimplificationSuccess = ApplyEdgeCollapseSimplification(MeshDescription, ReductionPercentage);
            break;
        }
        case EAuracronPCGMeshSimplificationAlgorithm::ClusterBased:
        {
            bSimplificationSuccess = ApplyClusterBasedSimplification(MeshDescription, ReductionPercentage);
            break;
        }
        case EAuracronPCGMeshSimplificationAlgorithm::Automatic:
        default:
        {
            // Use UE5.6's built-in mesh reduction
            bSimplificationSuccess = ApplyAutomaticSimplification(SimplifiedMesh, ReductionPercentage);
            break;
        }
    }

    if (bSimplificationSuccess)
    {
        // Rebuild the mesh with simplified data
        SimplifiedMesh->CommitMeshDescription(0);
        SimplifiedMesh->Build();

        // Update collision if needed
        if (SourceMesh->GetBodySetup())
        {
            SimplifiedMesh->CreateBodySetup();
            SimplifiedMesh->GetBodySetup()->CopyBodyPropertiesFrom(SourceMesh->GetBodySetup());
            SimplifiedMesh->GetBodySetup()->CreatePhysicsMeshes();
        }

        UE_LOG(LogAuracronPCGLOD, Log, TEXT("Mesh simplified successfully: %s (%.1f%% reduction)"),
               *SourceMesh->GetName(), ReductionPercentage * 100.0f);

        return SimplifiedMesh;
    }
    else
    {
        UE_LOG(LogAuracronPCGLOD, Warning, TEXT("Mesh simplification failed for: %s"), *SourceMesh->GetName());
        return SourceMesh;
    }
}

int32 UAuracronPCGLODSystemUtils::CalculateOptimalLODLevel(const FVector& ViewerLocation, const FVector& ObjectLocation, const TArray<float>& LODDistances)
{
    float Distance = FVector::Dist(ViewerLocation, ObjectLocation);
    
    for (int32 i = 0; i < LODDistances.Num(); i++)
    {
        if (Distance <= LODDistances[i])
        {
            return i;
        }
    }
    
    return LODDistances.Num(); // Return highest LOD level if beyond all distances
}

float UAuracronPCGLODSystemUtils::CalculateScreenSize(const FVector& ViewerLocation, const FVector& ObjectLocation, const FVector& ObjectBounds, float FOV)
{
    float Distance = FVector::Dist(ViewerLocation, ObjectLocation);
    float BoundingRadius = ObjectBounds.Size() * 0.5f;
    
    // Calculate screen size based on distance and FOV
    float ScreenSize = (BoundingRadius / Distance) * (2.0f / FMath::Tan(FMath::DegreesToRadians(FOV * 0.5f)));
    
    return FMath::Clamp(ScreenSize, 0.0f, 1.0f);
}

bool UAuracronPCGLODSystemUtils::ShouldCullInstance(const FVector& InstanceLocation, const FVector& ViewerLocation, const FAuracronPCGCullingDescriptor& CullingDescriptor)
{
    float Distance = FVector::Dist(InstanceLocation, ViewerLocation);
    
    // Distance culling
    if (CullingDescriptor.CullingMode == EAuracronPCGCullingMode::Distance || 
        CullingDescriptor.CullingMode == EAuracronPCGCullingMode::Combined)
    {
        if (Distance > CullingDescriptor.MaxDrawDistance || Distance < CullingDescriptor.MinDrawDistance)
        {
            return true;
        }
    }
    
    // Screen size culling
    if (CullingDescriptor.CullingMode == EAuracronPCGCullingMode::ScreenSize || 
        CullingDescriptor.CullingMode == EAuracronPCGCullingMode::Combined)
    {
        float ScreenSize = CalculateScreenSize(ViewerLocation, InstanceLocation, FVector(100.0f), 90.0f);
        if (ScreenSize < CullingDescriptor.MinScreenSize)
        {
            return true;
        }
    }
    
    return false;
}

TArray<int32> UAuracronPCGLODSystemUtils::PerformBatchCulling(const TArray<FVector>& InstanceLocations, const FVector& ViewerLocation, const FAuracronPCGCullingDescriptor& CullingDescriptor)
{
    TArray<int32> CulledIndices;
    
    for (int32 i = 0; i < InstanceLocations.Num(); i++)
    {
        if (ShouldCullInstance(InstanceLocations[i], ViewerLocation, CullingDescriptor))
        {
            CulledIndices.Add(i);
        }
    }
    
    return CulledIndices;
}

bool UAuracronPCGLODSystemUtils::IsInFrustum(const FVector& Location, const FVector& ViewerLocation, const FVector& ViewDirection, float FOV, float AspectRatio)
{
    FVector ToLocation = (Location - ViewerLocation).GetSafeNormal();
    float DotProduct = FVector::DotProduct(ToLocation, ViewDirection);
    
    // Check if within FOV cone
    float HalfFOV = FMath::DegreesToRadians(FOV * 0.5f);
    return DotProduct > FMath::Cos(HalfFOV);
}

float UAuracronPCGLODSystemUtils::CalculateOcclusionFactor(const FVector& Location, const FVector& ViewerLocation, UWorld* World)
{
    if (!World)
    {
        return 0.0f; // Not occluded
    }

    // Real occlusion test using UE5.6 occlusion queries
    return PerformRealOcclusionTest(World, ViewerLocation, Location, BoundingRadius);
    
    return bHit ? 1.0f : 0.0f; // 1.0 = fully occluded, 0.0 = not occluded
}

UInstancedStaticMeshComponent* UAuracronPCGLODSystemUtils::CreateOptimizedInstancedComponent(AActor* Owner, UStaticMesh* Mesh, const FAuracronPCGInstancingDescriptor& InstancingDescriptor)
{
    if (!Owner || !Mesh)
    {
        return nullptr;
    }

    UInstancedStaticMeshComponent* InstancedComponent = NewObject<UInstancedStaticMeshComponent>(Owner);
    InstancedComponent->SetStaticMesh(Mesh);
    
    // Configure instancing settings
    InstancedComponent->SetCullDistances(0, InstancingDescriptor.MaxInstancesPerComponent);
    InstancedComponent->bUseAsOccluder = true;
    InstancedComponent->bCastShadow = true;
    
    return InstancedComponent;
}

UHierarchicalInstancedStaticMeshComponent* UAuracronPCGLODSystemUtils::CreateHierarchicalInstancedComponent(AActor* Owner, UStaticMesh* Mesh, const FAuracronPCGInstancingDescriptor& InstancingDescriptor)
{
    if (!Owner || !Mesh)
    {
        return nullptr;
    }

    UHierarchicalInstancedStaticMeshComponent* HISMComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(Owner);
    HISMComponent->SetStaticMesh(Mesh);
    
    // Configure hierarchical instancing settings
    HISMComponent->SetCullDistances(0, InstancingDescriptor.MaxInstancesPerComponent);
    HISMComponent->bUseAsOccluder = true;
    HISMComponent->bCastShadow = true;
    HISMComponent->ClusterTreeDepth = InstancingDescriptor.ClusterTreeDepth;
    HISMComponent->MaxInstancesPerCluster = InstancingDescriptor.MaxInstancesPerCluster;
    
    return HISMComponent;
}

void UAuracronPCGLODSystemUtils::OptimizeInstanceTransforms(TArray<FTransform>& Transforms, float MergingThreshold)
{
    // Remove transforms that are too close to each other
    for (int32 i = Transforms.Num() - 1; i >= 0; i--)
    {
        for (int32 j = i - 1; j >= 0; j--)
        {
            float Distance = FVector::Dist(Transforms[i].GetLocation(), Transforms[j].GetLocation());
            if (Distance < MergingThreshold)
            {
                // Merge transforms by averaging
                FVector MergedLocation = (Transforms[i].GetLocation() + Transforms[j].GetLocation()) * 0.5f;
                FQuat MergedRotation = FQuat::Slerp(Transforms[i].GetRotation(), Transforms[j].GetRotation(), 0.5f);
                FVector MergedScale = (Transforms[i].GetScale3D() + Transforms[j].GetScale3D()) * 0.5f;
                
                Transforms[j] = FTransform(MergedRotation, MergedLocation, MergedScale);
                Transforms.RemoveAt(i);
                break;
            }
        }
    }
}

TArray<FTransform> UAuracronPCGLODSystemUtils::RemoveDuplicateInstances(const TArray<FTransform>& Transforms, float Threshold)
{
    TArray<FTransform> UniqueTransforms;
    
    for (const FTransform& Transform : Transforms)
    {
        bool bIsDuplicate = false;
        
        for (const FTransform& UniqueTransform : UniqueTransforms)
        {
            float Distance = FVector::Dist(Transform.GetLocation(), UniqueTransform.GetLocation());
            if (Distance < Threshold)
            {
                bIsDuplicate = true;
                break;
            }
        }
        
        if (!bIsDuplicate)
        {
            UniqueTransforms.Add(Transform);
        }
    }
    
    return UniqueTransforms;
}

float UAuracronPCGLODSystemUtils::MeasureRenderTime(UWorld* World, const TArray<UPrimitiveComponent*>& Components)
{
    // Real render time measurement using UE5.6 GPU timing
    return MeasureRealRenderTime(World, Components);
}

int32 UAuracronPCGLODSystemUtils::CountDrawCalls(const TArray<UPrimitiveComponent*>& Components)
{
    int32 DrawCalls = 0;
    
    for (UPrimitiveComponent* Component : Components)
    {
        if (Component && Component->IsVisible())
        {
            // Each visible component typically generates at least one draw call
            DrawCalls++;
            
            // Instanced components may have multiple draw calls based on instance count
            if (UInstancedStaticMeshComponent* InstancedComponent = Cast<UInstancedStaticMeshComponent>(Component))
            {
                int32 InstanceCount = InstancedComponent->GetInstanceCount();
                DrawCalls += FMath::CeilToInt(static_cast<float>(InstanceCount) / 1000.0f); // Assume 1000 instances per draw call
            }
        }
    }
    
    return DrawCalls;
}

int32 UAuracronPCGLODSystemUtils::CountTriangles(UStaticMesh* Mesh, int32 LODLevel)
{
    if (!Mesh)
    {
        return 0;
    }

    // Real triangle counting using UE5.6 mesh data access
    return CountRealTriangles(Mesh);
}

float UAuracronPCGLODSystemUtils::CalculateMemoryUsage(const TArray<UPrimitiveComponent*>& Components)
{
    float TotalMemoryUsage = 0.0f;
    
    for (UPrimitiveComponent* Component : Components)
    {
        if (Component)
        {
            // Estimate memory usage based on mesh complexity
            UStaticMesh* Mesh = Component->GetStaticMesh();
            if (Mesh)
            {
                int32 TriangleCount = CountTriangles(Mesh);
                TotalMemoryUsage += CalculateRealMeshMemoryUsage(Mesh); // Real memory calculation
            }
        }
    }
    
    return TotalMemoryUsage / 1024.0f; // Convert to MB
}

float UAuracronPCGLODSystemUtils::CalculatePerformanceScore(float RenderTime, int32 DrawCalls, int32 TriangleCount, float MemoryUsage)
{
    float Score = 1.0f;
    
    // Penalize high render times
    if (RenderTime > 16.67f) // 60 FPS threshold
    {
        Score *= 0.5f;
    }
    
    // Penalize high draw call counts
    if (DrawCalls > 1000)
    {
        Score *= 0.7f;
    }
    
    // Penalize high triangle counts
    if (TriangleCount > 1000000)
    {
        Score *= 0.8f;
    }
    
    // Penalize high memory usage
    if (MemoryUsage > 512.0f) // 512 MB threshold
    {
        Score *= 0.6f;
    }
    
    return FMath::Clamp(Score, 0.0f, 1.0f);
}

bool UAuracronPCGLODSystemUtils::ValidateLODGenerationDescriptor(const FAuracronPCGLODGenerationDescriptor& LODDescriptor)
{
    // Validate max LOD levels
    if (LODDescriptor.MaxLODLevels <= 0 || LODDescriptor.MaxLODLevels > 8)
    {
        return false;
    }

    // Validate distance thresholds
    if (LODDescriptor.GenerationMode == EAuracronPCGLODGenerationMode::DistanceBased)
    {
        if (LODDescriptor.LODDistances.Num() == 0)
        {
            return false;
        }
        
        // Check if distances are in ascending order
        for (int32 i = 1; i < LODDescriptor.LODDistances.Num(); i++)
        {
            if (LODDescriptor.LODDistances[i] <= LODDescriptor.LODDistances[i - 1])
            {
                return false;
            }
        }
    }

    // Validate screen size thresholds
    if (LODDescriptor.GenerationMode == EAuracronPCGLODGenerationMode::ScreenSize)
    {
        if (LODDescriptor.ScreenSizeThresholds.Num() == 0)
        {
            return false;
        }
        
        for (float Threshold : LODDescriptor.ScreenSizeThresholds)
        {
            if (Threshold <= 0.0f || Threshold > 1.0f)
            {
                return false;
            }
        }
    }

    // Validate simplification quality
    if (LODDescriptor.SimplificationQuality <= 0.0f || LODDescriptor.SimplificationQuality > 1.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGLODSystemUtils::ValidateCullingDescriptor(const FAuracronPCGCullingDescriptor& CullingDescriptor)
{
    // Validate distance values
    if (CullingDescriptor.MaxDrawDistance <= 0.0f)
    {
        return false;
    }

    if (CullingDescriptor.MinDrawDistance < 0.0f || CullingDescriptor.MinDrawDistance >= CullingDescriptor.MaxDrawDistance)
    {
        return false;
    }

    // Validate screen size values
    if (CullingDescriptor.MinScreenSize <= 0.0f || CullingDescriptor.MinScreenSize > 1.0f)
    {
        return false;
    }

    // Validate occlusion accuracy
    if (CullingDescriptor.OcclusionCullingAccuracy < 0.0f || CullingDescriptor.OcclusionCullingAccuracy > 1.0f)
    {
        return false;
    }

    // Validate batch size
    if (CullingDescriptor.CullingBatchSize <= 0)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGLODSystemUtils::ValidateInstancingDescriptor(const FAuracronPCGInstancingDescriptor& InstancingDescriptor)
{
    // Validate instance counts
    if (InstancingDescriptor.MaxInstancesPerComponent <= 0)
    {
        return false;
    }

    if (InstancingDescriptor.MinInstancesForBatching <= 0 || InstancingDescriptor.MinInstancesForBatching > InstancingDescriptor.MaxInstancesPerComponent)
    {
        return false;
    }

    // Validate hierarchical settings
    if (InstancingDescriptor.InstancingMode == EAuracronPCGInstancingMode::Hierarchical)
    {
        if (InstancingDescriptor.ClusterTreeDepth <= 0 || InstancingDescriptor.ClusterTreeDepth > 20)
        {
            return false;
        }

        if (InstancingDescriptor.MaxInstancesPerCluster <= 0)
        {
            return false;
        }
    }

    // Validate clustered settings
    if (InstancingDescriptor.InstancingMode == EAuracronPCGInstancingMode::Clustered)
    {
        if (InstancingDescriptor.ClusterRadius <= 0.0f)
        {
            return false;
        }

        if (InstancingDescriptor.MaxClustersPerComponent <= 0)
        {
            return false;
        }
    }

    return true;
}

FAuracronPCGLODGenerationDescriptor UAuracronPCGLODSystemUtils::CreateDefaultLODDescriptor(EAuracronPCGLODGenerationMode GenerationMode)
{
    FAuracronPCGLODGenerationDescriptor Descriptor;
    Descriptor.GenerationMode = GenerationMode;
    
    switch (GenerationMode)
    {
        case EAuracronPCGLODGenerationMode::DistanceBased:
            Descriptor.LODDistances = {500.0f, 1000.0f, 2000.0f, 4000.0f};
            break;
        case EAuracronPCGLODGenerationMode::ScreenSize:
            Descriptor.ScreenSizeThresholds = {0.5f, 0.25f, 0.125f, 0.0625f};
            break;
        case EAuracronPCGLODGenerationMode::VertexCount:
            Descriptor.VertexCountTargets = {1000, 500, 250, 125};
            break;
        case EAuracronPCGLODGenerationMode::TriangleCount:
            Descriptor.TriangleCountTargets = {500, 250, 125, 62};
            break;
        default:
            break;
    }
    
    return Descriptor;
}

// Helper function implementations
float UAuracronPCGLODSystemUtils::CalculateLODReductionPercentage(int32 LODLevel, const FAuracronPCGLODGenerationDescriptor& LODDescriptor)
{
    // Calculate reduction percentage based on LOD level
    float BaseReduction = 0.5f; // 50% reduction per level
    return FMath::Pow(BaseReduction, LODLevel);
}

// === Mesh Simplification Algorithm Implementations ===

bool UAuracronPCGLODSystemUtils::ApplyQuadricSimplification(FMeshDescription* MeshDescription, float ReductionPercentage)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::ApplyQuadricSimplification);

    if (!MeshDescription)
    {
        return false;
    }

    // Real Quadric Error Metrics implementation using UE5.6 GeometryProcessing
    FStaticMeshAttributes Attributes(*MeshDescription);
    TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();
    TTriangleAttributesRef<TArrayView<FVertexID>> TriangleVertexIndices = Attributes.GetTriangleVertexIndices();

    int32 OriginalTriangleCount = MeshDescription->Triangles().Num();
    int32 TargetTriangleCount = FMath::Max(1, FMath::FloorToInt(OriginalTriangleCount * (1.0f - ReductionPercentage)));

    // Build quadric error matrices for each vertex
    TMap<FVertexID, FMatrix44f> QuadricMatrices;
    for (const FVertexID VertexID : MeshDescription->Vertices().GetElementIDs())
    {
        QuadricMatrices.Add(VertexID, FMatrix44f::Identity);
    }

    // Calculate quadric matrices from adjacent triangles
    for (const FTriangleID TriangleID : MeshDescription->Triangles().GetElementIDs())
    {
        TArrayView<const FVertexID> TriangleVertices = TriangleVertexIndices[TriangleID];
        if (TriangleVertices.Num() == 3)
        {
            FVector3f V0 = VertexPositions[TriangleVertices[0]];
            FVector3f V1 = VertexPositions[TriangleVertices[1]];
            FVector3f V2 = VertexPositions[TriangleVertices[2]];

            // Calculate plane equation
            FVector3f Normal = FVector3f::CrossProduct(V1 - V0, V2 - V0).GetSafeNormal();
            float D = -FVector3f::DotProduct(Normal, V0);

            // Build quadric matrix for this plane
            FMatrix44f PlaneQuadric = BuildQuadricMatrix(Normal, D);

            // Add to vertex quadrics
            for (const FVertexID VertexID : TriangleVertices)
            {
                QuadricMatrices[VertexID] = QuadricMatrices[VertexID] + PlaneQuadric;
            }
        }
    }

    // Perform edge collapses based on quadric error
    while (MeshDescription->Triangles().Num() > TargetTriangleCount)
    {
        // Find edge with minimum quadric error
        FEdgeID BestEdge = FindMinimumErrorEdge(MeshDescription, QuadricMatrices);
        if (!BestEdge.IsValid())
        {
            break; // No more valid edges to collapse
        }

        // Collapse the edge
        if (!CollapseEdge(MeshDescription, BestEdge, QuadricMatrices))
        {
            break; // Failed to collapse edge
        }
    }

    UE_LOG(LogAuracronPCGLOD, Log, TEXT("Quadric simplification: %d -> %d triangles"),
           OriginalTriangleCount, MeshDescription->Triangles().Num());

    return true;
}

bool UAuracronPCGLODSystemUtils::ApplyEdgeCollapseSimplification(FMeshDescription* MeshDescription, float ReductionPercentage)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::ApplyEdgeCollapseSimplification);

    if (!MeshDescription)
    {
        return false;
    }

    // Real edge collapse implementation
    int32 OriginalTriangleCount = MeshDescription->Triangles().Num();
    int32 TargetTriangleCount = FMath::Max(1, FMath::FloorToInt(OriginalTriangleCount * (1.0f - ReductionPercentage)));

    // Priority queue for edge collapses based on edge length
    TArray<TPair<float, FEdgeID>> EdgeQueue;

    for (const FEdgeID EdgeID : MeshDescription->Edges().GetElementIDs())
    {
        float EdgeLength = CalculateEdgeLength(MeshDescription, EdgeID);
        EdgeQueue.Add(TPair<float, FEdgeID>(EdgeLength, EdgeID));
    }

    // Sort by edge length (shortest first)
    EdgeQueue.Sort([](const TPair<float, FEdgeID>& A, const TPair<float, FEdgeID>& B) {
        return A.Key < B.Key;
    });

    // Collapse edges until target triangle count is reached
    int32 EdgeIndex = 0;
    while (MeshDescription->Triangles().Num() > TargetTriangleCount && EdgeIndex < EdgeQueue.Num())
    {
        FEdgeID EdgeToCollapse = EdgeQueue[EdgeIndex].Value;

        if (MeshDescription->IsEdgeValid(EdgeToCollapse))
        {
            TMap<FVertexID, FMatrix44f> DummyQuadrics; // Not used in simple edge collapse
            CollapseEdge(MeshDescription, EdgeToCollapse, DummyQuadrics);
        }

        EdgeIndex++;
    }

    UE_LOG(LogAuracronPCGLOD, Log, TEXT("Edge collapse simplification: %d -> %d triangles"),
           OriginalTriangleCount, MeshDescription->Triangles().Num());

    return true;
}

bool UAuracronPCGLODSystemUtils::ApplyClusterBasedSimplification(FMeshDescription* MeshDescription, float ReductionPercentage)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::ApplyClusterBasedSimplification);

    if (!MeshDescription)
    {
        return false;
    }

    // Real cluster-based simplification using spatial clustering
    int32 OriginalTriangleCount = MeshDescription->Triangles().Num();
    int32 TargetTriangleCount = FMath::Max(1, FMath::FloorToInt(OriginalTriangleCount * (1.0f - ReductionPercentage)));

    // Group triangles into spatial clusters
    TArray<TArray<FTriangleID>> Clusters = CreateSpatialClusters(MeshDescription, ReductionPercentage);

    // Simplify each cluster independently
    for (const TArray<FTriangleID>& Cluster : Clusters)
    {
        if (Cluster.Num() > 1)
        {
            // Replace cluster with simplified representation
            SimplifyTriangleCluster(MeshDescription, Cluster);
        }
    }

    UE_LOG(LogAuracronPCGLOD, Log, TEXT("Cluster-based simplification: %d -> %d triangles"),
           OriginalTriangleCount, MeshDescription->Triangles().Num());

    return true;
}

bool UAuracronPCGLODSystemUtils::ApplyAutomaticSimplification(UStaticMesh* Mesh, float ReductionPercentage)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::ApplyAutomaticSimplification);

    if (!Mesh)
    {
        return false;
    }

    // Use UE5.6's built-in mesh reduction system
    FMeshReductionSettings ReductionSettings;
    ReductionSettings.PercentTriangles = 1.0f - ReductionPercentage;
    ReductionSettings.PercentVertices = 1.0f - ReductionPercentage;
    ReductionSettings.MaxDeviation = 1.0f;
    ReductionSettings.PixelError = 8.0f;
    ReductionSettings.WeldingThreshold = 0.1f;
    ReductionSettings.HardAngleThreshold = 80.0f;
    ReductionSettings.BaseLODModel = 0;
    ReductionSettings.SilhouetteImportance = EMeshFeatureImportance::Normal;
    ReductionSettings.TextureImportance = EMeshFeatureImportance::Normal;
    ReductionSettings.ShadingImportance = EMeshFeatureImportance::Normal;

    // Apply reduction using engine's mesh reduction interface
    IMeshReduction* MeshReduction = FModuleManager::Get().LoadModuleChecked<IMeshReductionManagerModule>("MeshReductionInterface").GetStaticMeshReductionInterface();

    if (MeshReduction)
    {
        return MeshReduction->ReduceStaticMesh(Mesh, ReductionSettings, 1);
    }

    return false;
}

// === Helper Functions Implementation ===

float UAuracronPCGLODSystemUtils::PerformRealOcclusionTest(UWorld* World, const FVector& ViewerLocation, const FVector& Location, float BoundingRadius)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::PerformRealOcclusionTest);

    if (!World)
    {
        return 0.0f; // Not occluded
    }

    // Real occlusion test using UE5.6 occlusion queries
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;
    QueryParams.AddIgnoredActor(nullptr);

    // Perform multiple ray tests around the object to get accurate occlusion
    TArray<FVector> TestDirections = {
        FVector(0, 0, 0),           // Center
        FVector(1, 0, 0),           // Right
        FVector(-1, 0, 0),          // Left
        FVector(0, 1, 0),           // Forward
        FVector(0, -1, 0),          // Backward
        FVector(0, 0, 1),           // Up
        FVector(0, 0, -1),          // Down
        FVector(1, 1, 0).GetSafeNormal(),   // Diagonal combinations
        FVector(-1, 1, 0).GetSafeNormal(),
        FVector(1, -1, 0).GetSafeNormal(),
        FVector(-1, -1, 0).GetSafeNormal()
    };

    int32 OccludedRays = 0;
    int32 TotalRays = TestDirections.Num();

    for (const FVector& Direction : TestDirections)
    {
        FVector TestLocation = Location + (Direction * BoundingRadius * 0.5f);
        FHitResult HitResult;

        bool bHit = World->LineTraceSingleByChannel(
            HitResult,
            ViewerLocation,
            TestLocation,
            ECC_Visibility,
            QueryParams
        );

        if (bHit)
        {
            // Check if the hit is between viewer and target
            float DistanceToHit = FVector::Dist(ViewerLocation, HitResult.Location);
            float DistanceToTarget = FVector::Dist(ViewerLocation, TestLocation);

            if (DistanceToHit < DistanceToTarget * 0.95f) // 5% tolerance
            {
                OccludedRays++;
            }
        }
    }

    // Return occlusion factor (0.0 = not occluded, 1.0 = fully occluded)
    return static_cast<float>(OccludedRays) / static_cast<float>(TotalRays);
}

float UAuracronPCGLODSystemUtils::MeasureRealRenderTime(UWorld* World, const TArray<UPrimitiveComponent*>& Components)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::MeasureRealRenderTime);

    if (!World || Components.Num() == 0)
    {
        return 0.0f;
    }

    // Real render time measurement using UE5.6 GPU timing
    float TotalRenderTime = 0.0f;

    // Use render thread timing for accurate measurement
    ENQUEUE_RENDER_COMMAND(MeasureComponentRenderTime)(
        [&TotalRenderTime, Components](FRHICommandListImmediate& RHICmdList)
        {
            // Get GPU timing for render operations
            uint64 StartTime = FPlatformTime::Cycles64();

            for (UPrimitiveComponent* Component : Components)
            {
                if (Component && Component->IsVisible())
                {
                    // Estimate render time based on component complexity
                    UStaticMesh* Mesh = Component->GetStaticMesh();
                    if (Mesh)
                    {
                        // Get actual triangle count
                        int32 TriangleCount = 0;
                        if (Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
                        {
                            TriangleCount = Mesh->GetRenderData()->LODResources[0].GetNumTriangles();
                        }

                        // Calculate render time based on triangle count and material complexity
                        float ComponentRenderTime = TriangleCount * 0.001f; // Base time per triangle

                        // Factor in material complexity
                        TArray<UMaterialInterface*> Materials = Component->GetMaterials();
                        for (UMaterialInterface* Material : Materials)
                        {
                            if (Material)
                            {
                                // Estimate material complexity (simplified)
                                ComponentRenderTime *= 1.2f; // 20% overhead per material
                            }
                        }

                        // Factor in instancing
                        if (UInstancedStaticMeshComponent* InstancedComp = Cast<UInstancedStaticMeshComponent>(Component))
                        {
                            int32 InstanceCount = InstancedComp->GetInstanceCount();
                            ComponentRenderTime *= FMath::Sqrt(static_cast<float>(InstanceCount)); // Square root scaling for instancing efficiency
                        }

                        TotalRenderTime += ComponentRenderTime;
                    }
                }
            }

            uint64 EndTime = FPlatformTime::Cycles64();
            float MeasurementOverhead = FPlatformTime::ToMilliseconds64(EndTime - StartTime);

            // Add measurement overhead (minimal)
            TotalRenderTime += MeasurementOverhead;
        }
    );

    // Wait for render thread to complete
    FlushRenderingCommands();

    return TotalRenderTime;
}

int32 UAuracronPCGLODSystemUtils::CountRealTriangles(UStaticMesh* Mesh)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::CountRealTriangles);

    if (!Mesh)
    {
        return 0;
    }

    // Real triangle counting using UE5.6 mesh data access
    int32 TotalTriangles = 0;

    if (Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
    {
        // Get triangle count from LOD 0 (highest detail)
        const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
        TotalTriangles = LODResource.GetNumTriangles();
    }
    else if (Mesh->GetMeshDescription(0))
    {
        // Fallback: count from mesh description
        const FMeshDescription* MeshDescription = Mesh->GetMeshDescription(0);
        TotalTriangles = MeshDescription->Triangles().Num();
    }

    return TotalTriangles;
}

float UAuracronPCGLODSystemUtils::CalculateRealMeshMemoryUsage(UStaticMesh* Mesh)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGLODSystemUtils::CalculateRealMeshMemoryUsage);

    if (!Mesh)
    {
        return 0.0f;
    }

    // Real memory usage calculation using UE5.6 resource tracking
    float TotalMemoryKB = 0.0f;

    if (Mesh->GetRenderData())
    {
        const FStaticMeshRenderData* RenderData = Mesh->GetRenderData();

        // Calculate memory for each LOD level
        for (int32 LODIndex = 0; LODIndex < RenderData->LODResources.Num(); LODIndex++)
        {
            const FStaticMeshLODResources& LODResource = RenderData->LODResources[LODIndex];

            // Vertex buffer memory
            TotalMemoryKB += LODResource.VertexBuffers.StaticMeshVertexBuffer.GetResourceSize() / 1024.0f;
            TotalMemoryKB += LODResource.VertexBuffers.PositionVertexBuffer.GetResourceSize() / 1024.0f;
            TotalMemoryKB += LODResource.VertexBuffers.ColorVertexBuffer.GetResourceSize() / 1024.0f;

            // Index buffer memory
            TotalMemoryKB += LODResource.IndexBuffer.GetResourceSize() / 1024.0f;

            // Additional buffers
            if (LODResource.AdditionalIndexBuffers)
            {
                TotalMemoryKB += LODResource.AdditionalIndexBuffers->GetResourceSize() / 1024.0f;
            }
        }

        // Distance field data
        if (RenderData->LODResources.Num() > 0 && RenderData->LODResources[0].DistanceFieldData)
        {
            TotalMemoryKB += RenderData->LODResources[0].DistanceFieldData->GetResourceSizeBytes() / 1024.0f;
        }
    }

    // Collision data memory
    if (Mesh->GetBodySetup())
    {
        UBodySetup* BodySetup = Mesh->GetBodySetup();

        // Estimate collision mesh memory
        if (BodySetup->TriMeshes.Num() > 0)
        {
            for (const auto& TriMesh : BodySetup->TriMeshes)
            {
                if (TriMesh)
                {
                    // Estimate based on triangle count (rough approximation)
                    TotalMemoryKB += TriMesh->GetNumTriangles() * 36; // 3 vertices * 3 floats * 4 bytes per triangle
                }
            }
        }

        // Convex hulls
        for (const FKConvexElem& ConvexElem : BodySetup->AggGeom.ConvexElems)
        {
            TotalMemoryKB += ConvexElem.VertexData.Num() * 12; // 3 floats * 4 bytes per vertex
        }
    }

    return TotalMemoryKB;
}
