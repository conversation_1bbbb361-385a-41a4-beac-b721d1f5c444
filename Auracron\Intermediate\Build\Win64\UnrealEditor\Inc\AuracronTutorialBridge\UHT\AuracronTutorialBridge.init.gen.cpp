// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronTutorialBridge_init() {}
	AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature();
	AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature();
	AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature();
	AURACRONTUTORIALBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronTutorialBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronTutorialBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronTutorialBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronTutorialBridge_OnTutorialStepCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronTutorialSaveGame_OnAllProgressReset__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronTutorialBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x16BAED3F,
				0x173F3035,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronTutorialBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronTutorialBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronTutorialBridge(Z_Construct_UPackage__Script_AuracronTutorialBridge, TEXT("/Script/AuracronTutorialBridge"), Z_Registration_Info_UPackage__Script_AuracronTutorialBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x16BAED3F, 0x173F3035));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
