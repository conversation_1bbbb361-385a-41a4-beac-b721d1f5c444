// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGElementBase.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGElementBase_generated_h
#error "AuracronPCGElementBase.generated.h already included, missing '#pragma once' in AuracronPCGElementBase.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGElementBase_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronPCGElementResult *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_27_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGElementResult;
// ********** End ScriptStruct FAuracronPCGElementResult *******************************************

// ********** Begin ScriptStruct FAuracronPCGElementParams *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_61_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGElementParams;
// ********** End ScriptStruct FAuracronPCGElementParams *******************************************

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_144_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSettingsBase(); \
	friend struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSettingsBase, UPCGSettings, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSettingsBase)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_144_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSettingsBase(UAuracronPCGSettingsBase&&) = delete; \
	UAuracronPCGSettingsBase(const UAuracronPCGSettingsBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSettingsBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSettingsBase); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGSettingsBase) \
	NO_API virtual ~UAuracronPCGSettingsBase();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_141_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_144_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_144_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h_144_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSettingsBase;

// ********** End Class UAuracronPCGSettingsBase ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
