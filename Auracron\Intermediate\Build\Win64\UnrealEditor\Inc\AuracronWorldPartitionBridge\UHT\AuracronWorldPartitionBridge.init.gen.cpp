// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionBridge_init() {}
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionCellLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionCellUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionDataLayerChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionHLODGenerated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionMinimapGenerated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionStreamingCompleted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature();
	AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronWorldPartitionBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronWorldPartitionBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionCellLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionCellUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionDataLayerChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionHLODGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionMinimapGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronWorldPartitionBridge_OnWorldPartitionStreamingCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerTransitionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronDataLayerManager_OnDataLayerVisibilityChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionLODChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionCollisionManager_OnCollisionUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnActorAddedToCell__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellMerged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionGridManager_OnCellSubdivided__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnHeightmapUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeLODChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLandscapeManager_OnLandscapeUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingDeactivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingLODChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLightingManager_OnLightingUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnCellUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionManager_OnStreamingStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnCellStreamingStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionStreamingManager_OnMemoryPressureChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronWorldPartitionTestManager_OnTestSuiteCompleted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronWorldPartitionBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xA0000AFA,
				0x73D4EDB8,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronWorldPartitionBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronWorldPartitionBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronWorldPartitionBridge(Z_Construct_UPackage__Script_AuracronWorldPartitionBridge, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Registration_Info_UPackage__Script_AuracronWorldPartitionBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xA0000AFA, 0x73D4EDB8));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
