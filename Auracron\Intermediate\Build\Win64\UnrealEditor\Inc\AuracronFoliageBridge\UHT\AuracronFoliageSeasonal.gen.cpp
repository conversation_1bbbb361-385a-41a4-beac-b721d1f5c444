// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageSeasonal.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageSeasonal() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGrowthSimulationData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLifecycleData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalColorData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveLinearColor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollectionInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronSeasonType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSeasonType;
static UEnum* EAuracronSeasonType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSeasonType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronSeasonType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonType>()
{
	return EAuracronSeasonType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Autumn.DisplayName", "Autumn" },
		{ "Autumn.Name", "EAuracronSeasonType::Autumn" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Season types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronSeasonType::Custom" },
		{ "DrySeason.DisplayName", "Dry Season" },
		{ "DrySeason.Name", "EAuracronSeasonType::DrySeason" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
		{ "Spring.DisplayName", "Spring" },
		{ "Spring.Name", "EAuracronSeasonType::Spring" },
		{ "Summer.DisplayName", "Summer" },
		{ "Summer.Name", "EAuracronSeasonType::Summer" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Season types" },
#endif
		{ "WetSeason.DisplayName", "Wet Season" },
		{ "WetSeason.Name", "EAuracronSeasonType::WetSeason" },
		{ "Winter.DisplayName", "Winter" },
		{ "Winter.Name", "EAuracronSeasonType::Winter" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSeasonType::Spring", (int64)EAuracronSeasonType::Spring },
		{ "EAuracronSeasonType::Summer", (int64)EAuracronSeasonType::Summer },
		{ "EAuracronSeasonType::Autumn", (int64)EAuracronSeasonType::Autumn },
		{ "EAuracronSeasonType::Winter", (int64)EAuracronSeasonType::Winter },
		{ "EAuracronSeasonType::WetSeason", (int64)EAuracronSeasonType::WetSeason },
		{ "EAuracronSeasonType::DrySeason", (int64)EAuracronSeasonType::DrySeason },
		{ "EAuracronSeasonType::Custom", (int64)EAuracronSeasonType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronSeasonType",
	"EAuracronSeasonType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSeasonType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonType.InnerSingleton;
}
// ********** End Enum EAuracronSeasonType *********************************************************

// ********** Begin Enum EAuracronGrowthPhase ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGrowthPhase;
static UEnum* EAuracronGrowthPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGrowthPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGrowthPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronGrowthPhase"));
	}
	return Z_Registration_Info_UEnum_EAuracronGrowthPhase.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronGrowthPhase>()
{
	return EAuracronGrowthPhase_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Growth phases\n" },
#endif
		{ "Dead.DisplayName", "Dead" },
		{ "Dead.Name", "EAuracronGrowthPhase::Dead" },
		{ "Dormant.DisplayName", "Dormant" },
		{ "Dormant.Name", "EAuracronGrowthPhase::Dormant" },
		{ "Flowering.DisplayName", "Flowering" },
		{ "Flowering.Name", "EAuracronGrowthPhase::Flowering" },
		{ "Fruiting.DisplayName", "Fruiting" },
		{ "Fruiting.Name", "EAuracronGrowthPhase::Fruiting" },
		{ "Juvenile.DisplayName", "Juvenile" },
		{ "Juvenile.Name", "EAuracronGrowthPhase::Juvenile" },
		{ "Mature.DisplayName", "Mature" },
		{ "Mature.Name", "EAuracronGrowthPhase::Mature" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
		{ "Seed.DisplayName", "Seed" },
		{ "Seed.Name", "EAuracronGrowthPhase::Seed" },
		{ "Senescent.DisplayName", "Senescent" },
		{ "Senescent.Name", "EAuracronGrowthPhase::Senescent" },
		{ "Sprout.DisplayName", "Sprout" },
		{ "Sprout.Name", "EAuracronGrowthPhase::Sprout" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Growth phases" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGrowthPhase::Seed", (int64)EAuracronGrowthPhase::Seed },
		{ "EAuracronGrowthPhase::Sprout", (int64)EAuracronGrowthPhase::Sprout },
		{ "EAuracronGrowthPhase::Juvenile", (int64)EAuracronGrowthPhase::Juvenile },
		{ "EAuracronGrowthPhase::Mature", (int64)EAuracronGrowthPhase::Mature },
		{ "EAuracronGrowthPhase::Flowering", (int64)EAuracronGrowthPhase::Flowering },
		{ "EAuracronGrowthPhase::Fruiting", (int64)EAuracronGrowthPhase::Fruiting },
		{ "EAuracronGrowthPhase::Senescent", (int64)EAuracronGrowthPhase::Senescent },
		{ "EAuracronGrowthPhase::Dormant", (int64)EAuracronGrowthPhase::Dormant },
		{ "EAuracronGrowthPhase::Dead", (int64)EAuracronGrowthPhase::Dead },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronGrowthPhase",
	"EAuracronGrowthPhase",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase()
{
	if (!Z_Registration_Info_UEnum_EAuracronGrowthPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGrowthPhase.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGrowthPhase.InnerSingleton;
}
// ********** End Enum EAuracronGrowthPhase ********************************************************

// ********** Begin Enum EAuracronSeasonalChangeType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSeasonalChangeType;
static UEnum* EAuracronSeasonalChangeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronSeasonalChangeType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonalChangeType>()
{
	return EAuracronSeasonalChangeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ColorVariation.DisplayName", "Color Variation" },
		{ "ColorVariation.Name", "EAuracronSeasonalChangeType::ColorVariation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Seasonal change types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronSeasonalChangeType::Custom" },
		{ "DensityChange.DisplayName", "Density Change" },
		{ "DensityChange.Name", "EAuracronSeasonalChangeType::DensityChange" },
		{ "GrowthSimulation.DisplayName", "Growth Simulation" },
		{ "GrowthSimulation.Name", "EAuracronSeasonalChangeType::GrowthSimulation" },
		{ "LifecycleManagement.DisplayName", "Lifecycle Management" },
		{ "LifecycleManagement.Name", "EAuracronSeasonalChangeType::LifecycleManagement" },
		{ "MaterialTransition.DisplayName", "Material Transition" },
		{ "MaterialTransition.Name", "EAuracronSeasonalChangeType::MaterialTransition" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
		{ "ScaleVariation.DisplayName", "Scale Variation" },
		{ "ScaleVariation.Name", "EAuracronSeasonalChangeType::ScaleVariation" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal change types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSeasonalChangeType::ColorVariation", (int64)EAuracronSeasonalChangeType::ColorVariation },
		{ "EAuracronSeasonalChangeType::DensityChange", (int64)EAuracronSeasonalChangeType::DensityChange },
		{ "EAuracronSeasonalChangeType::GrowthSimulation", (int64)EAuracronSeasonalChangeType::GrowthSimulation },
		{ "EAuracronSeasonalChangeType::LifecycleManagement", (int64)EAuracronSeasonalChangeType::LifecycleManagement },
		{ "EAuracronSeasonalChangeType::MaterialTransition", (int64)EAuracronSeasonalChangeType::MaterialTransition },
		{ "EAuracronSeasonalChangeType::ScaleVariation", (int64)EAuracronSeasonalChangeType::ScaleVariation },
		{ "EAuracronSeasonalChangeType::Custom", (int64)EAuracronSeasonalChangeType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronSeasonalChangeType",
	"EAuracronSeasonalChangeType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalChangeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonalChangeType.InnerSingleton;
}
// ********** End Enum EAuracronSeasonalChangeType *************************************************

// ********** Begin Enum EAuracronLifecycleEvent ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLifecycleEvent;
static UEnum* EAuracronLifecycleEvent_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLifecycleEvent.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLifecycleEvent.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronLifecycleEvent"));
	}
	return Z_Registration_Info_UEnum_EAuracronLifecycleEvent.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLifecycleEvent>()
{
	return EAuracronLifecycleEvent_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lifecycle events\n" },
#endif
		{ "Death.DisplayName", "Death" },
		{ "Death.Name", "EAuracronLifecycleEvent::Death" },
		{ "Dormancy.DisplayName", "Dormancy" },
		{ "Dormancy.Name", "EAuracronLifecycleEvent::Dormancy" },
		{ "Flowering.DisplayName", "Flowering" },
		{ "Flowering.Name", "EAuracronLifecycleEvent::Flowering" },
		{ "Fruiting.DisplayName", "Fruiting" },
		{ "Fruiting.Name", "EAuracronLifecycleEvent::Fruiting" },
		{ "Germination.DisplayName", "Germination" },
		{ "Germination.Name", "EAuracronLifecycleEvent::Germination" },
		{ "LeafBudding.DisplayName", "Leaf Budding" },
		{ "LeafBudding.Name", "EAuracronLifecycleEvent::LeafBudding" },
		{ "LeafDrop.DisplayName", "Leaf Drop" },
		{ "LeafDrop.Name", "EAuracronLifecycleEvent::LeafDrop" },
		{ "LeafSenescence.DisplayName", "Leaf Senescence" },
		{ "LeafSenescence.Name", "EAuracronLifecycleEvent::LeafSenescence" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
		{ "Regeneration.DisplayName", "Regeneration" },
		{ "Regeneration.Name", "EAuracronLifecycleEvent::Regeneration" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lifecycle events" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLifecycleEvent::Germination", (int64)EAuracronLifecycleEvent::Germination },
		{ "EAuracronLifecycleEvent::LeafBudding", (int64)EAuracronLifecycleEvent::LeafBudding },
		{ "EAuracronLifecycleEvent::Flowering", (int64)EAuracronLifecycleEvent::Flowering },
		{ "EAuracronLifecycleEvent::Fruiting", (int64)EAuracronLifecycleEvent::Fruiting },
		{ "EAuracronLifecycleEvent::LeafSenescence", (int64)EAuracronLifecycleEvent::LeafSenescence },
		{ "EAuracronLifecycleEvent::LeafDrop", (int64)EAuracronLifecycleEvent::LeafDrop },
		{ "EAuracronLifecycleEvent::Dormancy", (int64)EAuracronLifecycleEvent::Dormancy },
		{ "EAuracronLifecycleEvent::Death", (int64)EAuracronLifecycleEvent::Death },
		{ "EAuracronLifecycleEvent::Regeneration", (int64)EAuracronLifecycleEvent::Regeneration },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronLifecycleEvent",
	"EAuracronLifecycleEvent",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent()
{
	if (!Z_Registration_Info_UEnum_EAuracronLifecycleEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLifecycleEvent.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLifecycleEvent.InnerSingleton;
}
// ********** End Enum EAuracronLifecycleEvent *****************************************************

// ********** Begin ScriptStruct FAuracronSeasonalConfiguration ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration;
class UScriptStruct* FAuracronSeasonalConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSeasonalConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Seasonal Configuration Data\n * Configuration for seasonal changes system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal Configuration Data\nConfiguration for seasonal changes system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSeasonalChanges_MetaData[] = {
		{ "Category", "Seasonal System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultSeason_MetaData[] = {
		{ "Category", "Seasonal System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonDurationDays_MetaData[] = {
		{ "Category", "Time System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DayLengthSeconds_MetaData[] = {
		{ "Category", "Time System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeAcceleration_MetaData[] = {
		{ "Category", "Time System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 20 minutes real time = 1 day game time\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "20 minutes real time = 1 day game time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableColorVariation_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorTransitionSpeed_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationIntensity_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDensityChanges_MetaData[] = {
		{ "Category", "Density Changes" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityChangeSpeed_MetaData[] = {
		{ "Category", "Density Changes" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDensityVariation_MetaData[] = {
		{ "Category", "Density Changes" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGrowthSimulation_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthRate_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGrowthScale_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLifecycleManagement_MetaData[] = {
		{ "Category", "Lifecycle Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleSpeed_MetaData[] = {
		{ "Category", "Lifecycle Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoRegeneration_MetaData[] = {
		{ "Category", "Lifecycle Management" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncSeasonalUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSeasonalUpdatesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSeasonalDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalParameterCollection_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonProgressParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonTypeParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthPhaseParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleProgressParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableSeasonalChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSeasonalChanges;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultSeason_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultSeason;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonDurationDays;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DayLengthSeconds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeAcceleration;
	static void NewProp_bEnableColorVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableColorVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorTransitionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariationIntensity;
	static void NewProp_bEnableDensityChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDensityChanges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityChangeSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDensityVariation;
	static void NewProp_bEnableGrowthSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGrowthSimulation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxGrowthScale;
	static void NewProp_bEnableLifecycleManagement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLifecycleManagement;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifecycleSpeed;
	static void NewProp_bAutoRegeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoRegeneration;
	static void NewProp_bEnableAsyncSeasonalUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncSeasonalUpdates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSeasonalUpdatesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSeasonalDistance;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SeasonalParameterCollection;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SeasonProgressParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SeasonTypeParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthPhaseParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleProgressParameterName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSeasonalConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableSeasonalChanges_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableSeasonalChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableSeasonalChanges = { "bEnableSeasonalChanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableSeasonalChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSeasonalChanges_MetaData), NewProp_bEnableSeasonalChanges_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DefaultSeason_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DefaultSeason = { "DefaultSeason", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, DefaultSeason), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultSeason_MetaData), NewProp_DefaultSeason_MetaData) }; // 1660267735
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonDurationDays = { "SeasonDurationDays", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, SeasonDurationDays), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonDurationDays_MetaData), NewProp_SeasonDurationDays_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DayLengthSeconds = { "DayLengthSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, DayLengthSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DayLengthSeconds_MetaData), NewProp_DayLengthSeconds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_TimeAcceleration = { "TimeAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, TimeAcceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeAcceleration_MetaData), NewProp_TimeAcceleration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableColorVariation_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableColorVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableColorVariation = { "bEnableColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableColorVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableColorVariation_MetaData), NewProp_bEnableColorVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_ColorTransitionSpeed = { "ColorTransitionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, ColorTransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorTransitionSpeed_MetaData), NewProp_ColorTransitionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_ColorVariationIntensity = { "ColorVariationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, ColorVariationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationIntensity_MetaData), NewProp_ColorVariationIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableDensityChanges_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableDensityChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableDensityChanges = { "bEnableDensityChanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableDensityChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDensityChanges_MetaData), NewProp_bEnableDensityChanges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DensityChangeSpeed = { "DensityChangeSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, DensityChangeSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityChangeSpeed_MetaData), NewProp_DensityChangeSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxDensityVariation = { "MaxDensityVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, MaxDensityVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDensityVariation_MetaData), NewProp_MaxDensityVariation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableGrowthSimulation_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableGrowthSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableGrowthSimulation = { "bEnableGrowthSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableGrowthSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGrowthSimulation_MetaData), NewProp_bEnableGrowthSimulation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_GrowthRate = { "GrowthRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, GrowthRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthRate_MetaData), NewProp_GrowthRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxGrowthScale = { "MaxGrowthScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, MaxGrowthScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGrowthScale_MetaData), NewProp_MaxGrowthScale_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableLifecycleManagement_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableLifecycleManagement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableLifecycleManagement = { "bEnableLifecycleManagement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableLifecycleManagement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLifecycleManagement_MetaData), NewProp_bEnableLifecycleManagement_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_LifecycleSpeed = { "LifecycleSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, LifecycleSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleSpeed_MetaData), NewProp_LifecycleSpeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bAutoRegeneration_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bAutoRegeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bAutoRegeneration = { "bAutoRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bAutoRegeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoRegeneration_MetaData), NewProp_bAutoRegeneration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableAsyncSeasonalUpdates_SetBit(void* Obj)
{
	((FAuracronSeasonalConfiguration*)Obj)->bEnableAsyncSeasonalUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableAsyncSeasonalUpdates = { "bEnableAsyncSeasonalUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalConfiguration), &Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableAsyncSeasonalUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncSeasonalUpdates_MetaData), NewProp_bEnableAsyncSeasonalUpdates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxSeasonalUpdatesPerFrame = { "MaxSeasonalUpdatesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, MaxSeasonalUpdatesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSeasonalUpdatesPerFrame_MetaData), NewProp_MaxSeasonalUpdatesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonalUpdateInterval = { "SeasonalUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, SeasonalUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalUpdateInterval_MetaData), NewProp_SeasonalUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxSeasonalDistance = { "MaxSeasonalDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, MaxSeasonalDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSeasonalDistance_MetaData), NewProp_MaxSeasonalDistance_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonalParameterCollection = { "SeasonalParameterCollection", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, SeasonalParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalParameterCollection_MetaData), NewProp_SeasonalParameterCollection_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonProgressParameterName = { "SeasonProgressParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, SeasonProgressParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonProgressParameterName_MetaData), NewProp_SeasonProgressParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonTypeParameterName = { "SeasonTypeParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, SeasonTypeParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonTypeParameterName_MetaData), NewProp_SeasonTypeParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_GrowthPhaseParameterName = { "GrowthPhaseParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, GrowthPhaseParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthPhaseParameterName_MetaData), NewProp_GrowthPhaseParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_LifecycleProgressParameterName = { "LifecycleProgressParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalConfiguration, LifecycleProgressParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleProgressParameterName_MetaData), NewProp_LifecycleProgressParameterName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableSeasonalChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DefaultSeason_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DefaultSeason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonDurationDays,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DayLengthSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_TimeAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_ColorTransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_ColorVariationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableDensityChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_DensityChangeSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxDensityVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableGrowthSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_GrowthRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxGrowthScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableLifecycleManagement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_LifecycleSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bAutoRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_bEnableAsyncSeasonalUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxSeasonalUpdatesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonalUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_MaxSeasonalDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonalParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonProgressParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_SeasonTypeParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_GrowthPhaseParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewProp_LifecycleProgressParameterName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSeasonalConfiguration",
	Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::PropPointers),
	sizeof(FAuracronSeasonalConfiguration),
	alignof(FAuracronSeasonalConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSeasonalConfiguration **************************************

// ********** Begin ScriptStruct FAuracronSeasonalColorData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData;
class UScriptStruct* FAuracronSeasonalColorData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSeasonalColorData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSeasonalColorData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Seasonal Color Data\n * Color variations for different seasons\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal Color Data\nColor variations for different seasons" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorDataId_MetaData[] = {
		{ "Category", "Seasonal Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringBaseColor_MetaData[] = {
		{ "Category", "Spring Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringAccentColor_MetaData[] = {
		{ "Category", "Spring Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SummerBaseColor_MetaData[] = {
		{ "Category", "Summer Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SummerAccentColor_MetaData[] = {
		{ "Category", "Summer Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutumnBaseColor_MetaData[] = {
		{ "Category", "Autumn Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutumnAccentColor_MetaData[] = {
		{ "Category", "Autumn Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WinterBaseColor_MetaData[] = {
		{ "Category", "Winter Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WinterAccentColor_MetaData[] = {
		{ "Category", "Winter Colors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorTransitionCurve_MetaData[] = {
		{ "Category", "Color Curves" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariationRange_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorSaturationMultiplier_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorBrightnessMultiplier_MetaData[] = {
		{ "Category", "Color Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorDataId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpringBaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpringAccentColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SummerBaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SummerAccentColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AutumnBaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AutumnAccentColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WinterBaseColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WinterAccentColor;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ColorTransitionCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariationRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorSaturationMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorBrightnessMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSeasonalColorData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorDataId = { "ColorDataId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, ColorDataId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorDataId_MetaData), NewProp_ColorDataId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SpringBaseColor = { "SpringBaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, SpringBaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringBaseColor_MetaData), NewProp_SpringBaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SpringAccentColor = { "SpringAccentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, SpringAccentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringAccentColor_MetaData), NewProp_SpringAccentColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SummerBaseColor = { "SummerBaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, SummerBaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SummerBaseColor_MetaData), NewProp_SummerBaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SummerAccentColor = { "SummerAccentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, SummerAccentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SummerAccentColor_MetaData), NewProp_SummerAccentColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_AutumnBaseColor = { "AutumnBaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, AutumnBaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutumnBaseColor_MetaData), NewProp_AutumnBaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_AutumnAccentColor = { "AutumnAccentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, AutumnAccentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutumnAccentColor_MetaData), NewProp_AutumnAccentColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_WinterBaseColor = { "WinterBaseColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, WinterBaseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WinterBaseColor_MetaData), NewProp_WinterBaseColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_WinterAccentColor = { "WinterAccentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, WinterAccentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WinterAccentColor_MetaData), NewProp_WinterAccentColor_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorTransitionCurve = { "ColorTransitionCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, ColorTransitionCurve), Z_Construct_UClass_UCurveLinearColor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorTransitionCurve_MetaData), NewProp_ColorTransitionCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorVariationRange = { "ColorVariationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, ColorVariationRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariationRange_MetaData), NewProp_ColorVariationRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorSaturationMultiplier = { "ColorSaturationMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, ColorSaturationMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorSaturationMultiplier_MetaData), NewProp_ColorSaturationMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorBrightnessMultiplier = { "ColorBrightnessMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalColorData, ColorBrightnessMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorBrightnessMultiplier_MetaData), NewProp_ColorBrightnessMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorDataId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SpringBaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SpringAccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SummerBaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_SummerAccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_AutumnBaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_AutumnAccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_WinterBaseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_WinterAccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorTransitionCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorVariationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorSaturationMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewProp_ColorBrightnessMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSeasonalColorData",
	Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::PropPointers),
	sizeof(FAuracronSeasonalColorData),
	alignof(FAuracronSeasonalColorData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalColorData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSeasonalColorData ******************************************

// ********** Begin ScriptStruct FAuracronGrowthSimulationData *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData;
class UScriptStruct* FAuracronGrowthSimulationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGrowthSimulationData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronGrowthSimulationData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Growth Simulation Data\n * Data for foliage growth simulation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Growth Simulation Data\nData for foliage growth simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthDataId_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthProgress_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthRate_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxScale_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentScale_MetaData[] = {
		{ "Category", "Growth Simulation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseDurations_MetaData[] = {
		{ "Category", "Growth Phases" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseScales_MetaData[] = {
		{ "Category", "Growth Phases" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthCurve_MetaData[] = {
		{ "Category", "Growth Curves" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureInfluence_MetaData[] = {
		{ "Category", "Environmental Factors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoistureInfluence_MetaData[] = {
		{ "Category", "Environmental Factors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightInfluence_MetaData[] = {
		{ "Category", "Environmental Factors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NutrientInfluence_MetaData[] = {
		{ "Category", "Environmental Factors" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGrowing_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastGrowthUpdate_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthDataId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseDurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhaseDurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhaseDurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhaseDurations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseScales_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhaseScales_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhaseScales_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhaseScales;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_GrowthCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemperatureInfluence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MoistureInfluence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightInfluence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NutrientInfluence;
	static void NewProp_bIsGrowing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGrowing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GrowthStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastGrowthUpdate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGrowthSimulationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthDataId = { "GrowthDataId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, GrowthDataId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthDataId_MetaData), NewProp_GrowthDataId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, CurrentPhase), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 3594492358
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthProgress = { "GrowthProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, GrowthProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthProgress_MetaData), NewProp_GrowthProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthRate = { "GrowthRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, GrowthRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthRate_MetaData), NewProp_GrowthRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_MaxScale = { "MaxScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, MaxScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxScale_MetaData), NewProp_MaxScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentScale = { "CurrentScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, CurrentScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentScale_MetaData), NewProp_CurrentScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_ValueProp = { "PhaseDurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_Key_KeyProp = { "PhaseDurations_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase, METADATA_PARAMS(0, nullptr) }; // 3594492358
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations = { "PhaseDurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, PhaseDurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseDurations_MetaData), NewProp_PhaseDurations_MetaData) }; // 3594492358
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_ValueProp = { "PhaseScales", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_Key_KeyProp = { "PhaseScales_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase, METADATA_PARAMS(0, nullptr) }; // 3594492358
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales = { "PhaseScales", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, PhaseScales), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseScales_MetaData), NewProp_PhaseScales_MetaData) }; // 3594492358
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthCurve = { "GrowthCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, GrowthCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthCurve_MetaData), NewProp_GrowthCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_TemperatureInfluence = { "TemperatureInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, TemperatureInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureInfluence_MetaData), NewProp_TemperatureInfluence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_MoistureInfluence = { "MoistureInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, MoistureInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoistureInfluence_MetaData), NewProp_MoistureInfluence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_LightInfluence = { "LightInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, LightInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightInfluence_MetaData), NewProp_LightInfluence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_NutrientInfluence = { "NutrientInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, NutrientInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NutrientInfluence_MetaData), NewProp_NutrientInfluence_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_bIsGrowing_SetBit(void* Obj)
{
	((FAuracronGrowthSimulationData*)Obj)->bIsGrowing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_bIsGrowing = { "bIsGrowing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGrowthSimulationData), &Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_bIsGrowing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGrowing_MetaData), NewProp_bIsGrowing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthStartTime = { "GrowthStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, GrowthStartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthStartTime_MetaData), NewProp_GrowthStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_LastGrowthUpdate = { "LastGrowthUpdate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGrowthSimulationData, LastGrowthUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastGrowthUpdate_MetaData), NewProp_LastGrowthUpdate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthDataId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_MaxScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_CurrentScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseDurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_PhaseScales,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_TemperatureInfluence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_MoistureInfluence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_LightInfluence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_NutrientInfluence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_bIsGrowing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_GrowthStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewProp_LastGrowthUpdate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronGrowthSimulationData",
	Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::PropPointers),
	sizeof(FAuracronGrowthSimulationData),
	alignof(FAuracronGrowthSimulationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGrowthSimulationData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGrowthSimulationData ***************************************

// ********** Begin ScriptStruct FAuracronLifecycleData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLifecycleData;
class UScriptStruct* FAuracronLifecycleData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLifecycleData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronLifecycleData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lifecycle Management Data\n * Data for foliage lifecycle management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lifecycle Management Data\nData for foliage lifecycle management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleId_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleProgress_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLifespan_MetaData[] = {
		{ "Category", "Lifecycle" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAge_MetaData[] = {
		{ "Category", "Lifecycle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// days\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "days" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedEvents_MetaData[] = {
		{ "Category", "Lifecycle Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// days\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "days" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingEvents_MetaData[] = {
		{ "Category", "Lifecycle Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTriggerAges_MetaData[] = {
		{ "Category", "Lifecycle Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRegenerate_MetaData[] = {
		{ "Category", "Regeneration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationChance_MetaData[] = {
		{ "Category", "Regeneration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationDelay_MetaData[] = {
		{ "Category", "Regeneration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// days\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "days" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Health" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthDecayRate_MetaData[] = {
		{ "Category", "Health" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAlive_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// per day\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "per day" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BirthTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastLifecycleUpdate_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifecycleProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalLifespan;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentAge;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompletedEvents_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CompletedEvents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedEvents;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PendingEvents_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PendingEvents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PendingEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EventTriggerAges_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EventTriggerAges_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EventTriggerAges_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventTriggerAges;
	static void NewProp_bCanRegenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRegenerate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationDelay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthDecayRate;
	static void NewProp_bIsAlive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAlive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BirthTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastLifecycleUpdate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLifecycleData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, LifecycleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleId_MetaData), NewProp_LifecycleId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LifecycleProgress = { "LifecycleProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, LifecycleProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleProgress_MetaData), NewProp_LifecycleProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_TotalLifespan = { "TotalLifespan", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, TotalLifespan), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLifespan_MetaData), NewProp_TotalLifespan_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CurrentAge = { "CurrentAge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, CurrentAge), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAge_MetaData), NewProp_CurrentAge_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents_Inner = { "CompletedEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, METADATA_PARAMS(0, nullptr) }; // 1576891042
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents = { "CompletedEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, CompletedEvents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedEvents_MetaData), NewProp_CompletedEvents_MetaData) }; // 1576891042
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents_Inner = { "PendingEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, METADATA_PARAMS(0, nullptr) }; // 1576891042
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents = { "PendingEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, PendingEvents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingEvents_MetaData), NewProp_PendingEvents_MetaData) }; // 1576891042
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_ValueProp = { "EventTriggerAges", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_Key_KeyProp = { "EventTriggerAges_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, METADATA_PARAMS(0, nullptr) }; // 1576891042
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges = { "EventTriggerAges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, EventTriggerAges), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTriggerAges_MetaData), NewProp_EventTriggerAges_MetaData) }; // 1576891042
void Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bCanRegenerate_SetBit(void* Obj)
{
	((FAuracronLifecycleData*)Obj)->bCanRegenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bCanRegenerate = { "bCanRegenerate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLifecycleData), &Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bCanRegenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRegenerate_MetaData), NewProp_bCanRegenerate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_RegenerationChance = { "RegenerationChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, RegenerationChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationChance_MetaData), NewProp_RegenerationChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_RegenerationDelay = { "RegenerationDelay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, RegenerationDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationDelay_MetaData), NewProp_RegenerationDelay_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_HealthDecayRate = { "HealthDecayRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, HealthDecayRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthDecayRate_MetaData), NewProp_HealthDecayRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bIsAlive_SetBit(void* Obj)
{
	((FAuracronLifecycleData*)Obj)->bIsAlive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bIsAlive = { "bIsAlive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLifecycleData), &Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bIsAlive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAlive_MetaData), NewProp_bIsAlive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_BirthTime = { "BirthTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, BirthTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BirthTime_MetaData), NewProp_BirthTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LastLifecycleUpdate = { "LastLifecycleUpdate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLifecycleData, LastLifecycleUpdate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastLifecycleUpdate_MetaData), NewProp_LastLifecycleUpdate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LifecycleProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_TotalLifespan,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CurrentAge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_CompletedEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_PendingEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_EventTriggerAges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bCanRegenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_RegenerationChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_RegenerationDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_HealthDecayRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_bIsAlive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_BirthTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewProp_LastLifecycleUpdate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronLifecycleData",
	Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::PropPointers),
	sizeof(FAuracronLifecycleData),
	alignof(FAuracronLifecycleData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLifecycleData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLifecycleData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLifecycleData **********************************************

// ********** Begin ScriptStruct FAuracronSeasonalPerformanceData **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData;
class UScriptStruct* FAuracronSeasonalPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSeasonalPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Seasonal Performance Data\n * Performance metrics for seasonal system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal Performance Data\nPerformance metrics for seasonal system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalSeasonalInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSeasonalInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowingInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialParameterUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalSeasonalInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveSeasonalInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GrowingInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LifecycleInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrowthUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifecycleUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaterialParameterUpdates;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSeasonalPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_TotalSeasonalInstances = { "TotalSeasonalInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, TotalSeasonalInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalSeasonalInstances_MetaData), NewProp_TotalSeasonalInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_ActiveSeasonalInstances = { "ActiveSeasonalInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, ActiveSeasonalInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSeasonalInstances_MetaData), NewProp_ActiveSeasonalInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_GrowingInstances = { "GrowingInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, GrowingInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowingInstances_MetaData), NewProp_GrowingInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_LifecycleInstances = { "LifecycleInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, LifecycleInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleInstances_MetaData), NewProp_LifecycleInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_SeasonalUpdateTime = { "SeasonalUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, SeasonalUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalUpdateTime_MetaData), NewProp_SeasonalUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_ColorUpdateTime = { "ColorUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, ColorUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorUpdateTime_MetaData), NewProp_ColorUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_GrowthUpdateTime = { "GrowthUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, GrowthUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthUpdateTime_MetaData), NewProp_GrowthUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_LifecycleUpdateTime = { "LifecycleUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, LifecycleUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleUpdateTime_MetaData), NewProp_LifecycleUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_MaterialParameterUpdates = { "MaterialParameterUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, MaterialParameterUpdates), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialParameterUpdates_MetaData), NewProp_MaterialParameterUpdates_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_TotalSeasonalInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_ActiveSeasonalInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_GrowingInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_LifecycleInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_SeasonalUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_ColorUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_GrowthUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_LifecycleUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_MaterialParameterUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSeasonalPerformanceData",
	Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::PropPointers),
	sizeof(FAuracronSeasonalPerformanceData),
	alignof(FAuracronSeasonalPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSeasonalPerformanceData ************************************

// ********** Begin Delegate FOnSeasonChanged ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics
{
	struct AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms
	{
		EAuracronSeasonType NewSeason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewSeason_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewSeason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::NewProp_NewSeason_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::NewProp_NewSeason = { "NewSeason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms, NewSeason), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::NewProp_NewSeason_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::NewProp_NewSeason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "OnSeasonChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageSeasonalManager::FOnSeasonChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSeasonChanged, EAuracronSeasonType NewSeason)
{
	struct AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms
	{
		EAuracronSeasonType NewSeason;
	};
	AuracronFoliageSeasonalManager_eventOnSeasonChanged_Parms Parms;
	Parms.NewSeason=NewSeason;
	OnSeasonChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSeasonChanged ********************************************************

// ********** Begin Delegate FOnGrowthPhaseChanged *************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics
{
	struct AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms
	{
		FString GrowthId;
		EAuracronGrowthPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_GrowthId = { "GrowthId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms, GrowthId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGrowthPhase, METADATA_PARAMS(0, nullptr) }; // 3594492358
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_GrowthId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "OnGrowthPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageSeasonalManager::FOnGrowthPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGrowthPhaseChanged, const FString& GrowthId, EAuracronGrowthPhase NewPhase)
{
	struct AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms
	{
		FString GrowthId;
		EAuracronGrowthPhase NewPhase;
	};
	AuracronFoliageSeasonalManager_eventOnGrowthPhaseChanged_Parms Parms;
	Parms.GrowthId=GrowthId;
	Parms.NewPhase=NewPhase;
	OnGrowthPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGrowthPhaseChanged ***************************************************

// ********** Begin Delegate FOnLifecycleEventTriggered ********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics
{
	struct AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms
	{
		FString LifecycleId;
		EAuracronLifecycleEvent Event;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Event_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Event;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms, LifecycleId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_Event_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_Event = { "Event", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms, Event), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, METADATA_PARAMS(0, nullptr) }; // 1576891042
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_Event_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::NewProp_Event,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "OnLifecycleEventTriggered__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageSeasonalManager::FOnLifecycleEventTriggered_DelegateWrapper(const FMulticastScriptDelegate& OnLifecycleEventTriggered, const FString& LifecycleId, EAuracronLifecycleEvent Event)
{
	struct AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms
	{
		FString LifecycleId;
		EAuracronLifecycleEvent Event;
	};
	AuracronFoliageSeasonalManager_eventOnLifecycleEventTriggered_Parms Parms;
	Parms.LifecycleId=LifecycleId;
	Parms.Event=Event;
	OnLifecycleEventTriggered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLifecycleEventTriggered **********************************************

// ********** Begin Delegate FOnColorVariationUpdated **********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics
{
	struct AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms
	{
		FString FoliageTypeId;
		FLinearColor NewColor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms, FoliageTypeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::NewProp_NewColor = { "NewColor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms, NewColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::NewProp_NewColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "OnColorVariationUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageSeasonalManager::FOnColorVariationUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnColorVariationUpdated, const FString& FoliageTypeId, FLinearColor NewColor)
{
	struct AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms
	{
		FString FoliageTypeId;
		FLinearColor NewColor;
	};
	AuracronFoliageSeasonalManager_eventOnColorVariationUpdated_Parms Parms;
	Parms.FoliageTypeId=FoliageTypeId;
	Parms.NewColor=NewColor;
	OnColorVariationUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnColorVariationUpdated ************************************************

// ********** Begin Class UAuracronFoliageSeasonalManager Function AdvanceSeasonByDays *************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics
{
	struct AuracronFoliageSeasonalManager_eventAdvanceSeasonByDays_Parms
	{
		float Days;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Days;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::NewProp_Days = { "Days", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventAdvanceSeasonByDays_Parms, Days), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::NewProp_Days,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "AdvanceSeasonByDays", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::AuracronFoliageSeasonalManager_eventAdvanceSeasonByDays_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::AuracronFoliageSeasonalManager_eventAdvanceSeasonByDays_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execAdvanceSeasonByDays)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Days);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceSeasonByDays(Z_Param_Days);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function AdvanceSeasonByDays ***************

// ********** Begin Class UAuracronFoliageSeasonalManager Function ApplyDensityChangesToBiome ******
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics
{
	struct AuracronFoliageSeasonalManager_eventApplyDensityChangesToBiome_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventApplyDensityChangesToBiome_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "ApplyDensityChangesToBiome", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::AuracronFoliageSeasonalManager_eventApplyDensityChangesToBiome_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::AuracronFoliageSeasonalManager_eventApplyDensityChangesToBiome_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execApplyDensityChangesToBiome)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyDensityChangesToBiome(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function ApplyDensityChangesToBiome ********

// ********** Begin Class UAuracronFoliageSeasonalManager Function CreateGrowthSimulation **********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics
{
	struct AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms
	{
		FString FoliageInstanceId;
		FAuracronGrowthSimulationData GrowthData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Growth simulation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Growth simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GrowthData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_GrowthData = { "GrowthData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms, GrowthData), Z_Construct_UScriptStruct_FAuracronGrowthSimulationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthData_MetaData), NewProp_GrowthData_MetaData) }; // 2797846052
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_GrowthData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "CreateGrowthSimulation", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventCreateGrowthSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execCreateGrowthSimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_STRUCT_REF(FAuracronGrowthSimulationData,Z_Param_Out_GrowthData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateGrowthSimulation(Z_Param_FoliageInstanceId,Z_Param_Out_GrowthData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function CreateGrowthSimulation ************

// ********** Begin Class UAuracronFoliageSeasonalManager Function CreateLifecycleManagement *******
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics
{
	struct AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms
	{
		FString FoliageInstanceId;
		FAuracronLifecycleData LifecycleData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lifecycle management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lifecycle management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LifecycleData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_LifecycleData = { "LifecycleData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms, LifecycleData), Z_Construct_UScriptStruct_FAuracronLifecycleData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleData_MetaData), NewProp_LifecycleData_MetaData) }; // 54288141
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_LifecycleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "CreateLifecycleManagement", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventCreateLifecycleManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execCreateLifecycleManagement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_STRUCT_REF(FAuracronLifecycleData,Z_Param_Out_LifecycleData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateLifecycleManagement(Z_Param_FoliageInstanceId,Z_Param_Out_LifecycleData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function CreateLifecycleManagement *********

// ********** Begin Class UAuracronFoliageSeasonalManager Function DrawDebugSeasonalInfo ***********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics
{
	struct AuracronFoliageSeasonalManager_eventDrawDebugSeasonalInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventDrawDebugSeasonalInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "DrawDebugSeasonalInfo", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::AuracronFoliageSeasonalManager_eventDrawDebugSeasonalInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::AuracronFoliageSeasonalManager_eventDrawDebugSeasonalInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execDrawDebugSeasonalInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugSeasonalInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function DrawDebugSeasonalInfo *************

// ********** Begin Class UAuracronFoliageSeasonalManager Function EnableDebugVisualization ********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageSeasonalManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::AuracronFoliageSeasonalManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::AuracronFoliageSeasonalManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function EnableDebugVisualization **********

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetActiveSeasonalInstanceCount **
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetActiveSeasonalInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetActiveSeasonalInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetActiveSeasonalInstanceCount", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::AuracronFoliageSeasonalManager_eventGetActiveSeasonalInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::AuracronFoliageSeasonalManager_eventGetActiveSeasonalInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetActiveSeasonalInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveSeasonalInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetActiveSeasonalInstanceCount ****

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetConfiguration ****************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetConfiguration_Parms
	{
		FAuracronSeasonalConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration, METADATA_PARAMS(0, nullptr) }; // 2084404029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::AuracronFoliageSeasonalManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::AuracronFoliageSeasonalManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSeasonalConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetConfiguration ******************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetCurrentGameDay ***************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetCurrentGameDay_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetCurrentGameDay_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetCurrentGameDay", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::AuracronFoliageSeasonalManager_eventGetCurrentGameDay_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::AuracronFoliageSeasonalManager_eventGetCurrentGameDay_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetCurrentGameDay)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrentGameDay();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetCurrentGameDay *****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetCurrentGameTime **************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetCurrentGameTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetCurrentGameTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetCurrentGameTime", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::AuracronFoliageSeasonalManager_eventGetCurrentGameTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::AuracronFoliageSeasonalManager_eventGetCurrentGameTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetCurrentGameTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentGameTime();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetCurrentGameTime ****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetCurrentSeason ****************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetCurrentSeason_Parms
	{
		EAuracronSeasonType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetCurrentSeason_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetCurrentSeason", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::AuracronFoliageSeasonalManager_eventGetCurrentSeason_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::AuracronFoliageSeasonalManager_eventGetCurrentSeason_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetCurrentSeason)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronSeasonType*)Z_Param__Result=P_THIS->GetCurrentSeason();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetCurrentSeason ******************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetCurrentSeasonalColor *********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms
	{
		FString FoliageTypeId;
		bool bUseAccentColor;
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "CPP_Default_bUseAccentColor", "false" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static void NewProp_bUseAccentColor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAccentColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_bUseAccentColor_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms*)Obj)->bUseAccentColor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_bUseAccentColor = { "bUseAccentColor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_bUseAccentColor_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_bUseAccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetCurrentSeasonalColor", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::AuracronFoliageSeasonalManager_eventGetCurrentSeasonalColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetCurrentSeasonalColor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_UBOOL(Z_Param_bUseAccentColor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetCurrentSeasonalColor(Z_Param_FoliageTypeId,Z_Param_bUseAccentColor);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetCurrentSeasonalColor ***********

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetGrowingInstanceCount *********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetGrowingInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetGrowingInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetGrowingInstanceCount", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::AuracronFoliageSeasonalManager_eventGetGrowingInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::AuracronFoliageSeasonalManager_eventGetGrowingInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetGrowingInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGrowingInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetGrowingInstanceCount ***********

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetGrowthSimulation *************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetGrowthSimulation_Parms
	{
		FString GrowthId;
		FAuracronGrowthSimulationData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::NewProp_GrowthId = { "GrowthId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetGrowthSimulation_Parms, GrowthId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthId_MetaData), NewProp_GrowthId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetGrowthSimulation_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGrowthSimulationData, METADATA_PARAMS(0, nullptr) }; // 2797846052
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::NewProp_GrowthId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetGrowthSimulation", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventGetGrowthSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventGetGrowthSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetGrowthSimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GrowthId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGrowthSimulationData*)Z_Param__Result=P_THIS->GetGrowthSimulation(Z_Param_GrowthId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetGrowthSimulation ***************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetInstance *********************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetInstance_Parms
	{
		UAuracronFoliageSeasonalManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::AuracronFoliageSeasonalManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::AuracronFoliageSeasonalManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageSeasonalManager**)Z_Param__Result=UAuracronFoliageSeasonalManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetInstance ***********************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetLifecycleManagement **********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetLifecycleManagement_Parms
	{
		FString LifecycleId;
		FAuracronLifecycleData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetLifecycleManagement_Parms, LifecycleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleId_MetaData), NewProp_LifecycleId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetLifecycleManagement_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLifecycleData, METADATA_PARAMS(0, nullptr) }; // 54288141
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetLifecycleManagement", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventGetLifecycleManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventGetLifecycleManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetLifecycleManagement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LifecycleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLifecycleData*)Z_Param__Result=P_THIS->GetLifecycleManagement(Z_Param_LifecycleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetLifecycleManagement ************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetMaterialParameterCollection **
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetMaterialParameterCollection_Parms
	{
		UMaterialParameterCollection* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetMaterialParameterCollection_Parms, ReturnValue), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetMaterialParameterCollection", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::AuracronFoliageSeasonalManager_eventGetMaterialParameterCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::AuracronFoliageSeasonalManager_eventGetMaterialParameterCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetMaterialParameterCollection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialParameterCollection**)Z_Param__Result=P_THIS->GetMaterialParameterCollection();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetMaterialParameterCollection ****

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetPerformanceData **************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetPerformanceData_Parms
	{
		FAuracronSeasonalPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData, METADATA_PARAMS(0, nullptr) }; // 901706888
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::AuracronFoliageSeasonalManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::AuracronFoliageSeasonalManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSeasonalPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetPerformanceData ****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetSeasonalColors ***************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetSeasonalColors_Parms
	{
		FString FoliageTypeId;
		FAuracronSeasonalColorData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetSeasonalColors_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetSeasonalColors_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSeasonalColorData, METADATA_PARAMS(0, nullptr) }; // 1578471479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetSeasonalColors", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::AuracronFoliageSeasonalManager_eventGetSeasonalColors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::AuracronFoliageSeasonalManager_eventGetSeasonalColors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetSeasonalColors)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSeasonalColorData*)Z_Param__Result=P_THIS->GetSeasonalColors(Z_Param_FoliageTypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetSeasonalColors *****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetSeasonalDensityMultiplier ****
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetSeasonalDensityMultiplier_Parms
	{
		EAuracronSeasonType Season;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Season_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Season;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_Season_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetSeasonalDensityMultiplier_Parms, Season), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetSeasonalDensityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_Season_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetSeasonalDensityMultiplier", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::AuracronFoliageSeasonalManager_eventGetSeasonalDensityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::AuracronFoliageSeasonalManager_eventGetSeasonalDensityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetSeasonalDensityMultiplier)
{
	P_GET_ENUM(EAuracronSeasonType,Z_Param_Season);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSeasonalDensityMultiplier(EAuracronSeasonType(Z_Param_Season));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetSeasonalDensityMultiplier ******

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetSeasonProgress ***************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetSeasonProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetSeasonProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetSeasonProgress", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::AuracronFoliageSeasonalManager_eventGetSeasonProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::AuracronFoliageSeasonalManager_eventGetSeasonProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetSeasonProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSeasonProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetSeasonProgress *****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function GetTimeAcceleration *************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics
{
	struct AuracronFoliageSeasonalManager_eventGetTimeAcceleration_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventGetTimeAcceleration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "GetTimeAcceleration", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::AuracronFoliageSeasonalManager_eventGetTimeAcceleration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::AuracronFoliageSeasonalManager_eventGetTimeAcceleration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execGetTimeAcceleration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeAcceleration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function GetTimeAcceleration ***************

// ********** Begin Class UAuracronFoliageSeasonalManager Function Initialize **********************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics
{
	struct AuracronFoliageSeasonalManager_eventInitialize_Parms
	{
		FAuracronSeasonalConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2084404029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::AuracronFoliageSeasonalManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::AuracronFoliageSeasonalManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronSeasonalConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function Initialize ************************

// ********** Begin Class UAuracronFoliageSeasonalManager Function IntegrateWithBiomeSystem ********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics
{
	struct AuracronFoliageSeasonalManager_eventIntegrateWithBiomeSystem_Parms
	{
		UAuracronFoliageBiomeManager* BiomeManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BiomeManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::NewProp_BiomeManager = { "BiomeManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventIntegrateWithBiomeSystem_Parms, BiomeManager), Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::NewProp_BiomeManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "IntegrateWithBiomeSystem", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::AuracronFoliageSeasonalManager_eventIntegrateWithBiomeSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::AuracronFoliageSeasonalManager_eventIntegrateWithBiomeSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execIntegrateWithBiomeSystem)
{
	P_GET_OBJECT(UAuracronFoliageBiomeManager,Z_Param_BiomeManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithBiomeSystem(Z_Param_BiomeManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function IntegrateWithBiomeSystem **********

// ********** Begin Class UAuracronFoliageSeasonalManager Function IntegrateWithWindSystem *********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics
{
	struct AuracronFoliageSeasonalManager_eventIntegrateWithWindSystem_Parms
	{
		UAuracronFoliageWindManager* WindManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WindManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::NewProp_WindManager = { "WindManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventIntegrateWithWindSystem_Parms, WindManager), Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::NewProp_WindManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "IntegrateWithWindSystem", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::AuracronFoliageSeasonalManager_eventIntegrateWithWindSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::AuracronFoliageSeasonalManager_eventIntegrateWithWindSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execIntegrateWithWindSystem)
{
	P_GET_OBJECT(UAuracronFoliageWindManager,Z_Param_WindManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWindSystem(Z_Param_WindManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function IntegrateWithWindSystem ***********

// ********** Begin Class UAuracronFoliageSeasonalManager Function IsDebugVisualizationEnabled *****
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageSeasonalManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageSeasonalManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageSeasonalManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function IsDebugVisualizationEnabled *******

// ********** Begin Class UAuracronFoliageSeasonalManager Function IsInitialized *******************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics
{
	struct AuracronFoliageSeasonalManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::AuracronFoliageSeasonalManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::AuracronFoliageSeasonalManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function IsInitialized *********************

// ********** Begin Class UAuracronFoliageSeasonalManager Function LogSeasonalStatistics ***********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "LogSeasonalStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execLogSeasonalStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogSeasonalStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function LogSeasonalStatistics *************

// ********** Begin Class UAuracronFoliageSeasonalManager Function RemoveGrowthSimulation **********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics
{
	struct AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms
	{
		FString GrowthId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_GrowthId = { "GrowthId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms, GrowthId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthId_MetaData), NewProp_GrowthId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_GrowthId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "RemoveGrowthSimulation", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventRemoveGrowthSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execRemoveGrowthSimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GrowthId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveGrowthSimulation(Z_Param_GrowthId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function RemoveGrowthSimulation ************

// ********** Begin Class UAuracronFoliageSeasonalManager Function RemoveLifecycleManagement *******
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics
{
	struct AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms
	{
		FString LifecycleId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms, LifecycleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleId_MetaData), NewProp_LifecycleId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "RemoveLifecycleManagement", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventRemoveLifecycleManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execRemoveLifecycleManagement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LifecycleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLifecycleManagement(Z_Param_LifecycleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function RemoveLifecycleManagement *********

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetConfiguration ****************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetConfiguration_Parms
	{
		FAuracronSeasonalConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2084404029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::AuracronFoliageSeasonalManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::AuracronFoliageSeasonalManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronSeasonalConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetConfiguration ******************

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetCurrentSeason ****************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetCurrentSeason_Parms
	{
		EAuracronSeasonType Season;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Season control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Season control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Season_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Season;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::NewProp_Season_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetCurrentSeason_Parms, Season), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonType, METADATA_PARAMS(0, nullptr) }; // 1660267735
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::NewProp_Season_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::NewProp_Season,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetCurrentSeason", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::AuracronFoliageSeasonalManager_eventSetCurrentSeason_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::AuracronFoliageSeasonalManager_eventSetCurrentSeason_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetCurrentSeason)
{
	P_GET_ENUM(EAuracronSeasonType,Z_Param_Season);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCurrentSeason(EAuracronSeasonType(Z_Param_Season));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetCurrentSeason ******************

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetMaterialParameterCollection **
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetMaterialParameterCollection_Parms
	{
		UMaterialParameterCollection* ParameterCollection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParameterCollection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::NewProp_ParameterCollection = { "ParameterCollection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetMaterialParameterCollection_Parms, ParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::NewProp_ParameterCollection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetMaterialParameterCollection", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::AuracronFoliageSeasonalManager_eventSetMaterialParameterCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::AuracronFoliageSeasonalManager_eventSetMaterialParameterCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetMaterialParameterCollection)
{
	P_GET_OBJECT(UMaterialParameterCollection,Z_Param_ParameterCollection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaterialParameterCollection(Z_Param_ParameterCollection);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetMaterialParameterCollection ****

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetSeasonalColors ***************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetSeasonalColors_Parms
	{
		FString FoliageTypeId;
		FAuracronSeasonalColorData ColorData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Color variation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Color variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetSeasonalColors_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::NewProp_ColorData = { "ColorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetSeasonalColors_Parms, ColorData), Z_Construct_UScriptStruct_FAuracronSeasonalColorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorData_MetaData), NewProp_ColorData_MetaData) }; // 1578471479
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::NewProp_ColorData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetSeasonalColors", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::AuracronFoliageSeasonalManager_eventSetSeasonalColors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::AuracronFoliageSeasonalManager_eventSetSeasonalColors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetSeasonalColors)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_STRUCT_REF(FAuracronSeasonalColorData,Z_Param_Out_ColorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSeasonalColors(Z_Param_FoliageTypeId,Z_Param_Out_ColorData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetSeasonalColors *****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetSeasonProgress ***************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetSeasonProgress_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetSeasonProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetSeasonProgress", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::AuracronFoliageSeasonalManager_eventSetSeasonProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::AuracronFoliageSeasonalManager_eventSetSeasonProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetSeasonProgress)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSeasonProgress(Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetSeasonProgress *****************

// ********** Begin Class UAuracronFoliageSeasonalManager Function SetTimeAcceleration *************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics
{
	struct AuracronFoliageSeasonalManager_eventSetTimeAcceleration_Parms
	{
		float Acceleration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Time system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Acceleration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::NewProp_Acceleration = { "Acceleration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSetTimeAcceleration_Parms, Acceleration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::NewProp_Acceleration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SetTimeAcceleration", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::AuracronFoliageSeasonalManager_eventSetTimeAcceleration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::AuracronFoliageSeasonalManager_eventSetTimeAcceleration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSetTimeAcceleration)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Acceleration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTimeAcceleration(Z_Param_Acceleration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SetTimeAcceleration ***************

// ********** Begin Class UAuracronFoliageSeasonalManager Function Shutdown ************************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function Shutdown **************************

// ********** Begin Class UAuracronFoliageSeasonalManager Function SynchronizeWithClimateData ******
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics
{
	struct AuracronFoliageSeasonalManager_eventSynchronizeWithClimateData_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventSynchronizeWithClimateData_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "SynchronizeWithClimateData", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::AuracronFoliageSeasonalManager_eventSynchronizeWithClimateData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::AuracronFoliageSeasonalManager_eventSynchronizeWithClimateData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execSynchronizeWithClimateData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithClimateData(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function SynchronizeWithClimateData ********

// ********** Begin Class UAuracronFoliageSeasonalManager Function Tick ****************************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics
{
	struct AuracronFoliageSeasonalManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::AuracronFoliageSeasonalManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::AuracronFoliageSeasonalManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function Tick ******************************

// ********** Begin Class UAuracronFoliageSeasonalManager Function TriggerLifecycleEvent ***********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics
{
	struct AuracronFoliageSeasonalManager_eventTriggerLifecycleEvent_Parms
	{
		FString LifecycleId;
		EAuracronLifecycleEvent Event;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Event_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Event;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventTriggerLifecycleEvent_Parms, LifecycleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleId_MetaData), NewProp_LifecycleId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_Event_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_Event = { "Event", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventTriggerLifecycleEvent_Parms, Event), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLifecycleEvent, METADATA_PARAMS(0, nullptr) }; // 1576891042
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_Event_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::NewProp_Event,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "TriggerLifecycleEvent", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::AuracronFoliageSeasonalManager_eventTriggerLifecycleEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::AuracronFoliageSeasonalManager_eventTriggerLifecycleEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execTriggerLifecycleEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LifecycleId);
	P_GET_ENUM(EAuracronLifecycleEvent,Z_Param_Event);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerLifecycleEvent(Z_Param_LifecycleId,EAuracronLifecycleEvent(Z_Param_Event));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function TriggerLifecycleEvent *************

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateAllLifecycleManagement ****
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateAllLifecycleManagement_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateAllLifecycleManagement_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateAllLifecycleManagement", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventUpdateAllLifecycleManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventUpdateAllLifecycleManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateAllLifecycleManagement)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAllLifecycleManagement(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateAllLifecycleManagement ******

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateColorVariation ************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateColorVariation_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateColorVariation_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateColorVariation", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::AuracronFoliageSeasonalManager_eventUpdateColorVariation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::AuracronFoliageSeasonalManager_eventUpdateColorVariation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateColorVariation)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateColorVariation(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateColorVariation **************

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateDensityChanges ************
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateDensityChanges_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Density changes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Density changes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateDensityChanges_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateDensityChanges", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::AuracronFoliageSeasonalManager_eventUpdateDensityChanges_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::AuracronFoliageSeasonalManager_eventUpdateDensityChanges_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateDensityChanges)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDensityChanges(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateDensityChanges **************

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateGrowthSimulation **********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms
	{
		FString GrowthId;
		FAuracronGrowthSimulationData GrowthData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrowthData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GrowthId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GrowthData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_GrowthId = { "GrowthId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms, GrowthId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthId_MetaData), NewProp_GrowthId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_GrowthData = { "GrowthData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms, GrowthData), Z_Construct_UScriptStruct_FAuracronGrowthSimulationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrowthData_MetaData), NewProp_GrowthData_MetaData) }; // 2797846052
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_GrowthId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_GrowthData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateGrowthSimulation", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::AuracronFoliageSeasonalManager_eventUpdateGrowthSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateGrowthSimulation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GrowthId);
	P_GET_STRUCT_REF(FAuracronGrowthSimulationData,Z_Param_Out_GrowthData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateGrowthSimulation(Z_Param_GrowthId,Z_Param_Out_GrowthData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateGrowthSimulation ************

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateGrowthSimulations *********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateGrowthSimulations_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateGrowthSimulations_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateGrowthSimulations", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::AuracronFoliageSeasonalManager_eventUpdateGrowthSimulations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::AuracronFoliageSeasonalManager_eventUpdateGrowthSimulations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateGrowthSimulations)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateGrowthSimulations(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateGrowthSimulations ***********

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateLifecycleManagement *******
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics
{
	struct AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms
	{
		FString LifecycleId;
		FAuracronLifecycleData LifecycleData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LifecycleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LifecycleData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_LifecycleId = { "LifecycleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms, LifecycleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleId_MetaData), NewProp_LifecycleId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_LifecycleData = { "LifecycleData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms, LifecycleData), Z_Construct_UScriptStruct_FAuracronLifecycleData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleData_MetaData), NewProp_LifecycleData_MetaData) }; // 54288141
void Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms), &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_LifecycleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_LifecycleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateLifecycleManagement", Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::AuracronFoliageSeasonalManager_eventUpdateLifecycleManagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateLifecycleManagement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LifecycleId);
	P_GET_STRUCT_REF(FAuracronLifecycleData,Z_Param_Out_LifecycleData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateLifecycleManagement(Z_Param_LifecycleId,Z_Param_Out_LifecycleData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateLifecycleManagement *********

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdateMaterialParameters ********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdateMaterialParameters", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdateMaterialParameters)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMaterialParameters();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdateMaterialParameters **********

// ********** Begin Class UAuracronFoliageSeasonalManager Function UpdatePerformanceMetrics ********
struct Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Seasonal Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageSeasonalManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageSeasonalManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageSeasonalManager Function UpdatePerformanceMetrics **********

// ********** Begin Class UAuracronFoliageSeasonalManager ******************************************
void UAuracronFoliageSeasonalManager::StaticRegisterNativesUAuracronFoliageSeasonalManager()
{
	UClass* Class = UAuracronFoliageSeasonalManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdvanceSeasonByDays", &UAuracronFoliageSeasonalManager::execAdvanceSeasonByDays },
		{ "ApplyDensityChangesToBiome", &UAuracronFoliageSeasonalManager::execApplyDensityChangesToBiome },
		{ "CreateGrowthSimulation", &UAuracronFoliageSeasonalManager::execCreateGrowthSimulation },
		{ "CreateLifecycleManagement", &UAuracronFoliageSeasonalManager::execCreateLifecycleManagement },
		{ "DrawDebugSeasonalInfo", &UAuracronFoliageSeasonalManager::execDrawDebugSeasonalInfo },
		{ "EnableDebugVisualization", &UAuracronFoliageSeasonalManager::execEnableDebugVisualization },
		{ "GetActiveSeasonalInstanceCount", &UAuracronFoliageSeasonalManager::execGetActiveSeasonalInstanceCount },
		{ "GetConfiguration", &UAuracronFoliageSeasonalManager::execGetConfiguration },
		{ "GetCurrentGameDay", &UAuracronFoliageSeasonalManager::execGetCurrentGameDay },
		{ "GetCurrentGameTime", &UAuracronFoliageSeasonalManager::execGetCurrentGameTime },
		{ "GetCurrentSeason", &UAuracronFoliageSeasonalManager::execGetCurrentSeason },
		{ "GetCurrentSeasonalColor", &UAuracronFoliageSeasonalManager::execGetCurrentSeasonalColor },
		{ "GetGrowingInstanceCount", &UAuracronFoliageSeasonalManager::execGetGrowingInstanceCount },
		{ "GetGrowthSimulation", &UAuracronFoliageSeasonalManager::execGetGrowthSimulation },
		{ "GetInstance", &UAuracronFoliageSeasonalManager::execGetInstance },
		{ "GetLifecycleManagement", &UAuracronFoliageSeasonalManager::execGetLifecycleManagement },
		{ "GetMaterialParameterCollection", &UAuracronFoliageSeasonalManager::execGetMaterialParameterCollection },
		{ "GetPerformanceData", &UAuracronFoliageSeasonalManager::execGetPerformanceData },
		{ "GetSeasonalColors", &UAuracronFoliageSeasonalManager::execGetSeasonalColors },
		{ "GetSeasonalDensityMultiplier", &UAuracronFoliageSeasonalManager::execGetSeasonalDensityMultiplier },
		{ "GetSeasonProgress", &UAuracronFoliageSeasonalManager::execGetSeasonProgress },
		{ "GetTimeAcceleration", &UAuracronFoliageSeasonalManager::execGetTimeAcceleration },
		{ "Initialize", &UAuracronFoliageSeasonalManager::execInitialize },
		{ "IntegrateWithBiomeSystem", &UAuracronFoliageSeasonalManager::execIntegrateWithBiomeSystem },
		{ "IntegrateWithWindSystem", &UAuracronFoliageSeasonalManager::execIntegrateWithWindSystem },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageSeasonalManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageSeasonalManager::execIsInitialized },
		{ "LogSeasonalStatistics", &UAuracronFoliageSeasonalManager::execLogSeasonalStatistics },
		{ "RemoveGrowthSimulation", &UAuracronFoliageSeasonalManager::execRemoveGrowthSimulation },
		{ "RemoveLifecycleManagement", &UAuracronFoliageSeasonalManager::execRemoveLifecycleManagement },
		{ "SetConfiguration", &UAuracronFoliageSeasonalManager::execSetConfiguration },
		{ "SetCurrentSeason", &UAuracronFoliageSeasonalManager::execSetCurrentSeason },
		{ "SetMaterialParameterCollection", &UAuracronFoliageSeasonalManager::execSetMaterialParameterCollection },
		{ "SetSeasonalColors", &UAuracronFoliageSeasonalManager::execSetSeasonalColors },
		{ "SetSeasonProgress", &UAuracronFoliageSeasonalManager::execSetSeasonProgress },
		{ "SetTimeAcceleration", &UAuracronFoliageSeasonalManager::execSetTimeAcceleration },
		{ "Shutdown", &UAuracronFoliageSeasonalManager::execShutdown },
		{ "SynchronizeWithClimateData", &UAuracronFoliageSeasonalManager::execSynchronizeWithClimateData },
		{ "Tick", &UAuracronFoliageSeasonalManager::execTick },
		{ "TriggerLifecycleEvent", &UAuracronFoliageSeasonalManager::execTriggerLifecycleEvent },
		{ "UpdateAllLifecycleManagement", &UAuracronFoliageSeasonalManager::execUpdateAllLifecycleManagement },
		{ "UpdateColorVariation", &UAuracronFoliageSeasonalManager::execUpdateColorVariation },
		{ "UpdateDensityChanges", &UAuracronFoliageSeasonalManager::execUpdateDensityChanges },
		{ "UpdateGrowthSimulation", &UAuracronFoliageSeasonalManager::execUpdateGrowthSimulation },
		{ "UpdateGrowthSimulations", &UAuracronFoliageSeasonalManager::execUpdateGrowthSimulations },
		{ "UpdateLifecycleManagement", &UAuracronFoliageSeasonalManager::execUpdateLifecycleManagement },
		{ "UpdateMaterialParameters", &UAuracronFoliageSeasonalManager::execUpdateMaterialParameters },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageSeasonalManager::execUpdatePerformanceMetrics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager;
UClass* UAuracronFoliageSeasonalManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageSeasonalManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageSeasonalManager"),
			Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageSeasonalManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister()
{
	return UAuracronFoliageSeasonalManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Seasonal Manager\n * Manager for the foliage seasonal changes system including color variation, growth simulation, and lifecycle management\n */" },
#endif
		{ "IncludePath", "AuracronFoliageSeasonal.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Seasonal Manager\nManager for the foliage seasonal changes system including color variation, growth simulation, and lifecycle management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSeasonChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGrowthPhaseChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLifecycleEventTriggered_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnColorVariationUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalParameterCollection_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter collection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter collection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalParameterInstance_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageSeasonal.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSeasonChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGrowthPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLifecycleEventTriggered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnColorVariationUpdated;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_SeasonalParameterCollection;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_SeasonalParameterInstance;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_BiomeManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WindManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_AdvanceSeasonByDays, "AdvanceSeasonByDays" }, // 845624325
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_ApplyDensityChangesToBiome, "ApplyDensityChangesToBiome" }, // 3445592880
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateGrowthSimulation, "CreateGrowthSimulation" }, // 1569192246
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_CreateLifecycleManagement, "CreateLifecycleManagement" }, // 1884833393
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_DrawDebugSeasonalInfo, "DrawDebugSeasonalInfo" }, // 2798422776
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 1435763517
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetActiveSeasonalInstanceCount, "GetActiveSeasonalInstanceCount" }, // 320522820
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetConfiguration, "GetConfiguration" }, // 2700737597
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameDay, "GetCurrentGameDay" }, // 3659929407
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentGameTime, "GetCurrentGameTime" }, // 3277550718
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeason, "GetCurrentSeason" }, // 3106345332
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetCurrentSeasonalColor, "GetCurrentSeasonalColor" }, // 3411082672
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowingInstanceCount, "GetGrowingInstanceCount" }, // 1329996274
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetGrowthSimulation, "GetGrowthSimulation" }, // 3073959335
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetInstance, "GetInstance" }, // 3215991704
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetLifecycleManagement, "GetLifecycleManagement" }, // 2343251170
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetMaterialParameterCollection, "GetMaterialParameterCollection" }, // 3672597482
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetPerformanceData, "GetPerformanceData" }, // 3535140047
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalColors, "GetSeasonalColors" }, // 2446120609
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonalDensityMultiplier, "GetSeasonalDensityMultiplier" }, // 1754506537
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetSeasonProgress, "GetSeasonProgress" }, // 2805738442
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_GetTimeAcceleration, "GetTimeAcceleration" }, // 1043258868
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Initialize, "Initialize" }, // 2783231168
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithBiomeSystem, "IntegrateWithBiomeSystem" }, // 750589038
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IntegrateWithWindSystem, "IntegrateWithWindSystem" }, // 3380179701
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2567846054
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_IsInitialized, "IsInitialized" }, // 2336087346
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_LogSeasonalStatistics, "LogSeasonalStatistics" }, // 3093185444
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature, "OnColorVariationUpdated__DelegateSignature" }, // 1647311645
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature, "OnGrowthPhaseChanged__DelegateSignature" }, // 3897419064
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature, "OnLifecycleEventTriggered__DelegateSignature" }, // 70273203
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature, "OnSeasonChanged__DelegateSignature" }, // 3016788884
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveGrowthSimulation, "RemoveGrowthSimulation" }, // 4013727080
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_RemoveLifecycleManagement, "RemoveLifecycleManagement" }, // 3555411199
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetConfiguration, "SetConfiguration" }, // 4073488576
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetCurrentSeason, "SetCurrentSeason" }, // 1506936942
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetMaterialParameterCollection, "SetMaterialParameterCollection" }, // 1035885771
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonalColors, "SetSeasonalColors" }, // 3236343904
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetSeasonProgress, "SetSeasonProgress" }, // 592962856
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SetTimeAcceleration, "SetTimeAcceleration" }, // 2005215953
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Shutdown, "Shutdown" }, // 3479141340
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_SynchronizeWithClimateData, "SynchronizeWithClimateData" }, // 1460514027
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_Tick, "Tick" }, // 2417333464
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_TriggerLifecycleEvent, "TriggerLifecycleEvent" }, // 1386155398
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateAllLifecycleManagement, "UpdateAllLifecycleManagement" }, // 1039834062
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateColorVariation, "UpdateColorVariation" }, // 2878147272
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateDensityChanges, "UpdateDensityChanges" }, // 2400402011
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulation, "UpdateGrowthSimulation" }, // 1698140955
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateGrowthSimulations, "UpdateGrowthSimulations" }, // 2299459513
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateLifecycleManagement, "UpdateLifecycleManagement" }, // 1530256525
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdateMaterialParameters, "UpdateMaterialParameters" }, // 1068684924
		{ &Z_Construct_UFunction_UAuracronFoliageSeasonalManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 2677230858
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageSeasonalManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnSeasonChanged = { "OnSeasonChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, OnSeasonChanged), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnSeasonChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSeasonChanged_MetaData), NewProp_OnSeasonChanged_MetaData) }; // 3016788884
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnGrowthPhaseChanged = { "OnGrowthPhaseChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, OnGrowthPhaseChanged), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnGrowthPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGrowthPhaseChanged_MetaData), NewProp_OnGrowthPhaseChanged_MetaData) }; // 3897419064
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnLifecycleEventTriggered = { "OnLifecycleEventTriggered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, OnLifecycleEventTriggered), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnLifecycleEventTriggered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLifecycleEventTriggered_MetaData), NewProp_OnLifecycleEventTriggered_MetaData) }; // 70273203
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnColorVariationUpdated = { "OnColorVariationUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, OnColorVariationUpdated), Z_Construct_UDelegateFunction_UAuracronFoliageSeasonalManager_OnColorVariationUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnColorVariationUpdated_MetaData), NewProp_OnColorVariationUpdated_MetaData) }; // 1647311645
void Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageSeasonalManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageSeasonalManager), &Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, Configuration), Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2084404029
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_SeasonalParameterCollection = { "SeasonalParameterCollection", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, SeasonalParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalParameterCollection_MetaData), NewProp_SeasonalParameterCollection_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_SeasonalParameterInstance = { "SeasonalParameterInstance", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, SeasonalParameterInstance), Z_Construct_UClass_UMaterialParameterCollectionInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalParameterInstance_MetaData), NewProp_SeasonalParameterInstance_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_BiomeManager = { "BiomeManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, BiomeManager), Z_Construct_UClass_UAuracronFoliageBiomeManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeManager_MetaData), NewProp_BiomeManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_WindManager = { "WindManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageSeasonalManager, WindManager), Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindManager_MetaData), NewProp_WindManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnSeasonChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnGrowthPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnLifecycleEventTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_OnColorVariationUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_SeasonalParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_SeasonalParameterInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_BiomeManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::NewProp_WindManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::ClassParams = {
	&UAuracronFoliageSeasonalManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager.OuterSingleton;
}
UAuracronFoliageSeasonalManager::UAuracronFoliageSeasonalManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageSeasonalManager);
UAuracronFoliageSeasonalManager::~UAuracronFoliageSeasonalManager() {}
// ********** End Class UAuracronFoliageSeasonalManager ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronSeasonType_StaticEnum, TEXT("EAuracronSeasonType"), &Z_Registration_Info_UEnum_EAuracronSeasonType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1660267735U) },
		{ EAuracronGrowthPhase_StaticEnum, TEXT("EAuracronGrowthPhase"), &Z_Registration_Info_UEnum_EAuracronGrowthPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3594492358U) },
		{ EAuracronSeasonalChangeType_StaticEnum, TEXT("EAuracronSeasonalChangeType"), &Z_Registration_Info_UEnum_EAuracronSeasonalChangeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2334684605U) },
		{ EAuracronLifecycleEvent_StaticEnum, TEXT("EAuracronLifecycleEvent"), &Z_Registration_Info_UEnum_EAuracronLifecycleEvent, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1576891042U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronSeasonalConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics::NewStructOps, TEXT("AuracronSeasonalConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronSeasonalConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSeasonalConfiguration), 2084404029U) },
		{ FAuracronSeasonalColorData::StaticStruct, Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics::NewStructOps, TEXT("AuracronSeasonalColorData"), &Z_Registration_Info_UScriptStruct_FAuracronSeasonalColorData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSeasonalColorData), 1578471479U) },
		{ FAuracronGrowthSimulationData::StaticStruct, Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics::NewStructOps, TEXT("AuracronGrowthSimulationData"), &Z_Registration_Info_UScriptStruct_FAuracronGrowthSimulationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGrowthSimulationData), 2797846052U) },
		{ FAuracronLifecycleData::StaticStruct, Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics::NewStructOps, TEXT("AuracronLifecycleData"), &Z_Registration_Info_UScriptStruct_FAuracronLifecycleData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLifecycleData), 54288141U) },
		{ FAuracronSeasonalPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics::NewStructOps, TEXT("AuracronSeasonalPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronSeasonalPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSeasonalPerformanceData), 901706888U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageSeasonalManager, UAuracronFoliageSeasonalManager::StaticClass, TEXT("UAuracronFoliageSeasonalManager"), &Z_Registration_Info_UClass_UAuracronFoliageSeasonalManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageSeasonalManager), 2801616059U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_942923438(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
