// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGMaterialSystem.h"
#include "PCGPoint.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGMaterialSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveLinearColor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
PCG_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPoint();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGMaterialSelectionMode *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode;
static UEnum* EAuracronPCGMaterialSelectionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMaterialSelectionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialSelectionMode>()
{
	return EAuracronPCGMaterialSelectionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ByAttribute.DisplayName", "By Attribute" },
		{ "ByAttribute.Name", "EAuracronPCGMaterialSelectionMode::ByAttribute" },
		{ "ByBiome.DisplayName", "By Biome" },
		{ "ByBiome.Name", "EAuracronPCGMaterialSelectionMode::ByBiome" },
		{ "ByDensity.DisplayName", "By Density" },
		{ "ByDensity.Name", "EAuracronPCGMaterialSelectionMode::ByDensity" },
		{ "ByDistance.DisplayName", "By Distance" },
		{ "ByDistance.Name", "EAuracronPCGMaterialSelectionMode::ByDistance" },
		{ "ByHeight.DisplayName", "By Height" },
		{ "ByHeight.Name", "EAuracronPCGMaterialSelectionMode::ByHeight" },
		{ "ByLighting.DisplayName", "By Lighting" },
		{ "ByLighting.Name", "EAuracronPCGMaterialSelectionMode::ByLighting" },
		{ "ByNoise.DisplayName", "By Noise" },
		{ "ByNoise.Name", "EAuracronPCGMaterialSelectionMode::ByNoise" },
		{ "ByProximity.DisplayName", "By Proximity" },
		{ "ByProximity.Name", "EAuracronPCGMaterialSelectionMode::ByProximity" },
		{ "ByRandom.DisplayName", "By Random" },
		{ "ByRandom.Name", "EAuracronPCGMaterialSelectionMode::ByRandom" },
		{ "BySlope.DisplayName", "By Slope" },
		{ "BySlope.Name", "EAuracronPCGMaterialSelectionMode::BySlope" },
		{ "ByWeather.DisplayName", "By Weather" },
		{ "ByWeather.Name", "EAuracronPCGMaterialSelectionMode::ByWeather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material selection modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGMaterialSelectionMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material selection modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMaterialSelectionMode::ByAttribute", (int64)EAuracronPCGMaterialSelectionMode::ByAttribute },
		{ "EAuracronPCGMaterialSelectionMode::ByDistance", (int64)EAuracronPCGMaterialSelectionMode::ByDistance },
		{ "EAuracronPCGMaterialSelectionMode::ByHeight", (int64)EAuracronPCGMaterialSelectionMode::ByHeight },
		{ "EAuracronPCGMaterialSelectionMode::BySlope", (int64)EAuracronPCGMaterialSelectionMode::BySlope },
		{ "EAuracronPCGMaterialSelectionMode::ByNoise", (int64)EAuracronPCGMaterialSelectionMode::ByNoise },
		{ "EAuracronPCGMaterialSelectionMode::ByDensity", (int64)EAuracronPCGMaterialSelectionMode::ByDensity },
		{ "EAuracronPCGMaterialSelectionMode::ByProximity", (int64)EAuracronPCGMaterialSelectionMode::ByProximity },
		{ "EAuracronPCGMaterialSelectionMode::ByRandom", (int64)EAuracronPCGMaterialSelectionMode::ByRandom },
		{ "EAuracronPCGMaterialSelectionMode::ByBiome", (int64)EAuracronPCGMaterialSelectionMode::ByBiome },
		{ "EAuracronPCGMaterialSelectionMode::ByLighting", (int64)EAuracronPCGMaterialSelectionMode::ByLighting },
		{ "EAuracronPCGMaterialSelectionMode::ByWeather", (int64)EAuracronPCGMaterialSelectionMode::ByWeather },
		{ "EAuracronPCGMaterialSelectionMode::Custom", (int64)EAuracronPCGMaterialSelectionMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMaterialSelectionMode",
	"EAuracronPCGMaterialSelectionMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGMaterialSelectionMode *******************************************

// ********** Begin Enum EAuracronPCGMaterialBlendMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode;
static UEnum* EAuracronPCGMaterialBlendMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMaterialBlendMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialBlendMode>()
{
	return EAuracronPCGMaterialBlendMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Additive.DisplayName", "Additive" },
		{ "Additive.Name", "EAuracronPCGMaterialBlendMode::Additive" },
		{ "BlueprintType", "true" },
		{ "ColorBurn.DisplayName", "Color Burn" },
		{ "ColorBurn.Name", "EAuracronPCGMaterialBlendMode::ColorBurn" },
		{ "ColorDodge.DisplayName", "Color Dodge" },
		{ "ColorDodge.Name", "EAuracronPCGMaterialBlendMode::ColorDodge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material blending modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGMaterialBlendMode::Custom" },
		{ "Difference.DisplayName", "Difference" },
		{ "Difference.Name", "EAuracronPCGMaterialBlendMode::Difference" },
		{ "Exclusion.DisplayName", "Exclusion" },
		{ "Exclusion.Name", "EAuracronPCGMaterialBlendMode::Exclusion" },
		{ "HardLight.DisplayName", "Hard Light" },
		{ "HardLight.Name", "EAuracronPCGMaterialBlendMode::HardLight" },
		{ "LinearBurn.DisplayName", "Linear Burn" },
		{ "LinearBurn.Name", "EAuracronPCGMaterialBlendMode::LinearBurn" },
		{ "LinearDodge.DisplayName", "Linear Dodge" },
		{ "LinearDodge.Name", "EAuracronPCGMaterialBlendMode::LinearDodge" },
		{ "LinearLight.DisplayName", "Linear Light" },
		{ "LinearLight.Name", "EAuracronPCGMaterialBlendMode::LinearLight" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronPCGMaterialBlendMode::Multiply" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "EAuracronPCGMaterialBlendMode::Overlay" },
		{ "PinLight.DisplayName", "Pin Light" },
		{ "PinLight.Name", "EAuracronPCGMaterialBlendMode::PinLight" },
		{ "Replace.DisplayName", "Replace" },
		{ "Replace.Name", "EAuracronPCGMaterialBlendMode::Replace" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "EAuracronPCGMaterialBlendMode::Screen" },
		{ "SoftLight.DisplayName", "Soft Light" },
		{ "SoftLight.Name", "EAuracronPCGMaterialBlendMode::SoftLight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material blending modes" },
#endif
		{ "VividLight.DisplayName", "Vivid Light" },
		{ "VividLight.Name", "EAuracronPCGMaterialBlendMode::VividLight" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMaterialBlendMode::Replace", (int64)EAuracronPCGMaterialBlendMode::Replace },
		{ "EAuracronPCGMaterialBlendMode::Additive", (int64)EAuracronPCGMaterialBlendMode::Additive },
		{ "EAuracronPCGMaterialBlendMode::Multiply", (int64)EAuracronPCGMaterialBlendMode::Multiply },
		{ "EAuracronPCGMaterialBlendMode::Overlay", (int64)EAuracronPCGMaterialBlendMode::Overlay },
		{ "EAuracronPCGMaterialBlendMode::SoftLight", (int64)EAuracronPCGMaterialBlendMode::SoftLight },
		{ "EAuracronPCGMaterialBlendMode::HardLight", (int64)EAuracronPCGMaterialBlendMode::HardLight },
		{ "EAuracronPCGMaterialBlendMode::Screen", (int64)EAuracronPCGMaterialBlendMode::Screen },
		{ "EAuracronPCGMaterialBlendMode::ColorBurn", (int64)EAuracronPCGMaterialBlendMode::ColorBurn },
		{ "EAuracronPCGMaterialBlendMode::ColorDodge", (int64)EAuracronPCGMaterialBlendMode::ColorDodge },
		{ "EAuracronPCGMaterialBlendMode::Difference", (int64)EAuracronPCGMaterialBlendMode::Difference },
		{ "EAuracronPCGMaterialBlendMode::Exclusion", (int64)EAuracronPCGMaterialBlendMode::Exclusion },
		{ "EAuracronPCGMaterialBlendMode::LinearBurn", (int64)EAuracronPCGMaterialBlendMode::LinearBurn },
		{ "EAuracronPCGMaterialBlendMode::LinearDodge", (int64)EAuracronPCGMaterialBlendMode::LinearDodge },
		{ "EAuracronPCGMaterialBlendMode::VividLight", (int64)EAuracronPCGMaterialBlendMode::VividLight },
		{ "EAuracronPCGMaterialBlendMode::LinearLight", (int64)EAuracronPCGMaterialBlendMode::LinearLight },
		{ "EAuracronPCGMaterialBlendMode::PinLight", (int64)EAuracronPCGMaterialBlendMode::PinLight },
		{ "EAuracronPCGMaterialBlendMode::Custom", (int64)EAuracronPCGMaterialBlendMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMaterialBlendMode",
	"EAuracronPCGMaterialBlendMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGMaterialBlendMode ***********************************************

// ********** Begin Enum EAuracronPCGUVGenerationMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode;
static UEnum* EAuracronPCGUVGenerationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGUVGenerationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGUVGenerationMode>()
{
	return EAuracronPCGUVGenerationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EAuracronPCGUVGenerationMode::Box" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV generation modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGUVGenerationMode::Custom" },
		{ "Cylindrical.DisplayName", "Cylindrical" },
		{ "Cylindrical.Name", "EAuracronPCGUVGenerationMode::Cylindrical" },
		{ "FromAttribute.DisplayName", "From Attribute" },
		{ "FromAttribute.Name", "EAuracronPCGUVGenerationMode::FromAttribute" },
		{ "LocalSpace.DisplayName", "Local Space" },
		{ "LocalSpace.Name", "EAuracronPCGUVGenerationMode::LocalSpace" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
		{ "Planar.DisplayName", "Planar" },
		{ "Planar.Name", "EAuracronPCGUVGenerationMode::Planar" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EAuracronPCGUVGenerationMode::Procedural" },
		{ "Spherical.DisplayName", "Spherical" },
		{ "Spherical.Name", "EAuracronPCGUVGenerationMode::Spherical" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV generation modes" },
#endif
		{ "Triplanar.DisplayName", "Triplanar" },
		{ "Triplanar.Name", "EAuracronPCGUVGenerationMode::Triplanar" },
		{ "WorldSpace.DisplayName", "World Space" },
		{ "WorldSpace.Name", "EAuracronPCGUVGenerationMode::WorldSpace" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGUVGenerationMode::WorldSpace", (int64)EAuracronPCGUVGenerationMode::WorldSpace },
		{ "EAuracronPCGUVGenerationMode::LocalSpace", (int64)EAuracronPCGUVGenerationMode::LocalSpace },
		{ "EAuracronPCGUVGenerationMode::Planar", (int64)EAuracronPCGUVGenerationMode::Planar },
		{ "EAuracronPCGUVGenerationMode::Cylindrical", (int64)EAuracronPCGUVGenerationMode::Cylindrical },
		{ "EAuracronPCGUVGenerationMode::Spherical", (int64)EAuracronPCGUVGenerationMode::Spherical },
		{ "EAuracronPCGUVGenerationMode::Box", (int64)EAuracronPCGUVGenerationMode::Box },
		{ "EAuracronPCGUVGenerationMode::Triplanar", (int64)EAuracronPCGUVGenerationMode::Triplanar },
		{ "EAuracronPCGUVGenerationMode::FromAttribute", (int64)EAuracronPCGUVGenerationMode::FromAttribute },
		{ "EAuracronPCGUVGenerationMode::Procedural", (int64)EAuracronPCGUVGenerationMode::Procedural },
		{ "EAuracronPCGUVGenerationMode::Custom", (int64)EAuracronPCGUVGenerationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGUVGenerationMode",
	"EAuracronPCGUVGenerationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGUVGenerationMode ************************************************

// ********** Begin Enum EAuracronPCGTextureCoordinateChannel **************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel;
static UEnum* EAuracronPCGTextureCoordinateChannel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGTextureCoordinateChannel"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTextureCoordinateChannel>()
{
	return EAuracronPCGTextureCoordinateChannel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Texture coordinate channels\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texture coordinate channels" },
#endif
		{ "UV0.DisplayName", "UV Channel 0" },
		{ "UV0.Name", "EAuracronPCGTextureCoordinateChannel::UV0" },
		{ "UV1.DisplayName", "UV Channel 1" },
		{ "UV1.Name", "EAuracronPCGTextureCoordinateChannel::UV1" },
		{ "UV2.DisplayName", "UV Channel 2" },
		{ "UV2.Name", "EAuracronPCGTextureCoordinateChannel::UV2" },
		{ "UV3.DisplayName", "UV Channel 3" },
		{ "UV3.Name", "EAuracronPCGTextureCoordinateChannel::UV3" },
		{ "UV4.DisplayName", "UV Channel 4" },
		{ "UV4.Name", "EAuracronPCGTextureCoordinateChannel::UV4" },
		{ "UV5.DisplayName", "UV Channel 5" },
		{ "UV5.Name", "EAuracronPCGTextureCoordinateChannel::UV5" },
		{ "UV6.DisplayName", "UV Channel 6" },
		{ "UV6.Name", "EAuracronPCGTextureCoordinateChannel::UV6" },
		{ "UV7.DisplayName", "UV Channel 7" },
		{ "UV7.Name", "EAuracronPCGTextureCoordinateChannel::UV7" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGTextureCoordinateChannel::UV0", (int64)EAuracronPCGTextureCoordinateChannel::UV0 },
		{ "EAuracronPCGTextureCoordinateChannel::UV1", (int64)EAuracronPCGTextureCoordinateChannel::UV1 },
		{ "EAuracronPCGTextureCoordinateChannel::UV2", (int64)EAuracronPCGTextureCoordinateChannel::UV2 },
		{ "EAuracronPCGTextureCoordinateChannel::UV3", (int64)EAuracronPCGTextureCoordinateChannel::UV3 },
		{ "EAuracronPCGTextureCoordinateChannel::UV4", (int64)EAuracronPCGTextureCoordinateChannel::UV4 },
		{ "EAuracronPCGTextureCoordinateChannel::UV5", (int64)EAuracronPCGTextureCoordinateChannel::UV5 },
		{ "EAuracronPCGTextureCoordinateChannel::UV6", (int64)EAuracronPCGTextureCoordinateChannel::UV6 },
		{ "EAuracronPCGTextureCoordinateChannel::UV7", (int64)EAuracronPCGTextureCoordinateChannel::UV7 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGTextureCoordinateChannel",
	"EAuracronPCGTextureCoordinateChannel",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel.InnerSingleton;
}
// ********** End Enum EAuracronPCGTextureCoordinateChannel ****************************************

// ********** Begin Enum EAuracronPCGMaterialParameterType *****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType;
static UEnum* EAuracronPCGMaterialParameterType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMaterialParameterType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialParameterType>()
{
	return EAuracronPCGMaterialParameterType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Boolean.DisplayName", "Boolean" },
		{ "Boolean.Name", "EAuracronPCGMaterialParameterType::Boolean" },
		{ "Color.DisplayName", "Color" },
		{ "Color.Name", "EAuracronPCGMaterialParameterType::Color" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGMaterialParameterType::Custom" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
		{ "Scalar.DisplayName", "Scalar" },
		{ "Scalar.Name", "EAuracronPCGMaterialParameterType::Scalar" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EAuracronPCGMaterialParameterType::Texture" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter types" },
#endif
		{ "Vector.DisplayName", "Vector" },
		{ "Vector.Name", "EAuracronPCGMaterialParameterType::Vector" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMaterialParameterType::Scalar", (int64)EAuracronPCGMaterialParameterType::Scalar },
		{ "EAuracronPCGMaterialParameterType::Vector", (int64)EAuracronPCGMaterialParameterType::Vector },
		{ "EAuracronPCGMaterialParameterType::Color", (int64)EAuracronPCGMaterialParameterType::Color },
		{ "EAuracronPCGMaterialParameterType::Texture", (int64)EAuracronPCGMaterialParameterType::Texture },
		{ "EAuracronPCGMaterialParameterType::Boolean", (int64)EAuracronPCGMaterialParameterType::Boolean },
		{ "EAuracronPCGMaterialParameterType::Custom", (int64)EAuracronPCGMaterialParameterType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMaterialParameterType",
	"EAuracronPCGMaterialParameterType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType.InnerSingleton;
}
// ********** End Enum EAuracronPCGMaterialParameterType *******************************************

// ********** Begin ScriptStruct FAuracronPCGMaterialSelectionDescriptor ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor;
class UScriptStruct* FAuracronPCGMaterialSelectionDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGMaterialSelectionDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material Selection Descriptor\n * Describes parameters for material selection and assignment\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Selection Descriptor\nDescribes parameters for material selection and assignment" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionMode_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOptions_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialTags_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionWeights_MetaData[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferenceLocation_MetaData[] = {
		{ "Category", "Distance Based" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceThresholds_MetaData[] = {
		{ "Category", "Distance Based" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightThresholds_MetaData[] = {
		{ "Category", "Height Based" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMaterialSelectionMode::ByHeight" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeThresholds_MetaData[] = {
		{ "Category", "Slope Based" },
		{ "EditCondition", "SelectionMode == EAuracronPCGMaterialSelectionMode::BySlope" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDeterministicSelection_MetaData[] = {
		{ "Category", "Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FallbackMaterial_MetaData[] = {
		{ "Category", "Fallback" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWeightedSelection_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowMaterialBlending_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SelectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SelectionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOptions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialOptions;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialTags;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SelectionWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectionWeights;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferenceLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceThresholds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DistanceThresholds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightThresholds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HeightThresholds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SlopeThresholds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SlopeThresholds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static void NewProp_bDeterministicSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDeterministicSelection;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FallbackMaterial;
	static void NewProp_bUseWeightedSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWeightedSelection;
	static void NewProp_bAllowMaterialBlending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowMaterialBlending;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGMaterialSelectionDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionMode = { "SelectionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, SelectionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionMode_MetaData), NewProp_SelectionMode_MetaData) }; // 2127283331
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialOptions_Inner = { "MaterialOptions", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialOptions = { "MaterialOptions", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, MaterialOptions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOptions_MetaData), NewProp_MaterialOptions_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialTags_Inner = { "MaterialTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialTags = { "MaterialTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, MaterialTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialTags_MetaData), NewProp_MaterialTags_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionWeights_Inner = { "SelectionWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionWeights = { "SelectionWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, SelectionWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionWeights_MetaData), NewProp_SelectionWeights_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_ReferenceLocation = { "ReferenceLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, ReferenceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferenceLocation_MetaData), NewProp_ReferenceLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_DistanceThresholds_Inner = { "DistanceThresholds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_DistanceThresholds = { "DistanceThresholds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, DistanceThresholds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceThresholds_MetaData), NewProp_DistanceThresholds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_HeightThresholds_Inner = { "HeightThresholds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_HeightThresholds = { "HeightThresholds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, HeightThresholds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightThresholds_MetaData), NewProp_HeightThresholds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SlopeThresholds_Inner = { "SlopeThresholds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SlopeThresholds = { "SlopeThresholds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, SlopeThresholds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeThresholds_MetaData), NewProp_SlopeThresholds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bDeterministicSelection_SetBit(void* Obj)
{
	((FAuracronPCGMaterialSelectionDescriptor*)Obj)->bDeterministicSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bDeterministicSelection = { "bDeterministicSelection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialSelectionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bDeterministicSelection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDeterministicSelection_MetaData), NewProp_bDeterministicSelection_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_FallbackMaterial = { "FallbackMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialSelectionDescriptor, FallbackMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FallbackMaterial_MetaData), NewProp_FallbackMaterial_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bUseWeightedSelection_SetBit(void* Obj)
{
	((FAuracronPCGMaterialSelectionDescriptor*)Obj)->bUseWeightedSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bUseWeightedSelection = { "bUseWeightedSelection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialSelectionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bUseWeightedSelection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWeightedSelection_MetaData), NewProp_bUseWeightedSelection_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bAllowMaterialBlending_SetBit(void* Obj)
{
	((FAuracronPCGMaterialSelectionDescriptor*)Obj)->bAllowMaterialBlending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bAllowMaterialBlending = { "bAllowMaterialBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialSelectionDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bAllowMaterialBlending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowMaterialBlending_MetaData), NewProp_bAllowMaterialBlending_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialOptions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialOptions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_MaterialTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SelectionWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_ReferenceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_DistanceThresholds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_DistanceThresholds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_HeightThresholds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_HeightThresholds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SlopeThresholds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_SlopeThresholds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_RandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bDeterministicSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_FallbackMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bUseWeightedSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewProp_bAllowMaterialBlending,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGMaterialSelectionDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGMaterialSelectionDescriptor),
	alignof(FAuracronPCGMaterialSelectionDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGMaterialSelectionDescriptor *****************************

// ********** Begin ScriptStruct FAuracronPCGMaterialBlendingDescriptor ****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor;
class UScriptStruct* FAuracronPCGMaterialBlendingDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGMaterialBlendingDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material Blending Descriptor\n * Describes parameters for material blending operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Blending Descriptor\nDescribes parameters for material blending operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendMode_MetaData[] = {
		{ "Category", "Blending" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendStrength_MetaData[] = {
		{ "Category", "Blending" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBlendMask_MetaData[] = {
		{ "Category", "Blending" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendMask_MetaData[] = {
		{ "Category", "Blending" },
		{ "EditCondition", "bUseBlendMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertBlendMask_MetaData[] = {
		{ "Category", "Blending" },
		{ "EditCondition", "bUseBlendMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionWidth_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSmoothTransition_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionCurve_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreserveOriginalAlpha_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeBlendWeights_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BlendMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BlendMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendStrength;
	static void NewProp_bUseBlendMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBlendMask;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BlendMask;
	static void NewProp_bInvertBlendMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertBlendMask;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionWidth;
	static void NewProp_bSmoothTransition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSmoothTransition;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionCurve;
	static void NewProp_bPreserveOriginalAlpha_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreserveOriginalAlpha;
	static void NewProp_bNormalizeBlendWeights_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeBlendWeights;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGMaterialBlendingDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMode = { "BlendMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialBlendingDescriptor, BlendMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialBlendMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendMode_MetaData), NewProp_BlendMode_MetaData) }; // 1425128422
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendStrength = { "BlendStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialBlendingDescriptor, BlendStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendStrength_MetaData), NewProp_BlendStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bUseBlendMask_SetBit(void* Obj)
{
	((FAuracronPCGMaterialBlendingDescriptor*)Obj)->bUseBlendMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bUseBlendMask = { "bUseBlendMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialBlendingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bUseBlendMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBlendMask_MetaData), NewProp_bUseBlendMask_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMask = { "BlendMask", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialBlendingDescriptor, BlendMask), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendMask_MetaData), NewProp_BlendMask_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bInvertBlendMask_SetBit(void* Obj)
{
	((FAuracronPCGMaterialBlendingDescriptor*)Obj)->bInvertBlendMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bInvertBlendMask = { "bInvertBlendMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialBlendingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bInvertBlendMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertBlendMask_MetaData), NewProp_bInvertBlendMask_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_TransitionWidth = { "TransitionWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialBlendingDescriptor, TransitionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionWidth_MetaData), NewProp_TransitionWidth_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bSmoothTransition_SetBit(void* Obj)
{
	((FAuracronPCGMaterialBlendingDescriptor*)Obj)->bSmoothTransition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bSmoothTransition = { "bSmoothTransition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialBlendingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bSmoothTransition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSmoothTransition_MetaData), NewProp_bSmoothTransition_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_TransitionCurve = { "TransitionCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialBlendingDescriptor, TransitionCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionCurve_MetaData), NewProp_TransitionCurve_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bPreserveOriginalAlpha_SetBit(void* Obj)
{
	((FAuracronPCGMaterialBlendingDescriptor*)Obj)->bPreserveOriginalAlpha = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bPreserveOriginalAlpha = { "bPreserveOriginalAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialBlendingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bPreserveOriginalAlpha_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreserveOriginalAlpha_MetaData), NewProp_bPreserveOriginalAlpha_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bNormalizeBlendWeights_SetBit(void* Obj)
{
	((FAuracronPCGMaterialBlendingDescriptor*)Obj)->bNormalizeBlendWeights = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bNormalizeBlendWeights = { "bNormalizeBlendWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialBlendingDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bNormalizeBlendWeights_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeBlendWeights_MetaData), NewProp_bNormalizeBlendWeights_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bUseBlendMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_BlendMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bInvertBlendMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_TransitionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bSmoothTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_TransitionCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bPreserveOriginalAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewProp_bNormalizeBlendWeights,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGMaterialBlendingDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGMaterialBlendingDescriptor),
	alignof(FAuracronPCGMaterialBlendingDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGMaterialBlendingDescriptor ******************************

// ********** Begin ScriptStruct FAuracronPCGUVGenerationDescriptor ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor;
class UScriptStruct* FAuracronPCGUVGenerationDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGUVGenerationDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UV Generation Descriptor\n * Describes parameters for UV coordinate generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV Generation Descriptor\nDescribes parameters for UV coordinate generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationMode_MetaData[] = {
		{ "Category", "UV Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetChannel_MetaData[] = {
		{ "Category", "UV Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVScale_MetaData[] = {
		{ "Category", "Scale" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVOffset_MetaData[] = {
		{ "Category", "Scale" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVRotation_MetaData[] = {
		{ "Category", "Scale" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionAxis_MetaData[] = {
		{ "Category", "Projection" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::Planar" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionCenter_MetaData[] = {
		{ "Category", "Projection" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::Cylindrical || GenerationMode == EAuracronPCGUVGenerationMode::Spherical" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CylinderRadius_MetaData[] = {
		{ "Category", "Projection" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::Cylindrical" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SphereRadius_MetaData[] = {
		{ "Category", "Projection" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::Spherical" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriplanarBlendSharpness_MetaData[] = {
		{ "Category", "Triplanar" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::Triplanar" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UAttributeName_MetaData[] = {
		{ "Category", "Attribute" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::FromAttribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VAttributeName_MetaData[] = {
		{ "Category", "Attribute" },
		{ "EditCondition", "GenerationMode == EAuracronPCGUVGenerationMode::FromAttribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeUVs_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWrapUVs_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFlipU_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFlipV_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetChannel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetChannel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UVRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProjectionAxis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProjectionCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CylinderRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SphereRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TriplanarBlendSharpness;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UAttributeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VAttributeName;
	static void NewProp_bNormalizeUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeUVs;
	static void NewProp_bWrapUVs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWrapUVs;
	static void NewProp_bFlipU_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFlipU;
	static void NewProp_bFlipV_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFlipV;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGUVGenerationDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_GenerationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_GenerationMode = { "GenerationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, GenerationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGUVGenerationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationMode_MetaData), NewProp_GenerationMode_MetaData) }; // 1155836973
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TargetChannel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TargetChannel = { "TargetChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, TargetChannel), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGTextureCoordinateChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetChannel_MetaData), NewProp_TargetChannel_MetaData) }; // 625166426
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVScale = { "UVScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, UVScale), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVScale_MetaData), NewProp_UVScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVOffset = { "UVOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, UVOffset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVOffset_MetaData), NewProp_UVOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVRotation = { "UVRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, UVRotation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVRotation_MetaData), NewProp_UVRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_ProjectionAxis = { "ProjectionAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, ProjectionAxis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionAxis_MetaData), NewProp_ProjectionAxis_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_ProjectionCenter = { "ProjectionCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, ProjectionCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionCenter_MetaData), NewProp_ProjectionCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_CylinderRadius = { "CylinderRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, CylinderRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CylinderRadius_MetaData), NewProp_CylinderRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_SphereRadius = { "SphereRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, SphereRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SphereRadius_MetaData), NewProp_SphereRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TriplanarBlendSharpness = { "TriplanarBlendSharpness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, TriplanarBlendSharpness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriplanarBlendSharpness_MetaData), NewProp_TriplanarBlendSharpness_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UAttributeName = { "UAttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, UAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UAttributeName_MetaData), NewProp_UAttributeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_VAttributeName = { "VAttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGUVGenerationDescriptor, VAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VAttributeName_MetaData), NewProp_VAttributeName_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bNormalizeUVs_SetBit(void* Obj)
{
	((FAuracronPCGUVGenerationDescriptor*)Obj)->bNormalizeUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bNormalizeUVs = { "bNormalizeUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGUVGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bNormalizeUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeUVs_MetaData), NewProp_bNormalizeUVs_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bWrapUVs_SetBit(void* Obj)
{
	((FAuracronPCGUVGenerationDescriptor*)Obj)->bWrapUVs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bWrapUVs = { "bWrapUVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGUVGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bWrapUVs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWrapUVs_MetaData), NewProp_bWrapUVs_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipU_SetBit(void* Obj)
{
	((FAuracronPCGUVGenerationDescriptor*)Obj)->bFlipU = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipU = { "bFlipU", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGUVGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipU_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFlipU_MetaData), NewProp_bFlipU_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipV_SetBit(void* Obj)
{
	((FAuracronPCGUVGenerationDescriptor*)Obj)->bFlipV = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipV = { "bFlipV", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGUVGenerationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipV_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFlipV_MetaData), NewProp_bFlipV_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_GenerationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_GenerationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TargetChannel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TargetChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UVRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_ProjectionAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_ProjectionCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_CylinderRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_SphereRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_TriplanarBlendSharpness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_UAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_VAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bNormalizeUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bWrapUVs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipU,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewProp_bFlipV,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGUVGenerationDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGUVGenerationDescriptor),
	alignof(FAuracronPCGUVGenerationDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGUVGenerationDescriptor **********************************

// ********** Begin ScriptStruct FAuracronPCGMaterialParameterDescriptor ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor;
class UScriptStruct* FAuracronPCGMaterialParameterDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGMaterialParameterDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material Parameter Descriptor\n * Describes parameters for dynamic material parameter assignment\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Parameter Descriptor\nDescribes parameters for dynamic material parameter assignment" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterType_MetaData[] = {
		{ "Category", "Parameter" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceAttributeName_MetaData[] = {
		{ "Category", "Source" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarValue_MetaData[] = {
		{ "Category", "Scalar" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Scalar" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarRange_MetaData[] = {
		{ "Category", "Scalar" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Scalar" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorValue_MetaData[] = {
		{ "Category", "Vector" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Vector" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorValue_MetaData[] = {
		{ "Category", "Color" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Color" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorCurve_MetaData[] = {
		{ "Category", "Color" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Color" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureValue_MetaData[] = {
		{ "Category", "Texture" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Texture" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BooleanValue_MetaData[] = {
		{ "Category", "Boolean" },
		{ "EditCondition", "ParameterType == EAuracronPCGMaterialParameterType::Boolean" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeAsSource_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeValue_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ParameterType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ParameterType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceAttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScalarValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScalarRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorValue;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ColorCurve;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TextureValue;
	static void NewProp_BooleanValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_BooleanValue;
	static void NewProp_bUseAttributeAsSource_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeAsSource;
	static void NewProp_bNormalizeValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGMaterialParameterDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterType = { "ParameterType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ParameterType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialParameterType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterType_MetaData), NewProp_ParameterType_MetaData) }; // 2367748895
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_SourceAttributeName = { "SourceAttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, SourceAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceAttributeName_MetaData), NewProp_SourceAttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ScalarValue = { "ScalarValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ScalarValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarValue_MetaData), NewProp_ScalarValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ScalarRange = { "ScalarRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ScalarRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarRange_MetaData), NewProp_ScalarRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_VectorValue = { "VectorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, VectorValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorValue_MetaData), NewProp_VectorValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ColorValue = { "ColorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ColorValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorValue_MetaData), NewProp_ColorValue_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ColorCurve = { "ColorCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, ColorCurve), Z_Construct_UClass_UCurveLinearColor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorCurve_MetaData), NewProp_ColorCurve_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_TextureValue = { "TextureValue", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGMaterialParameterDescriptor, TextureValue), Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureValue_MetaData), NewProp_TextureValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_BooleanValue_SetBit(void* Obj)
{
	((FAuracronPCGMaterialParameterDescriptor*)Obj)->BooleanValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_BooleanValue = { "BooleanValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_BooleanValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BooleanValue_MetaData), NewProp_BooleanValue_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bUseAttributeAsSource_SetBit(void* Obj)
{
	((FAuracronPCGMaterialParameterDescriptor*)Obj)->bUseAttributeAsSource = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bUseAttributeAsSource = { "bUseAttributeAsSource", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bUseAttributeAsSource_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeAsSource_MetaData), NewProp_bUseAttributeAsSource_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bNormalizeValue_SetBit(void* Obj)
{
	((FAuracronPCGMaterialParameterDescriptor*)Obj)->bNormalizeValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bNormalizeValue = { "bNormalizeValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGMaterialParameterDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bNormalizeValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeValue_MetaData), NewProp_bNormalizeValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ParameterType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_SourceAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ScalarValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ScalarRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_VectorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ColorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_ColorCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_TextureValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_BooleanValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bUseAttributeAsSource,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewProp_bNormalizeValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGMaterialParameterDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGMaterialParameterDescriptor),
	alignof(FAuracronPCGMaterialParameterDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGMaterialParameterDescriptor *****************************

// ********** Begin Class UAuracronPCGAdvancedMaterialSelectorSettings *****************************
void UAuracronPCGAdvancedMaterialSelectorSettings::StaticRegisterNativesUAuracronPCGAdvancedMaterialSelectorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings;
UClass* UAuracronPCGAdvancedMaterialSelectorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedMaterialSelectorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedMaterialSelectorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedMaterialSelectorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_NoRegister()
{
	return UAuracronPCGAdvancedMaterialSelectorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMaterialSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionDescriptor_MetaData[] = {
		{ "Category", "Material Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material selection configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material selection configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultipleCriteria_MetaData[] = {
		{ "Category", "Multi-Criteria" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiple selection criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiple selection criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalCriteria_MetaData[] = {
		{ "Category", "Multi-Criteria" },
		{ "EditCondition", "bUseMultipleCriteria" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriteriaWeights_MetaData[] = {
		{ "Category", "Multi-Criteria" },
		{ "EditCondition", "bUseMultipleCriteria" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMaterialVariation_MetaData[] = {
		{ "Category", "Variation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material variation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationStrength_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bEnableMaterialVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationSeed_MetaData[] = {
		{ "Category", "Variation" },
		{ "EditCondition", "bEnableMaterialVariation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputSelectionInfo_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialIndexAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialNameAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionDescriptor;
	static void NewProp_bUseMultipleCriteria_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultipleCriteria;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdditionalCriteria_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdditionalCriteria;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriteriaWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CriteriaWeights;
	static void NewProp_bEnableMaterialVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMaterialVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VariationSeed;
	static void NewProp_bOutputSelectionInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputSelectionInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialIndexAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialNameAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedMaterialSelectorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_SelectionDescriptor = { "SelectionDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, SelectionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionDescriptor_MetaData), NewProp_SelectionDescriptor_MetaData) }; // 3131025772
void Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bUseMultipleCriteria_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMaterialSelectorSettings*)Obj)->bUseMultipleCriteria = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bUseMultipleCriteria = { "bUseMultipleCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMaterialSelectorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bUseMultipleCriteria_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultipleCriteria_MetaData), NewProp_bUseMultipleCriteria_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_AdditionalCriteria_Inner = { "AdditionalCriteria", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(0, nullptr) }; // 3131025772
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_AdditionalCriteria = { "AdditionalCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, AdditionalCriteria), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalCriteria_MetaData), NewProp_AdditionalCriteria_MetaData) }; // 3131025772
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_CriteriaWeights_Inner = { "CriteriaWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_CriteriaWeights = { "CriteriaWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, CriteriaWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriteriaWeights_MetaData), NewProp_CriteriaWeights_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bEnableMaterialVariation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMaterialSelectorSettings*)Obj)->bEnableMaterialVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bEnableMaterialVariation = { "bEnableMaterialVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMaterialSelectorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bEnableMaterialVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMaterialVariation_MetaData), NewProp_bEnableMaterialVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_VariationStrength = { "VariationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, VariationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationStrength_MetaData), NewProp_VariationStrength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_VariationSeed = { "VariationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, VariationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationSeed_MetaData), NewProp_VariationSeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bOutputSelectionInfo_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedMaterialSelectorSettings*)Obj)->bOutputSelectionInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bOutputSelectionInfo = { "bOutputSelectionInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedMaterialSelectorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bOutputSelectionInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputSelectionInfo_MetaData), NewProp_bOutputSelectionInfo_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_MaterialIndexAttribute = { "MaterialIndexAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, MaterialIndexAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialIndexAttribute_MetaData), NewProp_MaterialIndexAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_MaterialNameAttribute = { "MaterialNameAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedMaterialSelectorSettings, MaterialNameAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialNameAttribute_MetaData), NewProp_MaterialNameAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_SelectionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bUseMultipleCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_AdditionalCriteria_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_AdditionalCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_CriteriaWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_CriteriaWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bEnableMaterialVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_VariationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_VariationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_bOutputSelectionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_MaterialIndexAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::NewProp_MaterialNameAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedMaterialSelectorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedMaterialSelectorSettings);
UAuracronPCGAdvancedMaterialSelectorSettings::~UAuracronPCGAdvancedMaterialSelectorSettings() {}
// ********** End Class UAuracronPCGAdvancedMaterialSelectorSettings *******************************

// ********** Begin Class UAuracronPCGMaterialBlenderSettings **************************************
void UAuracronPCGMaterialBlenderSettings::StaticRegisterNativesUAuracronPCGMaterialBlenderSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings;
UClass* UAuracronPCGMaterialBlenderSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMaterialBlenderSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMaterialBlenderSettings"),
			Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMaterialBlenderSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_NoRegister()
{
	return UAuracronPCGMaterialBlenderSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMaterialSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingDescriptor_MetaData[] = {
		{ "Category", "Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blending configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blending configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMaterials_MetaData[] = {
		{ "Category", "Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Source materials\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Source materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialWeights_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeAsMask_MetaData[] = {
		{ "Category", "Masks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blending masks\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blending masks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaskAttributeName_MetaData[] = {
		{ "Category", "Masks" },
		{ "EditCondition", "bUseAttributeAsMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseProceduralMask_MetaData[] = {
		{ "Category", "Masks" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaskNoiseScale_MetaData[] = {
		{ "Category", "Masks" },
		{ "EditCondition", "bUseProceduralMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaskNoiseSeed_MetaData[] = {
		{ "Category", "Masks" },
		{ "EditCondition", "bUseProceduralMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateDynamicMaterial_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced blending\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced blending" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeBlending_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBlendLayers_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputBlendWeights_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeightAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingDescriptor;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SourceMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SourceMaterials;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialWeights;
	static void NewProp_bUseAttributeAsMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeAsMask;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaskAttributeName;
	static void NewProp_bUseProceduralMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseProceduralMask;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaskNoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaskNoiseSeed;
	static void NewProp_bCreateDynamicMaterial_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateDynamicMaterial;
	static void NewProp_bOptimizeBlending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeBlending;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBlendLayers;
	static void NewProp_bOutputBlendWeights_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputBlendWeights;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendWeightAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMaterialBlenderSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_BlendingDescriptor = { "BlendingDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, BlendingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingDescriptor_MetaData), NewProp_BlendingDescriptor_MetaData) }; // 3080984592
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_SourceMaterials_Inner = { "SourceMaterials", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_SourceMaterials = { "SourceMaterials", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, SourceMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMaterials_MetaData), NewProp_SourceMaterials_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaterialWeights_Inner = { "MaterialWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaterialWeights = { "MaterialWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, MaterialWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialWeights_MetaData), NewProp_MaterialWeights_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseAttributeAsMask_SetBit(void* Obj)
{
	((UAuracronPCGMaterialBlenderSettings*)Obj)->bUseAttributeAsMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseAttributeAsMask = { "bUseAttributeAsMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialBlenderSettings), &Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseAttributeAsMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeAsMask_MetaData), NewProp_bUseAttributeAsMask_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskAttributeName = { "MaskAttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, MaskAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaskAttributeName_MetaData), NewProp_MaskAttributeName_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseProceduralMask_SetBit(void* Obj)
{
	((UAuracronPCGMaterialBlenderSettings*)Obj)->bUseProceduralMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseProceduralMask = { "bUseProceduralMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialBlenderSettings), &Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseProceduralMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseProceduralMask_MetaData), NewProp_bUseProceduralMask_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskNoiseScale = { "MaskNoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, MaskNoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaskNoiseScale_MetaData), NewProp_MaskNoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskNoiseSeed = { "MaskNoiseSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, MaskNoiseSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaskNoiseSeed_MetaData), NewProp_MaskNoiseSeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bCreateDynamicMaterial_SetBit(void* Obj)
{
	((UAuracronPCGMaterialBlenderSettings*)Obj)->bCreateDynamicMaterial = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bCreateDynamicMaterial = { "bCreateDynamicMaterial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialBlenderSettings), &Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bCreateDynamicMaterial_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateDynamicMaterial_MetaData), NewProp_bCreateDynamicMaterial_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOptimizeBlending_SetBit(void* Obj)
{
	((UAuracronPCGMaterialBlenderSettings*)Obj)->bOptimizeBlending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOptimizeBlending = { "bOptimizeBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialBlenderSettings), &Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOptimizeBlending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeBlending_MetaData), NewProp_bOptimizeBlending_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaxBlendLayers = { "MaxBlendLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, MaxBlendLayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBlendLayers_MetaData), NewProp_MaxBlendLayers_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOutputBlendWeights_SetBit(void* Obj)
{
	((UAuracronPCGMaterialBlenderSettings*)Obj)->bOutputBlendWeights = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOutputBlendWeights = { "bOutputBlendWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialBlenderSettings), &Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOutputBlendWeights_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputBlendWeights_MetaData), NewProp_bOutputBlendWeights_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_BlendWeightAttribute = { "BlendWeightAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialBlenderSettings, BlendWeightAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeightAttribute_MetaData), NewProp_BlendWeightAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_BlendingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_SourceMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_SourceMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaterialWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaterialWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseAttributeAsMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bUseProceduralMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskNoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaskNoiseSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bCreateDynamicMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOptimizeBlending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_MaxBlendLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_bOutputBlendWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::NewProp_BlendWeightAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::ClassParams = {
	&UAuracronPCGMaterialBlenderSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMaterialBlenderSettings);
UAuracronPCGMaterialBlenderSettings::~UAuracronPCGMaterialBlenderSettings() {}
// ********** End Class UAuracronPCGMaterialBlenderSettings ****************************************

// ********** Begin Class UAuracronPCGUVCoordinateGeneratorSettings ********************************
void UAuracronPCGUVCoordinateGeneratorSettings::StaticRegisterNativesUAuracronPCGUVCoordinateGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings;
UClass* UAuracronPCGUVCoordinateGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGUVCoordinateGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGUVCoordinateGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGUVCoordinateGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_NoRegister()
{
	return UAuracronPCGUVCoordinateGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMaterialSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVDescriptor_MetaData[] = {
		{ "Category", "UV Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV generation configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV generation configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateMultipleChannels_MetaData[] = {
		{ "Category", "Multi-Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multiple UV channels\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiple UV channels" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalChannels_MetaData[] = {
		{ "Category", "Multi-Channel" },
		{ "EditCondition", "bGenerateMultipleChannels" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableUVAnimation_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV animation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "EditCondition", "bEnableUVAnimation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTimeBasedAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "EditCondition", "bEnableUVAnimation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bApplyUVDistortion_MetaData[] = {
		{ "Category", "Distortion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV distortion\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV distortion" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistortionStrength_MetaData[] = {
		{ "Category", "Distortion" },
		{ "EditCondition", "bApplyUVDistortion" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistortionScale_MetaData[] = {
		{ "Category", "Distortion" },
		{ "EditCondition", "bApplyUVDistortion" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistortionSeed_MetaData[] = {
		{ "Category", "Distortion" },
		{ "EditCondition", "bApplyUVDistortion" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputUVAttributes_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UCoordinateAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VCoordinateAttribute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVDescriptor;
	static void NewProp_bGenerateMultipleChannels_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateMultipleChannels;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdditionalChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdditionalChannels;
	static void NewProp_bEnableUVAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableUVAnimation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AnimationSpeed;
	static void NewProp_bUseTimeBasedAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTimeBasedAnimation;
	static void NewProp_bApplyUVDistortion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bApplyUVDistortion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistortionStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistortionScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DistortionSeed;
	static void NewProp_bOutputUVAttributes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputUVAttributes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UCoordinateAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VCoordinateAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGUVCoordinateGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_UVDescriptor = { "UVDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, UVDescriptor), Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVDescriptor_MetaData), NewProp_UVDescriptor_MetaData) }; // 2043017876
void Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bGenerateMultipleChannels_SetBit(void* Obj)
{
	((UAuracronPCGUVCoordinateGeneratorSettings*)Obj)->bGenerateMultipleChannels = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bGenerateMultipleChannels = { "bGenerateMultipleChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGUVCoordinateGeneratorSettings), &Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bGenerateMultipleChannels_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateMultipleChannels_MetaData), NewProp_bGenerateMultipleChannels_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AdditionalChannels_Inner = { "AdditionalChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor, METADATA_PARAMS(0, nullptr) }; // 2043017876
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AdditionalChannels = { "AdditionalChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, AdditionalChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalChannels_MetaData), NewProp_AdditionalChannels_MetaData) }; // 2043017876
void Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bEnableUVAnimation_SetBit(void* Obj)
{
	((UAuracronPCGUVCoordinateGeneratorSettings*)Obj)->bEnableUVAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bEnableUVAnimation = { "bEnableUVAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGUVCoordinateGeneratorSettings), &Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bEnableUVAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableUVAnimation_MetaData), NewProp_bEnableUVAnimation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, AnimationSpeed), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bUseTimeBasedAnimation_SetBit(void* Obj)
{
	((UAuracronPCGUVCoordinateGeneratorSettings*)Obj)->bUseTimeBasedAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bUseTimeBasedAnimation = { "bUseTimeBasedAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGUVCoordinateGeneratorSettings), &Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bUseTimeBasedAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTimeBasedAnimation_MetaData), NewProp_bUseTimeBasedAnimation_MetaData) };
void Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bApplyUVDistortion_SetBit(void* Obj)
{
	((UAuracronPCGUVCoordinateGeneratorSettings*)Obj)->bApplyUVDistortion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bApplyUVDistortion = { "bApplyUVDistortion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGUVCoordinateGeneratorSettings), &Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bApplyUVDistortion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bApplyUVDistortion_MetaData), NewProp_bApplyUVDistortion_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionStrength = { "DistortionStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, DistortionStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistortionStrength_MetaData), NewProp_DistortionStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionScale = { "DistortionScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, DistortionScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistortionScale_MetaData), NewProp_DistortionScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionSeed = { "DistortionSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, DistortionSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistortionSeed_MetaData), NewProp_DistortionSeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bOutputUVAttributes_SetBit(void* Obj)
{
	((UAuracronPCGUVCoordinateGeneratorSettings*)Obj)->bOutputUVAttributes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bOutputUVAttributes = { "bOutputUVAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGUVCoordinateGeneratorSettings), &Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bOutputUVAttributes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputUVAttributes_MetaData), NewProp_bOutputUVAttributes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_UCoordinateAttribute = { "UCoordinateAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, UCoordinateAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UCoordinateAttribute_MetaData), NewProp_UCoordinateAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_VCoordinateAttribute = { "VCoordinateAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGUVCoordinateGeneratorSettings, VCoordinateAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VCoordinateAttribute_MetaData), NewProp_VCoordinateAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_UVDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bGenerateMultipleChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AdditionalChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AdditionalChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bEnableUVAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_AnimationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bUseTimeBasedAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bApplyUVDistortion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_DistortionSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_bOutputUVAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_UCoordinateAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::NewProp_VCoordinateAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGUVCoordinateGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGUVCoordinateGeneratorSettings);
UAuracronPCGUVCoordinateGeneratorSettings::~UAuracronPCGUVCoordinateGeneratorSettings() {}
// ********** End Class UAuracronPCGUVCoordinateGeneratorSettings **********************************

// ********** Begin Class UAuracronPCGMaterialParameterControllerSettings **************************
void UAuracronPCGMaterialParameterControllerSettings::StaticRegisterNativesUAuracronPCGMaterialParameterControllerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings;
UClass* UAuracronPCGMaterialParameterControllerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMaterialParameterControllerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMaterialParameterControllerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMaterialParameterControllerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_NoRegister()
{
	return UAuracronPCGMaterialParameterControllerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGMaterialSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterDescriptors_MetaData[] = {
		{ "Category", "Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Parameter configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameter configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetMaterial_MetaData[] = {
		{ "Category", "Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target material\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateMaterialInstances_MetaData[] = {
		{ "Category", "Target" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMaterialParameterCollection_MetaData[] = {
		{ "Category", "Target" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterCollection_MetaData[] = {
		{ "Category", "Target" },
		{ "EditCondition", "bUseMaterialParameterCollection" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableParameterAnimation_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Parameter animation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameter animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "EditCondition", "bEnableParameterAnimation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLoopAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "EditCondition", "bEnableParameterAnimation" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseParameterCurves_MetaData[] = {
		{ "Category", "Curves" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Parameter curves\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameter curves" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarCurves_MetaData[] = {
		{ "Category", "Curves" },
		{ "EditCondition", "bUseParameterCurves" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorCurves_MetaData[] = {
		{ "Category", "Curves" },
		{ "EditCondition", "bUseParameterCurves" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputParameterValues_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputMaterialInstances_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ParameterDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ParameterDescriptors;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetMaterial;
	static void NewProp_bCreateMaterialInstances_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateMaterialInstances;
	static void NewProp_bUseMaterialParameterCollection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMaterialParameterCollection;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ParameterCollection;
	static void NewProp_bEnableParameterAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableParameterAnimation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationSpeed;
	static void NewProp_bLoopAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLoopAnimation;
	static void NewProp_bUseParameterCurves_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseParameterCurves;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ScalarCurves_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScalarCurves_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ScalarCurves;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ColorCurves_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorCurves_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ColorCurves;
	static void NewProp_bOutputParameterValues_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputParameterValues;
	static void NewProp_bOutputMaterialInstances_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputMaterialInstances;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMaterialParameterControllerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterDescriptors_Inner = { "ParameterDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 1628278246
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterDescriptors = { "ParameterDescriptors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, ParameterDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterDescriptors_MetaData), NewProp_ParameterDescriptors_MetaData) }; // 1628278246
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_TargetMaterial = { "TargetMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, TargetMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetMaterial_MetaData), NewProp_TargetMaterial_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bCreateMaterialInstances_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bCreateMaterialInstances = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bCreateMaterialInstances = { "bCreateMaterialInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bCreateMaterialInstances_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateMaterialInstances_MetaData), NewProp_bCreateMaterialInstances_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseMaterialParameterCollection_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bUseMaterialParameterCollection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseMaterialParameterCollection = { "bUseMaterialParameterCollection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseMaterialParameterCollection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMaterialParameterCollection_MetaData), NewProp_bUseMaterialParameterCollection_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterCollection = { "ParameterCollection", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, ParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterCollection_MetaData), NewProp_ParameterCollection_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bEnableParameterAnimation_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bEnableParameterAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bEnableParameterAnimation = { "bEnableParameterAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bEnableParameterAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableParameterAnimation_MetaData), NewProp_bEnableParameterAnimation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, AnimationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bLoopAnimation_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bLoopAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bLoopAnimation = { "bLoopAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bLoopAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLoopAnimation_MetaData), NewProp_bLoopAnimation_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseParameterCurves_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bUseParameterCurves = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseParameterCurves = { "bUseParameterCurves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseParameterCurves_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseParameterCurves_MetaData), NewProp_bUseParameterCurves_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves_ValueProp = { "ScalarCurves", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves_Key_KeyProp = { "ScalarCurves_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves = { "ScalarCurves", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, ScalarCurves), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarCurves_MetaData), NewProp_ScalarCurves_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves_ValueProp = { "ColorCurves", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UCurveLinearColor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves_Key_KeyProp = { "ColorCurves_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves = { "ColorCurves", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGMaterialParameterControllerSettings, ColorCurves), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorCurves_MetaData), NewProp_ColorCurves_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputParameterValues_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bOutputParameterValues = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputParameterValues = { "bOutputParameterValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputParameterValues_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputParameterValues_MetaData), NewProp_bOutputParameterValues_MetaData) };
void Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputMaterialInstances_SetBit(void* Obj)
{
	((UAuracronPCGMaterialParameterControllerSettings*)Obj)->bOutputMaterialInstances = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputMaterialInstances = { "bOutputMaterialInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGMaterialParameterControllerSettings), &Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputMaterialInstances_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputMaterialInstances_MetaData), NewProp_bOutputMaterialInstances_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_TargetMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bCreateMaterialInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseMaterialParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bEnableParameterAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_AnimationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bLoopAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bUseParameterCurves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ScalarCurves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_ColorCurves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputParameterValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::NewProp_bOutputMaterialInstances,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::ClassParams = {
	&UAuracronPCGMaterialParameterControllerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMaterialParameterControllerSettings);
UAuracronPCGMaterialParameterControllerSettings::~UAuracronPCGMaterialParameterControllerSettings() {}
// ********** End Class UAuracronPCGMaterialParameterControllerSettings ****************************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function BlendMaterials ******************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms
	{
		TArray<UMaterialInterface*> SourceMaterials;
		TArray<float> BlendWeights;
		FAuracronPCGMaterialBlendingDescriptor BlendingDescriptor;
		UMaterialInstanceDynamic* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material blending utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material blending utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMaterials_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeights_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SourceMaterials;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendWeights;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_SourceMaterials_Inner = { "SourceMaterials", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_SourceMaterials = { "SourceMaterials", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms, SourceMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMaterials_MetaData), NewProp_SourceMaterials_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendWeights_Inner = { "BlendWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendWeights = { "BlendWeights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms, BlendWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeights_MetaData), NewProp_BlendWeights_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendingDescriptor = { "BlendingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms, BlendingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingDescriptor_MetaData), NewProp_BlendingDescriptor_MetaData) }; // 3080984592
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms, ReturnValue), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_SourceMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_SourceMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_BlendingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "BlendMaterials", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::AuracronPCGMaterialSystemUtils_eventBlendMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execBlendMaterials)
{
	P_GET_TARRAY_REF(UMaterialInterface*,Z_Param_Out_SourceMaterials);
	P_GET_TARRAY_REF(float,Z_Param_Out_BlendWeights);
	P_GET_STRUCT_REF(FAuracronPCGMaterialBlendingDescriptor,Z_Param_Out_BlendingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInstanceDynamic**)Z_Param__Result=UAuracronPCGMaterialSystemUtils::BlendMaterials(Z_Param_Out_SourceMaterials,Z_Param_Out_BlendWeights,Z_Param_Out_BlendingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function BlendMaterials ********************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function CalculateBlendWeight ************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms
	{
		FPCGPoint Point;
		FAuracronPCGMaterialBlendingDescriptor BlendingDescriptor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_BlendingDescriptor = { "BlendingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms, BlendingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingDescriptor_MetaData), NewProp_BlendingDescriptor_MetaData) }; // 3080984592
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_BlendingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "CalculateBlendWeight", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::AuracronPCGMaterialSystemUtils_eventCalculateBlendWeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execCalculateBlendWeight)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FAuracronPCGMaterialBlendingDescriptor,Z_Param_Out_BlendingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::CalculateBlendWeight(Z_Param_Out_Point,Z_Param_Out_BlendingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function CalculateBlendWeight **************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function CreateDefaultMaterialSelectionDescriptor 
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventCreateDefaultMaterialSelectionDescriptor_Parms
	{
		EAuracronPCGMaterialSelectionMode SelectionMode;
		FAuracronPCGMaterialSelectionDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SelectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SelectionMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_SelectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_SelectionMode = { "SelectionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCreateDefaultMaterialSelectionDescriptor_Parms, SelectionMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMaterialSelectionMode, METADATA_PARAMS(0, nullptr) }; // 2127283331
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCreateDefaultMaterialSelectionDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(0, nullptr) }; // 3131025772
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_SelectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_SelectionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "CreateDefaultMaterialSelectionDescriptor", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventCreateDefaultMaterialSelectionDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventCreateDefaultMaterialSelectionDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execCreateDefaultMaterialSelectionDescriptor)
{
	P_GET_ENUM(EAuracronPCGMaterialSelectionMode,Z_Param_SelectionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGMaterialSelectionDescriptor*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::CreateDefaultMaterialSelectionDescriptor(EAuracronPCGMaterialSelectionMode(Z_Param_SelectionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function CreateDefaultMaterialSelectionDescriptor 

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function CreateDynamicMaterialInstance ***
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms
	{
		UMaterialInterface* BaseMaterial;
		TArray<FAuracronPCGMaterialParameterDescriptor> Parameters;
		UMaterialInstanceDynamic* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameters_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_Parameters_Inner = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor, METADATA_PARAMS(0, nullptr) }; // 1628278246
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms, Parameters), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) }; // 1628278246
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms, ReturnValue), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_Parameters_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "CreateDynamicMaterialInstance", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::AuracronPCGMaterialSystemUtils_eventCreateDynamicMaterialInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execCreateDynamicMaterialInstance)
{
	P_GET_OBJECT(UMaterialInterface,Z_Param_BaseMaterial);
	P_GET_TARRAY_REF(FAuracronPCGMaterialParameterDescriptor,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInstanceDynamic**)Z_Param__Result=UAuracronPCGMaterialSystemUtils::CreateDynamicMaterialInstance(Z_Param_BaseMaterial,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function CreateDynamicMaterialInstance *****

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GenerateCylindricalUV ***********
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms
	{
		FVector Position;
		FVector Center;
		float Radius;
		FVector Axis;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Axis_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Axis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Axis = { "Axis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms, Axis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Axis_MetaData), NewProp_Axis_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_Axis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GenerateCylindricalUV", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateCylindricalUV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGenerateCylindricalUV)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Axis);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GenerateCylindricalUV(Z_Param_Out_Position,Z_Param_Out_Center,Z_Param_Radius,Z_Param_Out_Axis);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GenerateCylindricalUV *************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GeneratePlanarUV ****************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms
	{
		FVector Position;
		FVector ProjectionAxis;
		FVector2D Scale;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionAxis_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProjectionAxis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_ProjectionAxis = { "ProjectionAxis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms, ProjectionAxis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionAxis_MetaData), NewProp_ProjectionAxis_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms, Scale), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_ProjectionAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GeneratePlanarUV", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::AuracronPCGMaterialSystemUtils_eventGeneratePlanarUV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGeneratePlanarUV)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ProjectionAxis);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GeneratePlanarUV(Z_Param_Out_Position,Z_Param_Out_ProjectionAxis,Z_Param_Out_Scale);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GeneratePlanarUV ******************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GenerateSphericalUV *************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms
	{
		FVector Position;
		FVector Center;
		float Radius;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GenerateSphericalUV", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateSphericalUV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGenerateSphericalUV)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GenerateSphericalUV(Z_Param_Out_Position,Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GenerateSphericalUV ***************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GenerateTriplanarUV *************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms
	{
		FVector Position;
		FVector Normal;
		float BlendSharpness;
		TArray<FVector2D> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normal_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendSharpness;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_Normal = { "Normal", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms, Normal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normal_MetaData), NewProp_Normal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_BlendSharpness = { "BlendSharpness", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms, BlendSharpness), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_Normal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_BlendSharpness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GenerateTriplanarUV", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateTriplanarUV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGenerateTriplanarUV)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Normal);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BlendSharpness);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector2D>*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GenerateTriplanarUV(Z_Param_Out_Position,Z_Param_Out_Normal,Z_Param_BlendSharpness);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GenerateTriplanarUV ***************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GenerateUVCoordinates ***********
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms
	{
		FVector Position;
		FAuracronPCGUVGenerationDescriptor UVDescriptor;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UV generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UV generation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_UVDescriptor = { "UVDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms, UVDescriptor), Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVDescriptor_MetaData), NewProp_UVDescriptor_MetaData) }; // 2043017876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_UVDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GenerateUVCoordinates", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::AuracronPCGMaterialSystemUtils_eventGenerateUVCoordinates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGenerateUVCoordinates)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FAuracronPCGUVGenerationDescriptor,Z_Param_Out_UVDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GenerateUVCoordinates(Z_Param_Out_Position,Z_Param_Out_UVDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GenerateUVCoordinates *************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GenerateWorldSpaceUV ************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms
	{
		FVector Position;
		FVector2D Scale;
		FVector2D Offset;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms, Scale), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms, Offset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GenerateWorldSpaceUV", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::AuracronPCGMaterialSystemUtils_eventGenerateWorldSpaceUV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGenerateWorldSpaceUV)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Scale);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GenerateWorldSpaceUV(Z_Param_Out_Position,Z_Param_Out_Scale,Z_Param_Out_Offset);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GenerateWorldSpaceUV **************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsColor *************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms
	{
		FPCGPoint Point;
		FString AttributeName;
		FLinearColor DefaultValue;
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "CPP_Default_DefaultValue", "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms, DefaultValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GetAttributeAsColor", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGetAttributeAsColor)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GetAttributeAsColor(Z_Param_Out_Point,Z_Param_AttributeName,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsColor ***************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsFloat *************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms
	{
		FPCGPoint Point;
		FString AttributeName;
		float DefaultValue;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "CPP_Default_DefaultValue", "0.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms, DefaultValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GetAttributeAsFloat", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsFloat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGetAttributeAsFloat)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GetAttributeAsFloat(Z_Param_Out_Point,Z_Param_AttributeName,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsFloat ***************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsVector ************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms
	{
		FPCGPoint Point;
		FString AttributeName;
		FVector DefaultValue;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms, DefaultValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GetAttributeAsVector", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::AuracronPCGMaterialSystemUtils_eventGetAttributeAsVector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGetAttributeAsVector)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GetAttributeAsVector(Z_Param_Out_Point,Z_Param_AttributeName,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GetAttributeAsVector **************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GetMaterialByDistance ***********
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms
	{
		FVector Position;
		FVector ReferenceLocation;
		TArray<float> DistanceThresholds;
		TArray<TSoftObjectPtr<UMaterialInterface>> Materials;
		UMaterialInterface* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferenceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceThresholds_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Materials_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferenceLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceThresholds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DistanceThresholds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Materials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Materials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_ReferenceLocation = { "ReferenceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms, ReferenceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferenceLocation_MetaData), NewProp_ReferenceLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_DistanceThresholds_Inner = { "DistanceThresholds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_DistanceThresholds = { "DistanceThresholds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms, DistanceThresholds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceThresholds_MetaData), NewProp_DistanceThresholds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Materials_Inner = { "Materials", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Materials = { "Materials", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms, Materials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Materials_MetaData), NewProp_Materials_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms, ReturnValue), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_ReferenceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_DistanceThresholds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_DistanceThresholds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Materials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_Materials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GetMaterialByDistance", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::AuracronPCGMaterialSystemUtils_eventGetMaterialByDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGetMaterialByDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ReferenceLocation);
	P_GET_TARRAY_REF(float,Z_Param_Out_DistanceThresholds);
	P_GET_TARRAY_REF(TSoftObjectPtr<UMaterialInterface>,Z_Param_Out_Materials);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInterface**)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GetMaterialByDistance(Z_Param_Out_Position,Z_Param_Out_ReferenceLocation,Z_Param_Out_DistanceThresholds,Z_Param_Out_Materials);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GetMaterialByDistance *************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function GetMaterialIndexByAttribute *****
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms
	{
		FPCGPoint Point;
		FString AttributeName;
		TArray<FString> MaterialTags;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_MaterialTags_Inner = { "MaterialTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_MaterialTags = { "MaterialTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms, MaterialTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialTags_MetaData), NewProp_MaterialTags_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_MaterialTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_MaterialTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "GetMaterialIndexByAttribute", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::AuracronPCGMaterialSystemUtils_eventGetMaterialIndexByAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execGetMaterialIndexByAttribute)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_TARRAY_REF(FString,Z_Param_Out_MaterialTags);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::GetMaterialIndexByAttribute(Z_Param_Out_Point,Z_Param_AttributeName,Z_Param_Out_MaterialTags);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function GetMaterialIndexByAttribute *******

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function SelectMaterial ******************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms
	{
		FPCGPoint Point;
		FAuracronPCGMaterialSelectionDescriptor SelectionDescriptor;
		UMaterialInterface* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material selection utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material selection utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionDescriptor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_SelectionDescriptor = { "SelectionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms, SelectionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionDescriptor_MetaData), NewProp_SelectionDescriptor_MetaData) }; // 3131025772
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms, ReturnValue), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_SelectionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "SelectMaterial", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::AuracronPCGMaterialSystemUtils_eventSelectMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execSelectMaterial)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FAuracronPCGMaterialSelectionDescriptor,Z_Param_Out_SelectionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInterface**)Z_Param__Result=UAuracronPCGMaterialSystemUtils::SelectMaterial(Z_Param_Out_Point,Z_Param_Out_SelectionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function SelectMaterial ********************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function SelectMultipleMaterials *********
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms
	{
		FPCGPoint Point;
		TArray<FAuracronPCGMaterialSelectionDescriptor> SelectionDescriptors;
		TArray<float> Weights;
		TArray<UMaterialInterface*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionDescriptors_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectionDescriptors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Weights;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_SelectionDescriptors_Inner = { "SelectionDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(0, nullptr) }; // 3131025772
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_SelectionDescriptors = { "SelectionDescriptors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms, SelectionDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionDescriptors_MetaData), NewProp_SelectionDescriptors_MetaData) }; // 3131025772
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Weights_Inner = { "Weights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Weights = { "Weights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms, Weights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weights_MetaData), NewProp_Weights_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_SelectionDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_SelectionDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Weights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_Weights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "SelectMultipleMaterials", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::AuracronPCGMaterialSystemUtils_eventSelectMultipleMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execSelectMultipleMaterials)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_TARRAY_REF(FAuracronPCGMaterialSelectionDescriptor,Z_Param_Out_SelectionDescriptors);
	P_GET_TARRAY_REF(float,Z_Param_Out_Weights);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<UMaterialInterface*>*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::SelectMultipleMaterials(Z_Param_Out_Point,Z_Param_Out_SelectionDescriptors,Z_Param_Out_Weights);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function SelectMultipleMaterials ***********

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function SetMaterialParameter ************
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms
	{
		UMaterialInstanceDynamic* MaterialInstance;
		FAuracronPCGMaterialParameterDescriptor ParameterDescriptor;
		FPCGPoint Point;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Parameter utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameter utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialInstance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ParameterDescriptor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_MaterialInstance = { "MaterialInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms, MaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_ParameterDescriptor = { "ParameterDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms, ParameterDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterDescriptor_MetaData), NewProp_ParameterDescriptor_MetaData) }; // 1628278246
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_MaterialInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_ParameterDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::NewProp_Point,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "SetMaterialParameter", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::AuracronPCGMaterialSystemUtils_eventSetMaterialParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execSetMaterialParameter)
{
	P_GET_OBJECT(UMaterialInstanceDynamic,Z_Param_MaterialInstance);
	P_GET_STRUCT_REF(FAuracronPCGMaterialParameterDescriptor,Z_Param_Out_ParameterDescriptor);
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGMaterialSystemUtils::SetMaterialParameter(Z_Param_MaterialInstance,Z_Param_Out_ParameterDescriptor,Z_Param_Out_Point);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function SetMaterialParameter **************

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function ValidateMaterialBlendingDescriptor 
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms
	{
		FAuracronPCGMaterialBlendingDescriptor BlendingDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendingDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendingDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_BlendingDescriptor = { "BlendingDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms, BlendingDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendingDescriptor_MetaData), NewProp_BlendingDescriptor_MetaData) }; // 3080984592
void Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_BlendingDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "ValidateMaterialBlendingDescriptor", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateMaterialBlendingDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execValidateMaterialBlendingDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGMaterialBlendingDescriptor,Z_Param_Out_BlendingDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::ValidateMaterialBlendingDescriptor(Z_Param_Out_BlendingDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function ValidateMaterialBlendingDescriptor 

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function ValidateMaterialSelectionDescriptor 
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms
	{
		FAuracronPCGMaterialSelectionDescriptor SelectionDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_SelectionDescriptor = { "SelectionDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms, SelectionDescriptor), Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionDescriptor_MetaData), NewProp_SelectionDescriptor_MetaData) }; // 3131025772
void Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_SelectionDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "ValidateMaterialSelectionDescriptor", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateMaterialSelectionDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execValidateMaterialSelectionDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGMaterialSelectionDescriptor,Z_Param_Out_SelectionDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::ValidateMaterialSelectionDescriptor(Z_Param_Out_SelectionDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function ValidateMaterialSelectionDescriptor 

// ********** Begin Class UAuracronPCGMaterialSystemUtils Function ValidateUVGenerationDescriptor **
struct Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics
{
	struct AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms
	{
		FAuracronPCGUVGenerationDescriptor UVDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_UVDescriptor = { "UVDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms, UVDescriptor), Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVDescriptor_MetaData), NewProp_UVDescriptor_MetaData) }; // 2043017876
void Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_UVDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, nullptr, "ValidateUVGenerationDescriptor", Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::AuracronPCGMaterialSystemUtils_eventValidateUVGenerationDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGMaterialSystemUtils::execValidateUVGenerationDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGUVGenerationDescriptor,Z_Param_Out_UVDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGMaterialSystemUtils::ValidateUVGenerationDescriptor(Z_Param_Out_UVDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGMaterialSystemUtils Function ValidateUVGenerationDescriptor ****

// ********** Begin Class UAuracronPCGMaterialSystemUtils ******************************************
void UAuracronPCGMaterialSystemUtils::StaticRegisterNativesUAuracronPCGMaterialSystemUtils()
{
	UClass* Class = UAuracronPCGMaterialSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BlendMaterials", &UAuracronPCGMaterialSystemUtils::execBlendMaterials },
		{ "CalculateBlendWeight", &UAuracronPCGMaterialSystemUtils::execCalculateBlendWeight },
		{ "CreateDefaultMaterialSelectionDescriptor", &UAuracronPCGMaterialSystemUtils::execCreateDefaultMaterialSelectionDescriptor },
		{ "CreateDynamicMaterialInstance", &UAuracronPCGMaterialSystemUtils::execCreateDynamicMaterialInstance },
		{ "GenerateCylindricalUV", &UAuracronPCGMaterialSystemUtils::execGenerateCylindricalUV },
		{ "GeneratePlanarUV", &UAuracronPCGMaterialSystemUtils::execGeneratePlanarUV },
		{ "GenerateSphericalUV", &UAuracronPCGMaterialSystemUtils::execGenerateSphericalUV },
		{ "GenerateTriplanarUV", &UAuracronPCGMaterialSystemUtils::execGenerateTriplanarUV },
		{ "GenerateUVCoordinates", &UAuracronPCGMaterialSystemUtils::execGenerateUVCoordinates },
		{ "GenerateWorldSpaceUV", &UAuracronPCGMaterialSystemUtils::execGenerateWorldSpaceUV },
		{ "GetAttributeAsColor", &UAuracronPCGMaterialSystemUtils::execGetAttributeAsColor },
		{ "GetAttributeAsFloat", &UAuracronPCGMaterialSystemUtils::execGetAttributeAsFloat },
		{ "GetAttributeAsVector", &UAuracronPCGMaterialSystemUtils::execGetAttributeAsVector },
		{ "GetMaterialByDistance", &UAuracronPCGMaterialSystemUtils::execGetMaterialByDistance },
		{ "GetMaterialIndexByAttribute", &UAuracronPCGMaterialSystemUtils::execGetMaterialIndexByAttribute },
		{ "SelectMaterial", &UAuracronPCGMaterialSystemUtils::execSelectMaterial },
		{ "SelectMultipleMaterials", &UAuracronPCGMaterialSystemUtils::execSelectMultipleMaterials },
		{ "SetMaterialParameter", &UAuracronPCGMaterialSystemUtils::execSetMaterialParameter },
		{ "ValidateMaterialBlendingDescriptor", &UAuracronPCGMaterialSystemUtils::execValidateMaterialBlendingDescriptor },
		{ "ValidateMaterialSelectionDescriptor", &UAuracronPCGMaterialSystemUtils::execValidateMaterialSelectionDescriptor },
		{ "ValidateUVGenerationDescriptor", &UAuracronPCGMaterialSystemUtils::execValidateUVGenerationDescriptor },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils;
UClass* UAuracronPCGMaterialSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGMaterialSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGMaterialSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGMaterialSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_NoRegister()
{
	return UAuracronPCGMaterialSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Material System Utilities\n * Utility functions for material assignment and manipulation\n */" },
#endif
		{ "IncludePath", "AuracronPCGMaterialSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGMaterialSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material System Utilities\nUtility functions for material assignment and manipulation" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_BlendMaterials, "BlendMaterials" }, // 902224031
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CalculateBlendWeight, "CalculateBlendWeight" }, // 731634413
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDefaultMaterialSelectionDescriptor, "CreateDefaultMaterialSelectionDescriptor" }, // 1225870606
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_CreateDynamicMaterialInstance, "CreateDynamicMaterialInstance" }, // 2638233687
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateCylindricalUV, "GenerateCylindricalUV" }, // 14342820
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GeneratePlanarUV, "GeneratePlanarUV" }, // 2757281071
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateSphericalUV, "GenerateSphericalUV" }, // 2288278841
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateTriplanarUV, "GenerateTriplanarUV" }, // 3353323778
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateUVCoordinates, "GenerateUVCoordinates" }, // 4080713722
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GenerateWorldSpaceUV, "GenerateWorldSpaceUV" }, // 410695831
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsColor, "GetAttributeAsColor" }, // 3156638918
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsFloat, "GetAttributeAsFloat" }, // 3722615128
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetAttributeAsVector, "GetAttributeAsVector" }, // 408281181
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialByDistance, "GetMaterialByDistance" }, // 93306681
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_GetMaterialIndexByAttribute, "GetMaterialIndexByAttribute" }, // 2999501982
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMaterial, "SelectMaterial" }, // 1479037712
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SelectMultipleMaterials, "SelectMultipleMaterials" }, // 783799451
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_SetMaterialParameter, "SetMaterialParameter" }, // 1914740070
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialBlendingDescriptor, "ValidateMaterialBlendingDescriptor" }, // 226369127
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateMaterialSelectionDescriptor, "ValidateMaterialSelectionDescriptor" }, // 3586777980
		{ &Z_Construct_UFunction_UAuracronPCGMaterialSystemUtils_ValidateUVGenerationDescriptor, "ValidateUVGenerationDescriptor" }, // 2148158668
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGMaterialSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::ClassParams = {
	&UAuracronPCGMaterialSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils.OuterSingleton;
}
UAuracronPCGMaterialSystemUtils::UAuracronPCGMaterialSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGMaterialSystemUtils);
UAuracronPCGMaterialSystemUtils::~UAuracronPCGMaterialSystemUtils() {}
// ********** End Class UAuracronPCGMaterialSystemUtils ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGMaterialSelectionMode_StaticEnum, TEXT("EAuracronPCGMaterialSelectionMode"), &Z_Registration_Info_UEnum_EAuracronPCGMaterialSelectionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2127283331U) },
		{ EAuracronPCGMaterialBlendMode_StaticEnum, TEXT("EAuracronPCGMaterialBlendMode"), &Z_Registration_Info_UEnum_EAuracronPCGMaterialBlendMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1425128422U) },
		{ EAuracronPCGUVGenerationMode_StaticEnum, TEXT("EAuracronPCGUVGenerationMode"), &Z_Registration_Info_UEnum_EAuracronPCGUVGenerationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1155836973U) },
		{ EAuracronPCGTextureCoordinateChannel_StaticEnum, TEXT("EAuracronPCGTextureCoordinateChannel"), &Z_Registration_Info_UEnum_EAuracronPCGTextureCoordinateChannel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 625166426U) },
		{ EAuracronPCGMaterialParameterType_StaticEnum, TEXT("EAuracronPCGMaterialParameterType"), &Z_Registration_Info_UEnum_EAuracronPCGMaterialParameterType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2367748895U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGMaterialSelectionDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics::NewStructOps, TEXT("AuracronPCGMaterialSelectionDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGMaterialSelectionDescriptor), 3131025772U) },
		{ FAuracronPCGMaterialBlendingDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics::NewStructOps, TEXT("AuracronPCGMaterialBlendingDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGMaterialBlendingDescriptor), 3080984592U) },
		{ FAuracronPCGUVGenerationDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics::NewStructOps, TEXT("AuracronPCGUVGenerationDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGUVGenerationDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGUVGenerationDescriptor), 2043017876U) },
		{ FAuracronPCGMaterialParameterDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics::NewStructOps, TEXT("AuracronPCGMaterialParameterDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGMaterialParameterDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGMaterialParameterDescriptor), 1628278246U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings, UAuracronPCGAdvancedMaterialSelectorSettings::StaticClass, TEXT("UAuracronPCGAdvancedMaterialSelectorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedMaterialSelectorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedMaterialSelectorSettings), 775584374U) },
		{ Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings, UAuracronPCGMaterialBlenderSettings::StaticClass, TEXT("UAuracronPCGMaterialBlenderSettings"), &Z_Registration_Info_UClass_UAuracronPCGMaterialBlenderSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMaterialBlenderSettings), 2625763449U) },
		{ Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings, UAuracronPCGUVCoordinateGeneratorSettings::StaticClass, TEXT("UAuracronPCGUVCoordinateGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGUVCoordinateGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGUVCoordinateGeneratorSettings), 561120400U) },
		{ Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings, UAuracronPCGMaterialParameterControllerSettings::StaticClass, TEXT("UAuracronPCGMaterialParameterControllerSettings"), &Z_Registration_Info_UClass_UAuracronPCGMaterialParameterControllerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMaterialParameterControllerSettings), 3906209212U) },
		{ Z_Construct_UClass_UAuracronPCGMaterialSystemUtils, UAuracronPCGMaterialSystemUtils::StaticClass, TEXT("UAuracronPCGMaterialSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGMaterialSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGMaterialSystemUtils), 1699702742U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_3955903941(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
