// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronNetworkingBridge.h"

#ifdef AURACRONNETWORKINGBRIDGE_AuracronNetworkingBridge_generated_h
#error "AuracronNetworkingBridge.generated.h already included, missing '#pragma once' in AuracronNetworkingBridge.h"
#endif
#define AURACRONNETWORKINGBRIDGE_AuracronNetworkingBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronServerValidationType : uint8;
enum class EAuracronSessionState : uint8;
struct FAuracronNetworkingSessionConfiguration;
struct FAuracronPlayerInfo;
struct FAuracronSessionConfiguration;

// ********** Begin ScriptStruct FAuracronNetworkingSessionConfiguration ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronNetworkingSessionConfiguration;
// ********** End ScriptStruct FAuracronNetworkingSessionConfiguration *****************************

// ********** Begin ScriptStruct FAuracronPlayerInfo ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_172_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPlayerInfo;
// ********** End ScriptStruct FAuracronPlayerInfo *************************************************

// ********** Begin ScriptStruct FAuracronNetworkingAntiCheatConfiguration *************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_237_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronNetworkingAntiCheatConfiguration;
// ********** End ScriptStruct FAuracronNetworkingAntiCheatConfiguration ***************************

// ********** Begin ScriptStruct FAuracronReplicationConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_290_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronReplicationConfiguration;
// ********** End ScriptStruct FAuracronReplicationConfiguration ***********************************

// ********** Begin Delegate FOnSessionCreated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_616_DELEGATE \
static void FOnSessionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSessionCreated, const FString& SessionName, bool bSuccess);


// ********** End Delegate FOnSessionCreated *******************************************************

// ********** Begin Delegate FOnPlayerConnected ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_621_DELEGATE \
static void FOnPlayerConnected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerConnected, FAuracronPlayerInfo PlayerInfo);


// ********** End Delegate FOnPlayerConnected ******************************************************

// ********** Begin Delegate FOnPlayerDisconnected *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_626_DELEGATE \
static void FOnPlayerDisconnected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDisconnected, const FString& PlayerID, const FString& Reason);


// ********** End Delegate FOnPlayerDisconnected ***************************************************

// ********** Begin Delegate FOnSuspiciousActivityDetected *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_631_DELEGATE \
static void FOnSuspiciousActivityDetected_DelegateWrapper(const FMulticastScriptDelegate& OnSuspiciousActivityDetected, const FString& PlayerID, const FString& ActivityType, const FString& Evidence);


// ********** End Delegate FOnSuspiciousActivityDetected *******************************************

// ********** Begin Delegate FOnSessionStateChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_636_DELEGATE \
static void FOnSessionStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSessionStateChanged, EAuracronSessionState OldState, EAuracronSessionState NewState);


// ********** End Delegate FOnSessionStateChanged **************************************************

// ********** Begin Class UAuracronNetworkingBridge ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_ConnectedPlayers); \
	DECLARE_FUNCTION(execOnRep_ConnectionType); \
	DECLARE_FUNCTION(execOnRep_SessionState); \
	DECLARE_FUNCTION(execGetBandwidthUsage); \
	DECLARE_FUNCTION(execGetPacketLossRate); \
	DECLARE_FUNCTION(execGetAverageSessionPing); \
	DECLARE_FUNCTION(execGetNetworkStatistics); \
	DECLARE_FUNCTION(execIsPlayerUnderSurveillance); \
	DECLARE_FUNCTION(execReportSuspiciousActivity); \
	DECLARE_FUNCTION(execValidatePlayerAction); \
	DECLARE_FUNCTION(execBanPlayer); \
	DECLARE_FUNCTION(execKickPlayer); \
	DECLARE_FUNCTION(execUpdatePlayerInfo); \
	DECLARE_FUNCTION(execGetPlayerInfo); \
	DECLARE_FUNCTION(execGetConnectedPlayers); \
	DECLARE_FUNCTION(execGetCurrentSessionInfo); \
	DECLARE_FUNCTION(execGetSessionState); \
	DECLARE_FUNCTION(execDestroySession); \
	DECLARE_FUNCTION(execLeaveSession); \
	DECLARE_FUNCTION(execJoinSession); \
	DECLARE_FUNCTION(execFindSessions); \
	DECLARE_FUNCTION(execCreateSession);


AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronNetworkingBridge(); \
	friend struct Z_Construct_UClass_UAuracronNetworkingBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronNetworkingBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronNetworkingBridge"), Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronNetworkingBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentSessionConfig=NETFIELD_REP_START, \
		CurrentSessionState, \
		CurrentConnectionType, \
		ConnectedPlayers, \
		NETFIELD_REP_END=ConnectedPlayers	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronNetworkingBridge(UAuracronNetworkingBridge&&) = delete; \
	UAuracronNetworkingBridge(const UAuracronNetworkingBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronNetworkingBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronNetworkingBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronNetworkingBridge) \
	NO_API virtual ~UAuracronNetworkingBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_349_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h_352_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronNetworkingBridge;

// ********** End Class UAuracronNetworkingBridge **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h

// ********** Begin Enum EAuracronNetworkConnectionType ********************************************
#define FOREACH_ENUM_EAURACRONNETWORKCONNECTIONTYPE(op) \
	op(EAuracronNetworkConnectionType::None) \
	op(EAuracronNetworkConnectionType::Listen) \
	op(EAuracronNetworkConnectionType::Dedicated) \
	op(EAuracronNetworkConnectionType::Client) \
	op(EAuracronNetworkConnectionType::Standalone) 

enum class EAuracronNetworkConnectionType : uint8;
template<> struct TIsUEnumClass<EAuracronNetworkConnectionType> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronNetworkConnectionType>();
// ********** End Enum EAuracronNetworkConnectionType **********************************************

// ********** Begin Enum EAuracronSessionState *****************************************************
#define FOREACH_ENUM_EAURACRONSESSIONSTATE(op) \
	op(EAuracronSessionState::None) \
	op(EAuracronSessionState::Creating) \
	op(EAuracronSessionState::Searching) \
	op(EAuracronSessionState::Joining) \
	op(EAuracronSessionState::InLobby) \
	op(EAuracronSessionState::InGame) \
	op(EAuracronSessionState::Ending) \
	op(EAuracronSessionState::Disconnected) \
	op(EAuracronSessionState::Error) 

enum class EAuracronSessionState : uint8;
template<> struct TIsUEnumClass<EAuracronSessionState> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronSessionState>();
// ********** End Enum EAuracronSessionState *******************************************************

// ********** Begin Enum EAuracronServerValidationType *********************************************
#define FOREACH_ENUM_EAURACRONSERVERVALIDATIONTYPE(op) \
	op(EAuracronServerValidationType::None) \
	op(EAuracronServerValidationType::Movement) \
	op(EAuracronServerValidationType::Ability) \
	op(EAuracronServerValidationType::Damage) \
	op(EAuracronServerValidationType::ItemUsage) \
	op(EAuracronServerValidationType::Experience) \
	op(EAuracronServerValidationType::Gold) \
	op(EAuracronServerValidationType::Position) \
	op(EAuracronServerValidationType::Timing) \
	op(EAuracronServerValidationType::Input) 

enum class EAuracronServerValidationType : uint8;
template<> struct TIsUEnumClass<EAuracronServerValidationType> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronServerValidationType>();
// ********** End Enum EAuracronServerValidationType ***********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
