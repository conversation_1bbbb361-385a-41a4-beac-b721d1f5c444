// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronCombatBridge_init() {}
	AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature();
	AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature();
	AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature();
	AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronCombatBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronCombatBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronCombatBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronCombatBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x1164DC5F,
				0xEA9AA112,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronCombatBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronCombatBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronCombatBridge(Z_Construct_UPackage__Script_AuracronCombatBridge, TEXT("/Script/AuracronCombatBridge"), Z_Registration_Info_UPackage__Script_AuracronCombatBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x1164DC5F, 0xEA9AA112));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
