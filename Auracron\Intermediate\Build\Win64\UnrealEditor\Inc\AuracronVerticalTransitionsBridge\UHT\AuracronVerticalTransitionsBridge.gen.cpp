// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronVerticalTransitionsBridge/Public/AuracronVerticalTransitionsBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronVerticalTransitionsBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONVERTICALTRANSITIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge();
AURACRONVERTICALTRANSITIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge_NoRegister();
AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType();
AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType();
AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection();
AURACRONVERTICALTRANSITIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FStringArrayWrapper();
AURACRONVERTICALTRANSITIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FTransitionNetworkData();
AURACRONVERTICALTRANSITIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FTransitionPointData();
AURACRONVERTICALTRANSITIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FTransitionProperties();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UParticleSystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundCue_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ETransitionType();
UPackage* Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERealmType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERealmType;
static UEnum* ERealmType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERealmType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERealmType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("ERealmType"));
	}
	return Z_Registration_Info_UEnum_ERealmType.OuterSingleton;
}
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<ERealmType>()
{
	return ERealmType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbismoUmbrio.DisplayName", "Abismo Umbrio" },
		{ "AbismoUmbrio.Name", "ERealmType::AbismoUmbrio" },
		{ "BlueprintType", "true" },
		{ "FirmamentoZephyr.DisplayName", "Firmamento Zephyr" },
		{ "FirmamentoZephyr.Name", "ERealmType::FirmamentoZephyr" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
		{ "PlanicieRadiante.DisplayName", "Plan\xc3\xad""cie Radiante" },
		{ "PlanicieRadiante.Name", "ERealmType::PlanicieRadiante" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERealmType::PlanicieRadiante", (int64)ERealmType::PlanicieRadiante },
		{ "ERealmType::FirmamentoZephyr", (int64)ERealmType::FirmamentoZephyr },
		{ "ERealmType::AbismoUmbrio", (int64)ERealmType::AbismoUmbrio },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	"ERealmType",
	"ERealmType",
	Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType()
{
	if (!Z_Registration_Info_UEnum_ERealmType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERealmType.InnerSingleton, Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERealmType.InnerSingleton;
}
// ********** End Enum ERealmType ******************************************************************

// ********** Begin Enum EAuracronVerticalTransitionType *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVerticalTransitionType;
static UEnum* EAuracronVerticalTransitionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("EAuracronVerticalTransitionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.OuterSingleton;
}
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<EAuracronVerticalTransitionType>()
{
	return EAuracronVerticalTransitionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CaveEntrance.DisplayName", "Cave Entrance" },
		{ "CaveEntrance.Name", "EAuracronVerticalTransitionType::CaveEntrance" },
		{ "Elevator.DisplayName", "Elevator" },
		{ "Elevator.Name", "EAuracronVerticalTransitionType::Elevator" },
		{ "FloatingPlatform.DisplayName", "Floating Platform" },
		{ "FloatingPlatform.Name", "EAuracronVerticalTransitionType::FloatingPlatform" },
		{ "JumpPad.DisplayName", "Jump Pad" },
		{ "JumpPad.Name", "EAuracronVerticalTransitionType::JumpPad" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
		{ "Portal.DisplayName", "Portal" },
		{ "Portal.Name", "EAuracronVerticalTransitionType::Portal" },
		{ "Stairs.DisplayName", "Stairs" },
		{ "Stairs.Name", "EAuracronVerticalTransitionType::Stairs" },
		{ "TeleportCircle.DisplayName", "Teleport Circle" },
		{ "TeleportCircle.Name", "EAuracronVerticalTransitionType::TeleportCircle" },
		{ "WindCurrent.DisplayName", "Wind Current" },
		{ "WindCurrent.Name", "EAuracronVerticalTransitionType::WindCurrent" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVerticalTransitionType::Portal", (int64)EAuracronVerticalTransitionType::Portal },
		{ "EAuracronVerticalTransitionType::Elevator", (int64)EAuracronVerticalTransitionType::Elevator },
		{ "EAuracronVerticalTransitionType::Stairs", (int64)EAuracronVerticalTransitionType::Stairs },
		{ "EAuracronVerticalTransitionType::JumpPad", (int64)EAuracronVerticalTransitionType::JumpPad },
		{ "EAuracronVerticalTransitionType::TeleportCircle", (int64)EAuracronVerticalTransitionType::TeleportCircle },
		{ "EAuracronVerticalTransitionType::WindCurrent", (int64)EAuracronVerticalTransitionType::WindCurrent },
		{ "EAuracronVerticalTransitionType::CaveEntrance", (int64)EAuracronVerticalTransitionType::CaveEntrance },
		{ "EAuracronVerticalTransitionType::FloatingPlatform", (int64)EAuracronVerticalTransitionType::FloatingPlatform },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	"EAuracronVerticalTransitionType",
	"EAuracronVerticalTransitionType",
	Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.InnerSingleton, Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVerticalTransitionType.InnerSingleton;
}
// ********** End Enum EAuracronVerticalTransitionType *********************************************

// ********** Begin Enum ETransitionDirection ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETransitionDirection;
static UEnum* ETransitionDirection_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETransitionDirection.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETransitionDirection.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("ETransitionDirection"));
	}
	return Z_Registration_Info_UEnum_ETransitionDirection.OuterSingleton;
}
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<ETransitionDirection>()
{
	return ETransitionDirection_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bidirectional.DisplayName", "Bidirectional" },
		{ "Bidirectional.Name", "ETransitionDirection::Bidirectional" },
		{ "BlueprintType", "true" },
		{ "Down.DisplayName", "Down" },
		{ "Down.Name", "ETransitionDirection::Down" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
		{ "Up.DisplayName", "Up" },
		{ "Up.Name", "ETransitionDirection::Up" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETransitionDirection::Up", (int64)ETransitionDirection::Up },
		{ "ETransitionDirection::Down", (int64)ETransitionDirection::Down },
		{ "ETransitionDirection::Bidirectional", (int64)ETransitionDirection::Bidirectional },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	"ETransitionDirection",
	"ETransitionDirection",
	Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection()
{
	if (!Z_Registration_Info_UEnum_ETransitionDirection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETransitionDirection.InnerSingleton, Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETransitionDirection.InnerSingleton;
}
// ********** End Enum ETransitionDirection ********************************************************

// ********** Begin ScriptStruct FTransitionProperties *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTransitionProperties;
class UScriptStruct* FTransitionProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTransitionProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTransitionProperties, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("TransitionProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FTransitionProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTransitionProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSpeed_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownTime_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyCost_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationTime_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresChannel_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInterruptible_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibilityRange_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoundRange_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bVisualEffects_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicValue_MetaData[] = {
		{ "Category", "Transition Properties" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static void NewProp_bRequiresChannel_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresChannel;
	static void NewProp_bInterruptible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterruptible;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisibilityRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoundRange;
	static void NewProp_bVisualEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisualEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTransitionProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_TransitionSpeed = { "TransitionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, TransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSpeed_MetaData), NewProp_TransitionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_CooldownTime = { "CooldownTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, CooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownTime_MetaData), NewProp_CooldownTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_EnergyCost = { "EnergyCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, EnergyCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyCost_MetaData), NewProp_EnergyCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_ActivationTime = { "ActivationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, ActivationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationTime_MetaData), NewProp_ActivationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
void Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bRequiresChannel_SetBit(void* Obj)
{
	((FTransitionProperties*)Obj)->bRequiresChannel = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bRequiresChannel = { "bRequiresChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionProperties), &Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bRequiresChannel_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresChannel_MetaData), NewProp_bRequiresChannel_MetaData) };
void Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bInterruptible_SetBit(void* Obj)
{
	((FTransitionProperties*)Obj)->bInterruptible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bInterruptible = { "bInterruptible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionProperties), &Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bInterruptible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInterruptible_MetaData), NewProp_bInterruptible_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_VisibilityRange = { "VisibilityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, VisibilityRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibilityRange_MetaData), NewProp_VisibilityRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_SoundRange = { "SoundRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, SoundRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoundRange_MetaData), NewProp_SoundRange_MetaData) };
void Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bVisualEffects_SetBit(void* Obj)
{
	((FTransitionProperties*)Obj)->bVisualEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bVisualEffects = { "bVisualEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionProperties), &Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bVisualEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bVisualEffects_MetaData), NewProp_bVisualEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_StrategicValue = { "StrategicValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionProperties, StrategicValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicValue_MetaData), NewProp_StrategicValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTransitionProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_TransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_CooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_EnergyCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_ActivationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bRequiresChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bInterruptible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_VisibilityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_SoundRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_bVisualEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewProp_StrategicValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTransitionProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	&NewStructOps,
	"TransitionProperties",
	Z_Construct_UScriptStruct_FTransitionProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionProperties_Statics::PropPointers),
	sizeof(FTransitionProperties),
	alignof(FTransitionProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTransitionProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTransitionProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTransitionProperties.InnerSingleton, Z_Construct_UScriptStruct_FTransitionProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTransitionProperties.InnerSingleton;
}
// ********** End ScriptStruct FTransitionProperties ***********************************************

// ********** Begin ScriptStruct FTransitionPointData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTransitionPointData;
class UScriptStruct* FTransitionPointData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionPointData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTransitionPointData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTransitionPointData, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("TransitionPointData"));
	}
	return Z_Registration_Info_UScriptStruct_FTransitionPointData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTransitionPointData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionType_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceRealm_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationRealm_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationLocation_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceRotation_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationRotation_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionMesh_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionMaterial_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEffect_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSound_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresKey_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredKeyID_MetaData[] = {
		{ "Category", "Transition Point" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Direction_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DestinationRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DestinationRealm;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestinationLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestinationRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionMaterial;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionSound;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bRequiresKey_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredKeyID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTransitionPointData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionType), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_EAuracronVerticalTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionType_MetaData), NewProp_TransitionType_MetaData) }; // 3301278572
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Direction_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, Direction), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ETransitionDirection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) }; // 1752873855
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRealm = { "SourceRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, SourceRealm), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceRealm_MetaData), NewProp_SourceRealm_MetaData) }; // 2881442322
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRealm = { "DestinationRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, DestinationRealm), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationRealm_MetaData), NewProp_DestinationRealm_MetaData) }; // 2881442322
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationLocation = { "DestinationLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, DestinationLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationLocation_MetaData), NewProp_DestinationLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRotation = { "SourceRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, SourceRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceRotation_MetaData), NewProp_SourceRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRotation = { "DestinationRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, DestinationRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationRotation_MetaData), NewProp_DestinationRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, Properties), Z_Construct_UScriptStruct_FTransitionProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 3720635809
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionMesh = { "TransitionMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionMesh_MetaData), NewProp_TransitionMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionMaterial = { "TransitionMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionMaterial_MetaData), NewProp_TransitionMaterial_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionEffect = { "TransitionEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEffect_MetaData), NewProp_TransitionEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionSound = { "TransitionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, TransitionSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSound_MetaData), NewProp_TransitionSound_MetaData) };
void Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FTransitionPointData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionPointData), &Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bRequiresKey_SetBit(void* Obj)
{
	((FTransitionPointData*)Obj)->bRequiresKey = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bRequiresKey = { "bRequiresKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionPointData), &Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bRequiresKey_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresKey_MetaData), NewProp_bRequiresKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_RequiredKeyID = { "RequiredKeyID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionPointData, RequiredKeyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredKeyID_MetaData), NewProp_RequiredKeyID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTransitionPointData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Direction_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_SourceRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_DestinationRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_TransitionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_bRequiresKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewProp_RequiredKeyID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionPointData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTransitionPointData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	&NewStructOps,
	"TransitionPointData",
	Z_Construct_UScriptStruct_FTransitionPointData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionPointData_Statics::PropPointers),
	sizeof(FTransitionPointData),
	alignof(FTransitionPointData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionPointData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTransitionPointData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTransitionPointData()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionPointData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTransitionPointData.InnerSingleton, Z_Construct_UScriptStruct_FTransitionPointData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTransitionPointData.InnerSingleton;
}
// ********** End ScriptStruct FTransitionPointData ************************************************

// ********** Begin ScriptStruct FStringArrayWrapper ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FStringArrayWrapper;
class UScriptStruct* FStringArrayWrapper::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FStringArrayWrapper.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FStringArrayWrapper.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FStringArrayWrapper, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("StringArrayWrapper"));
	}
	return Z_Registration_Info_UScriptStruct_FStringArrayWrapper.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FStringArrayWrapper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para TArray<FString> em TMap\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para TArray<FString> em TMap" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strings_MetaData[] = {
		{ "Category", "String Array" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Strings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Strings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FStringArrayWrapper>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::NewProp_Strings_Inner = { "Strings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::NewProp_Strings = { "Strings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStringArrayWrapper, Strings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strings_MetaData), NewProp_Strings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::NewProp_Strings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::NewProp_Strings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	&NewStructOps,
	"StringArrayWrapper",
	Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::PropPointers),
	sizeof(FStringArrayWrapper),
	alignof(FStringArrayWrapper),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FStringArrayWrapper()
{
	if (!Z_Registration_Info_UScriptStruct_FStringArrayWrapper.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FStringArrayWrapper.InnerSingleton, Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FStringArrayWrapper.InnerSingleton;
}
// ********** End ScriptStruct FStringArrayWrapper *************************************************

// ********** Begin ScriptStruct FTransitionNetworkData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTransitionNetworkData;
class UScriptStruct* FTransitionNetworkData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionNetworkData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTransitionNetworkData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTransitionNetworkData, (UObject*)Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge(), TEXT("TransitionNetworkData"));
	}
	return Z_Registration_Info_UScriptStruct_FTransitionNetworkData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTransitionNetworkData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionPoints_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedTransitions_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkEfficiency_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalTransitions_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNetworkOptimized_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectedTransitions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConnectedTransitions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ConnectedTransitions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkEfficiency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalTransitions;
	static void NewProp_bNetworkOptimized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNetworkOptimized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTransitionNetworkData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TransitionPoints_Inner = { "TransitionPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransitionPointData, METADATA_PARAMS(0, nullptr) }; // 83480635
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TransitionPoints = { "TransitionPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionNetworkData, TransitionPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionPoints_MetaData), NewProp_TransitionPoints_MetaData) }; // 83480635
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions_ValueProp = { "ConnectedTransitions", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FStringArrayWrapper, METADATA_PARAMS(0, nullptr) }; // 2259794444
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions_Key_KeyProp = { "ConnectedTransitions_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions = { "ConnectedTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionNetworkData, ConnectedTransitions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedTransitions_MetaData), NewProp_ConnectedTransitions_MetaData) }; // 2259794444
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_NetworkEfficiency = { "NetworkEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionNetworkData, NetworkEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkEfficiency_MetaData), NewProp_NetworkEfficiency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TotalTransitions = { "TotalTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTransitionNetworkData, TotalTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalTransitions_MetaData), NewProp_TotalTransitions_MetaData) };
void Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_bNetworkOptimized_SetBit(void* Obj)
{
	((FTransitionNetworkData*)Obj)->bNetworkOptimized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_bNetworkOptimized = { "bNetworkOptimized", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTransitionNetworkData), &Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_bNetworkOptimized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNetworkOptimized_MetaData), NewProp_bNetworkOptimized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TransitionPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TransitionPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_ConnectedTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_NetworkEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_TotalTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewProp_bNetworkOptimized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
	nullptr,
	&NewStructOps,
	"TransitionNetworkData",
	Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::PropPointers),
	sizeof(FTransitionNetworkData),
	alignof(FTransitionNetworkData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTransitionNetworkData()
{
	if (!Z_Registration_Info_UScriptStruct_FTransitionNetworkData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTransitionNetworkData.InnerSingleton, Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTransitionNetworkData.InnerSingleton;
}
// ********** End ScriptStruct FTransitionNetworkData **********************************************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ActivateTransition ***********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics
{
	struct AuracronVerticalTransitionsBridge_eventActivateTransition_Parms
	{
		FString TransitionID;
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventActivateTransition_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventActivateTransition_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventActivateTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventActivateTransition_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ActivateTransition", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::AuracronVerticalTransitionsBridge_eventActivateTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::AuracronVerticalTransitionsBridge_eventActivateTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execActivateTransition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateTransition(Z_Param_TransitionID,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ActivateTransition *************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function BuildTransitionNetwork *******
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics
{
	struct AuracronVerticalTransitionsBridge_eventBuildTransitionNetwork_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Construir rede de transi\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Construir rede de transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventBuildTransitionNetwork_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventBuildTransitionNetwork_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "BuildTransitionNetwork", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::AuracronVerticalTransitionsBridge_eventBuildTransitionNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::AuracronVerticalTransitionsBridge_eventBuildTransitionNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execBuildTransitionNetwork)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BuildTransitionNetwork();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function BuildTransitionNetwork *********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function CancelTransitionChanneling ***
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics
{
	struct AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms
	{
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar channeling de transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar channeling de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "CancelTransitionChanneling", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::AuracronVerticalTransitionsBridge_eventCancelTransitionChanneling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execCancelTransitionChanneling)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelTransitionChanneling(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function CancelTransitionChanneling *****

// ********** Begin Class UAuracronVerticalTransitionsBridge Function CanPlayerUseTransition *******
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics
{
	struct AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms
	{
		FString TransitionID;
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se jogador pode usar transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se jogador pode usar transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "CanPlayerUseTransition", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::AuracronVerticalTransitionsBridge_eventCanPlayerUseTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execCanPlayerUseTransition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPlayerUseTransition(Z_Param_TransitionID,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function CanPlayerUseTransition *********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ClearTransitionNetwork *******
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar rede de transi\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar rede de transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ClearTransitionNetwork", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execClearTransitionNetwork)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearTransitionNetwork();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ClearTransitionNetwork *********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function CreateTransitionPoint ********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics
{
	struct AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms
	{
		FTransitionPointData TransitionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar ponto de transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar ponto de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_TransitionData = { "TransitionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms, TransitionData), Z_Construct_UScriptStruct_FTransitionPointData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionData_MetaData), NewProp_TransitionData_MetaData) }; // 83480635
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_TransitionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "CreateTransitionPoint", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::AuracronVerticalTransitionsBridge_eventCreateTransitionPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execCreateTransitionPoint)
{
	P_GET_STRUCT_REF(FTransitionPointData,Z_Param_Out_TransitionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateTransitionPoint(Z_Param_Out_TransitionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function CreateTransitionPoint **********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function DeactivateTransition *********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics
{
	struct AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms
	{
		FString TransitionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Desativar transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "DeactivateTransition", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::AuracronVerticalTransitionsBridge_eventDeactivateTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execDeactivateTransition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeactivateTransition(Z_Param_TransitionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function DeactivateTransition ***********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ExecutePythonScript **********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics
{
	struct AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms
	{
		FString ScriptPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar script Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar script Python" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ScriptPath = { "ScriptPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms, ScriptPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptPath_MetaData), NewProp_ScriptPath_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ScriptPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ExecutePythonScript", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::AuracronVerticalTransitionsBridge_eventExecutePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execExecutePythonScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecutePythonScript(Z_Param_ScriptPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ExecutePythonScript ************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ExecuteTransition ************
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics
{
	struct AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms
	{
		FString TransitionID;
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ExecuteTransition", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::AuracronVerticalTransitionsBridge_eventExecuteTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execExecuteTransition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteTransition(Z_Param_TransitionID,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ExecuteTransition **************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function FindPathBetweenRealms ********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics
{
	struct AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms
	{
		ERealmType SourceRealm;
		ERealmType DestinationRealm;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Encontrar caminho entre realms\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Encontrar caminho entre realms" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceRealm;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DestinationRealm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DestinationRealm;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_SourceRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_SourceRealm = { "SourceRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms, SourceRealm), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, METADATA_PARAMS(0, nullptr) }; // 2881442322
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_DestinationRealm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_DestinationRealm = { "DestinationRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms, DestinationRealm), Z_Construct_UEnum_AuracronVerticalTransitionsBridge_ERealmType, METADATA_PARAMS(0, nullptr) }; // 2881442322
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_SourceRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_SourceRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_DestinationRealm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_DestinationRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "FindPathBetweenRealms", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::AuracronVerticalTransitionsBridge_eventFindPathBetweenRealms_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execFindPathBetweenRealms)
{
	P_GET_ENUM(ERealmType,Z_Param_SourceRealm);
	P_GET_ENUM(ERealmType,Z_Param_DestinationRealm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->FindPathBetweenRealms(ERealmType(Z_Param_SourceRealm),ERealmType(Z_Param_DestinationRealm));
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function FindPathBetweenRealms **********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function GetNearbyTransitions *********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics
{
	struct AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms
	{
		FVector PlayerLocation;
		float SearchRadius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter transi\xc3\xa7\xc3\xb5""es pr\xc3\xb3ximas ao jogador\n     */" },
#endif
		{ "CPP_Default_SearchRadius", "500.000000" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter transi\xc3\xa7\xc3\xb5""es pr\xc3\xb3ximas ao jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRadius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_SearchRadius = { "SearchRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms, SearchRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_PlayerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_SearchRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "GetNearbyTransitions", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::AuracronVerticalTransitionsBridge_eventGetNearbyTransitions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execGetNearbyTransitions)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PlayerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SearchRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetNearbyTransitions(Z_Param_Out_PlayerLocation,Z_Param_SearchRadius);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function GetNearbyTransitions ***********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function GetNetworkStatistics *********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics
{
	struct AuracronVerticalTransitionsBridge_eventGetNetworkStatistics_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\xadsticas da rede\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas da rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventGetNetworkStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "GetNetworkStatistics", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::AuracronVerticalTransitionsBridge_eventGetNetworkStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::AuracronVerticalTransitionsBridge_eventGetNetworkStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execGetNetworkStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetNetworkStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function GetNetworkStatistics ***********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function GetTransitionDataForPython ***
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics
{
	struct AuracronVerticalTransitionsBridge_eventGetTransitionDataForPython_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter dados de transi\xc3\xa7\xc3\xb5""es para Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter dados de transi\xc3\xa7\xc3\xb5""es para Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventGetTransitionDataForPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "GetTransitionDataForPython", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::AuracronVerticalTransitionsBridge_eventGetTransitionDataForPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::AuracronVerticalTransitionsBridge_eventGetTransitionDataForPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execGetTransitionDataForPython)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetTransitionDataForPython();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function GetTransitionDataForPython *****

// ********** Begin Class UAuracronVerticalTransitionsBridge Function InitializePythonBindings *****
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics
{
	struct AuracronVerticalTransitionsBridge_eventInitializePythonBindings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicializar bindings Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar bindings Python" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventInitializePythonBindings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventInitializePythonBindings_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "InitializePythonBindings", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::AuracronVerticalTransitionsBridge_eventInitializePythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::AuracronVerticalTransitionsBridge_eventInitializePythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execInitializePythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function InitializePythonBindings *******

// ********** Begin Class UAuracronVerticalTransitionsBridge Function OptimizeTransitionNetwork ****
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics
{
	struct AuracronVerticalTransitionsBridge_eventOptimizeTransitionNetwork_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar rede de transi\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar rede de transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventOptimizeTransitionNetwork_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventOptimizeTransitionNetwork_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "OptimizeTransitionNetwork", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::AuracronVerticalTransitionsBridge_eventOptimizeTransitionNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::AuracronVerticalTransitionsBridge_eventOptimizeTransitionNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execOptimizeTransitionNetwork)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeTransitionNetwork();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function OptimizeTransitionNetwork ******

// ********** Begin Class UAuracronVerticalTransitionsBridge Function PlayTransitionEffect *********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics
{
	struct AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms
	{
		FString TransitionID;
		bool bActivation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir efeito de transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "CPP_Default_bActivation", "true" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir efeito de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static void NewProp_bActivation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActivation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_bActivation_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms*)Obj)->bActivation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_bActivation = { "bActivation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_bActivation_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_bActivation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "PlayTransitionEffect", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::AuracronVerticalTransitionsBridge_eventPlayTransitionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execPlayTransitionEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_GET_UBOOL(Z_Param_bActivation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayTransitionEffect(Z_Param_TransitionID,Z_Param_bActivation);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function PlayTransitionEffect ***********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function RemoveTransitionPoint ********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics
{
	struct AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms
	{
		FString TransitionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover ponto de transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover ponto de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "RemoveTransitionPoint", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::AuracronVerticalTransitionsBridge_eventRemoveTransitionPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execRemoveTransitionPoint)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveTransitionPoint(Z_Param_TransitionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function RemoveTransitionPoint **********

// ********** Begin Class UAuracronVerticalTransitionsBridge Function SetupAudioEffects ************
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics
{
	struct AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms
	{
		FString TransitionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar efeitos sonoros\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "SetupAudioEffects", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::AuracronVerticalTransitionsBridge_eventSetupAudioEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execSetupAudioEffects)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupAudioEffects(Z_Param_TransitionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function SetupAudioEffects **************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function SetupVisualEffects ***********
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics
{
	struct AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms
	{
		FString TransitionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar efeitos visuais\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "SetupVisualEffects", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::AuracronVerticalTransitionsBridge_eventSetupVisualEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execSetupVisualEffects)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupVisualEffects(Z_Param_TransitionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function SetupVisualEffects *************

// ********** Begin Class UAuracronVerticalTransitionsBridge Function StartTransitionChanneling ****
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics
{
	struct AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms
	{
		FString TransitionID;
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar channeling de transi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar channeling de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms, TransitionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "StartTransitionChanneling", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::AuracronVerticalTransitionsBridge_eventStartTransitionChanneling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execStartTransitionChanneling)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransitionID);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartTransitionChanneling(Z_Param_TransitionID,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function StartTransitionChanneling ******

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ValidateNetworkConnectivity **
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics
{
	struct AuracronVerticalTransitionsBridge_eventValidateNetworkConnectivity_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar conectividade da rede\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar conectividade da rede" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventValidateNetworkConnectivity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventValidateNetworkConnectivity_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ValidateNetworkConnectivity", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::AuracronVerticalTransitionsBridge_eventValidateNetworkConnectivity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::AuracronVerticalTransitionsBridge_eventValidateNetworkConnectivity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execValidateNetworkConnectivity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNetworkConnectivity();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ValidateNetworkConnectivity ****

// ********** Begin Class UAuracronVerticalTransitionsBridge Function ValidateNetworkIntegrity *****
struct Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics
{
	struct AuracronVerticalTransitionsBridge_eventValidateNetworkIntegrity_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Vertical Transitions|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar integridade da rede\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar integridade da rede" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVerticalTransitionsBridge_eventValidateNetworkIntegrity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVerticalTransitionsBridge_eventValidateNetworkIntegrity_Parms), &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVerticalTransitionsBridge, nullptr, "ValidateNetworkIntegrity", Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::AuracronVerticalTransitionsBridge_eventValidateNetworkIntegrity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::AuracronVerticalTransitionsBridge_eventValidateNetworkIntegrity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVerticalTransitionsBridge::execValidateNetworkIntegrity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNetworkIntegrity();
	P_NATIVE_END;
}
// ********** End Class UAuracronVerticalTransitionsBridge Function ValidateNetworkIntegrity *******

// ********** Begin Class UAuracronVerticalTransitionsBridge ***************************************
void UAuracronVerticalTransitionsBridge::StaticRegisterNativesUAuracronVerticalTransitionsBridge()
{
	UClass* Class = UAuracronVerticalTransitionsBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateTransition", &UAuracronVerticalTransitionsBridge::execActivateTransition },
		{ "BuildTransitionNetwork", &UAuracronVerticalTransitionsBridge::execBuildTransitionNetwork },
		{ "CancelTransitionChanneling", &UAuracronVerticalTransitionsBridge::execCancelTransitionChanneling },
		{ "CanPlayerUseTransition", &UAuracronVerticalTransitionsBridge::execCanPlayerUseTransition },
		{ "ClearTransitionNetwork", &UAuracronVerticalTransitionsBridge::execClearTransitionNetwork },
		{ "CreateTransitionPoint", &UAuracronVerticalTransitionsBridge::execCreateTransitionPoint },
		{ "DeactivateTransition", &UAuracronVerticalTransitionsBridge::execDeactivateTransition },
		{ "ExecutePythonScript", &UAuracronVerticalTransitionsBridge::execExecutePythonScript },
		{ "ExecuteTransition", &UAuracronVerticalTransitionsBridge::execExecuteTransition },
		{ "FindPathBetweenRealms", &UAuracronVerticalTransitionsBridge::execFindPathBetweenRealms },
		{ "GetNearbyTransitions", &UAuracronVerticalTransitionsBridge::execGetNearbyTransitions },
		{ "GetNetworkStatistics", &UAuracronVerticalTransitionsBridge::execGetNetworkStatistics },
		{ "GetTransitionDataForPython", &UAuracronVerticalTransitionsBridge::execGetTransitionDataForPython },
		{ "InitializePythonBindings", &UAuracronVerticalTransitionsBridge::execInitializePythonBindings },
		{ "OptimizeTransitionNetwork", &UAuracronVerticalTransitionsBridge::execOptimizeTransitionNetwork },
		{ "PlayTransitionEffect", &UAuracronVerticalTransitionsBridge::execPlayTransitionEffect },
		{ "RemoveTransitionPoint", &UAuracronVerticalTransitionsBridge::execRemoveTransitionPoint },
		{ "SetupAudioEffects", &UAuracronVerticalTransitionsBridge::execSetupAudioEffects },
		{ "SetupVisualEffects", &UAuracronVerticalTransitionsBridge::execSetupVisualEffects },
		{ "StartTransitionChanneling", &UAuracronVerticalTransitionsBridge::execStartTransitionChanneling },
		{ "ValidateNetworkConnectivity", &UAuracronVerticalTransitionsBridge::execValidateNetworkConnectivity },
		{ "ValidateNetworkIntegrity", &UAuracronVerticalTransitionsBridge::execValidateNetworkIntegrity },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge;
UClass* UAuracronVerticalTransitionsBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronVerticalTransitionsBridge;
	if (!Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronVerticalTransitionsBridge"),
			Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.InnerSingleton,
			StaticRegisterNativesUAuracronVerticalTransitionsBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge_NoRegister()
{
	return UAuracronVerticalTransitionsBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Vertical Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Transi\xc3\xa7\xc3\xb5""es Verticais\n * Gerencia transi\xc3\xa7\xc3\xb5""es entre os tr\xc3\xaas realms do AURACRON\n */" },
#endif
		{ "DisplayName", "AURACRON Vertical Transitions Bridge" },
		{ "IncludePath", "AuracronVerticalTransitionsBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Transi\xc3\xa7\xc3\xb5""es Verticais\nGerencia transi\xc3\xa7\xc3\xb5""es entre os tr\xc3\xaas realms do AURACRON" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionNetwork_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dados da rede de transi\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados da rede de transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTransitionProperties_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o por tipo de transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o por tipo de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse3DNavigation_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar navega\xc3\xa7\xc3\xa3o 3D */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar navega\xc3\xa7\xc3\xa3o 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDetectionRadius_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de detec\xc3\xa7\xc3\xa3o de transi\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de detec\xc3\xa7\xc3\xa3o de transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxChannelingTime_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\xa1ximo de channeling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\xa1ximo de channeling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVisualEffects_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar efeitos visuais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar efeitos visuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAudioEffects_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar efeitos sonoros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "999999" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVerticalTransitionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionNetwork;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultTransitionProperties_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultTransitionProperties_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultTransitionProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DefaultTransitionProperties;
	static void NewProp_bUse3DNavigation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse3DNavigation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDetectionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxChannelingTime;
	static void NewProp_bUseVisualEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVisualEffects;
	static void NewProp_bUseAudioEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAudioEffects;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GenerationSeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ActivateTransition, "ActivateTransition" }, // 464732164
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_BuildTransitionNetwork, "BuildTransitionNetwork" }, // 2807724380
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CancelTransitionChanneling, "CancelTransitionChanneling" }, // 4167845713
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CanPlayerUseTransition, "CanPlayerUseTransition" }, // 2854626282
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ClearTransitionNetwork, "ClearTransitionNetwork" }, // 3801379034
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_CreateTransitionPoint, "CreateTransitionPoint" }, // 2829672908
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_DeactivateTransition, "DeactivateTransition" }, // 1672619823
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecutePythonScript, "ExecutePythonScript" }, // 763587450
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ExecuteTransition, "ExecuteTransition" }, // 3144322074
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_FindPathBetweenRealms, "FindPathBetweenRealms" }, // 1766298683
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNearbyTransitions, "GetNearbyTransitions" }, // 2164728915
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetNetworkStatistics, "GetNetworkStatistics" }, // 1637887115
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_GetTransitionDataForPython, "GetTransitionDataForPython" }, // 3478821898
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_InitializePythonBindings, "InitializePythonBindings" }, // 292483822
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_OptimizeTransitionNetwork, "OptimizeTransitionNetwork" }, // 442678602
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_PlayTransitionEffect, "PlayTransitionEffect" }, // 3976192017
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_RemoveTransitionPoint, "RemoveTransitionPoint" }, // 1518499086
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupAudioEffects, "SetupAudioEffects" }, // 837788303
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_SetupVisualEffects, "SetupVisualEffects" }, // 2633137356
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_StartTransitionChanneling, "StartTransitionChanneling" }, // 454245681
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkConnectivity, "ValidateNetworkConnectivity" }, // 1381426477
		{ &Z_Construct_UFunction_UAuracronVerticalTransitionsBridge_ValidateNetworkIntegrity, "ValidateNetworkIntegrity" }, // 3582879205
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronVerticalTransitionsBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_TransitionNetwork = { "TransitionNetwork", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVerticalTransitionsBridge, TransitionNetwork), Z_Construct_UScriptStruct_FTransitionNetworkData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionNetwork_MetaData), NewProp_TransitionNetwork_MetaData) }; // 2738299376
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_ValueProp = { "DefaultTransitionProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FTransitionProperties, METADATA_PARAMS(0, nullptr) }; // 3720635809
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_Key_KeyProp = { "DefaultTransitionProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ETransitionType, METADATA_PARAMS(0, nullptr) }; // 196762770
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties = { "DefaultTransitionProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVerticalTransitionsBridge, DefaultTransitionProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTransitionProperties_MetaData), NewProp_DefaultTransitionProperties_MetaData) }; // 196762770 3720635809
void Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUse3DNavigation_SetBit(void* Obj)
{
	((UAuracronVerticalTransitionsBridge*)Obj)->bUse3DNavigation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUse3DNavigation = { "bUse3DNavigation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronVerticalTransitionsBridge), &Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUse3DNavigation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse3DNavigation_MetaData), NewProp_bUse3DNavigation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_TransitionDetectionRadius = { "TransitionDetectionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVerticalTransitionsBridge, TransitionDetectionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDetectionRadius_MetaData), NewProp_TransitionDetectionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_MaxChannelingTime = { "MaxChannelingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVerticalTransitionsBridge, MaxChannelingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxChannelingTime_MetaData), NewProp_MaxChannelingTime_MetaData) };
void Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseVisualEffects_SetBit(void* Obj)
{
	((UAuracronVerticalTransitionsBridge*)Obj)->bUseVisualEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseVisualEffects = { "bUseVisualEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronVerticalTransitionsBridge), &Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseVisualEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVisualEffects_MetaData), NewProp_bUseVisualEffects_MetaData) };
void Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseAudioEffects_SetBit(void* Obj)
{
	((UAuracronVerticalTransitionsBridge*)Obj)->bUseAudioEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseAudioEffects = { "bUseAudioEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronVerticalTransitionsBridge), &Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseAudioEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAudioEffects_MetaData), NewProp_bUseAudioEffects_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVerticalTransitionsBridge, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_TransitionNetwork,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_DefaultTransitionProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUse3DNavigation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_TransitionDetectionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_MaxChannelingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseVisualEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_bUseAudioEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::NewProp_GenerationSeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVerticalTransitionsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::ClassParams = {
	&UAuracronVerticalTransitionsBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.OuterSingleton, Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronVerticalTransitionsBridge);
UAuracronVerticalTransitionsBridge::~UAuracronVerticalTransitionsBridge() {}
// ********** End Class UAuracronVerticalTransitionsBridge *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERealmType_StaticEnum, TEXT("ERealmType"), &Z_Registration_Info_UEnum_ERealmType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2881442322U) },
		{ EAuracronVerticalTransitionType_StaticEnum, TEXT("EAuracronVerticalTransitionType"), &Z_Registration_Info_UEnum_EAuracronVerticalTransitionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3301278572U) },
		{ ETransitionDirection_StaticEnum, TEXT("ETransitionDirection"), &Z_Registration_Info_UEnum_ETransitionDirection, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1752873855U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FTransitionProperties::StaticStruct, Z_Construct_UScriptStruct_FTransitionProperties_Statics::NewStructOps, TEXT("TransitionProperties"), &Z_Registration_Info_UScriptStruct_FTransitionProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTransitionProperties), 3720635809U) },
		{ FTransitionPointData::StaticStruct, Z_Construct_UScriptStruct_FTransitionPointData_Statics::NewStructOps, TEXT("TransitionPointData"), &Z_Registration_Info_UScriptStruct_FTransitionPointData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTransitionPointData), 83480635U) },
		{ FStringArrayWrapper::StaticStruct, Z_Construct_UScriptStruct_FStringArrayWrapper_Statics::NewStructOps, TEXT("StringArrayWrapper"), &Z_Registration_Info_UScriptStruct_FStringArrayWrapper, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FStringArrayWrapper), 2259794444U) },
		{ FTransitionNetworkData::StaticStruct, Z_Construct_UScriptStruct_FTransitionNetworkData_Statics::NewStructOps, TEXT("TransitionNetworkData"), &Z_Registration_Info_UScriptStruct_FTransitionNetworkData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTransitionNetworkData), 2738299376U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronVerticalTransitionsBridge, UAuracronVerticalTransitionsBridge::StaticClass, TEXT("UAuracronVerticalTransitionsBridge"), &Z_Registration_Info_UClass_UAuracronVerticalTransitionsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronVerticalTransitionsBridge), 550630348U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_1690173139(TEXT("/Script/AuracronVerticalTransitionsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h__Script_AuracronVerticalTransitionsBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
