// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageWind.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageWind_generated_h
#error "AuracronFoliageWind.generated.h already included, missing '#pragma once' in AuracronFoliageWind.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageWind_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronFoliageWindManager;
class UMaterialParameterCollection;
class UWorld;
enum class EAuracronSeasonalWindPattern : uint8;
enum class EAuracronWindAnimationType : uint8;
enum class EAuracronWindStrength : uint8;
struct FAuracronSeasonalWindData;
struct FAuracronWindConfiguration;
struct FAuracronWindPerformanceData;
struct FAuracronWindZoneData;

// ********** Begin ScriptStruct FAuracronWindConfiguration ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_115_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWindConfiguration;
// ********** End ScriptStruct FAuracronWindConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronWindZoneData *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_228_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWindZoneData;
// ********** End ScriptStruct FAuracronWindZoneData ***********************************************

// ********** Begin ScriptStruct FAuracronSeasonalWindData *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_338_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSeasonalWindData;
// ********** End ScriptStruct FAuracronSeasonalWindData *******************************************

// ********** Begin ScriptStruct FAuracronWindPerformanceData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_404_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWindPerformanceData;
// ********** End ScriptStruct FAuracronWindPerformanceData ****************************************

// ********** Begin Delegate FOnWindZoneCreated ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_610_DELEGATE \
static void FOnWindZoneCreated_DelegateWrapper(const FMulticastScriptDelegate& OnWindZoneCreated, const FString& ZoneId, FAuracronWindZoneData ZoneData);


// ********** End Delegate FOnWindZoneCreated ******************************************************

// ********** Begin Delegate FOnWindZoneRemoved ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_611_DELEGATE \
static void FOnWindZoneRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnWindZoneRemoved, const FString& ZoneId);


// ********** End Delegate FOnWindZoneRemoved ******************************************************

// ********** Begin Delegate FOnGlobalWindChanged **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_612_DELEGATE \
static void FOnGlobalWindChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGlobalWindChanged, FVector Direction, float Strength);


// ********** End Delegate FOnGlobalWindChanged ****************************************************

// ********** Begin Delegate FOnSeasonalWindChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_613_DELEGATE \
static void FOnSeasonalWindChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSeasonalWindChanged, EAuracronSeasonalWindPattern Pattern);


// ********** End Delegate FOnSeasonalWindChanged **************************************************

// ********** Begin Class UAuracronFoliageWindManager **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogWindStatistics); \
	DECLARE_FUNCTION(execDrawDebugWindZones); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetAffectedFoliageCount); \
	DECLARE_FUNCTION(execGetActiveWindZoneCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execApplyBiomeWindToFoliage); \
	DECLARE_FUNCTION(execGetBiomeWindSettings); \
	DECLARE_FUNCTION(execSetBiomeWindSettings); \
	DECLARE_FUNCTION(execGetMaterialParameterCollection); \
	DECLARE_FUNCTION(execSetMaterialParameterCollection); \
	DECLARE_FUNCTION(execUpdateMaterialParameters); \
	DECLARE_FUNCTION(execGetAnimationSpeed); \
	DECLARE_FUNCTION(execSetAnimationSpeed); \
	DECLARE_FUNCTION(execUpdateFoliageWindAnimation); \
	DECLARE_FUNCTION(execSetFoliageWindAnimation); \
	DECLARE_FUNCTION(execGetSeasonalWindData); \
	DECLARE_FUNCTION(execUpdateSeasonalWinds); \
	DECLARE_FUNCTION(execGetSeasonalWindPattern); \
	DECLARE_FUNCTION(execSetSeasonalWindPattern); \
	DECLARE_FUNCTION(execGetWindAtLocation); \
	DECLARE_FUNCTION(execGetWindDirection); \
	DECLARE_FUNCTION(execSetWindDirection); \
	DECLARE_FUNCTION(execGetWindStrengthValue); \
	DECLARE_FUNCTION(execGetWindStrength); \
	DECLARE_FUNCTION(execSetWindStrength); \
	DECLARE_FUNCTION(execGetWindZonesInArea); \
	DECLARE_FUNCTION(execGetAllWindZones); \
	DECLARE_FUNCTION(execGetWindZone); \
	DECLARE_FUNCTION(execRemoveWindZone); \
	DECLARE_FUNCTION(execUpdateWindZone); \
	DECLARE_FUNCTION(execCreateWindZone); \
	DECLARE_FUNCTION(execIsGlobalWindEnabled); \
	DECLARE_FUNCTION(execEnableGlobalWind); \
	DECLARE_FUNCTION(execGetGlobalWind); \
	DECLARE_FUNCTION(execSetGlobalWind); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageWindManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageWindManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageWindManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageWindManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageWindManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageWindManager(UAuracronFoliageWindManager&&) = delete; \
	UAuracronFoliageWindManager(const UAuracronFoliageWindManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageWindManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageWindManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageWindManager) \
	NO_API virtual ~UAuracronFoliageWindManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_455_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h_458_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageWindManager;

// ********** End Class UAuracronFoliageWindManager ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h

// ********** Begin Enum EAuracronWindType *********************************************************
#define FOREACH_ENUM_EAURACRONWINDTYPE(op) \
	op(EAuracronWindType::Global) \
	op(EAuracronWindType::Directional) \
	op(EAuracronWindType::Radial) \
	op(EAuracronWindType::Vortex) \
	op(EAuracronWindType::Turbulence) \
	op(EAuracronWindType::Seasonal) \
	op(EAuracronWindType::Interactive) \
	op(EAuracronWindType::Custom) 

enum class EAuracronWindType : uint8;
template<> struct TIsUEnumClass<EAuracronWindType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindType>();
// ********** End Enum EAuracronWindType ***********************************************************

// ********** Begin Enum EAuracronWindStrength *****************************************************
#define FOREACH_ENUM_EAURACRONWINDSTRENGTH(op) \
	op(EAuracronWindStrength::Calm) \
	op(EAuracronWindStrength::Light) \
	op(EAuracronWindStrength::Moderate) \
	op(EAuracronWindStrength::Strong) \
	op(EAuracronWindStrength::Gale) \
	op(EAuracronWindStrength::Storm) \
	op(EAuracronWindStrength::Hurricane) \
	op(EAuracronWindStrength::Custom) 

enum class EAuracronWindStrength : uint8;
template<> struct TIsUEnumClass<EAuracronWindStrength> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindStrength>();
// ********** End Enum EAuracronWindStrength *******************************************************

// ********** Begin Enum EAuracronWindAnimationType ************************************************
#define FOREACH_ENUM_EAURACRONWINDANIMATIONTYPE(op) \
	op(EAuracronWindAnimationType::SimpleWave) \
	op(EAuracronWindAnimationType::ComplexWave) \
	op(EAuracronWindAnimationType::Noise) \
	op(EAuracronWindAnimationType::Procedural) \
	op(EAuracronWindAnimationType::PhysicsBased) \
	op(EAuracronWindAnimationType::Custom) 

enum class EAuracronWindAnimationType : uint8;
template<> struct TIsUEnumClass<EAuracronWindAnimationType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindAnimationType>();
// ********** End Enum EAuracronWindAnimationType **************************************************

// ********** Begin Enum EAuracronSeasonalWindPattern **********************************************
#define FOREACH_ENUM_EAURACRONSEASONALWINDPATTERN(op) \
	op(EAuracronSeasonalWindPattern::Spring) \
	op(EAuracronSeasonalWindPattern::Summer) \
	op(EAuracronSeasonalWindPattern::Autumn) \
	op(EAuracronSeasonalWindPattern::Winter) \
	op(EAuracronSeasonalWindPattern::Monsoon) \
	op(EAuracronSeasonalWindPattern::Trade) \
	op(EAuracronSeasonalWindPattern::Westerlies) \
	op(EAuracronSeasonalWindPattern::Custom) 

enum class EAuracronSeasonalWindPattern : uint8;
template<> struct TIsUEnumClass<EAuracronSeasonalWindPattern> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonalWindPattern>();
// ********** End Enum EAuracronSeasonalWindPattern ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
