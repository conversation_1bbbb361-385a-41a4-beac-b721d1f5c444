// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGElementLibrary.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGElementLibrary_generated_h
#error "AuracronPCGElementLibrary.generated.h already included, missing '#pragma once' in AuracronPCGElementLibrary.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGElementLibrary_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronPCGNodeSettings;
enum class EAuracronPCGElementCategory : uint8;

// ********** Begin Class UAuracronPCGPointGridSettings ********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_116_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPointGridSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPointGridSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPointGridSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPointGridSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_116_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPointGridSettings(UAuracronPCGPointGridSettings&&) = delete; \
	UAuracronPCGPointGridSettings(const UAuracronPCGPointGridSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPointGridSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPointGridSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGPointGridSettings) \
	NO_API virtual ~UAuracronPCGPointGridSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_113_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_116_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_116_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_116_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPointGridSettings;

// ********** End Class UAuracronPCGPointGridSettings **********************************************

// ********** Begin Class UAuracronPCGBiomeGeneratorSettings ***************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_173_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGBiomeGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGBiomeGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGBiomeGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_173_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGBiomeGeneratorSettings(UAuracronPCGBiomeGeneratorSettings&&) = delete; \
	UAuracronPCGBiomeGeneratorSettings(const UAuracronPCGBiomeGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGBiomeGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGBiomeGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGBiomeGeneratorSettings) \
	NO_API virtual ~UAuracronPCGBiomeGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_170_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_173_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_173_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_173_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGBiomeGeneratorSettings;

// ********** End Class UAuracronPCGBiomeGeneratorSettings *****************************************

// ********** Begin Class UAuracronPCGAdvancedSurfaceSamplerSettings *******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_231_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedSurfaceSamplerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedSurfaceSamplerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedSurfaceSamplerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_231_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedSurfaceSamplerSettings(UAuracronPCGAdvancedSurfaceSamplerSettings&&) = delete; \
	UAuracronPCGAdvancedSurfaceSamplerSettings(const UAuracronPCGAdvancedSurfaceSamplerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedSurfaceSamplerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedSurfaceSamplerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedSurfaceSamplerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedSurfaceSamplerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_228_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_231_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_231_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_231_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedSurfaceSamplerSettings;

// ********** End Class UAuracronPCGAdvancedSurfaceSamplerSettings *********************************

// ********** Begin Class UAuracronPCGLandscapeSamplerSettings *************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_279_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLandscapeSamplerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLandscapeSamplerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLandscapeSamplerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_279_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLandscapeSamplerSettings(UAuracronPCGLandscapeSamplerSettings&&) = delete; \
	UAuracronPCGLandscapeSamplerSettings(const UAuracronPCGLandscapeSamplerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLandscapeSamplerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLandscapeSamplerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGLandscapeSamplerSettings) \
	NO_API virtual ~UAuracronPCGLandscapeSamplerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_276_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_279_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_279_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_279_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLandscapeSamplerSettings;

// ********** End Class UAuracronPCGLandscapeSamplerSettings ***************************************

// ********** Begin Class UAuracronPCGAdvancedFilterSettings ***************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_331_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedFilterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedFilterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedFilterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_331_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedFilterSettings(UAuracronPCGAdvancedFilterSettings&&) = delete; \
	UAuracronPCGAdvancedFilterSettings(const UAuracronPCGAdvancedFilterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedFilterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedFilterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedFilterSettings) \
	NO_API virtual ~UAuracronPCGAdvancedFilterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_328_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_331_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_331_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_331_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedFilterSettings;

// ********** End Class UAuracronPCGAdvancedFilterSettings *****************************************

// ********** Begin Class UAuracronPCGElementLibrary ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateElementConfiguration); \
	DECLARE_FUNCTION(execCreateElementInstance); \
	DECLARE_FUNCTION(execGetElementCount); \
	DECLARE_FUNCTION(execGetAvailableCategories); \
	DECLARE_FUNCTION(execGetElementsByCategory); \
	DECLARE_FUNCTION(execRegisterAllElements);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGElementLibrary_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGElementLibrary(); \
	friend struct Z_Construct_UClass_UAuracronPCGElementLibrary_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGElementLibrary_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGElementLibrary, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGElementLibrary_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGElementLibrary)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGElementLibrary(UAuracronPCGElementLibrary&&) = delete; \
	UAuracronPCGElementLibrary(const UAuracronPCGElementLibrary&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGElementLibrary); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGElementLibrary); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGElementLibrary) \
	NO_API virtual ~UAuracronPCGElementLibrary();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_386_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h_389_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGElementLibrary;

// ********** End Class UAuracronPCGElementLibrary *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h

// ********** Begin Enum EAuracronPCGElementCategory ***********************************************
#define FOREACH_ENUM_EAURACRONPCGELEMENTCATEGORY(op) \
	op(EAuracronPCGElementCategory::Generators) \
	op(EAuracronPCGElementCategory::Samplers) \
	op(EAuracronPCGElementCategory::Filters) \
	op(EAuracronPCGElementCategory::Transformers) \
	op(EAuracronPCGElementCategory::Spawners) \
	op(EAuracronPCGElementCategory::Utilities) \
	op(EAuracronPCGElementCategory::Noise) \
	op(EAuracronPCGElementCategory::Spatial) \
	op(EAuracronPCGElementCategory::Landscape) \
	op(EAuracronPCGElementCategory::Spline) \
	op(EAuracronPCGElementCategory::Custom) 

enum class EAuracronPCGElementCategory : uint8;
template<> struct TIsUEnumClass<EAuracronPCGElementCategory> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGElementCategory>();
// ********** End Enum EAuracronPCGElementCategory *************************************************

// ********** Begin Enum EAuracronPCGNoiseType *****************************************************
#define FOREACH_ENUM_EAURACRONPCGNOISETYPE(op) \
	op(EAuracronPCGNoiseType::Perlin) \
	op(EAuracronPCGNoiseType::Simplex) \
	op(EAuracronPCGNoiseType::Worley) \
	op(EAuracronPCGNoiseType::Ridge) \
	op(EAuracronPCGNoiseType::Fractal) \
	op(EAuracronPCGNoiseType::Turbulence) 

enum class EAuracronPCGNoiseType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNoiseType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseType>();
// ********** End Enum EAuracronPCGNoiseType *******************************************************

// ********** Begin Enum EAuracronPCGDistributionPattern *******************************************
#define FOREACH_ENUM_EAURACRONPCGDISTRIBUTIONPATTERN(op) \
	op(EAuracronPCGDistributionPattern::Random) \
	op(EAuracronPCGDistributionPattern::Grid) \
	op(EAuracronPCGDistributionPattern::Hexagonal) \
	op(EAuracronPCGDistributionPattern::Poisson) \
	op(EAuracronPCGDistributionPattern::Spiral) \
	op(EAuracronPCGDistributionPattern::Radial) \
	op(EAuracronPCGDistributionPattern::Custom) 

enum class EAuracronPCGDistributionPattern : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDistributionPattern> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDistributionPattern>();
// ********** End Enum EAuracronPCGDistributionPattern *********************************************

// ********** Begin Enum EAuracronPCGBiomeType *****************************************************
#define FOREACH_ENUM_EAURACRONPCGBIOMETYPE(op) \
	op(EAuracronPCGBiomeType::Forest) \
	op(EAuracronPCGBiomeType::Desert) \
	op(EAuracronPCGBiomeType::Grassland) \
	op(EAuracronPCGBiomeType::Mountain) \
	op(EAuracronPCGBiomeType::Swamp) \
	op(EAuracronPCGBiomeType::Tundra) \
	op(EAuracronPCGBiomeType::Ocean) \
	op(EAuracronPCGBiomeType::Urban) \
	op(EAuracronPCGBiomeType::Volcanic) \
	op(EAuracronPCGBiomeType::Crystal) 

enum class EAuracronPCGBiomeType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGBiomeType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGBiomeType>();
// ********** End Enum EAuracronPCGBiomeType *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
