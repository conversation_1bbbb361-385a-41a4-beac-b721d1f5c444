// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronUIBridge_init() {}
	AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature();
	AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature();
	AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature();
	AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature();
	AURACRONUIBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronUIBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronUIBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronUIBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronUIBridge_OnInputPlatformChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronUIBridge_OnMinimapUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIConfigurationUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIHidden__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronUIBridge_OnUIShown__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronUIBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x9B48600E,
				0x8F3D0521,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronUIBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronUIBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronUIBridge(Z_Construct_UPackage__Script_AuracronUIBridge, TEXT("/Script/AuracronUIBridge"), Z_Registration_Info_UPackage__Script_AuracronUIBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x9B48600E, 0x8F3D0521));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
