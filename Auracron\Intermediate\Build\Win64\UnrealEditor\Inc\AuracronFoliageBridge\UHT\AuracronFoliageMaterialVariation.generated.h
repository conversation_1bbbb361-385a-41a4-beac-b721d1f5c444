// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageMaterialVariation.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageMaterialVariation_generated_h
#error "AuracronFoliageMaterialVariation.generated.h already included, missing '#pragma once' in AuracronFoliageMaterialVariation.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageMaterialVariation_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronFoliageMaterialVariationManager;
class UAuracronFoliageSeasonalManager;
class UMaterialInstanceDynamic;
class UMaterialInterface;
class UTexture2D;
class UWorld;
enum class EAuracronSeasonType : uint8;
struct FAuracronColorVariationData;
struct FAuracronFoliageMaterialVariationConfiguration;
struct FAuracronMaterialInstanceData;
struct FAuracronMaterialVariationPerformanceData;
struct FAuracronTextureBlendingData;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronFoliageMaterialVariationConfiguration ********************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_127_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageMaterialVariationConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageMaterialVariationConfiguration;
// ********** End ScriptStruct FAuracronFoliageMaterialVariationConfiguration **********************

// ********** Begin ScriptStruct FAuracronColorVariationData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_258_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronColorVariationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronColorVariationData;
// ********** End ScriptStruct FAuracronColorVariationData *****************************************

// ********** Begin ScriptStruct FAuracronTextureBlendingData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_323_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTextureBlendingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTextureBlendingData;
// ********** End ScriptStruct FAuracronTextureBlendingData ****************************************

// ********** Begin ScriptStruct FAuracronMaterialInstanceData *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_388_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMaterialInstanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMaterialInstanceData;
// ********** End ScriptStruct FAuracronMaterialInstanceData ***************************************

// ********** Begin ScriptStruct FAuracronMaterialVariationPerformanceData *************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_441_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMaterialVariationPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMaterialVariationPerformanceData;
// ********** End ScriptStruct FAuracronMaterialVariationPerformanceData ***************************

// ********** Begin Delegate FOnMaterialInstanceCreated ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_620_DELEGATE \
static void FOnMaterialInstanceCreated_DelegateWrapper(const FMulticastScriptDelegate& OnMaterialInstanceCreated, const FString& InstanceId, UMaterialInstanceDynamic* MaterialInstance);


// ********** End Delegate FOnMaterialInstanceCreated **********************************************

// ********** Begin Delegate FOnMaterialInstanceRemoved ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_621_DELEGATE \
static void FOnMaterialInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnMaterialInstanceRemoved, const FString& InstanceId);


// ********** End Delegate FOnMaterialInstanceRemoved **********************************************

// ********** Begin Delegate FOnColorVariationApplied **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_622_DELEGATE \
static void FOnColorVariationApplied_DelegateWrapper(const FMulticastScriptDelegate& OnColorVariationApplied, const FString& VariationId, FLinearColor ResultColor);


// ********** End Delegate FOnColorVariationApplied ************************************************

// ********** Begin Delegate FOnTextureBlendingCompleted *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_623_DELEGATE \
static void FOnTextureBlendingCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTextureBlendingCompleted, const FString& BlendingId, UTexture2D* ResultTexture);


// ********** End Delegate FOnTextureBlendingCompleted *********************************************

// ********** Begin Class UAuracronFoliageMaterialVariationManager *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogMaterialVariationStatistics); \
	DECLARE_FUNCTION(execDrawDebugMaterialInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetMaterialMemoryUsage); \
	DECLARE_FUNCTION(execGetActiveMaterialInstanceCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execSynchronizeWithBiomeSystem); \
	DECLARE_FUNCTION(execApplySeasonalMaterialChanges); \
	DECLARE_FUNCTION(execIntegrateWithSeasonalSystem); \
	DECLARE_FUNCTION(execSelectMaterialByDensity); \
	DECLARE_FUNCTION(execSelectMaterialByDistance); \
	DECLARE_FUNCTION(execGetMaterialsForBiome); \
	DECLARE_FUNCTION(execAssignProceduralMaterial); \
	DECLARE_FUNCTION(execApplyTextureBlending); \
	DECLARE_FUNCTION(execGetTextureBlending); \
	DECLARE_FUNCTION(execRemoveTextureBlending); \
	DECLARE_FUNCTION(execUpdateTextureBlending); \
	DECLARE_FUNCTION(execCreateTextureBlending); \
	DECLARE_FUNCTION(execApplyColorVariation); \
	DECLARE_FUNCTION(execGetColorVariation); \
	DECLARE_FUNCTION(execRemoveColorVariation); \
	DECLARE_FUNCTION(execUpdateColorVariation); \
	DECLARE_FUNCTION(execCreateColorVariation); \
	DECLARE_FUNCTION(execGetAllMaterialInstances); \
	DECLARE_FUNCTION(execGetMaterialInstance); \
	DECLARE_FUNCTION(execRemoveMaterialInstance); \
	DECLARE_FUNCTION(execUpdateMaterialInstance); \
	DECLARE_FUNCTION(execCreateMaterialInstance); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageMaterialVariationManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageMaterialVariationManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageMaterialVariationManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageMaterialVariationManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageMaterialVariationManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageMaterialVariationManager(UAuracronFoliageMaterialVariationManager&&) = delete; \
	UAuracronFoliageMaterialVariationManager(const UAuracronFoliageMaterialVariationManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageMaterialVariationManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageMaterialVariationManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageMaterialVariationManager) \
	NO_API virtual ~UAuracronFoliageMaterialVariationManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_492_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h_495_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageMaterialVariationManager;

// ********** End Class UAuracronFoliageMaterialVariationManager ***********************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageMaterialVariation_h

// ********** Begin Enum EAuracronMaterialVariationStrategy ****************************************
#define FOREACH_ENUM_EAURACRONMATERIALVARIATIONSTRATEGY(op) \
	op(EAuracronMaterialVariationStrategy::None) \
	op(EAuracronMaterialVariationStrategy::ColorOnly) \
	op(EAuracronMaterialVariationStrategy::TextureBlending) \
	op(EAuracronMaterialVariationStrategy::ParameterVariation) \
	op(EAuracronMaterialVariationStrategy::InstanceSwapping) \
	op(EAuracronMaterialVariationStrategy::ProceduralGeneration) \
	op(EAuracronMaterialVariationStrategy::Hybrid) \
	op(EAuracronMaterialVariationStrategy::Custom) 

enum class EAuracronMaterialVariationStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronMaterialVariationStrategy> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronMaterialVariationStrategy>();
// ********** End Enum EAuracronMaterialVariationStrategy ******************************************

// ********** Begin Enum EAuracronColorVariationMode ***********************************************
#define FOREACH_ENUM_EAURACRONCOLORVARIATIONMODE(op) \
	op(EAuracronColorVariationMode::None) \
	op(EAuracronColorVariationMode::HSV) \
	op(EAuracronColorVariationMode::RGB) \
	op(EAuracronColorVariationMode::Palette) \
	op(EAuracronColorVariationMode::Gradient) \
	op(EAuracronColorVariationMode::Seasonal) \
	op(EAuracronColorVariationMode::Environmental) \
	op(EAuracronColorVariationMode::Custom) 

enum class EAuracronColorVariationMode : uint8;
template<> struct TIsUEnumClass<EAuracronColorVariationMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronColorVariationMode>();
// ********** End Enum EAuracronColorVariationMode *************************************************

// ********** Begin Enum EAuracronTextureBlendingMode **********************************************
#define FOREACH_ENUM_EAURACRONTEXTUREBLENDINGMODE(op) \
	op(EAuracronTextureBlendingMode::None) \
	op(EAuracronTextureBlendingMode::Multiply) \
	op(EAuracronTextureBlendingMode::Screen) \
	op(EAuracronTextureBlendingMode::Overlay) \
	op(EAuracronTextureBlendingMode::SoftLight) \
	op(EAuracronTextureBlendingMode::HardLight) \
	op(EAuracronTextureBlendingMode::ColorDodge) \
	op(EAuracronTextureBlendingMode::ColorBurn) \
	op(EAuracronTextureBlendingMode::Darken) \
	op(EAuracronTextureBlendingMode::Lighten) \
	op(EAuracronTextureBlendingMode::Difference) \
	op(EAuracronTextureBlendingMode::Exclusion) \
	op(EAuracronTextureBlendingMode::Custom) 

enum class EAuracronTextureBlendingMode : uint8;
template<> struct TIsUEnumClass<EAuracronTextureBlendingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTextureBlendingMode>();
// ********** End Enum EAuracronTextureBlendingMode ************************************************

// ********** Begin Enum EAuracronMaterialAssignmentMode *******************************************
#define FOREACH_ENUM_EAURACRONMATERIALASSIGNMENTMODE(op) \
	op(EAuracronMaterialAssignmentMode::Random) \
	op(EAuracronMaterialAssignmentMode::DistanceBased) \
	op(EAuracronMaterialAssignmentMode::DensityBased) \
	op(EAuracronMaterialAssignmentMode::BiomeBased) \
	op(EAuracronMaterialAssignmentMode::SeasonalBased) \
	op(EAuracronMaterialAssignmentMode::AttributeBased) \
	op(EAuracronMaterialAssignmentMode::RuleBased) \
	op(EAuracronMaterialAssignmentMode::Custom) 

enum class EAuracronMaterialAssignmentMode : uint8;
template<> struct TIsUEnumClass<EAuracronMaterialAssignmentMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronMaterialAssignmentMode>();
// ********** End Enum EAuracronMaterialAssignmentMode *********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
