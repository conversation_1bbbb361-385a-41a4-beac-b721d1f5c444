// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Extended Element Library Header
// Bridge 2.3: PCG Framework - Element Library (Extended)

#pragma once

#include "CoreMinimal.h"
#include "AuracronPCGElementLibrary.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// Additional PCG includes
#include "Elements/PCGSplineSampler.h"
#include "Elements/PCGMeshSelectorWeighted.h"
#include "Elements/PCGAttributeNoise.h"

#include "AuracronPCGElementLibraryExtended.generated.h"

// =============================================================================
// TRANSFORMER ELEMENTS
// =============================================================================

/**
 * Point Transformer
 * Advanced point transformation with multiple transformation modes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGPointTransformerSettings, FAuracronPCGPointTransformerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGPointTransformerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGPointTransformerSettings();

    // Transform modes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformRotation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformScale = true;

    // Position transformation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position Transform", meta = (EditCondition = "bTransformPosition"))
    FVector PositionOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position Transform", meta = (EditCondition = "bTransformPosition"))
    FVector PositionMin = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position Transform", meta = (EditCondition = "bTransformPosition"))
    FVector PositionMax = FVector::ZeroVector;

    // Rotation transformation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation Transform", meta = (EditCondition = "bTransformRotation"))
    FRotator RotationOffset = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation Transform", meta = (EditCondition = "bTransformRotation"))
    FRotator RotationMin = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation Transform", meta = (EditCondition = "bTransformRotation"))
    FRotator RotationMax = FRotator(0, 0, 360);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation Transform", meta = (EditCondition = "bTransformRotation"))
    bool bAbsoluteRotation = false;

    // Scale transformation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale Transform", meta = (EditCondition = "bTransformScale"))
    FVector ScaleOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale Transform", meta = (EditCondition = "bTransformScale"))
    FVector ScaleMin = FVector(0.5f, 0.5f, 0.5f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale Transform", meta = (EditCondition = "bTransformScale"))
    FVector ScaleMax = FVector(1.5f, 1.5f, 1.5f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale Transform", meta = (EditCondition = "bTransformScale"))
    bool bUniformScale = true;

    // Noise-based transformation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform")
    bool bUseNoiseTransform = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform"))
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform", ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform", ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseIntensity = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGPointTransformerElement, UAuracronPCGPointTransformerSettings)

// =============================================================================
// SPAWNER ELEMENTS
// =============================================================================

/**
 * Advanced Mesh Spawner
 * Enhanced mesh spawning with weighted selection and LOD support
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedMeshSpawnerSettings, FAuracronPCGAdvancedMeshSpawnerElement)

USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGMeshEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry")
    TSoftObjectPtr<UStaticMesh> Mesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry")
    FVector ScaleMultiplier = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry")
    bool bUseCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Entry")
    bool bCastShadows = true;

    FAuracronPCGMeshEntry()
    {
        Weight = 1.0f;
        ScaleMultiplier = FVector::OneVector;
        bUseCollision = true;
        bCastShadows = true;
    }
};

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedMeshSpawnerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedMeshSpawnerSettings();

    // Mesh entries
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Settings")
    TArray<FAuracronPCGMeshEntry> MeshEntries;

    // Spawning settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SpawnProbability = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning Settings")
    bool bAlignToSurface = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning Settings")
    bool bScaleByDensity = false;

    // LOD settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Settings")
    bool bUseLOD = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Settings", meta = (EditCondition = "bUseLOD"))
    TArray<float> LODDistances;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Settings", meta = (EditCondition = "bUseLOD"))
    TArray<TSoftObjectPtr<UStaticMesh>> LODMeshes;

    // Culling settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Settings")
    bool bUseCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling Settings", meta = (EditCondition = "bUseCulling", ClampMin = "100.0", ClampMax = "10000.0"))
    float CullingDistance = 2000.0f;

    // Instance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance Settings")
    bool bUseInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance Settings", meta = (EditCondition = "bUseInstancing", ClampMin = "1", ClampMax = "10000"))
    int32 MaxInstancesPerComponent = 1000;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedMeshSpawnerElement, UAuracronPCGAdvancedMeshSpawnerSettings)

// =============================================================================
// UTILITY ELEMENTS
// =============================================================================

/**
 * Attribute Modifier
 * Modifies point attributes with various operations
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAttributeModifierSettings, FAuracronPCGAttributeModifierElement)

UENUM(BlueprintType)
enum class EAuracronPCGAttributeOperation : uint8
{
    Set             UMETA(DisplayName = "Set"),
    Add             UMETA(DisplayName = "Add"),
    Multiply        UMETA(DisplayName = "Multiply"),
    Min             UMETA(DisplayName = "Min"),
    Max             UMETA(DisplayName = "Max"),
    Lerp            UMETA(DisplayName = "Lerp"),
    Clamp           UMETA(DisplayName = "Clamp"),
    Normalize       UMETA(DisplayName = "Normalize"),
    Invert          UMETA(DisplayName = "Invert")
};

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGExtendedAttributeModifierSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAttributeModifierSettings();

    // Attribute settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Settings")
    FString AttributeName = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Settings")
    EAuracronPCGAttributeOperation Operation = EAuracronPCGAttributeOperation::Set;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Settings")
    float Value = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Settings")
    FVector VectorValue = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Settings")
    bool bUseVectorValue = false;

    // Range settings (for clamp and lerp operations)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range Settings")
    float MinValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range Settings")
    float MaxValue = 1.0f;

    // Noise modulation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Modulation")
    bool bUseNoise = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Modulation", meta = (EditCondition = "bUseNoise"))
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Modulation", meta = (EditCondition = "bUseNoise", ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Modulation", meta = (EditCondition = "bUseNoise", ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseInfluence = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAttributeModifierElement, UAuracronPCGAttributeModifierSettings)

/**
 * Debug Visualizer
 * Visualizes point data for debugging purposes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGDebugVisualizerSettings, FAuracronPCGDebugVisualizerElement)

UENUM(BlueprintType)
enum class EAuracronPCGDebugVisualizationMode : uint8
{
    Points          UMETA(DisplayName = "Points"),
    Bounds          UMETA(DisplayName = "Bounds"),
    Normals         UMETA(DisplayName = "Normals"),
    Density         UMETA(DisplayName = "Density"),
    Attributes      UMETA(DisplayName = "Attributes"),
    Connections     UMETA(DisplayName = "Connections")
};

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGDebugVisualizerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGDebugVisualizerSettings();

    // Visualization settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Settings")
    EAuracronPCGDebugVisualizationMode VisualizationMode = EAuracronPCGDebugVisualizationMode::Points;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Settings")
    bool bShowInEditor = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Settings")
    bool bShowInGame = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Settings")
    FLinearColor DebugColor = FLinearColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Settings", meta = (ClampMin = "0.1", ClampMax = "100.0"))
    float DebugSize = 5.0f;

    // Attribute visualization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Visualization", meta = (EditCondition = "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes"))
    FString AttributeToVisualize = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Visualization", meta = (EditCondition = "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes"))
    FLinearColor MinColor = FLinearColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Visualization", meta = (EditCondition = "VisualizationMode == EAuracronPCGDebugVisualizationMode::Attributes"))
    FLinearColor MaxColor = FLinearColor::Red;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings", meta = (ClampMin = "1", ClampMax = "10000"))
    int32 MaxPointsToVisualize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float VisualizationDuration = 0.0f; // 0 = persistent

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGDebugVisualizerElement, UAuracronPCGDebugVisualizerSettings)
