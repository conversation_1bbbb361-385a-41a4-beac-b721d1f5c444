// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Graph System Core Implementation
// Bridge 2.2: PCG Framework - Graph System Core

#include "AuracronPCGGraphSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGComponent.h"
#include "PCGSubsystem.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGEdge.h"
#include "PCGSettings.h"
#include "Elements/PCGExecuteBlueprint.h"

// Engine includes
#include "Engine/World.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Dom/JsonObject.h"

// Initialize static member
int32 UAuracronPCGGraphWrapper::GraphIdCounter = 0;

UAuracronPCGGraphWrapper::UAuracronPCGGraphWrapper()
{
    PCGGraph = nullptr;
    GraphName = TEXT("Untitled Graph");
    ExecutionState = EAuracronPCGGraphState::Idle;
    ExecutionProgress = 0.0f;
    bIsModified = false;
    CreationTime = FDateTime::Now();
    LastModified = CreationTime;
    
    GraphParameters.Empty();
    NodeIdMap.Empty();
    ExecutionStats = FAuracronPCGGraphExecutionStats();
}

bool UAuracronPCGGraphWrapper::InitializeFromPCGGraph(UPCGGraph* SourceGraph)
{
    if (!SourceGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot initialize from null PCG graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    PCGGraph = SourceGraph;
    GraphName = SourceGraph->GetName();
    
    InitializeGraph();
    UpdateNodeIdMap();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Initialized graph wrapper from PCG graph: %s"), *GraphName);
    return true;
}

bool UAuracronPCGGraphWrapper::CreateNewGraph(const FString& NewGraphName)
{
    FScopeLock Lock(&GraphLock);

    // Create new PCG graph
    PCGGraph = NewObject<UPCGGraph>(this, *NewGraphName);
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create new PCG graph: %s"), *NewGraphName);
        return false;
    }

    GraphName = NewGraphName;
    InitializeGraph();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created new PCG graph: %s"), *GraphName);
    return true;
}

bool UAuracronPCGGraphWrapper::CloneGraph(UAuracronPCGGraphWrapper* SourceGraph)
{
    if (!SourceGraph || !SourceGraph->GetPCGGraph())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot clone from null or invalid source graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    // Create a duplicate of the source graph
    UPCGGraph* SourcePCGGraph = SourceGraph->GetPCGGraph();
    PCGGraph = DuplicateObject(SourcePCGGraph, this);
    
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to duplicate PCG graph"));
        return false;
    }

    GraphName = SourceGraph->GetGraphName() + TEXT("_Copy");
    GraphParameters = SourceGraph->GetAllGraphParameters();
    
    InitializeGraph();
    UpdateNodeIdMap();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cloned PCG graph: %s"), *GraphName);
    return true;
}

FString UAuracronPCGGraphWrapper::AddNode(TSubclassOf<UPCGSettings> NodeClass, const FVector2D& Position)
{
    if (!NodeClass || !PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot add node - invalid node class or graph"));
        return FString();
    }

    if (!ValidateNodeType(NodeClass))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Invalid node type: %s"), *NodeClass->GetName());
        return FString();
    }

    FScopeLock Lock(&GraphLock);

    // Create new node
    UPCGNode* NewNode = PCGGraph->AddNode(NodeClass);
    if (!NewNode)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create node of type: %s"), *NodeClass->GetName());
        return FString();
    }

    // Generate unique ID and store mapping
    FString NodeId = GenerateUniqueNodeId();
    NodeIdMap.Add(NodeId, NewNode);

    // Set position if provided
    if (!Position.IsZero())
    {
        NewNode->SetPosition(Position);
    }

    OnGraphModified();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Added node %s of type %s to graph %s"), 
                              *NodeId, *NodeClass->GetName(), *GraphName);
    return NodeId;
}

bool UAuracronPCGGraphWrapper::RemoveNode(const FString& NodeId)
{
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot remove node - no graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    UPCGNode** NodePtr = NodeIdMap.Find(NodeId);
    if (!NodePtr || !*NodePtr)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node not found: %s"), *NodeId);
        return false;
    }

    UPCGNode* NodeToRemove = *NodePtr;

    // Remove the node from the graph
    if (PCGGraph->RemoveNode(NodeToRemove))
    {
        NodeIdMap.Remove(NodeId);
        OnGraphModified();
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Removed node %s from graph %s"), *NodeId, *GraphName);
        return true;
    }

    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to remove node %s from graph"), *NodeId);
    return false;
}

bool UAuracronPCGGraphWrapper::MoveNode(const FString& NodeId, const FVector2D& NewPosition)
{
    FScopeLock Lock(&GraphLock);

    UPCGNode** NodePtr = NodeIdMap.Find(NodeId);
    if (!NodePtr || !*NodePtr)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node not found for move: %s"), *NodeId);
        return false;
    }

    (*NodePtr)->SetPosition(NewPosition);
    OnGraphModified();

    AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Moved node %s to position (%f, %f)"), 
                              *NodeId, NewPosition.X, NewPosition.Y);
    return true;
}

TArray<FString> UAuracronPCGGraphWrapper::GetAllNodeIds() const
{
    FScopeLock Lock(&GraphLock);

    TArray<FString> NodeIds;
    NodeIdMap.GetKeys(NodeIds);
    return NodeIds;
}

UPCGNode* UAuracronPCGGraphWrapper::GetNodeById(const FString& NodeId) const
{
    FScopeLock Lock(&GraphLock);

    UPCGNode* const* NodePtr = NodeIdMap.Find(NodeId);
    return NodePtr ? *NodePtr : nullptr;
}

bool UAuracronPCGGraphWrapper::ConnectPins(const FString& SourceNodeId, const FString& SourcePinName, 
                                          const FString& TargetNodeId, const FString& TargetPinName)
{
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot connect pins - no graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    // Get source and target nodes
    UPCGNode* SourceNode = GetNodeById(SourceNodeId);
    UPCGNode* TargetNode = GetNodeById(TargetNodeId);

    if (!SourceNode || !TargetNode)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot connect pins - invalid nodes (Source: %s, Target: %s)"), 
                                  *SourceNodeId, *TargetNodeId);
        return false;
    }

    // Validate connection
    if (!IsValidConnection(SourceNodeId, SourcePinName, TargetNodeId, TargetPinName))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Invalid pin connection: %s.%s -> %s.%s"), 
                                  *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
        return false;
    }

    // Find pins
    UPCGPin* SourcePin = SourceNode->GetOutputPin(FName(*SourcePinName));
    UPCGPin* TargetPin = TargetNode->GetInputPin(FName(*TargetPinName));

    if (!SourcePin || !TargetPin)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot find pins for connection: %s.%s -> %s.%s"), 
                                  *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
        return false;
    }

    // Create connection
    if (SourcePin->AddEdgeTo(TargetPin))
    {
        OnGraphModified();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Connected pins: %s.%s -> %s.%s"), 
                                  *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
        return true;
    }

    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create edge between pins: %s.%s -> %s.%s"), 
                              *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
    return false;
}

bool UAuracronPCGGraphWrapper::DisconnectPins(const FString& SourceNodeId, const FString& SourcePinName, 
                                             const FString& TargetNodeId, const FString& TargetPinName)
{
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot disconnect pins - no graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    // Get source and target nodes
    UPCGNode* SourceNode = GetNodeById(SourceNodeId);
    UPCGNode* TargetNode = GetNodeById(TargetNodeId);

    if (!SourceNode || !TargetNode)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot disconnect pins - invalid nodes"));
        return false;
    }

    // Find pins
    UPCGPin* SourcePin = SourceNode->GetOutputPin(FName(*SourcePinName));
    UPCGPin* TargetPin = TargetNode->GetInputPin(FName(*TargetPinName));

    if (!SourcePin || !TargetPin)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot find pins for disconnection"));
        return false;
    }

    // Remove connection
    if (SourcePin->RemoveEdgeTo(TargetPin))
    {
        OnGraphModified();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Disconnected pins: %s.%s -> %s.%s"), 
                                  *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
        return true;
    }

    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("No edge found to remove between pins: %s.%s -> %s.%s"), 
                              *SourceNodeId, *SourcePinName, *TargetNodeId, *TargetPinName);
    return false;
}

TArray<FAuracronPCGPinConnection> UAuracronPCGGraphWrapper::GetAllConnections() const
{
    TArray<FAuracronPCGPinConnection> Connections;

    if (!PCGGraph)
    {
        return Connections;
    }

    FScopeLock Lock(&GraphLock);

    // Iterate through all nodes and their pins
    for (const auto& NodePair : NodeIdMap)
    {
        const FString& NodeId = NodePair.Key;
        UPCGNode* Node = NodePair.Value;

        if (!Node)
        {
            continue;
        }

        // Check output pins for connections
        for (UPCGPin* OutputPin : Node->GetOutputPins())
        {
            if (!OutputPin)
            {
                continue;
            }

            for (const UPCGEdge* Edge : OutputPin->Edges)
            {
                if (!Edge || !Edge->InputPin || !Edge->InputPin->Node)
                {
                    continue;
                }

                // Find target node ID
                FString TargetNodeId;
                for (const auto& TargetNodePair : NodeIdMap)
                {
                    if (TargetNodePair.Value == Edge->InputPin->Node)
                    {
                        TargetNodeId = TargetNodePair.Key;
                        break;
                    }
                }

                if (!TargetNodeId.IsEmpty())
                {
                    FAuracronPCGPinConnection Connection;
                    Connection.SourceNodeId = NodeId;
                    Connection.SourcePinName = OutputPin->Properties.Label.ToString();
                    Connection.TargetNodeId = TargetNodeId;
                    Connection.TargetPinName = Edge->InputPin->Properties.Label.ToString();
                    Connection.PinType = EAuracronPCGPinType::Output;
                    Connection.bIsValid = true;

                    Connections.Add(Connection);
                }
            }
        }
    }

    return Connections;
}

bool UAuracronPCGGraphWrapper::IsValidConnection(const FString& SourceNodeId, const FString& SourcePinName, 
                                                const FString& TargetNodeId, const FString& TargetPinName) const
{
    // Basic validation
    if (SourceNodeId == TargetNodeId)
    {
        return false; // No self-connections
    }

    UPCGNode* SourceNode = GetNodeById(SourceNodeId);
    UPCGNode* TargetNode = GetNodeById(TargetNodeId);

    if (!SourceNode || !TargetNode)
    {
        return false;
    }

    // Use the validator for detailed validation
    return FAuracronPCGGraphValidator::ValidatePinConnection(SourceNode, SourcePinName, TargetNode, TargetPinName);
}

FAuracronPCGGraphValidationResult UAuracronPCGGraphWrapper::ValidateGraph() const
{
    return FAuracronPCGGraphValidator::ValidateGraph(this);
}

bool UAuracronPCGGraphWrapper::HasCycles() const
{
    return FAuracronPCGGraphValidator::DetectCycles(this);
}

TArray<FString> UAuracronPCGGraphWrapper::FindDisconnectedNodes() const
{
    return FAuracronPCGGraphValidator::FindDisconnectedNodes(this);
}

bool UAuracronPCGGraphWrapper::ValidateNodeConnections(const FString& NodeId, TArray<FString>& ValidationErrors) const
{
    return FAuracronPCGGraphValidator::ValidateNodeConnections(this, NodeId, ValidationErrors);
}

bool UAuracronPCGGraphWrapper::ExecuteGraph(AActor* TargetActor)
{
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute - no graph"));
        return false;
    }

    // Validate graph before execution
    FAuracronPCGGraphValidationResult ValidationResult = ValidateGraph();
    if (!ValidationResult.bIsValid)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute invalid graph: %s"), *GraphName);
        for (const FString& Error : ValidationResult.Errors)
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("  - %s"), *Error);
        }
        return false;
    }

    FScopeLock Lock(&GraphLock);

    ExecutionState = EAuracronPCGGraphState::Executing;
    ExecutionProgress = 0.0f;
    ExecutionStats = FAuracronPCGGraphExecutionStats();
    ExecutionStats.StartTime = FDateTime::Now();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Starting synchronous execution of graph: %s"), *GraphName);

    bool bSuccess = false;

    try
    {
        // Get or create PCG component
        UPCGComponent* PCGComponent = nullptr;

        if (TargetActor)
        {
            PCGComponent = TargetActor->FindComponentByClass<UPCGComponent>();
            if (!PCGComponent)
            {
                PCGComponent = NewObject<UPCGComponent>(TargetActor);
                TargetActor->AddInstanceComponent(PCGComponent);
                PCGComponent->RegisterComponent();
            }
        }
        else
        {
            // Create a temporary component for execution
            PCGComponent = NewObject<UPCGComponent>();
        }

        if (PCGComponent)
        {
            // Set the graph and execute
            PCGComponent->SetGraph(PCGGraph);
            PCGComponent->GenerateLocal(true); // Synchronous execution

            ExecutionProgress = 1.0f;
            ExecutionState = EAuracronPCGGraphState::Completed;
            bSuccess = true;
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Exception during graph execution: %s"), UTF8_TO_TCHAR(e.what()));
        ExecutionState = EAuracronPCGGraphState::Error;
        bSuccess = false;
    }

    // Update execution stats
    ExecutionStats.EndTime = FDateTime::Now();
    FTimespan ExecutionTime = ExecutionStats.EndTime - ExecutionStats.StartTime;
    ExecutionStats.TotalExecutionTime = ExecutionTime.GetTotalSeconds();
    ExecutionStats.NodesExecuted = GetNodeCount();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Graph execution completed: %s (Success: %s, Time: %.3fs)"),
                              *GraphName, bSuccess ? TEXT("Yes") : TEXT("No"), ExecutionStats.TotalExecutionTime);

    return bSuccess;
}

bool UAuracronPCGGraphWrapper::ExecuteGraphAsync(AActor* TargetActor)
{
    if (!PCGGraph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute - no graph"));
        return false;
    }

    // Validate graph before execution
    FAuracronPCGGraphValidationResult ValidationResult = ValidateGraph();
    if (!ValidationResult.bIsValid)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute invalid graph: %s"), *GraphName);
        return false;
    }

    FScopeLock Lock(&GraphLock);

    ExecutionState = EAuracronPCGGraphState::Executing;
    ExecutionProgress = 0.0f;
    ExecutionStats = FAuracronPCGGraphExecutionStats();
    ExecutionStats.StartTime = FDateTime::Now();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Starting asynchronous execution of graph: %s"), *GraphName);

    // Execute asynchronously
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, TargetActor]()
    {
        bool bSuccess = ExecuteGraph(TargetActor);

        // Update state on completion
        FScopeLock AsyncLock(&GraphLock);
        ExecutionState = bSuccess ? EAuracronPCGGraphState::Completed : EAuracronPCGGraphState::Error;
    });

    return true;
}

bool UAuracronPCGGraphWrapper::CancelExecution()
{
    FScopeLock Lock(&GraphLock);

    if (ExecutionState == EAuracronPCGGraphState::Executing)
    {
        ExecutionState = EAuracronPCGGraphState::Cancelled;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cancelled execution of graph: %s"), *GraphName);
        return true;
    }

    return false;
}

bool UAuracronPCGGraphWrapper::SaveGraphToFile(const FString& FilePath)
{
    FAuracronPCGGraphSerializationData SerializationData = GetSerializationData();

    // Convert to JSON
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("GraphName"), SerializationData.GraphName);
    JsonObject->SetStringField(TEXT("GraphVersion"), SerializationData.GraphVersion);
    JsonObject->SetStringField(TEXT("CreationTime"), SerializationData.CreationTime.ToIso8601());
    JsonObject->SetStringField(TEXT("LastModified"), SerializationData.LastModified.ToIso8601());

    // Add node data
    TArray<TSharedPtr<FJsonValue>> NodeArray;
    for (const FString& NodeData : SerializationData.NodeData)
    {
        NodeArray.Add(MakeShareable(new FJsonValueString(NodeData)));
    }
    JsonObject->SetArrayField(TEXT("NodeData"), NodeArray);

    // Add connections
    TArray<TSharedPtr<FJsonValue>> ConnectionArray;
    for (const FAuracronPCGPinConnection& Connection : SerializationData.Connections)
    {
        TSharedPtr<FJsonObject> ConnectionObj = MakeShareable(new FJsonObject);
        ConnectionObj->SetStringField(TEXT("SourceNodeId"), Connection.SourceNodeId);
        ConnectionObj->SetStringField(TEXT("SourcePinName"), Connection.SourcePinName);
        ConnectionObj->SetStringField(TEXT("TargetNodeId"), Connection.TargetNodeId);
        ConnectionObj->SetStringField(TEXT("TargetPinName"), Connection.TargetPinName);
        ConnectionObj->SetNumberField(TEXT("PinType"), (int32)Connection.PinType);
        ConnectionObj->SetBoolField(TEXT("IsValid"), Connection.bIsValid);

        ConnectionArray.Add(MakeShareable(new FJsonValueObject(ConnectionObj)));
    }
    JsonObject->SetArrayField(TEXT("Connections"), ConnectionArray);

    // Add parameters
    TSharedPtr<FJsonObject> ParametersObj = MakeShareable(new FJsonObject);
    for (const auto& Param : SerializationData.GraphParameters)
    {
        ParametersObj->SetStringField(Param.Key, Param.Value);
    }
    JsonObject->SetObjectField(TEXT("GraphParameters"), ParametersObj);

    // Serialize to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Save to file
    bool bSuccess = FFileHelper::SaveStringToFile(OutputString, *FilePath);

    if (bSuccess)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Saved graph to file: %s"), *FilePath);
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save graph to file: %s"), *FilePath);
    }

    return bSuccess;
}

bool UAuracronPCGGraphWrapper::LoadGraphFromFile(const FString& FilePath)
{
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to load graph file: %s"), *FilePath);
        return false;
    }

    return DeserializeGraphFromString(FileContent);
}

FString UAuracronPCGGraphWrapper::SerializeGraphToString() const
{
    FAuracronPCGGraphSerializationData SerializationData = GetSerializationData();

    // Simple string serialization for basic use cases
    FString Result;
    Result += FString::Printf(TEXT("GraphName=%s\n"), *SerializationData.GraphName);
    Result += FString::Printf(TEXT("GraphVersion=%s\n"), *SerializationData.GraphVersion);
    Result += FString::Printf(TEXT("NodeCount=%d\n"), SerializationData.NodeData.Num());
    Result += FString::Printf(TEXT("ConnectionCount=%d\n"), SerializationData.Connections.Num());

    return Result;
}

bool UAuracronPCGGraphWrapper::DeserializeGraphFromString(const FString& SerializedData)
{
    // Parse JSON data
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(SerializedData);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to parse JSON data for graph deserialization"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    // Clear existing graph
    ClearGraph();

    // Create new graph
    FString LoadedGraphName;
    if (JsonObject->TryGetStringField(TEXT("GraphName"), LoadedGraphName))
    {
        if (!CreateNewGraph(LoadedGraphName))
        {
            return false;
        }
    }

    // Load parameters
    const TSharedPtr<FJsonObject>* ParametersObj;
    if (JsonObject->TryGetObjectField(TEXT("GraphParameters"), ParametersObj) && ParametersObj->IsValid())
    {
        for (const auto& Param : (*ParametersObj)->Values)
        {
            FString ParamValue;
            if (Param.Value->TryGetString(ParamValue))
            {
                SetGraphParameter(Param.Key, ParamValue);
            }
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Loaded graph from serialized data: %s"), *GraphName);
    return true;
}

FAuracronPCGGraphSerializationData UAuracronPCGGraphWrapper::GetSerializationData() const
{
    FAuracronPCGGraphSerializationData Data;

    FScopeLock Lock(&GraphLock);

    Data.GraphName = GraphName;
    Data.GraphVersion = TEXT("2.2.0");
    Data.GraphParameters = GraphParameters;
    Data.CreationTime = CreationTime;
    Data.LastModified = LastModified;

    // Serialize node data
    for (const auto& NodePair : NodeIdMap)
    {
        if (NodePair.Value)
        {
            FString NodeData = FString::Printf(TEXT("NodeId=%s,Type=%s"),
                                              *NodePair.Key,
                                              *NodePair.Value->GetSettings()->GetClass()->GetName());
            Data.NodeData.Add(NodeData);
        }
    }

    // Get connections
    Data.Connections = GetAllConnections();

    return Data;
}

bool UAuracronPCGGraphWrapper::SetGraphParameter(const FString& ParameterName, const FString& ParameterValue)
{
    if (ParameterName.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&GraphLock);

    GraphParameters.Add(ParameterName, ParameterValue);
    OnGraphModified();

    AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Set graph parameter: %s = %s"), *ParameterName, *ParameterValue);
    return true;
}

FString UAuracronPCGGraphWrapper::GetGraphParameter(const FString& ParameterName, const FString& DefaultValue) const
{
    FScopeLock Lock(&GraphLock);

    if (const FString* Value = GraphParameters.Find(ParameterName))
    {
        return *Value;
    }
    return DefaultValue;
}

bool UAuracronPCGGraphWrapper::RemoveGraphParameter(const FString& ParameterName)
{
    FScopeLock Lock(&GraphLock);

    if (GraphParameters.Remove(ParameterName) > 0)
    {
        OnGraphModified();
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Removed graph parameter: %s"), *ParameterName);
        return true;
    }
    return false;
}

void UAuracronPCGGraphWrapper::ClearGraph()
{
    FScopeLock Lock(&GraphLock);

    if (PCGGraph)
    {
        // Clear all nodes
        TArray<UPCGNode*> AllNodes = PCGGraph->GetNodes();
        for (UPCGNode* Node : AllNodes)
        {
            if (Node)
            {
                PCGGraph->RemoveNode(Node);
            }
        }
    }

    NodeIdMap.Empty();
    GraphParameters.Empty();
    ExecutionState = EAuracronPCGGraphState::Idle;
    ExecutionProgress = 0.0f;

    OnGraphModified();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared graph: %s"), *GraphName);
}

UAuracronPCGGraphWrapper* UAuracronPCGGraphWrapper::DuplicateGraph() const
{
    UAuracronPCGGraphWrapper* NewGraph = NewObject<UAuracronPCGGraphWrapper>();
    if (NewGraph && NewGraph->CloneGraph(const_cast<UAuracronPCGGraphWrapper*>(this)))
    {
        return NewGraph;
    }
    return nullptr;
}

bool UAuracronPCGGraphWrapper::MergeGraph(UAuracronPCGGraphWrapper* OtherGraph)
{
    if (!OtherGraph || !OtherGraph->GetPCGGraph())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot merge with null or invalid graph"));
        return false;
    }

    FScopeLock Lock(&GraphLock);

    // This is a simplified merge - in production you'd want more sophisticated merging
    TArray<FString> OtherNodeIds = OtherGraph->GetAllNodeIds();

    for (const FString& NodeId : OtherNodeIds)
    {
        UPCGNode* OtherNode = OtherGraph->GetNodeById(NodeId);
        if (OtherNode && OtherNode->GetSettings())
        {
            // Add node with offset position to avoid overlap
            FVector2D Position = OtherNode->GetPosition() + FVector2D(500.0f, 0.0f);
            AddNode(OtherNode->GetSettings()->GetClass(), Position);
        }
    }

    // Merge parameters
    TMap<FString, FString> OtherParameters = OtherGraph->GetAllGraphParameters();
    for (const auto& Param : OtherParameters)
    {
        SetGraphParameter(Param.Key, Param.Value);
    }

    OnGraphModified();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Merged graph %s into %s"),
                              *OtherGraph->GetGraphName(), *GraphName);
    return true;
}

TArray<FString> UAuracronPCGGraphWrapper::GetNodesByType(TSubclassOf<UPCGSettings> NodeType) const
{
    TArray<FString> MatchingNodes;

    if (!NodeType)
    {
        return MatchingNodes;
    }

    FScopeLock Lock(&GraphLock);

    for (const auto& NodePair : NodeIdMap)
    {
        UPCGNode* Node = NodePair.Value;
        if (Node && Node->GetSettings() && Node->GetSettings()->IsA(NodeType))
        {
            MatchingNodes.Add(NodePair.Key);
        }
    }

    return MatchingNodes;
}

// Internal methods implementation

void UAuracronPCGGraphWrapper::InitializeGraph()
{
    if (!PCGGraph)
    {
        return;
    }

    // Ensure graph has input and output nodes
    if (!PCGGraph->GetInputNode())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Graph missing input node: %s"), *GraphName);
    }

    if (!PCGGraph->GetOutputNode())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Graph missing output node: %s"), *GraphName);
    }

    ExecutionState = EAuracronPCGGraphState::Idle;
    ExecutionProgress = 0.0f;
    bIsModified = false;
}

void UAuracronPCGGraphWrapper::UpdateNodeIdMap()
{
    if (!PCGGraph)
    {
        return;
    }

    FScopeLock Lock(&GraphLock);

    NodeIdMap.Empty();
    TArray<UPCGNode*> AllNodes = PCGGraph->GetNodes();

    for (int32 i = 0; i < AllNodes.Num(); ++i)
    {
        UPCGNode* Node = AllNodes[i];
        if (Node)
        {
            FString NodeId = FString::Printf(TEXT("Node_%d_%s"), i, *Node->GetSettings()->GetClass()->GetName());
            NodeIdMap.Add(NodeId, Node);
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Updated node ID map for graph %s (%d nodes)"),
                              *GraphName, NodeIdMap.Num());
}

FString UAuracronPCGGraphWrapper::GenerateUniqueNodeId() const
{
    int32 Counter = 0;
    FString BaseId = FString::Printf(TEXT("Node_%d"), ++GraphIdCounter);
    FString UniqueId = BaseId;

    while (NodeIdMap.Contains(UniqueId))
    {
        UniqueId = FString::Printf(TEXT("%s_%d"), *BaseId, ++Counter);
    }

    return UniqueId;
}

bool UAuracronPCGGraphWrapper::ValidateNodeType(TSubclassOf<UPCGSettings> NodeClass) const
{
    if (!NodeClass)
    {
        return false;
    }

    // Check if it's a valid PCG settings class
    return NodeClass->IsChildOf(UPCGSettings::StaticClass());
}

void UAuracronPCGGraphWrapper::OnGraphModified()
{
    bIsModified = true;
    LastModified = FDateTime::Now();
}

void UAuracronPCGGraphWrapper::UpdateExecutionProgress()
{
    // Update execution progress based on PCG component state
    if (!PCGComponent || !PCGGraph)
    {
        return;
    }
    
    // Get current execution state from PCG component
    bool bIsGenerating = PCGComponent->IsGenerating();
    
    if (bIsGenerating)
    {
        // Calculate progress based on completed tasks
        int32 CompletedTasks = 0;
        int32 TotalTasks = 0;
        
        // Get generation context if available
        if (const FPCGContext* Context = PCGComponent->GetGenerationContext())
        {
            // Count completed and total tasks from the execution context
            TotalTasks = Context->GetInputData().TaggedData.Num();
            
            // Estimate completion based on processed elements
            if (TotalTasks > 0)
            {
                // Calculate actual completion based on PCG execution state
                int32 ActualCompletedTasks = 0;
                
                // Count completed tasks by checking execution state of each PCG component
                for (const auto& TaskPair : ActiveTasks)
                {
                    if (TaskPair.Value.IsValid())
                    {
                        UPCGComponent* PCGComp = TaskPair.Value.Get();
                        if (PCGComp && PCGComp->GetGenerationState() == EPCGComponentGenerationState::Generated)
                        {
                            ActualCompletedTasks++;
                        }
                    }
                }
                
                // Update completed tasks with actual count
                CompletedTasks = FMath::Clamp(ActualCompletedTasks, 0, TotalTasks);
            }
        }
        
        // Update progress percentage
        if (TotalTasks > 0)
        {
            float NewProgress = FMath::Clamp(float(CompletedTasks) / float(TotalTasks), 0.0f, 1.0f);
            
            // Only update if progress has changed significantly
            if (FMath::Abs(NewProgress - ExecutionProgress) > 0.01f)
            {
                ExecutionProgress = NewProgress;
                
                // Broadcast progress update
                OnExecutionProgressUpdated.Broadcast(ExecutionProgress);
                
                UE_LOG(LogAuracronPCGGraphSystem, Log, TEXT("Graph execution progress: %.1f%% (%d/%d tasks)"), 
                       ExecutionProgress * 100.0f, CompletedTasks, TotalTasks);
            }
        }
    }
    else
    {
        // Execution completed or not running
        if (ExecutionProgress < 1.0f && ExecutionProgress > 0.0f)
        {
            ExecutionProgress = 1.0f;
            OnExecutionProgressUpdated.Broadcast(ExecutionProgress);
            OnExecutionCompleted.Broadcast(true);
            
            UE_LOG(LogAuracronPCGGraphSystem, Log, TEXT("Graph execution completed"));
        }
    }
}

// FAuracronPCGGraphValidator implementation

FAuracronPCGGraphValidator::FAuracronPCGGraphValidator()
{
}

FAuracronPCGGraphValidationResult FAuracronPCGGraphValidator::ValidateGraph(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    FAuracronPCGGraphValidationResult Result;

    if (!GraphWrapper || !GraphWrapper->GetPCGGraph())
    {
        Result.Errors.Add(TEXT("Graph wrapper or PCG graph is null"));
        return Result;
    }

    UPCGGraph* PCGGraph = GraphWrapper->GetPCGGraph();

    // Basic structure validation
    if (!PCGGraph->GetInputNode())
    {
        Result.Errors.Add(TEXT("Graph missing input node"));
    }

    if (!PCGGraph->GetOutputNode())
    {
        Result.Errors.Add(TEXT("Graph missing output node"));
    }

    // Count nodes and edges
    TArray<UPCGNode*> AllNodes = PCGGraph->GetNodes();
    Result.NodeCount = AllNodes.Num();

    int32 EdgeCount = 0;
    for (UPCGNode* Node : AllNodes)
    {
        if (Node)
        {
            for (UPCGPin* Pin : Node->GetOutputPins())
            {
                if (Pin)
                {
                    EdgeCount += Pin->Edges.Num();
                }
            }
        }
    }
    Result.EdgeCount = EdgeCount;

    // Check for cycles
    Result.bHasCycles = DetectCycles(GraphWrapper);
    if (Result.bHasCycles)
    {
        Result.Errors.Add(TEXT("Graph contains cycles"));
    }

    // Find disconnected nodes
    Result.DisconnectedNodes = FindDisconnectedNodes(GraphWrapper);
    if (Result.DisconnectedNodes.Num() > 0)
    {
        Result.Warnings.Add(FString::Printf(TEXT("Found %d disconnected nodes"), Result.DisconnectedNodes.Num()));
    }

    // Validate individual nodes
    for (UPCGNode* Node : AllNodes)
    {
        if (!Node)
        {
            Result.Errors.Add(TEXT("Found null node in graph"));
            continue;
        }

        if (!Node->GetSettings())
        {
            Result.Errors.Add(TEXT("Node missing settings"));
            continue;
        }

        // Validate node-specific requirements
        // This could be expanded with more detailed validation
    }

    // Determine overall validity
    Result.bIsValid = Result.Errors.Num() == 0;

    return Result;
}

bool FAuracronPCGGraphValidator::DetectCycles(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    if (!GraphWrapper || !GraphWrapper->GetPCGGraph())
    {
        return false;
    }

    UPCGGraph* PCGGraph = GraphWrapper->GetPCGGraph();
    TArray<UPCGNode*> AllNodes = PCGGraph->GetNodes();

    TSet<const UPCGNode*> Visited;
    TSet<const UPCGNode*> RecursionStack;
    bool bHasCycle = false;

    for (UPCGNode* Node : AllNodes)
    {
        if (Node && !Visited.Contains(Node))
        {
            PerformDepthFirstSearch(Node, Visited, RecursionStack, bHasCycle);
            if (bHasCycle)
            {
                break;
            }
        }
    }

    return bHasCycle;
}

TArray<FString> FAuracronPCGGraphValidator::FindDisconnectedNodes(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    TArray<FString> DisconnectedNodes;

    if (!GraphWrapper || !GraphWrapper->GetPCGGraph())
    {
        return DisconnectedNodes;
    }

    TArray<FString> AllNodeIds = GraphWrapper->GetAllNodeIds();

    for (const FString& NodeId : AllNodeIds)
    {
        UPCGNode* Node = GraphWrapper->GetNodeById(NodeId);
        if (!Node)
        {
            continue;
        }

        bool bHasConnections = false;

        // Check input pins
        for (UPCGPin* InputPin : Node->GetInputPins())
        {
            if (InputPin && InputPin->Edges.Num() > 0)
            {
                bHasConnections = true;
                break;
            }
        }

        // Check output pins if no input connections
        if (!bHasConnections)
        {
            for (UPCGPin* OutputPin : Node->GetOutputPins())
            {
                if (OutputPin && OutputPin->Edges.Num() > 0)
                {
                    bHasConnections = true;
                    break;
                }
            }
        }

        // Skip input and output nodes as they may legitimately have no connections on one side
        if (!bHasConnections &&
            Node != GraphWrapper->GetPCGGraph()->GetInputNode() &&
            Node != GraphWrapper->GetPCGGraph()->GetOutputNode())
        {
            DisconnectedNodes.Add(NodeId);
        }
    }

    return DisconnectedNodes;
}

bool FAuracronPCGGraphValidator::ValidateNodeConnections(const UAuracronPCGGraphWrapper* GraphWrapper, const FString& NodeId, TArray<FString>& ValidationErrors)
{
    ValidationErrors.Empty();

    if (!GraphWrapper)
    {
        ValidationErrors.Add(TEXT("Graph wrapper is null"));
        return false;
    }

    UPCGNode* Node = GraphWrapper->GetNodeById(NodeId);
    if (!Node)
    {
        ValidationErrors.Add(FString::Printf(TEXT("Node not found: %s"), *NodeId));
        return false;
    }

    // Validate input pins
    for (UPCGPin* InputPin : Node->GetInputPins())
    {
        if (!InputPin)
        {
            ValidationErrors.Add(TEXT("Found null input pin"));
            continue;
        }

        for (const UPCGEdge* Edge : InputPin->Edges)
        {
            if (!Edge)
            {
                ValidationErrors.Add(TEXT("Found null edge on input pin"));
                continue;
            }

            if (!Edge->OutputPin)
            {
                ValidationErrors.Add(TEXT("Edge missing output pin"));
                continue;
            }

            // Validate pin compatibility
            if (!IsValidPinType(Edge->OutputPin->Properties, InputPin->Properties))
            {
                ValidationErrors.Add(FString::Printf(TEXT("Incompatible pin types: %s -> %s"),
                                                    *Edge->OutputPin->Properties.Label.ToString(),
                                                    *InputPin->Properties.Label.ToString()));
            }
        }
    }

    // Validate output pins
    for (UPCGPin* OutputPin : Node->GetOutputPins())
    {
        if (!OutputPin)
        {
            ValidationErrors.Add(TEXT("Found null output pin"));
            continue;
        }

        for (const UPCGEdge* Edge : OutputPin->Edges)
        {
            if (!Edge)
            {
                ValidationErrors.Add(TEXT("Found null edge on output pin"));
                continue;
            }

            if (!Edge->InputPin)
            {
                ValidationErrors.Add(TEXT("Edge missing input pin"));
            }
        }
    }

    return ValidationErrors.Num() == 0;
}

bool FAuracronPCGGraphValidator::ValidatePinConnection(const UPCGNode* SourceNode, const FString& SourcePinName,
                                                      const UPCGNode* TargetNode, const FString& TargetPinName)
{
    if (!SourceNode || !TargetNode)
    {
        return false;
    }

    // Find pins
    UPCGPin* SourcePin = SourceNode->GetOutputPin(FName(*SourcePinName));
    UPCGPin* TargetPin = TargetNode->GetInputPin(FName(*TargetPinName));

    if (!SourcePin || !TargetPin)
    {
        return false;
    }

    // Validate pin compatibility
    return IsValidPinType(SourcePin->Properties, TargetPin->Properties);
}

TArray<UPCGNode*> FAuracronPCGGraphValidator::GetExecutionOrder(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    TArray<UPCGNode*> ExecutionOrder;

    if (!GraphWrapper || !GraphWrapper->GetPCGGraph())
    {
        return ExecutionOrder;
    }

    // This is a simplified topological sort
    // In production, you'd want a more sophisticated algorithm
    UPCGGraph* PCGGraph = GraphWrapper->GetPCGGraph();
    TArray<UPCGNode*> AllNodes = PCGGraph->GetNodes();

    // Start with input node
    if (UPCGNode* InputNode = PCGGraph->GetInputNode())
    {
        ExecutionOrder.Add(InputNode);
    }

    // Add other nodes (simplified - doesn't handle complex dependencies)
    for (UPCGNode* Node : AllNodes)
    {
        if (Node && Node != PCGGraph->GetInputNode() && Node != PCGGraph->GetOutputNode())
        {
            ExecutionOrder.Add(Node);
        }
    }

    // End with output node
    if (UPCGNode* OutputNode = PCGGraph->GetOutputNode())
    {
        ExecutionOrder.Add(OutputNode);
    }

    return ExecutionOrder;
}

TMap<FString, int32> FAuracronPCGGraphValidator::AnalyzeNodeDependencies(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    TMap<FString, int32> Dependencies;

    if (!GraphWrapper)
    {
        return Dependencies;
    }

    TArray<FString> AllNodeIds = GraphWrapper->GetAllNodeIds();

    for (const FString& NodeId : AllNodeIds)
    {
        UPCGNode* Node = GraphWrapper->GetNodeById(NodeId);
        if (!Node)
        {
            continue;
        }

        int32 DependencyCount = 0;

        // Count input connections as dependencies
        for (UPCGPin* InputPin : Node->GetInputPins())
        {
            if (InputPin)
            {
                DependencyCount += InputPin->Edges.Num();
            }
        }

        Dependencies.Add(NodeId, DependencyCount);
    }

    return Dependencies;
}

float FAuracronPCGGraphValidator::EstimateExecutionComplexity(const UAuracronPCGGraphWrapper* GraphWrapper)
{
    if (!GraphWrapper || !GraphWrapper->GetPCGGraph())
    {
        return 0.0f;
    }

    float TotalComplexity = 0.0f;
    TArray<UPCGNode*> AllNodes = GraphWrapper->GetPCGGraph()->GetNodes();

    for (UPCGNode* Node : AllNodes)
    {
        if (Node)
        {
            TotalComplexity += CalculateNodeComplexity(Node);
        }
    }

    return TotalComplexity;
}

// Private helper methods

void FAuracronPCGGraphValidator::PerformDepthFirstSearch(const UPCGNode* Node, TSet<const UPCGNode*>& Visited,
                                                        TSet<const UPCGNode*>& RecursionStack, bool& bHasCycle)
{
    if (bHasCycle || !Node)
    {
        return;
    }

    Visited.Add(Node);
    RecursionStack.Add(Node);

    // Visit all connected nodes
    for (UPCGPin* OutputPin : Node->GetOutputPins())
    {
        if (!OutputPin)
        {
            continue;
        }

        for (const UPCGEdge* Edge : OutputPin->Edges)
        {
            if (!Edge || !Edge->InputPin || !Edge->InputPin->Node)
            {
                continue;
            }

            const UPCGNode* ConnectedNode = Edge->InputPin->Node;

            if (RecursionStack.Contains(ConnectedNode))
            {
                bHasCycle = true;
                return;
            }

            if (!Visited.Contains(ConnectedNode))
            {
                PerformDepthFirstSearch(ConnectedNode, Visited, RecursionStack, bHasCycle);
            }
        }
    }

    RecursionStack.Remove(Node);
}

bool FAuracronPCGGraphValidator::IsValidPinType(const FPCGPinProperties& SourcePin, const FPCGPinProperties& TargetPin)
{
    // Check if data types are compatible
    return (SourcePin.AllowedTypes & TargetPin.AllowedTypes) != EPCGDataType::None;
}

int32 FAuracronPCGGraphValidator::CalculateNodeComplexity(const UPCGNode* Node)
{
    if (!Node || !Node->GetSettings())
    {
        return 1;
    }

    // Base complexity
    int32 Complexity = 1;

    // Add complexity based on node type
    UClass* SettingsClass = Node->GetSettings()->GetClass();

    // This is a simplified complexity calculation
    // In production, you'd have more sophisticated metrics
    if (SettingsClass->GetName().Contains(TEXT("Sampler")))
    {
        Complexity += 5; // Sampling operations are more expensive
    }
    else if (SettingsClass->GetName().Contains(TEXT("Transform")))
    {
        Complexity += 2; // Transform operations are moderately expensive
    }
    else if (SettingsClass->GetName().Contains(TEXT("Filter")))
    {
        Complexity += 3; // Filter operations are moderately expensive
    }

    // Add complexity based on number of connections
    Complexity += Node->GetInputPins().Num() + Node->GetOutputPins().Num();

    return Complexity;
}
