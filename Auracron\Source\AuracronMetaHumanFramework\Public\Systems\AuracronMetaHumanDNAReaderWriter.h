#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Engine.h"
#include "AuracronMetaHumanDNAReaderWriter.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanDNAReaderWriter, Log, All);

// === DNA Data Structures ===

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronVertexDelta
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    int32 VertexIndex = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FVector PositionDelta = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FVector NormalDelta = FVector::ZeroVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronBlendShapeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FString Name;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    TArray<FAuracronVertexDelta> VertexDeltas;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    TArray<FString> BoneNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    TArray<int32> BoneParentIndices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    TArray<FString> BlendShapeNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    TArray<FAuracronBlendShapeData> BlendShapeDeltas;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FString Version = TEXT("1.0");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FDateTime CreationTime = FDateTime::Now();
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAReadResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    bool bSuccess = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FString Message;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    float ProcessingTimeMS = 0.0f;

    FAuracronDNAReadResult() = default;
    
    FAuracronDNAReadResult(bool InSuccess, const FString& InMessage)
        : bSuccess(InSuccess), Message(InMessage) {}
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAWriteResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    bool bSuccess = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    FString Message;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    float ProcessingTimeMS = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DNA")
    int32 BytesWritten = 0;

    FAuracronDNAWriteResult() = default;
    
    FAuracronDNAWriteResult(bool InSuccess, const FString& InMessage)
        : bSuccess(InSuccess), Message(InMessage) {}
};

// === Forward Declarations ===
class FAuracronDNAReader;
class FAuracronDNAWriter;

/**
 * MetaHuman DNA Reader/Writer System
 * Handles reading and writing of MetaHuman DNA data using UE5.6 APIs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanDNAReaderWriter : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanDNAReaderWriter();
    virtual ~UAuracronMetaHumanDNAReaderWriter();

    // === Core Functions ===

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    bool Initialize();

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    void Shutdown();

    // === Reading Functions ===

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FAuracronDNAReadResult ReadDNAFromFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FAuracronDNAReadResult ReadDNAFromMemory(const TArray<uint8>& DNAData);

    // === Writing Functions ===

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FAuracronDNAWriteResult WriteDNAToFile(const FString& FilePath, const FAuracronDNAData& DNAData);

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FAuracronDNAWriteResult WriteDNAToMemory(const FAuracronDNAData& DNAData, TArray<uint8>& OutData);

    // === Validation Functions ===

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    bool ValidateDNAData(const FAuracronDNAData& DNAData, TArray<FString>& OutErrors);

    // === Status Functions ===

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FAuracronDNAData GetCurrentDNAData() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    bool IsReaderReady() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    bool IsWriterReady() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FString GetReaderStatus() const;

    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|ReaderWriter")
    FString GetWriterStatus() const;

private:
    UPROPERTY()
    bool bIsInitialized;

    TSharedPtr<FAuracronDNAReader> DNAReaderInstance;
    TSharedPtr<FAuracronDNAWriter> DNAWriterInstance;
};

/**
 * DNA Reader Implementation
 * Handles reading and parsing of MetaHuman DNA data
 */
class AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAReader
{
public:
    FAuracronDNAReader() : bIsReady(false) {}
    virtual ~FAuracronDNAReader() = default;

    bool Initialize();
    void Shutdown();

    FAuracronDNAReadResult ReadFromFile(const FString& FilePath);
    FAuracronDNAReadResult ReadFromMemory(const TArray<uint8>& Data);

    bool IsReady() const { return bIsReady; }
    FString GetStatus() const { return bIsReady ? TEXT("Ready") : TEXT("Not Ready"); }
    FAuracronDNAData GetDNAData() const { return DNAData; }

private:
    bool ParseDNAData(const TArray<uint8>& Data);

    bool bIsReady;
    FAuracronDNAData DNAData;
};

/**
 * DNA Writer Implementation
 * Handles writing and serialization of MetaHuman DNA data
 */
class AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAWriter
{
public:
    FAuracronDNAWriter() : bIsReady(false) {}
    virtual ~FAuracronDNAWriter() = default;

    bool Initialize();
    void Shutdown();

    FAuracronDNAWriteResult WriteToFile(const FString& FilePath, const FAuracronDNAData& DNAData);
    FAuracronDNAWriteResult WriteToMemory(const FAuracronDNAData& DNAData, TArray<uint8>& OutData);

    bool IsReady() const { return bIsReady; }
    FString GetStatus() const { return bIsReady ? TEXT("Ready") : TEXT("Not Ready"); }

private:
    bool SerializeDNAData(const FAuracronDNAData& DNAData, TArray<uint8>& OutData);

    bool bIsReady;
};
