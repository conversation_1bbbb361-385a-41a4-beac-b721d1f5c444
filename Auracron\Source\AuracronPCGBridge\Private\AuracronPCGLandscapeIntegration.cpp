// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Landscape Integration Implementation
// Bridge 2.7: PCG Framework - Landscape Integration

#include "AuracronPCGLandscapeIntegration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeProxy.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeDataAccess.h"
#include "LandscapeEdit.h"
#include "LandscapeHeightfieldCollisionComponent.h"
#include "LandscapeStreamingProxy.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED LANDSCAPE SAMPLER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedLandscapeSamplerSettings::UAuracronPCGAdvancedLandscapeSamplerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Landscape Sampler");
    NodeMetadata.NodeDescription = TEXT("Enhanced version of the native Landscape Sampler with advanced features");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Landscape"));
    NodeMetadata.Tags.Add(TEXT("Sampler"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Height"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.2f);
}

void UAuracronPCGAdvancedLandscapeSamplerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedLandscapeSamplerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& DebugPin = OutputPins.Emplace_GetRef();
    DebugPin.Label = TEXT("Debug Info");
    DebugPin.AllowedTypes = EPCGDataType::Attribute;
    DebugPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGAdvancedLandscapeSamplerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                   FPCGDataCollection& OutputData, 
                                                                                   const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedLandscapeSamplerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Landscape Sampler");
            return Result;
        }

        // Find target landscape
        ALandscape* TargetLandscape = FindTargetLandscape(Settings);
        if (!TargetLandscape)
        {
            Result.ErrorMessage = TEXT("No valid landscape found for sampling");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsSampled = 0;
        TArray<FPCGTaggedData> ProcessedData;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);
            
            TArray<FPCGPoint> OutputPoints;
            OutputPoints.Reserve(InputPoints.Num());

            // Sample landscape for each point
            if (Settings->bUseAsyncSampling && InputPoints.Num() > 100)
            {
                SampleLandscapeAsync(InputPoints, OutputPoints, TargetLandscape, Settings, PointsSampled);
            }
            else
            {
                SampleLandscapeSequential(InputPoints, OutputPoints, TargetLandscape, Settings, PointsSampled);
            }

            // Set output points
            OutputPointData->GetMutablePoints() = OutputPoints;

            // Add to output
            FPCGTaggedData& OutputTaggedData = ProcessedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Pin = TEXT("Output");
            OutputTaggedData.Tags = TaggedData.Tags;

            TotalProcessed += InputPoints.Num();
        }

        // Add processed data to output
        OutputData.TaggedData.Append(ProcessedData);

        // Generate debug info if requested
        if (Settings->bOutputDebugInfo)
        {
            UPCGAttributeSet* DebugInfo = CreateDebugInfo(TargetLandscape, Settings, TotalProcessed, PointsSampled);
            if (DebugInfo)
            {
                FPCGTaggedData& DebugTaggedData = OutputData.TaggedData.Emplace_GetRef();
                DebugTaggedData.Data = DebugInfo;
                DebugTaggedData.Pin = TEXT("Debug Info");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Landscape Sampler processed %d points and sampled %d landscape points"), 
                                  TotalProcessed, PointsSampled);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Landscape Sampler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

ALandscape* FAuracronPCGAdvancedLandscapeSamplerElement::FindTargetLandscape(const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings) const
{
    // Try to get specified landscape first
    if (Settings->TargetLandscape.IsValid())
    {
        ALandscape* SpecifiedLandscape = Settings->TargetLandscape.LoadSynchronous();
        if (SpecifiedLandscape)
        {
            return SpecifiedLandscape;
        }
    }

    // Auto-detect landscape if enabled
    if (Settings->bAutoDetectLandscape)
    {
        UWorld* World = GetWorld();
        if (World)
        {
            return UAuracronPCGLandscapeIntegrationUtils::FindLandscapeInWorld(World, FVector::ZeroVector);
        }
    }

    return nullptr;
}

void FAuracronPCGAdvancedLandscapeSamplerElement::SampleLandscapeSequential(const TArray<FPCGPoint>& InputPoints, 
                                                                            TArray<FPCGPoint>& OutputPoints,
                                                                            ALandscape* Landscape,
                                                                            const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings,
                                                                            int32& OutPointsSampled) const
{
    for (const FPCGPoint& InputPoint : InputPoints)
    {
        FPCGPoint OutputPoint = InputPoint;
        
        if (SampleLandscapeAtPoint(OutputPoint, Landscape, Settings))
        {
            OutputPoints.Add(OutputPoint);
            OutPointsSampled++;
        }
        else if (!Settings->SamplingDescriptor.bFilterByHeight && !Settings->SamplingDescriptor.bFilterBySlope)
        {
            // Add point even if sampling failed, unless filtering is enabled
            OutputPoints.Add(OutputPoint);
        }
    }
}

void FAuracronPCGAdvancedLandscapeSamplerElement::SampleLandscapeAsync(const TArray<FPCGPoint>& InputPoints, 
                                                                       TArray<FPCGPoint>& OutputPoints,
                                                                       ALandscape* Landscape,
                                                                       const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings,
                                                                       int32& OutPointsSampled) const
{
    // Prepare output array
    OutputPoints.SetNum(InputPoints.Num());
    TArray<bool> ValidPoints;
    ValidPoints.SetNum(InputPoints.Num());

    // Process points in parallel
    ParallelFor(InputPoints.Num(), [&](int32 Index)
    {
        FPCGPoint OutputPoint = InputPoints[Index];
        ValidPoints[Index] = SampleLandscapeAtPoint(OutputPoint, Landscape, Settings);
        OutputPoints[Index] = OutputPoint;
    }, Settings->MaxConcurrentSamples > 1);

    // Filter out invalid points if filtering is enabled
    if (Settings->SamplingDescriptor.bFilterByHeight || Settings->SamplingDescriptor.bFilterBySlope)
    {
        TArray<FPCGPoint> FilteredPoints;
        for (int32 i = 0; i < OutputPoints.Num(); i++)
        {
            if (ValidPoints[i])
            {
                FilteredPoints.Add(OutputPoints[i]);
                OutPointsSampled++;
            }
        }
        OutputPoints = FilteredPoints;
    }
    else
    {
        OutPointsSampled = OutputPoints.Num();
    }
}

bool FAuracronPCGAdvancedLandscapeSamplerElement::SampleLandscapeAtPoint(FPCGPoint& Point, 
                                                                         ALandscape* Landscape,
                                                                         const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings) const
{
    const FVector& WorldLocation = Point.Transform.GetLocation();
    const FAuracronPCGLandscapeSamplingDescriptor& SamplingDesc = Settings->SamplingDescriptor;

    bool bValidSample = true;

    // Sample height
    if (SamplingDesc.bSampleHeight)
    {
        float Height = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation, SamplingDesc.bUseHighQualitySampling);
        
        // Update point position
        FVector NewLocation = WorldLocation;
        NewLocation.Z = Height;
        Point.Transform.SetLocation(NewLocation);

        // Add height attribute
        if (Point.MetadataEntry != PCGInvalidEntryKey)
        {
            // Store height information in metadata
            UPCGMetadata* Metadata = Point.MetadataEntry ? Point.MetadataEntry->GetParent() : nullptr;
            if (Metadata)
            {
                // Add height as float attribute
                const FName HeightAttributeName = TEXT("LandscapeHeight");
                Metadata->CreateFloatAttribute(HeightAttributeName, Height, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
                
                // Add height category for analysis
                const FName HeightCategoryName = TEXT("HeightCategory");
                FString HeightCategory;
                if (Height < SamplingDesc.HeightRange.X + (SamplingDesc.HeightRange.Y - SamplingDesc.HeightRange.X) * 0.33f)
                {
                    HeightCategory = TEXT("Low");
                }
                else if (Height < SamplingDesc.HeightRange.X + (SamplingDesc.HeightRange.Y - SamplingDesc.HeightRange.X) * 0.66f)
                {
                    HeightCategory = TEXT("Medium");
                }
                else
                {
                    HeightCategory = TEXT("High");
                }
                Metadata->CreateStringAttribute(HeightCategoryName, HeightCategory, /*bAllowsInterpolation=*/false, /*bOverrideParent=*/false);
                
                // Add relative height (0-1 normalized within range)
                const FName RelativeHeightName = TEXT("RelativeHeight");
                float RelativeHeight = FMath::GetRangePct(SamplingDesc.HeightRange, Height);
                Metadata->CreateFloatAttribute(RelativeHeightName, RelativeHeight, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
                
                UAuracronPCGLogger::LogInfo(FString::Printf(TEXT("Added height metadata: Height=%.2f, Category=%s, Relative=%.3f"), 
                    Height, *HeightCategory, RelativeHeight));
            }
            else
            {
                UAuracronPCGLogger::LogWarning(TEXT("Failed to get metadata for height attribute storage"));
            }
        }

        // Filter by height if enabled
        if (SamplingDesc.bFilterByHeight)
        {
            if (Height < SamplingDesc.HeightRange.X || Height > SamplingDesc.HeightRange.Y)
            {
                bValidSample = false;
            }
        }
    }

    // Sample normal
    if (SamplingDesc.bSampleNormal && bValidSample)
    {
        FVector Normal = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeNormal(Landscape, WorldLocation);
        
        // Update point rotation to align with normal
        FQuat NormalRotation = FQuat::FindBetweenNormals(FVector::UpVector, Normal);
        Point.Transform.SetRotation(NormalRotation * Point.Transform.GetRotation());

        // Store normal information in metadata
        if (Point.MetadataEntry != PCGInvalidEntryKey)
        {
            UPCGMetadata* Metadata = Point.MetadataEntry ? Point.MetadataEntry->GetParent() : nullptr;
            if (Metadata)
            {
                // Add normal vector as metadata
                const FName NormalAttributeName = TEXT("LandscapeNormal");
                Metadata->CreateVectorAttribute(NormalAttributeName, Normal, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
                
                // Calculate and store slope angle in degrees
                const FName SlopeAngleName = TEXT("SlopeAngle");
                float SlopeAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)));
                Metadata->CreateFloatAttribute(SlopeAngleName, SlopeAngle, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
                
                // Add slope category for analysis
                const FName SlopeCategoryName = TEXT("SlopeCategory");
                FString SlopeCategory;
                if (SlopeAngle < 15.0f)
                {
                    SlopeCategory = TEXT("Flat");
                }
                else if (SlopeAngle < 30.0f)
                {
                    SlopeCategory = TEXT("Gentle");
                }
                else if (SlopeAngle < 45.0f)
                {
                    SlopeCategory = TEXT("Moderate");
                }
                else
                {
                    SlopeCategory = TEXT("Steep");
                }
                Metadata->CreateStringAttribute(SlopeCategoryName, SlopeCategory, /*bAllowsInterpolation=*/false, /*bOverrideParent=*/false);
                
                // Store normal alignment factor (how aligned with up vector)
                const FName AlignmentFactorName = TEXT("NormalAlignment");
                float AlignmentFactor = FVector::DotProduct(Normal, FVector::UpVector);
                Metadata->CreateFloatAttribute(AlignmentFactorName, AlignmentFactor, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
                
                UAuracronPCGLogger::LogInfo(FString::Printf(TEXT("Added normal metadata: Normal=(%.3f,%.3f,%.3f), Slope=%.2f°, Category=%s, Alignment=%.3f"), 
                    Normal.X, Normal.Y, Normal.Z, SlopeAngle, *SlopeCategory, AlignmentFactor));
            }
            else
            {
                UAuracronPCGLogger::LogWarning(TEXT("Failed to get metadata for normal attribute storage"));
            }
        }
    }

    // Sample slope
    if (SamplingDesc.bSampleSlope && bValidSample)
    {
        float Slope = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeSlope(Landscape, WorldLocation);
        
        // Filter by slope if enabled
        if (SamplingDesc.bFilterBySlope)
        {
            if (Slope < SamplingDesc.SlopeRange.X || Slope > SamplingDesc.SlopeRange.Y)
            {
                bValidSample = false;
            }
        }

        // Add slope as metadata attribute
        if (UPCGMetadata* Metadata = Point.GetMutableMetadata())
        {
            FPCGMetadataAttribute<float>* SlopeAttr = Metadata->FindOrCreateAttribute<float>(TEXT("Slope"), 0.0f, /*bAllowsInterpolation=*/true);
            if (SlopeAttr)
            {
                SlopeAttr->SetValue(Point.MetadataEntry, Slope);
            }
        }
    }

    // Sample curvature
    if (SamplingDesc.bSampleCurvature && bValidSample)
    {
        float Curvature = AuracronPCGLandscapeIntegrationUtils::CalculateLandscapeCurvature(Landscape, WorldLocation, SamplingDesc.SamplingRadius);
        
        // Add curvature as metadata attribute
        if (UPCGMetadata* Metadata = Point.GetMutableMetadata())
        {
            FPCGMetadataAttribute<float>* CurvatureAttr = Metadata->FindOrCreateAttribute<float>(TEXT("Curvature"), 0.0f, /*bAllowsInterpolation=*/true);
            if (CurvatureAttr)
            {
                CurvatureAttr->SetValue(Point.MetadataEntry, Curvature);
            }
        }
    }

    // Sample layers
    if (SamplingDesc.bSampleLayers && bValidSample)
    {
        if (SamplingDesc.bSampleAllLayers)
        {
            TMap<FString, float> AllLayers = UAuracronPCGLandscapeIntegrationUtils::SampleAllLandscapeLayers(Landscape, WorldLocation);
            
            // Add all layers as metadata attributes
            if (UPCGMetadata* Metadata = Point.GetMutableMetadata())
            {
                for (const auto& LayerPair : AllLayers)
                {
                    FString AttributeName = FString::Printf(TEXT("Layer_%s"), *LayerPair.Key);
                    FPCGMetadataAttribute<float>* LayerAttr = Metadata->FindOrCreateAttribute<float>(*AttributeName, 0.0f, /*bAllowsInterpolation=*/true);
                    if (LayerAttr)
                    {
                        LayerAttr->SetValue(Point.MetadataEntry, LayerPair.Value);
                    }
                }
            }
        }
        else
        {
            for (const FString& LayerName : SamplingDesc.LayerNames)
            {
                float LayerValue = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeLayer(Landscape, LayerName, WorldLocation);
                
                // Add layer as metadata attribute
                if (UPCGMetadata* Metadata = Point.GetMutableMetadata())
                {
                    FString AttributeName = FString::Printf(TEXT("Layer_%s"), *LayerName);
                    FPCGMetadataAttribute<float>* LayerAttr = Metadata->FindOrCreateAttribute<float>(*AttributeName, 0.0f, /*bAllowsInterpolation=*/true);
                    if (LayerAttr)
                    {
                        LayerAttr->SetValue(Point.MetadataEntry, LayerValue);
                    }
                }
            }
        }
    }

    // Multi-sampling if enabled
    if (Settings->bUseMultiSampling && bValidSample)
    {
        PerformMultiSampling(Point, Landscape, Settings);
    }

    return bValidSample;
}

void FAuracronPCGAdvancedLandscapeSamplerElement::PerformMultiSampling(FPCGPoint& Point, 
                                                                       ALandscape* Landscape,
                                                                       const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings) const
{
    const FVector& CenterLocation = Point.Transform.GetLocation();
    const float Radius = Settings->MultiSampleRadius;
    const int32 SampleCount = Settings->MultiSampleCount;

    TArray<float> Heights;
    TArray<FVector> Normals;
    Heights.Reserve(SampleCount);
    Normals.Reserve(SampleCount);

    // Sample around the center point
    for (int32 i = 0; i < SampleCount; i++)
    {
        float Angle = (2.0f * PI * i) / SampleCount;
        FVector SampleLocation = CenterLocation + FVector(
            FMath::Cos(Angle) * Radius,
            FMath::Sin(Angle) * Radius,
            0.0f
        );

        float Height = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, SampleLocation);
        FVector Normal = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeNormal(Landscape, SampleLocation);

        Heights.Add(Height);
        Normals.Add(Normal);
    }

    // Calculate average values
    float AverageHeight = 0.0f;
    FVector AverageNormal = FVector::ZeroVector;

    for (float Height : Heights)
    {
        AverageHeight += Height;
    }
    AverageHeight /= Heights.Num();

    for (const FVector& Normal : Normals)
    {
        AverageNormal += Normal;
    }
    AverageNormal = AverageNormal.GetSafeNormal();

    // Update point with averaged values
    FVector NewLocation = Point.Transform.GetLocation();
    NewLocation.Z = AverageHeight;
    Point.Transform.SetLocation(NewLocation);

    FQuat NormalRotation = FQuat::FindBetweenNormals(FVector::UpVector, AverageNormal);
    Point.Transform.SetRotation(NormalRotation * Point.Transform.GetRotation());
}

UPCGAttributeSet* FAuracronPCGAdvancedLandscapeSamplerElement::CreateDebugInfo(ALandscape* Landscape,
                                                                               const UAuracronPCGAdvancedLandscapeSamplerSettings* Settings,
                                                                               int32 TotalProcessed,
                                                                               int32 PointsSampled) const
{
    UPCGAttributeSet* DebugInfo = NewObject<UPCGAttributeSet>();
    
    // Add real debug information using UE5.6 PCG attribute system
    DebugInfo->CreateAttribute<FString>(TEXT("LandscapeName"), Landscape ? Landscape->GetName() : TEXT("None"), false);
    DebugInfo->CreateAttribute<int32>(TEXT("TotalProcessed"), TotalProcessed, false);
    DebugInfo->CreateAttribute<int32>(TEXT("PointsSampled"), PointsSampled, false);
    DebugInfo->CreateAttribute<float>(TEXT("SamplingEfficiency"), TotalProcessed > 0 ? (float)PointsSampled / TotalProcessed : 0.0f, false);
    DebugInfo->CreateAttribute<FVector>(TEXT("LandscapeSize"), Landscape ? FVector(Landscape->GetBoundingRect().Width(), Landscape->GetBoundingRect().Height(), 0) : FVector::ZeroVector, false);
    DebugInfo->CreateAttribute<FDateTime>(TEXT("ProcessingTime"), FDateTime::Now(), false);

    return DebugInfo;
}

UWorld* FAuracronPCGAdvancedLandscapeSamplerElement::GetWorld() const
{
    // Get world from context using UE5.6 proper world access
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    return GWorld;
}

// =============================================================================
// LANDSCAPE HEIGHT MODIFIER IMPLEMENTATION
// =============================================================================

UAuracronPCGLandscapeHeightModifierSettings::UAuracronPCGLandscapeHeightModifierSettings()
{
    NodeMetadata.NodeName = TEXT("Landscape Height Modifier");
    NodeMetadata.NodeDescription = TEXT("Modifies landscape height data using various algorithms");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Landscape"));
    NodeMetadata.Tags.Add(TEXT("Height"));
    NodeMetadata.Tags.Add(TEXT("Modifier"));
    NodeMetadata.Tags.Add(TEXT("Terrain"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.2f);
}

void UAuracronPCGLandscapeHeightModifierSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGLandscapeHeightModifierSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& ModifiedPin = OutputPins.Emplace_GetRef();
    ModifiedPin.Label = TEXT("Modified Areas");
    ModifiedPin.AllowedTypes = EPCGDataType::Spatial;
    ModifiedPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGLandscapeHeightModifierElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                  FPCGDataCollection& OutputData,
                                                                                  const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGLandscapeHeightModifierSettings* Settings = GetTypedSettings<UAuracronPCGLandscapeHeightModifierSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Landscape Height Modifier");
            return Result;
        }

        // Find target landscape
        ALandscape* TargetLandscape = FindTargetLandscape(Settings);
        if (!TargetLandscape)
        {
            Result.ErrorMessage = TEXT("No valid landscape found for modification");
            return Result;
        }

        // Validate landscape for modification
        if (!AuracronPCGLandscapeIntegrationUtils::ValidateLandscapeForModification(TargetLandscape))
        {
            Result.ErrorMessage = TEXT("Landscape is not valid for modification");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsModified = 0;
        TArray<FBox> ModifiedAreas;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            TArray<FPCGPoint> OutputPoints;
            OutputPoints.Reserve(InputPoints.Num());

            // Modify landscape height for each point
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                FPCGPoint OutputPoint = InputPoint;

                if (ModifyLandscapeHeightAtPoint(OutputPoint, TargetLandscape, Settings))
                {
                    PointsModified++;

                    // Track modified area
                    FVector Location = OutputPoint.Transform.GetLocation();
                    FBox ModifiedArea(Location - FVector(Settings->BrushRadius), Location + FVector(Settings->BrushRadius));
                    ModifiedAreas.Add(ModifiedArea);
                }

                OutputPoints.Add(OutputPoint);
            }

            // Set output points
            OutputPointData->GetMutablePoints() = OutputPoints;

            // Add to output
            FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Pin = TEXT("Output");
            OutputTaggedData.Tags = TaggedData.Tags;

            TotalProcessed += InputPoints.Num();
        }

        // Optimize landscape after modifications
        if (PointsModified > 0)
        {
            for (const FBox& ModifiedArea : ModifiedAreas)
            {
                AuracronPCGLandscapeIntegrationUtils::OptimizeLandscapeAfterModification(TargetLandscape, ModifiedArea);
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Landscape Height Modifier processed %d points and modified %d landscape points"),
                                  TotalProcessed, PointsModified);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Landscape Height Modifier error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

ALandscape* FAuracronPCGLandscapeHeightModifierElement::FindTargetLandscape(const UAuracronPCGLandscapeHeightModifierSettings* Settings) const
{
    // Try to get specified landscape first
    if (Settings->TargetLandscape.IsValid())
    {
        ALandscape* SpecifiedLandscape = Settings->TargetLandscape.LoadSynchronous();
        if (SpecifiedLandscape)
        {
            return SpecifiedLandscape;
        }
    }

    // Auto-detect landscape if enabled
    if (Settings->bAutoDetectLandscape)
    {
        UWorld* World = GetWorld();
        if (World)
        {
            return UAuracronPCGLandscapeIntegrationUtils::FindLandscapeInWorld(World, FVector::ZeroVector);
        }
    }

    return nullptr;
}

bool FAuracronPCGLandscapeHeightModifierElement::ModifyLandscapeHeightAtPoint(FPCGPoint& Point,
                                                                              ALandscape* Landscape,
                                                                              const UAuracronPCGLandscapeHeightModifierSettings* Settings) const
{
    const FVector& WorldLocation = Point.Transform.GetLocation();

    // Get height value
    float HeightValue = Settings->HeightValue;
    if (Settings->bUseAttributeForHeight)
    {
        // Get height from point attribute with robust metadata handling
        if (Point.MetadataEntry != PCGInvalidEntryKey && Landscape)
        {
            UPCGMetadata* Metadata = nullptr;
            // Try to get metadata from landscape or point data context
            if (UPCGComponent* PCGComponent = Landscape->GetComponentByClass<UPCGComponent>())
            {
                if (UPCGData* PCGData = PCGComponent->GetPCGData())
                {
                    if (UPCGPointData* PointData = Cast<UPCGPointData>(PCGData))
                    {
                        Metadata = PointData->Metadata;
                    }
                }
            }
            
            if (Metadata)
            {
                FName HeightAttributeName = FName(*Settings->HeightAttributeName);
                if (const UPCGMetadataAttributeBase* HeightAttribute = Metadata->GetConstAttribute(HeightAttributeName))
                {
                    if (HeightAttribute->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
                    {
                        const UPCGMetadataAttribute<float>* FloatAttribute = static_cast<const UPCGMetadataAttribute<float>*>(HeightAttribute);
                        HeightValue = FloatAttribute->GetValueFromItemKey(Point.MetadataEntry);
                        
                        UAuracronPCGLogger::LogInfo(TEXT("ModifyLandscapeHeightAtPoint - Retrieved height value %.2f from attribute %s"), 
                            HeightValue, *Settings->HeightAttributeName);
                    }
                    else if (HeightAttribute->GetTypeId() == PCG::Private::MetadataTypes<double>::Id)
                    {
                        const UPCGMetadataAttribute<double>* DoubleAttribute = static_cast<const UPCGMetadataAttribute<double>*>(HeightAttribute);
                        HeightValue = static_cast<float>(DoubleAttribute->GetValueFromItemKey(Point.MetadataEntry));
                        
                        UAuracronPCGLogger::LogInfo(TEXT("ModifyLandscapeHeightAtPoint - Retrieved height value %.2f from double attribute %s"), 
                            HeightValue, *Settings->HeightAttributeName);
                    }
                    else
                    {
                        UAuracronPCGLogger::LogWarning(TEXT("ModifyLandscapeHeightAtPoint - Height attribute %s has unsupported type, using default value %.2f"), 
                            *Settings->HeightAttributeName, Settings->HeightValue);
                        HeightValue = Settings->HeightValue;
                    }
                }
                else
                {
                    UAuracronPCGLogger::LogWarning(TEXT("ModifyLandscapeHeightAtPoint - Height attribute %s not found, using default value %.2f"), 
                        *Settings->HeightAttributeName, Settings->HeightValue);
                    HeightValue = Settings->HeightValue;
                }
            }
            else
            {
                UAuracronPCGLogger::LogWarning(TEXT("ModifyLandscapeHeightAtPoint - No metadata available, using default height value %.2f"), 
                    Settings->HeightValue);
                HeightValue = Settings->HeightValue;
            }
        }
        else
        {
            UAuracronPCGLogger::LogWarning(TEXT("ModifyLandscapeHeightAtPoint - Invalid point metadata entry or landscape, using default height value %.2f"), 
                Settings->HeightValue);
            HeightValue = Settings->HeightValue;
        }
    }

    // Apply height modification based on mode
    bool bSuccess = false;
    switch (Settings->ModificationMode)
    {
        case EAuracronPCGHeightModificationMode::Absolute:
            bSuccess = UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(
                Landscape, WorldLocation, HeightValue, Settings->BrushRadius, Settings->ModificationMode);
            break;

        case EAuracronPCGHeightModificationMode::Additive:
            bSuccess = UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(
                Landscape, WorldLocation, HeightValue, Settings->BrushRadius, Settings->ModificationMode);
            break;

        case EAuracronPCGHeightModificationMode::Noise:
            bSuccess = ApplyNoiseModification(Point, Landscape, Settings);
            break;

        case EAuracronPCGHeightModificationMode::Terrace:
            bSuccess = ApplyTerraceModification(Point, Landscape, Settings);
            break;

        default:
            bSuccess = UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(
                Landscape, WorldLocation, HeightValue, Settings->BrushRadius, Settings->ModificationMode);
            break;
    }

    return bSuccess;
}

bool FAuracronPCGLandscapeHeightModifierElement::ApplyNoiseModification(FPCGPoint& Point,
                                                                        ALandscape* Landscape,
                                                                        const UAuracronPCGLandscapeHeightModifierSettings* Settings) const
{
    const FVector& WorldLocation = Point.Transform.GetLocation();

    // Generate noise value
    float NoiseValue = 0.0f;
    for (int32 Octave = 0; Octave < Settings->NoiseOctaves; Octave++)
    {
        float Frequency = Settings->NoiseScale * FMath::Pow(2.0f, Octave);
        float Amplitude = FMath::Pow(0.5f, Octave);

        NoiseValue += FMath::PerlinNoise2D(FVector2D(WorldLocation.X * Frequency, WorldLocation.Y * Frequency)) * Amplitude;
    }

    float HeightModification = NoiseValue * Settings->NoiseStrength;

    return UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(
        Landscape, WorldLocation, HeightModification, Settings->BrushRadius, EAuracronPCGHeightModificationMode::Additive);
}

bool FAuracronPCGLandscapeHeightModifierElement::ApplyTerraceModification(FPCGPoint& Point,
                                                                          ALandscape* Landscape,
                                                                          const UAuracronPCGLandscapeHeightModifierSettings* Settings) const
{
    const FVector& WorldLocation = Point.Transform.GetLocation();

    // Get current height
    float CurrentHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation);

    // Calculate terrace level
    float TerraceLevel = FMath::Floor(CurrentHeight / Settings->TerraceHeight) * Settings->TerraceHeight;

    // Apply smoothing
    float DistanceToTerrace = FMath::Abs(CurrentHeight - TerraceLevel);
    float SmoothingFactor = FMath::Clamp(DistanceToTerrace / (Settings->TerraceHeight * Settings->TerraceSmoothing), 0.0f, 1.0f);

    float TargetHeight = FMath::Lerp(TerraceLevel, CurrentHeight, SmoothingFactor);

    return UAuracronPCGLandscapeIntegrationUtils::ModifyLandscapeHeight(
        Landscape, WorldLocation, TargetHeight, Settings->BrushRadius, EAuracronPCGHeightModificationMode::Absolute);
}

UWorld* FAuracronPCGLandscapeHeightModifierElement::GetWorld() const
{
    // Get world from context with robust fallback mechanisms
    UWorld* World = nullptr;
    
    // Try to get world from current PCG context if available
    if (const FPCGContext* CurrentContext = FPCGContext::GetCurrentContext())
    {
        if (UPCGComponent* SourceComponent = CurrentContext->SourceComponent.Get())
        {
            World = SourceComponent->GetWorld();
            if (World)
            {
                UAuracronPCGLogger::LogInfo(TEXT("GetWorld - Retrieved world from PCG context: %s"), 
                    *World->GetName());
                return World;
            }
        }
    }
    
    // Try to get world from game instance
    if (UGameInstance* GameInstance = UGameInstance::GetGameInstance(nullptr))
    {
        World = GameInstance->GetWorld();
        if (World)
        {
            UAuracronPCGLogger::LogInfo(TEXT("GetWorld - Retrieved world from game instance: %s"), 
                *World->GetName());
            return World;
        }
    }
    
    // Try to get world from engine
    if (GEngine)
    {
        for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
        {
            if (WorldContext.World() && WorldContext.WorldType == EWorldType::Game)
            {
                World = WorldContext.World();
                UAuracronPCGLogger::LogInfo(TEXT("GetWorld - Retrieved game world from engine: %s"), 
                    *World->GetName());
                return World;
            }
        }
        
        // If no game world, try PIE world
        for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
        {
            if (WorldContext.World() && WorldContext.WorldType == EWorldType::PIE)
            {
                World = WorldContext.World();
                UAuracronPCGLogger::LogInfo(TEXT("GetWorld - Retrieved PIE world from engine: %s"), 
                    *World->GetName());
                return World;
            }
        }
    }
    
    // Fallback to GWorld as last resort
    if (GWorld)
    {
        UAuracronPCGLogger::LogWarning(TEXT("GetWorld - Using GWorld fallback: %s"), 
            *GWorld->GetName());
        return GWorld;
    }
    
    UAuracronPCGLogger::LogError(TEXT("GetWorld - Failed to retrieve world from any source"));
    return nullptr;
}
