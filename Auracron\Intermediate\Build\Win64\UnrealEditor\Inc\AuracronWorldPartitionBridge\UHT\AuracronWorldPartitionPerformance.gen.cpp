// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionPerformance.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionPerformance() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceMetric();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceReport();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTimespan();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPerformanceMonitoringState ***************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState;
static UEnum* EAuracronPerformanceMonitoringState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronPerformanceMonitoringState"));
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceMonitoringState>()
{
	return EAuracronPerformanceMonitoringState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Analyzing.DisplayName", "Analyzing" },
		{ "Analyzing.Name", "EAuracronPerformanceMonitoringState::Analyzing" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring states\n" },
#endif
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EAuracronPerformanceMonitoringState::Disabled" },
		{ "Enabled.DisplayName", "Enabled" },
		{ "Enabled.Name", "EAuracronPerformanceMonitoringState::Enabled" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
		{ "Recording.DisplayName", "Recording" },
		{ "Recording.Name", "EAuracronPerformanceMonitoringState::Recording" },
		{ "Reporting.DisplayName", "Reporting" },
		{ "Reporting.Name", "EAuracronPerformanceMonitoringState::Reporting" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPerformanceMonitoringState::Disabled", (int64)EAuracronPerformanceMonitoringState::Disabled },
		{ "EAuracronPerformanceMonitoringState::Enabled", (int64)EAuracronPerformanceMonitoringState::Enabled },
		{ "EAuracronPerformanceMonitoringState::Recording", (int64)EAuracronPerformanceMonitoringState::Recording },
		{ "EAuracronPerformanceMonitoringState::Analyzing", (int64)EAuracronPerformanceMonitoringState::Analyzing },
		{ "EAuracronPerformanceMonitoringState::Reporting", (int64)EAuracronPerformanceMonitoringState::Reporting },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronPerformanceMonitoringState",
	"EAuracronPerformanceMonitoringState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState.InnerSingleton;
}
// ********** End Enum EAuracronPerformanceMonitoringState *****************************************

// ********** Begin Enum EAuracronPerformanceSeverity **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPerformanceSeverity;
static UEnum* EAuracronPerformanceSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronPerformanceSeverity"));
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceSeverity>()
{
	return EAuracronPerformanceSeverity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance severity levels\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronPerformanceSeverity::Critical" },
		{ "Emergency.DisplayName", "Emergency" },
		{ "Emergency.Name", "EAuracronPerformanceSeverity::Emergency" },
		{ "Info.DisplayName", "Info" },
		{ "Info.Name", "EAuracronPerformanceSeverity::Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance severity levels" },
#endif
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EAuracronPerformanceSeverity::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPerformanceSeverity::Info", (int64)EAuracronPerformanceSeverity::Info },
		{ "EAuracronPerformanceSeverity::Warning", (int64)EAuracronPerformanceSeverity::Warning },
		{ "EAuracronPerformanceSeverity::Critical", (int64)EAuracronPerformanceSeverity::Critical },
		{ "EAuracronPerformanceSeverity::Emergency", (int64)EAuracronPerformanceSeverity::Emergency },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronPerformanceSeverity",
	"EAuracronPerformanceSeverity",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceSeverity.InnerSingleton;
}
// ********** End Enum EAuracronPerformanceSeverity ************************************************

// ********** Begin Enum EAuracronPerformanceMetricType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPerformanceMetricType;
static UEnum* EAuracronPerformanceMetricType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronPerformanceMetricType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceMetricType>()
{
	return EAuracronPerformanceMetricType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metric types\n" },
#endif
		{ "CPU.DisplayName", "CPU" },
		{ "CPU.Name", "EAuracronPerformanceMetricType::CPU" },
		{ "GPU.DisplayName", "GPU" },
		{ "GPU.Name", "EAuracronPerformanceMetricType::GPU" },
		{ "IO.DisplayName", "Input/Output" },
		{ "IO.Name", "EAuracronPerformanceMetricType::IO" },
		{ "Memory.DisplayName", "Memory" },
		{ "Memory.Name", "EAuracronPerformanceMetricType::Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
		{ "Network.DisplayName", "Network" },
		{ "Network.Name", "EAuracronPerformanceMetricType::Network" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronPerformanceMetricType::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metric types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPerformanceMetricType::Memory", (int64)EAuracronPerformanceMetricType::Memory },
		{ "EAuracronPerformanceMetricType::CPU", (int64)EAuracronPerformanceMetricType::CPU },
		{ "EAuracronPerformanceMetricType::GPU", (int64)EAuracronPerformanceMetricType::GPU },
		{ "EAuracronPerformanceMetricType::Streaming", (int64)EAuracronPerformanceMetricType::Streaming },
		{ "EAuracronPerformanceMetricType::Network", (int64)EAuracronPerformanceMetricType::Network },
		{ "EAuracronPerformanceMetricType::IO", (int64)EAuracronPerformanceMetricType::IO },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronPerformanceMetricType",
	"EAuracronPerformanceMetricType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceMetricType.InnerSingleton;
}
// ********** End Enum EAuracronPerformanceMetricType **********************************************

// ********** Begin Enum EAuracronBottleneckType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronBottleneckType;
static UEnum* EAuracronBottleneckType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronBottleneckType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronBottleneckType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronBottleneckType"));
	}
	return Z_Registration_Info_UEnum_EAuracronBottleneckType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronBottleneckType>()
{
	return EAuracronBottleneckType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bottleneck types\n" },
#endif
		{ "CPU.DisplayName", "CPU" },
		{ "CPU.Name", "EAuracronBottleneckType::CPU" },
		{ "GPU.DisplayName", "GPU" },
		{ "GPU.Name", "EAuracronBottleneckType::GPU" },
		{ "IO.DisplayName", "I/O" },
		{ "IO.Name", "EAuracronBottleneckType::IO" },
		{ "Memory.DisplayName", "Memory" },
		{ "Memory.Name", "EAuracronBottleneckType::Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
		{ "Network.DisplayName", "Network" },
		{ "Network.Name", "EAuracronBottleneckType::Network" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronBottleneckType::None" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronBottleneckType::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bottleneck types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronBottleneckType::None", (int64)EAuracronBottleneckType::None },
		{ "EAuracronBottleneckType::Memory", (int64)EAuracronBottleneckType::Memory },
		{ "EAuracronBottleneckType::CPU", (int64)EAuracronBottleneckType::CPU },
		{ "EAuracronBottleneckType::GPU", (int64)EAuracronBottleneckType::GPU },
		{ "EAuracronBottleneckType::IO", (int64)EAuracronBottleneckType::IO },
		{ "EAuracronBottleneckType::Network", (int64)EAuracronBottleneckType::Network },
		{ "EAuracronBottleneckType::Streaming", (int64)EAuracronBottleneckType::Streaming },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronBottleneckType",
	"EAuracronBottleneckType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType()
{
	if (!Z_Registration_Info_UEnum_EAuracronBottleneckType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronBottleneckType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronBottleneckType.InnerSingleton;
}
// ********** End Enum EAuracronBottleneckType *****************************************************

// ********** Begin ScriptStruct FAuracronPerformanceConfiguration *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration;
class UScriptStruct* FAuracronPerformanceConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPerformanceConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Configuration\n * Configuration settings for performance monitoring in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Configuration\nConfiguration settings for performance monitoring in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryTracking_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCPUProfiling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUProfiling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreamingMetrics_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringUpdateInterval_MetaData[] = {
		{ "Category", "Monitoring" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricSamplingRate_MetaData[] = {
		{ "Category", "Monitoring" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMetricHistorySize_MetaData[] = {
		{ "Category", "Monitoring" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryWarningThresholdMB_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryCriticalThresholdMB_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUWarningThresholdPercent_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUCriticalThresholdPercent_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUWarningThresholdPercent_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUCriticalThresholdPercent_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingWarningThresholdMBps_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCriticalThresholdMBps_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutoOptimization_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBottleneckDetection_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOptimizationSuggestions_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceReporting_MetaData[] = {
		{ "Category", "Reporting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogPerformanceMetrics_MetaData[] = {
		{ "Category", "Reporting" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static void NewProp_bEnableMemoryTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryTracking;
	static void NewProp_bEnableCPUProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCPUProfiling;
	static void NewProp_bEnableGPUProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUProfiling;
	static void NewProp_bEnableStreamingMetrics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreamingMetrics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MonitoringUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MetricSamplingRate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMetricHistorySize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryWarningThresholdMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryCriticalThresholdMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUWarningThresholdPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUCriticalThresholdPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUWarningThresholdPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUCriticalThresholdPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingWarningThresholdMBps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingCriticalThresholdMBps;
	static void NewProp_bEnableAutoOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutoOptimization;
	static void NewProp_bEnableBottleneckDetection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBottleneckDetection;
	static void NewProp_bEnableOptimizationSuggestions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOptimizationSuggestions;
	static void NewProp_bEnablePerformanceReporting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceReporting;
	static void NewProp_bLogPerformanceMetrics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogPerformanceMetrics;
	static void NewProp_bEnablePerformanceDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceDebug;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableMemoryTracking_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableMemoryTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableMemoryTracking = { "bEnableMemoryTracking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableMemoryTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryTracking_MetaData), NewProp_bEnableMemoryTracking_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableCPUProfiling_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableCPUProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableCPUProfiling = { "bEnableCPUProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableCPUProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCPUProfiling_MetaData), NewProp_bEnableCPUProfiling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableGPUProfiling_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableGPUProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableGPUProfiling = { "bEnableGPUProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableGPUProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUProfiling_MetaData), NewProp_bEnableGPUProfiling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableStreamingMetrics_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableStreamingMetrics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableStreamingMetrics = { "bEnableStreamingMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableStreamingMetrics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreamingMetrics_MetaData), NewProp_bEnableStreamingMetrics_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MonitoringUpdateInterval = { "MonitoringUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, MonitoringUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringUpdateInterval_MetaData), NewProp_MonitoringUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MetricSamplingRate = { "MetricSamplingRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, MetricSamplingRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricSamplingRate_MetaData), NewProp_MetricSamplingRate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MaxMetricHistorySize = { "MaxMetricHistorySize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, MaxMetricHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMetricHistorySize_MetaData), NewProp_MaxMetricHistorySize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MemoryWarningThresholdMB = { "MemoryWarningThresholdMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, MemoryWarningThresholdMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryWarningThresholdMB_MetaData), NewProp_MemoryWarningThresholdMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MemoryCriticalThresholdMB = { "MemoryCriticalThresholdMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, MemoryCriticalThresholdMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryCriticalThresholdMB_MetaData), NewProp_MemoryCriticalThresholdMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_CPUWarningThresholdPercent = { "CPUWarningThresholdPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, CPUWarningThresholdPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUWarningThresholdPercent_MetaData), NewProp_CPUWarningThresholdPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_CPUCriticalThresholdPercent = { "CPUCriticalThresholdPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, CPUCriticalThresholdPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUCriticalThresholdPercent_MetaData), NewProp_CPUCriticalThresholdPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_GPUWarningThresholdPercent = { "GPUWarningThresholdPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, GPUWarningThresholdPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUWarningThresholdPercent_MetaData), NewProp_GPUWarningThresholdPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_GPUCriticalThresholdPercent = { "GPUCriticalThresholdPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, GPUCriticalThresholdPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUCriticalThresholdPercent_MetaData), NewProp_GPUCriticalThresholdPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_StreamingWarningThresholdMBps = { "StreamingWarningThresholdMBps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, StreamingWarningThresholdMBps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingWarningThresholdMBps_MetaData), NewProp_StreamingWarningThresholdMBps_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_StreamingCriticalThresholdMBps = { "StreamingCriticalThresholdMBps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceConfiguration, StreamingCriticalThresholdMBps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCriticalThresholdMBps_MetaData), NewProp_StreamingCriticalThresholdMBps_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableAutoOptimization_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableAutoOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableAutoOptimization = { "bEnableAutoOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableAutoOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutoOptimization_MetaData), NewProp_bEnableAutoOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableBottleneckDetection_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableBottleneckDetection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableBottleneckDetection = { "bEnableBottleneckDetection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableBottleneckDetection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBottleneckDetection_MetaData), NewProp_bEnableBottleneckDetection_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableOptimizationSuggestions_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnableOptimizationSuggestions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableOptimizationSuggestions = { "bEnableOptimizationSuggestions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableOptimizationSuggestions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOptimizationSuggestions_MetaData), NewProp_bEnableOptimizationSuggestions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceReporting_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnablePerformanceReporting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceReporting = { "bEnablePerformanceReporting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceReporting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceReporting_MetaData), NewProp_bEnablePerformanceReporting_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bLogPerformanceMetrics_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bLogPerformanceMetrics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bLogPerformanceMetrics = { "bLogPerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bLogPerformanceMetrics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogPerformanceMetrics_MetaData), NewProp_bLogPerformanceMetrics_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceDebug_SetBit(void* Obj)
{
	((FAuracronPerformanceConfiguration*)Obj)->bEnablePerformanceDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceDebug = { "bEnablePerformanceDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceConfiguration), &Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceDebug_MetaData), NewProp_bEnablePerformanceDebug_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableMemoryTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableCPUProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableGPUProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableStreamingMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MonitoringUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MetricSamplingRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MaxMetricHistorySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MemoryWarningThresholdMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_MemoryCriticalThresholdMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_CPUWarningThresholdPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_CPUCriticalThresholdPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_GPUWarningThresholdPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_GPUCriticalThresholdPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_StreamingWarningThresholdMBps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_StreamingCriticalThresholdMBps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableAutoOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableBottleneckDetection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnableOptimizationSuggestions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceReporting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bLogPerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewProp_bEnablePerformanceDebug,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceConfiguration",
	Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::PropPointers),
	sizeof(FAuracronPerformanceConfiguration),
	alignof(FAuracronPerformanceConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronPerformanceMetric ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric;
class UScriptStruct* FAuracronPerformanceMetric::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceMetric, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPerformanceMetric"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Metric\n * Individual performance metric data point\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Metric\nIndividual performance metric data point" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricId_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricType_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValue_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageValue_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Unit_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Performance Metric" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceMetric>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricId = { "MetricId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, MetricId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricId_MetaData), NewProp_MetricId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricType_MetaData), NewProp_MetricType_MetaData) }; // 1828647668
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, MinValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValue_MetaData), NewProp_MinValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_AverageValue = { "AverageValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, AverageValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageValue_MetaData), NewProp_AverageValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, Unit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Unit_MetaData), NewProp_Unit_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, Severity), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 1490741379
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceMetric, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_AverageValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewProp_Description,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceMetric",
	Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::PropPointers),
	sizeof(FAuracronPerformanceMetric),
	alignof(FAuracronPerformanceMetric),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceMetric()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceMetric ******************************************

// ********** Begin ScriptStruct FAuracronPerformanceReport ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport;
class UScriptStruct* FAuracronPerformanceReport::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceReport, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPerformanceReport"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance Report\n * Comprehensive performance analysis report\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Report\nComprehensive performance analysis report" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReportId_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringDuration_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryBottleneck_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationSuggestions_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallPerformanceScore_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningCount_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalCount_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Summary_MetaData[] = {
		{ "Category", "Performance Report" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReportId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MonitoringDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Metrics_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Metrics;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PrimaryBottleneck_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PrimaryBottleneck;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OptimizationSuggestions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OptimizationSuggestions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallPerformanceScore;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WarningCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CriticalCount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Summary;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceReport>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_ReportId = { "ReportId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, ReportId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReportId_MetaData), NewProp_ReportId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_MonitoringDuration = { "MonitoringDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, MonitoringDuration), Z_Construct_UScriptStruct_FTimespan, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringDuration_MetaData), NewProp_MonitoringDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Metrics_Inner = { "Metrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, Metrics), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) }; // 913329360
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_PrimaryBottleneck_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_PrimaryBottleneck = { "PrimaryBottleneck", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, PrimaryBottleneck), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryBottleneck_MetaData), NewProp_PrimaryBottleneck_MetaData) }; // 3403593494
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OptimizationSuggestions_Inner = { "OptimizationSuggestions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OptimizationSuggestions = { "OptimizationSuggestions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, OptimizationSuggestions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationSuggestions_MetaData), NewProp_OptimizationSuggestions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OverallPerformanceScore = { "OverallPerformanceScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, OverallPerformanceScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallPerformanceScore_MetaData), NewProp_OverallPerformanceScore_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_WarningCount = { "WarningCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, WarningCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningCount_MetaData), NewProp_WarningCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_CriticalCount = { "CriticalCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, CriticalCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalCount_MetaData), NewProp_CriticalCount_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Summary = { "Summary", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceReport, Summary), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Summary_MetaData), NewProp_Summary_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_ReportId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_GenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_MonitoringDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Metrics_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Metrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_PrimaryBottleneck_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_PrimaryBottleneck,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OptimizationSuggestions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OptimizationSuggestions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_OverallPerformanceScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_WarningCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_CriticalCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewProp_Summary,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceReport",
	Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::PropPointers),
	sizeof(FAuracronPerformanceReport),
	alignof(FAuracronPerformanceReport),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceReport()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceReport ******************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms
	{
		FAuracronPerformanceMetric Metric;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Metric;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_Metric = { "Metric", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms, Metric), Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_Metric,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "OnPerformanceAlert__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPerformanceManager::FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FAuracronPerformanceMetric Metric)
{
	struct AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms
	{
		FAuracronPerformanceMetric Metric;
	};
	AuracronWorldPartitionPerformanceManager_eventOnPerformanceAlert_Parms Parms;
	Parms.Metric=Metric;
	OnPerformanceAlert.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnBottleneckDetected *************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms
	{
		EAuracronBottleneckType BottleneckType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms, BottleneckType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(0, nullptr) }; // 3403593494
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::NewProp_BottleneckType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "OnBottleneckDetected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPerformanceManager::FOnBottleneckDetected_DelegateWrapper(const FMulticastScriptDelegate& OnBottleneckDetected, EAuracronBottleneckType BottleneckType)
{
	struct AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms
	{
		EAuracronBottleneckType BottleneckType;
	};
	AuracronWorldPartitionPerformanceManager_eventOnBottleneckDetected_Parms Parms;
	Parms.BottleneckType=BottleneckType;
	OnBottleneckDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBottleneckDetected ***************************************************

// ********** Begin Delegate FOnPerformanceReportGenerated *****************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms
	{
		FAuracronPerformanceReport Report;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Report;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::NewProp_Report = { "Report", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms, Report), Z_Construct_UScriptStruct_FAuracronPerformanceReport, METADATA_PARAMS(0, nullptr) }; // 2122339513
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::NewProp_Report,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "OnPerformanceReportGenerated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPerformanceManager::FOnPerformanceReportGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceReportGenerated, FAuracronPerformanceReport Report)
{
	struct AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms
	{
		FAuracronPerformanceReport Report;
	};
	AuracronWorldPartitionPerformanceManager_eventOnPerformanceReportGenerated_Parms Parms;
	Parms.Report=Report;
	OnPerformanceReportGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceReportGenerated *******************************************

// ********** Begin Delegate FOnThresholdExceeded **************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(0, nullptr) }; // 1828647668
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "OnThresholdExceeded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPerformanceManager::FOnThresholdExceeded_DelegateWrapper(const FMulticastScriptDelegate& OnThresholdExceeded, EAuracronPerformanceMetricType MetricType, float Value)
{
	struct AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		float Value;
	};
	AuracronWorldPartitionPerformanceManager_eventOnThresholdExceeded_Parms Parms;
	Parms.MetricType=MetricType;
	Parms.Value=Value;
	OnThresholdExceeded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnThresholdExceeded ****************************************************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function ApplyAutoOptimization *
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventApplyAutoOptimization_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPerformanceManager_eventApplyAutoOptimization_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPerformanceManager_eventApplyAutoOptimization_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "ApplyAutoOptimization", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::AuracronWorldPartitionPerformanceManager_eventApplyAutoOptimization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::AuracronWorldPartitionPerformanceManager_eventApplyAutoOptimization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execApplyAutoOptimization)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyAutoOptimization();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function ApplyAutoOptimization ***

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function CalculatePerformanceScore 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventCalculatePerformanceScore_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance scoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance scoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventCalculatePerformanceScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "CalculatePerformanceScore", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::AuracronWorldPartitionPerformanceManager_eventCalculatePerformanceScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::AuracronWorldPartitionPerformanceManager_eventCalculatePerformanceScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execCalculatePerformanceScore)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculatePerformanceScore();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function CalculatePerformanceScore 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function CollectMetrics ********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Metric collection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Metric collection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "CollectMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execCollectMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CollectMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function CollectMetrics **********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function DetectBottleneck ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventDetectBottleneck_Parms
	{
		EAuracronBottleneckType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bottleneck detection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bottleneck detection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventDetectBottleneck_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(0, nullptr) }; // 3403593494
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "DetectBottleneck", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::AuracronWorldPartitionPerformanceManager_eventDetectBottleneck_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::AuracronWorldPartitionPerformanceManager_eventDetectBottleneck_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execDetectBottleneck)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronBottleneckType*)Z_Param__Result=P_THIS->DetectBottleneck();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function DetectBottleneck ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function DrawDebugPerformanceInfo 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventDrawDebugPerformanceInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventDrawDebugPerformanceInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "DrawDebugPerformanceInfo", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::AuracronWorldPartitionPerformanceManager_eventDrawDebugPerformanceInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::AuracronWorldPartitionPerformanceManager_eventDrawDebugPerformanceInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execDrawDebugPerformanceInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugPerformanceInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function DrawDebugPerformanceInfo 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function EnablePerformanceDebug 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventEnablePerformanceDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionPerformanceManager_eventEnablePerformanceDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPerformanceManager_eventEnablePerformanceDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "EnablePerformanceDebug", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::AuracronWorldPartitionPerformanceManager_eventEnablePerformanceDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::AuracronWorldPartitionPerformanceManager_eventEnablePerformanceDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execEnablePerformanceDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePerformanceDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function EnablePerformanceDebug **

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GeneratePerformanceReport 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGeneratePerformanceReport_Parms
	{
		FAuracronPerformanceReport ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reporting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGeneratePerformanceReport_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceReport, METADATA_PARAMS(0, nullptr) }; // 2122339513
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GeneratePerformanceReport", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventGeneratePerformanceReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventGeneratePerformanceReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGeneratePerformanceReport)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceReport*)Z_Param__Result=P_THIS->GeneratePerformanceReport();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GeneratePerformanceReport 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetActiveAlerts *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetActiveAlerts_Parms
	{
		TArray<FAuracronPerformanceMetric> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetActiveAlerts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetActiveAlerts", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::AuracronWorldPartitionPerformanceManager_eventGetActiveAlerts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::AuracronWorldPartitionPerformanceManager_eventGetActiveAlerts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetActiveAlerts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceMetric>*)Z_Param__Result=P_THIS->GetActiveAlerts();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetActiveAlerts *********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetActiveStreamingOperations 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetActiveStreamingOperations_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetActiveStreamingOperations_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetActiveStreamingOperations", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::AuracronWorldPartitionPerformanceManager_eventGetActiveStreamingOperations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::AuracronWorldPartitionPerformanceManager_eventGetActiveStreamingOperations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetActiveStreamingOperations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveStreamingOperations();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetActiveStreamingOperations 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetAllCurrentMetrics **
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetAllCurrentMetrics_Parms
	{
		TArray<FAuracronPerformanceMetric> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetAllCurrentMetrics_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetAllCurrentMetrics", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::AuracronWorldPartitionPerformanceManager_eventGetAllCurrentMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::AuracronWorldPartitionPerformanceManager_eventGetAllCurrentMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetAllCurrentMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceMetric>*)Z_Param__Result=P_THIS->GetAllCurrentMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetAllCurrentMetrics ****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetAvailableMemoryMB **
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetAvailableMemoryMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetAvailableMemoryMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetAvailableMemoryMB", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetAvailableMemoryMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetAvailableMemoryMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetAvailableMemoryMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAvailableMemoryMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetAvailableMemoryMB ****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetAverageCPUUsagePercent 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetAverageCPUUsagePercent_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetAverageCPUUsagePercent_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetAverageCPUUsagePercent", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetAverageCPUUsagePercent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetAverageCPUUsagePercent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetAverageCPUUsagePercent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageCPUUsagePercent();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetAverageCPUUsagePercent 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetBottleneckDetails **
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetBottleneckDetails_Parms
	{
		EAuracronBottleneckType BottleneckType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetBottleneckDetails_Parms, BottleneckType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(0, nullptr) }; // 3403593494
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetBottleneckDetails_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_BottleneckType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetBottleneckDetails", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::AuracronWorldPartitionPerformanceManager_eventGetBottleneckDetails_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::AuracronWorldPartitionPerformanceManager_eventGetBottleneckDetails_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetBottleneckDetails)
{
	P_GET_ENUM(EAuracronBottleneckType,Z_Param_BottleneckType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetBottleneckDetails(EAuracronBottleneckType(Z_Param_BottleneckType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetBottleneckDetails ****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetBottleneckSeverity *
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetBottleneckSeverity_Parms
	{
		EAuracronBottleneckType BottleneckType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetBottleneckSeverity_Parms, BottleneckType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(0, nullptr) }; // 3403593494
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetBottleneckSeverity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_BottleneckType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetBottleneckSeverity", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::AuracronWorldPartitionPerformanceManager_eventGetBottleneckSeverity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::AuracronWorldPartitionPerformanceManager_eventGetBottleneckSeverity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetBottleneckSeverity)
{
	P_GET_ENUM(EAuracronBottleneckType,Z_Param_BottleneckType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBottleneckSeverity(EAuracronBottleneckType(Z_Param_BottleneckType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetBottleneckSeverity ***

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetConfiguration ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetConfiguration_Parms
	{
		FAuracronPerformanceConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration, METADATA_PARAMS(0, nullptr) }; // 284650170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::AuracronWorldPartitionPerformanceManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::AuracronWorldPartitionPerformanceManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetConfiguration ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetCPUBreakdown *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetCPUBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCPUBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetCPUBreakdown", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetCPUBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetCPUBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetCPUBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetCPUBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetCPUBreakdown *********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetCurrentCPUUsagePercent 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetCurrentCPUUsagePercent_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CPU monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CPU monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCurrentCPUUsagePercent_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetCurrentCPUUsagePercent", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentCPUUsagePercent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentCPUUsagePercent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetCurrentCPUUsagePercent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentCPUUsagePercent();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetCurrentCPUUsagePercent 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetCurrentGPUUsagePercent 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetCurrentGPUUsagePercent_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCurrentGPUUsagePercent_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetCurrentGPUUsagePercent", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentGPUUsagePercent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentGPUUsagePercent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetCurrentGPUUsagePercent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentGPUUsagePercent();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetCurrentGPUUsagePercent 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetCurrentMemoryUsageMB 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetCurrentMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCurrentMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetCurrentMemoryUsageMB", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetCurrentMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetCurrentMemoryUsageMB *

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetCurrentMetric ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetCurrentMetric_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		FAuracronPerformanceMetric ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCurrentMetric_Parms, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(0, nullptr) }; // 1828647668
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetCurrentMetric_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetCurrentMetric", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::AuracronWorldPartitionPerformanceManager_eventGetCurrentMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetCurrentMetric)
{
	P_GET_ENUM(EAuracronPerformanceMetricType,Z_Param_MetricType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceMetric*)Z_Param__Result=P_THIS->GetCurrentMetric(EAuracronPerformanceMetricType(Z_Param_MetricType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetCurrentMetric ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetGPUBreakdown *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetGPUBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetGPUBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetGPUBreakdown", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetGPUBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetGPUBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetGPUBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetGPUBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetGPUBreakdown *********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetGPUMemoryUsageMB ***
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetGPUMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetGPUMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetGPUMemoryUsageMB", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetGPUMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetGPUMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetGPUMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetGPUMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetGPUMemoryUsageMB *****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetInstance ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionPerformanceManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::AuracronWorldPartitionPerformanceManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::AuracronWorldPartitionPerformanceManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionPerformanceManager**)Z_Param__Result=UAuracronWorldPartitionPerformanceManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetInstance *************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetMemoryBreakdown ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetMemoryBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetMemoryBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetMemoryBreakdown", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetMemoryBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetMemoryBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetMemoryBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetMemoryBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetMemoryBreakdown ******

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetMetricHistory ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		int32 MaxSamples;
		TArray<FAuracronPerformanceMetric> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "CPP_Default_MaxSamples", "100" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSamples;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(0, nullptr) }; // 1828647668
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MaxSamples = { "MaxSamples", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms, MaxSamples), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 913329360
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_MaxSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetMetricHistory", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::AuracronWorldPartitionPerformanceManager_eventGetMetricHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetMetricHistory)
{
	P_GET_ENUM(EAuracronPerformanceMetricType,Z_Param_MetricType);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxSamples);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceMetric>*)Z_Param__Result=P_THIS->GetMetricHistory(EAuracronPerformanceMetricType(Z_Param_MetricType),Z_Param_MaxSamples);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetMetricHistory ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetMonitoringState ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetMonitoringState_Parms
	{
		EAuracronPerformanceMonitoringState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetMonitoringState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState, METADATA_PARAMS(0, nullptr) }; // 3945916028
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetMonitoringState", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::AuracronWorldPartitionPerformanceManager_eventGetMonitoringState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::AuracronWorldPartitionPerformanceManager_eventGetMonitoringState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetMonitoringState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPerformanceMonitoringState*)Z_Param__Result=P_THIS->GetMonitoringState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetMonitoringState ******

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetOptimizationSuggestions 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestions_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization suggestions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization suggestions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetOptimizationSuggestions", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetOptimizationSuggestions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetOptimizationSuggestions();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetOptimizationSuggestions 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetOptimizationSuggestionsForBottleneck 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestionsForBottleneck_Parms
	{
		EAuracronBottleneckType BottleneckType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestionsForBottleneck_Parms, BottleneckType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronBottleneckType, METADATA_PARAMS(0, nullptr) }; // 3403593494
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestionsForBottleneck_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_BottleneckType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetOptimizationSuggestionsForBottleneck", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestionsForBottleneck_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::AuracronWorldPartitionPerformanceManager_eventGetOptimizationSuggestionsForBottleneck_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetOptimizationSuggestionsForBottleneck)
{
	P_GET_ENUM(EAuracronBottleneckType,Z_Param_BottleneckType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetOptimizationSuggestionsForBottleneck(EAuracronBottleneckType(Z_Param_BottleneckType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetOptimizationSuggestionsForBottleneck 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetPeakMemoryUsageMB **
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetPeakMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetPeakMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetPeakMemoryUsageMB", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetPeakMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::AuracronWorldPartitionPerformanceManager_eventGetPeakMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetPeakMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPeakMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetPeakMemoryUsageMB ****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetPerformanceScoreBreakdown 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetPerformanceScoreBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetPerformanceScoreBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetPerformanceScoreBreakdown", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetPerformanceScoreBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetPerformanceScoreBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetPerformanceScoreBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetPerformanceScoreBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetPerformanceScoreBreakdown 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetStreamingBandwidthMBps 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetStreamingBandwidthMBps_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetStreamingBandwidthMBps_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetStreamingBandwidthMBps", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::AuracronWorldPartitionPerformanceManager_eventGetStreamingBandwidthMBps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::AuracronWorldPartitionPerformanceManager_eventGetStreamingBandwidthMBps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetStreamingBandwidthMBps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStreamingBandwidthMBps();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetStreamingBandwidthMBps 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetStreamingBreakdown *
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetStreamingBreakdown_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetStreamingBreakdown_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetStreamingBreakdown", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetStreamingBreakdown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::AuracronWorldPartitionPerformanceManager_eventGetStreamingBreakdown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetStreamingBreakdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetStreamingBreakdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetStreamingBreakdown ***

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function GetThreshold **********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		EAuracronPerformanceSeverity Severity;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(0, nullptr) }; // 1828647668
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms, Severity), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity, METADATA_PARAMS(0, nullptr) }; // 1490741379
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "GetThreshold", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::AuracronWorldPartitionPerformanceManager_eventGetThreshold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execGetThreshold)
{
	P_GET_ENUM(EAuracronPerformanceMetricType,Z_Param_MetricType);
	P_GET_ENUM(EAuracronPerformanceSeverity,Z_Param_Severity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetThreshold(EAuracronPerformanceMetricType(Z_Param_MetricType),EAuracronPerformanceSeverity(Z_Param_Severity));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function GetThreshold ************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function Initialize ************
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventInitialize_Parms
	{
		FAuracronPerformanceConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 284650170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::AuracronWorldPartitionPerformanceManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::AuracronWorldPartitionPerformanceManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronPerformanceConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function Initialize **************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function IsInitialized *********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPerformanceManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPerformanceManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::AuracronWorldPartitionPerformanceManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::AuracronWorldPartitionPerformanceManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function IsInitialized ***********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function IsPerformanceDebugEnabled 
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventIsPerformanceDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPerformanceManager_eventIsPerformanceDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPerformanceManager_eventIsPerformanceDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "IsPerformanceDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::AuracronWorldPartitionPerformanceManager_eventIsPerformanceDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::AuracronWorldPartitionPerformanceManager_eventIsPerformanceDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execIsPerformanceDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPerformanceDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function IsPerformanceDebugEnabled 

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function LoadPerformanceReport *
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventLoadPerformanceReport_Parms
	{
		FString FilePath;
		FAuracronPerformanceReport ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventLoadPerformanceReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventLoadPerformanceReport_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPerformanceReport, METADATA_PARAMS(0, nullptr) }; // 2122339513
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "LoadPerformanceReport", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventLoadPerformanceReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventLoadPerformanceReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execLoadPerformanceReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPerformanceReport*)Z_Param__Result=P_THIS->LoadPerformanceReport(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function LoadPerformanceReport ***

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function LogPerformanceState ***
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "LogPerformanceState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execLogPerformanceState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogPerformanceState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function LogPerformanceState *****

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function PauseMonitoring *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "PauseMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execPauseMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function PauseMonitoring *********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function ResetMetrics **********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "ResetMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execResetMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function ResetMetrics ************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function ResumeMonitoring ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "ResumeMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execResumeMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function ResumeMonitoring ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function SavePerformanceReport *
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms
	{
		FAuracronPerformanceReport Report;
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Report_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Report;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_Report = { "Report", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms, Report), Z_Construct_UScriptStruct_FAuracronPerformanceReport, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Report_MetaData), NewProp_Report_MetaData) }; // 2122339513
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_Report,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "SavePerformanceReport", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::AuracronWorldPartitionPerformanceManager_eventSavePerformanceReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execSavePerformanceReport)
{
	P_GET_STRUCT_REF(FAuracronPerformanceReport,Z_Param_Out_Report);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SavePerformanceReport(Z_Param_Out_Report,Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function SavePerformanceReport ***

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function SetConfiguration ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventSetConfiguration_Parms
	{
		FAuracronPerformanceConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 284650170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::AuracronWorldPartitionPerformanceManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::AuracronWorldPartitionPerformanceManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronPerformanceConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function SetConfiguration ********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function SetThreshold **********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms
	{
		EAuracronPerformanceMetricType MetricType;
		EAuracronPerformanceSeverity Severity;
		float Threshold;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Thresholds and alerts\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Thresholds and alerts" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MetricType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MetricType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_MetricType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_MetricType = { "MetricType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms, MetricType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMetricType, METADATA_PARAMS(0, nullptr) }; // 1828647668
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms, Severity), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceSeverity, METADATA_PARAMS(0, nullptr) }; // 1490741379
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_MetricType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_MetricType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::NewProp_Threshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "SetThreshold", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::AuracronWorldPartitionPerformanceManager_eventSetThreshold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execSetThreshold)
{
	P_GET_ENUM(EAuracronPerformanceMetricType,Z_Param_MetricType);
	P_GET_ENUM(EAuracronPerformanceSeverity,Z_Param_Severity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetThreshold(EAuracronPerformanceMetricType(Z_Param_MetricType),EAuracronPerformanceSeverity(Z_Param_Severity),Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function SetThreshold ************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function Shutdown **************
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function Shutdown ****************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function StartMonitoring *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Monitoring control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitoring control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "StartMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execStartMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function StartMonitoring *********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function StopMonitoring ********
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "StopMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execStopMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function StopMonitoring **********

// ********** Begin Class UAuracronWorldPartitionPerformanceManager Function Tick ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics
{
	struct AuracronWorldPartitionPerformanceManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPerformanceManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::AuracronWorldPartitionPerformanceManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::AuracronWorldPartitionPerformanceManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPerformanceManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPerformanceManager Function Tick ********************

// ********** Begin Class UAuracronWorldPartitionPerformanceManager ********************************
void UAuracronWorldPartitionPerformanceManager::StaticRegisterNativesUAuracronWorldPartitionPerformanceManager()
{
	UClass* Class = UAuracronWorldPartitionPerformanceManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAutoOptimization", &UAuracronWorldPartitionPerformanceManager::execApplyAutoOptimization },
		{ "CalculatePerformanceScore", &UAuracronWorldPartitionPerformanceManager::execCalculatePerformanceScore },
		{ "CollectMetrics", &UAuracronWorldPartitionPerformanceManager::execCollectMetrics },
		{ "DetectBottleneck", &UAuracronWorldPartitionPerformanceManager::execDetectBottleneck },
		{ "DrawDebugPerformanceInfo", &UAuracronWorldPartitionPerformanceManager::execDrawDebugPerformanceInfo },
		{ "EnablePerformanceDebug", &UAuracronWorldPartitionPerformanceManager::execEnablePerformanceDebug },
		{ "GeneratePerformanceReport", &UAuracronWorldPartitionPerformanceManager::execGeneratePerformanceReport },
		{ "GetActiveAlerts", &UAuracronWorldPartitionPerformanceManager::execGetActiveAlerts },
		{ "GetActiveStreamingOperations", &UAuracronWorldPartitionPerformanceManager::execGetActiveStreamingOperations },
		{ "GetAllCurrentMetrics", &UAuracronWorldPartitionPerformanceManager::execGetAllCurrentMetrics },
		{ "GetAvailableMemoryMB", &UAuracronWorldPartitionPerformanceManager::execGetAvailableMemoryMB },
		{ "GetAverageCPUUsagePercent", &UAuracronWorldPartitionPerformanceManager::execGetAverageCPUUsagePercent },
		{ "GetBottleneckDetails", &UAuracronWorldPartitionPerformanceManager::execGetBottleneckDetails },
		{ "GetBottleneckSeverity", &UAuracronWorldPartitionPerformanceManager::execGetBottleneckSeverity },
		{ "GetConfiguration", &UAuracronWorldPartitionPerformanceManager::execGetConfiguration },
		{ "GetCPUBreakdown", &UAuracronWorldPartitionPerformanceManager::execGetCPUBreakdown },
		{ "GetCurrentCPUUsagePercent", &UAuracronWorldPartitionPerformanceManager::execGetCurrentCPUUsagePercent },
		{ "GetCurrentGPUUsagePercent", &UAuracronWorldPartitionPerformanceManager::execGetCurrentGPUUsagePercent },
		{ "GetCurrentMemoryUsageMB", &UAuracronWorldPartitionPerformanceManager::execGetCurrentMemoryUsageMB },
		{ "GetCurrentMetric", &UAuracronWorldPartitionPerformanceManager::execGetCurrentMetric },
		{ "GetGPUBreakdown", &UAuracronWorldPartitionPerformanceManager::execGetGPUBreakdown },
		{ "GetGPUMemoryUsageMB", &UAuracronWorldPartitionPerformanceManager::execGetGPUMemoryUsageMB },
		{ "GetInstance", &UAuracronWorldPartitionPerformanceManager::execGetInstance },
		{ "GetMemoryBreakdown", &UAuracronWorldPartitionPerformanceManager::execGetMemoryBreakdown },
		{ "GetMetricHistory", &UAuracronWorldPartitionPerformanceManager::execGetMetricHistory },
		{ "GetMonitoringState", &UAuracronWorldPartitionPerformanceManager::execGetMonitoringState },
		{ "GetOptimizationSuggestions", &UAuracronWorldPartitionPerformanceManager::execGetOptimizationSuggestions },
		{ "GetOptimizationSuggestionsForBottleneck", &UAuracronWorldPartitionPerformanceManager::execGetOptimizationSuggestionsForBottleneck },
		{ "GetPeakMemoryUsageMB", &UAuracronWorldPartitionPerformanceManager::execGetPeakMemoryUsageMB },
		{ "GetPerformanceScoreBreakdown", &UAuracronWorldPartitionPerformanceManager::execGetPerformanceScoreBreakdown },
		{ "GetStreamingBandwidthMBps", &UAuracronWorldPartitionPerformanceManager::execGetStreamingBandwidthMBps },
		{ "GetStreamingBreakdown", &UAuracronWorldPartitionPerformanceManager::execGetStreamingBreakdown },
		{ "GetThreshold", &UAuracronWorldPartitionPerformanceManager::execGetThreshold },
		{ "Initialize", &UAuracronWorldPartitionPerformanceManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionPerformanceManager::execIsInitialized },
		{ "IsPerformanceDebugEnabled", &UAuracronWorldPartitionPerformanceManager::execIsPerformanceDebugEnabled },
		{ "LoadPerformanceReport", &UAuracronWorldPartitionPerformanceManager::execLoadPerformanceReport },
		{ "LogPerformanceState", &UAuracronWorldPartitionPerformanceManager::execLogPerformanceState },
		{ "PauseMonitoring", &UAuracronWorldPartitionPerformanceManager::execPauseMonitoring },
		{ "ResetMetrics", &UAuracronWorldPartitionPerformanceManager::execResetMetrics },
		{ "ResumeMonitoring", &UAuracronWorldPartitionPerformanceManager::execResumeMonitoring },
		{ "SavePerformanceReport", &UAuracronWorldPartitionPerformanceManager::execSavePerformanceReport },
		{ "SetConfiguration", &UAuracronWorldPartitionPerformanceManager::execSetConfiguration },
		{ "SetThreshold", &UAuracronWorldPartitionPerformanceManager::execSetThreshold },
		{ "Shutdown", &UAuracronWorldPartitionPerformanceManager::execShutdown },
		{ "StartMonitoring", &UAuracronWorldPartitionPerformanceManager::execStartMonitoring },
		{ "StopMonitoring", &UAuracronWorldPartitionPerformanceManager::execStopMonitoring },
		{ "Tick", &UAuracronWorldPartitionPerformanceManager::execTick },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager;
UClass* UAuracronWorldPartitionPerformanceManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionPerformanceManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionPerformanceManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionPerformanceManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_NoRegister()
{
	return UAuracronWorldPartitionPerformanceManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Performance Manager\n * Central manager for performance monitoring in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionPerformance.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Performance Manager\nCentral manager for performance monitoring in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceAlert_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBottleneckDetected_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceReportGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnThresholdExceeded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringState_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPerformance.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceAlert;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBottleneckDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceReportGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnThresholdExceeded;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MonitoringState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MonitoringState;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ApplyAutoOptimization, "ApplyAutoOptimization" }, // 2301814804
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CalculatePerformanceScore, "CalculatePerformanceScore" }, // 1253985284
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_CollectMetrics, "CollectMetrics" }, // 25983744
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DetectBottleneck, "DetectBottleneck" }, // 1479396505
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_DrawDebugPerformanceInfo, "DrawDebugPerformanceInfo" }, // 152937061
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_EnablePerformanceDebug, "EnablePerformanceDebug" }, // 2289163755
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GeneratePerformanceReport, "GeneratePerformanceReport" }, // 2508999265
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveAlerts, "GetActiveAlerts" }, // 3809196584
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetActiveStreamingOperations, "GetActiveStreamingOperations" }, // 3230577159
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAllCurrentMetrics, "GetAllCurrentMetrics" }, // 306366752
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAvailableMemoryMB, "GetAvailableMemoryMB" }, // 1604683918
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetAverageCPUUsagePercent, "GetAverageCPUUsagePercent" }, // 1897783010
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckDetails, "GetBottleneckDetails" }, // 1628226854
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetBottleneckSeverity, "GetBottleneckSeverity" }, // 798481263
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetConfiguration, "GetConfiguration" }, // 4087459324
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCPUBreakdown, "GetCPUBreakdown" }, // 3745821811
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentCPUUsagePercent, "GetCurrentCPUUsagePercent" }, // 1371229214
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentGPUUsagePercent, "GetCurrentGPUUsagePercent" }, // 3397064015
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMemoryUsageMB, "GetCurrentMemoryUsageMB" }, // 3463171914
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetCurrentMetric, "GetCurrentMetric" }, // 413889737
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUBreakdown, "GetGPUBreakdown" }, // 325585578
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetGPUMemoryUsageMB, "GetGPUMemoryUsageMB" }, // 2430140035
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetInstance, "GetInstance" }, // 3644383620
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMemoryBreakdown, "GetMemoryBreakdown" }, // 3084427935
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMetricHistory, "GetMetricHistory" }, // 3695761751
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetMonitoringState, "GetMonitoringState" }, // 2757841816
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestions, "GetOptimizationSuggestions" }, // 2118943055
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetOptimizationSuggestionsForBottleneck, "GetOptimizationSuggestionsForBottleneck" }, // 1733239058
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPeakMemoryUsageMB, "GetPeakMemoryUsageMB" }, // 4198710228
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetPerformanceScoreBreakdown, "GetPerformanceScoreBreakdown" }, // 4077526581
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBandwidthMBps, "GetStreamingBandwidthMBps" }, // 3545794750
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetStreamingBreakdown, "GetStreamingBreakdown" }, // 444195802
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_GetThreshold, "GetThreshold" }, // 1002056446
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Initialize, "Initialize" }, // 2876272795
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsInitialized, "IsInitialized" }, // 2851921717
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_IsPerformanceDebugEnabled, "IsPerformanceDebugEnabled" }, // 1028085232
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LoadPerformanceReport, "LoadPerformanceReport" }, // 2176752132
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_LogPerformanceState, "LogPerformanceState" }, // 3925590307
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature, "OnBottleneckDetected__DelegateSignature" }, // 1754544322
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature, "OnPerformanceAlert__DelegateSignature" }, // 3282384526
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature, "OnPerformanceReportGenerated__DelegateSignature" }, // 722955955
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature, "OnThresholdExceeded__DelegateSignature" }, // 1016478358
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_PauseMonitoring, "PauseMonitoring" }, // 2212001384
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResetMetrics, "ResetMetrics" }, // 2791720371
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_ResumeMonitoring, "ResumeMonitoring" }, // 4233342005
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SavePerformanceReport, "SavePerformanceReport" }, // 2090477091
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetConfiguration, "SetConfiguration" }, // 766020989
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_SetThreshold, "SetThreshold" }, // 1379807221
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Shutdown, "Shutdown" }, // 1549919888
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StartMonitoring, "StartMonitoring" }, // 291670868
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_StopMonitoring, "StopMonitoring" }, // 3671331230
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPerformanceManager_Tick, "Tick" }, // 109430509
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionPerformanceManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnPerformanceAlert = { "OnPerformanceAlert", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, OnPerformanceAlert), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceAlert__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceAlert_MetaData), NewProp_OnPerformanceAlert_MetaData) }; // 3282384526
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnBottleneckDetected = { "OnBottleneckDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, OnBottleneckDetected), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnBottleneckDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBottleneckDetected_MetaData), NewProp_OnBottleneckDetected_MetaData) }; // 1754544322
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnPerformanceReportGenerated = { "OnPerformanceReportGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, OnPerformanceReportGenerated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnPerformanceReportGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceReportGenerated_MetaData), NewProp_OnPerformanceReportGenerated_MetaData) }; // 722955955
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnThresholdExceeded = { "OnThresholdExceeded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, OnThresholdExceeded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPerformanceManager_OnThresholdExceeded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnThresholdExceeded_MetaData), NewProp_OnThresholdExceeded_MetaData) }; // 1016478358
void Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionPerformanceManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionPerformanceManager), &Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, Configuration), Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 284650170
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_MonitoringState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_MonitoringState = { "MonitoringState", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, MonitoringState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPerformanceMonitoringState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringState_MetaData), NewProp_MonitoringState_MetaData) }; // 3945916028
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPerformanceManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnPerformanceAlert,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnBottleneckDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnPerformanceReportGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_OnThresholdExceeded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_MonitoringState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_MonitoringState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::ClassParams = {
	&UAuracronWorldPartitionPerformanceManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager.OuterSingleton;
}
UAuracronWorldPartitionPerformanceManager::UAuracronWorldPartitionPerformanceManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionPerformanceManager);
UAuracronWorldPartitionPerformanceManager::~UAuracronWorldPartitionPerformanceManager() {}
// ********** End Class UAuracronWorldPartitionPerformanceManager **********************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPerformanceMonitoringState_StaticEnum, TEXT("EAuracronPerformanceMonitoringState"), &Z_Registration_Info_UEnum_EAuracronPerformanceMonitoringState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3945916028U) },
		{ EAuracronPerformanceSeverity_StaticEnum, TEXT("EAuracronPerformanceSeverity"), &Z_Registration_Info_UEnum_EAuracronPerformanceSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1490741379U) },
		{ EAuracronPerformanceMetricType_StaticEnum, TEXT("EAuracronPerformanceMetricType"), &Z_Registration_Info_UEnum_EAuracronPerformanceMetricType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1828647668U) },
		{ EAuracronBottleneckType_StaticEnum, TEXT("EAuracronBottleneckType"), &Z_Registration_Info_UEnum_EAuracronBottleneckType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3403593494U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPerformanceConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceConfiguration_Statics::NewStructOps, TEXT("AuracronPerformanceConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceConfiguration), 284650170U) },
		{ FAuracronPerformanceMetric::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceMetric_Statics::NewStructOps, TEXT("AuracronPerformanceMetric"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceMetric, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceMetric), 913329360U) },
		{ FAuracronPerformanceReport::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceReport_Statics::NewStructOps, TEXT("AuracronPerformanceReport"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceReport, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceReport), 2122339513U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionPerformanceManager, UAuracronWorldPartitionPerformanceManager::StaticClass, TEXT("UAuracronWorldPartitionPerformanceManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionPerformanceManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionPerformanceManager), 1807308498U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_2555926484(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPerformance_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
