// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGAsyncProcessing.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGAsyncProcessing_generated_h
#error "AuracronPCGAsyncProcessing.generated.h already included, missing '#pragma once' in AuracronPCGAsyncProcessing.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGAsyncProcessing_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronPCGAsyncTaskManager;
class UAuracronPCGCancellationToken;
class UAuracronPCGMemoryPoolManager;
class UPCGGraph;
class UPCGPointData;
enum class EAuracronPCGAsyncProcessingMode : uint8;
enum class EAuracronPCGTaskPriority : uint8;
enum class EAuracronPCGTaskState : uint8;
struct FAuracronPCGAsyncTaskDescriptor;
struct FAuracronPCGAsyncTaskResult;
struct FAuracronPCGProgressInfo;

// ********** Begin ScriptStruct FAuracronPCGAsyncTaskDescriptor ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGAsyncTaskDescriptor;
// ********** End ScriptStruct FAuracronPCGAsyncTaskDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGProgressInfo ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_221_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGProgressInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGProgressInfo;
// ********** End ScriptStruct FAuracronPCGProgressInfo ********************************************

// ********** Begin ScriptStruct FAuracronPCGAsyncTaskResult ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_297_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGAsyncTaskResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGAsyncTaskResult;
// ********** End ScriptStruct FAuracronPCGAsyncTaskResult *****************************************

// ********** Begin Class UAuracronPCGProgressTracker **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRemoveTask); \
	DECLARE_FUNCTION(execClearAllTasks); \
	DECLARE_FUNCTION(execClearCompletedTasks); \
	DECLARE_FUNCTION(execGetActiveTaskCount); \
	DECLARE_FUNCTION(execGetOverallProgress); \
	DECLARE_FUNCTION(execIsTaskActive); \
	DECLARE_FUNCTION(execGetAllProgressInfo); \
	DECLARE_FUNCTION(execGetProgressInfo); \
	DECLARE_FUNCTION(execFinishTracking); \
	DECLARE_FUNCTION(execAddWarning); \
	DECLARE_FUNCTION(execAddError); \
	DECLARE_FUNCTION(execSetTaskState); \
	DECLARE_FUNCTION(execUpdateProgress); \
	DECLARE_FUNCTION(execStartTracking);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGProgressTracker(); \
	friend struct Z_Construct_UClass_UAuracronPCGProgressTracker_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGProgressTracker, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGProgressTracker_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGProgressTracker)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGProgressTracker(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGProgressTracker(UAuracronPCGProgressTracker&&) = delete; \
	UAuracronPCGProgressTracker(const UAuracronPCGProgressTracker&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGProgressTracker); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGProgressTracker); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGProgressTracker) \
	NO_API virtual ~UAuracronPCGProgressTracker();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_359_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_362_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGProgressTracker;

// ********** End Class UAuracronPCGProgressTracker ************************************************

// ********** Begin Class UAuracronPCGMemoryPoolManager ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearMemoryTrackingInfo); \
	DECLARE_FUNCTION(execGetMemoryTrackingInfo); \
	DECLARE_FUNCTION(execEnableMemoryTracking); \
	DECLARE_FUNCTION(execGetAllocationCount); \
	DECLARE_FUNCTION(execGetMemoryUsagePercentage); \
	DECLARE_FUNCTION(execGetFreeMemory); \
	DECLARE_FUNCTION(execGetUsedMemory); \
	DECLARE_FUNCTION(execGetTotalPoolSize); \
	DECLARE_FUNCTION(execIsPoolInitialized); \
	DECLARE_FUNCTION(execShutdownPool); \
	DECLARE_FUNCTION(execInitializePool); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMemoryPoolManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGMemoryPoolManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMemoryPoolManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMemoryPoolManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMemoryPoolManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGMemoryPoolManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMemoryPoolManager(UAuracronPCGMemoryPoolManager&&) = delete; \
	UAuracronPCGMemoryPoolManager(const UAuracronPCGMemoryPoolManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMemoryPoolManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMemoryPoolManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGMemoryPoolManager) \
	NO_API virtual ~UAuracronPCGMemoryPoolManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_599_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_602_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMemoryPoolManager;

// ********** End Class UAuracronPCGMemoryPoolManager **********************************************

// ********** Begin Class UAuracronPCGCancellationToken ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetRemainingTime); \
	DECLARE_FUNCTION(execHasTimeout); \
	DECLARE_FUNCTION(execClearTimeout); \
	DECLARE_FUNCTION(execSetTimeout); \
	DECLARE_FUNCTION(execUnregisterCancellationCallback); \
	DECLARE_FUNCTION(execRegisterCancellationCallback); \
	DECLARE_FUNCTION(execThrowIfCancelled); \
	DECLARE_FUNCTION(execIsCancelled); \
	DECLARE_FUNCTION(execReset); \
	DECLARE_FUNCTION(execCancel); \
	DECLARE_FUNCTION(execCreateToken);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCancellationToken(); \
	friend struct Z_Construct_UClass_UAuracronPCGCancellationToken_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCancellationToken, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCancellationToken_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCancellationToken)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCancellationToken(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCancellationToken(UAuracronPCGCancellationToken&&) = delete; \
	UAuracronPCGCancellationToken(const UAuracronPCGCancellationToken&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCancellationToken); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCancellationToken); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCancellationToken) \
	NO_API virtual ~UAuracronPCGCancellationToken();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_688_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_691_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCancellationToken;

// ********** End Class UAuracronPCGCancellationToken **********************************************

// ********** Begin Delegate FOnTaskCompleted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_882_DELEGATE \
static void FOnTaskCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTaskCompleted, const FString& TaskId, FAuracronPCGAsyncTaskResult Result);


// ********** End Delegate FOnTaskCompleted ********************************************************

// ********** Begin Delegate FOnTaskFailed *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_883_DELEGATE \
static void FOnTaskFailed_DelegateWrapper(const FMulticastScriptDelegate& OnTaskFailed, const FString& TaskId, const FString& ErrorMessage);


// ********** End Delegate FOnTaskFailed ***********************************************************

// ********** Begin Delegate FOnTaskProgress *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_884_DELEGATE \
static void FOnTaskProgress_DelegateWrapper(const FMulticastScriptDelegate& OnTaskProgress, const FString& TaskId, FAuracronPCGProgressInfo ProgressInfo);


// ********** End Delegate FOnTaskProgress *********************************************************

// ********** Begin Class UAuracronPCGAsyncTaskManager *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCleanupAllTasks); \
	DECLARE_FUNCTION(execCleanupFailedTasks); \
	DECLARE_FUNCTION(execCleanupCompletedTasks); \
	DECLARE_FUNCTION(execGetDefaultTaskDescriptor); \
	DECLARE_FUNCTION(execSetDefaultTaskDescriptor); \
	DECLARE_FUNCTION(execGetMaxConcurrentTasks); \
	DECLARE_FUNCTION(execSetMaxConcurrentTasks); \
	DECLARE_FUNCTION(execGetSystemLoadPercentage); \
	DECLARE_FUNCTION(execGetTotalTasksExecuted); \
	DECLARE_FUNCTION(execGetAverageTaskExecutionTime); \
	DECLARE_FUNCTION(execGetPerformanceMetrics); \
	DECLARE_FUNCTION(execAreBatchTasksCompleted); \
	DECLARE_FUNCTION(execWaitForBatchCompletion); \
	DECLARE_FUNCTION(execExecuteBatchPointProcessing); \
	DECLARE_FUNCTION(execGetActiveTaskCount); \
	DECLARE_FUNCTION(execGetCompletedTasks); \
	DECLARE_FUNCTION(execGetActiveTasks); \
	DECLARE_FUNCTION(execGetTaskResult); \
	DECLARE_FUNCTION(execGetTaskProgress); \
	DECLARE_FUNCTION(execGetTaskState); \
	DECLARE_FUNCTION(execIsTaskActive); \
	DECLARE_FUNCTION(execResumeAllTasks); \
	DECLARE_FUNCTION(execPauseAllTasks); \
	DECLARE_FUNCTION(execResumeTask); \
	DECLARE_FUNCTION(execPauseTask); \
	DECLARE_FUNCTION(execCancelAllTasks); \
	DECLARE_FUNCTION(execCancelTask); \
	DECLARE_FUNCTION(execExecuteCustomTaskAsync); \
	DECLARE_FUNCTION(execExecuteGraphAsync); \
	DECLARE_FUNCTION(execExecutePointProcessingAsync); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAsyncTaskManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGAsyncTaskManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAsyncTaskManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAsyncTaskManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAsyncTaskManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGAsyncTaskManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAsyncTaskManager(UAuracronPCGAsyncTaskManager&&) = delete; \
	UAuracronPCGAsyncTaskManager(const UAuracronPCGAsyncTaskManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAsyncTaskManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAsyncTaskManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGAsyncTaskManager) \
	NO_API virtual ~UAuracronPCGAsyncTaskManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_752_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_755_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAsyncTaskManager;

// ********** End Class UAuracronPCGAsyncTaskManager ***********************************************

// ********** Begin Class UAuracronPCGAsyncProcessingUtils *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOptimizeTaskDescriptor); \
	DECLARE_FUNCTION(execIsTaskDescriptorOptimal); \
	DECLARE_FUNCTION(execValidateTaskDescriptor); \
	DECLARE_FUNCTION(execLogSystemInfo); \
	DECLARE_FUNCTION(execGeneratePerformanceReport); \
	DECLARE_FUNCTION(execIsAsyncDebuggingEnabled); \
	DECLARE_FUNCTION(execEnableAsyncDebugging); \
	DECLARE_FUNCTION(execWillTaskFitInMemory); \
	DECLARE_FUNCTION(execEstimateMemoryUsage); \
	DECLARE_FUNCTION(execEstimateExecutionTime); \
	DECLARE_FUNCTION(execCreateOptimizedDescriptor); \
	DECLARE_FUNCTION(execRecommendProcessingMode); \
	DECLARE_FUNCTION(execCalculateOptimalBatchSize); \
	DECLARE_FUNCTION(execIsMultithreadingSupported); \
	DECLARE_FUNCTION(execGetCPUUsagePercentage); \
	DECLARE_FUNCTION(execGetAvailableMemoryMB); \
	DECLARE_FUNCTION(execGetOptimalThreadCount);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAsyncProcessingUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAsyncProcessingUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAsyncProcessingUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAsyncProcessingUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGAsyncProcessingUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAsyncProcessingUtils(UAuracronPCGAsyncProcessingUtils&&) = delete; \
	UAuracronPCGAsyncProcessingUtils(const UAuracronPCGAsyncProcessingUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAsyncProcessingUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAsyncProcessingUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGAsyncProcessingUtils) \
	NO_API virtual ~UAuracronPCGAsyncProcessingUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_945_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h_948_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAsyncProcessingUtils;

// ********** End Class UAuracronPCGAsyncProcessingUtils *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAsyncProcessing_h

// ********** Begin Enum EAuracronPCGAsyncProcessingMode *******************************************
#define FOREACH_ENUM_EAURACRONPCGASYNCPROCESSINGMODE(op) \
	op(EAuracronPCGAsyncProcessingMode::Sequential) \
	op(EAuracronPCGAsyncProcessingMode::Parallel) \
	op(EAuracronPCGAsyncProcessingMode::TaskGraph) \
	op(EAuracronPCGAsyncProcessingMode::AsyncTask) \
	op(EAuracronPCGAsyncProcessingMode::ParallelFor) \
	op(EAuracronPCGAsyncProcessingMode::Hybrid) 

enum class EAuracronPCGAsyncProcessingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAsyncProcessingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAsyncProcessingMode>();
// ********** End Enum EAuracronPCGAsyncProcessingMode *********************************************

// ********** Begin Enum EAuracronPCGTaskPriority **************************************************
#define FOREACH_ENUM_EAURACRONPCGTASKPRIORITY(op) \
	op(EAuracronPCGTaskPriority::Low) \
	op(EAuracronPCGTaskPriority::Normal) \
	op(EAuracronPCGTaskPriority::High) \
	op(EAuracronPCGTaskPriority::Critical) \
	op(EAuracronPCGTaskPriority::Immediate) 

enum class EAuracronPCGTaskPriority : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTaskPriority> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTaskPriority>();
// ********** End Enum EAuracronPCGTaskPriority ****************************************************

// ********** Begin Enum EAuracronPCGTaskState *****************************************************
#define FOREACH_ENUM_EAURACRONPCGTASKSTATE(op) \
	op(EAuracronPCGTaskState::Pending) \
	op(EAuracronPCGTaskState::Running) \
	op(EAuracronPCGTaskState::Completed) \
	op(EAuracronPCGTaskState::Failed) \
	op(EAuracronPCGTaskState::Cancelled) \
	op(EAuracronPCGTaskState::Paused) 

enum class EAuracronPCGTaskState : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTaskState> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTaskState>();
// ********** End Enum EAuracronPCGTaskState *******************************************************

// ********** Begin Enum EAuracronPCGThreadType ****************************************************
#define FOREACH_ENUM_EAURACRONPCGTHREADTYPE(op) \
	op(EAuracronPCGThreadType::GameThread) \
	op(EAuracronPCGThreadType::RenderThread) \
	op(EAuracronPCGThreadType::AnyThread) \
	op(EAuracronPCGThreadType::BackgroundThread) \
	op(EAuracronPCGThreadType::WorkerThread) \
	op(EAuracronPCGThreadType::HighPriorityThread) 

enum class EAuracronPCGThreadType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGThreadType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGThreadType>();
// ********** End Enum EAuracronPCGThreadType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
