// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionPython.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionDebug.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionPerformance.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionPython() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugCellInfo();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGridCell();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceMetric();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonCallbackData();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonExecutionResult();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPythonCallbackType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPythonCallbackType;
static UEnum* EAuracronPythonCallbackType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonCallbackType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPythonCallbackType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronPythonCallbackType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPythonCallbackType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPythonCallbackType>()
{
	return EAuracronPythonCallbackType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CellLoaded.DisplayName", "Cell Loaded" },
		{ "CellLoaded.Name", "EAuracronPythonCallbackType::CellLoaded" },
		{ "CellUnloaded.DisplayName", "Cell Unloaded" },
		{ "CellUnloaded.Name", "EAuracronPythonCallbackType::CellUnloaded" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python callback types\n" },
#endif
		{ "DebugEvent.DisplayName", "Debug Event" },
		{ "DebugEvent.Name", "EAuracronPythonCallbackType::DebugEvent" },
		{ "GridUpdated.DisplayName", "Grid Updated" },
		{ "GridUpdated.Name", "EAuracronPythonCallbackType::GridUpdated" },
		{ "LayerChanged.DisplayName", "Layer Changed" },
		{ "LayerChanged.Name", "EAuracronPythonCallbackType::LayerChanged" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
		{ "PerformanceAlert.DisplayName", "Performance Alert" },
		{ "PerformanceAlert.Name", "EAuracronPythonCallbackType::PerformanceAlert" },
		{ "StreamingCompleted.DisplayName", "Streaming Completed" },
		{ "StreamingCompleted.Name", "EAuracronPythonCallbackType::StreamingCompleted" },
		{ "StreamingStarted.DisplayName", "Streaming Started" },
		{ "StreamingStarted.Name", "EAuracronPythonCallbackType::StreamingStarted" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python callback types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPythonCallbackType::CellLoaded", (int64)EAuracronPythonCallbackType::CellLoaded },
		{ "EAuracronPythonCallbackType::CellUnloaded", (int64)EAuracronPythonCallbackType::CellUnloaded },
		{ "EAuracronPythonCallbackType::StreamingStarted", (int64)EAuracronPythonCallbackType::StreamingStarted },
		{ "EAuracronPythonCallbackType::StreamingCompleted", (int64)EAuracronPythonCallbackType::StreamingCompleted },
		{ "EAuracronPythonCallbackType::PerformanceAlert", (int64)EAuracronPythonCallbackType::PerformanceAlert },
		{ "EAuracronPythonCallbackType::DebugEvent", (int64)EAuracronPythonCallbackType::DebugEvent },
		{ "EAuracronPythonCallbackType::GridUpdated", (int64)EAuracronPythonCallbackType::GridUpdated },
		{ "EAuracronPythonCallbackType::LayerChanged", (int64)EAuracronPythonCallbackType::LayerChanged },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronPythonCallbackType",
	"EAuracronPythonCallbackType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonCallbackType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPythonCallbackType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPythonCallbackType.InnerSingleton;
}
// ********** End Enum EAuracronPythonCallbackType *************************************************

// ********** Begin Enum EAuracronPythonExecutionMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPythonExecutionMode;
static UEnum* EAuracronPythonExecutionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronPythonExecutionMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronPythonExecutionMode>()
{
	return EAuracronPythonExecutionMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Asynchronous.DisplayName", "Asynchronous" },
		{ "Asynchronous.Name", "EAuracronPythonExecutionMode::Asynchronous" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python execution modes\n" },
#endif
		{ "Deferred.DisplayName", "Deferred" },
		{ "Deferred.Name", "EAuracronPythonExecutionMode::Deferred" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
		{ "Synchronous.DisplayName", "Synchronous" },
		{ "Synchronous.Name", "EAuracronPythonExecutionMode::Synchronous" },
		{ "Threaded.DisplayName", "Threaded" },
		{ "Threaded.Name", "EAuracronPythonExecutionMode::Threaded" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python execution modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPythonExecutionMode::Synchronous", (int64)EAuracronPythonExecutionMode::Synchronous },
		{ "EAuracronPythonExecutionMode::Asynchronous", (int64)EAuracronPythonExecutionMode::Asynchronous },
		{ "EAuracronPythonExecutionMode::Threaded", (int64)EAuracronPythonExecutionMode::Threaded },
		{ "EAuracronPythonExecutionMode::Deferred", (int64)EAuracronPythonExecutionMode::Deferred },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronPythonExecutionMode",
	"EAuracronPythonExecutionMode",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPythonExecutionMode.InnerSingleton;
}
// ********** End Enum EAuracronPythonExecutionMode ************************************************

// ********** Begin ScriptStruct FAuracronPythonConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration;
class UScriptStruct* FAuracronPythonConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPythonConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Configuration\n * Configuration settings for Python integration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Configuration\nConfiguration settings for Python integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePythonIntegration_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncExecution_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCallbacks_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableErrorHandling_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultExecutionMode_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonModulePath_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainModuleName_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackTimeout_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentExecutions_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogPythonExecution_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogPythonErrors_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoReloadModules_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalPythonPaths_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PythonEnvironmentVariables_MetaData[] = {
		{ "Category", "Python" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnablePythonIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePythonIntegration;
	static void NewProp_bEnableAsyncExecution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncExecution;
	static void NewProp_bEnableCallbacks_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCallbacks;
	static void NewProp_bEnableErrorHandling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableErrorHandling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultExecutionMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonModulePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MainModuleName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CallbackTimeout;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentExecutions;
	static void NewProp_bLogPythonExecution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogPythonExecution;
	static void NewProp_bLogPythonErrors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogPythonErrors;
	static void NewProp_bAutoReloadModules_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoReloadModules;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalPythonPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdditionalPythonPaths;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonEnvironmentVariables_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PythonEnvironmentVariables_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PythonEnvironmentVariables;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnablePythonIntegration_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bEnablePythonIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnablePythonIntegration = { "bEnablePythonIntegration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnablePythonIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePythonIntegration_MetaData), NewProp_bEnablePythonIntegration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableAsyncExecution_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bEnableAsyncExecution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableAsyncExecution = { "bEnableAsyncExecution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableAsyncExecution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncExecution_MetaData), NewProp_bEnableAsyncExecution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableCallbacks_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bEnableCallbacks = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableCallbacks = { "bEnableCallbacks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableCallbacks_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCallbacks_MetaData), NewProp_bEnableCallbacks_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableErrorHandling_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bEnableErrorHandling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableErrorHandling = { "bEnableErrorHandling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableErrorHandling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableErrorHandling_MetaData), NewProp_bEnableErrorHandling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_DefaultExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_DefaultExecutionMode = { "DefaultExecutionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, DefaultExecutionMode), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultExecutionMode_MetaData), NewProp_DefaultExecutionMode_MetaData) }; // 602965309
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonModulePath = { "PythonModulePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, PythonModulePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonModulePath_MetaData), NewProp_PythonModulePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_MainModuleName = { "MainModuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, MainModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainModuleName_MetaData), NewProp_MainModuleName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_CallbackTimeout = { "CallbackTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, CallbackTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackTimeout_MetaData), NewProp_CallbackTimeout_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_MaxConcurrentExecutions = { "MaxConcurrentExecutions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, MaxConcurrentExecutions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentExecutions_MetaData), NewProp_MaxConcurrentExecutions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonExecution_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bLogPythonExecution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonExecution = { "bLogPythonExecution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonExecution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogPythonExecution_MetaData), NewProp_bLogPythonExecution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonErrors_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bLogPythonErrors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonErrors = { "bLogPythonErrors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonErrors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogPythonErrors_MetaData), NewProp_bLogPythonErrors_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bAutoReloadModules_SetBit(void* Obj)
{
	((FAuracronPythonConfiguration*)Obj)->bAutoReloadModules = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bAutoReloadModules = { "bAutoReloadModules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonConfiguration), &Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bAutoReloadModules_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoReloadModules_MetaData), NewProp_bAutoReloadModules_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_AdditionalPythonPaths_Inner = { "AdditionalPythonPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_AdditionalPythonPaths = { "AdditionalPythonPaths", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, AdditionalPythonPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalPythonPaths_MetaData), NewProp_AdditionalPythonPaths_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables_ValueProp = { "PythonEnvironmentVariables", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables_Key_KeyProp = { "PythonEnvironmentVariables_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables = { "PythonEnvironmentVariables", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonConfiguration, PythonEnvironmentVariables), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PythonEnvironmentVariables_MetaData), NewProp_PythonEnvironmentVariables_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnablePythonIntegration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableAsyncExecution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableCallbacks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bEnableErrorHandling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_DefaultExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_DefaultExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonModulePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_MainModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_CallbackTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_MaxConcurrentExecutions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonExecution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bLogPythonErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_bAutoReloadModules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_AdditionalPythonPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_AdditionalPythonPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewProp_PythonEnvironmentVariables,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPythonConfiguration",
	Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::PropPointers),
	sizeof(FAuracronPythonConfiguration),
	alignof(FAuracronPythonConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronPythonCallbackData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData;
class UScriptStruct* FAuracronPythonCallbackData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonCallbackData, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPythonCallbackData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Callback Data\n * Data structure for Python callbacks\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Callback Data\nData structure for Python callbacks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackType_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackId_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAsync_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Python Callback" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CallbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CallbackType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static void NewProp_bIsAsync_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAsync;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonCallbackData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackType = { "CallbackType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, CallbackType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackType_MetaData), NewProp_CallbackType_MetaData) }; // 787678367
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackId = { "CallbackId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, CallbackId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackId_MetaData), NewProp_CallbackId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_bIsAsync_SetBit(void* Obj)
{
	((FAuracronPythonCallbackData*)Obj)->bIsAsync = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_bIsAsync = { "bIsAsync", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonCallbackData), &Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_bIsAsync_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAsync_MetaData), NewProp_bIsAsync_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonCallbackData, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_CallbackId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_bIsAsync,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPythonCallbackData",
	Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::PropPointers),
	sizeof(FAuracronPythonCallbackData),
	alignof(FAuracronPythonCallbackData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonCallbackData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonCallbackData *****************************************

// ********** Begin ScriptStruct FAuracronPythonExecutionResult ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult;
class UScriptStruct* FAuracronPythonExecutionResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPythonExecutionResult, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronPythonExecutionResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Python Execution Result\n * Result data from Python script execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python Execution Result\nResult data from Python script execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResultData_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTime_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimestamp_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputParameters_MetaData[] = {
		{ "Category", "Python Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResultData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExecutionTimestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_OutputParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPythonExecutionResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPythonExecutionResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPythonExecutionResult), &Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ResultData = { "ResultData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonExecutionResult, ResultData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResultData_MetaData), NewProp_ResultData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonExecutionResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ExecutionTime = { "ExecutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonExecutionResult, ExecutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTime_MetaData), NewProp_ExecutionTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ExecutionTimestamp = { "ExecutionTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonExecutionResult, ExecutionTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimestamp_MetaData), NewProp_ExecutionTimestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters_ValueProp = { "OutputParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters_Key_KeyProp = { "OutputParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters = { "OutputParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPythonExecutionResult, OutputParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputParameters_MetaData), NewProp_OutputParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ResultData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ExecutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_ExecutionTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewProp_OutputParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronPythonExecutionResult",
	Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::PropPointers),
	sizeof(FAuracronPythonExecutionResult),
	alignof(FAuracronPythonExecutionResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPythonExecutionResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPythonExecutionResult **************************************

// ********** Begin ScriptStruct FAuracronStreamingData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStreamingData;
class UScriptStruct* FAuracronStreamingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStreamingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStreamingData, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronStreamingData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStreamingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Streaming Data\n * Data structure for streaming information\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Data\nData structure for streaming information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Streaming Data" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Streaming Data" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingProgress_MetaData[] = {
		{ "Category", "Streaming Data" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellBounds_MetaData[] = {
		{ "Category", "Streaming Data" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorCount_MetaData[] = {
		{ "Category", "Streaming Data" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellBounds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStreamingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingData, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingData, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 676551000
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_LoadingProgress = { "LoadingProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingData, LoadingProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingProgress_MetaData), NewProp_LoadingProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_CellBounds = { "CellBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingData, CellBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellBounds_MetaData), NewProp_CellBounds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_ActorCount = { "ActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStreamingData, ActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorCount_MetaData), NewProp_ActorCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_LoadingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_CellBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewProp_ActorCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronStreamingData",
	Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::PropPointers),
	sizeof(FAuracronStreamingData),
	alignof(FAuracronStreamingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStreamingData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStreamingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStreamingData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStreamingData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStreamingData **********************************************

// ********** Begin Delegate FOnPythonExecutionComplete ********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms
	{
		bool bSuccess;
		FAuracronPythonExecutionResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 3018705873
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "OnPythonExecutionComplete__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPythonBridge::FOnPythonExecutionComplete_DelegateWrapper(const FMulticastScriptDelegate& OnPythonExecutionComplete, bool bSuccess, FAuracronPythonExecutionResult Result)
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms
	{
		bool bSuccess;
		FAuracronPythonExecutionResult Result;
	};
	AuracronWorldPartitionPythonBridge_eventOnPythonExecutionComplete_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	Parms.Result=Result;
	OnPythonExecutionComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPythonExecutionComplete **********************************************

// ********** Begin Delegate FOnPythonError ********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms
	{
		FString ErrorMessage;
		FString Context;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Context;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms, Context), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::NewProp_Context,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "OnPythonError__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPythonBridge::FOnPythonError_DelegateWrapper(const FMulticastScriptDelegate& OnPythonError, const FString& ErrorMessage, const FString& Context)
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms
	{
		FString ErrorMessage;
		FString Context;
	};
	AuracronWorldPartitionPythonBridge_eventOnPythonError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.Context=Context;
	OnPythonError.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPythonError **********************************************************

// ********** Begin Delegate FOnPythonCallback *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms
	{
		EAuracronPythonCallbackType CallbackType;
		FAuracronPythonCallbackData CallbackData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CallbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CallbackType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CallbackData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackType = { "CallbackType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms, CallbackType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, METADATA_PARAMS(0, nullptr) }; // 787678367
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackData = { "CallbackData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms, CallbackData), Z_Construct_UScriptStruct_FAuracronPythonCallbackData, METADATA_PARAMS(0, nullptr) }; // 1012494777
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::NewProp_CallbackData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "OnPythonCallback__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionPythonBridge::FOnPythonCallback_DelegateWrapper(const FMulticastScriptDelegate& OnPythonCallback, EAuracronPythonCallbackType CallbackType, FAuracronPythonCallbackData CallbackData)
{
	struct AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms
	{
		EAuracronPythonCallbackType CallbackType;
		FAuracronPythonCallbackData CallbackData;
	};
	AuracronWorldPartitionPythonBridge_eventOnPythonCallback_Parms Parms;
	Parms.CallbackType=CallbackType;
	Parms.CallbackData=CallbackData;
	OnPythonCallback.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPythonCallback *******************************************************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function CancelPythonExecution *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "CancelPythonExecution", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execCancelPythonExecution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelPythonExecution();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function CancelPythonExecution *********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ClearAllCallbacks ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ClearAllCallbacks", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execClearAllCallbacks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllCallbacks();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ClearAllCallbacks *************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ClearPythonErrors ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ClearPythonErrors", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execClearPythonErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearPythonErrors();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ClearPythonErrors *************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ClearPythonLogs *************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ClearPythonLogs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execClearPythonLogs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearPythonLogs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ClearPythonLogs ***************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ConvertDebugDataToPython ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventConvertDebugDataToPython_Parms
	{
		FAuracronDebugCellInfo DebugData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DebugData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::NewProp_DebugData = { "DebugData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertDebugDataToPython_Parms, DebugData), Z_Construct_UScriptStruct_FAuracronDebugCellInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugData_MetaData), NewProp_DebugData_MetaData) }; // 3877642631
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertDebugDataToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::NewProp_DebugData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ConvertDebugDataToPython", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertDebugDataToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertDebugDataToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execConvertDebugDataToPython)
{
	P_GET_STRUCT_REF(FAuracronDebugCellInfo,Z_Param_Out_DebugData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ConvertDebugDataToPython(Z_Param_Out_DebugData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ConvertDebugDataToPython ******

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ConvertGridCellToPython *****
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventConvertGridCellToPython_Parms
	{
		FAuracronGridCell Cell;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data conversion utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data conversion utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Cell_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Cell;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::NewProp_Cell = { "Cell", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertGridCellToPython_Parms, Cell), Z_Construct_UScriptStruct_FAuracronGridCell, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Cell_MetaData), NewProp_Cell_MetaData) }; // 454277303
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertGridCellToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::NewProp_Cell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ConvertGridCellToPython", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertGridCellToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertGridCellToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execConvertGridCellToPython)
{
	P_GET_STRUCT_REF(FAuracronGridCell,Z_Param_Out_Cell);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ConvertGridCellToPython(Z_Param_Out_Cell);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ConvertGridCellToPython *******

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ConvertPerformanceDataToPython 
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventConvertPerformanceDataToPython_Parms
	{
		FAuracronPerformanceMetric PerformanceData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::NewProp_PerformanceData = { "PerformanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertPerformanceDataToPython_Parms, PerformanceData), Z_Construct_UScriptStruct_FAuracronPerformanceMetric, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceData_MetaData), NewProp_PerformanceData_MetaData) }; // 913329360
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertPerformanceDataToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::NewProp_PerformanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ConvertPerformanceDataToPython", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertPerformanceDataToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertPerformanceDataToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execConvertPerformanceDataToPython)
{
	P_GET_STRUCT_REF(FAuracronPerformanceMetric,Z_Param_Out_PerformanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ConvertPerformanceDataToPython(Z_Param_Out_PerformanceData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ConvertPerformanceDataToPython 

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ConvertStreamingDataToPython 
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventConvertStreamingDataToPython_Parms
	{
		FAuracronStreamingData StreamingData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::NewProp_StreamingData = { "StreamingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertStreamingDataToPython_Parms, StreamingData), Z_Construct_UScriptStruct_FAuracronStreamingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingData_MetaData), NewProp_StreamingData_MetaData) }; // 4018811434
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventConvertStreamingDataToPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::NewProp_StreamingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ConvertStreamingDataToPython", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertStreamingDataToPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::AuracronWorldPartitionPythonBridge_eventConvertStreamingDataToPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execConvertStreamingDataToPython)
{
	P_GET_STRUCT_REF(FAuracronStreamingData,Z_Param_Out_StreamingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ConvertStreamingDataToPython(Z_Param_Out_StreamingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ConvertStreamingDataToPython **

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function EnableErrorHandling *********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventEnableErrorHandling_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Error handling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Error handling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventEnableErrorHandling_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventEnableErrorHandling_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "EnableErrorHandling", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::AuracronWorldPartitionPythonBridge_eventEnableErrorHandling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::AuracronWorldPartitionPythonBridge_eventEnableErrorHandling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execEnableErrorHandling)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableErrorHandling(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function EnableErrorHandling ***********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function EnablePythonLogging *********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventEnablePythonLogging_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Logging and debugging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Logging and debugging" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventEnablePythonLogging_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventEnablePythonLogging_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "EnablePythonLogging", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::AuracronWorldPartitionPythonBridge_eventEnablePythonLogging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::AuracronWorldPartitionPythonBridge_eventEnablePythonLogging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execEnablePythonLogging)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePythonLogging(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function EnablePythonLogging ***********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExecutePythonFunction *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms
	{
		FString ModuleName;
		FString FunctionName;
		TMap<FString,FString> Parameters;
		FAuracronPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 3018705873
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExecutePythonFunction", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonFunction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExecutePythonFunction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonExecutionResult*)Z_Param__Result=P_THIS->ExecutePythonFunction(Z_Param_ModuleName,Z_Param_FunctionName,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExecutePythonFunction *********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExecutePythonFunctionAsync **
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms
	{
		FString ModuleName;
		FString FunctionName;
		TMap<FString,FString> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExecutePythonFunctionAsync", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonFunctionAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExecutePythonFunctionAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecutePythonFunctionAsync(Z_Param_ModuleName,Z_Param_FunctionName,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExecutePythonFunctionAsync ****

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExecutePythonScript *********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms
	{
		FString Script;
		EAuracronPythonExecutionMode ExecutionMode;
		FAuracronPythonExecutionResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python script execution\n" },
#endif
		{ "CPP_Default_ExecutionMode", "Synchronous" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python script execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Script_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Script;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ExecutionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ExecutionMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_Script = { "Script", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms, Script), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Script_MetaData), NewProp_Script_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ExecutionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ExecutionMode = { "ExecutionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms, ExecutionMode), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonExecutionMode, METADATA_PARAMS(0, nullptr) }; // 602965309
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonExecutionResult, METADATA_PARAMS(0, nullptr) }; // 3018705873
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_Script,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ExecutionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ExecutionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExecutePythonScript", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::AuracronWorldPartitionPythonBridge_eventExecutePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExecutePythonScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Script);
	P_GET_ENUM(EAuracronPythonExecutionMode,Z_Param_ExecutionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonExecutionResult*)Z_Param__Result=P_THIS->ExecutePythonScript(Z_Param_Script,EAuracronPythonExecutionMode(Z_Param_ExecutionMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExecutePythonScript ***********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExposeDebugAPIs *************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExposeDebugAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExposeDebugAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExposeDebugAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExposeDebugAPIs ***************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExposeGridSystemAPIs ********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExposeGridSystemAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExposeGridSystemAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExposeGridSystemAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExposeGridSystemAPIs **********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExposePerformanceAPIs *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExposePerformanceAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExposePerformanceAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExposePerformanceAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExposePerformanceAPIs *********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExposeStreamingAPIs *********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExposeStreamingAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExposeStreamingAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExposeStreamingAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExposeStreamingAPIs ***********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ExposeWorldPartitionAPIs ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World Partition API exposure\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition API exposure" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ExposeWorldPartitionAPIs", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execExposeWorldPartitionAPIs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExposeWorldPartitionAPIs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ExposeWorldPartitionAPIs ******

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetConfiguration_Parms
	{
		FAuracronPythonConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPythonConfiguration, METADATA_PARAMS(0, nullptr) }; // 2876235695
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::AuracronWorldPartitionPythonBridge_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::AuracronWorldPartitionPythonBridge_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPythonConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetInstance *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetInstance_Parms
	{
		UAuracronWorldPartitionPythonBridge* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::AuracronWorldPartitionPythonBridge_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::AuracronWorldPartitionPythonBridge_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionPythonBridge**)Z_Param__Result=UAuracronWorldPartitionPythonBridge::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetInstance *******************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetLoadedModules ************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetLoadedModules_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetLoadedModules_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetLoadedModules", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::AuracronWorldPartitionPythonBridge_eventGetLoadedModules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::AuracronWorldPartitionPythonBridge_eventGetLoadedModules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetLoadedModules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedModules();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetLoadedModules **************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetPythonErrors *************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetPythonErrors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetPythonErrors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetPythonErrors", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::AuracronWorldPartitionPythonBridge_eventGetPythonErrors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::AuracronWorldPartitionPythonBridge_eventGetPythonErrors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetPythonErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPythonErrors();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetPythonErrors ***************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetPythonLogs ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetPythonLogs_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetPythonLogs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetPythonLogs", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::AuracronWorldPartitionPythonBridge_eventGetPythonLogs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::AuracronWorldPartitionPythonBridge_eventGetPythonLogs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetPythonLogs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPythonLogs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetPythonLogs *****************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function GetRegisteredCallbacks ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventGetRegisteredCallbacks_Parms
	{
		EAuracronPythonCallbackType CallbackType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CallbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CallbackType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_CallbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_CallbackType = { "CallbackType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetRegisteredCallbacks_Parms, CallbackType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, METADATA_PARAMS(0, nullptr) }; // 787678367
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventGetRegisteredCallbacks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_CallbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_CallbackType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "GetRegisteredCallbacks", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::AuracronWorldPartitionPythonBridge_eventGetRegisteredCallbacks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::AuracronWorldPartitionPythonBridge_eventGetRegisteredCallbacks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execGetRegisteredCallbacks)
{
	P_GET_ENUM(EAuracronPythonCallbackType,Z_Param_CallbackType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetRegisteredCallbacks(EAuracronPythonCallbackType(Z_Param_CallbackType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function GetRegisteredCallbacks ********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function Initialize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventInitialize_Parms
	{
		FAuracronPythonConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Bridge lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bridge lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronPythonConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2876235695
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::AuracronWorldPartitionPythonBridge_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::AuracronWorldPartitionPythonBridge_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronPythonConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function Initialize ********************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function InitializePythonEnvironment *
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventInitializePythonEnvironment_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Python environment management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Python environment management" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventInitializePythonEnvironment_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventInitializePythonEnvironment_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "InitializePythonEnvironment", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::AuracronWorldPartitionPythonBridge_eventInitializePythonEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::AuracronWorldPartitionPythonBridge_eventInitializePythonEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execInitializePythonEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function InitializePythonEnvironment ***

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function IsErrorHandlingEnabled ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventIsErrorHandlingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventIsErrorHandlingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventIsErrorHandlingEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "IsErrorHandlingEnabled", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::AuracronWorldPartitionPythonBridge_eventIsErrorHandlingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::AuracronWorldPartitionPythonBridge_eventIsErrorHandlingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execIsErrorHandlingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsErrorHandlingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function IsErrorHandlingEnabled ********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function IsInitialized ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::AuracronWorldPartitionPythonBridge_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::AuracronWorldPartitionPythonBridge_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function IsInitialized *****************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function IsPythonEnvironmentReady ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventIsPythonEnvironmentReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventIsPythonEnvironmentReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventIsPythonEnvironmentReady_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "IsPythonEnvironmentReady", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonEnvironmentReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonEnvironmentReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execIsPythonEnvironmentReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPythonEnvironmentReady();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function IsPythonEnvironmentReady ******

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function IsPythonExecutionInProgress *
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventIsPythonExecutionInProgress_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventIsPythonExecutionInProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventIsPythonExecutionInProgress_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "IsPythonExecutionInProgress", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonExecutionInProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonExecutionInProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execIsPythonExecutionInProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPythonExecutionInProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function IsPythonExecutionInProgress ***

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function IsPythonLoggingEnabled ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventIsPythonLoggingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventIsPythonLoggingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventIsPythonLoggingEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "IsPythonLoggingEnabled", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonLoggingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::AuracronWorldPartitionPythonBridge_eventIsPythonLoggingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execIsPythonLoggingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPythonLoggingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function IsPythonLoggingEnabled ********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function LoadPythonModule ************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "LoadPythonModule", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::AuracronWorldPartitionPythonBridge_eventLoadPythonModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execLoadPythonModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadPythonModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function LoadPythonModule **************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function RegisterPythonCallback ******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms
	{
		EAuracronPythonCallbackType CallbackType;
		FString FunctionName;
		FString ModuleName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callback management\n" },
#endif
		{ "CPP_Default_ModuleName", "" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CallbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CallbackType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_CallbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_CallbackType = { "CallbackType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms, CallbackType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, METADATA_PARAMS(0, nullptr) }; // 787678367
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_CallbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_CallbackType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::NewProp_ModuleName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "RegisterPythonCallback", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventRegisterPythonCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execRegisterPythonCallback)
{
	P_GET_ENUM(EAuracronPythonCallbackType,Z_Param_CallbackType);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPythonCallback(EAuracronPythonCallbackType(Z_Param_CallbackType),Z_Param_FunctionName,Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function RegisterPythonCallback ********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ReloadPythonModule **********
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms
	{
		FString ModuleName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModuleName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ModuleName = { "ModuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms, ModuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModuleName_MetaData), NewProp_ModuleName_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ModuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ReloadPythonModule", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::AuracronWorldPartitionPythonBridge_eventReloadPythonModule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execReloadPythonModule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReloadPythonModule(Z_Param_ModuleName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ReloadPythonModule ************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function SetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventSetConfiguration_Parms
	{
		FAuracronPythonConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronPythonConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2876235695
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::AuracronWorldPartitionPythonBridge_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::AuracronWorldPartitionPythonBridge_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronPythonConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function SetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function Shutdown ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function Shutdown **********************

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function ShutdownPythonEnvironment ***
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "ShutdownPythonEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execShutdownPythonEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPythonEnvironment();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function ShutdownPythonEnvironment *****

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function TriggerPythonCallback *******
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventTriggerPythonCallback_Parms
	{
		FAuracronPythonCallbackData CallbackData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CallbackData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::NewProp_CallbackData = { "CallbackData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventTriggerPythonCallback_Parms, CallbackData), Z_Construct_UScriptStruct_FAuracronPythonCallbackData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackData_MetaData), NewProp_CallbackData_MetaData) }; // 1012494777
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::NewProp_CallbackData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "TriggerPythonCallback", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventTriggerPythonCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventTriggerPythonCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execTriggerPythonCallback)
{
	P_GET_STRUCT_REF(FAuracronPythonCallbackData,Z_Param_Out_CallbackData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerPythonCallback(Z_Param_Out_CallbackData);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function TriggerPythonCallback *********

// ********** Begin Class UAuracronWorldPartitionPythonBridge Function UnregisterPythonCallback ****
struct Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics
{
	struct AuracronWorldPartitionPythonBridge_eventUnregisterPythonCallback_Parms
	{
		EAuracronPythonCallbackType CallbackType;
		FString FunctionName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Python Bridge" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CallbackType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CallbackType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_CallbackType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_CallbackType = { "CallbackType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventUnregisterPythonCallback_Parms, CallbackType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronPythonCallbackType, METADATA_PARAMS(0, nullptr) }; // 787678367
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionPythonBridge_eventUnregisterPythonCallback_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_CallbackType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_CallbackType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::NewProp_FunctionName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, nullptr, "UnregisterPythonCallback", Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventUnregisterPythonCallback_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::AuracronWorldPartitionPythonBridge_eventUnregisterPythonCallback_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionPythonBridge::execUnregisterPythonCallback)
{
	P_GET_ENUM(EAuracronPythonCallbackType,Z_Param_CallbackType);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterPythonCallback(EAuracronPythonCallbackType(Z_Param_CallbackType),Z_Param_FunctionName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionPythonBridge Function UnregisterPythonCallback ******

// ********** Begin Class UAuracronWorldPartitionPythonBridge **************************************
void UAuracronWorldPartitionPythonBridge::StaticRegisterNativesUAuracronWorldPartitionPythonBridge()
{
	UClass* Class = UAuracronWorldPartitionPythonBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelPythonExecution", &UAuracronWorldPartitionPythonBridge::execCancelPythonExecution },
		{ "ClearAllCallbacks", &UAuracronWorldPartitionPythonBridge::execClearAllCallbacks },
		{ "ClearPythonErrors", &UAuracronWorldPartitionPythonBridge::execClearPythonErrors },
		{ "ClearPythonLogs", &UAuracronWorldPartitionPythonBridge::execClearPythonLogs },
		{ "ConvertDebugDataToPython", &UAuracronWorldPartitionPythonBridge::execConvertDebugDataToPython },
		{ "ConvertGridCellToPython", &UAuracronWorldPartitionPythonBridge::execConvertGridCellToPython },
		{ "ConvertPerformanceDataToPython", &UAuracronWorldPartitionPythonBridge::execConvertPerformanceDataToPython },
		{ "ConvertStreamingDataToPython", &UAuracronWorldPartitionPythonBridge::execConvertStreamingDataToPython },
		{ "EnableErrorHandling", &UAuracronWorldPartitionPythonBridge::execEnableErrorHandling },
		{ "EnablePythonLogging", &UAuracronWorldPartitionPythonBridge::execEnablePythonLogging },
		{ "ExecutePythonFunction", &UAuracronWorldPartitionPythonBridge::execExecutePythonFunction },
		{ "ExecutePythonFunctionAsync", &UAuracronWorldPartitionPythonBridge::execExecutePythonFunctionAsync },
		{ "ExecutePythonScript", &UAuracronWorldPartitionPythonBridge::execExecutePythonScript },
		{ "ExposeDebugAPIs", &UAuracronWorldPartitionPythonBridge::execExposeDebugAPIs },
		{ "ExposeGridSystemAPIs", &UAuracronWorldPartitionPythonBridge::execExposeGridSystemAPIs },
		{ "ExposePerformanceAPIs", &UAuracronWorldPartitionPythonBridge::execExposePerformanceAPIs },
		{ "ExposeStreamingAPIs", &UAuracronWorldPartitionPythonBridge::execExposeStreamingAPIs },
		{ "ExposeWorldPartitionAPIs", &UAuracronWorldPartitionPythonBridge::execExposeWorldPartitionAPIs },
		{ "GetConfiguration", &UAuracronWorldPartitionPythonBridge::execGetConfiguration },
		{ "GetInstance", &UAuracronWorldPartitionPythonBridge::execGetInstance },
		{ "GetLoadedModules", &UAuracronWorldPartitionPythonBridge::execGetLoadedModules },
		{ "GetPythonErrors", &UAuracronWorldPartitionPythonBridge::execGetPythonErrors },
		{ "GetPythonLogs", &UAuracronWorldPartitionPythonBridge::execGetPythonLogs },
		{ "GetRegisteredCallbacks", &UAuracronWorldPartitionPythonBridge::execGetRegisteredCallbacks },
		{ "Initialize", &UAuracronWorldPartitionPythonBridge::execInitialize },
		{ "InitializePythonEnvironment", &UAuracronWorldPartitionPythonBridge::execInitializePythonEnvironment },
		{ "IsErrorHandlingEnabled", &UAuracronWorldPartitionPythonBridge::execIsErrorHandlingEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionPythonBridge::execIsInitialized },
		{ "IsPythonEnvironmentReady", &UAuracronWorldPartitionPythonBridge::execIsPythonEnvironmentReady },
		{ "IsPythonExecutionInProgress", &UAuracronWorldPartitionPythonBridge::execIsPythonExecutionInProgress },
		{ "IsPythonLoggingEnabled", &UAuracronWorldPartitionPythonBridge::execIsPythonLoggingEnabled },
		{ "LoadPythonModule", &UAuracronWorldPartitionPythonBridge::execLoadPythonModule },
		{ "RegisterPythonCallback", &UAuracronWorldPartitionPythonBridge::execRegisterPythonCallback },
		{ "ReloadPythonModule", &UAuracronWorldPartitionPythonBridge::execReloadPythonModule },
		{ "SetConfiguration", &UAuracronWorldPartitionPythonBridge::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionPythonBridge::execShutdown },
		{ "ShutdownPythonEnvironment", &UAuracronWorldPartitionPythonBridge::execShutdownPythonEnvironment },
		{ "TriggerPythonCallback", &UAuracronWorldPartitionPythonBridge::execTriggerPythonCallback },
		{ "UnregisterPythonCallback", &UAuracronWorldPartitionPythonBridge::execUnregisterPythonCallback },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge;
UClass* UAuracronWorldPartitionPythonBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionPythonBridge;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionPythonBridge"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionPythonBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_NoRegister()
{
	return UAuracronWorldPartitionPythonBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Python Bridge\n * Main bridge class for Python integration with World Partition system\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionPython.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Python Bridge\nMain bridge class for Python integration with World Partition system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPythonExecutionComplete_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPythonError_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPythonCallback_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPythonEnvironmentReady_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionPython.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPythonExecutionComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPythonError;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPythonCallback;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static void NewProp_bPythonEnvironmentReady_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPythonEnvironmentReady;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_CancelPythonExecution, "CancelPythonExecution" }, // 4006765556
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearAllCallbacks, "ClearAllCallbacks" }, // 819073713
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonErrors, "ClearPythonErrors" }, // 1129087805
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ClearPythonLogs, "ClearPythonLogs" }, // 164568134
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertDebugDataToPython, "ConvertDebugDataToPython" }, // 2950504179
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertGridCellToPython, "ConvertGridCellToPython" }, // 2668607258
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertPerformanceDataToPython, "ConvertPerformanceDataToPython" }, // 85529337
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ConvertStreamingDataToPython, "ConvertStreamingDataToPython" }, // 2838568141
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnableErrorHandling, "EnableErrorHandling" }, // 3446716319
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_EnablePythonLogging, "EnablePythonLogging" }, // 3191820135
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunction, "ExecutePythonFunction" }, // 1394009095
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonFunctionAsync, "ExecutePythonFunctionAsync" }, // 440404431
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExecutePythonScript, "ExecutePythonScript" }, // 2492163796
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeDebugAPIs, "ExposeDebugAPIs" }, // 1628316739
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeGridSystemAPIs, "ExposeGridSystemAPIs" }, // 850131681
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposePerformanceAPIs, "ExposePerformanceAPIs" }, // 1032079002
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeStreamingAPIs, "ExposeStreamingAPIs" }, // 1437718217
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ExposeWorldPartitionAPIs, "ExposeWorldPartitionAPIs" }, // 3138602473
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetConfiguration, "GetConfiguration" }, // 1920505006
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetInstance, "GetInstance" }, // 3394863494
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetLoadedModules, "GetLoadedModules" }, // 2486751998
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonErrors, "GetPythonErrors" }, // 2575288152
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetPythonLogs, "GetPythonLogs" }, // 1158327382
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_GetRegisteredCallbacks, "GetRegisteredCallbacks" }, // 2821083913
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Initialize, "Initialize" }, // 1749057845
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_InitializePythonEnvironment, "InitializePythonEnvironment" }, // 106942539
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsErrorHandlingEnabled, "IsErrorHandlingEnabled" }, // 3789453200
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsInitialized, "IsInitialized" }, // 4218071134
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonEnvironmentReady, "IsPythonEnvironmentReady" }, // 3489292970
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonExecutionInProgress, "IsPythonExecutionInProgress" }, // 1205434958
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_IsPythonLoggingEnabled, "IsPythonLoggingEnabled" }, // 4285069454
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_LoadPythonModule, "LoadPythonModule" }, // 890135287
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature, "OnPythonCallback__DelegateSignature" }, // 579631247
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature, "OnPythonError__DelegateSignature" }, // 950413552
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature, "OnPythonExecutionComplete__DelegateSignature" }, // 1348574942
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_RegisterPythonCallback, "RegisterPythonCallback" }, // 120975214
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ReloadPythonModule, "ReloadPythonModule" }, // 2528597684
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_SetConfiguration, "SetConfiguration" }, // 2807425358
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_Shutdown, "Shutdown" }, // 2644203277
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_ShutdownPythonEnvironment, "ShutdownPythonEnvironment" }, // 477032182
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_TriggerPythonCallback, "TriggerPythonCallback" }, // 2575768306
		{ &Z_Construct_UFunction_UAuracronWorldPartitionPythonBridge_UnregisterPythonCallback, "UnregisterPythonCallback" }, // 163959925
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionPythonBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonExecutionComplete = { "OnPythonExecutionComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPythonBridge, OnPythonExecutionComplete), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonExecutionComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPythonExecutionComplete_MetaData), NewProp_OnPythonExecutionComplete_MetaData) }; // 1348574942
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonError = { "OnPythonError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPythonBridge, OnPythonError), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPythonError_MetaData), NewProp_OnPythonError_MetaData) }; // 950413552
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonCallback = { "OnPythonCallback", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPythonBridge, OnPythonCallback), Z_Construct_UDelegateFunction_UAuracronWorldPartitionPythonBridge_OnPythonCallback__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPythonCallback_MetaData), NewProp_OnPythonCallback_MetaData) }; // 579631247
void Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionPythonBridge*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionPythonBridge), &Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionPythonBridge, Configuration), Z_Construct_UScriptStruct_FAuracronPythonConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2876235695
void Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bPythonEnvironmentReady_SetBit(void* Obj)
{
	((UAuracronWorldPartitionPythonBridge*)Obj)->bPythonEnvironmentReady = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bPythonEnvironmentReady = { "bPythonEnvironmentReady", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionPythonBridge), &Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bPythonEnvironmentReady_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPythonEnvironmentReady_MetaData), NewProp_bPythonEnvironmentReady_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonExecutionComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_OnPythonCallback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::NewProp_bPythonEnvironmentReady,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::ClassParams = {
	&UAuracronWorldPartitionPythonBridge::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionPythonBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionPythonBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge.OuterSingleton;
}
UAuracronWorldPartitionPythonBridge::UAuracronWorldPartitionPythonBridge(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionPythonBridge);
UAuracronWorldPartitionPythonBridge::~UAuracronWorldPartitionPythonBridge() {}
// ********** End Class UAuracronWorldPartitionPythonBridge ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPythonCallbackType_StaticEnum, TEXT("EAuracronPythonCallbackType"), &Z_Registration_Info_UEnum_EAuracronPythonCallbackType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 787678367U) },
		{ EAuracronPythonExecutionMode_StaticEnum, TEXT("EAuracronPythonExecutionMode"), &Z_Registration_Info_UEnum_EAuracronPythonExecutionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 602965309U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPythonConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonConfiguration_Statics::NewStructOps, TEXT("AuracronPythonConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronPythonConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonConfiguration), 2876235695U) },
		{ FAuracronPythonCallbackData::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonCallbackData_Statics::NewStructOps, TEXT("AuracronPythonCallbackData"), &Z_Registration_Info_UScriptStruct_FAuracronPythonCallbackData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonCallbackData), 1012494777U) },
		{ FAuracronPythonExecutionResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPythonExecutionResult_Statics::NewStructOps, TEXT("AuracronPythonExecutionResult"), &Z_Registration_Info_UScriptStruct_FAuracronPythonExecutionResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPythonExecutionResult), 3018705873U) },
		{ FAuracronStreamingData::StaticStruct, Z_Construct_UScriptStruct_FAuracronStreamingData_Statics::NewStructOps, TEXT("AuracronStreamingData"), &Z_Registration_Info_UScriptStruct_FAuracronStreamingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStreamingData), 4018811434U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionPythonBridge, UAuracronWorldPartitionPythonBridge::StaticClass, TEXT("UAuracronWorldPartitionPythonBridge"), &Z_Registration_Info_UClass_UAuracronWorldPartitionPythonBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionPythonBridge), 1146390106U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_2190451821(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionPython_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
