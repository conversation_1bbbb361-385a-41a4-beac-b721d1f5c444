// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronLoreBridge_init() {}
	AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature();
	AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature();
	AURACRONLOREBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronLoreBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronLoreBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronLoreBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnCollectionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreDiscovered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronLoreBridge_OnLoreRead__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronLoreBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x49430E88,
				0xCB6401D6,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronLoreBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronLoreBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronLoreBridge(Z_Construct_UPackage__Script_AuracronLoreBridge, TEXT("/Script/AuracronLoreBridge"), Z_Registration_Info_UPackage__Script_AuracronLoreBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x49430E88, 0xCB6401D6));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
