// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGLODSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGLODSystem_generated_h
#error "AuracronPCGLODSystem.generated.h already included, missing '#pragma once' in AuracronPCGLODSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGLODSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UHierarchicalInstancedStaticMeshComponent;
class UInstancedStaticMeshComponent;
class UPrimitiveComponent;
class UStaticMesh;
class UWorld;
enum class EAuracronPCGLODGenerationMode : uint8;
enum class EAuracronPCGMeshSimplificationAlgorithm : uint8;
struct FAuracronPCGCullingDescriptor;
struct FAuracronPCGInstancingDescriptor;
struct FAuracronPCGLODGenerationDescriptor;

// ********** Begin ScriptStruct FAuracronPCGLODGenerationDescriptor *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_114_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGLODGenerationDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGLODGenerationDescriptor;
// ********** End ScriptStruct FAuracronPCGLODGenerationDescriptor *********************************

// ********** Begin ScriptStruct FAuracronPCGCullingDescriptor *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_203_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCullingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCullingDescriptor;
// ********** End ScriptStruct FAuracronPCGCullingDescriptor ***************************************

// ********** Begin ScriptStruct FAuracronPCGInstancingDescriptor **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_273_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGInstancingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGInstancingDescriptor;
// ********** End ScriptStruct FAuracronPCGInstancingDescriptor ************************************

// ********** Begin ScriptStruct FAuracronPCGPerformanceProfilingDescriptor ************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_355_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPerformanceProfilingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPerformanceProfilingDescriptor;
// ********** End ScriptStruct FAuracronPCGPerformanceProfilingDescriptor **************************

// ********** Begin Class UAuracronPCGLODGeneratorSettings *****************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_436_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLODGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLODGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLODGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLODGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_436_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLODGeneratorSettings(UAuracronPCGLODGeneratorSettings&&) = delete; \
	UAuracronPCGLODGeneratorSettings(const UAuracronPCGLODGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLODGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLODGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGLODGeneratorSettings) \
	NO_API virtual ~UAuracronPCGLODGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_433_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_436_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_436_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_436_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLODGeneratorSettings;

// ********** End Class UAuracronPCGLODGeneratorSettings *******************************************

// ********** Begin Class UAuracronPCGDistanceBasedCullerSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_492_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDistanceBasedCullerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDistanceBasedCullerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDistanceBasedCullerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDistanceBasedCullerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_492_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDistanceBasedCullerSettings(UAuracronPCGDistanceBasedCullerSettings&&) = delete; \
	UAuracronPCGDistanceBasedCullerSettings(const UAuracronPCGDistanceBasedCullerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDistanceBasedCullerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDistanceBasedCullerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGDistanceBasedCullerSettings) \
	NO_API virtual ~UAuracronPCGDistanceBasedCullerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_489_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_492_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_492_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_492_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDistanceBasedCullerSettings;

// ********** End Class UAuracronPCGDistanceBasedCullerSettings ************************************

// ********** Begin Class UAuracronPCGInstancingOptimizerSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_558_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGInstancingOptimizerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGInstancingOptimizerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGInstancingOptimizerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGInstancingOptimizerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_558_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGInstancingOptimizerSettings(UAuracronPCGInstancingOptimizerSettings&&) = delete; \
	UAuracronPCGInstancingOptimizerSettings(const UAuracronPCGInstancingOptimizerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGInstancingOptimizerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGInstancingOptimizerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGInstancingOptimizerSettings) \
	NO_API virtual ~UAuracronPCGInstancingOptimizerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_555_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_558_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_558_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_558_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGInstancingOptimizerSettings;

// ********** End Class UAuracronPCGInstancingOptimizerSettings ************************************

// ********** Begin Class UAuracronPCGPerformanceProfilerSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_633_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPerformanceProfilerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPerformanceProfilerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPerformanceProfilerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPerformanceProfilerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_633_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPerformanceProfilerSettings(UAuracronPCGPerformanceProfilerSettings&&) = delete; \
	UAuracronPCGPerformanceProfilerSettings(const UAuracronPCGPerformanceProfilerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPerformanceProfilerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPerformanceProfilerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGPerformanceProfilerSettings) \
	NO_API virtual ~UAuracronPCGPerformanceProfilerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_630_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_633_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_633_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_633_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPerformanceProfilerSettings;

// ********** End Class UAuracronPCGPerformanceProfilerSettings ************************************

// ********** Begin Class UAuracronPCGMeshSimplifierSettings ***************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_705_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMeshSimplifierSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMeshSimplifierSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMeshSimplifierSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMeshSimplifierSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_705_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMeshSimplifierSettings(UAuracronPCGMeshSimplifierSettings&&) = delete; \
	UAuracronPCGMeshSimplifierSettings(const UAuracronPCGMeshSimplifierSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMeshSimplifierSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMeshSimplifierSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGMeshSimplifierSettings) \
	NO_API virtual ~UAuracronPCGMeshSimplifierSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_702_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_705_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_705_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_705_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMeshSimplifierSettings;

// ********** End Class UAuracronPCGMeshSimplifierSettings *****************************************

// ********** Begin Class UAuracronPCGLODSystemUtils ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateDefaultLODDescriptor); \
	DECLARE_FUNCTION(execValidateInstancingDescriptor); \
	DECLARE_FUNCTION(execValidateCullingDescriptor); \
	DECLARE_FUNCTION(execValidateLODGenerationDescriptor); \
	DECLARE_FUNCTION(execCalculatePerformanceScore); \
	DECLARE_FUNCTION(execCalculateMemoryUsage); \
	DECLARE_FUNCTION(execCountTriangles); \
	DECLARE_FUNCTION(execCountDrawCalls); \
	DECLARE_FUNCTION(execMeasureRenderTime); \
	DECLARE_FUNCTION(execRemoveDuplicateInstances); \
	DECLARE_FUNCTION(execOptimizeInstanceTransforms); \
	DECLARE_FUNCTION(execCreateHierarchicalInstancedComponent); \
	DECLARE_FUNCTION(execCreateOptimizedInstancedComponent); \
	DECLARE_FUNCTION(execCalculateOcclusionFactor); \
	DECLARE_FUNCTION(execIsInFrustum); \
	DECLARE_FUNCTION(execPerformBatchCulling); \
	DECLARE_FUNCTION(execShouldCullInstance); \
	DECLARE_FUNCTION(execCalculateScreenSize); \
	DECLARE_FUNCTION(execCalculateOptimalLODLevel); \
	DECLARE_FUNCTION(execSimplifyMesh); \
	DECLARE_FUNCTION(execGenerateLODChain);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGLODSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGLODSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLODSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGLODSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGLODSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGLODSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGLODSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGLODSystemUtils(UAuracronPCGLODSystemUtils&&) = delete; \
	UAuracronPCGLODSystemUtils(const UAuracronPCGLODSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGLODSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGLODSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGLODSystemUtils) \
	NO_API virtual ~UAuracronPCGLODSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_775_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h_778_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGLODSystemUtils;

// ********** End Class UAuracronPCGLODSystemUtils *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGLODSystem_h

// ********** Begin Enum EAuracronPCGLODGenerationMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGLODGENERATIONMODE(op) \
	op(EAuracronPCGLODGenerationMode::Automatic) \
	op(EAuracronPCGLODGenerationMode::DistanceBased) \
	op(EAuracronPCGLODGenerationMode::ScreenSize) \
	op(EAuracronPCGLODGenerationMode::VertexCount) \
	op(EAuracronPCGLODGenerationMode::TriangleCount) \
	op(EAuracronPCGLODGenerationMode::Custom) \
	op(EAuracronPCGLODGenerationMode::Nanite) \
	op(EAuracronPCGLODGenerationMode::HLOD) \
	op(EAuracronPCGLODGenerationMode::Impostor) 

enum class EAuracronPCGLODGenerationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGLODGenerationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGLODGenerationMode>();
// ********** End Enum EAuracronPCGLODGenerationMode ***********************************************

// ********** Begin Enum EAuracronPCGCullingMode ***************************************************
#define FOREACH_ENUM_EAURACRONPCGCULLINGMODE(op) \
	op(EAuracronPCGCullingMode::None) \
	op(EAuracronPCGCullingMode::Distance) \
	op(EAuracronPCGCullingMode::Frustum) \
	op(EAuracronPCGCullingMode::Occlusion) \
	op(EAuracronPCGCullingMode::ScreenSize) \
	op(EAuracronPCGCullingMode::Combined) \
	op(EAuracronPCGCullingMode::Custom) 

enum class EAuracronPCGCullingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCullingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCullingMode>();
// ********** End Enum EAuracronPCGCullingMode *****************************************************

// ********** Begin Enum EAuracronPCGInstancingMode ************************************************
#define FOREACH_ENUM_EAURACRONPCGINSTANCINGMODE(op) \
	op(EAuracronPCGInstancingMode::None) \
	op(EAuracronPCGInstancingMode::Static) \
	op(EAuracronPCGInstancingMode::Hierarchical) \
	op(EAuracronPCGInstancingMode::Clustered) \
	op(EAuracronPCGInstancingMode::GPU) \
	op(EAuracronPCGInstancingMode::Nanite) \
	op(EAuracronPCGInstancingMode::Custom) 

enum class EAuracronPCGInstancingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGInstancingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGInstancingMode>();
// ********** End Enum EAuracronPCGInstancingMode **************************************************

// ********** Begin Enum EAuracronPCGPerformanceMetric *********************************************
#define FOREACH_ENUM_EAURACRONPCGPERFORMANCEMETRIC(op) \
	op(EAuracronPCGPerformanceMetric::RenderTime) \
	op(EAuracronPCGPerformanceMetric::DrawCalls) \
	op(EAuracronPCGPerformanceMetric::TriangleCount) \
	op(EAuracronPCGPerformanceMetric::VertexCount) \
	op(EAuracronPCGPerformanceMetric::MemoryUsage) \
	op(EAuracronPCGPerformanceMetric::InstanceCount) \
	op(EAuracronPCGPerformanceMetric::CulledInstances) \
	op(EAuracronPCGPerformanceMetric::LODLevel) \
	op(EAuracronPCGPerformanceMetric::ScreenSize) \
	op(EAuracronPCGPerformanceMetric::Custom) 

enum class EAuracronPCGPerformanceMetric : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPerformanceMetric> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPerformanceMetric>();
// ********** End Enum EAuracronPCGPerformanceMetric ***********************************************

// ********** Begin Enum EAuracronPCGMeshSimplificationAlgorithm ***********************************
#define FOREACH_ENUM_EAURACRONPCGMESHSIMPLIFICATIONALGORITHM(op) \
	op(EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics) \
	op(EAuracronPCGMeshSimplificationAlgorithm::EdgeCollapse) \
	op(EAuracronPCGMeshSimplificationAlgorithm::VertexClustering) \
	op(EAuracronPCGMeshSimplificationAlgorithm::Progressive) \
	op(EAuracronPCGMeshSimplificationAlgorithm::Adaptive) \
	op(EAuracronPCGMeshSimplificationAlgorithm::Custom) 

enum class EAuracronPCGMeshSimplificationAlgorithm : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMeshSimplificationAlgorithm> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMeshSimplificationAlgorithm>();
// ********** End Enum EAuracronPCGMeshSimplificationAlgorithm *************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
