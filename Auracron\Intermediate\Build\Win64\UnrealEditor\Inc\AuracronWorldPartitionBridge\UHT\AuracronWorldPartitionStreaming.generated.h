// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionStreaming.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionStreaming_generated_h
#error "AuracronWorldPartitionStreaming.generated.h already included, missing '#pragma once' in AuracronWorldPartitionStreaming.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionStreaming_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionStreamingManager;
class UWorld;
enum class EAuracronStreamingPriority : uint8;
enum class EAuracronStreamingRequestType : uint8;
struct FAuracronStreamingBridgeStatistics;
struct FAuracronStreamingConfiguration;
struct FAuracronStreamingRequest;
struct FAuracronStreamingSource;

// ********** Begin ScriptStruct FAuracronStreamingConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_95_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingConfiguration;
// ********** End ScriptStruct FAuracronStreamingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronStreamingRequest *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_181_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingRequest_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingRequest;
// ********** End ScriptStruct FAuracronStreamingRequest *******************************************

// ********** Begin ScriptStruct FAuracronStreamingSource ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_249_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingSource_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingSource;
// ********** End ScriptStruct FAuracronStreamingSource ********************************************

// ********** Begin ScriptStruct FAuracronStreamingBridgeStatistics ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_308_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingBridgeStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingBridgeStatistics;
// ********** End ScriptStruct FAuracronStreamingBridgeStatistics **********************************

// ********** Begin Delegate FOnCellStreamingStarted ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_525_DELEGATE \
static void FOnCellStreamingStarted_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingStarted, const FString& CellId, EAuracronStreamingRequestType RequestType);


// ********** End Delegate FOnCellStreamingStarted *************************************************

// ********** Begin Delegate FOnCellStreamingCompleted *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_526_DELEGATE \
static void FOnCellStreamingCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingCompleted, const FString& CellId, EAuracronStreamingRequestType RequestType, float ProcessingTime);


// ********** End Delegate FOnCellStreamingCompleted ***********************************************

// ********** Begin Delegate FOnCellStreamingFailed ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_527_DELEGATE \
static void FOnCellStreamingFailed_DelegateWrapper(const FMulticastScriptDelegate& OnCellStreamingFailed, const FString& CellId, EAuracronStreamingRequestType RequestType, const FString& ErrorMessage);


// ********** End Delegate FOnCellStreamingFailed **************************************************

// ********** Begin Delegate FOnMemoryPressureChanged **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_528_DELEGATE \
static void FOnMemoryPressureChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMemoryPressureChanged, float MemoryPressure);


// ********** End Delegate FOnMemoryPressureChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionStreamingManager **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogStreamingState); \
	DECLARE_FUNCTION(execDrawDebugStreamingInfo); \
	DECLARE_FUNCTION(execIsStreamingDebugEnabled); \
	DECLARE_FUNCTION(execEnableStreamingDebug); \
	DECLARE_FUNCTION(execGetStreamingEfficiency); \
	DECLARE_FUNCTION(execGetActiveRequestCount); \
	DECLARE_FUNCTION(execGetPendingRequestCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetStreamingStatistics); \
	DECLARE_FUNCTION(execGetStreamingDistance); \
	DECLARE_FUNCTION(execSetStreamingDistance); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetRequestsByPriority); \
	DECLARE_FUNCTION(execSetRequestPriority); \
	DECLARE_FUNCTION(execUpdateRequestPriorities); \
	DECLARE_FUNCTION(execClearCompletedRequests); \
	DECLARE_FUNCTION(execGetActiveRequests); \
	DECLARE_FUNCTION(execGetPendingRequests); \
	DECLARE_FUNCTION(execGetStreamingRequest); \
	DECLARE_FUNCTION(execGetMemoryPressure); \
	DECLARE_FUNCTION(execGetCurrentMemoryUsage); \
	DECLARE_FUNCTION(execForceMemoryCleanup); \
	DECLARE_FUNCTION(execIsMemoryPressureHigh); \
	DECLARE_FUNCTION(execUpdateMemoryManagement); \
	DECLARE_FUNCTION(execCalculateCellPriority); \
	DECLARE_FUNCTION(execGetCellsInStreamingRange); \
	DECLARE_FUNCTION(execUpdateDistanceBasedStreaming); \
	DECLARE_FUNCTION(execUpdateStreamingSourcesFromWorld); \
	DECLARE_FUNCTION(execGetActiveStreamingSources); \
	DECLARE_FUNCTION(execUpdateStreamingSource); \
	DECLARE_FUNCTION(execRemoveStreamingSource); \
	DECLARE_FUNCTION(execAddStreamingSource); \
	DECLARE_FUNCTION(execProcessStreamingRequests); \
	DECLARE_FUNCTION(execCancelStreamingRequest); \
	DECLARE_FUNCTION(execRequestCellPreloading); \
	DECLARE_FUNCTION(execRequestCellUnloading); \
	DECLARE_FUNCTION(execRequestCellLoading); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionStreamingManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionStreamingManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionStreamingManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionStreamingManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionStreamingManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionStreamingManager(UAuracronWorldPartitionStreamingManager&&) = delete; \
	UAuracronWorldPartitionStreamingManager(const UAuracronWorldPartitionStreamingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionStreamingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionStreamingManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionStreamingManager) \
	NO_API virtual ~UAuracronWorldPartitionStreamingManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_378_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h_381_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionStreamingManager;

// ********** End Class UAuracronWorldPartitionStreamingManager ************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionStreaming_h

// ********** Begin Enum EAuracronStreamingPriority ************************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGPRIORITY(op) \
	op(EAuracronStreamingPriority::Lowest) \
	op(EAuracronStreamingPriority::Low) \
	op(EAuracronStreamingPriority::Normal) \
	op(EAuracronStreamingPriority::High) \
	op(EAuracronStreamingPriority::Highest) \
	op(EAuracronStreamingPriority::Critical) 

enum class EAuracronStreamingPriority : uint8;
template<> struct TIsUEnumClass<EAuracronStreamingPriority> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingPriority>();
// ********** End Enum EAuracronStreamingPriority **************************************************

// ********** Begin Enum EAuracronStreamingRequestType *********************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGREQUESTTYPE(op) \
	op(EAuracronStreamingRequestType::Load) \
	op(EAuracronStreamingRequestType::Unload) \
	op(EAuracronStreamingRequestType::Preload) \
	op(EAuracronStreamingRequestType::ForceLoad) \
	op(EAuracronStreamingRequestType::ForceUnload) 

enum class EAuracronStreamingRequestType : uint8;
template<> struct TIsUEnumClass<EAuracronStreamingRequestType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingRequestType>();
// ********** End Enum EAuracronStreamingRequestType ***********************************************

// ********** Begin Enum EAuracronStreamingRequestState ********************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGREQUESTSTATE(op) \
	op(EAuracronStreamingRequestState::Pending) \
	op(EAuracronStreamingRequestState::Processing) \
	op(EAuracronStreamingRequestState::Completed) \
	op(EAuracronStreamingRequestState::Failed) \
	op(EAuracronStreamingRequestState::Cancelled) \
	op(EAuracronStreamingRequestState::Timeout) 

enum class EAuracronStreamingRequestState : uint8;
template<> struct TIsUEnumClass<EAuracronStreamingRequestState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingRequestState>();
// ********** End Enum EAuracronStreamingRequestState **********************************************

// ********** Begin Enum EAuracronMemoryManagementStrategy *****************************************
#define FOREACH_ENUM_EAURACRONMEMORYMANAGEMENTSTRATEGY(op) \
	op(EAuracronMemoryManagementStrategy::Conservative) \
	op(EAuracronMemoryManagementStrategy::Balanced) \
	op(EAuracronMemoryManagementStrategy::Aggressive) \
	op(EAuracronMemoryManagementStrategy::Custom) 

enum class EAuracronMemoryManagementStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronMemoryManagementStrategy> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronMemoryManagementStrategy>();
// ********** End Enum EAuracronMemoryManagementStrategy *******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
