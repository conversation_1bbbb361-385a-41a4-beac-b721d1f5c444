// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageCollision.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageCollision() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionMeshData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTramplingEffectData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionChannel();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronFoliageCollisionType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageCollisionType;
static UEnum* EAuracronFoliageCollisionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageCollisionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageCollisionType>()
{
	return EAuracronFoliageCollisionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CollisionEnabled.DisplayName", "Collision Enabled" },
		{ "CollisionEnabled.Name", "EAuracronFoliageCollisionType::CollisionEnabled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision types for foliage\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageCollisionType::Custom" },
		{ "Destructible.DisplayName", "Destructible" },
		{ "Destructible.Name", "EAuracronFoliageCollisionType::Destructible" },
		{ "Interactive.DisplayName", "Interactive" },
		{ "Interactive.Name", "EAuracronFoliageCollisionType::Interactive" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
		{ "NoCollision.DisplayName", "No Collision" },
		{ "NoCollision.Name", "EAuracronFoliageCollisionType::NoCollision" },
		{ "PhysicsOnly.DisplayName", "Physics Only" },
		{ "PhysicsOnly.Name", "EAuracronFoliageCollisionType::PhysicsOnly" },
		{ "QueryOnly.DisplayName", "Query Only" },
		{ "QueryOnly.Name", "EAuracronFoliageCollisionType::QueryOnly" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision types for foliage" },
#endif
		{ "Trampling.DisplayName", "Trampling" },
		{ "Trampling.Name", "EAuracronFoliageCollisionType::Trampling" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageCollisionType::NoCollision", (int64)EAuracronFoliageCollisionType::NoCollision },
		{ "EAuracronFoliageCollisionType::QueryOnly", (int64)EAuracronFoliageCollisionType::QueryOnly },
		{ "EAuracronFoliageCollisionType::PhysicsOnly", (int64)EAuracronFoliageCollisionType::PhysicsOnly },
		{ "EAuracronFoliageCollisionType::CollisionEnabled", (int64)EAuracronFoliageCollisionType::CollisionEnabled },
		{ "EAuracronFoliageCollisionType::Destructible", (int64)EAuracronFoliageCollisionType::Destructible },
		{ "EAuracronFoliageCollisionType::Interactive", (int64)EAuracronFoliageCollisionType::Interactive },
		{ "EAuracronFoliageCollisionType::Trampling", (int64)EAuracronFoliageCollisionType::Trampling },
		{ "EAuracronFoliageCollisionType::Custom", (int64)EAuracronFoliageCollisionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageCollisionType",
	"EAuracronFoliageCollisionType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageCollisionType.InnerSingleton;
}
// ********** End Enum EAuracronFoliageCollisionType ***********************************************

// ********** Begin Enum EAuracronPhysicsInteractionType *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType;
static UEnum* EAuracronPhysicsInteractionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronPhysicsInteractionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsInteractionType>()
{
	return EAuracronPhysicsInteractionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics interaction types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPhysicsInteractionType::Custom" },
		{ "Destructible.DisplayName", "Destructible" },
		{ "Destructible.Name", "EAuracronPhysicsInteractionType::Destructible" },
		{ "Kinematic.DisplayName", "Kinematic" },
		{ "Kinematic.Name", "EAuracronPhysicsInteractionType::Kinematic" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
		{ "Simulated.DisplayName", "Simulated Physics" },
		{ "Simulated.Name", "EAuracronPhysicsInteractionType::Simulated" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronPhysicsInteractionType::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics interaction types" },
#endif
		{ "Trampling.DisplayName", "Trampling Effect" },
		{ "Trampling.Name", "EAuracronPhysicsInteractionType::Trampling" },
		{ "WindInteraction.DisplayName", "Wind Interaction" },
		{ "WindInteraction.Name", "EAuracronPhysicsInteractionType::WindInteraction" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPhysicsInteractionType::Static", (int64)EAuracronPhysicsInteractionType::Static },
		{ "EAuracronPhysicsInteractionType::Kinematic", (int64)EAuracronPhysicsInteractionType::Kinematic },
		{ "EAuracronPhysicsInteractionType::Simulated", (int64)EAuracronPhysicsInteractionType::Simulated },
		{ "EAuracronPhysicsInteractionType::Destructible", (int64)EAuracronPhysicsInteractionType::Destructible },
		{ "EAuracronPhysicsInteractionType::Trampling", (int64)EAuracronPhysicsInteractionType::Trampling },
		{ "EAuracronPhysicsInteractionType::WindInteraction", (int64)EAuracronPhysicsInteractionType::WindInteraction },
		{ "EAuracronPhysicsInteractionType::Custom", (int64)EAuracronPhysicsInteractionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronPhysicsInteractionType",
	"EAuracronPhysicsInteractionType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType.InnerSingleton;
}
// ********** End Enum EAuracronPhysicsInteractionType *********************************************

// ********** Begin Enum EAuracronDestructibleBehavior *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDestructibleBehavior;
static UEnum* EAuracronDestructibleBehavior_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronDestructibleBehavior"));
	}
	return Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronDestructibleBehavior>()
{
	return EAuracronDestructibleBehavior_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Break.DisplayName", "Break" },
		{ "Break.Name", "EAuracronDestructibleBehavior::Break" },
		{ "Burn.DisplayName", "Burn" },
		{ "Burn.Name", "EAuracronDestructibleBehavior::Burn" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Destructible behavior types\n" },
#endif
		{ "Crumble.DisplayName", "Crumble" },
		{ "Crumble.Name", "EAuracronDestructibleBehavior::Crumble" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronDestructibleBehavior::Custom" },
		{ "Dissolve.DisplayName", "Dissolve" },
		{ "Dissolve.Name", "EAuracronDestructibleBehavior::Dissolve" },
		{ "Explode.DisplayName", "Explode" },
		{ "Explode.Name", "EAuracronDestructibleBehavior::Explode" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronDestructibleBehavior::None" },
		{ "Shatter.DisplayName", "Shatter" },
		{ "Shatter.Name", "EAuracronDestructibleBehavior::Shatter" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destructible behavior types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDestructibleBehavior::None", (int64)EAuracronDestructibleBehavior::None },
		{ "EAuracronDestructibleBehavior::Break", (int64)EAuracronDestructibleBehavior::Break },
		{ "EAuracronDestructibleBehavior::Shatter", (int64)EAuracronDestructibleBehavior::Shatter },
		{ "EAuracronDestructibleBehavior::Crumble", (int64)EAuracronDestructibleBehavior::Crumble },
		{ "EAuracronDestructibleBehavior::Burn", (int64)EAuracronDestructibleBehavior::Burn },
		{ "EAuracronDestructibleBehavior::Dissolve", (int64)EAuracronDestructibleBehavior::Dissolve },
		{ "EAuracronDestructibleBehavior::Explode", (int64)EAuracronDestructibleBehavior::Explode },
		{ "EAuracronDestructibleBehavior::Custom", (int64)EAuracronDestructibleBehavior::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronDestructibleBehavior",
	"EAuracronDestructibleBehavior",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior()
{
	if (!Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDestructibleBehavior.InnerSingleton;
}
// ********** End Enum EAuracronDestructibleBehavior ***********************************************

// ********** Begin Enum EAuracronTramplingEffect **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTramplingEffect;
static UEnum* EAuracronTramplingEffect_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTramplingEffect.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTramplingEffect.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronTramplingEffect"));
	}
	return Z_Registration_Info_UEnum_EAuracronTramplingEffect.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronTramplingEffect>()
{
	return EAuracronTramplingEffect_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bend.DisplayName", "Bend" },
		{ "Bend.Name", "EAuracronTramplingEffect::Bend" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Trampling effect types\n" },
#endif
		{ "Crush.DisplayName", "Crush" },
		{ "Crush.Name", "EAuracronTramplingEffect::Crush" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronTramplingEffect::Custom" },
		{ "Damage.DisplayName", "Damage" },
		{ "Damage.Name", "EAuracronTramplingEffect::Damage" },
		{ "Destroy.DisplayName", "Destroy" },
		{ "Destroy.Name", "EAuracronTramplingEffect::Destroy" },
		{ "Flatten.DisplayName", "Flatten" },
		{ "Flatten.Name", "EAuracronTramplingEffect::Flatten" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronTramplingEffect::None" },
		{ "Recover.DisplayName", "Recover Over Time" },
		{ "Recover.Name", "EAuracronTramplingEffect::Recover" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trampling effect types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTramplingEffect::None", (int64)EAuracronTramplingEffect::None },
		{ "EAuracronTramplingEffect::Bend", (int64)EAuracronTramplingEffect::Bend },
		{ "EAuracronTramplingEffect::Flatten", (int64)EAuracronTramplingEffect::Flatten },
		{ "EAuracronTramplingEffect::Crush", (int64)EAuracronTramplingEffect::Crush },
		{ "EAuracronTramplingEffect::Damage", (int64)EAuracronTramplingEffect::Damage },
		{ "EAuracronTramplingEffect::Destroy", (int64)EAuracronTramplingEffect::Destroy },
		{ "EAuracronTramplingEffect::Recover", (int64)EAuracronTramplingEffect::Recover },
		{ "EAuracronTramplingEffect::Custom", (int64)EAuracronTramplingEffect::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronTramplingEffect",
	"EAuracronTramplingEffect",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect()
{
	if (!Z_Registration_Info_UEnum_EAuracronTramplingEffect.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTramplingEffect.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTramplingEffect.InnerSingleton;
}
// ********** End Enum EAuracronTramplingEffect ****************************************************

// ********** Begin ScriptStruct FAuracronFoliageCollisionConfiguration ****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration;
class UScriptStruct* FAuracronFoliageCollisionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageCollisionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Collision Configuration\n * Configuration for foliage collision system behavior\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Collision Configuration\nConfiguration for foliage collision system behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionSystem_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultCollisionType_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPhysicsType_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageCollisionChannel_MetaData[] = {
		{ "Category", "Collision Channels" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerCollisionChannel_MetaData[] = {
		{ "Category", "Collision Channels" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectileCollisionChannel_MetaData[] = {
		{ "Category", "Collision Channels" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePhysicsInteraction_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMass_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLinearDamping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAngularDamping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultRestitution_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFriction_MetaData[] = {
		{ "Category", "Physics" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDestructibleFoliage_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDestructibleBehavior_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionThreshold_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionImpulse_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTramplingEffects_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTramplingEffect_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingRadius_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingForce_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingRecoveryTime_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncCollisionUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCollisionUpdatesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCollisionDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLODBasedCollision_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDisableCollisionForDistantFoliage_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDisableDistance_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableCollisionSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionSystem;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultCollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultCollisionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultPhysicsType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultPhysicsType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FoliageCollisionChannel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlayerCollisionChannel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProjectileCollisionChannel;
	static void NewProp_bEnablePhysicsInteraction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePhysicsInteraction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultMass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultLinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultAngularDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultRestitution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFriction;
	static void NewProp_bEnableDestructibleFoliage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDestructibleFoliage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultDestructibleBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultDestructibleBehavior;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionImpulse;
	static void NewProp_bEnableTramplingEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTramplingEffects;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultTramplingEffect_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultTramplingEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingRecoveryTime;
	static void NewProp_bEnableAsyncCollisionUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncCollisionUpdates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCollisionUpdatesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxCollisionDistance;
	static void NewProp_bUseLODBasedCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLODBasedCollision;
	static void NewProp_bDisableCollisionForDistantFoliage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableCollisionForDistantFoliage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionDisableDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageCollisionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableCollisionSystem_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bEnableCollisionSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableCollisionSystem = { "bEnableCollisionSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableCollisionSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionSystem_MetaData), NewProp_bEnableCollisionSystem_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultCollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultCollisionType = { "DefaultCollisionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultCollisionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultCollisionType_MetaData), NewProp_DefaultCollisionType_MetaData) }; // 1409904130
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultPhysicsType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultPhysicsType = { "DefaultPhysicsType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultPhysicsType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPhysicsType_MetaData), NewProp_DefaultPhysicsType_MetaData) }; // 368324703
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_FoliageCollisionChannel = { "FoliageCollisionChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, FoliageCollisionChannel), Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageCollisionChannel_MetaData), NewProp_FoliageCollisionChannel_MetaData) }; // 756624936
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_PlayerCollisionChannel = { "PlayerCollisionChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, PlayerCollisionChannel), Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerCollisionChannel_MetaData), NewProp_PlayerCollisionChannel_MetaData) }; // 756624936
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_ProjectileCollisionChannel = { "ProjectileCollisionChannel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, ProjectileCollisionChannel), Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectileCollisionChannel_MetaData), NewProp_ProjectileCollisionChannel_MetaData) }; // 756624936
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnablePhysicsInteraction_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bEnablePhysicsInteraction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnablePhysicsInteraction = { "bEnablePhysicsInteraction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnablePhysicsInteraction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePhysicsInteraction_MetaData), NewProp_bEnablePhysicsInteraction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultMass = { "DefaultMass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultMass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMass_MetaData), NewProp_DefaultMass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultLinearDamping = { "DefaultLinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultLinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLinearDamping_MetaData), NewProp_DefaultLinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultAngularDamping = { "DefaultAngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultAngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAngularDamping_MetaData), NewProp_DefaultAngularDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultRestitution = { "DefaultRestitution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultRestitution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultRestitution_MetaData), NewProp_DefaultRestitution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultFriction = { "DefaultFriction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultFriction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFriction_MetaData), NewProp_DefaultFriction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableDestructibleFoliage_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bEnableDestructibleFoliage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableDestructibleFoliage = { "bEnableDestructibleFoliage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableDestructibleFoliage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDestructibleFoliage_MetaData), NewProp_bEnableDestructibleFoliage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultDestructibleBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultDestructibleBehavior = { "DefaultDestructibleBehavior", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultDestructibleBehavior), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDestructibleBehavior_MetaData), NewProp_DefaultDestructibleBehavior_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DestructionThreshold = { "DestructionThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DestructionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionThreshold_MetaData), NewProp_DestructionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DestructionImpulse = { "DestructionImpulse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DestructionImpulse), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionImpulse_MetaData), NewProp_DestructionImpulse_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableTramplingEffects_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bEnableTramplingEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableTramplingEffects = { "bEnableTramplingEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableTramplingEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTramplingEffects_MetaData), NewProp_bEnableTramplingEffects_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultTramplingEffect_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultTramplingEffect = { "DefaultTramplingEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, DefaultTramplingEffect), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTramplingEffect_MetaData), NewProp_DefaultTramplingEffect_MetaData) }; // 2136187517
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingRadius = { "TramplingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, TramplingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingRadius_MetaData), NewProp_TramplingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingForce = { "TramplingForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, TramplingForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingForce_MetaData), NewProp_TramplingForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingRecoveryTime = { "TramplingRecoveryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, TramplingRecoveryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingRecoveryTime_MetaData), NewProp_TramplingRecoveryTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionUpdates_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bEnableAsyncCollisionUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionUpdates = { "bEnableAsyncCollisionUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncCollisionUpdates_MetaData), NewProp_bEnableAsyncCollisionUpdates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_MaxCollisionUpdatesPerFrame = { "MaxCollisionUpdatesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, MaxCollisionUpdatesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCollisionUpdatesPerFrame_MetaData), NewProp_MaxCollisionUpdatesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_CollisionUpdateInterval = { "CollisionUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, CollisionUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionUpdateInterval_MetaData), NewProp_CollisionUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_MaxCollisionDistance = { "MaxCollisionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, MaxCollisionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCollisionDistance_MetaData), NewProp_MaxCollisionDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bUseLODBasedCollision_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bUseLODBasedCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bUseLODBasedCollision = { "bUseLODBasedCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bUseLODBasedCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLODBasedCollision_MetaData), NewProp_bUseLODBasedCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bDisableCollisionForDistantFoliage_SetBit(void* Obj)
{
	((FAuracronFoliageCollisionConfiguration*)Obj)->bDisableCollisionForDistantFoliage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bDisableCollisionForDistantFoliage = { "bDisableCollisionForDistantFoliage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageCollisionConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bDisableCollisionForDistantFoliage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDisableCollisionForDistantFoliage_MetaData), NewProp_bDisableCollisionForDistantFoliage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_CollisionDisableDistance = { "CollisionDisableDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageCollisionConfiguration, CollisionDisableDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDisableDistance_MetaData), NewProp_CollisionDisableDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableCollisionSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultCollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultCollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultPhysicsType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultPhysicsType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_FoliageCollisionChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_PlayerCollisionChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_ProjectileCollisionChannel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnablePhysicsInteraction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultMass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultLinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultAngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultRestitution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultFriction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableDestructibleFoliage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultDestructibleBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultDestructibleBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DestructionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DestructionImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableTramplingEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultTramplingEffect_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_DefaultTramplingEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_TramplingRecoveryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bEnableAsyncCollisionUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_MaxCollisionUpdatesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_CollisionUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_MaxCollisionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bUseLODBasedCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_bDisableCollisionForDistantFoliage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewProp_CollisionDisableDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageCollisionConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageCollisionConfiguration),
	alignof(FAuracronFoliageCollisionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageCollisionConfiguration ******************************

// ********** Begin ScriptStruct FAuracronCollisionMeshData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData;
class UScriptStruct* FAuracronCollisionMeshData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionMeshData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronCollisionMeshData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Mesh Data\n * Data for collision mesh generation and management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Mesh Data\nData for collision mesh generation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMeshId_MetaData[] = {
		{ "Category", "Collision Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMesh_MetaData[] = {
		{ "Category", "Collision Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMesh_MetaData[] = {
		{ "Category", "Collision Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionType_MetaData[] = {
		{ "Category", "Collision Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComplexity_MetaData[] = {
		{ "Category", "Collision Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCollisionTriangles_MetaData[] = {
		{ "Category", "Collision Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseComplexCollision_MetaData[] = {
		{ "Category", "Collision Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateOverlapEvents_MetaData[] = {
		{ "Category", "Collision Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mass_MetaData[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearDamping_MetaData[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularDamping_MetaData[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Restitution_MetaData[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Friction_MetaData[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerated_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionMeshId;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CollisionMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionComplexity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCollisionTriangles;
	static void NewProp_bUseComplexCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseComplexCollision;
	static void NewProp_bGenerateOverlapEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateOverlapEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Restitution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Friction;
	static void NewProp_bIsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerated;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionMeshData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionMeshId = { "CollisionMeshId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, CollisionMeshId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMeshId_MetaData), NewProp_CollisionMeshId_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMesh_MetaData), NewProp_SourceMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionMesh = { "CollisionMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, CollisionMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMesh_MetaData), NewProp_CollisionMesh_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionType = { "CollisionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, CollisionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionType_MetaData), NewProp_CollisionType_MetaData) }; // 1409904130
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionComplexity = { "CollisionComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, CollisionComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComplexity_MetaData), NewProp_CollisionComplexity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_MaxCollisionTriangles = { "MaxCollisionTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, MaxCollisionTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCollisionTriangles_MetaData), NewProp_MaxCollisionTriangles_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bUseComplexCollision_SetBit(void* Obj)
{
	((FAuracronCollisionMeshData*)Obj)->bUseComplexCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bUseComplexCollision = { "bUseComplexCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionMeshData), &Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bUseComplexCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseComplexCollision_MetaData), NewProp_bUseComplexCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bGenerateOverlapEvents_SetBit(void* Obj)
{
	((FAuracronCollisionMeshData*)Obj)->bGenerateOverlapEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bGenerateOverlapEvents = { "bGenerateOverlapEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionMeshData), &Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bGenerateOverlapEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateOverlapEvents_MetaData), NewProp_bGenerateOverlapEvents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, Mass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mass_MetaData), NewProp_Mass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, LinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearDamping_MetaData), NewProp_LinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, AngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularDamping_MetaData), NewProp_AngularDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Restitution = { "Restitution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, Restitution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Restitution_MetaData), NewProp_Restitution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Friction = { "Friction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, Friction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Friction_MetaData), NewProp_Friction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bIsGenerated_SetBit(void* Obj)
{
	((FAuracronCollisionMeshData*)Obj)->bIsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bIsGenerated = { "bIsGenerated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionMeshData), &Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bIsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerated_MetaData), NewProp_bIsGenerated_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionMeshData, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionMeshId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_CollisionComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_MaxCollisionTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bUseComplexCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bGenerateOverlapEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Mass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_LinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_AngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Restitution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_Friction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_bIsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewProp_GenerationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionMeshData",
	Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::PropPointers),
	sizeof(FAuracronCollisionMeshData),
	alignof(FAuracronCollisionMeshData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionMeshData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionMeshData ******************************************

// ********** Begin ScriptStruct FAuracronDestructibleFoliageData **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData;
class UScriptStruct* FAuracronDestructibleFoliageData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronDestructibleFoliageData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Destructible Foliage Data\n * Data for destructible foliage behavior\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destructible Foliage Data\nData for destructible foliage behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleId_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleBehavior_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionThreshold_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionImpulse_MetaData[] = {
		{ "Category", "Destructible" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentCount_MetaData[] = {
		{ "Category", "Fragments" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentLifetime_MetaData[] = {
		{ "Category", "Fragments" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentSpread_MetaData[] = {
		{ "Category", "Fragments" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionEffects_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionSounds_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRegenerate_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationTime_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationRate_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestroyed_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DestructibleBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DestructibleBehavior;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionImpulse;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FragmentCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FragmentLifetime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FragmentSpread;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DestructionEffects;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructionSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DestructionSounds;
	static void NewProp_bCanRegenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRegenerate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationRate;
	static void NewProp_bIsDestroyed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestroyed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDestructibleFoliageData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructibleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleId_MetaData), NewProp_DestructibleId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleBehavior = { "DestructibleBehavior", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructibleBehavior), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDestructibleBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleBehavior_MetaData), NewProp_DestructibleBehavior_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionThreshold = { "DestructionThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionThreshold_MetaData), NewProp_DestructionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionImpulse = { "DestructionImpulse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructionImpulse), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionImpulse_MetaData), NewProp_DestructionImpulse_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentCount = { "FragmentCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, FragmentCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentCount_MetaData), NewProp_FragmentCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentLifetime = { "FragmentLifetime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, FragmentLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentLifetime_MetaData), NewProp_FragmentLifetime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentSpread = { "FragmentSpread", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, FragmentSpread), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentSpread_MetaData), NewProp_FragmentSpread_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionEffects_Inner = { "DestructionEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionEffects = { "DestructionEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionEffects_MetaData), NewProp_DestructionEffects_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionSounds_Inner = { "DestructionSounds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionSounds = { "DestructionSounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructionSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionSounds_MetaData), NewProp_DestructionSounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bCanRegenerate_SetBit(void* Obj)
{
	((FAuracronDestructibleFoliageData*)Obj)->bCanRegenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bCanRegenerate = { "bCanRegenerate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructibleFoliageData), &Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bCanRegenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRegenerate_MetaData), NewProp_bCanRegenerate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_RegenerationTime = { "RegenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, RegenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationTime_MetaData), NewProp_RegenerationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_RegenerationRate = { "RegenerationRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, RegenerationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationRate_MetaData), NewProp_RegenerationRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bIsDestroyed_SetBit(void* Obj)
{
	((FAuracronDestructibleFoliageData*)Obj)->bIsDestroyed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bIsDestroyed = { "bIsDestroyed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructibleFoliageData), &Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bIsDestroyed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestroyed_MetaData), NewProp_bIsDestroyed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionTime = { "DestructionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructibleFoliageData, DestructionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionTime_MetaData), NewProp_DestructionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructibleBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentLifetime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_FragmentSpread,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bCanRegenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_RegenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_RegenerationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_bIsDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewProp_DestructionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronDestructibleFoliageData",
	Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::PropPointers),
	sizeof(FAuracronDestructibleFoliageData),
	alignof(FAuracronDestructibleFoliageData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDestructibleFoliageData ************************************

// ********** Begin ScriptStruct FAuracronTramplingEffectData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData;
class UScriptStruct* FAuracronTramplingEffectData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTramplingEffectData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronTramplingEffectData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Trampling Effect Data\n * Data for trampling effects on foliage\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trampling Effect Data\nData for trampling effects on foliage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingId_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingEffect_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingLocation_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingRadius_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingForce_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingIntensity_MetaData[] = {
		{ "Category", "Trampling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryTime_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoveryRate_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoRecover_MetaData[] = {
		{ "Category", "Recovery" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BendAngle_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlattenAmount_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeformationDirection_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingEffects_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingSounds_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRecoveryProgress_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TramplingEffect_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TramplingEffect;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TramplingLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoveryRate;
	static void NewProp_bAutoRecover_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoRecover;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BendAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlattenAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DeformationDirection;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TramplingEffects;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TramplingSounds;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentRecoveryProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TramplingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTramplingEffectData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingId = { "TramplingId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingId_MetaData), NewProp_TramplingId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffect_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffect = { "TramplingEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingEffect), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingEffect_MetaData), NewProp_TramplingEffect_MetaData) }; // 2136187517
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingLocation = { "TramplingLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingLocation_MetaData), NewProp_TramplingLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingRadius = { "TramplingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingRadius_MetaData), NewProp_TramplingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingForce = { "TramplingForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingForce_MetaData), NewProp_TramplingForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingIntensity = { "TramplingIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingIntensity_MetaData), NewProp_TramplingIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_RecoveryTime = { "RecoveryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, RecoveryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryTime_MetaData), NewProp_RecoveryTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_RecoveryRate = { "RecoveryRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, RecoveryRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoveryRate_MetaData), NewProp_RecoveryRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bAutoRecover_SetBit(void* Obj)
{
	((FAuracronTramplingEffectData*)Obj)->bAutoRecover = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bAutoRecover = { "bAutoRecover", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTramplingEffectData), &Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bAutoRecover_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoRecover_MetaData), NewProp_bAutoRecover_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_BendAngle = { "BendAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, BendAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BendAngle_MetaData), NewProp_BendAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_FlattenAmount = { "FlattenAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, FlattenAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlattenAmount_MetaData), NewProp_FlattenAmount_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_DeformationDirection = { "DeformationDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, DeformationDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeformationDirection_MetaData), NewProp_DeformationDirection_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffects_Inner = { "TramplingEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffects = { "TramplingEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingEffects_MetaData), NewProp_TramplingEffects_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingSounds_Inner = { "TramplingSounds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingSounds = { "TramplingSounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingSounds_MetaData), NewProp_TramplingSounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronTramplingEffectData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTramplingEffectData), &Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_CurrentRecoveryProgress = { "CurrentRecoveryProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, CurrentRecoveryProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRecoveryProgress_MetaData), NewProp_CurrentRecoveryProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingTime = { "TramplingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTramplingEffectData, TramplingTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingTime_MetaData), NewProp_TramplingTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffect_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_RecoveryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_RecoveryRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bAutoRecover,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_BendAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_FlattenAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_DeformationDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_CurrentRecoveryProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewProp_TramplingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronTramplingEffectData",
	Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::PropPointers),
	sizeof(FAuracronTramplingEffectData),
	alignof(FAuracronTramplingEffectData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTramplingEffectData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTramplingEffectData ****************************************

// ********** Begin ScriptStruct FAuracronCollisionPerformanceData *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData;
class UScriptStruct* FAuracronCollisionPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronCollisionPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Collision Performance Data\n * Performance metrics for collision system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Performance Data\nPerformance metrics for collision system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalCollisionMeshes_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveCollisionMeshes_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingEffects_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionQueries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsInteractions_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalCollisionMeshes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveCollisionMeshes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DestructibleInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TramplingEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhysicsUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TramplingUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollisionQueries;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PhysicsInteractions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TotalCollisionMeshes = { "TotalCollisionMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, TotalCollisionMeshes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalCollisionMeshes_MetaData), NewProp_TotalCollisionMeshes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_ActiveCollisionMeshes = { "ActiveCollisionMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, ActiveCollisionMeshes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveCollisionMeshes_MetaData), NewProp_ActiveCollisionMeshes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_DestructibleInstances = { "DestructibleInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, DestructibleInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleInstances_MetaData), NewProp_DestructibleInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TramplingEffects = { "TramplingEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, TramplingEffects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingEffects_MetaData), NewProp_TramplingEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_CollisionUpdateTime = { "CollisionUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, CollisionUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionUpdateTime_MetaData), NewProp_CollisionUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_PhysicsUpdateTime = { "PhysicsUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, PhysicsUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsUpdateTime_MetaData), NewProp_PhysicsUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_DestructionUpdateTime = { "DestructionUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, DestructionUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionUpdateTime_MetaData), NewProp_DestructionUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TramplingUpdateTime = { "TramplingUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, TramplingUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingUpdateTime_MetaData), NewProp_TramplingUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_CollisionQueries = { "CollisionQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, CollisionQueries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionQueries_MetaData), NewProp_CollisionQueries_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_PhysicsInteractions = { "PhysicsInteractions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, PhysicsInteractions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsInteractions_MetaData), NewProp_PhysicsInteractions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TotalCollisionMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_ActiveCollisionMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_DestructibleInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TramplingEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_CollisionUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_PhysicsUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_DestructionUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_TramplingUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_CollisionQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_PhysicsInteractions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronCollisionPerformanceData",
	Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::PropPointers),
	sizeof(FAuracronCollisionPerformanceData),
	alignof(FAuracronCollisionPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionPerformanceData ***********************************

// ********** Begin Delegate FOnCollisionMeshGenerated *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics
{
	struct AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms
	{
		FString CollisionMeshId;
		FAuracronCollisionMeshData MeshData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionMeshId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::NewProp_CollisionMeshId = { "CollisionMeshId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms, CollisionMeshId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::NewProp_MeshData = { "MeshData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms, MeshData), Z_Construct_UScriptStruct_FAuracronCollisionMeshData, METADATA_PARAMS(0, nullptr) }; // 2728292572
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::NewProp_CollisionMeshId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::NewProp_MeshData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "OnCollisionMeshGenerated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageCollisionManager::FOnCollisionMeshGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionMeshGenerated, const FString& CollisionMeshId, FAuracronCollisionMeshData MeshData)
{
	struct AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms
	{
		FString CollisionMeshId;
		FAuracronCollisionMeshData MeshData;
	};
	AuracronFoliageCollisionManager_eventOnCollisionMeshGenerated_Parms Parms;
	Parms.CollisionMeshId=CollisionMeshId;
	Parms.MeshData=MeshData;
	OnCollisionMeshGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCollisionMeshGenerated ***********************************************

// ********** Begin Delegate FOnFoliageDestroyed ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics
{
	struct AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms
	{
		FString DestructibleId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms, DestructibleId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::NewProp_DestructibleId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "OnFoliageDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageCollisionManager::FOnFoliageDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageDestroyed, const FString& DestructibleId)
{
	struct AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms
	{
		FString DestructibleId;
	};
	AuracronFoliageCollisionManager_eventOnFoliageDestroyed_Parms Parms;
	Parms.DestructibleId=DestructibleId;
	OnFoliageDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageDestroyed *****************************************************

// ********** Begin Delegate FOnTramplingEffectCreated *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics
{
	struct AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms
	{
		FString TramplingId;
		FAuracronTramplingEffectData TramplingData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TramplingData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::NewProp_TramplingId = { "TramplingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms, TramplingId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::NewProp_TramplingData = { "TramplingData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms, TramplingData), Z_Construct_UScriptStruct_FAuracronTramplingEffectData, METADATA_PARAMS(0, nullptr) }; // 3592431104
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::NewProp_TramplingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::NewProp_TramplingData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "OnTramplingEffectCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageCollisionManager::FOnTramplingEffectCreated_DelegateWrapper(const FMulticastScriptDelegate& OnTramplingEffectCreated, const FString& TramplingId, FAuracronTramplingEffectData TramplingData)
{
	struct AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms
	{
		FString TramplingId;
		FAuracronTramplingEffectData TramplingData;
	};
	AuracronFoliageCollisionManager_eventOnTramplingEffectCreated_Parms Parms;
	Parms.TramplingId=TramplingId;
	Parms.TramplingData=TramplingData;
	OnTramplingEffectCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTramplingEffectCreated ***********************************************

// ********** Begin Delegate FOnPhysicsInteraction *************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics
{
	struct AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms
	{
		FString FoliageInstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms, FoliageInstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::NewProp_FoliageInstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "OnPhysicsInteraction__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageCollisionManager::FOnPhysicsInteraction_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsInteraction, const FString& FoliageInstanceId)
{
	struct AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms
	{
		FString FoliageInstanceId;
	};
	AuracronFoliageCollisionManager_eventOnPhysicsInteraction_Parms Parms;
	Parms.FoliageInstanceId=FoliageInstanceId;
	OnPhysicsInteraction.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsInteraction ***************************************************

// ********** Begin Class UAuracronFoliageCollisionManager Function ApplyPhysicsToFoliageInstance **
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics
{
	struct AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms
	{
		UHierarchicalInstancedStaticMeshComponent* Component;
		int32 InstanceIndex;
		FAuracronCollisionMeshData CollisionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms, Component), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_CollisionData = { "CollisionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms, CollisionData), Z_Construct_UScriptStruct_FAuracronCollisionMeshData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionData_MetaData), NewProp_CollisionData_MetaData) }; // 2728292572
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::NewProp_CollisionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "ApplyPhysicsToFoliageInstance", Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::AuracronFoliageCollisionManager_eventApplyPhysicsToFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execApplyPhysicsToFoliageInstance)
{
	P_GET_OBJECT(UHierarchicalInstancedStaticMeshComponent,Z_Param_Component);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_GET_STRUCT_REF(FAuracronCollisionMeshData,Z_Param_Out_CollisionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyPhysicsToFoliageInstance(Z_Param_Component,Z_Param_InstanceIndex,Z_Param_Out_CollisionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function ApplyPhysicsToFoliageInstance ****

// ********** Begin Class UAuracronFoliageCollisionManager Function ApplyTramplingToArea ***********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics
{
	struct AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms
	{
		FVector Location;
		float Radius;
		float Force;
		EAuracronTramplingEffect EffectType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_EffectType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms, EffectType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect, METADATA_PARAMS(0, nullptr) }; // 2136187517
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_EffectType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::NewProp_EffectType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "ApplyTramplingToArea", Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::AuracronFoliageCollisionManager_eventApplyTramplingToArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execApplyTramplingToArea)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_ENUM(EAuracronTramplingEffect,Z_Param_EffectType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTramplingToArea(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Force,EAuracronTramplingEffect(Z_Param_EffectType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function ApplyTramplingToArea *************

// ********** Begin Class UAuracronFoliageCollisionManager Function CreateDestructibleFoliage ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms
	{
		FString FoliageInstanceId;
		FAuracronDestructibleFoliageData DestructibleData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Destructible foliage\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destructible foliage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageInstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageInstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructibleData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_FoliageInstanceId = { "FoliageInstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms, FoliageInstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageInstanceId_MetaData), NewProp_FoliageInstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_DestructibleData = { "DestructibleData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms, DestructibleData), Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleData_MetaData), NewProp_DestructibleData_MetaData) }; // 3044957000
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_FoliageInstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_DestructibleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "CreateDestructibleFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventCreateDestructibleFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execCreateDestructibleFoliage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageInstanceId);
	P_GET_STRUCT_REF(FAuracronDestructibleFoliageData,Z_Param_Out_DestructibleData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateDestructibleFoliage(Z_Param_FoliageInstanceId,Z_Param_Out_DestructibleData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function CreateDestructibleFoliage ********

// ********** Begin Class UAuracronFoliageCollisionManager Function CreateTramplingEffect **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics
{
	struct AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms
	{
		FVector Location;
		float Radius;
		float Force;
		EAuracronTramplingEffect EffectType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Trampling effects\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trampling effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EffectType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_EffectType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms, EffectType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronTramplingEffect, METADATA_PARAMS(0, nullptr) }; // 2136187517
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_EffectType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "CreateTramplingEffect", Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::AuracronFoliageCollisionManager_eventCreateTramplingEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execCreateTramplingEffect)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_ENUM(EAuracronTramplingEffect,Z_Param_EffectType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateTramplingEffect(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Force,EAuracronTramplingEffect(Z_Param_EffectType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function CreateTramplingEffect ************

// ********** Begin Class UAuracronFoliageCollisionManager Function DestroyFoliageInstance *********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics
{
	struct AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms
	{
		FString DestructibleId;
		float DamageAmount;
		FVector ImpactLocation;
		FVector ImpactDirection;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpactLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpactDirection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms, DestructibleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleId_MetaData), NewProp_DestructibleId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ImpactLocation = { "ImpactLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms, ImpactLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactLocation_MetaData), NewProp_ImpactLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ImpactDirection = { "ImpactDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms, ImpactDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactDirection_MetaData), NewProp_ImpactDirection_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_DestructibleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ImpactLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ImpactDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "DestroyFoliageInstance", Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::AuracronFoliageCollisionManager_eventDestroyFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execDestroyFoliageInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DestructibleId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ImpactLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ImpactDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroyFoliageInstance(Z_Param_DestructibleId,Z_Param_DamageAmount,Z_Param_Out_ImpactLocation,Z_Param_Out_ImpactDirection);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function DestroyFoliageInstance ***********

// ********** Begin Class UAuracronFoliageCollisionManager Function DrawDebugCollisionMeshes *******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics
{
	struct AuracronFoliageCollisionManager_eventDrawDebugCollisionMeshes_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventDrawDebugCollisionMeshes_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "DrawDebugCollisionMeshes", Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::AuracronFoliageCollisionManager_eventDrawDebugCollisionMeshes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::AuracronFoliageCollisionManager_eventDrawDebugCollisionMeshes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execDrawDebugCollisionMeshes)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugCollisionMeshes(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function DrawDebugCollisionMeshes *********

// ********** Begin Class UAuracronFoliageCollisionManager Function EnableDebugVisualization *******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageCollisionManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::AuracronFoliageCollisionManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::AuracronFoliageCollisionManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function EnableDebugVisualization *********

// ********** Begin Class UAuracronFoliageCollisionManager Function EnablePhysicsForFoliageType ****
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics
{
	struct AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms
	{
		FString FoliageTypeId;
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "EnablePhysicsForFoliageType", Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::AuracronFoliageCollisionManager_eventEnablePhysicsForFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execEnablePhysicsForFoliageType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePhysicsForFoliageType(Z_Param_FoliageTypeId,Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function EnablePhysicsForFoliageType ******

// ********** Begin Class UAuracronFoliageCollisionManager Function GenerateCollisionMesh **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics
{
	struct AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms
	{
		UStaticMesh* SourceMesh;
		EAuracronFoliageCollisionType CollisionType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision mesh generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision mesh generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CollisionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_CollisionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_CollisionType = { "CollisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms, CollisionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageCollisionType, METADATA_PARAMS(0, nullptr) }; // 1409904130
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_CollisionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_CollisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GenerateCollisionMesh", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::AuracronFoliageCollisionManager_eventGenerateCollisionMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGenerateCollisionMesh)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_GET_ENUM(EAuracronFoliageCollisionType,Z_Param_CollisionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateCollisionMesh(Z_Param_SourceMesh,EAuracronFoliageCollisionType(Z_Param_CollisionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GenerateCollisionMesh ************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetActiveCollisionCount ********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics
{
	struct AuracronFoliageCollisionManager_eventGetActiveCollisionCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetActiveCollisionCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetActiveCollisionCount", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::AuracronFoliageCollisionManager_eventGetActiveCollisionCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::AuracronFoliageCollisionManager_eventGetActiveCollisionCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetActiveCollisionCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveCollisionCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetActiveCollisionCount **********

// ********** Begin Class UAuracronFoliageCollisionManager Function GetAllCollisionMeshes **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics
{
	struct AuracronFoliageCollisionManager_eventGetAllCollisionMeshes_Parms
	{
		TArray<FAuracronCollisionMeshData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCollisionMeshData, METADATA_PARAMS(0, nullptr) }; // 2728292572
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetAllCollisionMeshes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2728292572
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetAllCollisionMeshes", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::AuracronFoliageCollisionManager_eventGetAllCollisionMeshes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::AuracronFoliageCollisionManager_eventGetAllCollisionMeshes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetAllCollisionMeshes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronCollisionMeshData>*)Z_Param__Result=P_THIS->GetAllCollisionMeshes();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetAllCollisionMeshes ************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetAllDestructibleFoliage ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventGetAllDestructibleFoliage_Parms
	{
		TArray<FAuracronDestructibleFoliageData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData, METADATA_PARAMS(0, nullptr) }; // 3044957000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetAllDestructibleFoliage_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3044957000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetAllDestructibleFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventGetAllDestructibleFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventGetAllDestructibleFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetAllDestructibleFoliage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDestructibleFoliageData>*)Z_Param__Result=P_THIS->GetAllDestructibleFoliage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetAllDestructibleFoliage ********

// ********** Begin Class UAuracronFoliageCollisionManager Function GetAllTramplingEffects *********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics
{
	struct AuracronFoliageCollisionManager_eventGetAllTramplingEffects_Parms
	{
		TArray<FAuracronTramplingEffectData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronTramplingEffectData, METADATA_PARAMS(0, nullptr) }; // 3592431104
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetAllTramplingEffects_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3592431104
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetAllTramplingEffects", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::AuracronFoliageCollisionManager_eventGetAllTramplingEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::AuracronFoliageCollisionManager_eventGetAllTramplingEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetAllTramplingEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronTramplingEffectData>*)Z_Param__Result=P_THIS->GetAllTramplingEffects();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetAllTramplingEffects ***********

// ********** Begin Class UAuracronFoliageCollisionManager Function GetCollisionMesh ***************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics
{
	struct AuracronFoliageCollisionManager_eventGetCollisionMesh_Parms
	{
		FString CollisionMeshId;
		FAuracronCollisionMeshData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMeshId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionMeshId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::NewProp_CollisionMeshId = { "CollisionMeshId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetCollisionMesh_Parms, CollisionMeshId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMeshId_MetaData), NewProp_CollisionMeshId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetCollisionMesh_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCollisionMeshData, METADATA_PARAMS(0, nullptr) }; // 2728292572
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::NewProp_CollisionMeshId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetCollisionMesh", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::AuracronFoliageCollisionManager_eventGetCollisionMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::AuracronFoliageCollisionManager_eventGetCollisionMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetCollisionMesh)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionMeshId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCollisionMeshData*)Z_Param__Result=P_THIS->GetCollisionMesh(Z_Param_CollisionMeshId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetCollisionMesh *****************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics
{
	struct AuracronFoliageCollisionManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageCollisionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration, METADATA_PARAMS(0, nullptr) }; // 3949760375
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::AuracronFoliageCollisionManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::AuracronFoliageCollisionManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageCollisionConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetConfiguration *****************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetDestructibleCount ***********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics
{
	struct AuracronFoliageCollisionManager_eventGetDestructibleCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetDestructibleCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetDestructibleCount", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::AuracronFoliageCollisionManager_eventGetDestructibleCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::AuracronFoliageCollisionManager_eventGetDestructibleCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetDestructibleCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetDestructibleCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetDestructibleCount *************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetDestructibleFoliage *********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventGetDestructibleFoliage_Parms
	{
		FString DestructibleId;
		FAuracronDestructibleFoliageData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetDestructibleFoliage_Parms, DestructibleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleId_MetaData), NewProp_DestructibleId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetDestructibleFoliage_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData, METADATA_PARAMS(0, nullptr) }; // 3044957000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::NewProp_DestructibleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetDestructibleFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventGetDestructibleFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventGetDestructibleFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetDestructibleFoliage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DestructibleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDestructibleFoliageData*)Z_Param__Result=P_THIS->GetDestructibleFoliage(Z_Param_DestructibleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetDestructibleFoliage ***********

// ********** Begin Class UAuracronFoliageCollisionManager Function GetFoliageInstancesInBox *******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics
{
	struct AuracronFoliageCollisionManager_eventGetFoliageInstancesInBox_Parms
	{
		FBox Box;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Box_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Box;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_Box = { "Box", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliageInstancesInBox_Parms, Box), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Box_MetaData), NewProp_Box_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliageInstancesInBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_Box,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetFoliageInstancesInBox", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::AuracronFoliageCollisionManager_eventGetFoliageInstancesInBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::AuracronFoliageCollisionManager_eventGetFoliageInstancesInBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetFoliageInstancesInBox)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Box);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetFoliageInstancesInBox(Z_Param_Out_Box);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetFoliageInstancesInBox *********

// ********** Begin Class UAuracronFoliageCollisionManager Function GetFoliageInstancesInRadius ****
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics
{
	struct AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms
	{
		FVector Center;
		float Radius;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetFoliageInstancesInRadius", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::AuracronFoliageCollisionManager_eventGetFoliageInstancesInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetFoliageInstancesInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetFoliageInstancesInRadius(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetFoliageInstancesInRadius ******

// ********** Begin Class UAuracronFoliageCollisionManager Function GetFoliagePhysicsType **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics
{
	struct AuracronFoliageCollisionManager_eventGetFoliagePhysicsType_Parms
	{
		FString FoliageTypeId;
		EAuracronPhysicsInteractionType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliagePhysicsType_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetFoliagePhysicsType_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType, METADATA_PARAMS(0, nullptr) }; // 368324703
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetFoliagePhysicsType", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::AuracronFoliageCollisionManager_eventGetFoliagePhysicsType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::AuracronFoliageCollisionManager_eventGetFoliagePhysicsType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetFoliagePhysicsType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPhysicsInteractionType*)Z_Param__Result=P_THIS->GetFoliagePhysicsType(Z_Param_FoliageTypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetFoliagePhysicsType ************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetInstance ********************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics
{
	struct AuracronFoliageCollisionManager_eventGetInstance_Parms
	{
		UAuracronFoliageCollisionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::AuracronFoliageCollisionManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::AuracronFoliageCollisionManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageCollisionManager**)Z_Param__Result=UAuracronFoliageCollisionManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetInstance **********************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetPerformanceData *************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics
{
	struct AuracronFoliageCollisionManager_eventGetPerformanceData_Parms
	{
		FAuracronCollisionPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2325588069
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::AuracronFoliageCollisionManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::AuracronFoliageCollisionManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCollisionPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetPerformanceData ***************

// ********** Begin Class UAuracronFoliageCollisionManager Function GetTramplingEffect *************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics
{
	struct AuracronFoliageCollisionManager_eventGetTramplingEffect_Parms
	{
		FString TramplingId;
		FAuracronTramplingEffectData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::NewProp_TramplingId = { "TramplingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetTramplingEffect_Parms, TramplingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingId_MetaData), NewProp_TramplingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventGetTramplingEffect_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTramplingEffectData, METADATA_PARAMS(0, nullptr) }; // 3592431104
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::NewProp_TramplingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "GetTramplingEffect", Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::AuracronFoliageCollisionManager_eventGetTramplingEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::AuracronFoliageCollisionManager_eventGetTramplingEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execGetTramplingEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TramplingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTramplingEffectData*)Z_Param__Result=P_THIS->GetTramplingEffect(Z_Param_TramplingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function GetTramplingEffect ***************

// ********** Begin Class UAuracronFoliageCollisionManager Function Initialize *********************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics
{
	struct AuracronFoliageCollisionManager_eventInitialize_Parms
	{
		FAuracronFoliageCollisionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3949760375
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::AuracronFoliageCollisionManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::AuracronFoliageCollisionManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageCollisionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function Initialize ***********************

// ********** Begin Class UAuracronFoliageCollisionManager Function IsDebugVisualizationEnabled ****
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageCollisionManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageCollisionManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageCollisionManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function IsDebugVisualizationEnabled ******

// ********** Begin Class UAuracronFoliageCollisionManager Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics
{
	struct AuracronFoliageCollisionManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::AuracronFoliageCollisionManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::AuracronFoliageCollisionManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function IsInitialized ********************

// ********** Begin Class UAuracronFoliageCollisionManager Function LineTraceAgainstFoliage ********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms
	{
		FVector Start;
		FVector End;
		FHitResult OutHitResult;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutHitResult;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_OutHitResult = { "OutHitResult", nullptr, (EPropertyFlags)0x0010008000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms, OutHitResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(0, nullptr) }; // 267591329
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_OutHitResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "LineTraceAgainstFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::AuracronFoliageCollisionManager_eventLineTraceAgainstFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execLineTraceAgainstFoliage)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_OutHitResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LineTraceAgainstFoliage(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_OutHitResult);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function LineTraceAgainstFoliage **********

// ********** Begin Class UAuracronFoliageCollisionManager Function LogCollisionStatistics *********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "LogCollisionStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execLogCollisionStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCollisionStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function LogCollisionStatistics ***********

// ********** Begin Class UAuracronFoliageCollisionManager Function OptimizeCollisionForDistance ***
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics
{
	struct AuracronFoliageCollisionManager_eventOptimizeCollisionForDistance_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventOptimizeCollisionForDistance_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "OptimizeCollisionForDistance", Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::AuracronFoliageCollisionManager_eventOptimizeCollisionForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::AuracronFoliageCollisionManager_eventOptimizeCollisionForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execOptimizeCollisionForDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeCollisionForDistance(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function OptimizeCollisionForDistance *****

// ********** Begin Class UAuracronFoliageCollisionManager Function RegenerateFoliageInstance ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics
{
	struct AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms
	{
		FString DestructibleId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms, DestructibleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleId_MetaData), NewProp_DestructibleId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_DestructibleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "RegenerateFoliageInstance", Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::AuracronFoliageCollisionManager_eventRegenerateFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execRegenerateFoliageInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DestructibleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegenerateFoliageInstance(Z_Param_DestructibleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function RegenerateFoliageInstance ********

// ********** Begin Class UAuracronFoliageCollisionManager Function RemoveCollisionMesh ************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics
{
	struct AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms
	{
		FString CollisionMeshId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMeshId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionMeshId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_CollisionMeshId = { "CollisionMeshId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms, CollisionMeshId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMeshId_MetaData), NewProp_CollisionMeshId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_CollisionMeshId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "RemoveCollisionMesh", Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::AuracronFoliageCollisionManager_eventRemoveCollisionMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execRemoveCollisionMesh)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionMeshId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCollisionMesh(Z_Param_CollisionMeshId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function RemoveCollisionMesh **************

// ********** Begin Class UAuracronFoliageCollisionManager Function RemoveTramplingEffect **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics
{
	struct AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms
	{
		FString TramplingId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_TramplingId = { "TramplingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms, TramplingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingId_MetaData), NewProp_TramplingId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_TramplingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "RemoveTramplingEffect", Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::AuracronFoliageCollisionManager_eventRemoveTramplingEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execRemoveTramplingEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TramplingId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveTramplingEffect(Z_Param_TramplingId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function RemoveTramplingEffect ************

// ********** Begin Class UAuracronFoliageCollisionManager Function SetCollisionLOD ****************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics
{
	struct AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms
	{
		FString FoliageTypeId;
		int32 LODLevel;
		bool bEnableCollision;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static void NewProp_bEnableCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollision;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_bEnableCollision_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms*)Obj)->bEnableCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_bEnableCollision = { "bEnableCollision", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_bEnableCollision_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::NewProp_bEnableCollision,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "SetCollisionLOD", Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::AuracronFoliageCollisionManager_eventSetCollisionLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execSetCollisionLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_GET_UBOOL(Z_Param_bEnableCollision);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCollisionLOD(Z_Param_FoliageTypeId,Z_Param_LODLevel,Z_Param_bEnableCollision);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function SetCollisionLOD ******************

// ********** Begin Class UAuracronFoliageCollisionManager Function SetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics
{
	struct AuracronFoliageCollisionManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageCollisionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3949760375
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::AuracronFoliageCollisionManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::AuracronFoliageCollisionManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageCollisionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function SetConfiguration *****************

// ********** Begin Class UAuracronFoliageCollisionManager Function SetFoliagePhysicsType **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics
{
	struct AuracronFoliageCollisionManager_eventSetFoliagePhysicsType_Parms
	{
		FString FoliageTypeId;
		EAuracronPhysicsInteractionType PhysicsType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhysicsType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhysicsType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSetFoliagePhysicsType_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_PhysicsType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_PhysicsType = { "PhysicsType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSetFoliagePhysicsType_Parms, PhysicsType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPhysicsInteractionType, METADATA_PARAMS(0, nullptr) }; // 368324703
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_PhysicsType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::NewProp_PhysicsType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "SetFoliagePhysicsType", Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::AuracronFoliageCollisionManager_eventSetFoliagePhysicsType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::AuracronFoliageCollisionManager_eventSetFoliagePhysicsType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execSetFoliagePhysicsType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_ENUM(EAuracronPhysicsInteractionType,Z_Param_PhysicsType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFoliagePhysicsType(Z_Param_FoliageTypeId,EAuracronPhysicsInteractionType(Z_Param_PhysicsType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function SetFoliagePhysicsType ************

// ********** Begin Class UAuracronFoliageCollisionManager Function Shutdown ***********************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function Shutdown *************************

// ********** Begin Class UAuracronFoliageCollisionManager Function SphereTraceAgainstFoliage ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms
	{
		FVector Start;
		FVector End;
		float Radius;
		FHitResult OutHitResult;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutHitResult;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_OutHitResult = { "OutHitResult", nullptr, (EPropertyFlags)0x0010008000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms, OutHitResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(0, nullptr) }; // 267591329
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_OutHitResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "SphereTraceAgainstFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::AuracronFoliageCollisionManager_eventSphereTraceAgainstFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execSphereTraceAgainstFoliage)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_OutHitResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SphereTraceAgainstFoliage(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Radius,Z_Param_Out_OutHitResult);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function SphereTraceAgainstFoliage ********

// ********** Begin Class UAuracronFoliageCollisionManager Function Tick ***************************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics
{
	struct AuracronFoliageCollisionManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::AuracronFoliageCollisionManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::AuracronFoliageCollisionManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function Tick *****************************

// ********** Begin Class UAuracronFoliageCollisionManager Function UpdateCollisionBasedOnLOD ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics
{
	struct AuracronFoliageCollisionManager_eventUpdateCollisionBasedOnLOD_Parms
	{
		FString FoliageTypeId;
		int32 CurrentLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateCollisionBasedOnLOD_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::NewProp_CurrentLOD = { "CurrentLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateCollisionBasedOnLOD_Parms, CurrentLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::NewProp_CurrentLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "UpdateCollisionBasedOnLOD", Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::AuracronFoliageCollisionManager_eventUpdateCollisionBasedOnLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::AuracronFoliageCollisionManager_eventUpdateCollisionBasedOnLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execUpdateCollisionBasedOnLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_PROPERTY(FIntProperty,Z_Param_CurrentLOD);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCollisionBasedOnLOD(Z_Param_FoliageTypeId,Z_Param_CurrentLOD);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function UpdateCollisionBasedOnLOD ********

// ********** Begin Class UAuracronFoliageCollisionManager Function UpdateCollisionMesh ************
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics
{
	struct AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms
	{
		FString CollisionMeshId;
		FAuracronCollisionMeshData MeshData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMeshId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionMeshId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_CollisionMeshId = { "CollisionMeshId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms, CollisionMeshId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMeshId_MetaData), NewProp_CollisionMeshId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_MeshData = { "MeshData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms, MeshData), Z_Construct_UScriptStruct_FAuracronCollisionMeshData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshData_MetaData), NewProp_MeshData_MetaData) }; // 2728292572
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_CollisionMeshId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_MeshData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "UpdateCollisionMesh", Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::AuracronFoliageCollisionManager_eventUpdateCollisionMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execUpdateCollisionMesh)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CollisionMeshId);
	P_GET_STRUCT_REF(FAuracronCollisionMeshData,Z_Param_Out_MeshData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateCollisionMesh(Z_Param_CollisionMeshId,Z_Param_Out_MeshData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function UpdateCollisionMesh **************

// ********** Begin Class UAuracronFoliageCollisionManager Function UpdateDestructibleFoliage ******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics
{
	struct AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms
	{
		FString DestructibleId;
		FAuracronDestructibleFoliageData DestructibleData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructibleData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestructibleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructibleData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_DestructibleId = { "DestructibleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms, DestructibleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleId_MetaData), NewProp_DestructibleId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_DestructibleData = { "DestructibleData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms, DestructibleData), Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructibleData_MetaData), NewProp_DestructibleData_MetaData) }; // 3044957000
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_DestructibleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_DestructibleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "UpdateDestructibleFoliage", Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::AuracronFoliageCollisionManager_eventUpdateDestructibleFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execUpdateDestructibleFoliage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DestructibleId);
	P_GET_STRUCT_REF(FAuracronDestructibleFoliageData,Z_Param_Out_DestructibleData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateDestructibleFoliage(Z_Param_DestructibleId,Z_Param_Out_DestructibleData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function UpdateDestructibleFoliage ********

// ********** Begin Class UAuracronFoliageCollisionManager Function UpdatePerformanceMetrics *******
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function UpdatePerformanceMetrics *********

// ********** Begin Class UAuracronFoliageCollisionManager Function UpdateTramplingEffect **********
struct Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics
{
	struct AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms
	{
		FString TramplingId;
		FAuracronTramplingEffectData TramplingData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TramplingData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TramplingId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TramplingData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_TramplingId = { "TramplingId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms, TramplingId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingId_MetaData), NewProp_TramplingId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_TramplingData = { "TramplingData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms, TramplingData), Z_Construct_UScriptStruct_FAuracronTramplingEffectData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TramplingData_MetaData), NewProp_TramplingData_MetaData) }; // 3592431104
void Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms), &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_TramplingId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_TramplingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageCollisionManager, nullptr, "UpdateTramplingEffect", Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::AuracronFoliageCollisionManager_eventUpdateTramplingEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageCollisionManager::execUpdateTramplingEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TramplingId);
	P_GET_STRUCT_REF(FAuracronTramplingEffectData,Z_Param_Out_TramplingData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateTramplingEffect(Z_Param_TramplingId,Z_Param_Out_TramplingData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageCollisionManager Function UpdateTramplingEffect ************

// ********** Begin Class UAuracronFoliageCollisionManager *****************************************
void UAuracronFoliageCollisionManager::StaticRegisterNativesUAuracronFoliageCollisionManager()
{
	UClass* Class = UAuracronFoliageCollisionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyPhysicsToFoliageInstance", &UAuracronFoliageCollisionManager::execApplyPhysicsToFoliageInstance },
		{ "ApplyTramplingToArea", &UAuracronFoliageCollisionManager::execApplyTramplingToArea },
		{ "CreateDestructibleFoliage", &UAuracronFoliageCollisionManager::execCreateDestructibleFoliage },
		{ "CreateTramplingEffect", &UAuracronFoliageCollisionManager::execCreateTramplingEffect },
		{ "DestroyFoliageInstance", &UAuracronFoliageCollisionManager::execDestroyFoliageInstance },
		{ "DrawDebugCollisionMeshes", &UAuracronFoliageCollisionManager::execDrawDebugCollisionMeshes },
		{ "EnableDebugVisualization", &UAuracronFoliageCollisionManager::execEnableDebugVisualization },
		{ "EnablePhysicsForFoliageType", &UAuracronFoliageCollisionManager::execEnablePhysicsForFoliageType },
		{ "GenerateCollisionMesh", &UAuracronFoliageCollisionManager::execGenerateCollisionMesh },
		{ "GetActiveCollisionCount", &UAuracronFoliageCollisionManager::execGetActiveCollisionCount },
		{ "GetAllCollisionMeshes", &UAuracronFoliageCollisionManager::execGetAllCollisionMeshes },
		{ "GetAllDestructibleFoliage", &UAuracronFoliageCollisionManager::execGetAllDestructibleFoliage },
		{ "GetAllTramplingEffects", &UAuracronFoliageCollisionManager::execGetAllTramplingEffects },
		{ "GetCollisionMesh", &UAuracronFoliageCollisionManager::execGetCollisionMesh },
		{ "GetConfiguration", &UAuracronFoliageCollisionManager::execGetConfiguration },
		{ "GetDestructibleCount", &UAuracronFoliageCollisionManager::execGetDestructibleCount },
		{ "GetDestructibleFoliage", &UAuracronFoliageCollisionManager::execGetDestructibleFoliage },
		{ "GetFoliageInstancesInBox", &UAuracronFoliageCollisionManager::execGetFoliageInstancesInBox },
		{ "GetFoliageInstancesInRadius", &UAuracronFoliageCollisionManager::execGetFoliageInstancesInRadius },
		{ "GetFoliagePhysicsType", &UAuracronFoliageCollisionManager::execGetFoliagePhysicsType },
		{ "GetInstance", &UAuracronFoliageCollisionManager::execGetInstance },
		{ "GetPerformanceData", &UAuracronFoliageCollisionManager::execGetPerformanceData },
		{ "GetTramplingEffect", &UAuracronFoliageCollisionManager::execGetTramplingEffect },
		{ "Initialize", &UAuracronFoliageCollisionManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageCollisionManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageCollisionManager::execIsInitialized },
		{ "LineTraceAgainstFoliage", &UAuracronFoliageCollisionManager::execLineTraceAgainstFoliage },
		{ "LogCollisionStatistics", &UAuracronFoliageCollisionManager::execLogCollisionStatistics },
		{ "OptimizeCollisionForDistance", &UAuracronFoliageCollisionManager::execOptimizeCollisionForDistance },
		{ "RegenerateFoliageInstance", &UAuracronFoliageCollisionManager::execRegenerateFoliageInstance },
		{ "RemoveCollisionMesh", &UAuracronFoliageCollisionManager::execRemoveCollisionMesh },
		{ "RemoveTramplingEffect", &UAuracronFoliageCollisionManager::execRemoveTramplingEffect },
		{ "SetCollisionLOD", &UAuracronFoliageCollisionManager::execSetCollisionLOD },
		{ "SetConfiguration", &UAuracronFoliageCollisionManager::execSetConfiguration },
		{ "SetFoliagePhysicsType", &UAuracronFoliageCollisionManager::execSetFoliagePhysicsType },
		{ "Shutdown", &UAuracronFoliageCollisionManager::execShutdown },
		{ "SphereTraceAgainstFoliage", &UAuracronFoliageCollisionManager::execSphereTraceAgainstFoliage },
		{ "Tick", &UAuracronFoliageCollisionManager::execTick },
		{ "UpdateCollisionBasedOnLOD", &UAuracronFoliageCollisionManager::execUpdateCollisionBasedOnLOD },
		{ "UpdateCollisionMesh", &UAuracronFoliageCollisionManager::execUpdateCollisionMesh },
		{ "UpdateDestructibleFoliage", &UAuracronFoliageCollisionManager::execUpdateDestructibleFoliage },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageCollisionManager::execUpdatePerformanceMetrics },
		{ "UpdateTramplingEffect", &UAuracronFoliageCollisionManager::execUpdateTramplingEffect },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageCollisionManager;
UClass* UAuracronFoliageCollisionManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageCollisionManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageCollisionManager"),
			Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageCollisionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager_NoRegister()
{
	return UAuracronFoliageCollisionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Collision Manager\n * Manager for the foliage collision system including collision mesh generation, physics interaction, and destructible foliage\n */" },
#endif
		{ "IncludePath", "AuracronFoliageCollision.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Collision Manager\nManager for the foliage collision system including collision mesh generation, physics interaction, and destructible foliage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCollisionMeshGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageDestroyed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTramplingEffectCreated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsInteraction_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageCollision.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCollisionMeshGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTramplingEffectCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsInteraction;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyPhysicsToFoliageInstance, "ApplyPhysicsToFoliageInstance" }, // 1579629452
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_ApplyTramplingToArea, "ApplyTramplingToArea" }, // 3317318355
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateDestructibleFoliage, "CreateDestructibleFoliage" }, // 2406299159
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_CreateTramplingEffect, "CreateTramplingEffect" }, // 4011054919
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_DestroyFoliageInstance, "DestroyFoliageInstance" }, // 3639323213
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_DrawDebugCollisionMeshes, "DrawDebugCollisionMeshes" }, // 4009534663
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 1425330438
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_EnablePhysicsForFoliageType, "EnablePhysicsForFoliageType" }, // 3345882768
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GenerateCollisionMesh, "GenerateCollisionMesh" }, // 1092138336
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetActiveCollisionCount, "GetActiveCollisionCount" }, // 1512775270
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllCollisionMeshes, "GetAllCollisionMeshes" }, // 819409758
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllDestructibleFoliage, "GetAllDestructibleFoliage" }, // 1906801271
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetAllTramplingEffects, "GetAllTramplingEffects" }, // 2428623641
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetCollisionMesh, "GetCollisionMesh" }, // 932871782
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetConfiguration, "GetConfiguration" }, // 4132288390
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleCount, "GetDestructibleCount" }, // 961279743
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetDestructibleFoliage, "GetDestructibleFoliage" }, // 3502684372
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInBox, "GetFoliageInstancesInBox" }, // 3882148876
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliageInstancesInRadius, "GetFoliageInstancesInRadius" }, // 1183618032
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetFoliagePhysicsType, "GetFoliagePhysicsType" }, // 1846260286
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetInstance, "GetInstance" }, // 2611163188
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetPerformanceData, "GetPerformanceData" }, // 3978397156
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_GetTramplingEffect, "GetTramplingEffect" }, // 3624498164
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_Initialize, "Initialize" }, // 2315238567
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 1991448840
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_IsInitialized, "IsInitialized" }, // 3516086496
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_LineTraceAgainstFoliage, "LineTraceAgainstFoliage" }, // 285794168
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_LogCollisionStatistics, "LogCollisionStatistics" }, // 2692968449
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature, "OnCollisionMeshGenerated__DelegateSignature" }, // 3808117809
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature, "OnFoliageDestroyed__DelegateSignature" }, // 1869581510
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature, "OnPhysicsInteraction__DelegateSignature" }, // 3306354215
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature, "OnTramplingEffectCreated__DelegateSignature" }, // 2867067241
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_OptimizeCollisionForDistance, "OptimizeCollisionForDistance" }, // 2289931732
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RegenerateFoliageInstance, "RegenerateFoliageInstance" }, // 4215562181
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveCollisionMesh, "RemoveCollisionMesh" }, // 83686020
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_RemoveTramplingEffect, "RemoveTramplingEffect" }, // 843664665
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetCollisionLOD, "SetCollisionLOD" }, // 2051333224
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetConfiguration, "SetConfiguration" }, // 4236164744
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SetFoliagePhysicsType, "SetFoliagePhysicsType" }, // 391864970
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_Shutdown, "Shutdown" }, // 3230572813
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_SphereTraceAgainstFoliage, "SphereTraceAgainstFoliage" }, // 69984807
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_Tick, "Tick" }, // 3581447647
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionBasedOnLOD, "UpdateCollisionBasedOnLOD" }, // 1789666902
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateCollisionMesh, "UpdateCollisionMesh" }, // 773965737
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateDestructibleFoliage, "UpdateDestructibleFoliage" }, // 2247106436
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 507959950
		{ &Z_Construct_UFunction_UAuracronFoliageCollisionManager_UpdateTramplingEffect, "UpdateTramplingEffect" }, // 3100397167
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageCollisionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnCollisionMeshGenerated = { "OnCollisionMeshGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, OnCollisionMeshGenerated), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnCollisionMeshGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCollisionMeshGenerated_MetaData), NewProp_OnCollisionMeshGenerated_MetaData) }; // 3808117809
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnFoliageDestroyed = { "OnFoliageDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, OnFoliageDestroyed), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnFoliageDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageDestroyed_MetaData), NewProp_OnFoliageDestroyed_MetaData) }; // 1869581510
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnTramplingEffectCreated = { "OnTramplingEffectCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, OnTramplingEffectCreated), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnTramplingEffectCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTramplingEffectCreated_MetaData), NewProp_OnTramplingEffectCreated_MetaData) }; // 2867067241
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnPhysicsInteraction = { "OnPhysicsInteraction", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, OnPhysicsInteraction), Z_Construct_UDelegateFunction_UAuracronFoliageCollisionManager_OnPhysicsInteraction__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsInteraction_MetaData), NewProp_OnPhysicsInteraction_MetaData) }; // 3306354215
void Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageCollisionManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageCollisionManager), &Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3949760375
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageCollisionManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnCollisionMeshGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnFoliageDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnTramplingEffectCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_OnPhysicsInteraction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::ClassParams = {
	&UAuracronFoliageCollisionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageCollisionManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageCollisionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageCollisionManager.OuterSingleton;
}
UAuracronFoliageCollisionManager::UAuracronFoliageCollisionManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageCollisionManager);
UAuracronFoliageCollisionManager::~UAuracronFoliageCollisionManager() {}
// ********** End Class UAuracronFoliageCollisionManager *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronFoliageCollisionType_StaticEnum, TEXT("EAuracronFoliageCollisionType"), &Z_Registration_Info_UEnum_EAuracronFoliageCollisionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1409904130U) },
		{ EAuracronPhysicsInteractionType_StaticEnum, TEXT("EAuracronPhysicsInteractionType"), &Z_Registration_Info_UEnum_EAuracronPhysicsInteractionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 368324703U) },
		{ EAuracronDestructibleBehavior_StaticEnum, TEXT("EAuracronDestructibleBehavior"), &Z_Registration_Info_UEnum_EAuracronDestructibleBehavior, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
		{ EAuracronTramplingEffect_StaticEnum, TEXT("EAuracronTramplingEffect"), &Z_Registration_Info_UEnum_EAuracronTramplingEffect, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2136187517U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageCollisionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageCollisionConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageCollisionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageCollisionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageCollisionConfiguration), 3949760375U) },
		{ FAuracronCollisionMeshData::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionMeshData_Statics::NewStructOps, TEXT("AuracronCollisionMeshData"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionMeshData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionMeshData), 2728292572U) },
		{ FAuracronDestructibleFoliageData::StaticStruct, Z_Construct_UScriptStruct_FAuracronDestructibleFoliageData_Statics::NewStructOps, TEXT("AuracronDestructibleFoliageData"), &Z_Registration_Info_UScriptStruct_FAuracronDestructibleFoliageData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDestructibleFoliageData), 3044957000U) },
		{ FAuracronTramplingEffectData::StaticStruct, Z_Construct_UScriptStruct_FAuracronTramplingEffectData_Statics::NewStructOps, TEXT("AuracronTramplingEffectData"), &Z_Registration_Info_UScriptStruct_FAuracronTramplingEffectData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTramplingEffectData), 3592431104U) },
		{ FAuracronCollisionPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionPerformanceData_Statics::NewStructOps, TEXT("AuracronCollisionPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionPerformanceData), 2325588069U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageCollisionManager, UAuracronFoliageCollisionManager::StaticClass, TEXT("UAuracronFoliageCollisionManager"), &Z_Registration_Info_UClass_UAuracronFoliageCollisionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageCollisionManager), 2868813480U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_1659684154(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageCollision_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
