﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Epic Online Services Bridge
// IntegraÃ§Ã£o C++ para EOS usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "OnlineSubsystemEOS.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineFriendsInterface.h"
#include "Interfaces/OnlinePresenceInterface.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronEOSBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para status de conexÃ£o EOS
 */
UENUM(BlueprintType)
enum class EAuracronEOSConnectionStatus : uint8
{
    Disconnected        UMETA(DisplayName = "Disconnected"),
    Connecting          UMETA(DisplayName = "Connecting"),
    Connected           UMETA(DisplayName = "Connected"),
    Authenticating      UMETA(DisplayName = "Authenticating"),
    Authenticated       UMETA(DisplayName = "Authenticated"),
    Error               UMETA(DisplayName = "Error"),
    Banned              UMETA(DisplayName = "Banned"),
    Suspended           UMETA(DisplayName = "Suspended")
};

/**
 * EnumeraÃ§Ã£o para tipos de sessÃ£o
 */
UENUM(BlueprintType)
enum class EAuracronSessionType : uint8
{
    None                UMETA(DisplayName = "None"),
    Ranked              UMETA(DisplayName = "Ranked Match"),
    Casual              UMETA(DisplayName = "Casual Match"),
    Custom              UMETA(DisplayName = "Custom Game"),
    Training            UMETA(DisplayName = "Training"),
    Tournament          UMETA(DisplayName = "Tournament"),
    Event               UMETA(DisplayName = "Event Match")
};

/**
 * Estrutura para configuraÃ§Ã£o de sessÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONEOSBRIDG_API FAuracronSessionConfiguration
{
    GENERATED_BODY()

    /** Nome da sessÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString SessionName;

    /** Tipo de sessÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    EAuracronSessionType SessionType = EAuracronSessionType::Casual;

    /** NÃºmero mÃ¡ximo de jogadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "2", ClampMax = "10"))
    int32 MaxPlayers = 10;

    /** SessÃ£o Ã© privada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bIsPrivate = false;

    /** Usar anti-cheat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bUseAntiCheat = true;

    /** RegiÃ£o preferida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString PreferredRegion = TEXT("us-east-1");

    /** Mapa da sessÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString MapName = TEXT("AuracronArena");

    /** Modo de jogo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString GameMode = TEXT("5v5");

    /** ConfiguraÃ§Ãµes customizadas (não pode ser replicado) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", NotReplicated)
    TMap<FString, FString> CustomSettings;

    /** Senha da sessÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    FString SessionPassword;

    /** Permitir espectadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration")
    bool bAllowSpectators = false;

    /** NÃºmero mÃ¡ximo de espectadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Session Configuration", meta = (ClampMin = "0", ClampMax = "100"))
    int32 MaxSpectators = 0;
};

/**
 * Estrutura para amigo EOS
 */
USTRUCT(BlueprintType)
struct AURACRONEOSBRIDG_API FAuracronEOSFriend
{
    GENERATED_BODY()

    /** ID do usuÃ¡rio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FString UserID;

    /** Nome de exibiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FString DisplayName;

    /** Status online */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    bool bIsOnline = false;

    /** Status de presenÃ§a */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FString PresenceStatus;

    /** Jogando Auracron */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    bool bIsPlayingAuracron = false;

    /** Pode ser convidado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    bool bCanBeInvited = true;

    /** EstÃ¡ em partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    bool bIsInMatch = false;

    /** ID da sessÃ£o atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FString CurrentSessionID;

    /** Ãšltima vez online */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FDateTime LastOnlineTime;

    /** Avatar do usuÃ¡rio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    TSoftObjectPtr<UTexture2D> UserAvatar;

    /** NÃ­vel do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    int32 PlayerLevel = 1;

    /** Rank do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Friend")
    FString PlayerRank = TEXT("Bronze");
};

/**
 * Estrutura para achievement EOS
 */
USTRUCT(BlueprintType)
struct AURACRONEOSBRIDG_API FAuracronEOSAchievement
{
    GENERATED_BODY()

    /** ID do achievement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    FString AchievementID;

    /** Nome do achievement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    FText AchievementName;

    /** DescriÃ§Ã£o do achievement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    FText AchievementDescription;

    /** Foi desbloqueado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    bool bIsUnlocked = false;

    /** Progresso atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Progress = 0.0f;

    /** Data de desbloqueio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    FDateTime UnlockDate;

    /** Ã‰ achievement secreto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    bool bIsSecret = false;

    /** Pontos concedidos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement", meta = (ClampMin = "0"))
    int32 Points = 0;

    /** Ãcone do achievement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement")
    TSoftObjectPtr<UTexture2D> AchievementIcon;

    /** Raridade do achievement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EOS Achievement", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Rarity = 1.0f; // 1.0 = comum, 0.01 = muito raro
};

/**
 * Classe principal do Bridge para Epic Online Services
 * ResponsÃ¡vel pela integraÃ§Ã£o completa com EOS
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|EOS", meta = (DisplayName = "AURACRON EOS Bridge", BlueprintSpawnableComponent))
class AURACRONEOSBRIDG_API UAuracronEOSBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronEOSBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Authentication ===

    /**
     * Fazer login com EOS
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Authentication", CallInEditor)
    bool LoginWithEOS(const FString& LoginType = TEXT("AccountPortal"));

    /**
     * Fazer logout
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Authentication", CallInEditor)
    bool LogoutFromEOS();

    /**
     * Verificar status de autenticaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Authentication", CallInEditor)
    EAuracronEOSConnectionStatus GetAuthenticationStatus() const;

    /**
     * Obter ID do usuÃ¡rio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Authentication", CallInEditor)
    FString GetUserID() const;

    /**
     * Obter nome de exibiÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Authentication", CallInEditor)
    FString GetDisplayName() const;

    // === Sessions ===

    /**
     * Criar sessÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Sessions", CallInEditor)
    bool CreateSession(const FAuracronSessionConfiguration& SessionConfig);

    /**
     * Encontrar sessÃµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Sessions", CallInEditor)
    bool FindSessions(EAuracronSessionType SessionType, const FString& Region = TEXT(""));

    /**
     * Entrar em sessÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Sessions", CallInEditor)
    bool JoinSession(const FString& SessionID);

    /**
     * Sair da sessÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Sessions", CallInEditor)
    bool LeaveSession();

    /**
     * Destruir sessÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Sessions", CallInEditor)
    bool DestroySession();

    // === Friends ===

    /**
     * Carregar lista de amigos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Friends", CallInEditor)
    bool LoadFriendsList();

    /**
     * Obter lista de amigos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Friends", CallInEditor)
    TArray<FAuracronEOSFriend> GetFriendsList() const;

    /**
     * Adicionar amigo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Friends", CallInEditor)
    bool AddFriend(const FString& UserID);

    /**
     * Remover amigo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Friends", CallInEditor)
    bool RemoveFriend(const FString& UserID);

    /**
     * Convidar amigo para sessÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Friends", CallInEditor)
    bool InviteFriendToSession(const FString& FriendID);

    // === Achievements ===

    /**
     * Desbloquear achievement
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Achievements", CallInEditor)
    bool UnlockAchievement(const FString& AchievementID);

    /**
     * Atualizar progresso de achievement
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Achievements", CallInEditor)
    bool UpdateAchievementProgress(const FString& AchievementID, float Progress);

    /**
     * Obter achievements
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Achievements", CallInEditor)
    TArray<FAuracronEOSAchievement> GetAchievements() const;

    /**
     * Verificar se achievement foi desbloqueado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Achievements", CallInEditor)
    bool IsAchievementUnlocked(const FString& AchievementID) const;

    // === Leaderboards ===

    /**
     * Enviar score para leaderboard
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Leaderboards", CallInEditor)
    bool SubmitScore(const FString& LeaderboardID, int32 Score);

    /**
     * Obter ranking do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Leaderboards", CallInEditor)
    int32 GetPlayerRank(const FString& LeaderboardID) const;

    /**
     * Obter top rankings
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Leaderboards", CallInEditor)
    TArray<FString> GetTopRankings(const FString& LeaderboardID, int32 Count = 10) const;

    // === Player Data ===

    /**
     * Salvar dados do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|PlayerData", CallInEditor)
    bool SavePlayerData(const FString& Key, const FString& Data);

    /**
     * Carregar dados do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|PlayerData", CallInEditor)
    FString LoadPlayerData(const FString& Key);

    /**
     * Deletar dados do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|PlayerData", CallInEditor)
    bool DeletePlayerData(const FString& Key);

    // === Presence ===

    /**
     * Definir presenÃ§a
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Presence", CallInEditor)
    bool SetPresence(const FString& Status, const TMap<FString, FString>& Properties);

    /**
     * Obter presenÃ§a de amigo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Presence", CallInEditor)
    FString GetFriendPresence(const FString& FriendID) const;

    // === Metrics ===

    /**
     * Enviar mÃ©trica personalizada
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Metrics", CallInEditor)
    bool SendCustomMetric(const FString& MetricName, const TMap<FString, FString>& Properties);

    /**
     * Rastrear evento de jogo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON EOS|Metrics", CallInEditor)
    bool TrackGameEvent(const FString& EventName, const TMap<FString, float>& NumericData, const TMap<FString, FString>& StringData);

protected:
    // === Internal Methods ===
    
    /** Inicializar EOS */
    bool InitializeEOS();
    
    /** Configurar callbacks */
    bool SetupEOSCallbacks();
    
    /** Processar callbacks EOS */
    void ProcessEOSCallbacks(float DeltaTime);
    
    /** Validar configuraÃ§Ã£o de sessÃ£o */
    bool ValidateSessionConfiguration(const FAuracronSessionConfiguration& Config) const;

public:
    // === Configuration Properties ===

    /** Status de conexÃ£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_ConnectionStatus)
    EAuracronEOSConnectionStatus CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;

    /** ID do usuÃ¡rio atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    FString CurrentUserID;

    /** Nome de exibiÃ§Ã£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    FString CurrentDisplayName;

    /** Lista de amigos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronEOSFriend> FriendsList;

    /** Achievements do jogador */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronEOSAchievement> PlayerAchievements;

    /** SessÃ£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    FAuracronSessionConfiguration CurrentSession;

    /** EstÃ¡ em sessÃ£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    bool bIsInSession = false;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para callbacks */
    FTimerHandle CallbackTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection EOSMutex;

    // === Replication Callbacks ===
    
    UFUNCTION()
    void OnRep_ConnectionStatus();

public:
    // === Delegates ===
    
    /** Delegate chamado quando login Ã© completado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEOSLoginCompleted, bool, bWasSuccessful, FString, ErrorMessage);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON EOS|Events")
    FOnEOSLoginCompleted OnEOSLoginCompleted;
    
    /** Delegate chamado quando sessÃ£o Ã© criada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSessionCreated, bool, bWasSuccessful, FString, SessionID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON EOS|Events")
    FOnSessionCreated OnSessionCreated;
    
    /** Delegate chamado quando achievement Ã© desbloqueado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAchievementUnlocked, FAuracronEOSAchievement, Achievement);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON EOS|Events")
    FOnAchievementUnlocked OnAchievementUnlocked;
    
    /** Delegate chamado quando amigo fica online */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFriendOnline, FAuracronEOSFriend, Friend);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON EOS|Events")
    FOnFriendOnline OnFriendOnline;
};

