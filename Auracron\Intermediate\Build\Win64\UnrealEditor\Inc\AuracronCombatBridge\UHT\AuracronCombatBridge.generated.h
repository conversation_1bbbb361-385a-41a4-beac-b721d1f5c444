// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronCombatBridge.h"

#ifdef AURACRONCOMBATBRIDGE_AuracronCombatBridge_generated_h
#error "AuracronCombatBridge.generated.h already included, missing '#pragma once' in AuracronCombatBridge.h"
#endif
#define AURACRONCOMBATBRIDGE_AuracronCombatBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAuracronCombatLayer : uint8;
enum class EAuracronDamageType : uint8;
struct FAuracronCombatEffectsConfiguration;
struct FAuracronDamageConfiguration;
struct FAuracronTargetingConfiguration;
struct FAuracronTargetingResult;

// ********** Begin ScriptStruct FAuracronDamageConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_110_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDamageConfiguration;
// ********** End ScriptStruct FAuracronDamageConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronTargetingConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_175_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTargetingConfiguration;
// ********** End ScriptStruct FAuracronTargetingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronTargetingResult ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_252_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTargetingResult;
// ********** End ScriptStruct FAuracronTargetingResult ********************************************

// ********** Begin ScriptStruct FAuracronCombatEffectsConfiguration *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_293_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCombatEffectsConfiguration;
// ********** End ScriptStruct FAuracronCombatEffectsConfiguration *********************************

// ********** Begin Delegate FOnTargetingExecuted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_533_DELEGATE \
static void FOnTargetingExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnTargetingExecuted, FAuracronTargetingResult Result, FAuracronTargetingConfiguration Config);


// ********** End Delegate FOnTargetingExecuted ****************************************************

// ********** Begin Delegate FOnDamageApplied ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_538_DELEGATE \
static void FOnDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnDamageApplied, AActor* TargetActor, float DamageAmount, EAuracronDamageType DamageType);


// ********** End Delegate FOnDamageApplied ********************************************************

// ********** Begin Delegate FOnCombatLayerChanged *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_543_DELEGATE \
static void FOnCombatLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCombatLayerChanged, EAuracronCombatLayer OldLayer, EAuracronCombatLayer NewLayer);


// ********** End Delegate FOnCombatLayerChanged ***************************************************

// ********** Begin Delegate FOnCombatEffectsSpawned ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_548_DELEGATE \
static void FOnCombatEffectsSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnCombatEffectsSpawned, FVector Location, FAuracronCombatEffectsConfiguration EffectsConfig);


// ********** End Delegate FOnCombatEffectsSpawned *************************************************

// ********** Begin Class UAuracronCombatBridge ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_CurrentCombatLayer); \
	DECLARE_FUNCTION(execCreateChaosExplosion); \
	DECLARE_FUNCTION(execApplyFieldSystemDestruction); \
	DECLARE_FUNCTION(execSpawnCombatEffects); \
	DECLARE_FUNCTION(execGetActorCombatLayer); \
	DECLARE_FUNCTION(execIsValidTarget); \
	DECLARE_FUNCTION(execCalculateFinalDamage); \
	DECLARE_FUNCTION(execGetTargetsInVerticalColumn); \
	DECLARE_FUNCTION(execGetTargetsInCone); \
	DECLARE_FUNCTION(execGetTargetsInRadius); \
	DECLARE_FUNCTION(execCheckLineOfSight); \
	DECLARE_FUNCTION(execApplyAreaDamage); \
	DECLARE_FUNCTION(execApplyDamageToTarget); \
	DECLARE_FUNCTION(execExecuteTargeting);


AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronCombatBridge(); \
	friend struct Z_Construct_UClass_UAuracronCombatBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronCombatBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronCombatBridge"), Z_Construct_UClass_UAuracronCombatBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronCombatBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentCombatLayer=NETFIELD_REP_START, \
		NETFIELD_REP_END=CurrentCombatLayer	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronCombatBridge(UAuracronCombatBridge&&) = delete; \
	UAuracronCombatBridge(const UAuracronCombatBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronCombatBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronCombatBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronCombatBridge) \
	NO_API virtual ~UAuracronCombatBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_356_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_359_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronCombatBridge;

// ********** End Class UAuracronCombatBridge ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h

// ********** Begin Enum EAuracronDamageType *******************************************************
#define FOREACH_ENUM_EAURACRONDAMAGETYPE(op) \
	op(EAuracronDamageType::None) \
	op(EAuracronDamageType::Physical) \
	op(EAuracronDamageType::Magical) \
	op(EAuracronDamageType::TrueDamage) \
	op(EAuracronDamageType::Healing) \
	op(EAuracronDamageType::Shield) \
	op(EAuracronDamageType::Percentage) \
	op(EAuracronDamageType::OverTime) \
	op(EAuracronDamageType::Area) 

enum class EAuracronDamageType : uint8;
template<> struct TIsUEnumClass<EAuracronDamageType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronDamageType>();
// ********** End Enum EAuracronDamageType *********************************************************

// ********** Begin Enum EAuracronCombatLayer ******************************************************
#define FOREACH_ENUM_EAURACRONCOMBATLAYER(op) \
	op(EAuracronCombatLayer::Surface) \
	op(EAuracronCombatLayer::Sky) \
	op(EAuracronCombatLayer::Underground) \
	op(EAuracronCombatLayer::All) 

enum class EAuracronCombatLayer : uint8;
template<> struct TIsUEnumClass<EAuracronCombatLayer> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronCombatLayer>();
// ********** End Enum EAuracronCombatLayer ********************************************************

// ********** Begin Enum EAuracronTargetingType ****************************************************
#define FOREACH_ENUM_EAURACRONTARGETINGTYPE(op) \
	op(EAuracronTargetingType::None) \
	op(EAuracronTargetingType::SingleTarget) \
	op(EAuracronTargetingType::LineTrace) \
	op(EAuracronTargetingType::AreaOfEffect) \
	op(EAuracronTargetingType::Cone) \
	op(EAuracronTargetingType::Sphere) \
	op(EAuracronTargetingType::Box) \
	op(EAuracronTargetingType::Cylinder) \
	op(EAuracronTargetingType::VerticalColumn) \
	op(EAuracronTargetingType::CrossLayer) \
	op(EAuracronTargetingType::GroundTarget) \
	op(EAuracronTargetingType::SkyTarget) \
	op(EAuracronTargetingType::UndergroundTarget) 

enum class EAuracronTargetingType : uint8;
template<> struct TIsUEnumClass<EAuracronTargetingType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronTargetingType>();
// ********** End Enum EAuracronTargetingType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
