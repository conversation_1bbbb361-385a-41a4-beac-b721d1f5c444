// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanDNAReaderWriter.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanDNAReaderWriter() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBlendShapeData();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAData();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAReadResult();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAWriteResult();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVertexDelta();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronVertexDelta **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVertexDelta;
class UScriptStruct* FAuracronVertexDelta::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVertexDelta, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronVertexDelta"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === DNA Data Structures ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== DNA Data Structures ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexIndex_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionDelta_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalDelta_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_VertexIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionDelta;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NormalDelta;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVertexDelta>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_VertexIndex = { "VertexIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVertexDelta, VertexIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexIndex_MetaData), NewProp_VertexIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_PositionDelta = { "PositionDelta", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVertexDelta, PositionDelta), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionDelta_MetaData), NewProp_PositionDelta_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_NormalDelta = { "NormalDelta", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVertexDelta, NormalDelta), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalDelta_MetaData), NewProp_NormalDelta_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_VertexIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_PositionDelta,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewProp_NormalDelta,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronVertexDelta",
	Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::PropPointers),
	sizeof(FAuracronVertexDelta),
	alignof(FAuracronVertexDelta),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVertexDelta()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVertexDelta.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVertexDelta ************************************************

// ********** Begin ScriptStruct FAuracronBlendShapeData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData;
class UScriptStruct* FAuracronBlendShapeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBlendShapeData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronBlendShapeData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexDeltas_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VertexDeltas_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VertexDeltas;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBlendShapeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBlendShapeData, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_VertexDeltas_Inner = { "VertexDeltas", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronVertexDelta, METADATA_PARAMS(0, nullptr) }; // 2443803695
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_VertexDeltas = { "VertexDeltas", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBlendShapeData, VertexDeltas), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexDeltas_MetaData), NewProp_VertexDeltas_MetaData) }; // 2443803695
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_VertexDeltas_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewProp_VertexDeltas,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronBlendShapeData",
	Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::PropPointers),
	sizeof(FAuracronBlendShapeData),
	alignof(FAuracronBlendShapeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBlendShapeData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBlendShapeData *********************************************

// ********** Begin ScriptStruct FAuracronDNAData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAData;
class UScriptStruct* FAuracronDNAData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneNames_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoneParentIndices_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeNames_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeDeltas_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Version_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BoneNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BoneNames;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BoneParentIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BoneParentIndices;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendShapeNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendShapeNames;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlendShapeDeltas_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendShapeDeltas;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Version;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneNames_Inner = { "BoneNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneNames = { "BoneNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, BoneNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneNames_MetaData), NewProp_BoneNames_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneParentIndices_Inner = { "BoneParentIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneParentIndices = { "BoneParentIndices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, BoneParentIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoneParentIndices_MetaData), NewProp_BoneParentIndices_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeNames_Inner = { "BlendShapeNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeNames = { "BlendShapeNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, BlendShapeNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeNames_MetaData), NewProp_BlendShapeNames_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeDeltas_Inner = { "BlendShapeDeltas", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronBlendShapeData, METADATA_PARAMS(0, nullptr) }; // 1657338270
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeDeltas = { "BlendShapeDeltas", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, BlendShapeDeltas), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeDeltas_MetaData), NewProp_BlendShapeDeltas_MetaData) }; // 1657338270
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_Version = { "Version", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, Version), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Version_MetaData), NewProp_Version_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneParentIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BoneParentIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeDeltas_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_BlendShapeDeltas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_Version,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAData",
	Z_Construct_UScriptStruct_FAuracronDNAData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAData_Statics::PropPointers),
	sizeof(FAuracronDNAData),
	alignof(FAuracronDNAData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAData ****************************************************

// ********** Begin ScriptStruct FAuracronDNAReadResult ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult;
class UScriptStruct* FAuracronDNAReadResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAReadResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAReadResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAReadResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronDNAReadResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDNAReadResult), &Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAReadResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAReadResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewProp_ProcessingTimeMS,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAReadResult",
	Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::PropPointers),
	sizeof(FAuracronDNAReadResult),
	alignof(FAuracronDNAReadResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAReadResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAReadResult **********************************************

// ********** Begin ScriptStruct FAuracronDNAWriteResult *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult;
class UScriptStruct* FAuracronDNAWriteResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAWriteResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAWriteResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BytesWritten_MetaData[] = {
		{ "Category", "DNA" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BytesWritten;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAWriteResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronDNAWriteResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDNAWriteResult), &Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAWriteResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAWriteResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_BytesWritten = { "BytesWritten", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAWriteResult, BytesWritten), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BytesWritten_MetaData), NewProp_BytesWritten_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_ProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewProp_BytesWritten,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAWriteResult",
	Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::PropPointers),
	sizeof(FAuracronDNAWriteResult),
	alignof(FAuracronDNAWriteResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAWriteResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAWriteResult *********************************************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function GetCurrentDNAData *************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventGetCurrentDNAData_Parms
	{
		FAuracronDNAData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Status Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Status Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventGetCurrentDNAData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAData, METADATA_PARAMS(0, nullptr) }; // 3099763100
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "GetCurrentDNAData", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::AuracronMetaHumanDNAReaderWriter_eventGetCurrentDNAData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::AuracronMetaHumanDNAReaderWriter_eventGetCurrentDNAData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execGetCurrentDNAData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAData*)Z_Param__Result=P_THIS->GetCurrentDNAData();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function GetCurrentDNAData ***************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function GetReaderStatus ***************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventGetReaderStatus_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventGetReaderStatus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "GetReaderStatus", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::AuracronMetaHumanDNAReaderWriter_eventGetReaderStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::AuracronMetaHumanDNAReaderWriter_eventGetReaderStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execGetReaderStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetReaderStatus();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function GetReaderStatus *****************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function GetWriterStatus ***************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventGetWriterStatus_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventGetWriterStatus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "GetWriterStatus", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::AuracronMetaHumanDNAReaderWriter_eventGetWriterStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::AuracronMetaHumanDNAReaderWriter_eventGetWriterStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execGetWriterStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetWriterStatus();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function GetWriterStatus *****************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function Initialize ********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventInitialize_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNAReaderWriter_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNAReaderWriter_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::AuracronMetaHumanDNAReaderWriter_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::AuracronMetaHumanDNAReaderWriter_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execInitialize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function Initialize **********************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function IsReaderReady *****************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventIsReaderReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNAReaderWriter_eventIsReaderReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNAReaderWriter_eventIsReaderReady_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "IsReaderReady", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::AuracronMetaHumanDNAReaderWriter_eventIsReaderReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::AuracronMetaHumanDNAReaderWriter_eventIsReaderReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execIsReaderReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsReaderReady();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function IsReaderReady *******************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function IsWriterReady *****************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventIsWriterReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNAReaderWriter_eventIsWriterReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNAReaderWriter_eventIsWriterReady_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "IsWriterReady", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::AuracronMetaHumanDNAReaderWriter_eventIsWriterReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::AuracronMetaHumanDNAReaderWriter_eventIsWriterReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execIsWriterReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWriterReady();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function IsWriterReady *******************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function ReadDNAFromFile ***************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventReadDNAFromFile_Parms
	{
		FString FilePath;
		FAuracronDNAReadResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Reading Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Reading Functions ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventReadDNAFromFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventReadDNAFromFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAReadResult, METADATA_PARAMS(0, nullptr) }; // 1647604152
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "ReadDNAFromFile", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::AuracronMetaHumanDNAReaderWriter_eventReadDNAFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::AuracronMetaHumanDNAReaderWriter_eventReadDNAFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execReadDNAFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAReadResult*)Z_Param__Result=P_THIS->ReadDNAFromFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function ReadDNAFromFile *****************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function ReadDNAFromMemory *************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventReadDNAFromMemory_Parms
	{
		TArray<uint8> DNAData;
		FAuracronDNAReadResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DNAData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DNAData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DNAData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_DNAData_Inner = { "DNAData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_DNAData = { "DNAData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventReadDNAFromMemory_Parms, DNAData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DNAData_MetaData), NewProp_DNAData_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventReadDNAFromMemory_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAReadResult, METADATA_PARAMS(0, nullptr) }; // 1647604152
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_DNAData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_DNAData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "ReadDNAFromMemory", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::AuracronMetaHumanDNAReaderWriter_eventReadDNAFromMemory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::AuracronMetaHumanDNAReaderWriter_eventReadDNAFromMemory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execReadDNAFromMemory)
{
	P_GET_TARRAY_REF(uint8,Z_Param_Out_DNAData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAReadResult*)Z_Param__Result=P_THIS->ReadDNAFromMemory(Z_Param_Out_DNAData);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function ReadDNAFromMemory ***************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function Shutdown **********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function Shutdown ************************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function ValidateDNAData ***************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms
	{
		FAuracronDNAData DNAData;
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Validation Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Validation Functions ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DNAData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DNAData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_DNAData = { "DNAData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms, DNAData), Z_Construct_UScriptStruct_FAuracronDNAData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DNAData_MetaData), NewProp_DNAData_MetaData) }; // 3099763100
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_DNAData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "ValidateDNAData", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::AuracronMetaHumanDNAReaderWriter_eventValidateDNAData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execValidateDNAData)
{
	P_GET_STRUCT_REF(FAuracronDNAData,Z_Param_Out_DNAData);
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateDNAData(Z_Param_Out_DNAData,Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function ValidateDNAData *****************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function WriteDNAToFile ****************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms
	{
		FString FilePath;
		FAuracronDNAData DNAData;
		FAuracronDNAWriteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Writing Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Writing Functions ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DNAData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DNAData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_DNAData = { "DNAData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms, DNAData), Z_Construct_UScriptStruct_FAuracronDNAData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DNAData_MetaData), NewProp_DNAData_MetaData) }; // 3099763100
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAWriteResult, METADATA_PARAMS(0, nullptr) }; // 1350835428
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_DNAData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "WriteDNAToFile", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::AuracronMetaHumanDNAReaderWriter_eventWriteDNAToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execWriteDNAToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_GET_STRUCT_REF(FAuracronDNAData,Z_Param_Out_DNAData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAWriteResult*)Z_Param__Result=P_THIS->WriteDNAToFile(Z_Param_FilePath,Z_Param_Out_DNAData);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function WriteDNAToFile ******************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter Function WriteDNAToMemory **************
struct Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics
{
	struct AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms
	{
		FAuracronDNAData DNAData;
		TArray<uint8> OutData;
		FAuracronDNAWriteResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON MetaHuman|DNA|ReaderWriter" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DNAData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DNAData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OutData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_DNAData = { "DNAData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms, DNAData), Z_Construct_UScriptStruct_FAuracronDNAData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DNAData_MetaData), NewProp_DNAData_MetaData) }; // 3099763100
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_OutData_Inner = { "OutData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_OutData = { "OutData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms, OutData), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAWriteResult, METADATA_PARAMS(0, nullptr) }; // 1350835428
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_DNAData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_OutData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_OutData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, nullptr, "WriteDNAToMemory", Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::AuracronMetaHumanDNAReaderWriter_eventWriteDNAToMemory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNAReaderWriter::execWriteDNAToMemory)
{
	P_GET_STRUCT_REF(FAuracronDNAData,Z_Param_Out_DNAData);
	P_GET_TARRAY_REF(uint8,Z_Param_Out_OutData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAWriteResult*)Z_Param__Result=P_THIS->WriteDNAToMemory(Z_Param_Out_DNAData,Z_Param_Out_OutData);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNAReaderWriter Function WriteDNAToMemory ****************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter ****************************************
void UAuracronMetaHumanDNAReaderWriter::StaticRegisterNativesUAuracronMetaHumanDNAReaderWriter()
{
	UClass* Class = UAuracronMetaHumanDNAReaderWriter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetCurrentDNAData", &UAuracronMetaHumanDNAReaderWriter::execGetCurrentDNAData },
		{ "GetReaderStatus", &UAuracronMetaHumanDNAReaderWriter::execGetReaderStatus },
		{ "GetWriterStatus", &UAuracronMetaHumanDNAReaderWriter::execGetWriterStatus },
		{ "Initialize", &UAuracronMetaHumanDNAReaderWriter::execInitialize },
		{ "IsReaderReady", &UAuracronMetaHumanDNAReaderWriter::execIsReaderReady },
		{ "IsWriterReady", &UAuracronMetaHumanDNAReaderWriter::execIsWriterReady },
		{ "ReadDNAFromFile", &UAuracronMetaHumanDNAReaderWriter::execReadDNAFromFile },
		{ "ReadDNAFromMemory", &UAuracronMetaHumanDNAReaderWriter::execReadDNAFromMemory },
		{ "Shutdown", &UAuracronMetaHumanDNAReaderWriter::execShutdown },
		{ "ValidateDNAData", &UAuracronMetaHumanDNAReaderWriter::execValidateDNAData },
		{ "WriteDNAToFile", &UAuracronMetaHumanDNAReaderWriter::execWriteDNAToFile },
		{ "WriteDNAToMemory", &UAuracronMetaHumanDNAReaderWriter::execWriteDNAToMemory },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter;
UClass* UAuracronMetaHumanDNAReaderWriter::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanDNAReaderWriter;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanDNAReaderWriter"),
			Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanDNAReaderWriter,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_NoRegister()
{
	return UAuracronMetaHumanDNAReaderWriter::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MetaHuman DNA Reader/Writer System\n * Handles reading and writing of MetaHuman DNA data using UE5.6 APIs\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanDNAReaderWriter.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MetaHuman DNA Reader/Writer System\nHandles reading and writing of MetaHuman DNA data using UE5.6 APIs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNAReaderWriter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetCurrentDNAData, "GetCurrentDNAData" }, // 3847835740
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetReaderStatus, "GetReaderStatus" }, // 1288631848
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_GetWriterStatus, "GetWriterStatus" }, // 111779341
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Initialize, "Initialize" }, // 253259623
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsReaderReady, "IsReaderReady" }, // 1296486096
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_IsWriterReady, "IsWriterReady" }, // 1394597434
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromFile, "ReadDNAFromFile" }, // 3810991324
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ReadDNAFromMemory, "ReadDNAFromMemory" }, // 4073085596
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_Shutdown, "Shutdown" }, // 3153195728
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_ValidateDNAData, "ValidateDNAData" }, // 3746201134
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToFile, "WriteDNAToFile" }, // 696337259
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNAReaderWriter_WriteDNAToMemory, "WriteDNAToMemory" }, // 2933723082
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanDNAReaderWriter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanDNAReaderWriter*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanDNAReaderWriter), &Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::NewProp_bIsInitialized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::ClassParams = {
	&UAuracronMetaHumanDNAReaderWriter::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanDNAReaderWriter);
// ********** End Class UAuracronMetaHumanDNAReaderWriter ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronVertexDelta::StaticStruct, Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics::NewStructOps, TEXT("AuracronVertexDelta"), &Z_Registration_Info_UScriptStruct_FAuracronVertexDelta, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVertexDelta), 2443803695U) },
		{ FAuracronBlendShapeData::StaticStruct, Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics::NewStructOps, TEXT("AuracronBlendShapeData"), &Z_Registration_Info_UScriptStruct_FAuracronBlendShapeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBlendShapeData), 1657338270U) },
		{ FAuracronDNAData::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAData_Statics::NewStructOps, TEXT("AuracronDNAData"), &Z_Registration_Info_UScriptStruct_FAuracronDNAData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAData), 3099763100U) },
		{ FAuracronDNAReadResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics::NewStructOps, TEXT("AuracronDNAReadResult"), &Z_Registration_Info_UScriptStruct_FAuracronDNAReadResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAReadResult), 1647604152U) },
		{ FAuracronDNAWriteResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics::NewStructOps, TEXT("AuracronDNAWriteResult"), &Z_Registration_Info_UScriptStruct_FAuracronDNAWriteResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAWriteResult), 1350835428U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter, UAuracronMetaHumanDNAReaderWriter::StaticClass, TEXT("UAuracronMetaHumanDNAReaderWriter"), &Z_Registration_Info_UClass_UAuracronMetaHumanDNAReaderWriter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanDNAReaderWriter), 2979192918U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_2729664994(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
