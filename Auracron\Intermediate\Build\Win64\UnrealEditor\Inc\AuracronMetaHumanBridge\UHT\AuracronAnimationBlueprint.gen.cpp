// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronAnimationBlueprint.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAnimationBlueprint() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAnimationBlueprintLODData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAnimationStringArrayWrapper();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FEmotionMappingData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FFacialAnimationData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FLipSyncData();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundWave_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAnimationStringArrayWrapper **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper;
class UScriptStruct* FAnimationStringArrayWrapper::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAnimationStringArrayWrapper, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AnimationStringArrayWrapper"));
	}
	return Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para TArray<FString> em TMap (Animation Blueprint)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para TArray<FString> em TMap (Animation Blueprint)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strings_MetaData[] = {
		{ "Category", "String Array" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Strings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Strings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAnimationStringArrayWrapper>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::NewProp_Strings_Inner = { "Strings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::NewProp_Strings = { "Strings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationStringArrayWrapper, Strings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strings_MetaData), NewProp_Strings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::NewProp_Strings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::NewProp_Strings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AnimationStringArrayWrapper",
	Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::PropPointers),
	sizeof(FAnimationStringArrayWrapper),
	alignof(FAnimationStringArrayWrapper),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAnimationStringArrayWrapper()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.InnerSingleton, Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper.InnerSingleton;
}
// ********** End ScriptStruct FAnimationStringArrayWrapper ****************************************

// ********** Begin Enum EAnimationBlueprintType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAnimationBlueprintType;
static UEnum* EAnimationBlueprintType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAnimationBlueprintType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAnimationBlueprintType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EAnimationBlueprintType"));
	}
	return Z_Registration_Info_UEnum_EAnimationBlueprintType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAnimationBlueprintType>()
{
	return EAnimationBlueprintType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EAnimationBlueprintType::Advanced" },
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EAnimationBlueprintType::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for animation blueprint generation\n" },
#endif
		{ "Emotion.DisplayName", "Emotion" },
		{ "Emotion.Name", "EAnimationBlueprintType::Emotion" },
		{ "Facial.DisplayName", "Facial" },
		{ "Facial.Name", "EAnimationBlueprintType::Facial" },
		{ "FullBody.DisplayName", "Full Body" },
		{ "FullBody.Name", "EAnimationBlueprintType::FullBody" },
		{ "LipSync.DisplayName", "Lip Sync" },
		{ "LipSync.Name", "EAnimationBlueprintType::LipSync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for animation blueprint generation" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAnimationBlueprintType::Basic", (int64)EAnimationBlueprintType::Basic },
		{ "EAnimationBlueprintType::Facial", (int64)EAnimationBlueprintType::Facial },
		{ "EAnimationBlueprintType::FullBody", (int64)EAnimationBlueprintType::FullBody },
		{ "EAnimationBlueprintType::LipSync", (int64)EAnimationBlueprintType::LipSync },
		{ "EAnimationBlueprintType::Emotion", (int64)EAnimationBlueprintType::Emotion },
		{ "EAnimationBlueprintType::Advanced", (int64)EAnimationBlueprintType::Advanced },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EAnimationBlueprintType",
	"EAnimationBlueprintType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType()
{
	if (!Z_Registration_Info_UEnum_EAnimationBlueprintType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAnimationBlueprintType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAnimationBlueprintType.InnerSingleton;
}
// ********** End Enum EAnimationBlueprintType *****************************************************

// ********** Begin Enum EFacialAnimationType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EFacialAnimationType;
static UEnum* EFacialAnimationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EFacialAnimationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EFacialAnimationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EFacialAnimationType"));
	}
	return Z_Registration_Info_UEnum_EFacialAnimationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EFacialAnimationType>()
{
	return EFacialAnimationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlendShapes.DisplayName", "Blend Shapes" },
		{ "BlendShapes.Name", "EFacialAnimationType::BlendShapes" },
		{ "BlueprintType", "true" },
		{ "BoneTransforms.DisplayName", "Bone Transforms" },
		{ "BoneTransforms.Name", "EFacialAnimationType::BoneTransforms" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EFacialAnimationType::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EFacialAnimationType::BlendShapes", (int64)EFacialAnimationType::BlendShapes },
		{ "EFacialAnimationType::BoneTransforms", (int64)EFacialAnimationType::BoneTransforms },
		{ "EFacialAnimationType::Hybrid", (int64)EFacialAnimationType::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EFacialAnimationType",
	"EFacialAnimationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType()
{
	if (!Z_Registration_Info_UEnum_EFacialAnimationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EFacialAnimationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EFacialAnimationType.InnerSingleton;
}
// ********** End Enum EFacialAnimationType ********************************************************

// ********** Begin Enum ELipSyncMethod ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELipSyncMethod;
static UEnum* ELipSyncMethod_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELipSyncMethod.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELipSyncMethod.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ELipSyncMethod"));
	}
	return Z_Registration_Info_UEnum_ELipSyncMethod.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ELipSyncMethod>()
{
	return ELipSyncMethod_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AudioAnalysis.DisplayName", "Audio Analysis" },
		{ "AudioAnalysis.Name", "ELipSyncMethod::AudioAnalysis" },
		{ "BlueprintType", "true" },
		{ "ML.DisplayName", "Machine Learning" },
		{ "ML.Name", "ELipSyncMethod::ML" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
		{ "Phoneme.DisplayName", "Phoneme Based" },
		{ "Phoneme.Name", "ELipSyncMethod::Phoneme" },
		{ "Viseme.DisplayName", "Viseme Based" },
		{ "Viseme.Name", "ELipSyncMethod::Viseme" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELipSyncMethod::Phoneme", (int64)ELipSyncMethod::Phoneme },
		{ "ELipSyncMethod::Viseme", (int64)ELipSyncMethod::Viseme },
		{ "ELipSyncMethod::AudioAnalysis", (int64)ELipSyncMethod::AudioAnalysis },
		{ "ELipSyncMethod::ML", (int64)ELipSyncMethod::ML },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ELipSyncMethod",
	"ELipSyncMethod",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod()
{
	if (!Z_Registration_Info_UEnum_ELipSyncMethod.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELipSyncMethod.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELipSyncMethod.InnerSingleton;
}
// ********** End Enum ELipSyncMethod **************************************************************

// ********** Begin Enum EEmotionBlendMode *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEmotionBlendMode;
static UEnum* EEmotionBlendMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEmotionBlendMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEmotionBlendMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EEmotionBlendMode"));
	}
	return Z_Registration_Info_UEnum_EEmotionBlendMode.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEmotionBlendMode>()
{
	return EEmotionBlendMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Additive.DisplayName", "Additive" },
		{ "Additive.Name", "EEmotionBlendMode::Additive" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EEmotionBlendMode::Multiply" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "EEmotionBlendMode::Overlay" },
		{ "Replace.DisplayName", "Replace" },
		{ "Replace.Name", "EEmotionBlendMode::Replace" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEmotionBlendMode::Replace", (int64)EEmotionBlendMode::Replace },
		{ "EEmotionBlendMode::Additive", (int64)EEmotionBlendMode::Additive },
		{ "EEmotionBlendMode::Multiply", (int64)EEmotionBlendMode::Multiply },
		{ "EEmotionBlendMode::Overlay", (int64)EEmotionBlendMode::Overlay },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EEmotionBlendMode",
	"EEmotionBlendMode",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode()
{
	if (!Z_Registration_Info_UEnum_EEmotionBlendMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEmotionBlendMode.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEmotionBlendMode.InnerSingleton;
}
// ********** End Enum EEmotionBlendMode ***********************************************************

// ********** Begin ScriptStruct FFacialAnimationData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FFacialAnimationData;
class UScriptStruct* FFacialAnimationData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FFacialAnimationData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FFacialAnimationData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FFacialAnimationData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("FacialAnimationData"));
	}
	return Z_Registration_Info_UScriptStruct_FFacialAnimationData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FFacialAnimationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for animation blueprint generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for animation blueprint generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationType_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeNames_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlBoneNames_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEyeTracking_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEyeBlink_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEyebrowControl_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableJawControl_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeMultiplier_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "Facial Animation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AnimationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AnimationType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendShapeNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendShapeNames;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ControlBoneNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ControlBoneNames;
	static void NewProp_bEnableEyeTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEyeTracking;
	static void NewProp_bEnableEyeBlink_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEyeBlink;
	static void NewProp_bEnableEyebrowControl_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEyebrowControl;
	static void NewProp_bEnableJawControl_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableJawControl;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendShapeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FFacialAnimationData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationType = { "AnimationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFacialAnimationData, AnimationType), Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialAnimationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationType_MetaData), NewProp_AnimationType_MetaData) }; // 2058369154
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeNames_Inner = { "BlendShapeNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeNames = { "BlendShapeNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFacialAnimationData, BlendShapeNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeNames_MetaData), NewProp_BlendShapeNames_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_ControlBoneNames_Inner = { "ControlBoneNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_ControlBoneNames = { "ControlBoneNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFacialAnimationData, ControlBoneNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlBoneNames_MetaData), NewProp_ControlBoneNames_MetaData) };
void Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeTracking_SetBit(void* Obj)
{
	((FFacialAnimationData*)Obj)->bEnableEyeTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeTracking = { "bEnableEyeTracking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFacialAnimationData), &Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEyeTracking_MetaData), NewProp_bEnableEyeTracking_MetaData) };
void Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeBlink_SetBit(void* Obj)
{
	((FFacialAnimationData*)Obj)->bEnableEyeBlink = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeBlink = { "bEnableEyeBlink", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFacialAnimationData), &Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeBlink_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEyeBlink_MetaData), NewProp_bEnableEyeBlink_MetaData) };
void Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyebrowControl_SetBit(void* Obj)
{
	((FFacialAnimationData*)Obj)->bEnableEyebrowControl = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyebrowControl = { "bEnableEyebrowControl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFacialAnimationData), &Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyebrowControl_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEyebrowControl_MetaData), NewProp_bEnableEyebrowControl_MetaData) };
void Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableJawControl_SetBit(void* Obj)
{
	((FFacialAnimationData*)Obj)->bEnableJawControl = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableJawControl = { "bEnableJawControl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFacialAnimationData), &Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableJawControl_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableJawControl_MetaData), NewProp_bEnableJawControl_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeMultiplier = { "BlendShapeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFacialAnimationData, BlendShapeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeMultiplier_MetaData), NewProp_BlendShapeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFacialAnimationData, AnimationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FFacialAnimationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_ControlBoneNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_ControlBoneNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyeBlink,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableEyebrowControl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_bEnableJawControl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_BlendShapeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewProp_AnimationSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFacialAnimationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FFacialAnimationData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"FacialAnimationData",
	Z_Construct_UScriptStruct_FFacialAnimationData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFacialAnimationData_Statics::PropPointers),
	sizeof(FFacialAnimationData),
	alignof(FFacialAnimationData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFacialAnimationData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FFacialAnimationData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FFacialAnimationData()
{
	if (!Z_Registration_Info_UScriptStruct_FFacialAnimationData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FFacialAnimationData.InnerSingleton, Z_Construct_UScriptStruct_FFacialAnimationData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FFacialAnimationData.InnerSingleton;
}
// ********** End ScriptStruct FFacialAnimationData ************************************************

// ********** Begin ScriptStruct FLipSyncData ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLipSyncData;
class UScriptStruct* FLipSyncData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLipSyncData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLipSyncData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLipSyncData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("LipSyncData"));
	}
	return Z_Registration_Info_UScriptStruct_FLipSyncData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLipSyncData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipSyncMethod_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhonemeToBlendShapeMapping_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisemeToBlendShapeMapping_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioAsset_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipSyncIntensity_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SmoothingFactor_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioAnalysis_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FFTSize_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrequencyRange_MetaData[] = {
		{ "Category", "Lip Sync" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LipSyncMethod_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LipSyncMethod;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhonemeToBlendShapeMapping_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhonemeToBlendShapeMapping_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhonemeToBlendShapeMapping;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VisemeToBlendShapeMapping_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VisemeToBlendShapeMapping_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VisemeToBlendShapeMapping;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AudioAsset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LipSyncIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SmoothingFactor;
	static void NewProp_bEnableAudioAnalysis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioAnalysis;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FFTSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrequencyRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLipSyncData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncMethod_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncMethod = { "LipSyncMethod", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, LipSyncMethod), Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncMethod, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipSyncMethod_MetaData), NewProp_LipSyncMethod_MetaData) }; // 1811006609
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping_ValueProp = { "PhonemeToBlendShapeMapping", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping_Key_KeyProp = { "PhonemeToBlendShapeMapping_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping = { "PhonemeToBlendShapeMapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, PhonemeToBlendShapeMapping), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhonemeToBlendShapeMapping_MetaData), NewProp_PhonemeToBlendShapeMapping_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping_ValueProp = { "VisemeToBlendShapeMapping", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping_Key_KeyProp = { "VisemeToBlendShapeMapping_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping = { "VisemeToBlendShapeMapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, VisemeToBlendShapeMapping), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisemeToBlendShapeMapping_MetaData), NewProp_VisemeToBlendShapeMapping_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_AudioAsset = { "AudioAsset", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, AudioAsset), Z_Construct_UClass_USoundWave_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioAsset_MetaData), NewProp_AudioAsset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncIntensity = { "LipSyncIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, LipSyncIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipSyncIntensity_MetaData), NewProp_LipSyncIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_SmoothingFactor = { "SmoothingFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, SmoothingFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SmoothingFactor_MetaData), NewProp_SmoothingFactor_MetaData) };
void Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_bEnableAudioAnalysis_SetBit(void* Obj)
{
	((FLipSyncData*)Obj)->bEnableAudioAnalysis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_bEnableAudioAnalysis = { "bEnableAudioAnalysis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLipSyncData), &Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_bEnableAudioAnalysis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioAnalysis_MetaData), NewProp_bEnableAudioAnalysis_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_FFTSize = { "FFTSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, FFTSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FFTSize_MetaData), NewProp_FFTSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_FrequencyRange = { "FrequencyRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLipSyncData, FrequencyRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrequencyRange_MetaData), NewProp_FrequencyRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLipSyncData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncMethod_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncMethod,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_PhonemeToBlendShapeMapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_VisemeToBlendShapeMapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_AudioAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_LipSyncIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_SmoothingFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_bEnableAudioAnalysis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_FFTSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLipSyncData_Statics::NewProp_FrequencyRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLipSyncData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLipSyncData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"LipSyncData",
	Z_Construct_UScriptStruct_FLipSyncData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLipSyncData_Statics::PropPointers),
	sizeof(FLipSyncData),
	alignof(FLipSyncData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLipSyncData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLipSyncData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLipSyncData()
{
	if (!Z_Registration_Info_UScriptStruct_FLipSyncData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLipSyncData.InnerSingleton, Z_Construct_UScriptStruct_FLipSyncData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLipSyncData.InnerSingleton;
}
// ********** End ScriptStruct FLipSyncData ********************************************************

// ********** Begin ScriptStruct FEmotionMappingData ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEmotionMappingData;
class UScriptStruct* FEmotionMappingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEmotionMappingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEmotionMappingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEmotionMappingData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EmotionMappingData"));
	}
	return Z_Registration_Info_UScriptStruct_FEmotionMappingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEmotionMappingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionToBlendShapeMapping_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionIntensities_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendMode_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSpeed_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEmotionBlending_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSubtleExpressions_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubtleExpressionIntensity_MetaData[] = {
		{ "Category", "Emotion Mapping" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmotionToBlendShapeMapping_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EmotionToBlendShapeMapping_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EmotionToBlendShapeMapping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmotionIntensities_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EmotionIntensities_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EmotionIntensities;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BlendMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BlendMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSpeed;
	static void NewProp_bEnableEmotionBlending_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEmotionBlending;
	static void NewProp_bEnableSubtleExpressions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSubtleExpressions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SubtleExpressionIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEmotionMappingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping_ValueProp = { "EmotionToBlendShapeMapping", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAnimationStringArrayWrapper, METADATA_PARAMS(0, nullptr) }; // 1593750168
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping_Key_KeyProp = { "EmotionToBlendShapeMapping_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping = { "EmotionToBlendShapeMapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEmotionMappingData, EmotionToBlendShapeMapping), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionToBlendShapeMapping_MetaData), NewProp_EmotionToBlendShapeMapping_MetaData) }; // 1593750168
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities_ValueProp = { "EmotionIntensities", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities_Key_KeyProp = { "EmotionIntensities_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities = { "EmotionIntensities", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEmotionMappingData, EmotionIntensities), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionIntensities_MetaData), NewProp_EmotionIntensities_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_BlendMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_BlendMode = { "BlendMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEmotionMappingData, BlendMode), Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionBlendMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendMode_MetaData), NewProp_BlendMode_MetaData) }; // 1074429026
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_TransitionSpeed = { "TransitionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEmotionMappingData, TransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSpeed_MetaData), NewProp_TransitionSpeed_MetaData) };
void Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableEmotionBlending_SetBit(void* Obj)
{
	((FEmotionMappingData*)Obj)->bEnableEmotionBlending = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableEmotionBlending = { "bEnableEmotionBlending", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEmotionMappingData), &Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableEmotionBlending_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEmotionBlending_MetaData), NewProp_bEnableEmotionBlending_MetaData) };
void Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableSubtleExpressions_SetBit(void* Obj)
{
	((FEmotionMappingData*)Obj)->bEnableSubtleExpressions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableSubtleExpressions = { "bEnableSubtleExpressions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEmotionMappingData), &Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableSubtleExpressions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSubtleExpressions_MetaData), NewProp_bEnableSubtleExpressions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_SubtleExpressionIntensity = { "SubtleExpressionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEmotionMappingData, SubtleExpressionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubtleExpressionIntensity_MetaData), NewProp_SubtleExpressionIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEmotionMappingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionToBlendShapeMapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_EmotionIntensities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_BlendMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_BlendMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_TransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableEmotionBlending,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_bEnableSubtleExpressions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewProp_SubtleExpressionIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEmotionMappingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEmotionMappingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"EmotionMappingData",
	Z_Construct_UScriptStruct_FEmotionMappingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEmotionMappingData_Statics::PropPointers),
	sizeof(FEmotionMappingData),
	alignof(FEmotionMappingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEmotionMappingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEmotionMappingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEmotionMappingData()
{
	if (!Z_Registration_Info_UScriptStruct_FEmotionMappingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEmotionMappingData.InnerSingleton, Z_Construct_UScriptStruct_FEmotionMappingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEmotionMappingData.InnerSingleton;
}
// ********** End ScriptStruct FEmotionMappingData *************************************************

// ********** Begin ScriptStruct FAnimationBlueprintLODData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData;
class UScriptStruct* FAnimationBlueprintLODData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAnimationBlueprintLODData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AnimationBlueprintLODData"));
	}
	return Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumLODs_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeReductions_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateFrequencies_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableScreenSizeBasedLOD_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenSizeThreshold_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDisableFacialAnimationAtDistance_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FacialAnimationCullDistance_MetaData[] = {
		{ "Category", "Animation Blueprint LOD" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumLODs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendShapeReductions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BlendShapeReductions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpdateFrequencies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UpdateFrequencies;
	static void NewProp_bEnableScreenSizeBasedLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableScreenSizeBasedLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenSizeThreshold;
	static void NewProp_bDisableFacialAnimationAtDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableFacialAnimationAtDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FacialAnimationCullDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAnimationBlueprintLODData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_NumLODs = { "NumLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, NumLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumLODs_MetaData), NewProp_NumLODs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_BlendShapeReductions_Inner = { "BlendShapeReductions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_BlendShapeReductions = { "BlendShapeReductions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, BlendShapeReductions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeReductions_MetaData), NewProp_BlendShapeReductions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_UpdateFrequencies_Inner = { "UpdateFrequencies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_UpdateFrequencies = { "UpdateFrequencies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, UpdateFrequencies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateFrequencies_MetaData), NewProp_UpdateFrequencies_MetaData) };
void Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bEnableScreenSizeBasedLOD_SetBit(void* Obj)
{
	((FAnimationBlueprintLODData*)Obj)->bEnableScreenSizeBasedLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bEnableScreenSizeBasedLOD = { "bEnableScreenSizeBasedLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAnimationBlueprintLODData), &Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bEnableScreenSizeBasedLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableScreenSizeBasedLOD_MetaData), NewProp_bEnableScreenSizeBasedLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_ScreenSizeThreshold = { "ScreenSizeThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, ScreenSizeThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenSizeThreshold_MetaData), NewProp_ScreenSizeThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bDisableFacialAnimationAtDistance_SetBit(void* Obj)
{
	((FAnimationBlueprintLODData*)Obj)->bDisableFacialAnimationAtDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bDisableFacialAnimationAtDistance = { "bDisableFacialAnimationAtDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAnimationBlueprintLODData), &Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bDisableFacialAnimationAtDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDisableFacialAnimationAtDistance_MetaData), NewProp_bDisableFacialAnimationAtDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_FacialAnimationCullDistance = { "FacialAnimationCullDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintLODData, FacialAnimationCullDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FacialAnimationCullDistance_MetaData), NewProp_FacialAnimationCullDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_NumLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_BlendShapeReductions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_BlendShapeReductions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_UpdateFrequencies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_UpdateFrequencies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bEnableScreenSizeBasedLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_ScreenSizeThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_bDisableFacialAnimationAtDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewProp_FacialAnimationCullDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AnimationBlueprintLODData",
	Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::PropPointers),
	sizeof(FAnimationBlueprintLODData),
	alignof(FAnimationBlueprintLODData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAnimationBlueprintLODData()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.InnerSingleton, Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData.InnerSingleton;
}
// ********** End ScriptStruct FAnimationBlueprintLODData ******************************************

// ********** Begin ScriptStruct FAnimationBlueprintGenerationParameters ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters;
class UScriptStruct* FAnimationBlueprintGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("AnimationBlueprintGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueprintType_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSkeletalMesh_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FacialData_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LipSyncData_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionData_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODData_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateLODs_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeUpdates_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForPerformance_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueprintName_MetaData[] = {
		{ "Category", "Animation Blueprint Generation" },
		{ "ModuleRelativePath", "Public/AuracronAnimationBlueprint.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BlueprintType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BlueprintType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetSkeletalMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FacialData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LipSyncData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmotionData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODData;
	static void NewProp_bGenerateLODs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateLODs;
	static void NewProp_bEnableRealTimeUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeUpdates;
	static void NewProp_bOptimizeForPerformance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForPerformance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlueprintName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAnimationBlueprintGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintType = { "BlueprintType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, BlueprintType), Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueprintType_MetaData), NewProp_BlueprintType_MetaData) }; // 3944110617
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_TargetSkeletalMesh = { "TargetSkeletalMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, TargetSkeletalMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSkeletalMesh_MetaData), NewProp_TargetSkeletalMesh_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_FacialData = { "FacialData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, FacialData), Z_Construct_UScriptStruct_FFacialAnimationData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FacialData_MetaData), NewProp_FacialData_MetaData) }; // 1315214699
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_LipSyncData = { "LipSyncData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, LipSyncData), Z_Construct_UScriptStruct_FLipSyncData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LipSyncData_MetaData), NewProp_LipSyncData_MetaData) }; // 2743504235
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_EmotionData = { "EmotionData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, EmotionData), Z_Construct_UScriptStruct_FEmotionMappingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionData_MetaData), NewProp_EmotionData_MetaData) }; // 3881220728
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_LODData = { "LODData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, LODData), Z_Construct_UScriptStruct_FAnimationBlueprintLODData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODData_MetaData), NewProp_LODData_MetaData) }; // 2342992504
void Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit(void* Obj)
{
	((FAnimationBlueprintGenerationParameters*)Obj)->bGenerateLODs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bGenerateLODs = { "bGenerateLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAnimationBlueprintGenerationParameters), &Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bGenerateLODs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateLODs_MetaData), NewProp_bGenerateLODs_MetaData) };
void Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bEnableRealTimeUpdates_SetBit(void* Obj)
{
	((FAnimationBlueprintGenerationParameters*)Obj)->bEnableRealTimeUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bEnableRealTimeUpdates = { "bEnableRealTimeUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAnimationBlueprintGenerationParameters), &Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bEnableRealTimeUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeUpdates_MetaData), NewProp_bEnableRealTimeUpdates_MetaData) };
void Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bOptimizeForPerformance_SetBit(void* Obj)
{
	((FAnimationBlueprintGenerationParameters*)Obj)->bOptimizeForPerformance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bOptimizeForPerformance = { "bOptimizeForPerformance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAnimationBlueprintGenerationParameters), &Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bOptimizeForPerformance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForPerformance_MetaData), NewProp_bOptimizeForPerformance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintName = { "BlueprintName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAnimationBlueprintGenerationParameters, BlueprintName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueprintName_MetaData), NewProp_BlueprintName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_TargetSkeletalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_FacialData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_LipSyncData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_EmotionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_LODData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bGenerateLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bEnableRealTimeUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_bOptimizeForPerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewProp_BlueprintName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"AnimationBlueprintGenerationParameters",
	Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::PropPointers),
	sizeof(FAnimationBlueprintGenerationParameters),
	alignof(FAnimationBlueprintGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FAnimationBlueprintGenerationParameters *****************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAnimationBlueprintType_StaticEnum, TEXT("EAnimationBlueprintType"), &Z_Registration_Info_UEnum_EAnimationBlueprintType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3944110617U) },
		{ EFacialAnimationType_StaticEnum, TEXT("EFacialAnimationType"), &Z_Registration_Info_UEnum_EFacialAnimationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2058369154U) },
		{ ELipSyncMethod_StaticEnum, TEXT("ELipSyncMethod"), &Z_Registration_Info_UEnum_ELipSyncMethod, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1811006609U) },
		{ EEmotionBlendMode_StaticEnum, TEXT("EEmotionBlendMode"), &Z_Registration_Info_UEnum_EEmotionBlendMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1074429026U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAnimationStringArrayWrapper::StaticStruct, Z_Construct_UScriptStruct_FAnimationStringArrayWrapper_Statics::NewStructOps, TEXT("AnimationStringArrayWrapper"), &Z_Registration_Info_UScriptStruct_FAnimationStringArrayWrapper, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAnimationStringArrayWrapper), 1593750168U) },
		{ FFacialAnimationData::StaticStruct, Z_Construct_UScriptStruct_FFacialAnimationData_Statics::NewStructOps, TEXT("FacialAnimationData"), &Z_Registration_Info_UScriptStruct_FFacialAnimationData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FFacialAnimationData), 1315214699U) },
		{ FLipSyncData::StaticStruct, Z_Construct_UScriptStruct_FLipSyncData_Statics::NewStructOps, TEXT("LipSyncData"), &Z_Registration_Info_UScriptStruct_FLipSyncData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLipSyncData), 2743504235U) },
		{ FEmotionMappingData::StaticStruct, Z_Construct_UScriptStruct_FEmotionMappingData_Statics::NewStructOps, TEXT("EmotionMappingData"), &Z_Registration_Info_UScriptStruct_FEmotionMappingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEmotionMappingData), 3881220728U) },
		{ FAnimationBlueprintLODData::StaticStruct, Z_Construct_UScriptStruct_FAnimationBlueprintLODData_Statics::NewStructOps, TEXT("AnimationBlueprintLODData"), &Z_Registration_Info_UScriptStruct_FAnimationBlueprintLODData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAnimationBlueprintLODData), 2342992504U) },
		{ FAnimationBlueprintGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FAnimationBlueprintGenerationParameters_Statics::NewStructOps, TEXT("AnimationBlueprintGenerationParameters"), &Z_Registration_Info_UScriptStruct_FAnimationBlueprintGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAnimationBlueprintGenerationParameters), 2387953046U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_4057550745(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronAnimationBlueprint_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
