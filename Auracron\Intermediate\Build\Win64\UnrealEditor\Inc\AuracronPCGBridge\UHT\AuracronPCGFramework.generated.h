// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGFramework.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGFramework_generated_h
#error "AuracronPCGFramework.generated.h already included, missing '#pragma once' in AuracronPCGFramework.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGFramework_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronPCGErrorInfo *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h_162_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGErrorInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGErrorInfo;
// ********** End ScriptStruct FAuracronPCGErrorInfo ***********************************************

// ********** Begin ScriptStruct FAuracronPCGPerformanceMetrics ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h_200_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPerformanceMetrics;
// ********** End ScriptStruct FAuracronPCGPerformanceMetrics **************************************

// ********** Begin ScriptStruct FAuracronPCGConfiguration *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h_240_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGConfiguration;
// ********** End ScriptStruct FAuracronPCGConfiguration *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGFramework_h

// ********** Begin Enum EAuracronPCGErrorCode *****************************************************
#define FOREACH_ENUM_EAURACRONPCGERRORCODE(op) \
	op(EAuracronPCGErrorCode::None) \
	op(EAuracronPCGErrorCode::InvalidInput) \
	op(EAuracronPCGErrorCode::InvalidGraph) \
	op(EAuracronPCGErrorCode::InvalidElement) \
	op(EAuracronPCGErrorCode::ExecutionTimeout) \
	op(EAuracronPCGErrorCode::MemoryAllocationFailed) \
	op(EAuracronPCGErrorCode::InvalidPointData) \
	op(EAuracronPCGErrorCode::InvalidSpatialData) \
	op(EAuracronPCGErrorCode::InvalidMetadata) \
	op(EAuracronPCGErrorCode::GenerationFailed) \
	op(EAuracronPCGErrorCode::UnknownError) 

enum class EAuracronPCGErrorCode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGErrorCode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGErrorCode>();
// ********** End Enum EAuracronPCGErrorCode *******************************************************

// ********** Begin Enum EAuracronPCGExecutionMode *************************************************
#define FOREACH_ENUM_EAURACRONPCGEXECUTIONMODE(op) \
	op(EAuracronPCGExecutionMode::Synchronous) \
	op(EAuracronPCGExecutionMode::Asynchronous) \
	op(EAuracronPCGExecutionMode::Threaded) \
	op(EAuracronPCGExecutionMode::GPU) 

enum class EAuracronPCGExecutionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGExecutionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGExecutionMode>();
// ********** End Enum EAuracronPCGExecutionMode ***************************************************

// ********** Begin Enum EAuracronPCGQualityLevel **************************************************
#define FOREACH_ENUM_EAURACRONPCGQUALITYLEVEL(op) \
	op(EAuracronPCGQualityLevel::Low) \
	op(EAuracronPCGQualityLevel::Medium) \
	op(EAuracronPCGQualityLevel::High) \
	op(EAuracronPCGQualityLevel::Ultra) \
	op(EAuracronPCGQualityLevel::Custom) 

enum class EAuracronPCGQualityLevel : uint8;
template<> struct TIsUEnumClass<EAuracronPCGQualityLevel> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGQualityLevel>();
// ********** End Enum EAuracronPCGQualityLevel ****************************************************

// ********** Begin Enum EAuracronPCGGenerationType ************************************************
#define FOREACH_ENUM_EAURACRONPCGGENERATIONTYPE(op) \
	op(EAuracronPCGGenerationType::Points) \
	op(EAuracronPCGGenerationType::Meshes) \
	op(EAuracronPCGGenerationType::Landscapes) \
	op(EAuracronPCGGenerationType::Foliage) \
	op(EAuracronPCGGenerationType::Buildings) \
	op(EAuracronPCGGenerationType::Roads) \
	op(EAuracronPCGGenerationType::Terrain) \
	op(EAuracronPCGGenerationType::Biomes) \
	op(EAuracronPCGGenerationType::Custom) 

enum class EAuracronPCGGenerationType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGGenerationType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGGenerationType>();
// ********** End Enum EAuracronPCGGenerationType **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
