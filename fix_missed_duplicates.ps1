# Script para corrigir duplicados que foram perdidos

Write-Host "Corrigindo duplicados perdidos..."

# 1. Corrigir FAuracronSessionConfiguration que não foi renomeado corretamente
$file1 = "Auracron\Source\AuracronNetworkingBridge\Public\AuracronNetworkingBridge.h"
$content1 = Get-Content $file1 -Raw
# Verificar se ainda existe o nome antigo
if ($content1 -match "struct FAuracronSessionConfiguration") {
    Write-Host "Encontrado FAuracronSessionConfiguration não renomeado, corrigindo..."
    $content1 = $content1 -replace "struct FAuracronSessionConfiguration", "struct FAuracronNetworkingSessionConfiguration"
    $content1 = $content1 -replace "FAuracronSessionConfiguration ", "FAuracronNetworkingSessionConfiguration "
    Set-Content -Path $file1 -Value $content1 -NoNewline
}

# 2. Corrigir FAuracronAntiCheatConfiguration que não foi renomeado corretamente
if ($content1 -match "struct FAuracronAntiCheatConfiguration") {
    Write-Host "Encontrado FAuracronAntiCheatConfiguration não renomeado, corrigindo..."
    $content1 = Get-Content $file1 -Raw
    $content1 = $content1 -replace "struct FAuracronAntiCheatConfiguration", "struct FAuracronNetworkingAntiCheatConfiguration"
    $content1 = $content1 -replace "FAuracronAntiCheatConfiguration ", "FAuracronNetworkingAntiCheatConfiguration "
    Set-Content -Path $file1 -Value $content1 -NoNewline
}

# 3. Corrigir EAuracronTextureType duplicado em AuracronMetaHumanTextureSystem.h
$file2 = "Auracron\Source\AuracronMetaHumanFramework\Public\Systems\AuracronMetaHumanTextureSystem.h"
if (Test-Path $file2) {
    $content2 = Get-Content $file2 -Raw
    $content2 = $content2 -replace "enum class EAuracronTextureType", "enum class EAuracronMetaHumanTextureType"
    $content2 = $content2 -replace "EAuracronTextureType::", "EAuracronMetaHumanTextureType::"
    $content2 = $content2 -replace "EAuracronTextureType ", "EAuracronMetaHumanTextureType "
    Set-Content -Path $file2 -Value $content2 -NoNewline
    Write-Host "Renomeado EAuracronTextureType para EAuracronMetaHumanTextureType"
}

# 4. Corrigir FAuracronAudioConfiguration duplicado
$file3 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionAudio.h"
$content3 = Get-Content $file3 -Raw
if ($content3 -match "struct FAuracronAudioConfiguration") {
    Write-Host "Encontrado FAuracronAudioConfiguration não renomeado, corrigindo..."
    $content3 = $content3 -replace "struct FAuracronAudioConfiguration", "struct FAuracronWorldPartitionAudioConfiguration"
    $content3 = $content3 -replace "FAuracronAudioConfiguration ", "FAuracronWorldPartitionAudioConfiguration "
    Set-Content -Path $file3 -Value $content3 -NoNewline
}

# 5. Corrigir FAuracronStreamingStatistics duplicado
$file4 = "Auracron\Source\AuracronWorldPartitionBridge\Public\AuracronWorldPartitionStreaming.h"
$content4 = Get-Content $file4 -Raw
if ($content4 -match "struct FAuracronStreamingStatistics") {
    Write-Host "Encontrado FAuracronStreamingStatistics não renomeado, corrigindo..."
    $content4 = $content4 -replace "struct FAuracronStreamingStatistics", "struct FAuracronStreamingBridgeStatistics"
    $content4 = $content4 -replace "FAuracronStreamingStatistics ", "FAuracronStreamingBridgeStatistics "
    Set-Content -Path $file4 -Value $content4 -NoNewline
}

Write-Host "Correções de duplicados perdidos aplicadas com sucesso!"
