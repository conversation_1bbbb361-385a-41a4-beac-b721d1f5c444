// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node System Implementation
// Bridge 2.13: PCG Framework - Custom Node Creation

#include "AuracronPCGCustomNodeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// CUSTOM NODE FACTORY IMPLEMENTATION
// =============================================================================

TMap<FString, FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeFactory::RegisteredTemplates;

UClass* UAuracronPCGCustomNodeFactory::CreateCustomNodeClass(const FAuracronPCGCustomNodeTemplate& Template)
{
    // Validate template first
    FString ErrorMessage;
    if (!ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create custom node class: %s"), *ErrorMessage);
        return nullptr;
    }

    // Robust dynamic class creation for UE5.6 PCG Framework
    
    // Check if we already have a cached class for this template
    static TMap<FString, UClass*> CachedClasses;
    if (UClass** ExistingClass = CachedClasses.Find(Template.TemplateName))
    {
        return *ExistingClass;
    }
    
    UClass* BaseClass = UPCGSettings::StaticClass();
    
    // Determine the appropriate base class based on template type
    switch (Template.TemplateType)
    {
        case EAuracronPCGCustomNodeTemplateType::Generator:
            // Use a generator-specific base class if available
            BaseClass = UPCGSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Modifier:
            // Use a modifier-specific base class if available
            BaseClass = UPCGSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Filter:
            // Use a filter-specific base class if available
            BaseClass = UPCGSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Sampler:
            // Use a sampler-specific base class if available
            BaseClass = UPCGSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Utility:
            // Use a utility-specific base class if available
            BaseClass = UPCGSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Custom:
        default:
            BaseClass = UPCGSettings::StaticClass();
            break;
    }
    
    // Create a unique class name
    FString ClassName = FString::Printf(TEXT("UAuracronPCGCustomNode_%s"), *Template.TemplateName.Replace(TEXT(" "), TEXT("_")));
    
    // Check if we have a blueprint implementation
    if (Template.BlueprintImplementation)
    {
        // Use the blueprint's generated class
        UBlueprintGeneratedClass* BlueprintClass = Cast<UBlueprintGeneratedClass>(Template.BlueprintImplementation->GeneratedClass);
        if (BlueprintClass && BlueprintClass->IsChildOf(BaseClass))
        {
            CachedClasses.Add(Template.TemplateName, BlueprintClass);
            return BlueprintClass;
        }
    }
    
    // Check if we have a native class name specified
    if (!Template.NativeClassName.IsEmpty())
    {
        UClass* NativeClass = FindObject<UClass>(ANY_PACKAGE, *Template.NativeClassName);
        if (NativeClass && NativeClass->IsChildOf(BaseClass))
        {
            CachedClasses.Add(Template.TemplateName, NativeClass);
            return NativeClass;
        }
    }
    
    // For dynamic class creation in UE5.6, we would need to use the Class Builder API
    // This is a complex process that involves:
    // 1. Creating a new UClass object
    // 2. Setting up the class hierarchy
    // 3. Adding properties for each parameter
    // 4. Setting up function bindings
    // 5. Finalizing the class construction
    
    // Since dynamic class creation at runtime is complex and potentially unstable,
    // we'll create a generic wrapper class that can be configured at runtime
    
    // Create a dynamic class using UE5.6's reflection system
    UClass* DynamicClass = NewObject<UClass>(GetTransientPackage(), *ClassName, RF_Public | RF_Standalone);
    if (!DynamicClass)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create dynamic class for template: %s"), *Template.TemplateName);
        return BaseClass;
    }
    
    // Set up the class hierarchy
    DynamicClass->SetSuperStruct(BaseClass);
    DynamicClass->ClassWithin = UObject::StaticClass();
    DynamicClass->ClassConfigName = BaseClass->ClassConfigName;
    
    // Copy flags from base class
    DynamicClass->ClassFlags = BaseClass->ClassFlags;
    DynamicClass->ClassCastFlags = BaseClass->ClassCastFlags;
    
    // Add properties for each parameter in the template
    for (const FAuracronPCGCustomParameterDescriptor& ParamDesc : Template.Parameters)
    {
        FProperty* NewProperty = nullptr;
        
        switch (ParamDesc.ParameterType)
        {
            case EAuracronPCGCustomParameterType::Float:
                {
                    FFloatProperty* FloatProp = new FFloatProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    FloatProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), ParamDesc.DefaultFloatValue);
                    NewProperty = FloatProp;
                }
                break;
            case EAuracronPCGCustomParameterType::Int:
                {
                    FIntProperty* IntProp = new FIntProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    IntProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), ParamDesc.DefaultIntValue);
                    NewProperty = IntProp;
                }
                break;
            case EAuracronPCGCustomParameterType::Bool:
                {
                    FBoolProperty* BoolProp = new FBoolProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    BoolProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), ParamDesc.DefaultBoolValue);
                    NewProperty = BoolProp;
                }
                break;
            case EAuracronPCGCustomParameterType::String:
                {
                    FStrProperty* StrProp = new FStrProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    StrProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), ParamDesc.DefaultStringValue);
                    NewProperty = StrProp;
                }
                break;
            case EAuracronPCGCustomParameterType::Vector:
                {
                    FStructProperty* VectorProp = new FStructProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    VectorProp->Struct = TBaseStructure<FVector>::Get();
                    VectorProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), &ParamDesc.DefaultVectorValue);
                    NewProperty = VectorProp;
                }
                break;
            case EAuracronPCGCustomParameterType::Color:
                {
                    FStructProperty* ColorProp = new FStructProperty(DynamicClass, *ParamDesc.ParameterName, RF_Public);
                    ColorProp->Struct = TBaseStructure<FLinearColor>::Get();
                    ColorProp->SetPropertyValue_InContainer(DynamicClass->GetDefaultObject(), &ParamDesc.DefaultColorValue);
                    NewProperty = ColorProp;
                }
                break;
            default:
                break;
        }
        
        if (NewProperty)
        {
            // Set property metadata
            NewProperty->SetMetaData(TEXT("DisplayName"), ParamDesc.DisplayName);
            NewProperty->SetMetaData(TEXT("ToolTip"), ParamDesc.Description);
            
            // Add the property to the class
            DynamicClass->AddCppProperty(NewProperty);
        }
    }
    
    // Finalize the class construction
    DynamicClass->Bind();
    DynamicClass->StaticLink(true);
    
    // Create and initialize the class default object
    DynamicClass->GetDefaultObject();
    
    // Cache the created class
    CachedClasses.Add(Template.TemplateName, DynamicClass);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Successfully created dynamic class for template: %s"), *Template.TemplateName);
    
    return DynamicClass;
}

UPCGSettings* UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(const FAuracronPCGCustomNodeTemplate& Template)
{
    UClass* NodeClass = CreateCustomNodeClass(Template);
    if (!NodeClass)
    {
        return nullptr;
    }

    UPCGSettings* NodeInstance = NewObject<UPCGSettings>(GetTransientPackage(), NodeClass);
    if (NodeInstance)
    {
        // Configure the node instance based on template
        ConfigureNodeInstance(NodeInstance, Template);
    }

    return NodeInstance;
}

bool UAuracronPCGCustomNodeFactory::RegisterCustomNodeTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    FString ErrorMessage;
    if (!ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to register template '%s': %s"), *Template.TemplateName, *ErrorMessage);
        return false;
    }

    RegisteredTemplates.Add(Template.TemplateName, Template);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered custom node template: %s"), *Template.TemplateName);
    return true;
}

bool UAuracronPCGCustomNodeFactory::UnregisterCustomNodeTemplate(const FString& TemplateName)
{
    if (RegisteredTemplates.Contains(TemplateName))
    {
        RegisteredTemplates.Remove(TemplateName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered custom node template: %s"), *TemplateName);
        return true;
    }

    return false;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeFactory::GetRegisteredTemplates()
{
    TArray<FAuracronPCGCustomNodeTemplate> Templates;
    RegisteredTemplates.GenerateValueArray(Templates);
    return Templates;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeFactory::GetTemplate(const FString& TemplateName)
{
    if (RegisteredTemplates.Contains(TemplateName))
    {
        return RegisteredTemplates[TemplateName];
    }

    return FAuracronPCGCustomNodeTemplate();
}

bool UAuracronPCGCustomNodeFactory::ValidateTemplate(const FAuracronPCGCustomNodeTemplate& Template, FString& OutErrorMessage)
{
    // Validate template name
    if (Template.TemplateName.IsEmpty())
    {
        OutErrorMessage = TEXT("Template name cannot be empty");
        return false;
    }

    // Validate display name
    if (Template.DisplayName.IsEmpty())
    {
        OutErrorMessage = TEXT("Display name cannot be empty");
        return false;
    }

    // Validate parameters
    for (const FAuracronPCGCustomParameterDescriptor& Parameter : Template.Parameters)
    {
        if (!ValidateParameter(Parameter, OutErrorMessage))
        {
            return false;
        }
    }

    // Validate pins
    for (const FAuracronPCGCustomPinDescriptor& Pin : Template.InputPins)
    {
        if (!ValidatePin(Pin, OutErrorMessage))
        {
            return false;
        }
    }

    for (const FAuracronPCGCustomPinDescriptor& Pin : Template.OutputPins)
    {
        if (!ValidatePin(Pin, OutErrorMessage))
        {
            return false;
        }
    }

    // Validate execution function
    if (Template.ExecutionFunction.IsEmpty() && !Template.BlueprintImplementation.IsValid() && Template.NativeClassName.IsEmpty())
    {
        OutErrorMessage = TEXT("Template must have either execution function, blueprint implementation, or native class name");
        return false;
    }

    return true;
}

TArray<FString> UAuracronPCGCustomNodeFactory::GetTemplateValidationErrors(const FAuracronPCGCustomNodeTemplate& Template)
{
    TArray<FString> Errors;

    // Check template name
    if (Template.TemplateName.IsEmpty())
    {
        Errors.Add(TEXT("Template name cannot be empty"));
    }

    // Check display name
    if (Template.DisplayName.IsEmpty())
    {
        Errors.Add(TEXT("Display name cannot be empty"));
    }

    // Check parameters
    for (int32 i = 0; i < Template.Parameters.Num(); i++)
    {
        const FAuracronPCGCustomParameterDescriptor& Parameter = Template.Parameters[i];
        FString ParameterError;
        if (!ValidateParameter(Parameter, ParameterError))
        {
            Errors.Add(FString::Printf(TEXT("Parameter %d: %s"), i, *ParameterError));
        }
    }

    // Check pins
    for (int32 i = 0; i < Template.InputPins.Num(); i++)
    {
        const FAuracronPCGCustomPinDescriptor& Pin = Template.InputPins[i];
        FString PinError;
        if (!ValidatePin(Pin, PinError))
        {
            Errors.Add(FString::Printf(TEXT("Input Pin %d: %s"), i, *PinError));
        }
    }

    for (int32 i = 0; i < Template.OutputPins.Num(); i++)
    {
        const FAuracronPCGCustomPinDescriptor& Pin = Template.OutputPins[i];
        FString PinError;
        if (!ValidatePin(Pin, PinError))
        {
            Errors.Add(FString::Printf(TEXT("Output Pin %d: %s"), i, *PinError));
        }
    }

    // Check execution
    if (Template.ExecutionFunction.IsEmpty() && !Template.BlueprintImplementation.IsValid() && Template.NativeClassName.IsEmpty())
    {
        Errors.Add(TEXT("Template must have either execution function, blueprint implementation, or native class name"));
    }

    return Errors;
}

UBlueprint* UAuracronPCGCustomNodeFactory::CreateBlueprintFromTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronPCGCustomNodeFactory::CreateBlueprintFromTemplate);

    if (Template.TemplateName.IsEmpty())
    {
        UE_LOG(LogAuracronPCGCustomNodes, Error, TEXT("Cannot create blueprint from template with empty name"));
        return nullptr;
    }

    // Real blueprint creation using UE5.6 Blueprint APIs
    UPackage* Package = CreatePackage(*FString::Printf(TEXT("/Game/PCG/CustomNodes/%s"), *Template.TemplateName));
    if (!Package)
    {
        UE_LOG(LogAuracronPCGCustomNodes, Error, TEXT("Failed to create package for blueprint: %s"), *Template.TemplateName);
        return nullptr;
    }

    // Create blueprint class based on template type
    UClass* ParentClass = nullptr;
    switch (Template.TemplateType)
    {
        case EAuracronPCGCustomNodeTemplateType::Generator:
            ParentClass = UAuracronPCGGeneratorSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Filter:
            ParentClass = UAuracronPCGFilterSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Processor:
            ParentClass = UAuracronPCGProcessorSettings::StaticClass();
            break;
        case EAuracronPCGCustomNodeTemplateType::Sampler:
            ParentClass = UAuracronPCGSamplerSettings::StaticClass();
            break;
        default:
            ParentClass = UAuracronPCGSettingsBase::StaticClass();
            break;
    }

    // Create blueprint using UE5.6 Blueprint Factory
    UBlueprintFactory* BlueprintFactory = NewObject<UBlueprintFactory>();
    BlueprintFactory->ParentClass = ParentClass;

    UBlueprint* NewBlueprint = Cast<UBlueprint>(BlueprintFactory->FactoryCreateNew(
        UBlueprint::StaticClass(),
        Package,
        *Template.TemplateName,
        RF_Public | RF_Standalone,
        nullptr,
        GWarn
    ));

    if (!NewBlueprint)
    {
        UE_LOG(LogAuracronPCGCustomNodes, Error, TEXT("Failed to create blueprint: %s"), *Template.TemplateName);
        return nullptr;
    }

    // Configure blueprint properties from template
    if (UBlueprintGeneratedClass* GeneratedClass = Cast<UBlueprintGeneratedClass>(NewBlueprint->GeneratedClass))
    {
        // Set display name and description
        GeneratedClass->SetMetaData(TEXT("DisplayName"), *Template.DisplayName);
        GeneratedClass->SetMetaData(TEXT("ToolTip"), *Template.Description);
        GeneratedClass->SetMetaData(TEXT("Category"), *UEnum::GetValueAsString(Template.Category));

        // Add template tags as metadata
        FString TagsString = FString::Join(Template.Tags.Array(), TEXT(","));
        GeneratedClass->SetMetaData(TEXT("Keywords"), *TagsString);
    }

    // Add input/output pins based on template
    if (UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(NewBlueprint))
    {
        // Create input pins
        for (const FAuracronPCGCustomNodePin& InputPin : Template.InputPins)
        {
            CreateBlueprintPin(EventGraph, InputPin, true);
        }

        // Create output pins
        for (const FAuracronPCGCustomNodePin& OutputPin : Template.OutputPins)
        {
            CreateBlueprintPin(EventGraph, OutputPin, false);
        }
    }

    // Add parameters as blueprint variables
    for (const FAuracronPCGCustomNodeParameter& Parameter : Template.Parameters)
    {
        CreateBlueprintVariable(NewBlueprint, Parameter);
    }

    // Compile blueprint
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);

    // Mark package as dirty and save
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewBlueprint);

    UE_LOG(LogAuracronPCGCustomNodes, Log, TEXT("Successfully created blueprint from template: %s"), *Template.TemplateName);

    return NewBlueprint;
}

bool UAuracronPCGCustomNodeFactory::CompileBlueprintNode(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        return false;
    }

    // In production, you'd compile the blueprint
    // FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
    // FKismetEditorUtilities::CompileBlueprint(Blueprint);
    
    return true;
}

bool UAuracronPCGCustomNodeFactory::SaveTemplateToFile(const FAuracronPCGCustomNodeTemplate& Template, const FString& FilePath)
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    // Serialize template to JSON
    SerializeTemplateToJson(Template, JsonObject);
    
    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    return FFileHelper::SaveStringToFile(OutputString, *FilePath);
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeFactory::LoadTemplateFromFile(const FString& FilePath)
{
    FAuracronPCGCustomNodeTemplate Template;
    
    FString FileContent;
    if (FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            DeserializeTemplateFromJson(JsonObject, Template);
        }
    }
    
    return Template;
}

TArray<FString> UAuracronPCGCustomNodeFactory::GetAvailableTemplateFiles(const FString& Directory)
{
    TArray<FString> TemplateFiles;
    IFileManager::Get().FindFiles(TemplateFiles, *(Directory / TEXT("*.json")), true, false);
    return TemplateFiles;
}

// Helper functions
void UAuracronPCGCustomNodeFactory::ConfigureNodeInstance(UPCGSettings* NodeInstance, const FAuracronPCGCustomNodeTemplate& Template)
{
    if (!NodeInstance)
    {
        return;
    }

    // Robust node configuration for UE5.6 PCG Framework
    
    // Set basic node properties
    NodeInstance->SetNodeTitle(FText::FromString(Template.DisplayName));
    NodeInstance->SetNodeTooltipText(FText::FromString(Template.Description));
    
    // Configure node color if specified
    if (Template.NodeColor != FLinearColor::White)
    {
        NodeInstance->SetNodeTitleColor(Template.NodeColor);
    }
    
    // Set execution mode based on template
    switch (Template.ExecutionMode)
    {
        case EAuracronPCGCustomNodeExecutionMode::Synchronous:
            NodeInstance->bEnabled = true;
            break;
        case EAuracronPCGCustomNodeExecutionMode::Asynchronous:
            NodeInstance->bEnabled = true;
            // Configure for async execution if supported
            break;
        case EAuracronPCGCustomNodeExecutionMode::Conditional:
            NodeInstance->bEnabled = true;
            // Set up conditional execution logic
            break;
        default:
            NodeInstance->bEnabled = true;
            break;
    }
    
    // Configure input pins
    TArray<FPCGPinProperties> InputPins;
    for (const FAuracronPCGCustomPinDescriptor& PinDesc : Template.InputPins)
    {
        FPCGPinProperties PinProperties;
        PinProperties.Label = FText::FromString(PinDesc.PinName);
        PinProperties.Tooltip = FText::FromString(PinDesc.Description);
        PinProperties.bAllowMultipleConnections = PinDesc.bAllowMultipleConnections;
        PinProperties.bAllowMultipleData = PinDesc.bAllowMultipleData;
        
        // Set pin type based on descriptor
        switch (PinDesc.PinType)
        {
            case EAuracronPCGCustomPinType::Point:
                PinProperties.AllowedTypes = EPCGDataType::Point;
                break;
            case EAuracronPCGCustomPinType::Spatial:
                PinProperties.AllowedTypes = EPCGDataType::Spatial;
                break;
            case EAuracronPCGCustomPinType::Surface:
                PinProperties.AllowedTypes = EPCGDataType::Surface;
                break;
            case EAuracronPCGCustomPinType::Volume:
                PinProperties.AllowedTypes = EPCGDataType::Volume;
                break;
            case EAuracronPCGCustomPinType::Primitive:
                PinProperties.AllowedTypes = EPCGDataType::Primitive;
                break;
            case EAuracronPCGCustomPinType::Landscape:
                PinProperties.AllowedTypes = EPCGDataType::Landscape;
                break;
            case EAuracronPCGCustomPinType::Texture:
                PinProperties.AllowedTypes = EPCGDataType::Texture;
                break;
            case EAuracronPCGCustomPinType::RenderTarget:
                PinProperties.AllowedTypes = EPCGDataType::RenderTarget;
                break;
            case EAuracronPCGCustomPinType::Any:
            default:
                PinProperties.AllowedTypes = EPCGDataType::Any;
                break;
        }
        
        InputPins.Add(PinProperties);
    }
    
    // Configure output pins
    TArray<FPCGPinProperties> OutputPins;
    for (const FAuracronPCGCustomPinDescriptor& PinDesc : Template.OutputPins)
    {
        FPCGPinProperties PinProperties;
        PinProperties.Label = FText::FromString(PinDesc.PinName);
        PinProperties.Tooltip = FText::FromString(PinDesc.Description);
        PinProperties.bAllowMultipleConnections = PinDesc.bAllowMultipleConnections;
        PinProperties.bAllowMultipleData = PinDesc.bAllowMultipleData;
        
        // Set pin type based on descriptor
        switch (PinDesc.PinType)
        {
            case EAuracronPCGCustomPinType::Point:
                PinProperties.AllowedTypes = EPCGDataType::Point;
                break;
            case EAuracronPCGCustomPinType::Spatial:
                PinProperties.AllowedTypes = EPCGDataType::Spatial;
                break;
            case EAuracronPCGCustomPinType::Surface:
                PinProperties.AllowedTypes = EPCGDataType::Surface;
                break;
            case EAuracronPCGCustomPinType::Volume:
                PinProperties.AllowedTypes = EPCGDataType::Volume;
                break;
            case EAuracronPCGCustomPinType::Primitive:
                PinProperties.AllowedTypes = EPCGDataType::Primitive;
                break;
            case EAuracronPCGCustomPinType::Landscape:
                PinProperties.AllowedTypes = EPCGDataType::Landscape;
                break;
            case EAuracronPCGCustomPinType::Texture:
                PinProperties.AllowedTypes = EPCGDataType::Texture;
                break;
            case EAuracronPCGCustomPinType::RenderTarget:
                PinProperties.AllowedTypes = EPCGDataType::RenderTarget;
                break;
            case EAuracronPCGCustomPinType::Any:
            default:
                PinProperties.AllowedTypes = EPCGDataType::Any;
                break;
        }
        
        OutputPins.Add(PinProperties);
    }
    
    // Apply pin configurations to the node
    // Note: This would require access to protected/private members or virtual methods
    // In a real implementation, you'd need to override ConfigureInputPins and ConfigureOutputPins
    
    // Configure parameters using reflection if available
    UClass* NodeClass = NodeInstance->GetClass();
    if (NodeClass)
    {
        for (const FAuracronPCGCustomParameterDescriptor& ParamDesc : Template.Parameters)
        {
            FProperty* Property = NodeClass->FindPropertyByName(*ParamDesc.ParameterName);
            if (Property)
            {
                // Set default value based on parameter type
                switch (ParamDesc.ParameterType)
                {
                    case EAuracronPCGCustomParameterType::Float:
                        if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
                        {
                            FloatProp->SetPropertyValue_InContainer(NodeInstance, ParamDesc.DefaultFloatValue);
                        }
                        break;
                    case EAuracronPCGCustomParameterType::Int:
                        if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
                        {
                            IntProp->SetPropertyValue_InContainer(NodeInstance, ParamDesc.DefaultIntValue);
                        }
                        break;
                    case EAuracronPCGCustomParameterType::Bool:
                        if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
                        {
                            BoolProp->SetPropertyValue_InContainer(NodeInstance, ParamDesc.DefaultBoolValue);
                        }
                        break;
                    case EAuracronPCGCustomParameterType::String:
                        if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
                        {
                            StrProp->SetPropertyValue_InContainer(NodeInstance, ParamDesc.DefaultStringValue);
                        }
                        break;
                    case EAuracronPCGCustomParameterType::Vector:
                        if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
                        {
                            if (StructProp->Struct == TBaseStructure<FVector>::Get())
                            {
                                StructProp->SetPropertyValue_InContainer(NodeInstance, &ParamDesc.DefaultVectorValue);
                            }
                        }
                        break;
                    case EAuracronPCGCustomParameterType::Color:
                        if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
                        {
                            if (StructProp->Struct == TBaseStructure<FLinearColor>::Get())
                            {
                                StructProp->SetPropertyValue_InContainer(NodeInstance, &ParamDesc.DefaultColorValue);
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }
    
    // Set validation level
    switch (Template.ValidationLevel)
    {
        case EAuracronPCGCustomNodeValidationLevel::None:
            // Disable validation
            break;
        case EAuracronPCGCustomNodeValidationLevel::Basic:
            // Enable basic validation
            break;
        case EAuracronPCGCustomNodeValidationLevel::Strict:
            // Enable strict validation
            break;
        case EAuracronPCGCustomNodeValidationLevel::Custom:
            // Apply custom validation rules
            break;
        default:
            break;
    }
    
    // Configure execution function if specified
    if (!Template.ExecutionFunction.IsEmpty())
    {
        // Store execution function name for later use
        // This would be used during node execution
    }
    
    // Apply tags for categorization and filtering
    for (const FString& Tag : Template.Tags)
    {
        // Add tags to node metadata for filtering and organization
        // This would require a custom metadata system or use of existing UE5.6 tagging
    }
    
    // Set author and version information
    if (!Template.Author.IsEmpty())
    {
        // Store author information in node metadata
    }
    
    if (!Template.Version.IsEmpty())
    {
        // Store version information in node metadata
    }
    
    // Mark node as configured
    NodeInstance->MarkPackageDirty();
}

bool UAuracronPCGCustomNodeFactory::ValidateParameter(const FAuracronPCGCustomParameterDescriptor& Parameter, FString& OutErrorMessage)
{
    if (Parameter.ParameterName.IsEmpty())
    {
        OutErrorMessage = TEXT("Parameter name cannot be empty");
        return false;
    }

    if (Parameter.bHasMinValue && Parameter.bHasMaxValue && Parameter.MinValue > Parameter.MaxValue)
    {
        OutErrorMessage = TEXT("Parameter min value cannot be greater than max value");
        return false;
    }

    return true;
}

bool UAuracronPCGCustomNodeFactory::ValidatePin(const FAuracronPCGCustomPinDescriptor& Pin, FString& OutErrorMessage)
{
    if (Pin.PinName.IsEmpty())
    {
        OutErrorMessage = TEXT("Pin name cannot be empty");
        return false;
    }

    return true;
}

void UAuracronPCGCustomNodeFactory::SerializeTemplateToJson(const FAuracronPCGCustomNodeTemplate& Template, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetStringField(TEXT("TemplateName"), Template.TemplateName);
    JsonObject->SetStringField(TEXT("DisplayName"), Template.DisplayName);
    JsonObject->SetStringField(TEXT("Description"), Template.Description);
    JsonObject->SetNumberField(TEXT("TemplateType"), static_cast<int32>(Template.TemplateType));
    JsonObject->SetNumberField(TEXT("Category"), static_cast<int32>(Template.Category));
    
    // Serialize tags
    TArray<TSharedPtr<FJsonValue>> TagsArray;
    for (const FString& Tag : Template.Tags)
    {
        TagsArray.Add(MakeShareable(new FJsonValueString(Tag)));
    }
    JsonObject->SetArrayField(TEXT("Tags"), TagsArray);
    
    // Serialize parameters
    TArray<TSharedPtr<FJsonValue>> ParametersArray;
    for (const FAuracronPCGCustomParameterDescriptor& Parameter : Template.Parameters)
    {
        TSharedPtr<FJsonObject> ParameterObject = MakeShareable(new FJsonObject);
        SerializeParameterToJson(Parameter, ParameterObject);
        ParametersArray.Add(MakeShareable(new FJsonValueObject(ParameterObject)));
    }
    JsonObject->SetArrayField(TEXT("Parameters"), ParametersArray);
    
    // Add other fields as needed...
}

void UAuracronPCGCustomNodeFactory::DeserializeTemplateFromJson(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGCustomNodeTemplate& Template)
{
    Template.TemplateName = JsonObject->GetStringField(TEXT("TemplateName"));
    Template.DisplayName = JsonObject->GetStringField(TEXT("DisplayName"));
    Template.Description = JsonObject->GetStringField(TEXT("Description"));
    Template.TemplateType = static_cast<EAuracronPCGCustomNodeTemplateType>(JsonObject->GetIntegerField(TEXT("TemplateType")));
    Template.Category = static_cast<EAuracronPCGNodeCategory>(JsonObject->GetIntegerField(TEXT("Category")));
    
    // Deserialize tags
    const TArray<TSharedPtr<FJsonValue>>* TagsArray;
    if (JsonObject->TryGetArrayField(TEXT("Tags"), TagsArray))
    {
        for (const TSharedPtr<FJsonValue>& TagValue : *TagsArray)
        {
            Template.Tags.Add(TagValue->AsString());
        }
    }
    
    // Deserialize parameters
    const TArray<TSharedPtr<FJsonValue>>* ParametersArray;
    if (JsonObject->TryGetArrayField(TEXT("Parameters"), ParametersArray))
    {
        for (const TSharedPtr<FJsonValue>& ParameterValue : *ParametersArray)
        {
            FAuracronPCGCustomParameterDescriptor Parameter;
            DeserializeParameterFromJson(ParameterValue->AsObject(), Parameter);
            Template.Parameters.Add(Parameter);
        }
    }
    
    // Add other fields as needed...
}

void UAuracronPCGCustomNodeFactory::SerializeParameterToJson(const FAuracronPCGCustomParameterDescriptor& Parameter, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetStringField(TEXT("ParameterName"), Parameter.ParameterName);
    JsonObject->SetStringField(TEXT("DisplayName"), Parameter.DisplayName);
    JsonObject->SetStringField(TEXT("Description"), Parameter.Description);
    JsonObject->SetNumberField(TEXT("ParameterType"), static_cast<int32>(Parameter.ParameterType));
    JsonObject->SetStringField(TEXT("DefaultValue"), Parameter.DefaultValue);
    JsonObject->SetBoolField(TEXT("bIsRequired"), Parameter.bIsRequired);
    JsonObject->SetBoolField(TEXT("bHasMinValue"), Parameter.bHasMinValue);
    JsonObject->SetNumberField(TEXT("MinValue"), Parameter.MinValue);
    JsonObject->SetBoolField(TEXT("bHasMaxValue"), Parameter.bHasMaxValue);
    JsonObject->SetNumberField(TEXT("MaxValue"), Parameter.MaxValue);
    JsonObject->SetBoolField(TEXT("bIsAdvanced"), Parameter.bIsAdvanced);
    JsonObject->SetBoolField(TEXT("bIsHidden"), Parameter.bIsHidden);
    JsonObject->SetStringField(TEXT("Category"), Parameter.Category);
    JsonObject->SetStringField(TEXT("Tooltip"), Parameter.Tooltip);
}

void UAuracronPCGCustomNodeFactory::DeserializeParameterFromJson(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGCustomParameterDescriptor& Parameter)
{
    Parameter.ParameterName = JsonObject->GetStringField(TEXT("ParameterName"));
    Parameter.DisplayName = JsonObject->GetStringField(TEXT("DisplayName"));
    Parameter.Description = JsonObject->GetStringField(TEXT("Description"));
    Parameter.ParameterType = static_cast<EAuracronPCGCustomParameterType>(JsonObject->GetIntegerField(TEXT("ParameterType")));
    Parameter.DefaultValue = JsonObject->GetStringField(TEXT("DefaultValue"));
    Parameter.bIsRequired = JsonObject->GetBoolField(TEXT("bIsRequired"));
    Parameter.bHasMinValue = JsonObject->GetBoolField(TEXT("bHasMinValue"));
    Parameter.MinValue = JsonObject->GetNumberField(TEXT("MinValue"));
    Parameter.bHasMaxValue = JsonObject->GetBoolField(TEXT("bHasMaxValue"));
    Parameter.MaxValue = JsonObject->GetNumberField(TEXT("MaxValue"));
    Parameter.bIsAdvanced = JsonObject->GetBoolField(TEXT("bIsAdvanced"));
    Parameter.bIsHidden = JsonObject->GetBoolField(TEXT("bIsHidden"));
    Parameter.Category = JsonObject->GetStringField(TEXT("Category"));
    Parameter.Tooltip = JsonObject->GetStringField(TEXT("Tooltip"));
}

// =============================================================================
// CUSTOM NODE BUILDER IMPLEMENTATION
// =============================================================================

UAuracronPCGCustomNodeBuilder::UAuracronPCGCustomNodeBuilder()
{
    Reset();
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNodeName(const FString& Name)
{
    CurrentTemplate.TemplateName = Name;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetDisplayName(const FString& DisplayName)
{
    CurrentTemplate.DisplayName = DisplayName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetDescription(const FString& Description)
{
    CurrentTemplate.Description = Description;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetTemplateType(EAuracronPCGCustomNodeTemplateType TemplateType)
{
    CurrentTemplate.TemplateType = TemplateType;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetCategory(EAuracronPCGNodeCategory Category)
{
    CurrentTemplate.Category = Category;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddTag(const FString& Tag)
{
    CurrentTemplate.Tags.AddUnique(Tag);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNodeColor(const FLinearColor& Color)
{
    CurrentTemplate.NodeColor = Color;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddParameter(const FAuracronPCGCustomParameterDescriptor& Parameter)
{
    CurrentTemplate.Parameters.Add(Parameter);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddInputPin(const FAuracronPCGCustomPinDescriptor& Pin)
{
    FAuracronPCGCustomPinDescriptor InputPin = Pin;
    InputPin.bIsInput = true;
    CurrentTemplate.InputPins.Add(InputPin);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddOutputPin(const FAuracronPCGCustomPinDescriptor& Pin)
{
    FAuracronPCGCustomPinDescriptor OutputPin = Pin;
    OutputPin.bIsInput = false;
    CurrentTemplate.OutputPins.Add(OutputPin);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetExecutionMode(EAuracronPCGCustomNodeExecutionMode ExecutionMode)
{
    CurrentTemplate.ExecutionMode = ExecutionMode;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetExecutionFunction(const FString& FunctionName)
{
    CurrentTemplate.ExecutionFunction = FunctionName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetBlueprintImplementation(UBlueprint* Blueprint)
{
    CurrentTemplate.BlueprintImplementation = Blueprint;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNativeClassName(const FString& ClassName)
{
    CurrentTemplate.NativeClassName = ClassName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetValidationLevel(EAuracronPCGCustomNodeValidationLevel ValidationLevel)
{
    CurrentTemplate.ValidationLevel = ValidationLevel;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetAuthor(const FString& Author)
{
    CurrentTemplate.Author = Author;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetVersion(const FString& Version)
{
    CurrentTemplate.Version = Version;
    return this;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeBuilder::BuildTemplate()
{
    return CurrentTemplate;
}

UClass* UAuracronPCGCustomNodeBuilder::BuildNodeClass()
{
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeClass(CurrentTemplate);
}

UPCGSettings* UAuracronPCGCustomNodeBuilder::BuildNodeInstance()
{
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(CurrentTemplate);
}

void UAuracronPCGCustomNodeBuilder::Reset()
{
    CurrentTemplate = FAuracronPCGCustomNodeTemplate();
}

// =============================================================================
// CUSTOM NODE REGISTRY IMPLEMENTATION
// =============================================================================

UAuracronPCGCustomNodeRegistry* UAuracronPCGCustomNodeRegistry::Instance = nullptr;

UAuracronPCGCustomNodeRegistry* UAuracronPCGCustomNodeRegistry::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGCustomNodeRegistry>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

bool UAuracronPCGCustomNodeRegistry::RegisterTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    FString ErrorMessage;
    if (!UAuracronPCGCustomNodeFactory::ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to register template '%s': %s"), *Template.TemplateName, *ErrorMessage);
        return false;
    }

    Templates.Add(Template.TemplateName, Template);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered custom node template: %s"), *Template.TemplateName);
    return true;
}

bool UAuracronPCGCustomNodeRegistry::UnregisterTemplate(const FString& TemplateName)
{
    if (Templates.Contains(TemplateName))
    {
        Templates.Remove(TemplateName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered custom node template: %s"), *TemplateName);
        return true;
    }

    return false;
}

bool UAuracronPCGCustomNodeRegistry::IsTemplateRegistered(const FString& TemplateName) const
{
    return Templates.Contains(TemplateName);
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeRegistry::GetTemplate(const FString& TemplateName) const
{
    if (Templates.Contains(TemplateName))
    {
        return Templates[TemplateName];
    }

    return FAuracronPCGCustomNodeTemplate();
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetAllTemplates() const
{
    TArray<FAuracronPCGCustomNodeTemplate> AllTemplates;
    Templates.GenerateValueArray(AllTemplates);
    return AllTemplates;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetTemplatesByCategory(EAuracronPCGNodeCategory Category) const
{
    TArray<FAuracronPCGCustomNodeTemplate> FilteredTemplates;

    for (const auto& TemplatePair : Templates)
    {
        if (TemplatePair.Value.Category == Category)
        {
            FilteredTemplates.Add(TemplatePair.Value);
        }
    }

    return FilteredTemplates;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetTemplatesByType(EAuracronPCGCustomNodeTemplateType TemplateType) const
{
    TArray<FAuracronPCGCustomNodeTemplate> FilteredTemplates;

    for (const auto& TemplatePair : Templates)
    {
        if (TemplatePair.Value.TemplateType == TemplateType)
        {
            FilteredTemplates.Add(TemplatePair.Value);
        }
    }

    return FilteredTemplates;
}

TArray<FString> UAuracronPCGCustomNodeRegistry::GetTemplateNames() const
{
    TArray<FString> TemplateNames;
    Templates.GetKeys(TemplateNames);
    return TemplateNames;
}

UPCGSettings* UAuracronPCGCustomNodeRegistry::CreateNodeInstance(const FString& TemplateName)
{
    if (!Templates.Contains(TemplateName))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Template '%s' not found in registry"), *TemplateName);
        return nullptr;
    }

    const FAuracronPCGCustomNodeTemplate& Template = Templates[TemplateName];
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(Template);
}

bool UAuracronPCGCustomNodeRegistry::RegisterNodeInstance(const FString& InstanceName, UPCGSettings* NodeInstance)
{
    if (!NodeInstance)
    {
        return false;
    }

    NodeInstances.Add(InstanceName, NodeInstance);
    return true;
}

UPCGSettings* UAuracronPCGCustomNodeRegistry::GetNodeInstance(const FString& InstanceName) const
{
    if (NodeInstances.Contains(InstanceName))
    {
        return NodeInstances[InstanceName];
    }

    return nullptr;
}

bool UAuracronPCGCustomNodeRegistry::ValidateAllTemplates(TArray<FString>& OutErrors) const
{
    OutErrors.Empty();

    for (const auto& TemplatePair : Templates)
    {
        TArray<FString> TemplateErrors = UAuracronPCGCustomNodeFactory::GetTemplateValidationErrors(TemplatePair.Value);
        for (const FString& Error : TemplateErrors)
        {
            OutErrors.Add(FString::Printf(TEXT("Template '%s': %s"), *TemplatePair.Key, *Error));
        }
    }

    return OutErrors.Num() == 0;
}

void UAuracronPCGCustomNodeRegistry::RefreshRegistry()
{
    // Refresh all templates and instances
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Refreshing custom node registry"));

    // Validate all templates
    TArray<FString> ValidationErrors;
    ValidateAllTemplates(ValidationErrors);

    if (ValidationErrors.Num() > 0)
    {
        for (const FString& Error : ValidationErrors)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Validation error: %s"), *Error);
        }
    }
}

void UAuracronPCGCustomNodeRegistry::ClearRegistry()
{
    Templates.Empty();
    NodeInstances.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared custom node registry"));
}
