using UnrealBuildTool;
public class AuracronFoliageBridge : ModuleRules
{
    public AuracronFoliageBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "Foliage","Landscape",
            "LandscapeEditor",
            "RenderCore",
            "RHI",
            "StaticMeshDescription",
            "MeshDescription",
            "GeometryCore",
            "DynamicMesh",
            "GeometryFramework",
            "InteractiveToolsFramework",
            "EditorInteractiveToolsFramework",
            "ModelingComponents",
            "ModelingOperators",
            "MeshConversion",
            "GeometryProcessingInterfaces",
            "PythonScriptPlugin",
            "Foliage","ChaosCore","PhysicsCore",
            "AudioMixer","NiagaraCore",
            "NiagaraShader",
            "MaterialUtilities",
            "TextureCompressor",
            "ImageWrapper",
            "ImageCore"
        });
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "Projects","DesktopPlatform",
            "ApplicationCore",
            "AppFramework",
            "MainFrame",
            "SourceControl",
            "SourceControlWindows",
            "ToolWidgets",
            "WorkspaceMenuStructure",
            "AssetTools",
            "AssetRegistry",
            "ContentBrowser",
            "ContentBrowserData",
            "EditorSubsystem",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities","NavigationSystem",
            "Engine",
            "DeveloperSettings",
            "MeshUtilities",
            "MeshUtilitiesCommon",
            "MaterialUtilities",
            "RawMesh",
            "MeshBuilder",
            "MeshDescriptionOperations",
            "SkeletalMeshUtilitiesCommon",
            "ClothingSystemRuntimeInterface",
            "ClothingSystemRuntimeCommon",
            "PhysicsCore","ChaosCore",});
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers","LandscapeEditor"
            });
        }
        // Enable RTTI for Python integration
        bUseRTTI = true;
        // Enable exceptions for Python integration
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_IOS=1");
        }
        // Foliage specific definitions
        PublicDefinitions.Add("WITH_FOLIAGE=1");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_PATCH=0");
    }
}
