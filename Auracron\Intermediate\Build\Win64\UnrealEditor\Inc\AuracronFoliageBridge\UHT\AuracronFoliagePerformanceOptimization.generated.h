// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliagePerformanceOptimization.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliagePerformanceOptimization_generated_h
#error "AuracronFoliagePerformanceOptimization.generated.h already included, missing '#pragma once' in AuracronFoliagePerformanceOptimization.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliagePerformanceOptimization_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronFoliageInstancedManager;
class UAuracronFoliageLODManager;
class UAuracronFoliagePerformanceOptimizationManager;
class UAuracronFoliageStreamingManager;
class UWorld;
enum class EAuracronGPUInstancingMode : uint8;
enum class EAuracronPerformanceTier : uint8;
struct FAuracronBatchingPerformanceData;
struct FAuracronCullingPerformanceData;
struct FAuracronFoliagePerformanceOptimizationConfiguration;
struct FAuracronGPUPerformanceData;
struct FAuracronOverallPerformanceData;

// ********** Begin ScriptStruct FAuracronFoliagePerformanceOptimizationConfiguration **************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_115_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliagePerformanceOptimizationConfiguration;
// ********** End ScriptStruct FAuracronFoliagePerformanceOptimizationConfiguration ****************

// ********** Begin ScriptStruct FAuracronCullingPerformanceData ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_293_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCullingPerformanceData;
// ********** End ScriptStruct FAuracronCullingPerformanceData *************************************

// ********** Begin ScriptStruct FAuracronBatchingPerformanceData **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_343_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBatchingPerformanceData;
// ********** End ScriptStruct FAuracronBatchingPerformanceData ************************************

// ********** Begin ScriptStruct FAuracronGPUPerformanceData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_393_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGPUPerformanceData;
// ********** End ScriptStruct FAuracronGPUPerformanceData *****************************************

// ********** Begin ScriptStruct FAuracronOverallPerformanceData ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_443_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronOverallPerformanceData;
// ********** End ScriptStruct FAuracronOverallPerformanceData *************************************

// ********** Begin Delegate FOnPerformanceTierChanged *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_642_DELEGATE \
static void FOnPerformanceTierChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceTierChanged, EAuracronPerformanceTier OldTier, EAuracronPerformanceTier NewTier);


// ********** End Delegate FOnPerformanceTierChanged ***********************************************

// ********** Begin Delegate FOnPerformanceOptimized ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_643_DELEGATE \
static void FOnPerformanceOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceOptimized, FAuracronOverallPerformanceData PerformanceData);


// ********** End Delegate FOnPerformanceOptimized *************************************************

// ********** Begin Delegate FOnBatchOptimized *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_644_DELEGATE \
static void FOnBatchOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnBatchOptimized, int32 BatchCount, float OptimizationTimeMs);


// ********** End Delegate FOnBatchOptimized *******************************************************

// ********** Begin Delegate FOnMemoryOptimized ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_645_DELEGATE \
static void FOnMemoryOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnMemoryOptimized, float MemorySavedMB);


// ********** End Delegate FOnMemoryOptimized ******************************************************

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogPerformanceStatistics); \
	DECLARE_FUNCTION(execDrawDebugPerformanceInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execSynchronizeWithInstancedSystem); \
	DECLARE_FUNCTION(execIntegrateWithStreamingSystem); \
	DECLARE_FUNCTION(execIntegrateWithLODSystem); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetGPUPerformanceData); \
	DECLARE_FUNCTION(execGetBatchingPerformanceData); \
	DECLARE_FUNCTION(execGetCullingPerformanceData); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execGetCurrentPerformanceTier); \
	DECLARE_FUNCTION(execSetPerformanceTier); \
	DECLARE_FUNCTION(execUpdateAdaptivePerformance); \
	DECLARE_FUNCTION(execEnableAdaptivePerformance); \
	DECLARE_FUNCTION(execIsMemoryOptimal); \
	DECLARE_FUNCTION(execGetMemoryUsageMB); \
	DECLARE_FUNCTION(execCompressInstanceData); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execGetGPUInstanceCount); \
	DECLARE_FUNCTION(execGetGPUInstancingMode); \
	DECLARE_FUNCTION(execSetGPUInstancingMode); \
	DECLARE_FUNCTION(execEnableGPUInstancing); \
	DECLARE_FUNCTION(execGetAverageInstancesPerBatch); \
	DECLARE_FUNCTION(execGetOptimizedBatchCount); \
	DECLARE_FUNCTION(execRebuildBatches); \
	DECLARE_FUNCTION(execOptimizeBatches); \
	DECLARE_FUNCTION(execEnableBatchingOptimization); \
	DECLARE_FUNCTION(execGetOcclusionCulledInstanceCount); \
	DECLARE_FUNCTION(execIsOcclusionCullingEnabled); \
	DECLARE_FUNCTION(execUpdateOcclusionCulling); \
	DECLARE_FUNCTION(execEnableOcclusionCulling); \
	DECLARE_FUNCTION(execGetFrustumCulledInstanceCount); \
	DECLARE_FUNCTION(execIsFrustumCullingEnabled); \
	DECLARE_FUNCTION(execUpdateFrustumCulling); \
	DECLARE_FUNCTION(execEnableFrustumCulling); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliagePerformanceOptimizationManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliagePerformanceOptimizationManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliagePerformanceOptimizationManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliagePerformanceOptimizationManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliagePerformanceOptimizationManager(UAuracronFoliagePerformanceOptimizationManager&&) = delete; \
	UAuracronFoliagePerformanceOptimizationManager(const UAuracronFoliagePerformanceOptimizationManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliagePerformanceOptimizationManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliagePerformanceOptimizationManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliagePerformanceOptimizationManager) \
	NO_API virtual ~UAuracronFoliagePerformanceOptimizationManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_491_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h_494_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliagePerformanceOptimizationManager;

// ********** End Class UAuracronFoliagePerformanceOptimizationManager *****************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h

// ********** Begin Enum EAuracronCullingStrategy **************************************************
#define FOREACH_ENUM_EAURACRONCULLINGSTRATEGY(op) \
	op(EAuracronCullingStrategy::None) \
	op(EAuracronCullingStrategy::FrustumOnly) \
	op(EAuracronCullingStrategy::OcclusionOnly) \
	op(EAuracronCullingStrategy::DistanceOnly) \
	op(EAuracronCullingStrategy::Combined) \
	op(EAuracronCullingStrategy::Adaptive) \
	op(EAuracronCullingStrategy::GPU) \
	op(EAuracronCullingStrategy::Custom) 

enum class EAuracronCullingStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronCullingStrategy> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronCullingStrategy>();
// ********** End Enum EAuracronCullingStrategy ****************************************************

// ********** Begin Enum EAuracronBatchingStrategy *************************************************
#define FOREACH_ENUM_EAURACRONBATCHINGSTRATEGY(op) \
	op(EAuracronBatchingStrategy::None) \
	op(EAuracronBatchingStrategy::Spatial) \
	op(EAuracronBatchingStrategy::Material) \
	op(EAuracronBatchingStrategy::LOD) \
	op(EAuracronBatchingStrategy::Hybrid) \
	op(EAuracronBatchingStrategy::Dynamic) \
	op(EAuracronBatchingStrategy::GPU) \
	op(EAuracronBatchingStrategy::Custom) 

enum class EAuracronBatchingStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronBatchingStrategy> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronBatchingStrategy>();
// ********** End Enum EAuracronBatchingStrategy ***************************************************

// ********** Begin Enum EAuracronGPUInstancingMode ************************************************
#define FOREACH_ENUM_EAURACRONGPUINSTANCINGMODE(op) \
	op(EAuracronGPUInstancingMode::Disabled) \
	op(EAuracronGPUInstancingMode::Standard) \
	op(EAuracronGPUInstancingMode::Hierarchical) \
	op(EAuracronGPUInstancingMode::GPUDriven) \
	op(EAuracronGPUInstancingMode::Nanite) \
	op(EAuracronGPUInstancingMode::Adaptive) \
	op(EAuracronGPUInstancingMode::Custom) 

enum class EAuracronGPUInstancingMode : uint8;
template<> struct TIsUEnumClass<EAuracronGPUInstancingMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronGPUInstancingMode>();
// ********** End Enum EAuracronGPUInstancingMode **************************************************

// ********** Begin Enum EAuracronPerformanceTier **************************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCETIER(op) \
	op(EAuracronPerformanceTier::Low) \
	op(EAuracronPerformanceTier::Medium) \
	op(EAuracronPerformanceTier::High) \
	op(EAuracronPerformanceTier::Ultra) \
	op(EAuracronPerformanceTier::Adaptive) \
	op(EAuracronPerformanceTier::Custom) 

enum class EAuracronPerformanceTier : uint8;
template<> struct TIsUEnumClass<EAuracronPerformanceTier> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceTier>();
// ********** End Enum EAuracronPerformanceTier ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
