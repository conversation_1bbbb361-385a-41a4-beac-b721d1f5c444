// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Landscape Integration Header
// Bridge 2.7: PCG Framework - Landscape Integration

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "Elements/PCGLandscapeData.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeProxy.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeDataAccess.h"
#include "LandscapeEdit.h"
#include "LandscapeHeightfieldCollisionComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialParameterCollection.h"
#include "Math/UnrealMathUtility.h"
#include "Curves/CurveFloat.h"

#include "AuracronPCGLandscapeIntegration.generated.h"

// Forward declarations
class ALandscape;
class ULandscapeComponent;
class ULandscapeLayerInfoObject;
class ULandscapeHeightfieldCollisionComponent;

// Landscape sampling modes
UENUM(BlueprintType)
enum class EAuracronPCGLandscapeSamplingMode : uint8
{
    Height              UMETA(DisplayName = "Height Only"),
    HeightAndNormal     UMETA(DisplayName = "Height and Normal"),
    HeightAndLayers     UMETA(DisplayName = "Height and Layers"),
    Complete            UMETA(DisplayName = "Complete Sampling"),
    LayersOnly          UMETA(DisplayName = "Layers Only"),
    NormalOnly          UMETA(DisplayName = "Normal Only"),
    SlopeOnly           UMETA(DisplayName = "Slope Only"),
    CurvatureOnly       UMETA(DisplayName = "Curvature Only")
};

// Landscape blending modes
UENUM(BlueprintType)
enum class EAuracronPCGLandscapeBlendMode : uint8
{
    Replace             UMETA(DisplayName = "Replace"),
    Add                 UMETA(DisplayName = "Add"),
    Subtract            UMETA(DisplayName = "Subtract"),
    Multiply            UMETA(DisplayName = "Multiply"),
    Screen              UMETA(DisplayName = "Screen"),
    Overlay             UMETA(DisplayName = "Overlay"),
    SoftLight           UMETA(DisplayName = "Soft Light"),
    HardLight           UMETA(DisplayName = "Hard Light"),
    ColorDodge          UMETA(DisplayName = "Color Dodge"),
    ColorBurn           UMETA(DisplayName = "Color Burn"),
    Darken              UMETA(DisplayName = "Darken"),
    Lighten             UMETA(DisplayName = "Lighten"),
    Difference          UMETA(DisplayName = "Difference"),
    Exclusion           UMETA(DisplayName = "Exclusion")
};

// Erosion simulation types
UENUM(BlueprintType)
enum class EAuracronPCGErosionType : uint8
{
    Thermal             UMETA(DisplayName = "Thermal Erosion"),
    Hydraulic           UMETA(DisplayName = "Hydraulic Erosion"),
    Wind                UMETA(DisplayName = "Wind Erosion"),
    Chemical            UMETA(DisplayName = "Chemical Erosion"),
    Glacial             UMETA(DisplayName = "Glacial Erosion"),
    Combined            UMETA(DisplayName = "Combined Erosion"),
    Custom              UMETA(DisplayName = "Custom Erosion")
};

// Layer painting modes
UENUM(BlueprintType)
enum class EAuracronPCGLayerPaintMode : uint8
{
    Paint               UMETA(DisplayName = "Paint"),
    Erase               UMETA(DisplayName = "Erase"),
    Smooth              UMETA(DisplayName = "Smooth"),
    Flatten             UMETA(DisplayName = "Flatten"),
    Noise               UMETA(DisplayName = "Noise"),
    Gradient            UMETA(DisplayName = "Gradient"),
    Pattern             UMETA(DisplayName = "Pattern"),
    Procedural          UMETA(DisplayName = "Procedural")
};

// Height modification modes
UENUM(BlueprintType)
enum class EAuracronPCGHeightModificationMode : uint8
{
    Absolute            UMETA(DisplayName = "Absolute"),
    Relative            UMETA(DisplayName = "Relative"),
    Additive            UMETA(DisplayName = "Additive"),
    Subtractive         UMETA(DisplayName = "Subtractive"),
    Smooth              UMETA(DisplayName = "Smooth"),
    Flatten             UMETA(DisplayName = "Flatten"),
    Noise               UMETA(DisplayName = "Noise"),
    Terrace             UMETA(DisplayName = "Terrace")
};

// =============================================================================
// LANDSCAPE SAMPLING DESCRIPTOR
// =============================================================================

/**
 * Landscape Sampling Descriptor
 * Describes parameters for landscape sampling operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGLandscapeSamplingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    EAuracronPCGLandscapeSamplingMode SamplingMode = EAuracronPCGLandscapeSamplingMode::Complete;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    bool bSampleHeight = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    bool bSampleNormal = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    bool bSampleSlope = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    bool bSampleCurvature = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layers")
    bool bSampleLayers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layers")
    TArray<FString> LayerNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layers")
    bool bSampleAllLayers = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bUseHighQualitySampling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float SamplingRadius = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 SampleCount = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByHeight = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByHeight"))
    FVector2D HeightRange = FVector2D(-10000.0f, 10000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterBySlope = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterBySlope"))
    FVector2D SlopeRange = FVector2D(0.0f, 90.0f);

    FAuracronPCGLandscapeSamplingDescriptor()
    {
        SamplingMode = EAuracronPCGLandscapeSamplingMode::Complete;
        bSampleHeight = true;
        bSampleNormal = true;
        bSampleSlope = false;
        bSampleCurvature = false;
        bSampleLayers = true;
        bSampleAllLayers = false;
        bUseHighQualitySampling = false;
        SamplingRadius = 0.0f;
        SampleCount = 1;
        bFilterByHeight = false;
        HeightRange = FVector2D(-10000.0f, 10000.0f);
        bFilterBySlope = false;
        SlopeRange = FVector2D(0.0f, 90.0f);
    }
};

// =============================================================================
// EROSION SIMULATION DESCRIPTOR
// =============================================================================

/**
 * Erosion Simulation Descriptor
 * Describes parameters for erosion simulation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGErosionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Erosion")
    EAuracronPCGErosionType ErosionType = EAuracronPCGErosionType::Hydraulic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Erosion")
    int32 Iterations = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Erosion")
    float Strength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thermal", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Thermal || ErosionType == EAuracronPCGErosionType::Combined"))
    float ThermalStrength = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thermal", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Thermal || ErosionType == EAuracronPCGErosionType::Combined"))
    float TalusAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydraulic", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined"))
    float RainAmount = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydraulic", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined"))
    float Evaporation = 0.02f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydraulic", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Hydraulic || ErosionType == EAuracronPCGErosionType::Combined"))
    float SedimentCapacity = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Wind || ErosionType == EAuracronPCGErosionType::Combined"))
    FVector WindDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind", meta = (EditCondition = "ErosionType == EAuracronPCGErosionType::Wind || ErosionType == EAuracronPCGErosionType::Combined"))
    float WindStrength = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bPreserveBoundaries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bGenerateDebugData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float NoiseScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    int32 RandomSeed = 12345;

    FAuracronPCGErosionDescriptor()
    {
        ErosionType = EAuracronPCGErosionType::Hydraulic;
        Iterations = 100;
        Strength = 1.0f;
        ThermalStrength = 0.5f;
        TalusAngle = 45.0f;
        RainAmount = 0.1f;
        Evaporation = 0.02f;
        SedimentCapacity = 0.1f;
        WindDirection = FVector(1.0f, 0.0f, 0.0f);
        WindStrength = 0.3f;
        bPreserveBoundaries = true;
        bGenerateDebugData = false;
        NoiseScale = 1.0f;
        RandomSeed = 12345;
    }
};

// =============================================================================
// ADVANCED LANDSCAPE SAMPLER
// =============================================================================

/**
 * Advanced Landscape Sampler
 * Enhanced version of the native Landscape Sampler with advanced features
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedLandscapeSamplerSettings, FAuracronPCGAdvancedLandscapeSamplerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedLandscapeSamplerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedLandscapeSamplerSettings();

    // Sampling configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling")
    FAuracronPCGLandscapeSamplingDescriptor SamplingDescriptor;

    // Landscape selection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    TSoftObjectPtr<ALandscape> TargetLandscape;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bAutoDetectLandscape = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bSampleAllLandscapes = false;

    // Advanced sampling
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Sampling")
    bool bUseMultiSampling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Sampling", meta = (EditCondition = "bUseMultiSampling"))
    int32 MultiSampleCount = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Sampling", meta = (EditCondition = "bUseMultiSampling"))
    float MultiSampleRadius = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Sampling")
    bool bUseAdaptiveSampling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Sampling", meta = (EditCondition = "bUseAdaptiveSampling"))
    float AdaptiveThreshold = 0.1f;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseAsyncSampling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCacheSamplingResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "16"))
    int32 MaxConcurrentSamples = 4;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputSamplingStatistics = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedLandscapeSamplerElement, UAuracronPCGAdvancedLandscapeSamplerSettings)

// =============================================================================
// LANDSCAPE HEIGHT MODIFIER
// =============================================================================

/**
 * Landscape Height Modifier
 * Modifies landscape height data using various algorithms
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGLandscapeHeightModifierSettings, FAuracronPCGLandscapeHeightModifierElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGLandscapeHeightModifierSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGLandscapeHeightModifierSettings();

    // Target landscape
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    TSoftObjectPtr<ALandscape> TargetLandscape;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bAutoDetectLandscape = true;

    // Height modification
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Modification")
    EAuracronPCGHeightModificationMode ModificationMode = EAuracronPCGHeightModificationMode::Additive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Modification")
    float HeightValue = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Modification")
    FString HeightAttribute = TEXT("Height");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Modification")
    bool bUseAttributeForHeight = false;

    // Brush settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    float BrushRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    float BrushStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    float BrushFalloff = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    TSoftObjectPtr<UCurveFloat> BrushFalloffCurve;

    // Noise settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Noise"))
    float NoiseScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Noise"))
    float NoiseStrength = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Noise"))
    int32 NoiseOctaves = 4;

    // Terrace settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrace", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Terrace"))
    float TerraceHeight = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrace", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Terrace"))
    int32 TerraceCount = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrace", meta = (EditCondition = "ModificationMode == EAuracronPCGHeightModificationMode::Terrace"))
    float TerraceSmoothing = 0.1f;

    // Advanced options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bPreserveExistingData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUpdateCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUpdateNavigation = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGLandscapeHeightModifierElement, UAuracronPCGLandscapeHeightModifierSettings)

// =============================================================================
// LANDSCAPE LAYER PAINTER
// =============================================================================

/**
 * Landscape Layer Painter
 * Paints landscape layers using various painting modes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGLandscapeLayerPainterSettings, FAuracronPCGLandscapeLayerPainterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGLandscapeLayerPainterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGLandscapeLayerPainterSettings();

    // Target landscape
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    TSoftObjectPtr<ALandscape> TargetLandscape;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bAutoDetectLandscape = true;

    // Layer settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer")
    FString LayerName = TEXT("Grass");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer")
    TSoftObjectPtr<ULandscapeLayerInfoObject> LayerInfo;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer")
    EAuracronPCGLayerPaintMode PaintMode = EAuracronPCGLayerPaintMode::Paint;

    // Paint settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Paint")
    float PaintStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Paint")
    FString StrengthAttribute = TEXT("Strength");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Paint")
    bool bUseAttributeForStrength = false;

    // Brush settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    float BrushRadius = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    float BrushFalloff = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Brush")
    TSoftObjectPtr<UCurveFloat> BrushFalloffCurve;

    // Blending settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    EAuracronPCGLandscapeBlendMode BlendMode = EAuracronPCGLandscapeBlendMode::Replace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    float BlendStrength = 1.0f;

    // Conditional painting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional")
    bool bUseConditionalPainting = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional", meta = (EditCondition = "bUseConditionalPainting"))
    FString ConditionAttribute = TEXT("Slope");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditional", meta = (EditCondition = "bUseConditionalPainting"))
    FVector2D ConditionRange = FVector2D(0.0f, 30.0f);

    // Advanced options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalizeLayers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bPreserveOtherLayers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUpdateMaterials = true;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGLandscapeLayerPainterElement, UAuracronPCGLandscapeLayerPainterSettings)

// =============================================================================
// LANDSCAPE EROSION SIMULATOR
// =============================================================================

/**
 * Landscape Erosion Simulator
 * Simulates various types of erosion on landscape heightmaps
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGLandscapeErosionSimulatorSettings, FAuracronPCGLandscapeErosionSimulatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGLandscapeErosionSimulatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGLandscapeErosionSimulatorSettings();

    // Target landscape
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    TSoftObjectPtr<ALandscape> TargetLandscape;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bAutoDetectLandscape = true;

    // Erosion configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Erosion")
    FAuracronPCGErosionDescriptor ErosionDescriptor;

    // Simulation area
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation Area")
    bool bUseCustomArea = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation Area", meta = (EditCondition = "bUseCustomArea"))
    FBox SimulationBounds = FBox(FVector(-1000.0f), FVector(1000.0f));

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseGPUAcceleration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "16"))
    int32 ThreadCount = 4;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputErosionMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputSedimentMap = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputWaterMap = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputVelocityMap = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGLandscapeErosionSimulatorElement, UAuracronPCGLandscapeErosionSimulatorSettings)

// =============================================================================
// LANDSCAPE INTEGRATION UTILITIES
// =============================================================================

/**
 * Landscape Integration Utilities
 * Utility functions for advanced landscape integration operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGLandscapeIntegrationUtils : public UObject
{
    GENERATED_BODY()

public:
    // Landscape detection and access
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static ALandscape* FindLandscapeInWorld(UWorld* World, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static TArray<ALandscape*> GetAllLandscapesInWorld(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool IsPointOnLandscape(ALandscape* Landscape, const FVector& WorldLocation);

    // Height sampling
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static float SampleLandscapeHeight(ALandscape* Landscape, const FVector& WorldLocation, bool bUseInterpolation = true);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static FVector SampleLandscapeNormal(ALandscape* Landscape, const FVector& WorldLocation);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static float SampleLandscapeSlope(ALandscape* Landscape, const FVector& WorldLocation);

    // Layer sampling
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static float SampleLandscapeLayer(ALandscape* Landscape, const FString& LayerName, const FVector& WorldLocation);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static TMap<FString, float> SampleAllLandscapeLayers(ALandscape* Landscape, const FVector& WorldLocation);

    // Height modification
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool ModifyLandscapeHeight(ALandscape* Landscape, const FVector& WorldLocation, float Height, float Radius, EAuracronPCGHeightModificationMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool ModifyLandscapeHeightBatch(ALandscape* Landscape, const TArray<FVector>& Locations, const TArray<float>& Heights, float Radius, EAuracronPCGHeightModificationMode Mode);

    // Layer painting
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool PaintLandscapeLayer(ALandscape* Landscape, const FString& LayerName, const FVector& WorldLocation, float Strength, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool PaintLandscapeLayerBatch(ALandscape* Landscape, const FString& LayerName, const TArray<FVector>& Locations, const TArray<float>& Strengths, float Radius);

    // Erosion simulation
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static bool SimulateErosion(ALandscape* Landscape, const FAuracronPCGErosionDescriptor& ErosionDescriptor, const FBox& SimulationArea);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static FIntPoint WorldLocationToLandscapeCoordinate(ALandscape* Landscape, const FVector& WorldLocation);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static FVector LandscapeCoordinateToWorldLocation(ALandscape* Landscape, const FIntPoint& LandscapeCoordinate);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static FBox GetLandscapeBounds(ALandscape* Landscape);

    UFUNCTION(BlueprintCallable, Category = "Landscape Integration Utils")
    static TArray<FString> GetLandscapeLayerNames(ALandscape* Landscape);
};

// Namespace for landscape integration utility functions
namespace AuracronPCGLandscapeIntegrationUtils
{
    AURACRONPCGFRAMEWORK_API ULandscapeComponent* FindLandscapeComponentAtLocation(ALandscape* Landscape, const FVector& WorldLocation);
    AURACRONPCGFRAMEWORK_API bool GetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, TArray<float>& OutHeightData, FIntPoint& OutDataSize);
    AURACRONPCGFRAMEWORK_API bool SetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, const TArray<float>& HeightData, const FIntPoint& DataSize);
    AURACRONPCGFRAMEWORK_API bool GetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, TArray<float>& OutLayerData, FIntPoint& OutDataSize);
    AURACRONPCGFRAMEWORK_API bool SetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, const TArray<float>& LayerData, const FIntPoint& DataSize);
    AURACRONPCGFRAMEWORK_API float CalculateLandscapeCurvature(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius = 100.0f);
    AURACRONPCGFRAMEWORK_API FVector CalculateLandscapeGradient(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius = 10.0f);
    AURACRONPCGFRAMEWORK_API bool ValidateLandscapeForModification(ALandscape* Landscape);
    AURACRONPCGFRAMEWORK_API void OptimizeLandscapeAfterModification(ALandscape* Landscape, const FBox& ModifiedArea);
    AURACRONPCGFRAMEWORK_API bool PerformThermalErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor);
    AURACRONPCGFRAMEWORK_API bool PerformHydraulicErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor);
    AURACRONPCGFRAMEWORK_API bool PerformWindErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor);
    AURACRONPCGFRAMEWORK_API float ApplyBlendMode(float BaseValue, float BlendValue, EAuracronPCGLandscapeBlendMode BlendMode, float BlendStrength);
}
