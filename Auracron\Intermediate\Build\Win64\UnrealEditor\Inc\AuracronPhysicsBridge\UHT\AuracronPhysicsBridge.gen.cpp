// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPhysicsBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPhysicsBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge();
AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister();
AURACRONPHYSICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType();
AURACRONPHYSICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType();
AURACRONPHYSICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature();
AURACRONPHYSICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDestructionConfiguration();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration();
CHAOSSOLVERENGINE_API UClass* Z_Construct_UClass_AChaosSolverActor_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
FIELDSYSTEMENGINE_API UClass* Z_Construct_UClass_UFieldSystemComponent_NoRegister();
GEOMETRYCOLLECTIONENGINE_API UClass* Z_Construct_UClass_AGeometryCollectionActor_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPhysicsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPhysicsType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPhysicsType;
static UEnum* EAuracronPhysicsType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPhysicsType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPhysicsType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType, (UObject*)Z_Construct_UPackage__Script_AuracronPhysicsBridge(), TEXT("EAuracronPhysicsType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPhysicsType.OuterSingleton;
}
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsType>()
{
	return EAuracronPhysicsType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Character.DisplayName", "Character" },
		{ "Character.Name", "EAuracronPhysicsType::Character" },
		{ "Cloth.DisplayName", "Cloth" },
		{ "Cloth.Name", "EAuracronPhysicsType::Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de f\xc3\x83\xc2\xadsica\n */" },
#endif
		{ "Constraint.DisplayName", "Constraint" },
		{ "Constraint.Name", "EAuracronPhysicsType::Constraint" },
		{ "Destruction.DisplayName", "Destruction" },
		{ "Destruction.Name", "EAuracronPhysicsType::Destruction" },
		{ "FieldSystem.DisplayName", "Field System" },
		{ "FieldSystem.Name", "EAuracronPhysicsType::FieldSystem" },
		{ "Fluid.DisplayName", "Fluid" },
		{ "Fluid.Name", "EAuracronPhysicsType::Fluid" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPhysicsType::None" },
		{ "RigidBody.DisplayName", "Rigid Body" },
		{ "RigidBody.Name", "EAuracronPhysicsType::RigidBody" },
		{ "SoftBody.DisplayName", "Soft Body" },
		{ "SoftBody.Name", "EAuracronPhysicsType::SoftBody" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de f\xc3\x83\xc2\xadsica" },
#endif
		{ "Vehicle.DisplayName", "Vehicle" },
		{ "Vehicle.Name", "EAuracronPhysicsType::Vehicle" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPhysicsType::None", (int64)EAuracronPhysicsType::None },
		{ "EAuracronPhysicsType::RigidBody", (int64)EAuracronPhysicsType::RigidBody },
		{ "EAuracronPhysicsType::SoftBody", (int64)EAuracronPhysicsType::SoftBody },
		{ "EAuracronPhysicsType::Fluid", (int64)EAuracronPhysicsType::Fluid },
		{ "EAuracronPhysicsType::Cloth", (int64)EAuracronPhysicsType::Cloth },
		{ "EAuracronPhysicsType::Destruction", (int64)EAuracronPhysicsType::Destruction },
		{ "EAuracronPhysicsType::FieldSystem", (int64)EAuracronPhysicsType::FieldSystem },
		{ "EAuracronPhysicsType::Constraint", (int64)EAuracronPhysicsType::Constraint },
		{ "EAuracronPhysicsType::Vehicle", (int64)EAuracronPhysicsType::Vehicle },
		{ "EAuracronPhysicsType::Character", (int64)EAuracronPhysicsType::Character },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
	nullptr,
	"EAuracronPhysicsType",
	"EAuracronPhysicsType",
	Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPhysicsType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPhysicsType.InnerSingleton, Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPhysicsType.InnerSingleton;
}
// ********** End Enum EAuracronPhysicsType ********************************************************

// ********** Begin Enum EAuracronDestructionType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDestructionType;
static UEnum* EAuracronDestructionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDestructionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDestructionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType, (UObject*)Z_Construct_UPackage__Script_AuracronPhysicsBridge(), TEXT("EAuracronDestructionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronDestructionType.OuterSingleton;
}
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronDestructionType>()
{
	return EAuracronDestructionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "Crumble.DisplayName", "Crumble" },
		{ "Crumble.Name", "EAuracronDestructionType::Crumble" },
		{ "Dissolve.DisplayName", "Dissolve" },
		{ "Dissolve.Name", "EAuracronDestructionType::Dissolve" },
		{ "Explosion.DisplayName", "Explosion" },
		{ "Explosion.Name", "EAuracronDestructionType::Explosion" },
		{ "Fracture.DisplayName", "Fracture" },
		{ "Fracture.Name", "EAuracronDestructionType::Fracture" },
		{ "Melt.DisplayName", "Melt" },
		{ "Melt.Name", "EAuracronDestructionType::Melt" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronDestructionType::None" },
		{ "Shatter.DisplayName", "Shatter" },
		{ "Shatter.Name", "EAuracronDestructionType::Shatter" },
		{ "Slice.DisplayName", "Slice" },
		{ "Slice.Name", "EAuracronDestructionType::Slice" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
		{ "Vaporize.DisplayName", "Vaporize" },
		{ "Vaporize.Name", "EAuracronDestructionType::Vaporize" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDestructionType::None", (int64)EAuracronDestructionType::None },
		{ "EAuracronDestructionType::Fracture", (int64)EAuracronDestructionType::Fracture },
		{ "EAuracronDestructionType::Explosion", (int64)EAuracronDestructionType::Explosion },
		{ "EAuracronDestructionType::Slice", (int64)EAuracronDestructionType::Slice },
		{ "EAuracronDestructionType::Crumble", (int64)EAuracronDestructionType::Crumble },
		{ "EAuracronDestructionType::Shatter", (int64)EAuracronDestructionType::Shatter },
		{ "EAuracronDestructionType::Melt", (int64)EAuracronDestructionType::Melt },
		{ "EAuracronDestructionType::Dissolve", (int64)EAuracronDestructionType::Dissolve },
		{ "EAuracronDestructionType::Vaporize", (int64)EAuracronDestructionType::Vaporize },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
	nullptr,
	"EAuracronDestructionType",
	"EAuracronDestructionType",
	Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronDestructionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDestructionType.InnerSingleton, Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDestructionType.InnerSingleton;
}
// ********** End Enum EAuracronDestructionType ****************************************************

// ********** Begin ScriptStruct FAuracronChaosPhysicsConfiguration ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration;
class UScriptStruct* FAuracronChaosPhysicsConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronPhysicsBridge(), TEXT("AuracronChaosPhysicsConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica Chaos\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica Chaos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePhysicsSimulation_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar simula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar simula\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomGravity_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gravidade customizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gravidade customizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomGravity_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar gravidade customizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar gravidade customizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirDensity_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade do ar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade do ar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirResistance_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resist\xc3\x83\xc2\xaancia do ar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\x83\xc2\xaancia do ar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSubStepping_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar sub-stepping */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar sub-stepping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubSteps_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de sub-steps */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de sub-steps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDeltaTime_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "0.1" },
		{ "ClampMin", "0.001" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delta time m\xc3\x83\xc2\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delta time m\xc3\x83\xc2\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCCD_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar CCD (Continuous Collision Detection) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar CCD (Continuous Collision Detection)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CCDThreshold_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold para CCD */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold para CCD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncPhysics_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar async physics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar async physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsThreads_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de threads de f\xc3\x83\xc2\xadsica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de threads de f\xc3\x83\xc2\xadsica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDeterministicPhysics_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar deterministic physics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar deterministic physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverIterations_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Solver iterations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Solver iterations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionIterations_MetaData[] = {
		{ "Category", "Chaos Physics Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Collision iterations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision iterations" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUsePhysicsSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePhysicsSimulation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomGravity;
	static void NewProp_bUseCustomGravity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomGravity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirResistance;
	static void NewProp_bUseSubStepping_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSubStepping;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SubSteps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDeltaTime;
	static void NewProp_bUseCCD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCCD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CCDThreshold;
	static void NewProp_bUseAsyncPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncPhysics;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PhysicsThreads;
	static void NewProp_bUseDeterministicPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDeterministicPhysics;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SolverIterations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollisionIterations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChaosPhysicsConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUsePhysicsSimulation_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUsePhysicsSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUsePhysicsSimulation = { "bUsePhysicsSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUsePhysicsSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePhysicsSimulation_MetaData), NewProp_bUsePhysicsSimulation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CustomGravity = { "CustomGravity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, CustomGravity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomGravity_MetaData), NewProp_CustomGravity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCustomGravity_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUseCustomGravity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCustomGravity = { "bUseCustomGravity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCustomGravity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomGravity_MetaData), NewProp_bUseCustomGravity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_AirDensity = { "AirDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, AirDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirDensity_MetaData), NewProp_AirDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_AirResistance = { "AirResistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, AirResistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirResistance_MetaData), NewProp_AirResistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseSubStepping_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUseSubStepping = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseSubStepping = { "bUseSubStepping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseSubStepping_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSubStepping_MetaData), NewProp_bUseSubStepping_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_SubSteps = { "SubSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, SubSteps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubSteps_MetaData), NewProp_SubSteps_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_MaxDeltaTime = { "MaxDeltaTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, MaxDeltaTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDeltaTime_MetaData), NewProp_MaxDeltaTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCCD_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUseCCD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCCD = { "bUseCCD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCCD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCCD_MetaData), NewProp_bUseCCD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CCDThreshold = { "CCDThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, CCDThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CCDThreshold_MetaData), NewProp_CCDThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseAsyncPhysics_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUseAsyncPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseAsyncPhysics = { "bUseAsyncPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseAsyncPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncPhysics_MetaData), NewProp_bUseAsyncPhysics_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_PhysicsThreads = { "PhysicsThreads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, PhysicsThreads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsThreads_MetaData), NewProp_PhysicsThreads_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseDeterministicPhysics_SetBit(void* Obj)
{
	((FAuracronChaosPhysicsConfiguration*)Obj)->bUseDeterministicPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseDeterministicPhysics = { "bUseDeterministicPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChaosPhysicsConfiguration), &Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseDeterministicPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDeterministicPhysics_MetaData), NewProp_bUseDeterministicPhysics_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_SolverIterations = { "SolverIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, SolverIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverIterations_MetaData), NewProp_SolverIterations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CollisionIterations = { "CollisionIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChaosPhysicsConfiguration, CollisionIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionIterations_MetaData), NewProp_CollisionIterations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUsePhysicsSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CustomGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCustomGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_AirDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_AirResistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseSubStepping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_SubSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_MaxDeltaTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseCCD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CCDThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseAsyncPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_PhysicsThreads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_bUseDeterministicPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_SolverIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewProp_CollisionIterations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChaosPhysicsConfiguration",
	Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::PropPointers),
	sizeof(FAuracronChaosPhysicsConfiguration),
	alignof(FAuracronChaosPhysicsConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChaosPhysicsConfiguration **********************************

// ********** Begin ScriptStruct FAuracronDestructionConfiguration *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration;
class UScriptStruct* FAuracronDestructionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDestructionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronPhysicsBridge(), TEXT("AuracronDestructionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionType_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionForce_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\x83\xc2\xa7""a da destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\x83\xc2\xa7""a da destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionRadius_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageThreshold_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold de dano para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold de dano para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFragments_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de fragmentos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de fragmentos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFragmentSize_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho m\xc3\x83\xc2\xadnimo de fragmento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho m\xc3\x83\xc2\xadnimo de fragmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDebris_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar debris */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar debris" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentLifetime_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de vida dos fragmentos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de vida dos fragmentos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFadeOut_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar fade out */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar fade out" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeOutTime_MetaData[] = {
		{ "Category", "Destruction Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de fade out */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de fade out" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FragmentMaterial_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material de fragmento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material de fragmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDestructionSound_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar som de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar som de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionSound_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseParticleEffects_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar efeitos de part\xc3\x83\xc2\xad""culas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar efeitos de part\xc3\x83\xc2\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionParticles_MetaData[] = {
		{ "Category", "Destruction Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de part\xc3\x83\xc2\xad""culas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de part\xc3\x83\xc2\xad""culas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DestructionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DestructionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxFragments;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFragmentSize;
	static void NewProp_bUseDebris_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDebris;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FragmentLifetime;
	static void NewProp_bUseFadeOut_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFadeOut;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeOutTime;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FragmentMaterial;
	static void NewProp_bUseDestructionSound_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDestructionSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DestructionSound;
	static void NewProp_bUseParticleEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseParticleEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DestructionParticles;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDestructionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionType = { "DestructionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DestructionType), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronDestructionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionType_MetaData), NewProp_DestructionType_MetaData) }; // 2662684909
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionForce = { "DestructionForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DestructionForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionForce_MetaData), NewProp_DestructionForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionRadius = { "DestructionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DestructionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionRadius_MetaData), NewProp_DestructionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DamageThreshold = { "DamageThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DamageThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageThreshold_MetaData), NewProp_DamageThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_MaxFragments = { "MaxFragments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, MaxFragments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFragments_MetaData), NewProp_MaxFragments_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_MinFragmentSize = { "MinFragmentSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, MinFragmentSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFragmentSize_MetaData), NewProp_MinFragmentSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDebris_SetBit(void* Obj)
{
	((FAuracronDestructionConfiguration*)Obj)->bUseDebris = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDebris = { "bUseDebris", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructionConfiguration), &Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDebris_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDebris_MetaData), NewProp_bUseDebris_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FragmentLifetime = { "FragmentLifetime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, FragmentLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentLifetime_MetaData), NewProp_FragmentLifetime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseFadeOut_SetBit(void* Obj)
{
	((FAuracronDestructionConfiguration*)Obj)->bUseFadeOut = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseFadeOut = { "bUseFadeOut", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructionConfiguration), &Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseFadeOut_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFadeOut_MetaData), NewProp_bUseFadeOut_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FadeOutTime = { "FadeOutTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, FadeOutTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeOutTime_MetaData), NewProp_FadeOutTime_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FragmentMaterial = { "FragmentMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, FragmentMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FragmentMaterial_MetaData), NewProp_FragmentMaterial_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDestructionSound_SetBit(void* Obj)
{
	((FAuracronDestructionConfiguration*)Obj)->bUseDestructionSound = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDestructionSound = { "bUseDestructionSound", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructionConfiguration), &Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDestructionSound_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDestructionSound_MetaData), NewProp_bUseDestructionSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionSound = { "DestructionSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DestructionSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionSound_MetaData), NewProp_DestructionSound_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseParticleEffects_SetBit(void* Obj)
{
	((FAuracronDestructionConfiguration*)Obj)->bUseParticleEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseParticleEffects = { "bUseParticleEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDestructionConfiguration), &Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseParticleEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseParticleEffects_MetaData), NewProp_bUseParticleEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionParticles = { "DestructionParticles", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDestructionConfiguration, DestructionParticles), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionParticles_MetaData), NewProp_DestructionParticles_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DamageThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_MaxFragments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_MinFragmentSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDebris,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FragmentLifetime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseFadeOut,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FadeOutTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_FragmentMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseDestructionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_bUseParticleEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewProp_DestructionParticles,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
	nullptr,
	&NewStructOps,
	"AuracronDestructionConfiguration",
	Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::PropPointers),
	sizeof(FAuracronDestructionConfiguration),
	alignof(FAuracronDestructionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDestructionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDestructionConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronFieldSystemConfiguration *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration;
class UScriptStruct* FAuracronFieldSystemConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronPhysicsBridge(), TEXT("AuracronFieldSystemConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Field System\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Field System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldType_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de campo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de campo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldForce_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\x83\xc2\xa7""a do campo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\x83\xc2\xa7""a do campo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldRadius_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do campo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do campo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldDuration_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do campo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do campo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFalloff_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar falloff */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar falloff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FalloffType_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de falloff */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de falloff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectDestructibleOnly_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Afetar apenas objetos destrut\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Afetar apenas objetos destrut\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectCharacterPhysics_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Afetar f\xc3\x83\xc2\xadsica de personagens */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Afetar f\xc3\x83\xc2\xadsica de personagens" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CharacterForceMultiplier_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de for\xc3\x83\xc2\xa7""a para personagens */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de for\xc3\x83\xc2\xa7""a para personagens" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomDirection_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar dire\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o customizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar dire\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o customizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomDirection_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dire\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o customizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dire\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o customizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoise_MetaData[] = {
		{ "Category", "Field System Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar noise */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar noise" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseIntensity_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do noise */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do noise" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseFrequency_MetaData[] = {
		{ "Category", "Field System Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\x83\xc2\xaancia do noise */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\x83\xc2\xaancia do noise" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FieldType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldDuration;
	static void NewProp_bUseFalloff_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFalloff;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FalloffType;
	static void NewProp_bAffectDestructibleOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectDestructibleOnly;
	static void NewProp_bAffectCharacterPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectCharacterPhysics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CharacterForceMultiplier;
	static void NewProp_bUseCustomDirection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomDirection;
	static void NewProp_bUseNoise_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoise;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFieldSystemConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldType = { "FieldType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, FieldType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldType_MetaData), NewProp_FieldType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldForce = { "FieldForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, FieldForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldForce_MetaData), NewProp_FieldForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldRadius = { "FieldRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, FieldRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldRadius_MetaData), NewProp_FieldRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldDuration = { "FieldDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, FieldDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldDuration_MetaData), NewProp_FieldDuration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseFalloff_SetBit(void* Obj)
{
	((FAuracronFieldSystemConfiguration*)Obj)->bUseFalloff = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseFalloff = { "bUseFalloff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFieldSystemConfiguration), &Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseFalloff_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFalloff_MetaData), NewProp_bUseFalloff_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FalloffType = { "FalloffType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, FalloffType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FalloffType_MetaData), NewProp_FalloffType_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectDestructibleOnly_SetBit(void* Obj)
{
	((FAuracronFieldSystemConfiguration*)Obj)->bAffectDestructibleOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectDestructibleOnly = { "bAffectDestructibleOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFieldSystemConfiguration), &Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectDestructibleOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectDestructibleOnly_MetaData), NewProp_bAffectDestructibleOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectCharacterPhysics_SetBit(void* Obj)
{
	((FAuracronFieldSystemConfiguration*)Obj)->bAffectCharacterPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectCharacterPhysics = { "bAffectCharacterPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFieldSystemConfiguration), &Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectCharacterPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectCharacterPhysics_MetaData), NewProp_bAffectCharacterPhysics_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_CharacterForceMultiplier = { "CharacterForceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, CharacterForceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CharacterForceMultiplier_MetaData), NewProp_CharacterForceMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseCustomDirection_SetBit(void* Obj)
{
	((FAuracronFieldSystemConfiguration*)Obj)->bUseCustomDirection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseCustomDirection = { "bUseCustomDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFieldSystemConfiguration), &Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseCustomDirection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomDirection_MetaData), NewProp_bUseCustomDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_CustomDirection = { "CustomDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, CustomDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomDirection_MetaData), NewProp_CustomDirection_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseNoise_SetBit(void* Obj)
{
	((FAuracronFieldSystemConfiguration*)Obj)->bUseNoise = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseNoise = { "bUseNoise", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFieldSystemConfiguration), &Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseNoise_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoise_MetaData), NewProp_bUseNoise_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_NoiseIntensity = { "NoiseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, NoiseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseIntensity_MetaData), NewProp_NoiseIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_NoiseFrequency = { "NoiseFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFieldSystemConfiguration, NoiseFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseFrequency_MetaData), NewProp_NoiseFrequency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FieldDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseFalloff,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_FalloffType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectDestructibleOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bAffectCharacterPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_CharacterForceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseCustomDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_CustomDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_bUseNoise,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_NoiseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewProp_NoiseFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
	nullptr,
	&NewStructOps,
	"AuracronFieldSystemConfiguration",
	Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::PropPointers),
	sizeof(FAuracronFieldSystemConfiguration),
	alignof(FAuracronFieldSystemConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFieldSystemConfiguration ***********************************

// ********** Begin Delegate FOnObjectDestroyed ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics
{
	struct AuracronPhysicsBridge_eventOnObjectDestroyed_Parms
	{
		AActor* DestroyedActor;
		FAuracronDestructionConfiguration DestructionConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando objeto \xc3\x83\xc2\xa9 destru\xc3\x83\xc2\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando objeto \xc3\x83\xc2\xa9 destru\xc3\x83\xc2\xad""do" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DestroyedActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructionConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::NewProp_DestroyedActor = { "DestroyedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventOnObjectDestroyed_Parms, DestroyedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::NewProp_DestructionConfig = { "DestructionConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventOnObjectDestroyed_Parms, DestructionConfig), Z_Construct_UScriptStruct_FAuracronDestructionConfiguration, METADATA_PARAMS(0, nullptr) }; // 3171473727
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::NewProp_DestroyedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::NewProp_DestructionConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "OnObjectDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::AuracronPhysicsBridge_eventOnObjectDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::AuracronPhysicsBridge_eventOnObjectDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronPhysicsBridge::FOnObjectDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectDestroyed, AActor* DestroyedActor, FAuracronDestructionConfiguration DestructionConfig)
{
	struct AuracronPhysicsBridge_eventOnObjectDestroyed_Parms
	{
		AActor* DestroyedActor;
		FAuracronDestructionConfiguration DestructionConfig;
	};
	AuracronPhysicsBridge_eventOnObjectDestroyed_Parms Parms;
	Parms.DestroyedActor=DestroyedActor;
	Parms.DestructionConfig=DestructionConfig;
	OnObjectDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectDestroyed ******************************************************

// ********** Begin Delegate FOnFieldSystemApplied *************************************************
struct Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics
{
	struct AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms
	{
		FVector Location;
		FAuracronFieldSystemConfiguration FieldConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Field System \xc3\x83\xc2\xa9 aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Field System \xc3\x83\xc2\xa9 aplicado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FieldConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::NewProp_FieldConfig = { "FieldConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms, FieldConfig), Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration, METADATA_PARAMS(0, nullptr) }; // 1266813753
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::NewProp_FieldConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "OnFieldSystemApplied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronPhysicsBridge::FOnFieldSystemApplied_DelegateWrapper(const FMulticastScriptDelegate& OnFieldSystemApplied, FVector Location, FAuracronFieldSystemConfiguration FieldConfig)
{
	struct AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms
	{
		FVector Location;
		FAuracronFieldSystemConfiguration FieldConfig;
	};
	AuracronPhysicsBridge_eventOnFieldSystemApplied_Parms Parms;
	Parms.Location=Location;
	Parms.FieldConfig=FieldConfig;
	OnFieldSystemApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFieldSystemApplied ***************************************************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyCustomGravityToObject ***************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics
{
	struct AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms
	{
		AActor* TargetActor;
		FVector Gravity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Gravity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar gravidade a objeto espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar gravidade a objeto espec\xc3\x83\xc2\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gravity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Gravity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_Gravity = { "Gravity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms, Gravity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gravity_MetaData), NewProp_Gravity_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_Gravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyCustomGravityToObject", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::AuracronPhysicsBridge_eventApplyCustomGravityToObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyCustomGravityToObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Gravity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyCustomGravityToObject(Z_Param_TargetActor,Z_Param_Out_Gravity);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyCustomGravityToObject *****************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyFieldSystem *************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics
{
	struct AuracronPhysicsBridge_eventApplyFieldSystem_Parms
	{
		FVector Location;
		FAuracronFieldSystemConfiguration FieldConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|FieldSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar Field System\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar Field System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FieldConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyFieldSystem_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_FieldConfig = { "FieldConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyFieldSystem_Parms, FieldConfig), Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldConfig_MetaData), NewProp_FieldConfig_MetaData) }; // 1266813753
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyFieldSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyFieldSystem_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_FieldConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyFieldSystem", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::AuracronPhysicsBridge_eventApplyFieldSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::AuracronPhysicsBridge_eventApplyFieldSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyFieldSystem)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronFieldSystemConfiguration,Z_Param_Out_FieldConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyFieldSystem(Z_Param_Out_Location,Z_Param_Out_FieldConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyFieldSystem ***************************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyForceToObject ***********************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics
{
	struct AuracronPhysicsBridge_eventApplyForceToObject_Parms
	{
		AActor* TargetActor;
		FVector Force;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Forces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar for\xc3\x83\xc2\xa7""a a objeto\n     */" },
#endif
		{ "CPP_Default_Location", "" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar for\xc3\x83\xc2\xa7""a a objeto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Force_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Force;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyForceToObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyForceToObject_Parms, Force), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Force_MetaData), NewProp_Force_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyForceToObject_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyForceToObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyForceToObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyForceToObject", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::AuracronPhysicsBridge_eventApplyForceToObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::AuracronPhysicsBridge_eventApplyForceToObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyForceToObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Force);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyForceToObject(Z_Param_TargetActor,Z_Param_Out_Force,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyForceToObject *************************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyImpulseToObject *********************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics
{
	struct AuracronPhysicsBridge_eventApplyImpulseToObject_Parms
	{
		AActor* TargetActor;
		FVector Impulse;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Forces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar impulso a objeto\n     */" },
#endif
		{ "CPP_Default_Location", "" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar impulso a objeto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Impulse_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Impulse;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyImpulseToObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_Impulse = { "Impulse", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyImpulseToObject_Parms, Impulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Impulse_MetaData), NewProp_Impulse_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyImpulseToObject_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyImpulseToObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyImpulseToObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_Impulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyImpulseToObject", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::AuracronPhysicsBridge_eventApplyImpulseToObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::AuracronPhysicsBridge_eventApplyImpulseToObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyImpulseToObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Impulse);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyImpulseToObject(Z_Param_TargetActor,Z_Param_Out_Impulse,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyImpulseToObject ***********************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyRealmPhysicsToObject ****************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics
{
	struct AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms
	{
		AActor* TargetActor;
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeitos de realm a objeto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos de realm a objeto" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyRealmPhysicsToObject", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::AuracronPhysicsBridge_eventApplyRealmPhysicsToObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyRealmPhysicsToObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyRealmPhysicsToObject(Z_Param_TargetActor,Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyRealmPhysicsToObject ******************

// ********** Begin Class UAuracronPhysicsBridge Function ApplyTorqueToObject **********************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics
{
	struct AuracronPhysicsBridge_eventApplyTorqueToObject_Parms
	{
		AActor* TargetActor;
		FVector Torque;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Forces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar torque a objeto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar torque a objeto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Torque_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Torque;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyTorqueToObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_Torque = { "Torque", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventApplyTorqueToObject_Parms, Torque), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Torque_MetaData), NewProp_Torque_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventApplyTorqueToObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventApplyTorqueToObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_Torque,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ApplyTorqueToObject", Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::AuracronPhysicsBridge_eventApplyTorqueToObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::AuracronPhysicsBridge_eventApplyTorqueToObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execApplyTorqueToObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Torque);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyTorqueToObject(Z_Param_TargetActor,Z_Param_Out_Torque);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ApplyTorqueToObject ************************

// ********** Begin Class UAuracronPhysicsBridge Function CleanupInactivePhysicsObjects ************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics
{
	struct AuracronPhysicsBridge_eventCleanupInactivePhysicsObjects_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar objetos f\xc3\x83\xc2\xadsicos inativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar objetos f\xc3\x83\xc2\xadsicos inativos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCleanupInactivePhysicsObjects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCleanupInactivePhysicsObjects_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CleanupInactivePhysicsObjects", Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::AuracronPhysicsBridge_eventCleanupInactivePhysicsObjects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::AuracronPhysicsBridge_eventCleanupInactivePhysicsObjects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCleanupInactivePhysicsObjects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CleanupInactivePhysicsObjects();
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CleanupInactivePhysicsObjects **************

// ********** Begin Class UAuracronPhysicsBridge Function ConfigureRealmPhysics ********************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics
{
	struct AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar f\xc3\x83\xc2\xadsica para realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar f\xc3\x83\xc2\xadsica para realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ConfigureRealmPhysics", Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::AuracronPhysicsBridge_eventConfigureRealmPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execConfigureRealmPhysics)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureRealmPhysics(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ConfigureRealmPhysics **********************

// ********** Begin Class UAuracronPhysicsBridge Function ConvertToGeometryCollection **************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics
{
	struct AuracronPhysicsBridge_eventConvertToGeometryCollection_Parms
	{
		AActor* TargetActor;
		AGeometryCollectionActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Converter para Geometry Collection\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Converter para Geometry Collection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventConvertToGeometryCollection_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventConvertToGeometryCollection_Parms, ReturnValue), Z_Construct_UClass_AGeometryCollectionActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "ConvertToGeometryCollection", Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::AuracronPhysicsBridge_eventConvertToGeometryCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::AuracronPhysicsBridge_eventConvertToGeometryCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execConvertToGeometryCollection)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AGeometryCollectionActor**)Z_Param__Result=P_THIS->ConvertToGeometryCollection(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function ConvertToGeometryCollection ****************

// ********** Begin Class UAuracronPhysicsBridge Function CreateDirectionalForceField **************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics
{
	struct AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms
	{
		FVector Location;
		FVector Direction;
		float Force;
		float Radius;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|FieldSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar campo de for\xc3\x83\xc2\xa7""a direcional\n     */" },
#endif
		{ "CPP_Default_Duration", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar campo de for\xc3\x83\xc2\xa7""a direcional" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CreateDirectionalForceField", Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::AuracronPhysicsBridge_eventCreateDirectionalForceField_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCreateDirectionalForceField)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateDirectionalForceField(Z_Param_Out_Location,Z_Param_Out_Direction,Z_Param_Force,Z_Param_Radius,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CreateDirectionalForceField ****************

// ********** Begin Class UAuracronPhysicsBridge Function CreateExplosion **************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics
{
	struct AuracronPhysicsBridge_eventCreateExplosion_Parms
	{
		FVector Location;
		FAuracronDestructionConfiguration DestructionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar explos\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar explos\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateExplosion_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_DestructionConfig = { "DestructionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateExplosion_Parms, DestructionConfig), Z_Construct_UScriptStruct_FAuracronDestructionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionConfig_MetaData), NewProp_DestructionConfig_MetaData) }; // 3171473727
void Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCreateExplosion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCreateExplosion_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_DestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CreateExplosion", Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::AuracronPhysicsBridge_eventCreateExplosion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::AuracronPhysicsBridge_eventCreateExplosion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCreateExplosion)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronDestructionConfiguration,Z_Param_Out_DestructionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateExplosion(Z_Param_Out_Location,Z_Param_Out_DestructionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CreateExplosion ****************************

// ********** Begin Class UAuracronPhysicsBridge Function CreateRadialForceField *******************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics
{
	struct AuracronPhysicsBridge_eventCreateRadialForceField_Parms
	{
		FVector Location;
		float Force;
		float Radius;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|FieldSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar campo de for\xc3\x83\xc2\xa7""a radial\n     */" },
#endif
		{ "CPP_Default_Duration", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar campo de for\xc3\x83\xc2\xa7""a radial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateRadialForceField_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateRadialForceField_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateRadialForceField_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateRadialForceField_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCreateRadialForceField_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCreateRadialForceField_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CreateRadialForceField", Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::AuracronPhysicsBridge_eventCreateRadialForceField_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::AuracronPhysicsBridge_eventCreateRadialForceField_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCreateRadialForceField)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateRadialForceField(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CreateRadialForceField *********************

// ********** Begin Class UAuracronPhysicsBridge Function CreateSpecialPhysicsZone *****************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics
{
	struct AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms
	{
		FVector Location;
		float Radius;
		FString ZoneType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar zona de f\xc3\x83\xc2\xadsica especial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar zona de f\xc3\x83\xc2\xadsica especial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ZoneType = { "ZoneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms, ZoneType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneType_MetaData), NewProp_ZoneType_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ZoneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CreateSpecialPhysicsZone", Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::AuracronPhysicsBridge_eventCreateSpecialPhysicsZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCreateSpecialPhysicsZone)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FStrProperty,Z_Param_ZoneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateSpecialPhysicsZone(Z_Param_Out_Location,Z_Param_Radius,Z_Param_ZoneType);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CreateSpecialPhysicsZone *******************

// ********** Begin Class UAuracronPhysicsBridge Function CreateVortexField ************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics
{
	struct AuracronPhysicsBridge_eventCreateVortexField_Parms
	{
		FVector Location;
		FVector Axis;
		float Force;
		float Radius;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|FieldSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar campo de v\xc3\x83\xc2\xb3rtice\n     */" },
#endif
		{ "CPP_Default_Duration", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar campo de v\xc3\x83\xc2\xb3rtice" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Axis_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Axis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateVortexField_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Axis = { "Axis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateVortexField_Parms, Axis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Axis_MetaData), NewProp_Axis_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateVortexField_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateVortexField_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventCreateVortexField_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventCreateVortexField_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventCreateVortexField_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Axis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "CreateVortexField", Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::AuracronPhysicsBridge_eventCreateVortexField_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::AuracronPhysicsBridge_eventCreateVortexField_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execCreateVortexField)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Axis);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateVortexField(Z_Param_Out_Location,Z_Param_Out_Axis,Z_Param_Force,Z_Param_Radius,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function CreateVortexField **************************

// ********** Begin Class UAuracronPhysicsBridge Function DestroyObject ****************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics
{
	struct AuracronPhysicsBridge_eventDestroyObject_Parms
	{
		AActor* TargetActor;
		FAuracronDestructionConfiguration DestructionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Destruir objeto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destruir objeto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventDestroyObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_DestructionConfig = { "DestructionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventDestroyObject_Parms, DestructionConfig), Z_Construct_UScriptStruct_FAuracronDestructionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionConfig_MetaData), NewProp_DestructionConfig_MetaData) }; // 3171473727
void Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventDestroyObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventDestroyObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_DestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "DestroyObject", Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::AuracronPhysicsBridge_eventDestroyObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::AuracronPhysicsBridge_eventDestroyObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execDestroyObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronDestructionConfiguration,Z_Param_Out_DestructionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroyObject(Z_Param_TargetActor,Z_Param_Out_DestructionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function DestroyObject ******************************

// ********** Begin Class UAuracronPhysicsBridge Function FractureObject ***************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics
{
	struct AuracronPhysicsBridge_eventFractureObject_Parms
	{
		AActor* TargetActor;
		FVector ImpactLocation;
		float Force;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Fraturar objeto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fraturar objeto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpactLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventFractureObject_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ImpactLocation = { "ImpactLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventFractureObject_Parms, ImpactLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactLocation_MetaData), NewProp_ImpactLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventFractureObject_Parms, Force), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventFractureObject_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventFractureObject_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ImpactLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "FractureObject", Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::AuracronPhysicsBridge_eventFractureObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::AuracronPhysicsBridge_eventFractureObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execFractureObject)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ImpactLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FractureObject(Z_Param_TargetActor,Z_Param_Out_ImpactLocation,Z_Param_Force);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function FractureObject *****************************

// ********** Begin Class UAuracronPhysicsBridge Function OptimizePhysicsByDistance ****************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics
{
	struct AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms
	{
		FVector ViewerLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar f\xc3\x83\xc2\xadsica por dist\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar f\xc3\x83\xc2\xadsica por dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "OptimizePhysicsByDistance", Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::AuracronPhysicsBridge_eventOptimizePhysicsByDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execOptimizePhysicsByDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizePhysicsByDistance(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function OptimizePhysicsByDistance ******************

// ********** Begin Class UAuracronPhysicsBridge Function SetCustomGravity *************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics
{
	struct AuracronPhysicsBridge_eventSetCustomGravity_Parms
	{
		FVector NewGravity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Gravity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir gravidade customizada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir gravidade customizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewGravity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewGravity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_NewGravity = { "NewGravity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventSetCustomGravity_Parms, NewGravity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewGravity_MetaData), NewProp_NewGravity_MetaData) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventSetCustomGravity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventSetCustomGravity_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_NewGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "SetCustomGravity", Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::AuracronPhysicsBridge_eventSetCustomGravity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::AuracronPhysicsBridge_eventSetCustomGravity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execSetCustomGravity)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewGravity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetCustomGravity(Z_Param_Out_NewGravity);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function SetCustomGravity ***************************

// ********** Begin Class UAuracronPhysicsBridge Function SetPhysicsQuality ************************
struct Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics
{
	struct AuracronPhysicsBridge_eventSetPhysicsQuality_Parms
	{
		int32 QualityLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Physics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir qualidade de f\xc3\x83\xc2\xadsica\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir qualidade de f\xc3\x83\xc2\xadsica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPhysicsBridge_eventSetPhysicsQuality_Parms, QualityLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPhysicsBridge_eventSetPhysicsQuality_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPhysicsBridge_eventSetPhysicsQuality_Parms), &Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPhysicsBridge, nullptr, "SetPhysicsQuality", Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::AuracronPhysicsBridge_eventSetPhysicsQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::AuracronPhysicsBridge_eventSetPhysicsQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPhysicsBridge::execSetPhysicsQuality)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetPhysicsQuality(Z_Param_QualityLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronPhysicsBridge Function SetPhysicsQuality **************************

// ********** Begin Class UAuracronPhysicsBridge ***************************************************
void UAuracronPhysicsBridge::StaticRegisterNativesUAuracronPhysicsBridge()
{
	UClass* Class = UAuracronPhysicsBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyCustomGravityToObject", &UAuracronPhysicsBridge::execApplyCustomGravityToObject },
		{ "ApplyFieldSystem", &UAuracronPhysicsBridge::execApplyFieldSystem },
		{ "ApplyForceToObject", &UAuracronPhysicsBridge::execApplyForceToObject },
		{ "ApplyImpulseToObject", &UAuracronPhysicsBridge::execApplyImpulseToObject },
		{ "ApplyRealmPhysicsToObject", &UAuracronPhysicsBridge::execApplyRealmPhysicsToObject },
		{ "ApplyTorqueToObject", &UAuracronPhysicsBridge::execApplyTorqueToObject },
		{ "CleanupInactivePhysicsObjects", &UAuracronPhysicsBridge::execCleanupInactivePhysicsObjects },
		{ "ConfigureRealmPhysics", &UAuracronPhysicsBridge::execConfigureRealmPhysics },
		{ "ConvertToGeometryCollection", &UAuracronPhysicsBridge::execConvertToGeometryCollection },
		{ "CreateDirectionalForceField", &UAuracronPhysicsBridge::execCreateDirectionalForceField },
		{ "CreateExplosion", &UAuracronPhysicsBridge::execCreateExplosion },
		{ "CreateRadialForceField", &UAuracronPhysicsBridge::execCreateRadialForceField },
		{ "CreateSpecialPhysicsZone", &UAuracronPhysicsBridge::execCreateSpecialPhysicsZone },
		{ "CreateVortexField", &UAuracronPhysicsBridge::execCreateVortexField },
		{ "DestroyObject", &UAuracronPhysicsBridge::execDestroyObject },
		{ "FractureObject", &UAuracronPhysicsBridge::execFractureObject },
		{ "OptimizePhysicsByDistance", &UAuracronPhysicsBridge::execOptimizePhysicsByDistance },
		{ "SetCustomGravity", &UAuracronPhysicsBridge::execSetCustomGravity },
		{ "SetPhysicsQuality", &UAuracronPhysicsBridge::execSetPhysicsQuality },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPhysicsBridge;
UClass* UAuracronPhysicsBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronPhysicsBridge;
	if (!Z_Registration_Info_UClass_UAuracronPhysicsBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPhysicsBridge"),
			Z_Registration_Info_UClass_UAuracronPhysicsBridge.InnerSingleton,
			StaticRegisterNativesUAuracronPhysicsBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPhysicsBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister()
{
	return UAuracronPhysicsBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPhysicsBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de F\xc3\x83\xc2\xadsica Chaos\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de f\xc3\x83\xc2\xadsica e destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "DisplayName", "AURACRON Physics Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronPhysicsBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de F\xc3\x83\xc2\xadsica Chaos\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de f\xc3\x83\xc2\xadsica e destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosPhysicsConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica Chaos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de f\xc3\x83\xc2\xadsica Chaos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDestructionConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o padr\xc3\x83\xc2\xa3o de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o padr\xc3\x83\xc2\xa3o de destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFieldSystemConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o padr\xc3\x83\xc2\xa3o de Field System */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o padr\xc3\x83\xc2\xa3o de Field System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePhysicsObjects_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objetos f\xc3\x83\xc2\xadsicos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetos f\xc3\x83\xc2\xadsicos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveFieldComponents_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de Field System ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de Field System ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosSolver_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Chaos Solver */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Chaos Solver" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectDestroyed_MetaData[] = {
		{ "Category", "AURACRON Physics|Events" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFieldSystemApplied_MetaData[] = {
		{ "Category", "AURACRON Physics|Events" },
		{ "ModuleRelativePath", "Public/AuracronPhysicsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChaosPhysicsConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultDestructionConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultFieldSystemConfiguration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivePhysicsObjects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActivePhysicsObjects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveFieldComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveFieldComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosSolver;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFieldSystemApplied;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyCustomGravityToObject, "ApplyCustomGravityToObject" }, // 3565001969
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyFieldSystem, "ApplyFieldSystem" }, // 192867662
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyForceToObject, "ApplyForceToObject" }, // 1117468157
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyImpulseToObject, "ApplyImpulseToObject" }, // 4062718816
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyRealmPhysicsToObject, "ApplyRealmPhysicsToObject" }, // 1972021981
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ApplyTorqueToObject, "ApplyTorqueToObject" }, // 2362908801
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CleanupInactivePhysicsObjects, "CleanupInactivePhysicsObjects" }, // 3091802702
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ConfigureRealmPhysics, "ConfigureRealmPhysics" }, // 3132614915
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_ConvertToGeometryCollection, "ConvertToGeometryCollection" }, // 4280424966
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateDirectionalForceField, "CreateDirectionalForceField" }, // 1698955833
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateExplosion, "CreateExplosion" }, // 4017684342
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateRadialForceField, "CreateRadialForceField" }, // 114755873
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateSpecialPhysicsZone, "CreateSpecialPhysicsZone" }, // 3007579664
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_CreateVortexField, "CreateVortexField" }, // 1328556030
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_DestroyObject, "DestroyObject" }, // 232089712
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_FractureObject, "FractureObject" }, // 3177906675
		{ &Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature, "OnFieldSystemApplied__DelegateSignature" }, // 3809126441
		{ &Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature, "OnObjectDestroyed__DelegateSignature" }, // 1409171112
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_OptimizePhysicsByDistance, "OptimizePhysicsByDistance" }, // 1240256002
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_SetCustomGravity, "SetCustomGravity" }, // 429311762
		{ &Z_Construct_UFunction_UAuracronPhysicsBridge_SetPhysicsQuality, "SetPhysicsQuality" }, // 19901910
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPhysicsBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ChaosPhysicsConfiguration = { "ChaosPhysicsConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, ChaosPhysicsConfiguration), Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosPhysicsConfiguration_MetaData), NewProp_ChaosPhysicsConfiguration_MetaData) }; // 2814242161
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_DefaultDestructionConfiguration = { "DefaultDestructionConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, DefaultDestructionConfiguration), Z_Construct_UScriptStruct_FAuracronDestructionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDestructionConfiguration_MetaData), NewProp_DefaultDestructionConfiguration_MetaData) }; // 3171473727
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_DefaultFieldSystemConfiguration = { "DefaultFieldSystemConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, DefaultFieldSystemConfiguration), Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFieldSystemConfiguration_MetaData), NewProp_DefaultFieldSystemConfiguration_MetaData) }; // 1266813753
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActivePhysicsObjects_Inner = { "ActivePhysicsObjects", nullptr, (EPropertyFlags)0x0104000000020000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActivePhysicsObjects = { "ActivePhysicsObjects", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, ActivePhysicsObjects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePhysicsObjects_MetaData), NewProp_ActivePhysicsObjects_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActiveFieldComponents_Inner = { "ActiveFieldComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UFieldSystemComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActiveFieldComponents = { "ActiveFieldComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, ActiveFieldComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveFieldComponents_MetaData), NewProp_ActiveFieldComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ChaosSolver = { "ChaosSolver", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, ChaosSolver), Z_Construct_UClass_AChaosSolverActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosSolver_MetaData), NewProp_ChaosSolver_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_OnObjectDestroyed = { "OnObjectDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, OnObjectDestroyed), Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnObjectDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectDestroyed_MetaData), NewProp_OnObjectDestroyed_MetaData) }; // 1409171112
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_OnFieldSystemApplied = { "OnFieldSystemApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPhysicsBridge, OnFieldSystemApplied), Z_Construct_UDelegateFunction_UAuracronPhysicsBridge_OnFieldSystemApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFieldSystemApplied_MetaData), NewProp_OnFieldSystemApplied_MetaData) }; // 3809126441
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPhysicsBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ChaosPhysicsConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_DefaultDestructionConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_DefaultFieldSystemConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActivePhysicsObjects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActivePhysicsObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActiveFieldComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ActiveFieldComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_ChaosSolver,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_OnObjectDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPhysicsBridge_Statics::NewProp_OnFieldSystemApplied,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPhysicsBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPhysicsBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPhysicsBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPhysicsBridge_Statics::ClassParams = {
	&UAuracronPhysicsBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPhysicsBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPhysicsBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPhysicsBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPhysicsBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPhysicsBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronPhysicsBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPhysicsBridge.OuterSingleton, Z_Construct_UClass_UAuracronPhysicsBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPhysicsBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronPhysicsBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ChaosPhysicsConfiguration(TEXT("ChaosPhysicsConfiguration"));
	const bool bIsValid = true
		&& Name_ChaosPhysicsConfiguration == ClassReps[(int32)ENetFields_Private::ChaosPhysicsConfiguration].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronPhysicsBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPhysicsBridge);
UAuracronPhysicsBridge::~UAuracronPhysicsBridge() {}
// ********** End Class UAuracronPhysicsBridge *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPhysicsType_StaticEnum, TEXT("EAuracronPhysicsType"), &Z_Registration_Info_UEnum_EAuracronPhysicsType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2670421062U) },
		{ EAuracronDestructionType_StaticEnum, TEXT("EAuracronDestructionType"), &Z_Registration_Info_UEnum_EAuracronDestructionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2662684909U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronChaosPhysicsConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics::NewStructOps, TEXT("AuracronChaosPhysicsConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronChaosPhysicsConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChaosPhysicsConfiguration), 2814242161U) },
		{ FAuracronDestructionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics::NewStructOps, TEXT("AuracronDestructionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDestructionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDestructionConfiguration), 3171473727U) },
		{ FAuracronFieldSystemConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics::NewStructOps, TEXT("AuracronFieldSystemConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFieldSystemConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFieldSystemConfiguration), 1266813753U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPhysicsBridge, UAuracronPhysicsBridge::StaticClass, TEXT("UAuracronPhysicsBridge"), &Z_Registration_Info_UClass_UAuracronPhysicsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPhysicsBridge), 4056465116U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_3917313278(TEXT("/Script/AuracronPhysicsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h__Script_AuracronPhysicsBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
