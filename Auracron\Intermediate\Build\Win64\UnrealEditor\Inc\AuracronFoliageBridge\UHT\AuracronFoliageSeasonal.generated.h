// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageSeasonal.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageSeasonal_generated_h
#error "AuracronFoliageSeasonal.generated.h already included, missing '#pragma once' in AuracronFoliageSeasonal.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageSeasonal_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronFoliageBiomeManager;
class UAuracronFoliageSeasonalManager;
class UAuracronFoliageWindManager;
class UMaterialParameterCollection;
class UWorld;
enum class EAuracronGrowthPhase : uint8;
enum class EAuracronLifecycleEvent : uint8;
enum class EAuracronSeasonType : uint8;
struct FAuracronGrowthSimulationData;
struct FAuracronLifecycleData;
struct FAuracronSeasonalColorData;
struct FAuracronSeasonalConfiguration;
struct FAuracronSeasonalPerformanceData;
struct FLinearColor;

// ********** Begin ScriptStruct FAuracronSeasonalConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSeasonalConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSeasonalConfiguration;
// ********** End ScriptStruct FAuracronSeasonalConfiguration **************************************

// ********** Begin ScriptStruct FAuracronSeasonalColorData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_240_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSeasonalColorData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSeasonalColorData;
// ********** End ScriptStruct FAuracronSeasonalColorData ******************************************

// ********** Begin ScriptStruct FAuracronGrowthSimulationData *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_308_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGrowthSimulationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGrowthSimulationData;
// ********** End ScriptStruct FAuracronGrowthSimulationData ***************************************

// ********** Begin ScriptStruct FAuracronLifecycleData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_406_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLifecycleData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLifecycleData;
// ********** End ScriptStruct FAuracronLifecycleData **********************************************

// ********** Begin ScriptStruct FAuracronSeasonalPerformanceData **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_503_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSeasonalPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSeasonalPerformanceData;
// ********** End ScriptStruct FAuracronSeasonalPerformanceData ************************************

// ********** Begin Delegate FOnSeasonChanged ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_722_DELEGATE \
static void FOnSeasonChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSeasonChanged, EAuracronSeasonType NewSeason);


// ********** End Delegate FOnSeasonChanged ********************************************************

// ********** Begin Delegate FOnGrowthPhaseChanged *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_723_DELEGATE \
static void FOnGrowthPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGrowthPhaseChanged, const FString& GrowthId, EAuracronGrowthPhase NewPhase);


// ********** End Delegate FOnGrowthPhaseChanged ***************************************************

// ********** Begin Delegate FOnLifecycleEventTriggered ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_724_DELEGATE \
static void FOnLifecycleEventTriggered_DelegateWrapper(const FMulticastScriptDelegate& OnLifecycleEventTriggered, const FString& LifecycleId, EAuracronLifecycleEvent Event);


// ********** End Delegate FOnLifecycleEventTriggered **********************************************

// ********** Begin Delegate FOnColorVariationUpdated **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_725_DELEGATE \
static void FOnColorVariationUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnColorVariationUpdated, const FString& FoliageTypeId, FLinearColor NewColor);


// ********** End Delegate FOnColorVariationUpdated ************************************************

// ********** Begin Class UAuracronFoliageSeasonalManager ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogSeasonalStatistics); \
	DECLARE_FUNCTION(execDrawDebugSeasonalInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetGrowingInstanceCount); \
	DECLARE_FUNCTION(execGetActiveSeasonalInstanceCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execSynchronizeWithClimateData); \
	DECLARE_FUNCTION(execIntegrateWithWindSystem); \
	DECLARE_FUNCTION(execIntegrateWithBiomeSystem); \
	DECLARE_FUNCTION(execGetMaterialParameterCollection); \
	DECLARE_FUNCTION(execSetMaterialParameterCollection); \
	DECLARE_FUNCTION(execUpdateMaterialParameters); \
	DECLARE_FUNCTION(execApplyDensityChangesToBiome); \
	DECLARE_FUNCTION(execGetSeasonalDensityMultiplier); \
	DECLARE_FUNCTION(execUpdateDensityChanges); \
	DECLARE_FUNCTION(execTriggerLifecycleEvent); \
	DECLARE_FUNCTION(execUpdateAllLifecycleManagement); \
	DECLARE_FUNCTION(execGetLifecycleManagement); \
	DECLARE_FUNCTION(execRemoveLifecycleManagement); \
	DECLARE_FUNCTION(execUpdateLifecycleManagement); \
	DECLARE_FUNCTION(execCreateLifecycleManagement); \
	DECLARE_FUNCTION(execUpdateGrowthSimulations); \
	DECLARE_FUNCTION(execGetGrowthSimulation); \
	DECLARE_FUNCTION(execRemoveGrowthSimulation); \
	DECLARE_FUNCTION(execUpdateGrowthSimulation); \
	DECLARE_FUNCTION(execCreateGrowthSimulation); \
	DECLARE_FUNCTION(execUpdateColorVariation); \
	DECLARE_FUNCTION(execGetCurrentSeasonalColor); \
	DECLARE_FUNCTION(execGetSeasonalColors); \
	DECLARE_FUNCTION(execSetSeasonalColors); \
	DECLARE_FUNCTION(execGetCurrentGameDay); \
	DECLARE_FUNCTION(execGetCurrentGameTime); \
	DECLARE_FUNCTION(execGetTimeAcceleration); \
	DECLARE_FUNCTION(execSetTimeAcceleration); \
	DECLARE_FUNCTION(execAdvanceSeasonByDays); \
	DECLARE_FUNCTION(execSetSeasonProgress); \
	DECLARE_FUNCTION(execGetSeasonProgress); \
	DECLARE_FUNCTION(execGetCurrentSeason); \
	DECLARE_FUNCTION(execSetCurrentSeason); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageSeasonalManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageSeasonalManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageSeasonalManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageSeasonalManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageSeasonalManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageSeasonalManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageSeasonalManager(UAuracronFoliageSeasonalManager&&) = delete; \
	UAuracronFoliageSeasonalManager(const UAuracronFoliageSeasonalManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageSeasonalManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageSeasonalManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageSeasonalManager) \
	NO_API virtual ~UAuracronFoliageSeasonalManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_558_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h_561_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageSeasonalManager;

// ********** End Class UAuracronFoliageSeasonalManager ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageSeasonal_h

// ********** Begin Enum EAuracronSeasonType *******************************************************
#define FOREACH_ENUM_EAURACRONSEASONTYPE(op) \
	op(EAuracronSeasonType::Spring) \
	op(EAuracronSeasonType::Summer) \
	op(EAuracronSeasonType::Autumn) \
	op(EAuracronSeasonType::Winter) \
	op(EAuracronSeasonType::WetSeason) \
	op(EAuracronSeasonType::DrySeason) \
	op(EAuracronSeasonType::Custom) 

enum class EAuracronSeasonType : uint8;
template<> struct TIsUEnumClass<EAuracronSeasonType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonType>();
// ********** End Enum EAuracronSeasonType *********************************************************

// ********** Begin Enum EAuracronGrowthPhase ******************************************************
#define FOREACH_ENUM_EAURACRONGROWTHPHASE(op) \
	op(EAuracronGrowthPhase::Seed) \
	op(EAuracronGrowthPhase::Sprout) \
	op(EAuracronGrowthPhase::Juvenile) \
	op(EAuracronGrowthPhase::Mature) \
	op(EAuracronGrowthPhase::Flowering) \
	op(EAuracronGrowthPhase::Fruiting) \
	op(EAuracronGrowthPhase::Senescent) \
	op(EAuracronGrowthPhase::Dormant) \
	op(EAuracronGrowthPhase::Dead) 

enum class EAuracronGrowthPhase : uint8;
template<> struct TIsUEnumClass<EAuracronGrowthPhase> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronGrowthPhase>();
// ********** End Enum EAuracronGrowthPhase ********************************************************

// ********** Begin Enum EAuracronSeasonalChangeType ***********************************************
#define FOREACH_ENUM_EAURACRONSEASONALCHANGETYPE(op) \
	op(EAuracronSeasonalChangeType::ColorVariation) \
	op(EAuracronSeasonalChangeType::DensityChange) \
	op(EAuracronSeasonalChangeType::GrowthSimulation) \
	op(EAuracronSeasonalChangeType::LifecycleManagement) \
	op(EAuracronSeasonalChangeType::MaterialTransition) \
	op(EAuracronSeasonalChangeType::ScaleVariation) \
	op(EAuracronSeasonalChangeType::Custom) 

enum class EAuracronSeasonalChangeType : uint8;
template<> struct TIsUEnumClass<EAuracronSeasonalChangeType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonalChangeType>();
// ********** End Enum EAuracronSeasonalChangeType *************************************************

// ********** Begin Enum EAuracronLifecycleEvent ***************************************************
#define FOREACH_ENUM_EAURACRONLIFECYCLEEVENT(op) \
	op(EAuracronLifecycleEvent::Germination) \
	op(EAuracronLifecycleEvent::LeafBudding) \
	op(EAuracronLifecycleEvent::Flowering) \
	op(EAuracronLifecycleEvent::Fruiting) \
	op(EAuracronLifecycleEvent::LeafSenescence) \
	op(EAuracronLifecycleEvent::LeafDrop) \
	op(EAuracronLifecycleEvent::Dormancy) \
	op(EAuracronLifecycleEvent::Death) \
	op(EAuracronLifecycleEvent::Regeneration) 

enum class EAuracronLifecycleEvent : uint8;
template<> struct TIsUEnumClass<EAuracronLifecycleEvent> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLifecycleEvent>();
// ********** End Enum EAuracronLifecycleEvent *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
