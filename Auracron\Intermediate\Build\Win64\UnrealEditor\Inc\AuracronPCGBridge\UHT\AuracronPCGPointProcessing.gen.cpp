// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGPointProcessing.h"
#include "PCGPoint.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGPointProcessing() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FMatrix();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
PCG_API UClass* Z_Construct_UClass_UPCGMetadata_NoRegister();
PCG_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPoint();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGProcessingOperation *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation;
static UEnum* EAuracronPCGProcessingOperation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGProcessingOperation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGProcessingOperation>()
{
	return EAuracronPCGProcessingOperation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cluster.DisplayName", "Cluster" },
		{ "Cluster.Name", "EAuracronPCGProcessingOperation::Cluster" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Processing operation types\n" },
#endif
		{ "Duplicate.DisplayName", "Duplicate" },
		{ "Duplicate.Name", "EAuracronPCGProcessingOperation::Duplicate" },
		{ "Filter.DisplayName", "Filter" },
		{ "Filter.Name", "EAuracronPCGProcessingOperation::Filter" },
		{ "Merge.DisplayName", "Merge" },
		{ "Merge.Name", "EAuracronPCGProcessingOperation::Merge" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
		{ "Prune.DisplayName", "Prune" },
		{ "Prune.Name", "EAuracronPCGProcessingOperation::Prune" },
		{ "Sort.DisplayName", "Sort" },
		{ "Sort.Name", "EAuracronPCGProcessingOperation::Sort" },
		{ "Split.DisplayName", "Split" },
		{ "Split.Name", "EAuracronPCGProcessingOperation::Split" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing operation types" },
#endif
		{ "Transform.DisplayName", "Transform" },
		{ "Transform.Name", "EAuracronPCGProcessingOperation::Transform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGProcessingOperation::Filter", (int64)EAuracronPCGProcessingOperation::Filter },
		{ "EAuracronPCGProcessingOperation::Transform", (int64)EAuracronPCGProcessingOperation::Transform },
		{ "EAuracronPCGProcessingOperation::Merge", (int64)EAuracronPCGProcessingOperation::Merge },
		{ "EAuracronPCGProcessingOperation::Split", (int64)EAuracronPCGProcessingOperation::Split },
		{ "EAuracronPCGProcessingOperation::Sort", (int64)EAuracronPCGProcessingOperation::Sort },
		{ "EAuracronPCGProcessingOperation::Duplicate", (int64)EAuracronPCGProcessingOperation::Duplicate },
		{ "EAuracronPCGProcessingOperation::Prune", (int64)EAuracronPCGProcessingOperation::Prune },
		{ "EAuracronPCGProcessingOperation::Cluster", (int64)EAuracronPCGProcessingOperation::Cluster },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGProcessingOperation",
	"EAuracronPCGProcessingOperation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation.InnerSingleton;
}
// ********** End Enum EAuracronPCGProcessingOperation *********************************************

// ********** Begin Enum EAuracronPCGMathOperation *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMathOperation;
static UEnum* EAuracronPCGMathOperation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMathOperation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMathOperation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMathOperation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMathOperation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMathOperation>()
{
	return EAuracronPCGMathOperation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abs.DisplayName", "Absolute" },
		{ "Abs.Name", "EAuracronPCGMathOperation::Abs" },
		{ "Add.DisplayName", "Add" },
		{ "Add.Name", "EAuracronPCGMathOperation::Add" },
		{ "Average.DisplayName", "Average" },
		{ "Average.Name", "EAuracronPCGMathOperation::Average" },
		{ "BlueprintType", "true" },
		{ "Ceil.DisplayName", "Ceiling" },
		{ "Ceil.Name", "EAuracronPCGMathOperation::Ceil" },
		{ "Clamp.DisplayName", "Clamp" },
		{ "Clamp.Name", "EAuracronPCGMathOperation::Clamp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mathematical operations for point processing\n" },
#endif
		{ "Cos.DisplayName", "Cosine" },
		{ "Cos.Name", "EAuracronPCGMathOperation::Cos" },
		{ "Divide.DisplayName", "Divide" },
		{ "Divide.Name", "EAuracronPCGMathOperation::Divide" },
		{ "Exp.DisplayName", "Exponential" },
		{ "Exp.Name", "EAuracronPCGMathOperation::Exp" },
		{ "Floor.DisplayName", "Floor" },
		{ "Floor.Name", "EAuracronPCGMathOperation::Floor" },
		{ "Frac.DisplayName", "Fractional" },
		{ "Frac.Name", "EAuracronPCGMathOperation::Frac" },
		{ "Lerp.DisplayName", "Lerp" },
		{ "Lerp.Name", "EAuracronPCGMathOperation::Lerp" },
		{ "Log.DisplayName", "Logarithm" },
		{ "Log.Name", "EAuracronPCGMathOperation::Log" },
		{ "Max.DisplayName", "Max" },
		{ "Max.Name", "EAuracronPCGMathOperation::Max" },
		{ "Min.DisplayName", "Min" },
		{ "Min.Name", "EAuracronPCGMathOperation::Min" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
		{ "Modulo.DisplayName", "Modulo" },
		{ "Modulo.Name", "EAuracronPCGMathOperation::Modulo" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronPCGMathOperation::Multiply" },
		{ "Normalize.DisplayName", "Normalize" },
		{ "Normalize.Name", "EAuracronPCGMathOperation::Normalize" },
		{ "Power.DisplayName", "Power" },
		{ "Power.Name", "EAuracronPCGMathOperation::Power" },
		{ "Round.DisplayName", "Round" },
		{ "Round.Name", "EAuracronPCGMathOperation::Round" },
		{ "Sign.DisplayName", "Sign" },
		{ "Sign.Name", "EAuracronPCGMathOperation::Sign" },
		{ "Sin.DisplayName", "Sine" },
		{ "Sin.Name", "EAuracronPCGMathOperation::Sin" },
		{ "Sqrt.DisplayName", "Square Root" },
		{ "Sqrt.Name", "EAuracronPCGMathOperation::Sqrt" },
		{ "Subtract.DisplayName", "Subtract" },
		{ "Subtract.Name", "EAuracronPCGMathOperation::Subtract" },
		{ "Tan.DisplayName", "Tangent" },
		{ "Tan.Name", "EAuracronPCGMathOperation::Tan" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mathematical operations for point processing" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMathOperation::Add", (int64)EAuracronPCGMathOperation::Add },
		{ "EAuracronPCGMathOperation::Subtract", (int64)EAuracronPCGMathOperation::Subtract },
		{ "EAuracronPCGMathOperation::Multiply", (int64)EAuracronPCGMathOperation::Multiply },
		{ "EAuracronPCGMathOperation::Divide", (int64)EAuracronPCGMathOperation::Divide },
		{ "EAuracronPCGMathOperation::Power", (int64)EAuracronPCGMathOperation::Power },
		{ "EAuracronPCGMathOperation::Modulo", (int64)EAuracronPCGMathOperation::Modulo },
		{ "EAuracronPCGMathOperation::Min", (int64)EAuracronPCGMathOperation::Min },
		{ "EAuracronPCGMathOperation::Max", (int64)EAuracronPCGMathOperation::Max },
		{ "EAuracronPCGMathOperation::Average", (int64)EAuracronPCGMathOperation::Average },
		{ "EAuracronPCGMathOperation::Lerp", (int64)EAuracronPCGMathOperation::Lerp },
		{ "EAuracronPCGMathOperation::Clamp", (int64)EAuracronPCGMathOperation::Clamp },
		{ "EAuracronPCGMathOperation::Normalize", (int64)EAuracronPCGMathOperation::Normalize },
		{ "EAuracronPCGMathOperation::Abs", (int64)EAuracronPCGMathOperation::Abs },
		{ "EAuracronPCGMathOperation::Sign", (int64)EAuracronPCGMathOperation::Sign },
		{ "EAuracronPCGMathOperation::Floor", (int64)EAuracronPCGMathOperation::Floor },
		{ "EAuracronPCGMathOperation::Ceil", (int64)EAuracronPCGMathOperation::Ceil },
		{ "EAuracronPCGMathOperation::Round", (int64)EAuracronPCGMathOperation::Round },
		{ "EAuracronPCGMathOperation::Frac", (int64)EAuracronPCGMathOperation::Frac },
		{ "EAuracronPCGMathOperation::Sqrt", (int64)EAuracronPCGMathOperation::Sqrt },
		{ "EAuracronPCGMathOperation::Log", (int64)EAuracronPCGMathOperation::Log },
		{ "EAuracronPCGMathOperation::Exp", (int64)EAuracronPCGMathOperation::Exp },
		{ "EAuracronPCGMathOperation::Sin", (int64)EAuracronPCGMathOperation::Sin },
		{ "EAuracronPCGMathOperation::Cos", (int64)EAuracronPCGMathOperation::Cos },
		{ "EAuracronPCGMathOperation::Tan", (int64)EAuracronPCGMathOperation::Tan },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMathOperation",
	"EAuracronPCGMathOperation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMathOperation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMathOperation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMathOperation.InnerSingleton;
}
// ********** End Enum EAuracronPCGMathOperation ***************************************************

// ********** Begin Enum EAuracronPCGSortCriteria **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSortCriteria;
static UEnum* EAuracronPCGSortCriteria_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSortCriteria"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSortCriteria>()
{
	return EAuracronPCGSortCriteria_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attribute.DisplayName", "Custom Attribute" },
		{ "Attribute.Name", "EAuracronPCGSortCriteria::Attribute" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sorting criteria for point sorting\n" },
#endif
		{ "Density.DisplayName", "Density" },
		{ "Density.Name", "EAuracronPCGSortCriteria::Density" },
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EAuracronPCGSortCriteria::Distance" },
		{ "Index.DisplayName", "Index" },
		{ "Index.Name", "EAuracronPCGSortCriteria::Index" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
		{ "Position.DisplayName", "Position" },
		{ "Position.Name", "EAuracronPCGSortCriteria::Position" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronPCGSortCriteria::Random" },
		{ "Rotation.DisplayName", "Rotation" },
		{ "Rotation.Name", "EAuracronPCGSortCriteria::Rotation" },
		{ "Scale.DisplayName", "Scale" },
		{ "Scale.Name", "EAuracronPCGSortCriteria::Scale" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sorting criteria for point sorting" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSortCriteria::Position", (int64)EAuracronPCGSortCriteria::Position },
		{ "EAuracronPCGSortCriteria::Distance", (int64)EAuracronPCGSortCriteria::Distance },
		{ "EAuracronPCGSortCriteria::Density", (int64)EAuracronPCGSortCriteria::Density },
		{ "EAuracronPCGSortCriteria::Scale", (int64)EAuracronPCGSortCriteria::Scale },
		{ "EAuracronPCGSortCriteria::Rotation", (int64)EAuracronPCGSortCriteria::Rotation },
		{ "EAuracronPCGSortCriteria::Attribute", (int64)EAuracronPCGSortCriteria::Attribute },
		{ "EAuracronPCGSortCriteria::Random", (int64)EAuracronPCGSortCriteria::Random },
		{ "EAuracronPCGSortCriteria::Index", (int64)EAuracronPCGSortCriteria::Index },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSortCriteria",
	"EAuracronPCGSortCriteria",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSortCriteria.InnerSingleton;
}
// ********** End Enum EAuracronPCGSortCriteria ****************************************************

// ********** Begin Enum EAuracronPCGMergeStrategy *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy;
static UEnum* EAuracronPCGMergeStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGMergeStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMergeStrategy>()
{
	return EAuracronPCGMergeStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Append.DisplayName", "Append" },
		{ "Append.Name", "EAuracronPCGMergeStrategy::Append" },
		{ "Attribute.DisplayName", "Attribute Based" },
		{ "Attribute.Name", "EAuracronPCGMergeStrategy::Attribute" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Merge strategies for point merging\n" },
#endif
		{ "Interleave.DisplayName", "Interleave" },
		{ "Interleave.Name", "EAuracronPCGMergeStrategy::Interleave" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
		{ "Priority.DisplayName", "Priority Based" },
		{ "Priority.Name", "EAuracronPCGMergeStrategy::Priority" },
		{ "Spatial.DisplayName", "Spatial" },
		{ "Spatial.Name", "EAuracronPCGMergeStrategy::Spatial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Merge strategies for point merging" },
#endif
		{ "Weighted.DisplayName", "Weighted" },
		{ "Weighted.Name", "EAuracronPCGMergeStrategy::Weighted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGMergeStrategy::Append", (int64)EAuracronPCGMergeStrategy::Append },
		{ "EAuracronPCGMergeStrategy::Interleave", (int64)EAuracronPCGMergeStrategy::Interleave },
		{ "EAuracronPCGMergeStrategy::Weighted", (int64)EAuracronPCGMergeStrategy::Weighted },
		{ "EAuracronPCGMergeStrategy::Spatial", (int64)EAuracronPCGMergeStrategy::Spatial },
		{ "EAuracronPCGMergeStrategy::Attribute", (int64)EAuracronPCGMergeStrategy::Attribute },
		{ "EAuracronPCGMergeStrategy::Priority", (int64)EAuracronPCGMergeStrategy::Priority },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGMergeStrategy",
	"EAuracronPCGMergeStrategy",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy.InnerSingleton;
}
// ********** End Enum EAuracronPCGMergeStrategy ***************************************************

// ********** Begin Enum EAuracronPCGSplitCriteria *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria;
static UEnum* EAuracronPCGSplitCriteria_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGSplitCriteria"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplitCriteria>()
{
	return EAuracronPCGSplitCriteria_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attribute.DisplayName", "Attribute" },
		{ "Attribute.Name", "EAuracronPCGSplitCriteria::Attribute" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Split criteria for point splitting\n" },
#endif
		{ "Count.DisplayName", "Count" },
		{ "Count.Name", "EAuracronPCGSplitCriteria::Count" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
		{ "Pattern.DisplayName", "Pattern" },
		{ "Pattern.Name", "EAuracronPCGSplitCriteria::Pattern" },
		{ "Percentage.DisplayName", "Percentage" },
		{ "Percentage.Name", "EAuracronPCGSplitCriteria::Percentage" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronPCGSplitCriteria::Random" },
		{ "Spatial.DisplayName", "Spatial" },
		{ "Spatial.Name", "EAuracronPCGSplitCriteria::Spatial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Split criteria for point splitting" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGSplitCriteria::Count", (int64)EAuracronPCGSplitCriteria::Count },
		{ "EAuracronPCGSplitCriteria::Percentage", (int64)EAuracronPCGSplitCriteria::Percentage },
		{ "EAuracronPCGSplitCriteria::Attribute", (int64)EAuracronPCGSplitCriteria::Attribute },
		{ "EAuracronPCGSplitCriteria::Spatial", (int64)EAuracronPCGSplitCriteria::Spatial },
		{ "EAuracronPCGSplitCriteria::Random", (int64)EAuracronPCGSplitCriteria::Random },
		{ "EAuracronPCGSplitCriteria::Pattern", (int64)EAuracronPCGSplitCriteria::Pattern },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGSplitCriteria",
	"EAuracronPCGSplitCriteria",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria.InnerSingleton;
}
// ********** End Enum EAuracronPCGSplitCriteria ***************************************************

// ********** Begin Class UAuracronPCGAdvancedPointFilterSettings **********************************
void UAuracronPCGAdvancedPointFilterSettings::StaticRegisterNativesUAuracronPCGAdvancedPointFilterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings;
UClass* UAuracronPCGAdvancedPointFilterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedPointFilterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedPointFilterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedPointFilterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_NoRegister()
{
	return UAuracronPCGAdvancedPointFilterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByPosition_MetaData[] = {
		{ "Category", "Filter Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filter criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filter criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionBounds_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "EditCondition", "bFilterByPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByDensity_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityRange_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "EditCondition", "bFilterByDensity" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByScale_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMin_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "EditCondition", "bFilterByScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMax_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "EditCondition", "bFilterByScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMathOperation_MetaData[] = {
		{ "Category", "Math Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mathematical operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mathematical operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MathOperation_MetaData[] = {
		{ "Category", "Math Operations" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceAttribute_MetaData[] = {
		{ "Category", "Math Operations" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MathOperand_MetaData[] = {
		{ "Category", "Math Operations" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResultRange_MetaData[] = {
		{ "Category", "Math Operations" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomExpression_MetaData[] = {
		{ "Category", "Advanced Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomExpression_MetaData[] = {
		{ "Category", "Advanced Filter" },
		{ "EditCondition", "bUseCustomExpression" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertFilter_MetaData[] = {
		{ "Category", "Advanced Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Performance Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bFilterByPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionBounds;
	static void NewProp_bFilterByDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityRange;
	static void NewProp_bFilterByScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMax;
	static void NewProp_bUseMathOperation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMathOperation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MathOperation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MathOperation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceAttribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MathOperand;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResultRange;
	static void NewProp_bUseCustomExpression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomExpression;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomExpression;
	static void NewProp_bInvertFilter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertFilter;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedPointFilterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByPosition_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bFilterByPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByPosition = { "bFilterByPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByPosition_MetaData), NewProp_bFilterByPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_PositionBounds = { "PositionBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, PositionBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionBounds_MetaData), NewProp_PositionBounds_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bFilterByDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByDensity = { "bFilterByDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByDensity_MetaData), NewProp_bFilterByDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_DensityRange = { "DensityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, DensityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityRange_MetaData), NewProp_DensityRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByScale_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bFilterByScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByScale = { "bFilterByScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByScale_MetaData), NewProp_bFilterByScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ScaleMin = { "ScaleMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, ScaleMin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMin_MetaData), NewProp_ScaleMin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ScaleMax = { "ScaleMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, ScaleMax), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMax_MetaData), NewProp_ScaleMax_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMathOperation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bUseMathOperation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMathOperation = { "bUseMathOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMathOperation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMathOperation_MetaData), NewProp_bUseMathOperation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperation = { "MathOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, MathOperation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MathOperation_MetaData), NewProp_MathOperation_MetaData) }; // 3505018763
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_SourceAttribute = { "SourceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, SourceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceAttribute_MetaData), NewProp_SourceAttribute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperand = { "MathOperand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, MathOperand), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MathOperand_MetaData), NewProp_MathOperand_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ResultRange = { "ResultRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, ResultRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResultRange_MetaData), NewProp_ResultRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseCustomExpression_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bUseCustomExpression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseCustomExpression = { "bUseCustomExpression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseCustomExpression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomExpression_MetaData), NewProp_bUseCustomExpression_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_CustomExpression = { "CustomExpression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointFilterSettings, CustomExpression), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomExpression_MetaData), NewProp_CustomExpression_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bInvertFilter_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bInvertFilter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bInvertFilter = { "bInvertFilter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bInvertFilter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertFilter_MetaData), NewProp_bInvertFilter_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointFilterSettings*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_PositionBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_DensityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bFilterByScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ScaleMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ScaleMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMathOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_SourceAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_MathOperand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_ResultRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseCustomExpression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_CustomExpression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bInvertFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::NewProp_bUseMultithreading,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedPointFilterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedPointFilterSettings);
UAuracronPCGAdvancedPointFilterSettings::~UAuracronPCGAdvancedPointFilterSettings() {}
// ********** End Class UAuracronPCGAdvancedPointFilterSettings ************************************

// ********** Begin Class UAuracronPCGAdvancedPointTransformerSettings *****************************
void UAuracronPCGAdvancedPointTransformerSettings::StaticRegisterNativesUAuracronPCGAdvancedPointTransformerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings;
UClass* UAuracronPCGAdvancedPointTransformerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedPointTransformerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedPointTransformerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedPointTransformerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_NoRegister()
{
	return UAuracronPCGAdvancedPointTransformerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformPosition_MetaData[] = {
		{ "Category", "Transform Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transform operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transform operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformRotation_MetaData[] = {
		{ "Category", "Transform Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTransformScale_MetaData[] = {
		{ "Category", "Transform Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMathTransform_MetaData[] = {
		{ "Category", "Math Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mathematical transformations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mathematical transformations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionMathOp_MetaData[] = {
		{ "Category", "Math Transform" },
		{ "EditCondition", "bUseMathTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionOperand_MetaData[] = {
		{ "Category", "Math Transform" },
		{ "EditCondition", "bUseMathTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMathOp_MetaData[] = {
		{ "Category", "Math Transform" },
		{ "EditCondition", "bUseMathTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleOperand_MetaData[] = {
		{ "Category", "Math Transform" },
		{ "EditCondition", "bUseMathTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMatrixTransform_MetaData[] = {
		{ "Category", "Matrix Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Matrix transformations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Matrix transformations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomTransformMatrix_MetaData[] = {
		{ "Category", "Matrix Transform" },
		{ "EditCondition", "bUseMatrixTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAttributeTransform_MetaData[] = {
		{ "Category", "Attribute Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute-driven transformations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute-driven transformations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionAttribute_MetaData[] = {
		{ "Category", "Attribute Transform" },
		{ "EditCondition", "bUseAttributeTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationAttribute_MetaData[] = {
		{ "Category", "Attribute Transform" },
		{ "EditCondition", "bUseAttributeTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleAttribute_MetaData[] = {
		{ "Category", "Attribute Transform" },
		{ "EditCondition", "bUseAttributeTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoiseTransform_MetaData[] = {
		{ "Category", "Noise Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise-based transformations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise-based transformations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseIntensity_MetaData[] = {
		{ "Category", "Noise Transform" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "EditCondition", "bUseNoiseTransform" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bTransformPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformPosition;
	static void NewProp_bTransformRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformRotation;
	static void NewProp_bTransformScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTransformScale;
	static void NewProp_bUseMathTransform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMathTransform;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PositionMathOp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PositionMathOp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionOperand;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ScaleMathOp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ScaleMathOp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleOperand;
	static void NewProp_bUseMatrixTransform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMatrixTransform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomTransformMatrix;
	static void NewProp_bUseAttributeTransform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAttributeTransform;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PositionAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RotationAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScaleAttribute;
	static void NewProp_bUseNoiseTransform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoiseTransform;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedPointTransformerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformPosition_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bTransformPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformPosition = { "bTransformPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformPosition_MetaData), NewProp_bTransformPosition_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformRotation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bTransformRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformRotation = { "bTransformRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformRotation_MetaData), NewProp_bTransformRotation_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformScale_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bTransformScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformScale = { "bTransformScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTransformScale_MetaData), NewProp_bTransformScale_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMathTransform_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bUseMathTransform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMathTransform = { "bUseMathTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMathTransform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMathTransform_MetaData), NewProp_bUseMathTransform_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionMathOp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionMathOp = { "PositionMathOp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, PositionMathOp), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionMathOp_MetaData), NewProp_PositionMathOp_MetaData) }; // 3505018763
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionOperand = { "PositionOperand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, PositionOperand), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionOperand_MetaData), NewProp_PositionOperand_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleMathOp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleMathOp = { "ScaleMathOp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, ScaleMathOp), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMathOp_MetaData), NewProp_ScaleMathOp_MetaData) }; // 3505018763
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleOperand = { "ScaleOperand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, ScaleOperand), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleOperand_MetaData), NewProp_ScaleOperand_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMatrixTransform_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bUseMatrixTransform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMatrixTransform = { "bUseMatrixTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMatrixTransform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMatrixTransform_MetaData), NewProp_bUseMatrixTransform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_CustomTransformMatrix = { "CustomTransformMatrix", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, CustomTransformMatrix), Z_Construct_UScriptStruct_FMatrix, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomTransformMatrix_MetaData), NewProp_CustomTransformMatrix_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseAttributeTransform_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bUseAttributeTransform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseAttributeTransform = { "bUseAttributeTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseAttributeTransform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAttributeTransform_MetaData), NewProp_bUseAttributeTransform_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionAttribute = { "PositionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, PositionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionAttribute_MetaData), NewProp_PositionAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_RotationAttribute = { "RotationAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, RotationAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationAttribute_MetaData), NewProp_RotationAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleAttribute = { "ScaleAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, ScaleAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleAttribute_MetaData), NewProp_ScaleAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseNoiseTransform_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointTransformerSettings*)Obj)->bUseNoiseTransform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseNoiseTransform = { "bUseNoiseTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointTransformerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseNoiseTransform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoiseTransform_MetaData), NewProp_bUseNoiseTransform_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 2308707076
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseIntensity = { "NoiseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointTransformerSettings, NoiseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseIntensity_MetaData), NewProp_NoiseIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bTransformScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMathTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionMathOp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionMathOp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionOperand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleMathOp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleMathOp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleOperand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseMatrixTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_CustomTransformMatrix,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseAttributeTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_PositionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_RotationAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_ScaleAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_bUseNoiseTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::NewProp_NoiseIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedPointTransformerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedPointTransformerSettings);
UAuracronPCGAdvancedPointTransformerSettings::~UAuracronPCGAdvancedPointTransformerSettings() {}
// ********** End Class UAuracronPCGAdvancedPointTransformerSettings *******************************

// ********** Begin Class UAuracronPCGAdvancedPointMergerSettings **********************************
void UAuracronPCGAdvancedPointMergerSettings::StaticRegisterNativesUAuracronPCGAdvancedPointMergerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings;
UClass* UAuracronPCGAdvancedPointMergerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedPointMergerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedPointMergerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedPointMergerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_NoRegister()
{
	return UAuracronPCGAdvancedPointMergerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MergeStrategy_MetaData[] = {
		{ "Category", "Merge Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Merge strategy\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Merge strategy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputWeights_MetaData[] = {
		{ "Category", "Weighted Merge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weighted merging\n" },
#endif
		{ "EditCondition", "MergeStrategy == EAuracronPCGMergeStrategy::Weighted" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weighted merging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeWeights_MetaData[] = {
		{ "Category", "Weighted Merge" },
		{ "EditCondition", "MergeStrategy == EAuracronPCGMergeStrategy::Weighted" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialThreshold_MetaData[] = {
		{ "Category", "Spatial Merge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial merging\n" },
#endif
		{ "EditCondition", "MergeStrategy == EAuracronPCGMergeStrategy::Spatial" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial merging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMergeOverlapping_MetaData[] = {
		{ "Category", "Spatial Merge" },
		{ "EditCondition", "MergeStrategy == EAuracronPCGMergeStrategy::Spatial" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMergeAttributes_MetaData[] = {
		{ "Category", "Attribute Merge" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute merging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute merging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributesToMerge_MetaData[] = {
		{ "Category", "Attribute Merge" },
		{ "EditCondition", "bMergeAttributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeMergeOperation_MetaData[] = {
		{ "Category", "Attribute Merge" },
		{ "EditCondition", "bMergeAttributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bResolveConflicts_MetaData[] = {
		{ "Category", "Conflict Resolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Conflict resolution\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conflict resolution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreferHigherDensity_MetaData[] = {
		{ "Category", "Conflict Resolution" },
		{ "EditCondition", "bResolveConflicts" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreferLargerScale_MetaData[] = {
		{ "Category", "Conflict Resolution" },
		{ "EditCondition", "bResolveConflicts" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MergeStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MergeStrategy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InputWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputWeights;
	static void NewProp_bNormalizeWeights_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeWeights;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpatialThreshold;
	static void NewProp_bMergeOverlapping_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMergeOverlapping;
	static void NewProp_bMergeAttributes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMergeAttributes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributesToMerge_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AttributesToMerge;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AttributeMergeOperation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AttributeMergeOperation;
	static void NewProp_bResolveConflicts_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bResolveConflicts;
	static void NewProp_bPreferHigherDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreferHigherDensity;
	static void NewProp_bPreferLargerScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreferLargerScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedPointMergerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_MergeStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_MergeStrategy = { "MergeStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointMergerSettings, MergeStrategy), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMergeStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MergeStrategy_MetaData), NewProp_MergeStrategy_MetaData) }; // 3135247307
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_InputWeights_Inner = { "InputWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_InputWeights = { "InputWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointMergerSettings, InputWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputWeights_MetaData), NewProp_InputWeights_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bNormalizeWeights_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bNormalizeWeights = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bNormalizeWeights = { "bNormalizeWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bNormalizeWeights_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeWeights_MetaData), NewProp_bNormalizeWeights_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_SpatialThreshold = { "SpatialThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointMergerSettings, SpatialThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialThreshold_MetaData), NewProp_SpatialThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeOverlapping_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bMergeOverlapping = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeOverlapping = { "bMergeOverlapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeOverlapping_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMergeOverlapping_MetaData), NewProp_bMergeOverlapping_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeAttributes_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bMergeAttributes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeAttributes = { "bMergeAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeAttributes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMergeAttributes_MetaData), NewProp_bMergeAttributes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributesToMerge_Inner = { "AttributesToMerge", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributesToMerge = { "AttributesToMerge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointMergerSettings, AttributesToMerge), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributesToMerge_MetaData), NewProp_AttributesToMerge_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributeMergeOperation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributeMergeOperation = { "AttributeMergeOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointMergerSettings, AttributeMergeOperation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeMergeOperation_MetaData), NewProp_AttributeMergeOperation_MetaData) }; // 3505018763
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bResolveConflicts_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bResolveConflicts = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bResolveConflicts = { "bResolveConflicts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bResolveConflicts_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bResolveConflicts_MetaData), NewProp_bResolveConflicts_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferHigherDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bPreferHigherDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferHigherDensity = { "bPreferHigherDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferHigherDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreferHigherDensity_MetaData), NewProp_bPreferHigherDensity_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferLargerScale_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointMergerSettings*)Obj)->bPreferLargerScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferLargerScale = { "bPreferLargerScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointMergerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferLargerScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreferLargerScale_MetaData), NewProp_bPreferLargerScale_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_MergeStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_MergeStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_InputWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_InputWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bNormalizeWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_SpatialThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeOverlapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bMergeAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributesToMerge_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributesToMerge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributeMergeOperation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_AttributeMergeOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bResolveConflicts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferHigherDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::NewProp_bPreferLargerScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedPointMergerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedPointMergerSettings);
UAuracronPCGAdvancedPointMergerSettings::~UAuracronPCGAdvancedPointMergerSettings() {}
// ********** End Class UAuracronPCGAdvancedPointMergerSettings ************************************

// ********** Begin Class UAuracronPCGAdvancedPointSplitterSettings ********************************
void UAuracronPCGAdvancedPointSplitterSettings::StaticRegisterNativesUAuracronPCGAdvancedPointSplitterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings;
UClass* UAuracronPCGAdvancedPointSplitterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedPointSplitterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedPointSplitterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedPointSplitterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_NoRegister()
{
	return UAuracronPCGAdvancedPointSplitterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplitCriteria_MetaData[] = {
		{ "Category", "Split Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Split criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Split criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputCount_MetaData[] = {
		{ "Category", "Count Split" },
		{ "ClampMax", "16" },
		{ "ClampMin", "2" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Count-based splitting\n" },
#endif
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Count" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Count-based splitting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsPerOutput_MetaData[] = {
		{ "Category", "Count Split" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Count" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPercentages_MetaData[] = {
		{ "Category", "Percentage Split" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Percentage-based splitting\n" },
#endif
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Percentage" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Percentage-based splitting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizePercentages_MetaData[] = {
		{ "Category", "Percentage Split" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Percentage" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplitAttribute_MetaData[] = {
		{ "Category", "Attribute Split" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute-based splitting\n" },
#endif
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute-based splitting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeRanges_MetaData[] = {
		{ "Category", "Attribute Split" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialRegions_MetaData[] = {
		{ "Category", "Spatial Split" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial splitting\n" },
#endif
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Spatial" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial splitting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseOverlappingRegions_MetaData[] = {
		{ "Category", "Spatial Split" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Spatial" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Random Split" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Random splitting\n" },
#endif
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Random splitting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnsureMinimumPoints_MetaData[] = {
		{ "Category", "Random Split" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumPointsPerOutput_MetaData[] = {
		{ "Category", "Random Split" },
		{ "ClampMin", "1" },
		{ "EditCondition", "SplitCriteria == EAuracronPCGSplitCriteria::Random" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SplitCriteria_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SplitCriteria;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OutputCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsPerOutput_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PointsPerOutput;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputPercentages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutputPercentages;
	static void NewProp_bNormalizePercentages_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizePercentages;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SplitAttribute;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttributeRanges_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AttributeRanges;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpatialRegions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpatialRegions;
	static void NewProp_bUseOverlappingRegions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseOverlappingRegions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static void NewProp_bEnsureMinimumPoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnsureMinimumPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinimumPointsPerOutput;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedPointSplitterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitCriteria_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitCriteria = { "SplitCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, SplitCriteria), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSplitCriteria, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplitCriteria_MetaData), NewProp_SplitCriteria_MetaData) }; // 3445282613
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputCount = { "OutputCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, OutputCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputCount_MetaData), NewProp_OutputCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_PointsPerOutput_Inner = { "PointsPerOutput", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_PointsPerOutput = { "PointsPerOutput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, PointsPerOutput), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsPerOutput_MetaData), NewProp_PointsPerOutput_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputPercentages_Inner = { "OutputPercentages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputPercentages = { "OutputPercentages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, OutputPercentages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPercentages_MetaData), NewProp_OutputPercentages_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bNormalizePercentages_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSplitterSettings*)Obj)->bNormalizePercentages = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bNormalizePercentages = { "bNormalizePercentages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSplitterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bNormalizePercentages_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizePercentages_MetaData), NewProp_bNormalizePercentages_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitAttribute = { "SplitAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, SplitAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplitAttribute_MetaData), NewProp_SplitAttribute_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_AttributeRanges_Inner = { "AttributeRanges", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_AttributeRanges = { "AttributeRanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, AttributeRanges), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeRanges_MetaData), NewProp_AttributeRanges_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SpatialRegions_Inner = { "SpatialRegions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SpatialRegions = { "SpatialRegions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, SpatialRegions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialRegions_MetaData), NewProp_SpatialRegions_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bUseOverlappingRegions_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSplitterSettings*)Obj)->bUseOverlappingRegions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bUseOverlappingRegions = { "bUseOverlappingRegions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSplitterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bUseOverlappingRegions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseOverlappingRegions_MetaData), NewProp_bUseOverlappingRegions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bEnsureMinimumPoints_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSplitterSettings*)Obj)->bEnsureMinimumPoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bEnsureMinimumPoints = { "bEnsureMinimumPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSplitterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bEnsureMinimumPoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnsureMinimumPoints_MetaData), NewProp_bEnsureMinimumPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_MinimumPointsPerOutput = { "MinimumPointsPerOutput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSplitterSettings, MinimumPointsPerOutput), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumPointsPerOutput_MetaData), NewProp_MinimumPointsPerOutput_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitCriteria_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_PointsPerOutput_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_PointsPerOutput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputPercentages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_OutputPercentages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bNormalizePercentages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SplitAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_AttributeRanges_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_AttributeRanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SpatialRegions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_SpatialRegions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bUseOverlappingRegions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_RandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_bEnsureMinimumPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::NewProp_MinimumPointsPerOutput,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedPointSplitterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedPointSplitterSettings);
UAuracronPCGAdvancedPointSplitterSettings::~UAuracronPCGAdvancedPointSplitterSettings() {}
// ********** End Class UAuracronPCGAdvancedPointSplitterSettings **********************************

// ********** Begin Class UAuracronPCGAdvancedPointSorterSettings **********************************
void UAuracronPCGAdvancedPointSorterSettings::StaticRegisterNativesUAuracronPCGAdvancedPointSorterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings;
UClass* UAuracronPCGAdvancedPointSorterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedPointSorterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedPointSorterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedPointSorterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_NoRegister()
{
	return UAuracronPCGAdvancedPointSorterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimarySortCriteria_MetaData[] = {
		{ "Category", "Sort Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sort criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sort criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSecondaryCriteria_MetaData[] = {
		{ "Category", "Sort Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondarySortCriteria_MetaData[] = {
		{ "Category", "Sort Settings" },
		{ "EditCondition", "bUseSecondaryCriteria" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDescendingOrder_MetaData[] = {
		{ "Category", "Sort Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SortAxis_MetaData[] = {
		{ "Category", "Position Sort" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Position sorting\n" },
#endif
		{ "EditCondition", "PrimarySortCriteria == EAuracronPCGSortCriteria::Position || SecondarySortCriteria == EAuracronPCGSortCriteria::Position" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Position sorting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAbsolutePosition_MetaData[] = {
		{ "Category", "Position Sort" },
		{ "EditCondition", "PrimarySortCriteria == EAuracronPCGSortCriteria::Position || SecondarySortCriteria == EAuracronPCGSortCriteria::Position" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePoint_MetaData[] = {
		{ "Category", "Distance Sort" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance sorting\n" },
#endif
		{ "EditCondition", "PrimarySortCriteria == EAuracronPCGSortCriteria::Distance || SecondarySortCriteria == EAuracronPCGSortCriteria::Distance" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance sorting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse2DDistance_MetaData[] = {
		{ "Category", "Distance Sort" },
		{ "EditCondition", "PrimarySortCriteria == EAuracronPCGSortCriteria::Distance || SecondarySortCriteria == EAuracronPCGSortCriteria::Distance" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SortAttribute_MetaData[] = {
		{ "Category", "Attribute Sort" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute sorting\n" },
#endif
		{ "EditCondition", "PrimarySortCriteria == EAuracronPCGSortCriteria::Attribute || SecondarySortCriteria == EAuracronPCGSortCriteria::Attribute" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute sorting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMathOperation_MetaData[] = {
		{ "Category", "Math Sort" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mathematical sorting\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mathematical sorting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SortMathOperation_MetaData[] = {
		{ "Category", "Math Sort" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MathSourceAttribute_MetaData[] = {
		{ "Category", "Math Sort" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MathOperand_MetaData[] = {
		{ "Category", "Math Sort" },
		{ "EditCondition", "bUseMathOperation" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseStableSort_MetaData[] = {
		{ "Category", "Performance Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseParallelSort_MetaData[] = {
		{ "Category", "Performance Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParallelSortThreshold_MetaData[] = {
		{ "Category", "Performance Settings" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "100" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PrimarySortCriteria_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PrimarySortCriteria;
	static void NewProp_bUseSecondaryCriteria_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSecondaryCriteria;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SecondarySortCriteria_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SecondarySortCriteria;
	static void NewProp_bDescendingOrder_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDescendingOrder;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SortAxis;
	static void NewProp_bUseAbsolutePosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAbsolutePosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferencePoint;
	static void NewProp_bUse2DDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse2DDistance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SortAttribute;
	static void NewProp_bUseMathOperation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMathOperation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SortMathOperation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SortMathOperation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MathSourceAttribute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MathOperand;
	static void NewProp_bUseStableSort_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseStableSort;
	static void NewProp_bUseParallelSort_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseParallelSort;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ParallelSortThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedPointSorterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_PrimarySortCriteria_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_PrimarySortCriteria = { "PrimarySortCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, PrimarySortCriteria), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimarySortCriteria_MetaData), NewProp_PrimarySortCriteria_MetaData) }; // 1807037663
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseSecondaryCriteria_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUseSecondaryCriteria = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseSecondaryCriteria = { "bUseSecondaryCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseSecondaryCriteria_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSecondaryCriteria_MetaData), NewProp_bUseSecondaryCriteria_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SecondarySortCriteria_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SecondarySortCriteria = { "SecondarySortCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, SecondarySortCriteria), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondarySortCriteria_MetaData), NewProp_SecondarySortCriteria_MetaData) }; // 1807037663
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bDescendingOrder_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bDescendingOrder = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bDescendingOrder = { "bDescendingOrder", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bDescendingOrder_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDescendingOrder_MetaData), NewProp_bDescendingOrder_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortAxis = { "SortAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, SortAxis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SortAxis_MetaData), NewProp_SortAxis_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseAbsolutePosition_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUseAbsolutePosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseAbsolutePosition = { "bUseAbsolutePosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseAbsolutePosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAbsolutePosition_MetaData), NewProp_bUseAbsolutePosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_ReferencePoint = { "ReferencePoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, ReferencePoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePoint_MetaData), NewProp_ReferencePoint_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUse2DDistance_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUse2DDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUse2DDistance = { "bUse2DDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUse2DDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse2DDistance_MetaData), NewProp_bUse2DDistance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortAttribute = { "SortAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, SortAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SortAttribute_MetaData), NewProp_SortAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseMathOperation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUseMathOperation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseMathOperation = { "bUseMathOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseMathOperation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMathOperation_MetaData), NewProp_bUseMathOperation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortMathOperation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortMathOperation = { "SortMathOperation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, SortMathOperation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SortMathOperation_MetaData), NewProp_SortMathOperation_MetaData) }; // 3505018763
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_MathSourceAttribute = { "MathSourceAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, MathSourceAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MathSourceAttribute_MetaData), NewProp_MathSourceAttribute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_MathOperand = { "MathOperand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, MathOperand), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MathOperand_MetaData), NewProp_MathOperand_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseStableSort_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUseStableSort = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseStableSort = { "bUseStableSort", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseStableSort_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseStableSort_MetaData), NewProp_bUseStableSort_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseParallelSort_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedPointSorterSettings*)Obj)->bUseParallelSort = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseParallelSort = { "bUseParallelSort", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedPointSorterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseParallelSort_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseParallelSort_MetaData), NewProp_bUseParallelSort_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_ParallelSortThreshold = { "ParallelSortThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedPointSorterSettings, ParallelSortThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParallelSortThreshold_MetaData), NewProp_ParallelSortThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_PrimarySortCriteria_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_PrimarySortCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseSecondaryCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SecondarySortCriteria_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SecondarySortCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bDescendingOrder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseAbsolutePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_ReferencePoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUse2DDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseMathOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortMathOperation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_SortMathOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_MathSourceAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_MathOperand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseStableSort,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_bUseParallelSort,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::NewProp_ParallelSortThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedPointSorterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedPointSorterSettings);
UAuracronPCGAdvancedPointSorterSettings::~UAuracronPCGAdvancedPointSorterSettings() {}
// ********** End Class UAuracronPCGAdvancedPointSorterSettings ************************************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function ApplyMathOperation *************
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics
{
	struct AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms
	{
		EAuracronPCGMathOperation Operation;
		float ValueA;
		float ValueB;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mathematical operations on point attributes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mathematical operations on point attributes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(0, nullptr) }; // 3505018763
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ValueA = { "ValueA", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms, ValueA), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ValueB = { "ValueB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms, ValueB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ValueA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ValueB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "ApplyMathOperation", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::AuracronPCGPointProcessingUtils_eventApplyMathOperation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execApplyMathOperation)
{
	P_GET_ENUM(EAuracronPCGMathOperation,Z_Param_Operation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ValueA);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ValueB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGPointProcessingUtils::ApplyMathOperation(EAuracronPCGMathOperation(Z_Param_Operation),Z_Param_ValueA,Z_Param_ValueB);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function ApplyMathOperation ***************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function ApplyMathOperationVector *******
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics
{
	struct AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms
	{
		EAuracronPCGMathOperation Operation;
		FVector ValueA;
		FVector ValueB;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValueA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValueB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGMathOperation, METADATA_PARAMS(0, nullptr) }; // 3505018763
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ValueA = { "ValueA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms, ValueA), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueA_MetaData), NewProp_ValueA_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ValueB = { "ValueB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms, ValueB), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueB_MetaData), NewProp_ValueB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ValueA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ValueB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "ApplyMathOperationVector", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::AuracronPCGPointProcessingUtils_eventApplyMathOperationVector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execApplyMathOperationVector)
{
	P_GET_ENUM(EAuracronPCGMathOperation,Z_Param_Operation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ValueA);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ValueB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGPointProcessingUtils::ApplyMathOperationVector(EAuracronPCGMathOperation(Z_Param_Operation),Z_Param_Out_ValueA,Z_Param_Out_ValueB);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function ApplyMathOperationVector *********

// ********** Begin Class UAuracronPCGPointProcessingUtils Function CalculatePointDistance *********
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics
{
	struct AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms
	{
		FPCGPoint PointA;
		FPCGPoint PointB;
		bool bUse2D;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial operations\n" },
#endif
		{ "CPP_Default_bUse2D", "false" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointB;
	static void NewProp_bUse2D_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse2D;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_PointA = { "PointA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms, PointA), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointA_MetaData), NewProp_PointA_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_PointB = { "PointB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms, PointB), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointB_MetaData), NewProp_PointB_MetaData) }; // 866600693
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_bUse2D_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms*)Obj)->bUse2D = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_bUse2D = { "bUse2D", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_bUse2D_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_PointA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_PointB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_bUse2D,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "CalculatePointDistance", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::AuracronPCGPointProcessingUtils_eventCalculatePointDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execCalculatePointDistance)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_PointA);
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_PointB);
	P_GET_UBOOL(Z_Param_bUse2D);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGPointProcessingUtils::CalculatePointDistance(Z_Param_Out_PointA,Z_Param_Out_PointB,Z_Param_bUse2D);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function CalculatePointDistance ***********

// ********** Begin Class UAuracronPCGPointProcessingUtils Function ComparePoints ******************
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics
{
	struct AuracronPCGPointProcessingUtils_eventComparePoints_Parms
	{
		FPCGPoint PointA;
		FPCGPoint PointB;
		EAuracronPCGSortCriteria Criteria;
		FVector ReferencePoint;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Point comparison functions\n" },
#endif
		{ "CPP_Default_ReferencePoint", "" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Point comparison functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointB_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PointB;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Criteria_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Criteria;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReferencePoint;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_PointA = { "PointA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventComparePoints_Parms, PointA), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointA_MetaData), NewProp_PointA_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_PointB = { "PointB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventComparePoints_Parms, PointB), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointB_MetaData), NewProp_PointB_MetaData) }; // 866600693
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_Criteria_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_Criteria = { "Criteria", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventComparePoints_Parms, Criteria), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGSortCriteria, METADATA_PARAMS(0, nullptr) }; // 1807037663
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReferencePoint = { "ReferencePoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventComparePoints_Parms, ReferencePoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePoint_MetaData), NewProp_ReferencePoint_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventComparePoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventComparePoints_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_PointA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_PointB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_Criteria_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_Criteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReferencePoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "ComparePoints", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::AuracronPCGPointProcessingUtils_eventComparePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::AuracronPCGPointProcessingUtils_eventComparePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execComparePoints)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_PointA);
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_PointB);
	P_GET_ENUM(EAuracronPCGSortCriteria,Z_Param_Criteria);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ReferencePoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPointProcessingUtils::ComparePoints(Z_Param_Out_PointA,Z_Param_Out_PointB,EAuracronPCGSortCriteria(Z_Param_Criteria),Z_Param_Out_ReferencePoint);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function ComparePoints ********************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function GetOptimalBatchSize ************
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics
{
	struct AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms
	{
		int32 TotalPoints;
		int32 ThreadCount;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance utilities\n" },
#endif
		{ "CPP_Default_ThreadCount", "0" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_TotalPoints = { "TotalPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms, TotalPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms, ThreadCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_TotalPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "GetOptimalBatchSize", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::AuracronPCGPointProcessingUtils_eventGetOptimalBatchSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execGetOptimalBatchSize)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TotalPoints);
	P_GET_PROPERTY(FIntProperty,Z_Param_ThreadCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGPointProcessingUtils::GetOptimalBatchSize(Z_Param_TotalPoints,Z_Param_ThreadCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function GetOptimalBatchSize **************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function GetPointAttributeValue *********
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics
{
	struct AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms
	{
		FPCGPoint Point;
		const UPCGMetadata* Metadata;
		FString AttributeName;
		float OutValue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attribute operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutValue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_OutValue = { "OutValue", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms, OutValue), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_OutValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "GetPointAttributeValue", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::AuracronPCGPointProcessingUtils_eventGetPointAttributeValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execGetPointAttributeValue)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_OutValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPointProcessingUtils::GetPointAttributeValue(Z_Param_Out_Point,Z_Param_Metadata,Z_Param_AttributeName,Z_Param_Out_OutValue);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function GetPointAttributeValue ***********

// ********** Begin Class UAuracronPCGPointProcessingUtils Function IsPointInRegion ****************
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics
{
	struct AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms
	{
		FPCGPoint Point;
		FBox Region;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Region_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Region;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) }; // 866600693
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_Region = { "Region", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms, Region), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Region_MetaData), NewProp_Region_MetaData) };
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_Region,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "IsPointInRegion", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::AuracronPCGPointProcessingUtils_eventIsPointInRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execIsPointInRegion)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Region);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPointProcessingUtils::IsPointInRegion(Z_Param_Out_Point,Z_Param_Out_Region);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function IsPointInRegion ******************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function ProcessPointsBatch *************
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics
{
	struct AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms
	{
		TArray<FPCGPoint> Points;
		EAuracronPCGProcessingOperation Operation;
		TMap<FString,float> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(0, nullptr) }; // 866600693
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 866600693
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms, Operation), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGProcessingOperation, METADATA_PARAMS(0, nullptr) }; // 923093131
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "ProcessPointsBatch", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::AuracronPCGPointProcessingUtils_eventProcessPointsBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execProcessPointsBatch)
{
	P_GET_TARRAY_REF(FPCGPoint,Z_Param_Out_Points);
	P_GET_ENUM(EAuracronPCGProcessingOperation,Z_Param_Operation);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGPointProcessingUtils::ProcessPointsBatch(Z_Param_Out_Points,EAuracronPCGProcessingOperation(Z_Param_Operation),Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function ProcessPointsBatch ***************

// ********** Begin Class UAuracronPCGPointProcessingUtils Function SetPointAttributeValue *********
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics
{
	struct AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms
	{
		FPCGPoint Point;
		UPCGMetadata* Metadata;
		FString AttributeName;
		float Value;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms, Point), Z_Construct_UScriptStruct_FPCGPoint, METADATA_PARAMS(0, nullptr) }; // 866600693
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms, Metadata), Z_Construct_UClass_UPCGMetadata_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms, Value), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Metadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "SetPointAttributeValue", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::AuracronPCGPointProcessingUtils_eventSetPointAttributeValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execSetPointAttributeValue)
{
	P_GET_STRUCT_REF(FPCGPoint,Z_Param_Out_Point);
	P_GET_OBJECT(UPCGMetadata,Z_Param_Metadata);
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPointProcessingUtils::SetPointAttributeValue(Z_Param_Out_Point,Z_Param_Metadata,Z_Param_AttributeName,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function SetPointAttributeValue ***********

// ********** Begin Class UAuracronPCGPointProcessingUtils Function ShouldUseParallelProcessing ****
struct Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics
{
	struct AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms
	{
		int32 PointCount;
		int32 Threshold;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Point Processing Utils" },
		{ "CPP_Default_Threshold", "1000" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Threshold;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_PointCount = { "PointCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms, PointCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms), &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_PointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGPointProcessingUtils, nullptr, "ShouldUseParallelProcessing", Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::AuracronPCGPointProcessingUtils_eventShouldUseParallelProcessing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGPointProcessingUtils::execShouldUseParallelProcessing)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_PointCount);
	P_GET_PROPERTY(FIntProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGPointProcessingUtils::ShouldUseParallelProcessing(Z_Param_PointCount,Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGPointProcessingUtils Function ShouldUseParallelProcessing ******

// ********** Begin Class UAuracronPCGPointProcessingUtils *****************************************
void UAuracronPCGPointProcessingUtils::StaticRegisterNativesUAuracronPCGPointProcessingUtils()
{
	UClass* Class = UAuracronPCGPointProcessingUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyMathOperation", &UAuracronPCGPointProcessingUtils::execApplyMathOperation },
		{ "ApplyMathOperationVector", &UAuracronPCGPointProcessingUtils::execApplyMathOperationVector },
		{ "CalculatePointDistance", &UAuracronPCGPointProcessingUtils::execCalculatePointDistance },
		{ "ComparePoints", &UAuracronPCGPointProcessingUtils::execComparePoints },
		{ "GetOptimalBatchSize", &UAuracronPCGPointProcessingUtils::execGetOptimalBatchSize },
		{ "GetPointAttributeValue", &UAuracronPCGPointProcessingUtils::execGetPointAttributeValue },
		{ "IsPointInRegion", &UAuracronPCGPointProcessingUtils::execIsPointInRegion },
		{ "ProcessPointsBatch", &UAuracronPCGPointProcessingUtils::execProcessPointsBatch },
		{ "SetPointAttributeValue", &UAuracronPCGPointProcessingUtils::execSetPointAttributeValue },
		{ "ShouldUseParallelProcessing", &UAuracronPCGPointProcessingUtils::execShouldUseParallelProcessing },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils;
UClass* UAuracronPCGPointProcessingUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPointProcessingUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPointProcessingUtils"),
			Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPointProcessingUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils_NoRegister()
{
	return UAuracronPCGPointProcessingUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Point Processing Utilities\n * Utility functions for advanced point processing operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGPointProcessing.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGPointProcessing.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Point Processing Utilities\nUtility functions for advanced point processing operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperation, "ApplyMathOperation" }, // 1817221909
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ApplyMathOperationVector, "ApplyMathOperationVector" }, // 160581222
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_CalculatePointDistance, "CalculatePointDistance" }, // 845760009
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ComparePoints, "ComparePoints" }, // 202267988
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetOptimalBatchSize, "GetOptimalBatchSize" }, // 853046526
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_GetPointAttributeValue, "GetPointAttributeValue" }, // 4108291771
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_IsPointInRegion, "IsPointInRegion" }, // 3772924138
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ProcessPointsBatch, "ProcessPointsBatch" }, // 1726047488
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_SetPointAttributeValue, "SetPointAttributeValue" }, // 2600567299
		{ &Z_Construct_UFunction_UAuracronPCGPointProcessingUtils_ShouldUseParallelProcessing, "ShouldUseParallelProcessing" }, // 2660076012
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPointProcessingUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::ClassParams = {
	&UAuracronPCGPointProcessingUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPointProcessingUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGPointProcessingUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils.OuterSingleton;
}
UAuracronPCGPointProcessingUtils::UAuracronPCGPointProcessingUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPointProcessingUtils);
UAuracronPCGPointProcessingUtils::~UAuracronPCGPointProcessingUtils() {}
// ********** End Class UAuracronPCGPointProcessingUtils *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGProcessingOperation_StaticEnum, TEXT("EAuracronPCGProcessingOperation"), &Z_Registration_Info_UEnum_EAuracronPCGProcessingOperation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 923093131U) },
		{ EAuracronPCGMathOperation_StaticEnum, TEXT("EAuracronPCGMathOperation"), &Z_Registration_Info_UEnum_EAuracronPCGMathOperation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3505018763U) },
		{ EAuracronPCGSortCriteria_StaticEnum, TEXT("EAuracronPCGSortCriteria"), &Z_Registration_Info_UEnum_EAuracronPCGSortCriteria, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1807037663U) },
		{ EAuracronPCGMergeStrategy_StaticEnum, TEXT("EAuracronPCGMergeStrategy"), &Z_Registration_Info_UEnum_EAuracronPCGMergeStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3135247307U) },
		{ EAuracronPCGSplitCriteria_StaticEnum, TEXT("EAuracronPCGSplitCriteria"), &Z_Registration_Info_UEnum_EAuracronPCGSplitCriteria, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3445282613U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedPointFilterSettings, UAuracronPCGAdvancedPointFilterSettings::StaticClass, TEXT("UAuracronPCGAdvancedPointFilterSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedPointFilterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedPointFilterSettings), 3592792746U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedPointTransformerSettings, UAuracronPCGAdvancedPointTransformerSettings::StaticClass, TEXT("UAuracronPCGAdvancedPointTransformerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedPointTransformerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedPointTransformerSettings), 876905662U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedPointMergerSettings, UAuracronPCGAdvancedPointMergerSettings::StaticClass, TEXT("UAuracronPCGAdvancedPointMergerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedPointMergerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedPointMergerSettings), 4067683316U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedPointSplitterSettings, UAuracronPCGAdvancedPointSplitterSettings::StaticClass, TEXT("UAuracronPCGAdvancedPointSplitterSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSplitterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedPointSplitterSettings), 3285941758U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedPointSorterSettings, UAuracronPCGAdvancedPointSorterSettings::StaticClass, TEXT("UAuracronPCGAdvancedPointSorterSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedPointSorterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedPointSorterSettings), 1899426893U) },
		{ Z_Construct_UClass_UAuracronPCGPointProcessingUtils, UAuracronPCGPointProcessingUtils::StaticClass, TEXT("UAuracronPCGPointProcessingUtils"), &Z_Registration_Info_UClass_UAuracronPCGPointProcessingUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPointProcessingUtils), 4188606198U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_368336428(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGPointProcessing_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
