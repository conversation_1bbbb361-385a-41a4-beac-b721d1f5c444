// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanClothingSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanClothingSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingResult();
CLOTHINGSYSTEMRUNTIMEINTERFACE_API UClass* Z_Construct_UClass_UClothingAssetBase_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronClothingType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronClothingType;
static UEnum* EAuracronClothingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronClothingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronClothingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronClothingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronClothingType.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronClothingType>()
{
	return EAuracronClothingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Accessory.DisplayName", "Accessory" },
		{ "Accessory.Name", "EAuracronClothingType::Accessory" },
		{ "BlueprintType", "true" },
		{ "Cape.DisplayName", "Cape" },
		{ "Cape.Name", "EAuracronClothingType::Cape" },
		{ "Coat.DisplayName", "Coat" },
		{ "Coat.Name", "EAuracronClothingType::Coat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Clothing Types\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronClothingType::Custom" },
		{ "Dress.DisplayName", "Dress" },
		{ "Dress.Name", "EAuracronClothingType::Dress" },
		{ "Gloves.DisplayName", "Gloves" },
		{ "Gloves.Name", "EAuracronClothingType::Gloves" },
		{ "Hat.DisplayName", "Hat" },
		{ "Hat.Name", "EAuracronClothingType::Hat" },
		{ "Jacket.DisplayName", "Jacket" },
		{ "Jacket.Name", "EAuracronClothingType::Jacket" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
		{ "Pants.DisplayName", "Pants" },
		{ "Pants.Name", "EAuracronClothingType::Pants" },
		{ "Scarf.DisplayName", "Scarf" },
		{ "Scarf.Name", "EAuracronClothingType::Scarf" },
		{ "Shirt.DisplayName", "Shirt" },
		{ "Shirt.Name", "EAuracronClothingType::Shirt" },
		{ "Shoes.DisplayName", "Shoes" },
		{ "Shoes.Name", "EAuracronClothingType::Shoes" },
		{ "Skirt.DisplayName", "Skirt" },
		{ "Skirt.Name", "EAuracronClothingType::Skirt" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing Types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronClothingType::Shirt", (int64)EAuracronClothingType::Shirt },
		{ "EAuracronClothingType::Pants", (int64)EAuracronClothingType::Pants },
		{ "EAuracronClothingType::Dress", (int64)EAuracronClothingType::Dress },
		{ "EAuracronClothingType::Jacket", (int64)EAuracronClothingType::Jacket },
		{ "EAuracronClothingType::Coat", (int64)EAuracronClothingType::Coat },
		{ "EAuracronClothingType::Skirt", (int64)EAuracronClothingType::Skirt },
		{ "EAuracronClothingType::Shoes", (int64)EAuracronClothingType::Shoes },
		{ "EAuracronClothingType::Hat", (int64)EAuracronClothingType::Hat },
		{ "EAuracronClothingType::Gloves", (int64)EAuracronClothingType::Gloves },
		{ "EAuracronClothingType::Scarf", (int64)EAuracronClothingType::Scarf },
		{ "EAuracronClothingType::Cape", (int64)EAuracronClothingType::Cape },
		{ "EAuracronClothingType::Accessory", (int64)EAuracronClothingType::Accessory },
		{ "EAuracronClothingType::Custom", (int64)EAuracronClothingType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronClothingType",
	"EAuracronClothingType",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronClothingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronClothingType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronClothingType.InnerSingleton;
}
// ********** End Enum EAuracronClothingType *******************************************************

// ********** Begin Enum EAuracronClothingMaterial *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronClothingMaterial;
static UEnum* EAuracronClothingMaterial_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronClothingMaterial.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronClothingMaterial.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronClothingMaterial"));
	}
	return Z_Registration_Info_UEnum_EAuracronClothingMaterial.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronClothingMaterial>()
{
	return EAuracronClothingMaterial_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Canvas.DisplayName", "Canvas" },
		{ "Canvas.Name", "EAuracronClothingMaterial::Canvas" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Clothing Material Types\n */" },
#endif
		{ "Cotton.DisplayName", "Cotton" },
		{ "Cotton.Name", "EAuracronClothingMaterial::Cotton" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronClothingMaterial::Custom" },
		{ "Denim.DisplayName", "Denim" },
		{ "Denim.Name", "EAuracronClothingMaterial::Denim" },
		{ "Leather.DisplayName", "Leather" },
		{ "Leather.Name", "EAuracronClothingMaterial::Leather" },
		{ "Linen.DisplayName", "Linen" },
		{ "Linen.Name", "EAuracronClothingMaterial::Linen" },
		{ "Metal.DisplayName", "Metal" },
		{ "Metal.Name", "EAuracronClothingMaterial::Metal" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
		{ "Polyester.DisplayName", "Polyester" },
		{ "Polyester.Name", "EAuracronClothingMaterial::Polyester" },
		{ "Rubber.DisplayName", "Rubber" },
		{ "Rubber.Name", "EAuracronClothingMaterial::Rubber" },
		{ "Satin.DisplayName", "Satin" },
		{ "Satin.Name", "EAuracronClothingMaterial::Satin" },
		{ "Silk.DisplayName", "Silk" },
		{ "Silk.Name", "EAuracronClothingMaterial::Silk" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing Material Types" },
#endif
		{ "Velvet.DisplayName", "Velvet" },
		{ "Velvet.Name", "EAuracronClothingMaterial::Velvet" },
		{ "Wool.DisplayName", "Wool" },
		{ "Wool.Name", "EAuracronClothingMaterial::Wool" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronClothingMaterial::Cotton", (int64)EAuracronClothingMaterial::Cotton },
		{ "EAuracronClothingMaterial::Silk", (int64)EAuracronClothingMaterial::Silk },
		{ "EAuracronClothingMaterial::Wool", (int64)EAuracronClothingMaterial::Wool },
		{ "EAuracronClothingMaterial::Leather", (int64)EAuracronClothingMaterial::Leather },
		{ "EAuracronClothingMaterial::Denim", (int64)EAuracronClothingMaterial::Denim },
		{ "EAuracronClothingMaterial::Polyester", (int64)EAuracronClothingMaterial::Polyester },
		{ "EAuracronClothingMaterial::Linen", (int64)EAuracronClothingMaterial::Linen },
		{ "EAuracronClothingMaterial::Velvet", (int64)EAuracronClothingMaterial::Velvet },
		{ "EAuracronClothingMaterial::Satin", (int64)EAuracronClothingMaterial::Satin },
		{ "EAuracronClothingMaterial::Canvas", (int64)EAuracronClothingMaterial::Canvas },
		{ "EAuracronClothingMaterial::Rubber", (int64)EAuracronClothingMaterial::Rubber },
		{ "EAuracronClothingMaterial::Metal", (int64)EAuracronClothingMaterial::Metal },
		{ "EAuracronClothingMaterial::Custom", (int64)EAuracronClothingMaterial::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronClothingMaterial",
	"EAuracronClothingMaterial",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial()
{
	if (!Z_Registration_Info_UEnum_EAuracronClothingMaterial.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronClothingMaterial.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronClothingMaterial.InnerSingleton;
}
// ********** End Enum EAuracronClothingMaterial ***************************************************

// ********** Begin ScriptStruct FAuracronClothingPhysicsConfig ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig;
class UScriptStruct* FAuracronClothingPhysicsConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronClothingPhysicsConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Clothing Physics Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing Physics Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSimulation_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable cloth simulation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable cloth simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stiffness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cloth stiffness (0.0 = very soft, 1.0 = very stiff) */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cloth stiffness (0.0 = very soft, 1.0 = very stiff)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damping_MetaData[] = {
		{ "Category", "Physics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cloth damping */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cloth damping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindResponsiveness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Wind responsiveness */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind responsiveness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityScale_MetaData[] = {
		{ "Category", "Physics" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gravity scale */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gravity scale" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionThickness_MetaData[] = {
		{ "Category", "Physics" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Collision thickness */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision thickness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSelfCollision_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Self collision */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Self collision" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCCD_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Continuous collision detection */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Continuous collision detection" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSimulation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Stiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindResponsiveness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionThickness;
	static void NewProp_bEnableSelfCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSelfCollision;
	static void NewProp_bEnableCCD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCCD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronClothingPhysicsConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSimulation_SetBit(void* Obj)
{
	((FAuracronClothingPhysicsConfig*)Obj)->bEnableSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSimulation = { "bEnableSimulation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingPhysicsConfig), &Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSimulation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSimulation_MetaData), NewProp_bEnableSimulation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_Stiffness = { "Stiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingPhysicsConfig, Stiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stiffness_MetaData), NewProp_Stiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_Damping = { "Damping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingPhysicsConfig, Damping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damping_MetaData), NewProp_Damping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_WindResponsiveness = { "WindResponsiveness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingPhysicsConfig, WindResponsiveness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindResponsiveness_MetaData), NewProp_WindResponsiveness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_GravityScale = { "GravityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingPhysicsConfig, GravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityScale_MetaData), NewProp_GravityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_CollisionThickness = { "CollisionThickness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingPhysicsConfig, CollisionThickness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionThickness_MetaData), NewProp_CollisionThickness_MetaData) };
void Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSelfCollision_SetBit(void* Obj)
{
	((FAuracronClothingPhysicsConfig*)Obj)->bEnableSelfCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSelfCollision = { "bEnableSelfCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingPhysicsConfig), &Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSelfCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSelfCollision_MetaData), NewProp_bEnableSelfCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableCCD_SetBit(void* Obj)
{
	((FAuracronClothingPhysicsConfig*)Obj)->bEnableCCD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableCCD = { "bEnableCCD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingPhysicsConfig), &Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableCCD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCCD_MetaData), NewProp_bEnableCCD_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_Stiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_Damping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_WindResponsiveness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_GravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_CollisionThickness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableSelfCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewProp_bEnableCCD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronClothingPhysicsConfig",
	Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::PropPointers),
	sizeof(FAuracronClothingPhysicsConfig),
	alignof(FAuracronClothingPhysicsConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronClothingPhysicsConfig **************************************

// ********** Begin ScriptStruct FAuracronClothingConfig *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronClothingConfig;
class UScriptStruct* FAuracronClothingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronClothingConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronClothingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Clothing Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingType_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Clothing type */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialType_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material type */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Clothing name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsConfig_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Physics configuration */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** LOD distances */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD distances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWindInteraction_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable wind interaction */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable wind interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCharacterInteraction_MetaData[] = {
		{ "Category", "Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable character interaction */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable character interaction" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ClothingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ClothingType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaterialType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaterialType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsConfig;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static void NewProp_bEnableWindInteraction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWindInteraction;
	static void NewProp_bEnableCharacterInteraction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCharacterInteraction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronClothingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingType = { "ClothingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingConfig, ClothingType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingType_MetaData), NewProp_ClothingType_MetaData) }; // 1901076960
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_MaterialType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_MaterialType = { "MaterialType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingConfig, MaterialType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialType_MetaData), NewProp_MaterialType_MetaData) }; // 2980797378
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingConfig, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_PhysicsConfig = { "PhysicsConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingConfig, PhysicsConfig), Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsConfig_MetaData), NewProp_PhysicsConfig_MetaData) }; // 1728097396
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingConfig, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
void Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableWindInteraction_SetBit(void* Obj)
{
	((FAuracronClothingConfig*)Obj)->bEnableWindInteraction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableWindInteraction = { "bEnableWindInteraction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingConfig), &Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableWindInteraction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWindInteraction_MetaData), NewProp_bEnableWindInteraction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableCharacterInteraction_SetBit(void* Obj)
{
	((FAuracronClothingConfig*)Obj)->bEnableCharacterInteraction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableCharacterInteraction = { "bEnableCharacterInteraction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingConfig), &Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableCharacterInteraction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCharacterInteraction_MetaData), NewProp_bEnableCharacterInteraction_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_MaterialType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_MaterialType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_PhysicsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableWindInteraction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewProp_bEnableCharacterInteraction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronClothingConfig",
	Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::PropPointers),
	sizeof(FAuracronClothingConfig),
	alignof(FAuracronClothingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronClothingConfig *********************************************

// ********** Begin ScriptStruct FAuracronClothingResult *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronClothingResult;
class UScriptStruct* FAuracronClothingResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronClothingResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronClothingResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronClothingResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronClothingResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Clothing Processing Result\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing Processing Result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether the operation was successful */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the operation was successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Result message */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Result message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedClothingAsset_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generated clothing asset */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generated clothing asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Processing time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPath_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Output file path */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output file path" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedClothingAsset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputPath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronClothingResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronClothingResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronClothingResult), &Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_GeneratedClothingAsset = { "GeneratedClothingAsset", nullptr, (EPropertyFlags)0x0114000000000014, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingResult, GeneratedClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedClothingAsset_MetaData), NewProp_GeneratedClothingAsset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_OutputPath = { "OutputPath", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronClothingResult, OutputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPath_MetaData), NewProp_OutputPath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_GeneratedClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_ProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewProp_OutputPath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronClothingResult",
	Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::PropPointers),
	sizeof(FAuracronClothingResult),
	alignof(FAuracronClothingResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronClothingResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronClothingResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronClothingResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronClothingResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronClothingResult *********************************************

// ********** Begin Delegate FAuracronClothingComplete *********************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms
	{
		FAuracronClothingResult Result;
		FString OperationID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 2625502296
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::NewProp_OperationID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronClothingComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronClothingComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronClothingComplete, FAuracronClothingResult const& Result, const FString& OperationID)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms
	{
		FAuracronClothingResult Result;
		FString OperationID;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronClothingComplete_Parms Parms;
	Parms.Result=Result;
	Parms.OperationID=OperationID;
	AuracronClothingComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronClothingComplete ***********************************************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function AnalyzeClothingPerformance *****
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics
{
	struct AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		FString ClothingName;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Analyze clothing performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze clothing performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "AnalyzeClothingPerformance", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::AuracronMetaHumanClothingSystem_eventAnalyzeClothingPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execAnalyzeClothingPerformance)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClothingName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->AnalyzeClothingPerformance(Z_Param_MeshComponent,Z_Param_ClothingName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function AnalyzeClothingPerformance *******

// ********** Begin Class UAuracronMetaHumanClothingSystem Function ApplyClothingToMetaHuman *******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics
{
	struct AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms
	{
		UClothingAssetBase* ClothingAsset;
		USkeletalMeshComponent* MetaHumanComponent;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply clothing to MetaHuman\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply clothing to MetaHuman" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetaHumanComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingAsset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MetaHumanComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_ClothingAsset = { "ClothingAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms, ClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_MetaHumanComponent = { "MetaHumanComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms, MetaHumanComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetaHumanComponent_MetaData), NewProp_MetaHumanComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_ClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_MetaHumanComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "ApplyClothingToMetaHuman", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::AuracronMetaHumanClothingSystem_eventApplyClothingToMetaHuman_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execApplyClothingToMetaHuman)
{
	P_GET_OBJECT(UClothingAssetBase,Z_Param_ClothingAsset);
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MetaHumanComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->ApplyClothingToMetaHuman(Z_Param_ClothingAsset,Z_Param_MetaHumanComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function ApplyClothingToMetaHuman *********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function ApplyMaterialToClothing ********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics
{
	struct AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		FString ClothingName;
		UMaterialInterface* Material;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply material to clothing\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply material to clothing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Material;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "ApplyMaterialToClothing", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::AuracronMetaHumanClothingSystem_eventApplyMaterialToClothing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execApplyMaterialToClothing)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClothingName);
	P_GET_OBJECT(UMaterialInterface,Z_Param_Material);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyMaterialToClothing(Z_Param_MeshComponent,Z_Param_ClothingName,Z_Param_Material);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function ApplyMaterialToClothing **********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function CreateClothingAsset ************
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics
{
	struct AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms
	{
		FAuracronClothingConfig Config;
		USkeletalMesh* TargetMesh;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create clothing asset\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create clothing asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms, Config), Z_Construct_UScriptStruct_FAuracronClothingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 532802727
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "CreateClothingAsset", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execCreateClothingAsset)
{
	P_GET_STRUCT_REF(FAuracronClothingConfig,Z_Param_Out_Config);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->CreateClothingAsset(Z_Param_Out_Config,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function CreateClothingAsset **************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function CreateClothingAssetAsync *******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics
{
	struct AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms
	{
		FAuracronClothingConfig Config;
		USkeletalMesh* TargetMesh;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create clothing asset asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create clothing asset asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms, Config), Z_Construct_UScriptStruct_FAuracronClothingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 532802727
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms, TargetMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "CreateClothingAssetAsync", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingAssetAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execCreateClothingAssetAsync)
{
	P_GET_STRUCT_REF(FAuracronClothingConfig,Z_Param_Out_Config);
	P_GET_OBJECT(USkeletalMesh,Z_Param_TargetMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateClothingAssetAsync(Z_Param_Out_Config,Z_Param_TargetMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function CreateClothingAssetAsync *********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function CreateClothingMaterial *********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics
{
	struct AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms
	{
		EAuracronClothingMaterial MaterialType;
		TMap<FString,float> Parameters;
		UMaterialInstance* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create clothing material\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create clothing material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaterialType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaterialType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_MaterialType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_MaterialType = { "MaterialType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms, MaterialType), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronClothingMaterial, METADATA_PARAMS(0, nullptr) }; // 2980797378
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms, ReturnValue), Z_Construct_UClass_UMaterialInstance_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_MaterialType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_MaterialType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "CreateClothingMaterial", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::AuracronMetaHumanClothingSystem_eventCreateClothingMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execCreateClothingMaterial)
{
	P_GET_ENUM(EAuracronClothingMaterial,Z_Param_MaterialType);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInstance**)Z_Param__Result=P_THIS->CreateClothingMaterial(EAuracronClothingMaterial(Z_Param_MaterialType),Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function CreateClothingMaterial ***********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function GenerateClothingLODs ***********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics
{
	struct AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms
	{
		UClothingAssetBase* ClothingAsset;
		TArray<float> LODDistances;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate clothing LODs\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate clothing LODs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingAsset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_ClothingAsset = { "ClothingAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms, ClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_ClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "GenerateClothingLODs", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::AuracronMetaHumanClothingSystem_eventGenerateClothingLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execGenerateClothingLODs)
{
	P_GET_OBJECT(UClothingAssetBase,Z_Param_ClothingAsset);
	P_GET_TARRAY_REF(float,Z_Param_Out_LODDistances);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->GenerateClothingLODs(Z_Param_ClothingAsset,Z_Param_Out_LODDistances);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function GenerateClothingLODs *************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function GetAsyncOperationResult ********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics
{
	struct AuracronMetaHumanClothingSystem_eventGetAsyncOperationResult_Parms
	{
		FString OperationID;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get async operation result\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get async operation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetAsyncOperationResult_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetAsyncOperationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "GetAsyncOperationResult", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanClothingSystem_eventGetAsyncOperationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::AuracronMetaHumanClothingSystem_eventGetAsyncOperationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execGetAsyncOperationResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->GetAsyncOperationResult(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function GetAsyncOperationResult **********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function GetClothingStatistics **********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics
{
	struct AuracronMetaHumanClothingSystem_eventGetClothingStatistics_Parms
	{
		UClothingAssetBase* ClothingAsset;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get clothing statistics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get clothing statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingAsset;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::NewProp_ClothingAsset = { "ClothingAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetClothingStatistics_Parms, ClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetClothingStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::NewProp_ClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "GetClothingStatistics", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::AuracronMetaHumanClothingSystem_eventGetClothingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::AuracronMetaHumanClothingSystem_eventGetClothingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execGetClothingStatistics)
{
	P_GET_OBJECT(UClothingAssetBase,Z_Param_ClothingAsset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetClothingStatistics(Z_Param_ClothingAsset);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function GetClothingStatistics ************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function GetSupportedClothingTypes ******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics
{
	struct AuracronMetaHumanClothingSystem_eventGetSupportedClothingTypes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get supported clothing types\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get supported clothing types" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetSupportedClothingTypes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "GetSupportedClothingTypes", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::AuracronMetaHumanClothingSystem_eventGetSupportedClothingTypes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::AuracronMetaHumanClothingSystem_eventGetSupportedClothingTypes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execGetSupportedClothingTypes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSupportedClothingTypes();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function GetSupportedClothingTypes ********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function GetSupportedMaterialTypes ******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics
{
	struct AuracronMetaHumanClothingSystem_eventGetSupportedMaterialTypes_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get supported material types\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get supported material types" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventGetSupportedMaterialTypes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "GetSupportedMaterialTypes", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::AuracronMetaHumanClothingSystem_eventGetSupportedMaterialTypes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::AuracronMetaHumanClothingSystem_eventGetSupportedMaterialTypes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execGetSupportedMaterialTypes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSupportedMaterialTypes();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function GetSupportedMaterialTypes ********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function Initialize *********************
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics
{
	struct AuracronMetaHumanClothingSystem_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize clothing system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize clothing system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::AuracronMetaHumanClothingSystem_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::AuracronMetaHumanClothingSystem_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function Initialize ***********************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function IsAsyncOperationComplete *******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics
{
	struct AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms
	{
		FString OperationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if async operation is complete\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if async operation is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "IsAsyncOperationComplete", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanClothingSystem_eventIsAsyncOperationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execIsAsyncOperationComplete)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncOperationComplete(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function IsAsyncOperationComplete *********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics
{
	struct AuracronMetaHumanClothingSystem_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if clothing system is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if clothing system is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::AuracronMetaHumanClothingSystem_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::AuracronMetaHumanClothingSystem_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function IsInitialized ********************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function OptimizeClothingPerformance ****
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics
{
	struct AuracronMetaHumanClothingSystem_eventOptimizeClothingPerformance_Parms
	{
		UClothingAssetBase* ClothingAsset;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize clothing performance\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize clothing performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingAsset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::NewProp_ClothingAsset = { "ClothingAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventOptimizeClothingPerformance_Parms, ClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventOptimizeClothingPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::NewProp_ClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "OptimizeClothingPerformance", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::AuracronMetaHumanClothingSystem_eventOptimizeClothingPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::AuracronMetaHumanClothingSystem_eventOptimizeClothingPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execOptimizeClothingPerformance)
{
	P_GET_OBJECT(UClothingAssetBase,Z_Param_ClothingAsset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->OptimizeClothingPerformance(Z_Param_ClothingAsset);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function OptimizeClothingPerformance ******

// ********** Begin Class UAuracronMetaHumanClothingSystem Function RemoveClothingFromMetaHuman ****
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics
{
	struct AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms
	{
		USkeletalMeshComponent* MetaHumanComponent;
		FString ClothingName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove clothing from MetaHuman\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove clothing from MetaHuman" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetaHumanComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MetaHumanComponent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_MetaHumanComponent = { "MetaHumanComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms, MetaHumanComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetaHumanComponent_MetaData), NewProp_MetaHumanComponent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_MetaHumanComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "RemoveClothingFromMetaHuman", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::AuracronMetaHumanClothingSystem_eventRemoveClothingFromMetaHuman_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execRemoveClothingFromMetaHuman)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MetaHumanComponent);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClothingName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveClothingFromMetaHuman(Z_Param_MetaHumanComponent,Z_Param_ClothingName);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function RemoveClothingFromMetaHuman ******

// ********** Begin Class UAuracronMetaHumanClothingSystem Function ResetClothingSimulation ********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics
{
	struct AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reset clothing simulation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset clothing simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "ResetClothingSimulation", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventResetClothingSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execResetClothingSimulation)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResetClothingSimulation(Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function ResetClothingSimulation **********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function SetWindParameters **************
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics
{
	struct AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms
	{
		FVector WindDirection;
		float WindStrength;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set wind parameters\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set wind parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindStrength;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms, WindStrength), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_WindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "SetWindParameters", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::AuracronMetaHumanClothingSystem_eventSetWindParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execSetWindParameters)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WindDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WindStrength);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetWindParameters(Z_Param_Out_WindDirection,Z_Param_WindStrength);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function SetWindParameters ****************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function Shutdown ***********************
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown clothing system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown clothing system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function Shutdown *************************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function SimplifyClothingMesh ***********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics
{
	struct AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms
	{
		UClothingAssetBase* ClothingAsset;
		float ReductionPercentage;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Simplify clothing mesh\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simplify clothing mesh" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothingAsset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReductionPercentage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ClothingAsset = { "ClothingAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms, ClothingAsset), Z_Construct_UClass_UClothingAssetBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ReductionPercentage = { "ReductionPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms, ReductionPercentage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ClothingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ReductionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "SimplifyClothingMesh", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::AuracronMetaHumanClothingSystem_eventSimplifyClothingMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execSimplifyClothingMesh)
{
	P_GET_OBJECT(UClothingAssetBase,Z_Param_ClothingAsset);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ReductionPercentage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->SimplifyClothingMesh(Z_Param_ClothingAsset,Z_Param_ReductionPercentage);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function SimplifyClothingMesh *************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function StartClothingSimulation ********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics
{
	struct AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Start clothing simulation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start clothing simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "StartClothingSimulation", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventStartClothingSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execStartClothingSimulation)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartClothingSimulation(Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function StartClothingSimulation **********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function StopClothingSimulation *********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics
{
	struct AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Stop clothing simulation\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop clothing simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "StopClothingSimulation", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::AuracronMetaHumanClothingSystem_eventStopClothingSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execStopClothingSimulation)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopClothingSimulation(Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function StopClothingSimulation ***********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function UpdateClothingPhysics **********
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics
{
	struct AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms
	{
		USkeletalMeshComponent* MeshComponent;
		FString ClothingName;
		FAuracronClothingPhysicsConfig PhysicsConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update clothing physics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update clothing physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClothingName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClothingName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms, MeshComponent), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ClothingName = { "ClothingName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms, ClothingName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClothingName_MetaData), NewProp_ClothingName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_PhysicsConfig = { "PhysicsConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms, PhysicsConfig), Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsConfig_MetaData), NewProp_PhysicsConfig_MetaData) }; // 1728097396
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ClothingName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_PhysicsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "UpdateClothingPhysics", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::AuracronMetaHumanClothingSystem_eventUpdateClothingPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execUpdateClothingPhysics)
{
	P_GET_OBJECT(USkeletalMeshComponent,Z_Param_MeshComponent);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClothingName);
	P_GET_STRUCT_REF(FAuracronClothingPhysicsConfig,Z_Param_Out_PhysicsConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateClothingPhysics(Z_Param_MeshComponent,Z_Param_ClothingName,Z_Param_Out_PhysicsConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function UpdateClothingPhysics ************

// ********** Begin Class UAuracronMetaHumanClothingSystem Function UpdateMaterialParameters *******
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics
{
	struct AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms
	{
		UMaterialInstanceDynamic* MaterialInstance;
		TMap<FString,float> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Update material parameters\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update material parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialInstance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_MaterialInstance = { "MaterialInstance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms, MaterialInstance), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms), &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_MaterialInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "UpdateMaterialParameters", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::AuracronMetaHumanClothingSystem_eventUpdateMaterialParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execUpdateMaterialParameters)
{
	P_GET_OBJECT(UMaterialInstanceDynamic,Z_Param_MaterialInstance);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateMaterialParameters(Z_Param_MaterialInstance,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function UpdateMaterialParameters *********

// ********** Begin Class UAuracronMetaHumanClothingSystem Function ValidateClothingConfiguration **
struct Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics
{
	struct AuracronMetaHumanClothingSystem_eventValidateClothingConfiguration_Parms
	{
		FAuracronClothingConfig Config;
		FAuracronClothingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|Clothing|Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate clothing configuration\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate clothing configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventValidateClothingConfiguration_Parms, Config), Z_Construct_UScriptStruct_FAuracronClothingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 532802727
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanClothingSystem_eventValidateClothingConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronClothingResult, METADATA_PARAMS(0, nullptr) }; // 2625502296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanClothingSystem, nullptr, "ValidateClothingConfiguration", Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::AuracronMetaHumanClothingSystem_eventValidateClothingConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::AuracronMetaHumanClothingSystem_eventValidateClothingConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanClothingSystem::execValidateClothingConfiguration)
{
	P_GET_STRUCT_REF(FAuracronClothingConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronClothingResult*)Z_Param__Result=P_THIS->ValidateClothingConfiguration(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanClothingSystem Function ValidateClothingConfiguration ****

// ********** Begin Class UAuracronMetaHumanClothingSystem *****************************************
void UAuracronMetaHumanClothingSystem::StaticRegisterNativesUAuracronMetaHumanClothingSystem()
{
	UClass* Class = UAuracronMetaHumanClothingSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzeClothingPerformance", &UAuracronMetaHumanClothingSystem::execAnalyzeClothingPerformance },
		{ "ApplyClothingToMetaHuman", &UAuracronMetaHumanClothingSystem::execApplyClothingToMetaHuman },
		{ "ApplyMaterialToClothing", &UAuracronMetaHumanClothingSystem::execApplyMaterialToClothing },
		{ "CreateClothingAsset", &UAuracronMetaHumanClothingSystem::execCreateClothingAsset },
		{ "CreateClothingAssetAsync", &UAuracronMetaHumanClothingSystem::execCreateClothingAssetAsync },
		{ "CreateClothingMaterial", &UAuracronMetaHumanClothingSystem::execCreateClothingMaterial },
		{ "GenerateClothingLODs", &UAuracronMetaHumanClothingSystem::execGenerateClothingLODs },
		{ "GetAsyncOperationResult", &UAuracronMetaHumanClothingSystem::execGetAsyncOperationResult },
		{ "GetClothingStatistics", &UAuracronMetaHumanClothingSystem::execGetClothingStatistics },
		{ "GetSupportedClothingTypes", &UAuracronMetaHumanClothingSystem::execGetSupportedClothingTypes },
		{ "GetSupportedMaterialTypes", &UAuracronMetaHumanClothingSystem::execGetSupportedMaterialTypes },
		{ "Initialize", &UAuracronMetaHumanClothingSystem::execInitialize },
		{ "IsAsyncOperationComplete", &UAuracronMetaHumanClothingSystem::execIsAsyncOperationComplete },
		{ "IsInitialized", &UAuracronMetaHumanClothingSystem::execIsInitialized },
		{ "OptimizeClothingPerformance", &UAuracronMetaHumanClothingSystem::execOptimizeClothingPerformance },
		{ "RemoveClothingFromMetaHuman", &UAuracronMetaHumanClothingSystem::execRemoveClothingFromMetaHuman },
		{ "ResetClothingSimulation", &UAuracronMetaHumanClothingSystem::execResetClothingSimulation },
		{ "SetWindParameters", &UAuracronMetaHumanClothingSystem::execSetWindParameters },
		{ "Shutdown", &UAuracronMetaHumanClothingSystem::execShutdown },
		{ "SimplifyClothingMesh", &UAuracronMetaHumanClothingSystem::execSimplifyClothingMesh },
		{ "StartClothingSimulation", &UAuracronMetaHumanClothingSystem::execStartClothingSimulation },
		{ "StopClothingSimulation", &UAuracronMetaHumanClothingSystem::execStopClothingSimulation },
		{ "UpdateClothingPhysics", &UAuracronMetaHumanClothingSystem::execUpdateClothingPhysics },
		{ "UpdateMaterialParameters", &UAuracronMetaHumanClothingSystem::execUpdateMaterialParameters },
		{ "ValidateClothingConfiguration", &UAuracronMetaHumanClothingSystem::execValidateClothingConfiguration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem;
UClass* UAuracronMetaHumanClothingSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanClothingSystem;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanClothingSystem"),
			Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanClothingSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem_NoRegister()
{
	return UAuracronMetaHumanClothingSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|Clothing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman Clothing System\n * Advanced clothing simulation and management system for UE 5.6\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanClothingSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman Clothing System\nAdvanced clothing simulation and management system for UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnClothingComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|Clothing|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when an async clothing operation completes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when an async clothing operation completes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanClothingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnClothingComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_AnalyzeClothingPerformance, "AnalyzeClothingPerformance" }, // 1032625607
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyClothingToMetaHuman, "ApplyClothingToMetaHuman" }, // 2157872264
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ApplyMaterialToClothing, "ApplyMaterialToClothing" }, // 667673981
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAsset, "CreateClothingAsset" }, // 3904141505
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingAssetAsync, "CreateClothingAssetAsync" }, // 796168736
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_CreateClothingMaterial, "CreateClothingMaterial" }, // 1091507648
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GenerateClothingLODs, "GenerateClothingLODs" }, // 2500233107
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetAsyncOperationResult, "GetAsyncOperationResult" }, // 2477357094
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetClothingStatistics, "GetClothingStatistics" }, // 2315809529
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedClothingTypes, "GetSupportedClothingTypes" }, // 1046065090
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_GetSupportedMaterialTypes, "GetSupportedMaterialTypes" }, // 526865360
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Initialize, "Initialize" }, // 3880470633
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsAsyncOperationComplete, "IsAsyncOperationComplete" }, // 2544469427
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_IsInitialized, "IsInitialized" }, // 463991789
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_OptimizeClothingPerformance, "OptimizeClothingPerformance" }, // 262602711
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_RemoveClothingFromMetaHuman, "RemoveClothingFromMetaHuman" }, // 1457095654
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ResetClothingSimulation, "ResetClothingSimulation" }, // 691930025
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SetWindParameters, "SetWindParameters" }, // 2508901435
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_Shutdown, "Shutdown" }, // 136263502
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_SimplifyClothingMesh, "SimplifyClothingMesh" }, // 1716817718
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StartClothingSimulation, "StartClothingSimulation" }, // 4096500970
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_StopClothingSimulation, "StopClothingSimulation" }, // 2350032658
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateClothingPhysics, "UpdateClothingPhysics" }, // 2987947457
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_UpdateMaterialParameters, "UpdateMaterialParameters" }, // 3682986482
		{ &Z_Construct_UFunction_UAuracronMetaHumanClothingSystem_ValidateClothingConfiguration, "ValidateClothingConfiguration" }, // 1502760803
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanClothingSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_OnClothingComplete = { "OnClothingComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanClothingSystem, OnClothingComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronClothingComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnClothingComplete_MetaData), NewProp_OnClothingComplete_MetaData) }; // 1715699148
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanClothingSystem, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanClothingSystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanClothingSystem), &Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanClothingSystem, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_OnClothingComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::ClassParams = {
	&UAuracronMetaHumanClothingSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanClothingSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanClothingSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanClothingSystem);
// ********** End Class UAuracronMetaHumanClothingSystem *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronClothingType_StaticEnum, TEXT("EAuracronClothingType"), &Z_Registration_Info_UEnum_EAuracronClothingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1901076960U) },
		{ EAuracronClothingMaterial_StaticEnum, TEXT("EAuracronClothingMaterial"), &Z_Registration_Info_UEnum_EAuracronClothingMaterial, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2980797378U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronClothingPhysicsConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronClothingPhysicsConfig_Statics::NewStructOps, TEXT("AuracronClothingPhysicsConfig"), &Z_Registration_Info_UScriptStruct_FAuracronClothingPhysicsConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronClothingPhysicsConfig), 1728097396U) },
		{ FAuracronClothingConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronClothingConfig_Statics::NewStructOps, TEXT("AuracronClothingConfig"), &Z_Registration_Info_UScriptStruct_FAuracronClothingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronClothingConfig), 532802727U) },
		{ FAuracronClothingResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronClothingResult_Statics::NewStructOps, TEXT("AuracronClothingResult"), &Z_Registration_Info_UScriptStruct_FAuracronClothingResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronClothingResult), 2625502296U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanClothingSystem, UAuracronMetaHumanClothingSystem::StaticClass, TEXT("UAuracronMetaHumanClothingSystem"), &Z_Registration_Info_UClass_UAuracronMetaHumanClothingSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanClothingSystem), 2912516709U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_2241543106(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanClothingSystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
