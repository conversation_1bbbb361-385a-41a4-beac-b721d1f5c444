// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronPCGBridge_init() {}
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheEviction__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheHit__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheMiss__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestCompleted__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestStarted__DelegateSignature();
	AURACRONPCGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestSuiteCompleted__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronPCGBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronPCGBridge_OnPCGGenerationProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGAsyncTaskManager_OnTaskProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheEviction__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheHit__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGCacheManager_OnCacheMiss__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronPCGTestRunner_OnTestSuiteCompleted__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronPCGBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x9B9DD778,
				0xD2A05917,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronPCGBridge(Z_Construct_UPackage__Script_AuracronPCGBridge, TEXT("/Script/AuracronPCGBridge"), Z_Registration_Info_UPackage__Script_AuracronPCGBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x9B9DD778, 0xD2A05917));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
