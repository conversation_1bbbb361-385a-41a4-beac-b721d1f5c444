// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronEOSBridge_init() {}
	AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature();
	AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature();
	AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature();
	AURACRONEOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronEOSBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronEOSBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronEOSBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnAchievementUnlocked__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnEOSLoginCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnFriendOnline__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronEOSBridge_OnSessionCreated__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronEOSBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x4038E690,
				0x691D3E34,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronEOSBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronEOSBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronEOSBridge(Z_Construct_UPackage__Script_AuracronEOSBridge, TEXT("/Script/AuracronEOSBridge"), Z_Registration_Info_UPackage__Script_AuracronEOSBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x4038E690, 0x691D3E34));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
