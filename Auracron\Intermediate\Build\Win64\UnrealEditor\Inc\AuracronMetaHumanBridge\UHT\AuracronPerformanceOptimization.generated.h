// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPerformanceOptimization.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronPerformanceOptimization_generated_h
#error "AuracronPerformanceOptimization.generated.h already included, missing '#pragma once' in AuracronPerformanceOptimization.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronPerformanceOptimization_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FMemoryPoolConfiguration ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_65_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMemoryPoolConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMemoryPoolConfiguration;
// ********** End ScriptStruct FMemoryPoolConfiguration ********************************************

// ********** Begin ScriptStruct FAsyncProcessingConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_92_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAsyncProcessingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAsyncProcessingConfiguration;
// ********** End ScriptStruct FAsyncProcessingConfiguration ***************************************

// ********** Begin ScriptStruct FGPUAccelerationConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGPUAccelerationConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FGPUAccelerationConfiguration;
// ********** End ScriptStruct FGPUAccelerationConfiguration ***************************************

// ********** Begin ScriptStruct FBatchProcessingConfiguration *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_143_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBatchProcessingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBatchProcessingConfiguration;
// ********** End ScriptStruct FBatchProcessingConfiguration ***************************************

// ********** Begin ScriptStruct FPerformanceOptimizationConfiguration *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_167_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPerformanceOptimizationConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPerformanceOptimizationConfiguration;
// ********** End ScriptStruct FPerformanceOptimizationConfiguration *******************************

// ********** Begin ScriptStruct FPerformanceMetrics ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h_197_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPerformanceMetrics;
// ********** End ScriptStruct FPerformanceMetrics *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronPerformanceOptimization_h

// ********** Begin Enum EPerformanceOptimizationLevel *********************************************
#define FOREACH_ENUM_EPERFORMANCEOPTIMIZATIONLEVEL(op) \
	op(EPerformanceOptimizationLevel::None) \
	op(EPerformanceOptimizationLevel::Basic) \
	op(EPerformanceOptimizationLevel::Moderate) \
	op(EPerformanceOptimizationLevel::Aggressive) \
	op(EPerformanceOptimizationLevel::Custom) 

enum class EPerformanceOptimizationLevel : uint8;
template<> struct TIsUEnumClass<EPerformanceOptimizationLevel> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPerformanceOptimizationLevel>();
// ********** End Enum EPerformanceOptimizationLevel ***********************************************

// ********** Begin Enum EMemoryPoolType ***********************************************************
#define FOREACH_ENUM_EMEMORYPOOLTYPE(op) \
	op(EMemoryPoolType::DNA) \
	op(EMemoryPoolType::Texture) \
	op(EMemoryPoolType::Mesh) \
	op(EMemoryPoolType::Animation) \
	op(EMemoryPoolType::Audio) \
	op(EMemoryPoolType::General) 

enum class EMemoryPoolType : uint8;
template<> struct TIsUEnumClass<EMemoryPoolType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMemoryPoolType>();
// ********** End Enum EMemoryPoolType *************************************************************

// ********** Begin Enum EAsyncProcessingPriority **************************************************
#define FOREACH_ENUM_EASYNCPROCESSINGPRIORITY(op) \
	op(EAsyncProcessingPriority::Low) \
	op(EAsyncProcessingPriority::Normal) \
	op(EAsyncProcessingPriority::High) \
	op(EAsyncProcessingPriority::Critical) 

enum class EAsyncProcessingPriority : uint8;
template<> struct TIsUEnumClass<EAsyncProcessingPriority> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAsyncProcessingPriority>();
// ********** End Enum EAsyncProcessingPriority ****************************************************

// ********** Begin Enum EBatchOperationType *******************************************************
#define FOREACH_ENUM_EBATCHOPERATIONTYPE(op) \
	op(EBatchOperationType::DNAProcessing) \
	op(EBatchOperationType::TextureGeneration) \
	op(EBatchOperationType::MeshDeformation) \
	op(EBatchOperationType::AnimationBaking) 

enum class EBatchOperationType : uint8;
template<> struct TIsUEnumClass<EBatchOperationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBatchOperationType>();
// ********** End Enum EBatchOperationType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
