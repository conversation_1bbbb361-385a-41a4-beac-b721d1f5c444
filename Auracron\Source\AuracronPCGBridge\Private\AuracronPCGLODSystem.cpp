// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - LOD System Implementation
// Bridge 2.12: PCG Framework - LOD e Optimization

#include "AuracronPCGLODSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/LODActor.h"
#include "Async/ParallelFor.h"
#include "RenderingThread.h"
#include "Stats/Stats.h"

// =============================================================================
// LOD GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGLODGeneratorSettings::UAuracronPCGLODGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("LOD Generator");
    NodeMetadata.NodeDescription = TEXT("Generates Level of Detail meshes and configurations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("LOD"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.6f);
}

void UAuracronPCGLODGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (!bUseSourceMeshFromInput)
    {
        FPCGPinProperties& MeshPin = InputPins.Emplace_GetRef();
        MeshPin.Label = TEXT("Source Mesh");
        MeshPin.AllowedTypes = EPCGDataType::Attribute;
        MeshPin.bAdvancedPin = true;
    }
}

void UAuracronPCGLODGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputLODMeshes)
    {
        FPCGPinProperties& LODPin = OutputPins.Emplace_GetRef();
        LODPin.Label = TEXT("LOD Meshes");
        LODPin.AllowedTypes = EPCGDataType::Attribute;
        LODPin.bAdvancedPin = true;
    }

    if (bOutputLODStatistics)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("LOD Statistics");
        StatsPin.AllowedTypes = EPCGDataType::Attribute;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGLODGeneratorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                       FPCGDataCollection& OutputData, 
                                                                       const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGLODGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGLODGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for LOD Generator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 LODsGenerated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Generate LODs for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Get source mesh for this point
                UStaticMesh* SourceMesh = GetSourceMeshForPoint(OutputPoint, Settings);
                
                if (SourceMesh)
                {
                    // Generate LOD chain
                    TArray<UStaticMesh*> LODMeshes = GenerateLODChainForPoint(SourceMesh, Settings);
                    
                    if (LODMeshes.Num() > 0)
                    {
                        ApplyLODToPoint(OutputPoint, LODMeshes, Settings);
                        LODsGenerated++;
                    }
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("LOD Generator processed %d points, generated %d LOD chains"), 
                                  TotalProcessed, LODsGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("LOD Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UStaticMesh* FAuracronPCGLODGeneratorElement::GetSourceMeshForPoint(const FPCGPoint& Point, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    if (Settings->bUseSourceMeshFromInput)
    {
        // In production, you'd extract mesh from point attributes
        // For now, return a default mesh if available
        return AuracronPCGLODSystemUtils::LoadMeshSafe(Settings->SourceMesh);
    }
    else
    {
        return AuracronPCGLODSystemUtils::LoadMeshSafe(Settings->SourceMesh);
    }
}

TArray<UStaticMesh*> FAuracronPCGLODGeneratorElement::GenerateLODChainForPoint(UStaticMesh* SourceMesh, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    if (!SourceMesh)
    {
        return TArray<UStaticMesh*>();
    }

    // Use utility function for LOD generation
    return UAuracronPCGLODSystemUtils::GenerateLODChain(SourceMesh, Settings->LODDescriptor);
}

void FAuracronPCGLODGeneratorElement::ApplyLODToPoint(FPCGPoint& Point, const TArray<UStaticMesh*>& LODMeshes, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    if (LODMeshes.Num() == 0)
    {
        return;
    }
    
    // Calculate appropriate LOD level based on distance and screen size
    float DistanceToCamera = 0.0f;
    if (UWorld* World = Settings->GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (APawn* Pawn = PC->GetPawn())
            {
                DistanceToCamera = FVector::Dist(Point.Transform.GetLocation(), Pawn->GetActorLocation());
            }
        }
    }
    
    // Determine LOD level based on distance thresholds
    int32 LODLevel = 0;
    for (int32 i = 0; i < Settings->LODDescriptor.DistanceThresholds.Num() && i < LODMeshes.Num() - 1; i++)
    {
        if (DistanceToCamera > Settings->LODDescriptor.DistanceThresholds[i])
        {
            LODLevel = i + 1;
        }
        else
        {
            break;
        }
    }
    
    // Clamp LOD level to available meshes
    LODLevel = FMath::Clamp(LODLevel, 0, LODMeshes.Num() - 1);
    
    // Store LOD information in point metadata
    if (UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? 
        const_cast<UPCGMetadata*>(Point.Metadata) : nullptr)
    {
        // Store selected LOD mesh path
        if (LODMeshes[LODLevel])
        {
            FString MeshPath = LODMeshes[LODLevel]->GetPathName();
            Metadata->SetStringAttribute(Point.MetadataEntry, TEXT("LODMeshPath"), MeshPath);
        }
        
        // Store LOD level
        Metadata->SetFloatAttribute(Point.MetadataEntry, TEXT("LODLevel"), static_cast<float>(LODLevel));
        
        // Store distance to camera
        Metadata->SetFloatAttribute(Point.MetadataEntry, TEXT("DistanceToCamera"), DistanceToCamera);
    }
    
    // Set density based on LOD quality (higher LOD = lower density for performance)
    float LODQuality = 1.0f - (static_cast<float>(LODLevel) / FMath::Max(1.0f, static_cast<float>(LODMeshes.Num() - 1)));
    Point.Density = FMath::Clamp(Point.Density * LODQuality, 0.1f, 1.0f);
    
    // Set color for LOD visualization (green = high quality, red = low quality)
    float LODRatio = static_cast<float>(LODLevel) / FMath::Max(1.0f, static_cast<float>(LODMeshes.Num() - 1));
    Point.Color = FVector4(LODRatio, 1.0f - LODRatio, 0.2f, 1.0f);
    
    AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Applied LOD %d to point at distance %.2f"), LODLevel, DistanceToCamera);
}

// =============================================================================
// DISTANCE BASED CULLER IMPLEMENTATION
// =============================================================================

UAuracronPCGDistanceBasedCullerSettings::UAuracronPCGDistanceBasedCullerSettings()
{
    NodeMetadata.NodeName = TEXT("Distance Based Culler");
    NodeMetadata.NodeDescription = TEXT("Culls instances based on distance and other criteria");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Culling"));
    NodeMetadata.Tags.Add(TEXT("Distance"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.2f);
}

void UAuracronPCGDistanceBasedCullerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (!bUseViewerLocation)
    {
        FPCGPinProperties& ReferencePin = InputPins.Emplace_GetRef();
        ReferencePin.Label = TEXT("Reference Location");
        ReferencePin.AllowedTypes = EPCGDataType::Attribute;
        ReferencePin.bAdvancedPin = true;
    }
}

void UAuracronPCGDistanceBasedCullerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputCullingInfo)
    {
        FPCGPinProperties& CullingPin = OutputPins.Emplace_GetRef();
        CullingPin.Label = TEXT("Culling Info");
        CullingPin.AllowedTypes = EPCGDataType::Attribute;
        CullingPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGDistanceBasedCullerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                              FPCGDataCollection& OutputData, 
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGDistanceBasedCullerSettings* Settings = GetTypedSettings<UAuracronPCGDistanceBasedCullerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Distance Based Culler");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 InstancesCulled = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Get reference location
        FVector ReferenceLocation = GetReferenceLocation(Settings);

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint> OutputPoints;
            OutputPoints.Reserve(InputPoints.Num());

            // Perform culling for each point
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                bool bShouldCull = ShouldCullPoint(InputPoint, ReferenceLocation, Settings);
                
                if (!bShouldCull)
                {
                    FPCGPoint OutputPoint = InputPoint;
                    ApplyCullingInfoToPoint(OutputPoint, ReferenceLocation, Settings);
                    OutputPoints.Add(OutputPoint);
                }
                else
                {
                    InstancesCulled++;
                }
                
                TotalProcessed++;
            }

            OutputPointData->GetMutablePoints() = OutputPoints;
            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Distance Based Culler processed %d points, culled %d instances"), 
                                  TotalProcessed, InstancesCulled);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Distance Based Culler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

FVector FAuracronPCGDistanceBasedCullerElement::GetReferenceLocation(const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    if (Settings->bUseViewerLocation)
    {
        // In production, you'd get actual viewer/camera location
        // For now, return a default location
        return FVector::ZeroVector;
    }
    else
    {
        return Settings->ReferenceLocation;
    }
}

bool FAuracronPCGDistanceBasedCullerElement::ShouldCullPoint(const FPCGPoint& Point, const FVector& ReferenceLocation, const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    FVector PointLocation = Point.Transform.GetLocation();
    
    // Use utility function for culling decision
    return UAuracronPCGLODSystemUtils::ShouldCullInstance(PointLocation, ReferenceLocation, Settings->CullingDescriptor);
}

void FAuracronPCGDistanceBasedCullerElement::ApplyCullingInfoToPoint(FPCGPoint& Point, const FVector& ReferenceLocation, const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    if (Settings->bOutputCullingInfo)
    {
        FVector PointLocation = Point.Transform.GetLocation();
        float Distance = FVector::Dist(PointLocation, ReferenceLocation);
        
        // Set distance as density
        float NormalizedDistance = Distance / Settings->CullingDescriptor.MaxDrawDistance;
        Point.Density = FMath::Clamp(1.0f - NormalizedDistance, 0.0f, 1.0f);
        
        // Set culling visualization as color
        Point.Color = FVector4(NormalizedDistance, 1.0f - NormalizedDistance, 0.0f, 1.0f);
    }
}

// =============================================================================
// INSTANCING OPTIMIZER IMPLEMENTATION
// =============================================================================

UAuracronPCGInstancingOptimizerSettings::UAuracronPCGInstancingOptimizerSettings()
{
    NodeMetadata.NodeName = TEXT("Instancing Optimizer");
    NodeMetadata.NodeDescription = TEXT("Optimizes mesh instances for better performance");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Instancing"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Batching"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.2f, 0.8f);
}

void UAuracronPCGInstancingOptimizerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGInstancingOptimizerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputInstanceComponents)
    {
        FPCGPinProperties& ComponentsPin = OutputPins.Emplace_GetRef();
        ComponentsPin.Label = TEXT("Instance Components");
        ComponentsPin.AllowedTypes = EPCGDataType::Attribute;
        ComponentsPin.bAdvancedPin = true;
    }

    if (bOutputOptimizationStats)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("Optimization Stats");
        StatsPin.AllowedTypes = EPCGDataType::Attribute;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGInstancingOptimizerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                              FPCGDataCollection& OutputData,
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGInstancingOptimizerSettings* Settings = GetTypedSettings<UAuracronPCGInstancingOptimizerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Instancing Optimizer");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 InstancesOptimized = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Group instances for optimization
            TMap<FString, TArray<FPCGPoint>> InstanceGroups = GroupInstancesForOptimization(InputPoints, Settings);

            // Optimize each group
            for (auto& GroupPair : InstanceGroups)
            {
                TArray<FPCGPoint> OptimizedInstances = OptimizeInstanceGroup(GroupPair.Value, Settings);

                for (FPCGPoint& OptimizedInstance : OptimizedInstances)
                {
                    ApplyOptimizationToPoint(OptimizedInstance, Settings);
                    OutputPoints.Add(OptimizedInstance);
                    InstancesOptimized++;
                }

                TotalProcessed += GroupPair.Value.Num();
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Instancing Optimizer processed %d points, optimized %d instances"),
                                  TotalProcessed, InstancesOptimized);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Instancing Optimizer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

TMap<FString, TArray<FPCGPoint>> FAuracronPCGInstancingOptimizerElement::GroupInstancesForOptimization(const TArray<FPCGPoint>& Points, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    TMap<FString, TArray<FPCGPoint>> Groups;

    for (const FPCGPoint& Point : Points)
    {
        FString GroupKey = GenerateGroupKey(Point, Settings);

        if (!Groups.Contains(GroupKey))
        {
            Groups.Add(GroupKey, TArray<FPCGPoint>());
        }

        Groups[GroupKey].Add(Point);
    }

    return Groups;
}

FString FAuracronPCGInstancingOptimizerElement::GenerateGroupKey(const FPCGPoint& Point, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    FString GroupKey = TEXT("Default");

    // Group by real mesh references using UE5.6 PCG point attributes
    if (Settings->bGroupByMesh)
    {
        GroupKey += GenerateRealMeshGroupKey(Point);
    }

    // Group by real material references using UE5.6 material system
    if (Settings->bGroupByMaterial)
    {
        GroupKey += GenerateRealMaterialGroupKey(Point);
    }

    // Group by LOD level
    if (Settings->bGroupByLODLevel)
    {
        int32 LODLevel = 0;
        
        // Try to extract LOD level from metadata first
        if (Point.Metadata && Point.MetadataEntry != PCGInvalidEntryKey)
        {
            if (const UPCGMetadata* Metadata = Point.Metadata)
            {
                // Check if LOD level is stored in metadata
                if (Metadata->HasAttribute(TEXT("LODLevel")))
                {
                    float LODLevelFloat = 0.0f;
                    if (Metadata->GetFloatAttribute(Point.MetadataEntry, TEXT("LODLevel"), LODLevelFloat))
                    {
                        LODLevel = FMath::FloorToInt(LODLevelFloat);
                    }
                }
                // Fallback to density-based calculation if no metadata
                else
                {
                    LODLevel = FMath::FloorToInt(Point.Density * 4.0f);
                }
            }
        }
        else
        {
            // Fallback to density-based calculation
            LODLevel = FMath::FloorToInt(Point.Density * 4.0f);
        }
        
        // Clamp LOD level to reasonable range
        LODLevel = FMath::Clamp(LODLevel, 0, 4);
        GroupKey += FString::Printf(TEXT("_LOD%d"), LODLevel);
    }

    return GroupKey;
}

TArray<FPCGPoint> FAuracronPCGInstancingOptimizerElement::OptimizeInstanceGroup(const TArray<FPCGPoint>& GroupPoints, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    TArray<FPCGPoint> OptimizedPoints = GroupPoints;

    // Remove duplicate instances
    if (Settings->bRemoveDuplicateInstances)
    {
        OptimizedPoints = RemoveDuplicateInstances(OptimizedPoints, Settings->DuplicateThreshold);
    }

    // Optimize instance transforms
    if (Settings->bOptimizeInstanceTransforms)
    {
        OptimizeInstanceTransforms(OptimizedPoints, Settings);
    }

    // Sort instances by distance
    if (Settings->bSortInstancesByDistance)
    {
        SortInstancesByDistance(OptimizedPoints);
    }

    return OptimizedPoints;
}

TArray<FPCGPoint> FAuracronPCGInstancingOptimizerElement::RemoveDuplicateInstances(const TArray<FPCGPoint>& Points, float Threshold) const
{
    TArray<FPCGPoint> UniquePoints;

    for (const FPCGPoint& Point : Points)
    {
        bool bIsDuplicate = false;

        for (const FPCGPoint& UniquePoint : UniquePoints)
        {
            float Distance = FVector::Dist(Point.Transform.GetLocation(), UniquePoint.Transform.GetLocation());
            if (Distance < Threshold)
            {
                bIsDuplicate = true;
                break;
            }
        }

        if (!bIsDuplicate)
        {
            UniquePoints.Add(Point);
        }
    }

    return UniquePoints;
}

void FAuracronPCGInstancingOptimizerElement::OptimizeInstanceTransforms(TArray<FPCGPoint>& Points, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    // Extract transforms
    TArray<FTransform> Transforms;
    for (const FPCGPoint& Point : Points)
    {
        Transforms.Add(Point.Transform);
    }

    // Use utility function for optimization
    UAuracronPCGLODSystemUtils::OptimizeInstanceTransforms(Transforms, Settings->InstancingDescriptor.InstanceMergingThreshold);

    // Apply optimized transforms back to points
    for (int32 i = 0; i < Points.Num() && i < Transforms.Num(); i++)
    {
        Points[i].Transform = Transforms[i];
    }
}

void FAuracronPCGInstancingOptimizerElement::SortInstancesByDistance(TArray<FPCGPoint>& Points) const
{
    // Sort by distance from origin (simplified)
    Points.Sort([](const FPCGPoint& A, const FPCGPoint& B) -> bool
    {
        float DistanceA = A.Transform.GetLocation().Size();
        float DistanceB = B.Transform.GetLocation().Size();
        return DistanceA < DistanceB;
    });
}

void FAuracronPCGInstancingOptimizerElement::ApplyOptimizationToPoint(FPCGPoint& Point, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    // Apply real optimization based on LOD level and distance
    FVector CameraLocation = FVector::ZeroVector;
    
    // Get actual camera location from player controller or viewport
    if (UWorld* World = GEngine->GetWorldFromContextObject(Settings, EGetWorldErrorMode::LogAndReturnNull))
    {
        if (APlayerController* PlayerController = World->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PlayerController->GetPawn())
            {
                CameraLocation = PlayerPawn->GetActorLocation();
            }
        }
        
        // Fallback to viewport camera if no player pawn
        if (CameraLocation.IsZero())
        {
            if (UGameViewportClient* ViewportClient = World->GetGameViewport())
            {
                FVector ViewLocation;
                FRotator ViewRotation;
                ViewportClient->GetViewPoint(ViewLocation, ViewRotation);
                CameraLocation = ViewLocation;
            }
        }
    }
    
    // Calculate actual distance from camera to point
    float DistanceFromCamera = FVector::Dist(Point.Transform.GetLocation(), CameraLocation);
    
    // Calculate LOD level based on distance
    int32 LODLevel = 0;
    if (DistanceFromCamera > Settings->LODDistances[0])
    {
        LODLevel = 1;
    }
    if (DistanceFromCamera > Settings->LODDistances[1])
    {
        LODLevel = 2;
    }
    if (DistanceFromCamera > Settings->LODDistances[2])
    {
        LODLevel = 3;
    }
    
    // Apply density reduction based on LOD level
    float DensityMultiplier = 1.0f;
    switch (LODLevel)
    {
        case 1: DensityMultiplier = 0.75f; break;
        case 2: DensityMultiplier = 0.5f; break;
        case 3: DensityMultiplier = 0.25f; break;
        default: DensityMultiplier = 1.0f; break;
    }
    
    Point.Density = FMath::Clamp(Point.Density * DensityMultiplier, 0.0f, 1.0f);
    
    // Apply scale reduction for distant objects
    if (LODLevel > 0)
    {
        FVector CurrentScale = Point.Transform.GetScale3D();
        float ScaleMultiplier = FMath::Lerp(1.0f, 0.8f, (float)LODLevel / 3.0f);
        Point.Transform.SetScale3D(CurrentScale * ScaleMultiplier);
    }
    
    // Set optimization visualization color based on LOD level
    FVector4 LODColors[] = {
        FVector4(1.0f, 1.0f, 1.0f, 1.0f), // White for LOD 0
        FVector4(0.0f, 1.0f, 0.0f, 1.0f), // Green for LOD 1
        FVector4(1.0f, 1.0f, 0.0f, 1.0f), // Yellow for LOD 2
        FVector4(1.0f, 0.0f, 0.0f, 1.0f)  // Red for LOD 3
    };
    Point.Color = LODColors[FMath::Clamp(LODLevel, 0, 3)];
}

// =============================================================================
// PERFORMANCE PROFILER IMPLEMENTATION
// =============================================================================

UAuracronPCGPerformanceProfilerSettings::UAuracronPCGPerformanceProfilerSettings()
{
    NodeMetadata.NodeName = TEXT("Performance Profiler");
    NodeMetadata.NodeDescription = TEXT("Profiles and monitors performance metrics");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Debug;
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Profiling"));
    NodeMetadata.Tags.Add(TEXT("Monitoring"));
    NodeMetadata.Tags.Add(TEXT("Debug"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.8f, 0.2f);
}

void UAuracronPCGPerformanceProfilerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGPerformanceProfilerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputPerformanceReport)
    {
        FPCGPinProperties& ReportPin = OutputPins.Emplace_GetRef();
        ReportPin.Label = TEXT("Performance Report");
        ReportPin.AllowedTypes = EPCGDataType::Attribute;
        ReportPin.bAdvancedPin = true;
    }

    if (bOutputRawMetrics)
    {
        FPCGPinProperties& MetricsPin = OutputPins.Emplace_GetRef();
        MetricsPin.Label = TEXT("Raw Metrics");
        MetricsPin.AllowedTypes = EPCGDataType::Attribute;
        MetricsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGPerformanceProfilerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                               FPCGDataCollection& OutputData,
                                                                               const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPerformanceProfilerSettings* Settings = GetTypedSettings<UAuracronPCGPerformanceProfilerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Performance Profiler");
            return Result;
        }

        // Start performance measurement
        double StartTime = FPlatformTime::Seconds();

        int32 TotalProcessed = 0;
        TArray<UPCGPointData*> ProcessedPointData;
        TMap<FString, float> PerformanceMetrics;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Profile each point processing
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Collect performance metrics for this point
                CollectPerformanceMetrics(OutputPoint, Settings, PerformanceMetrics);

                // Apply performance info to point
                ApplyPerformanceInfoToPoint(OutputPoint, PerformanceMetrics, Settings);

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // End performance measurement
        double EndTime = FPlatformTime::Seconds();
        double ExecutionTime = EndTime - StartTime;

        // Add execution time to metrics
        PerformanceMetrics.Add(TEXT("ExecutionTime"), static_cast<float>(ExecutionTime));
        PerformanceMetrics.Add(TEXT("PointsPerSecond"), TotalProcessed / static_cast<float>(ExecutionTime));

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Performance Profiler processed %d points in %.3fs"),
                                  TotalProcessed, ExecutionTime);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Performance Profiler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGPerformanceProfilerElement::CollectPerformanceMetrics(const FPCGPoint& Point, const UAuracronPCGPerformanceProfilerSettings* Settings, TMap<FString, float>& OutMetrics) const
{
    // Collect various performance metrics
    for (EAuracronPCGPerformanceMetric Metric : Settings->ProfilingDescriptor.MetricsToTrack)
    {
        float MetricValue = 0.0f;

        switch (Metric)
        {
            case EAuracronPCGPerformanceMetric::RenderTime:
                MetricValue = MeasureRenderTime(Point);
                break;
            case EAuracronPCGPerformanceMetric::DrawCalls:
                MetricValue = static_cast<float>(CountDrawCalls(Point));
                break;
            case EAuracronPCGPerformanceMetric::TriangleCount:
                MetricValue = static_cast<float>(CountTriangles(Point));
                break;
            case EAuracronPCGPerformanceMetric::MemoryUsage:
                MetricValue = MeasureMemoryUsage(Point);
                break;
            case EAuracronPCGPerformanceMetric::InstanceCount:
                MetricValue = 1.0f; // Each point represents one instance
                break;
            case EAuracronPCGPerformanceMetric::ScreenSize:
                MetricValue = CalculateScreenSize(Point);
                break;
            default:
                MetricValue = 1.0f;
                break;
        }

        FString MetricName = UEnum::GetValueAsString(Metric);
        OutMetrics.Add(MetricName, MetricValue);
    }
}

float FAuracronPCGPerformanceProfilerElement::MeasureRenderTime(const FPCGPoint& Point) const
{
    // Calculate render time based on point complexity and distance
    FVector Location = Point.Transform.GetLocation();
    float Distance = Location.Size();
    float Complexity = Point.Density * Point.Transform.GetScale3D().GetMax();
    
    // Base render time calculation
    float BaseRenderTime = 0.1f;
    float ComplexityMultiplier = FMath::Clamp(Complexity, 0.1f, 2.0f);
    float DistanceMultiplier = FMath::Clamp(1000.0f / FMath::Max(Distance, 100.0f), 0.1f, 2.0f);
    
    return BaseRenderTime * ComplexityMultiplier * DistanceMultiplier;
}

int32 FAuracronPCGPerformanceProfilerElement::CountDrawCalls(const FPCGPoint& Point) const
{
    // Calculate draw calls based on point properties and LOD level
    float Distance = Point.Transform.GetLocation().Size();
    float Scale = Point.Transform.GetScale3D().GetMax();
    
    // Determine LOD level based on distance
    int32 LODLevel = 0;
    if (Distance > 1000.0f) LODLevel = 1;
    if (Distance > 2000.0f) LODLevel = 2;
    if (Distance > 4000.0f) LODLevel = 3;
    
    // Base draw calls calculation
    int32 BaseDrawCalls = FMath::CeilToInt(Scale * Point.Density);
    
    // Reduce draw calls for higher LOD levels
    switch (LODLevel)
    {
        case 1: BaseDrawCalls = FMath::Max(1, BaseDrawCalls / 2); break;
        case 2: BaseDrawCalls = FMath::Max(1, BaseDrawCalls / 4); break;
        case 3: BaseDrawCalls = 1; break;
        default: break;
    }
    
    return FMath::Clamp(BaseDrawCalls, 1, 10);
}

int32 FAuracronPCGPerformanceProfilerElement::CountTriangles(const FPCGPoint& Point) const
{
    // Calculate triangle count based on point scale, density and LOD level
    float Distance = Point.Transform.GetLocation().Size();
    float Scale = Point.Transform.GetScale3D().GetMax();
    float Density = Point.Density;
    
    // Base triangle count (assuming a standard mesh complexity)
    int32 BaseTriangles = 500; // Base mesh triangle count
    
    // Scale multiplier based on object size
    float ScaleMultiplier = FMath::Clamp(Scale, 0.1f, 5.0f);
    
    // Density multiplier
    float DensityMultiplier = FMath::Clamp(Density, 0.1f, 2.0f);
    
    // LOD reduction based on distance
    float LODMultiplier = 1.0f;
    if (Distance > 1000.0f) LODMultiplier = 0.75f;
    if (Distance > 2000.0f) LODMultiplier = 0.5f;
    if (Distance > 4000.0f) LODMultiplier = 0.25f;
    
    int32 FinalTriangleCount = FMath::RoundToInt(BaseTriangles * ScaleMultiplier * DensityMultiplier * LODMultiplier);
    
    return FMath::Clamp(FinalTriangleCount, 50, 5000);
}

float FAuracronPCGPerformanceProfilerElement::MeasureMemoryUsage(const FPCGPoint& Point) const
{
    // Calculate memory usage based on point properties
    float Scale = Point.Transform.GetScale3D().GetMax();
    float Density = Point.Density;
    
    // Base memory usage in MB
    float BaseMemoryMB = 2.0f; // Base mesh and texture memory
    
    // Scale affects memory usage (larger objects need more detailed textures/geometry)
    float ScaleMultiplier = FMath::Pow(Scale, 1.5f); // Non-linear scaling
    
    // Density affects instance data memory
    float DensityMultiplier = FMath::Clamp(Density, 0.1f, 2.0f);
    
    // Additional memory for metadata and attributes
    float MetadataMemory = 0.5f;
    
    float TotalMemoryMB = (BaseMemoryMB * ScaleMultiplier * DensityMultiplier) + MetadataMemory;
    
    return FMath::Clamp(TotalMemoryMB, 0.5f, 50.0f);
}

float FAuracronPCGPerformanceProfilerElement::CalculateScreenSize(const FPCGPoint& Point) const
{
    // Calculate screen size based on object bounds and camera distance
    FVector Location = Point.Transform.GetLocation();
    FVector Scale = Point.Transform.GetScale3D();
    
    // Calculate object bounds radius
    float ObjectRadius = Scale.GetMax() * 50.0f; // Assuming 50 units base radius
    
    // Calculate distance from origin (simplified camera position)
    float Distance = FMath::Max(Location.Size(), 100.0f); // Minimum distance to avoid division by zero
    
    // Calculate angular size (in radians)
    float AngularSize = 2.0f * FMath::Atan(ObjectRadius / Distance);
    
    // Convert to screen space percentage (assuming 90 degree FOV)
    float FOV = PI / 2.0f; // 90 degrees in radians
    float ScreenSizeRatio = AngularSize / FOV;
    
    // Apply density as a visibility modifier
    ScreenSizeRatio *= Point.Density;
    
    return FMath::Clamp(ScreenSizeRatio, 0.001f, 1.0f);
}

void FAuracronPCGPerformanceProfilerElement::ApplyPerformanceInfoToPoint(FPCGPoint& Point, const TMap<FString, float>& PerformanceMetrics, const UAuracronPCGPerformanceProfilerSettings* Settings) const
{
    // Calculate overall performance score
    float PerformanceScore = CalculatePerformanceScore(PerformanceMetrics, Settings);

    // Set performance score as density
    Point.Density = FMath::Clamp(PerformanceScore, 0.0f, 1.0f);

    // Set performance visualization as color (green = good, red = bad)
    Point.Color = FVector4(1.0f - PerformanceScore, PerformanceScore, 0.0f, 1.0f);
}

float FAuracronPCGPerformanceProfilerElement::CalculatePerformanceScore(const TMap<FString, float>& PerformanceMetrics, const UAuracronPCGPerformanceProfilerSettings* Settings) const
{
    float Score = 1.0f;

    // Check against thresholds
    if (PerformanceMetrics.Contains(TEXT("RenderTime")))
    {
        float RenderTime = PerformanceMetrics[TEXT("RenderTime")];
        if (RenderTime > Settings->ProfilingDescriptor.MaxAcceptableRenderTime)
        {
            Score *= 0.5f;
        }
    }

    if (PerformanceMetrics.Contains(TEXT("DrawCalls")))
    {
        float DrawCalls = PerformanceMetrics[TEXT("DrawCalls")];
        if (DrawCalls > Settings->ProfilingDescriptor.MaxAcceptableDrawCalls)
        {
            Score *= 0.7f;
        }
    }

    if (PerformanceMetrics.Contains(TEXT("TriangleCount")))
    {
        float TriangleCount = PerformanceMetrics[TEXT("TriangleCount")];
        if (TriangleCount > Settings->ProfilingDescriptor.MaxAcceptableTriangles)
        {
            Score *= 0.8f;
        }
    }

    return FMath::Clamp(Score, 0.0f, 1.0f);
}
