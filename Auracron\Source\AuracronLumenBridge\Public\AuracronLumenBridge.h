﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Lumen Global Illumination Bridge Header
// Production-ready header file for UE5.6 Lumen API bridge

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/ReflectionCaptureComponent.h"
#include "Components/SphereReflectionCaptureComponent.h"
#include "Components/BoxReflectionCaptureComponent.h"
#include "Components/PlaneReflectionCaptureComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Engine/TextureCube.h"
#include "Engine/PostProcessVolume.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetRenderingLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "UObject/ConstructorHelpers.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/Package.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "HAL/PlatformFilemanager.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Logging/LogMacros.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

// Rendering Core
#include "RenderingThread.h"
#include "RenderResource.h"
#include "GlobalShader.h"
#include "SceneView.h"
#include "SceneViewExtension.h"
#include "SceneRendering.h"
#include "PrimitiveSceneProxy.h"
#include "StaticMeshResources.h"
#include "RenderGraphBuilder.h"
#include "RenderGraphUtils.h"
#include "ScreenPass.h"
#include "PostProcess/PostProcessing.h"
#include "PostProcess/PostProcessMaterial.h"

// Lumen Specific Includes
#include "Lumen/LumenSceneData.h"
#include "Lumen/LumenSurfaceCache.h"
#include "Lumen/LumenRadianceCache.h"
#include "Lumen/LumenScreenProbeGather.h"
#include "Lumen/LumenReflections.h"
#include "Lumen/LumenTranslucency.h"
#include "Lumen/LumenVisualize.h"
#include "Lumen/LumenHardwareRayTracing.h"
#include "Lumen/LumenTracingUtils.h"

// Global Illumination
#include "GlobalIllumination.h"
#include "DynamicGlobalIlluminationMethod.h"
#include "GlobalDistanceField.h"
#include "DistanceFieldAmbientOcclusion.h"
#include "DistanceFieldLightingShared.h"

// Nanite Integration
#include "Nanite/Nanite.h"
#include "Nanite/NaniteSceneProxy.h"
#include "Nanite/NaniteRayTracing.h"

// Virtual Shadow Maps
#include "VirtualShadowMaps/VirtualShadowMapArray.h"
#include "VirtualShadowMaps/VirtualShadowMapClipmap.h"

// Ray Tracing
#include "RayTracing/RayTracingDeclarations.h"
#include "RayTracing/RayTracingInstanceBufferUtil.h"
#include "RayTracing/RayTracingScene.h"

// Developer Settings
#include "Engine/DeveloperSettings.h"
#include "Engine/RendererSettings.h"

// Python Integration
#include "PythonScriptPlugin.h"

#include "AuracronLumenBridge.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronLumenBridge, Log, All);

// Forward Declarations
class UWorld;
class AActor;
class UActorComponent;
class UPrimitiveComponent;
class ULightComponent;
class UPostProcessVolume;
class UMaterialParameterCollection;
class UMaterialParameterCollectionInstance;
class FSceneView;
class FSceneViewFamily;
class FRDGBuilder;

/**
 * Lumen Quality Levels
 */
UENUM(BlueprintType)
enum class EAuracronLumenQuality : uint8
{
    Low         UMETA(DisplayName = "Low"),
    Medium      UMETA(DisplayName = "Medium"),
    High        UMETA(DisplayName = "High"),
    Epic        UMETA(DisplayName = "Epic"),
    Cinematic   UMETA(DisplayName = "Cinematic")
};

/**
 * Lumen Ray Tracing Mode
 */
UENUM(BlueprintType)
enum class EAuracronLumenRayTracingMode : uint8
{
    Software    UMETA(DisplayName = "Software Ray Tracing"),
    Hardware    UMETA(DisplayName = "Hardware Ray Tracing"),
    Hybrid      UMETA(DisplayName = "Hybrid Mode")
};

/**
 * Lumen Scene Representation Mode
 */
UENUM(BlueprintType)
enum class EAuracronLumenSceneMode : uint8
{
    SurfaceCache    UMETA(DisplayName = "Surface Cache"),
    GlobalSDF       UMETA(DisplayName = "Global SDF"),
    MeshCards       UMETA(DisplayName = "Mesh Cards"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

/**
 * Lumen Reflection Quality
 */
UENUM(BlueprintType)
enum class EAuracronLumenReflectionQuality : uint8
{
    Disabled    UMETA(DisplayName = "Disabled"),
    Low         UMETA(DisplayName = "Low"),
    Medium      UMETA(DisplayName = "Medium"),
    High        UMETA(DisplayName = "High"),
    Epic        UMETA(DisplayName = "Epic")
};

/**
 * Lumen Global Illumination State
 */
UENUM(BlueprintType)
enum class EAuracronLumenGIState : uint8
{
    Disabled        UMETA(DisplayName = "Disabled"),
    Initializing    UMETA(DisplayName = "Initializing"),
    Active          UMETA(DisplayName = "Active"),
    Updating        UMETA(DisplayName = "Updating"),
    Error           UMETA(DisplayName = "Error")
};

/**
 * Lumen Performance Metrics Structure
 */
USTRUCT(BlueprintType)
struct AURACRONLUMENBRIDGE_API FAuracronLumenMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float FrameTime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float GITime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float ReflectionTime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float SurfaceCacheTime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 SurfaceCacheMemoryMB;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 RadianceCacheMemoryMB;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ActiveProbeCount;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float TemporalStability;

    FAuracronLumenMetrics()
        : FrameTime(0.0f)
        , GITime(0.0f)
        , ReflectionTime(0.0f)
        , SurfaceCacheTime(0.0f)
        , SurfaceCacheMemoryMB(0)
        , RadianceCacheMemoryMB(0)
        , ActiveProbeCount(0)
        , TemporalStability(1.0f)
    {
    }
};

/**
 * Lumen Quality Settings Structure
 */
USTRUCT(BlueprintType)
struct AURACRONLUMENBRIDGE_API FAuracronLumenQualitySettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronLumenQuality GlobalIlluminationQuality;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronLumenReflectionQuality ReflectionQuality;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronLumenRayTracingMode RayTracingMode;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronLumenSceneMode SceneRepresentation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (ClampMin = "0.1", ClampMax = "4.0"))
    float ResolutionScale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (ClampMin = "1", ClampMax = "8"))
    int32 TracingStepCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bEnableTemporalUpsampling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bEnableDenoising;

    FAuracronLumenQualitySettings()
        : GlobalIlluminationQuality(EAuracronLumenQuality::Medium)
        , ReflectionQuality(EAuracronLumenReflectionQuality::Medium)
        , RayTracingMode(EAuracronLumenRayTracingMode::Software)
        , SceneRepresentation(EAuracronLumenSceneMode::SurfaceCache)
        , ResolutionScale(1.0f)
        , TracingStepCount(4)
        , bEnableTemporalUpsampling(true)
        , bEnableDenoising(true)
    {
    }
};

/**
 * Lumen Lighting Scenario Structure
 */
USTRUCT(BlueprintType)
struct AURACRONLUMENBRIDGE_API FAuracronLumenLightingScenario
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    FString ScenarioName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    FLinearColor SkyLightColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float SkyLightIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    FLinearColor DirectionalLightColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float DirectionalLightIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    FRotator DirectionalLightRotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float GlobalIlluminationIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float ReflectionIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    bool bEnableVolumetricFog;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scenario")
    FLinearColor VolumetricFogColor;

    FAuracronLumenLightingScenario()
        : ScenarioName(TEXT("Default"))
        , SkyLightColor(FLinearColor::White)
        , SkyLightIntensity(1.0f)
        , DirectionalLightColor(FLinearColor::White)
        , DirectionalLightIntensity(3.0f)
        , DirectionalLightRotation(FRotator(-45.0f, 0.0f, 0.0f))
        , GlobalIlluminationIntensity(1.0f)
        , ReflectionIntensity(1.0f)
        , bEnableVolumetricFog(false)
        , VolumetricFogColor(FLinearColor(0.5f, 0.5f, 0.5f, 1.0f))
    {
    }
};

// Delegate Declarations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLumenStateChanged, EAuracronLumenGIState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLumenQualityChanged, const FAuracronLumenQualitySettings&, NewSettings);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLumenMetricsUpdated, const FAuracronLumenMetrics&, Metrics);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLumenScenarioChanged, const FString&, ScenarioName, bool, bSuccess);

/**
 * Main API class for Lumen Bridge
 * Exposes UE5.6 Lumen Global Illumination functionality to Python
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONLUMENBRIDGE_API UAuracronLumenBridgeAPI : public UObject
{
    GENERATED_BODY()

public:
    UAuracronLumenBridgeAPI();

    // === CORE LUMEN SYSTEM ===
    
    /**
     * Initialize Lumen system for the current world
     * @param World Target world
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    bool InitializeLumenSystem(UWorld* World);

    /**
     * Shutdown Lumen system
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    bool ShutdownLumenSystem();

    /**
     * Check if Lumen is supported on current platform
     * @return True if supported
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    bool IsLumenSupported() const;

    /**
     * Check if Hardware Ray Tracing is available
     * @return True if available
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    bool IsHardwareRayTracingAvailable() const;

    /**
     * Get current Lumen state
     * @return Current state
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    EAuracronLumenGIState GetLumenState() const;

    /**
     * Force Lumen scene update
     * @param bFullUpdate True for full scene rebuild
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Core", CallInEditor)
    bool UpdateLumenScene(bool bFullUpdate = false);

    // === QUALITY SETTINGS ===
    
    /**
     * Set Lumen quality settings
     * @param QualitySettings New quality settings
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Quality", CallInEditor)
    bool SetLumenQualitySettings(const FAuracronLumenQualitySettings& QualitySettings);

    /**
     * Get current Lumen quality settings
     * @return Current quality settings
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Quality", CallInEditor)
    FAuracronLumenQualitySettings GetLumenQualitySettings() const;

    /**
     * Apply quality preset
     * @param Quality Quality level to apply
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Quality", CallInEditor)
    bool ApplyQualityPreset(EAuracronLumenQuality Quality);

    /**
     * Set Global Illumination intensity
     * @param Intensity GI intensity multiplier
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Quality", CallInEditor)
    bool SetGlobalIlluminationIntensity(float Intensity);

    /**
     * Set Reflection intensity
     * @param Intensity Reflection intensity multiplier
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Quality", CallInEditor)
    bool SetReflectionIntensity(float Intensity);

    // === LIGHTING SCENARIOS ===
    
    /**
     * Create lighting scenario
     * @param Scenario Scenario configuration
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Scenarios", CallInEditor)
    bool CreateLightingScenario(const FAuracronLumenLightingScenario& Scenario);

    /**
     * Apply lighting scenario
     * @param ScenarioName Name of scenario to apply
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Scenarios", CallInEditor)
    bool ApplyLightingScenario(const FString& ScenarioName);

    /**
     * Get all available lighting scenarios
     * @return Array of scenario names
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Scenarios", CallInEditor)
    TArray<FString> GetAvailableLightingScenarios() const;

    /**
     * Remove lighting scenario
     * @param ScenarioName Name of scenario to remove
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Scenarios", CallInEditor)
    bool RemoveLightingScenario(const FString& ScenarioName);

    // === PERFORMANCE MONITORING ===
    
    /**
     * Get current Lumen performance metrics
     * @return Performance metrics
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Performance", CallInEditor)
    FAuracronLumenMetrics GetLumenMetrics() const;

    /**
     * Enable performance monitoring
     * @param bEnable True to enable monitoring
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Performance", CallInEditor)
    bool EnablePerformanceMonitoring(bool bEnable);

    /**
     * Get Surface Cache memory usage in MB
     * @return Memory usage in megabytes
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Performance", CallInEditor)
    int32 GetSurfaceCacheMemoryUsage() const;

    /**
     * Get Radiance Cache memory usage in MB
     * @return Memory usage in megabytes
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Performance", CallInEditor)
    int32 GetRadianceCacheMemoryUsage() const;

    // === UE 5.6 ADVANCED FEATURES ===

    /**
     * Enable/Disable Lumen Hardware Ray Tracing
     * @param bEnable True to enable hardware ray tracing
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool SetHardwareRayTracingEnabled(bool bEnable);

    /**
     * Configure Lumen Surface Cache settings
     * @param Resolution Surface cache resolution
     * @param UpdateFrequency Update frequency in frames
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool ConfigureSurfaceCache(int32 Resolution = 1024, int32 UpdateFrequency = 4);

    /**
     * Configure Lumen Radiance Cache settings
     * @param ProbeSpacing Spacing between radiance probes
     * @param ProbeResolution Resolution of each probe
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool ConfigureRadianceCache(float ProbeSpacing = 100.0f, int32 ProbeResolution = 32);

    /**
     * Enable/Disable Lumen Screen Probe Gather
     * @param bEnable True to enable screen probe gather
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool SetScreenProbeGatherEnabled(bool bEnable);

    /**
     * Configure Lumen Translucency settings
     * @param VolumeResolution Volume resolution for translucency
     * @param ReflectionSamples Number of reflection samples
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool ConfigureTranslucency(int32 VolumeResolution = 64, int32 ReflectionSamples = 4);

    /**
     * Set Lumen Scene View Distance
     * @param ViewDistance Maximum view distance for Lumen
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool SetLumenSceneViewDistance(float ViewDistance);

    /**
     * Configure Lumen Final Gather settings
     * @param ImportanceSampleCount Number of importance samples
     * @param DownsampleFactor Downsample factor for final gather
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool ConfigureFinalGather(int32 ImportanceSampleCount = 16, float DownsampleFactor = 2.0f);

    /**
     * Enable/Disable Lumen Two Sided Foliage
     * @param bEnable True to enable two sided foliage
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool SetTwoSidedFoliageEnabled(bool bEnable);

    /**
     * Configure Lumen Mesh Cards settings
     * @param MeshCardsMaxLOD Maximum LOD for mesh cards
     * @param MeshCardsResolution Resolution for mesh cards
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool ConfigureMeshCards(int32 MeshCardsMaxLOD = 3, int32 MeshCardsResolution = 512);

    /**
     * Force Lumen Scene Capture update
     * @param bFullCapture True for full scene capture
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    bool UpdateLumenSceneCapture(bool bFullCapture = false);

    /**
     * Get detailed Lumen performance statistics
     * @return Detailed performance statistics
     */
    UFUNCTION(BlueprintCallable, Category = "Lumen Bridge|Advanced", CallInEditor)
    FAuracronLumenMetrics GetDetailedLumenStatistics() const;

    // === DELEGATES ===
    
    UPROPERTY(BlueprintAssignable, Category = "Lumen Bridge|Events")
    FOnLumenStateChanged OnLumenStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Lumen Bridge|Events")
    FOnLumenQualityChanged OnLumenQualityChanged;

    UPROPERTY(BlueprintAssignable, Category = "Lumen Bridge|Events")
    FOnLumenMetricsUpdated OnLumenMetricsUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Lumen Bridge|Events")
    FOnLumenScenarioChanged OnLumenScenarioChanged;

private:
    /** Current Lumen state */
    EAuracronLumenGIState CurrentState;

    /** Current quality settings */
    FAuracronLumenQualitySettings CurrentQualitySettings;

    /** Available lighting scenarios */
    TMap<FString, FAuracronLumenLightingScenario> LightingScenarios;

    /** Performance monitoring enabled */
    bool bPerformanceMonitoringEnabled;

    /** Current world reference */
    UPROPERTY()
    UWorld* CurrentWorld;

    /** Performance metrics cache */
    FAuracronLumenMetrics CachedMetrics;

    /** Last metrics update time */
    double LastMetricsUpdateTime;

    /** Metrics update interval in seconds */
    static constexpr double MetricsUpdateInterval = 0.1;

    // Internal helper functions
    void UpdatePerformanceMetrics();
    void BroadcastStateChange(EAuracronLumenGIState NewState);
    void BroadcastQualityChange(const FAuracronLumenQualitySettings& NewSettings);
    void BroadcastMetricsUpdate(const FAuracronLumenMetrics& Metrics);
    void BroadcastScenarioChange(const FString& ScenarioName, bool bSuccess);
    
    bool ValidateWorld(UWorld* World) const;
    bool ApplyQualitySettingsInternal(const FAuracronLumenQualitySettings& Settings);
    bool ApplyLightingScenarioInternal(const FAuracronLumenLightingScenario& Scenario);
    
    void InitializeDefaultScenarios();
    void CleanupLumenResources();
    
    // Logging helpers
    void LogInfo(const FString& Message) const;
    void LogWarning(const FString& Message) const;
    void LogError(const FString& Message) const;
};

/**
 * Module interface for Lumen Bridge
 */
class AURACRONLUMENBRIDGE_API FAuracronLumenBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /**
     * Singleton-like access to this module's interface. This is just for convenience!
     * Beware of calling this during the shutdown phase, though. Your module might have been unloaded already.
     *
     * @return Returns singleton instance, loading the module on demand if needed
     */
    static inline FAuracronLumenBridgeModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FAuracronLumenBridgeModule>("AuracronLumenBridge");
    }

    /**
     * Checks to see if this module is loaded and ready. It is only valid to call Get() if IsAvailable() returns true.
     *
     * @return True if the module is loaded and ready to use
     */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronLumenBridge");
    }

    /**
     * Get the Lumen Bridge API instance
     * @return Pointer to the API instance
     */
    UAuracronLumenBridgeAPI* GetLumenBridgeAPI() const;

private:
    /** Pointer to the Lumen Bridge API instance */
    UPROPERTY()
    UAuracronLumenBridgeAPI* LumenBridgeAPI;

    /** Handle to the test command UI extension */
    TSharedPtr<class FUICommandList> PluginCommands;
};

