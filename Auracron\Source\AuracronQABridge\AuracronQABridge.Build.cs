using UnrealBuildTool;
public class AuracronQABridge : ModuleRules
{
    public AuracronQABridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "AutomationController",
            "AutomationMessages",
            "AutomationTest",
            "AutomationWorker",
            "FunctionalTesting",
            "ScreenShotComparisonTools",
            "ImageWrapper",
            "RenderCore",
            "RHI","Slate",
            "SlateCore",
            "UMG",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin"
        });
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "Json",
            "Json","ApplicationCore",
            "InputCore",
            "DesktopPlatform","RenderCore",
            "RHI","ImageWrapper",
            "AutomationController",
            "AutomationMessages",
            "AutomationTest",
            "AutomationWorker",
            "FunctionalTesting",
            "ScreenShotComparisonTools",
            "SessionServices","LauncherServices",
            "GameplayDebugger",
            "EngineSettings",
            "DeveloperSettings"
        });
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Private"
        });
    }
}
