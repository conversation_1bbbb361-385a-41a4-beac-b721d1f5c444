// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageProcedural.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageProcedural() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FSphere();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ALandscape_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ULandscapeLayerInfoObject_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronDensityMapType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDensityMapType;
static UEnum* EAuracronDensityMapType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDensityMapType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDensityMapType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronDensityMapType"));
	}
	return Z_Registration_Info_UEnum_EAuracronDensityMapType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronDensityMapType>()
{
	return EAuracronDensityMapType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Density map types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronDensityMapType::Custom" },
		{ "Landscape.DisplayName", "Landscape Based" },
		{ "Landscape.Name", "EAuracronDensityMapType::Landscape" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
		{ "Noise.DisplayName", "Noise Based" },
		{ "Noise.Name", "EAuracronDensityMapType::Noise" },
		{ "Spline.DisplayName", "Spline Based" },
		{ "Spline.Name", "EAuracronDensityMapType::Spline" },
		{ "Texture.DisplayName", "Texture Based" },
		{ "Texture.Name", "EAuracronDensityMapType::Texture" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Density map types" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronDensityMapType::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDensityMapType::Uniform", (int64)EAuracronDensityMapType::Uniform },
		{ "EAuracronDensityMapType::Texture", (int64)EAuracronDensityMapType::Texture },
		{ "EAuracronDensityMapType::Noise", (int64)EAuracronDensityMapType::Noise },
		{ "EAuracronDensityMapType::Landscape", (int64)EAuracronDensityMapType::Landscape },
		{ "EAuracronDensityMapType::Spline", (int64)EAuracronDensityMapType::Spline },
		{ "EAuracronDensityMapType::Custom", (int64)EAuracronDensityMapType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronDensityMapType",
	"EAuracronDensityMapType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType()
{
	if (!Z_Registration_Info_UEnum_EAuracronDensityMapType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDensityMapType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDensityMapType.InnerSingleton;
}
// ********** End Enum EAuracronDensityMapType *****************************************************

// ********** Begin Enum EAuracronSlopeFilterMode **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSlopeFilterMode;
static UEnum* EAuracronSlopeFilterMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronSlopeFilterMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSlopeFilterMode>()
{
	return EAuracronSlopeFilterMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronSlopeFilterMode::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Slope filtering modes\n" },
#endif
		{ "Curve.DisplayName", "Curve Based" },
		{ "Curve.Name", "EAuracronSlopeFilterMode::Curve" },
		{ "Layered.DisplayName", "Layered" },
		{ "Layered.Name", "EAuracronSlopeFilterMode::Layered" },
		{ "MinMax.DisplayName", "Min Max Range" },
		{ "MinMax.Name", "EAuracronSlopeFilterMode::MinMax" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronSlopeFilterMode::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slope filtering modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSlopeFilterMode::None", (int64)EAuracronSlopeFilterMode::None },
		{ "EAuracronSlopeFilterMode::MinMax", (int64)EAuracronSlopeFilterMode::MinMax },
		{ "EAuracronSlopeFilterMode::Curve", (int64)EAuracronSlopeFilterMode::Curve },
		{ "EAuracronSlopeFilterMode::Adaptive", (int64)EAuracronSlopeFilterMode::Adaptive },
		{ "EAuracronSlopeFilterMode::Layered", (int64)EAuracronSlopeFilterMode::Layered },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronSlopeFilterMode",
	"EAuracronSlopeFilterMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSlopeFilterMode.InnerSingleton;
}
// ********** End Enum EAuracronSlopeFilterMode ****************************************************

// ********** Begin Enum EAuracronHeightConstraintMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronHeightConstraintMode;
static UEnum* EAuracronHeightConstraintMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronHeightConstraintMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronHeightConstraintMode>()
{
	return EAuracronHeightConstraintMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Absolute.DisplayName", "Absolute Height" },
		{ "Absolute.Name", "EAuracronHeightConstraintMode::Absolute" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height constraint modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronHeightConstraintMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronHeightConstraintMode::None" },
		{ "Relative.DisplayName", "Relative Height" },
		{ "Relative.Name", "EAuracronHeightConstraintMode::Relative" },
		{ "SeaLevel.DisplayName", "Sea Level Based" },
		{ "SeaLevel.Name", "EAuracronHeightConstraintMode::SeaLevel" },
		{ "Terrain.DisplayName", "Terrain Based" },
		{ "Terrain.Name", "EAuracronHeightConstraintMode::Terrain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height constraint modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronHeightConstraintMode::None", (int64)EAuracronHeightConstraintMode::None },
		{ "EAuracronHeightConstraintMode::Absolute", (int64)EAuracronHeightConstraintMode::Absolute },
		{ "EAuracronHeightConstraintMode::Relative", (int64)EAuracronHeightConstraintMode::Relative },
		{ "EAuracronHeightConstraintMode::SeaLevel", (int64)EAuracronHeightConstraintMode::SeaLevel },
		{ "EAuracronHeightConstraintMode::Terrain", (int64)EAuracronHeightConstraintMode::Terrain },
		{ "EAuracronHeightConstraintMode::Custom", (int64)EAuracronHeightConstraintMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronHeightConstraintMode",
	"EAuracronHeightConstraintMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronHeightConstraintMode.InnerSingleton;
}
// ********** End Enum EAuracronHeightConstraintMode ***********************************************

// ********** Begin Enum EAuracronNoiseDistributionType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronNoiseDistributionType;
static UEnum* EAuracronNoiseDistributionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronNoiseDistributionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronNoiseDistributionType>()
{
	return EAuracronNoiseDistributionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise distribution types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronNoiseDistributionType::Custom" },
		{ "Fractal.DisplayName", "Fractal" },
		{ "Fractal.Name", "EAuracronNoiseDistributionType::Fractal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
		{ "Perlin.DisplayName", "Perlin" },
		{ "Perlin.Name", "EAuracronNoiseDistributionType::Perlin" },
		{ "Ridged.DisplayName", "Ridged" },
		{ "Ridged.Name", "EAuracronNoiseDistributionType::Ridged" },
		{ "Simplex.DisplayName", "Simplex" },
		{ "Simplex.Name", "EAuracronNoiseDistributionType::Simplex" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise distribution types" },
#endif
		{ "Voronoi.DisplayName", "Voronoi" },
		{ "Voronoi.Name", "EAuracronNoiseDistributionType::Voronoi" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronNoiseDistributionType::Perlin", (int64)EAuracronNoiseDistributionType::Perlin },
		{ "EAuracronNoiseDistributionType::Simplex", (int64)EAuracronNoiseDistributionType::Simplex },
		{ "EAuracronNoiseDistributionType::Ridged", (int64)EAuracronNoiseDistributionType::Ridged },
		{ "EAuracronNoiseDistributionType::Voronoi", (int64)EAuracronNoiseDistributionType::Voronoi },
		{ "EAuracronNoiseDistributionType::Fractal", (int64)EAuracronNoiseDistributionType::Fractal },
		{ "EAuracronNoiseDistributionType::Custom", (int64)EAuracronNoiseDistributionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronNoiseDistributionType",
	"EAuracronNoiseDistributionType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronNoiseDistributionType.InnerSingleton;
}
// ********** End Enum EAuracronNoiseDistributionType **********************************************

// ********** Begin Enum EAuracronPlacementRuleType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPlacementRuleType;
static UEnum* EAuracronPlacementRuleType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlacementRuleType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPlacementRuleType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronPlacementRuleType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPlacementRuleType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlacementRuleType>()
{
	return EAuracronPlacementRuleType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Biome.DisplayName", "Biome Based" },
		{ "Biome.Name", "EAuracronPlacementRuleType::Biome" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rule-based placement types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPlacementRuleType::Custom" },
		{ "Density.DisplayName", "Density Based" },
		{ "Density.Name", "EAuracronPlacementRuleType::Density" },
		{ "Distance.DisplayName", "Distance Based" },
		{ "Distance.Name", "EAuracronPlacementRuleType::Distance" },
		{ "Exclusion.DisplayName", "Exclusion Based" },
		{ "Exclusion.Name", "EAuracronPlacementRuleType::Exclusion" },
		{ "Material.DisplayName", "Material Based" },
		{ "Material.Name", "EAuracronPlacementRuleType::Material" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rule-based placement types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPlacementRuleType::Distance", (int64)EAuracronPlacementRuleType::Distance },
		{ "EAuracronPlacementRuleType::Density", (int64)EAuracronPlacementRuleType::Density },
		{ "EAuracronPlacementRuleType::Biome", (int64)EAuracronPlacementRuleType::Biome },
		{ "EAuracronPlacementRuleType::Material", (int64)EAuracronPlacementRuleType::Material },
		{ "EAuracronPlacementRuleType::Exclusion", (int64)EAuracronPlacementRuleType::Exclusion },
		{ "EAuracronPlacementRuleType::Custom", (int64)EAuracronPlacementRuleType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronPlacementRuleType",
	"EAuracronPlacementRuleType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlacementRuleType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPlacementRuleType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPlacementRuleType.InnerSingleton;
}
// ********** End Enum EAuracronPlacementRuleType **************************************************

// ********** Begin Enum EAuracronPlacementPriority ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPlacementPriority;
static UEnum* EAuracronPlacementPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlacementPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPlacementPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronPlacementPriority"));
	}
	return Z_Registration_Info_UEnum_EAuracronPlacementPriority.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPlacementPriority>()
{
	return EAuracronPlacementPriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Placement priority modes\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAuracronPlacementPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronPlacementPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronPlacementPriority::Low" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAuracronPlacementPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement priority modes" },
#endif
		{ "VeryHigh.DisplayName", "Very High" },
		{ "VeryHigh.Name", "EAuracronPlacementPriority::VeryHigh" },
		{ "VeryLow.DisplayName", "Very Low" },
		{ "VeryLow.Name", "EAuracronPlacementPriority::VeryLow" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPlacementPriority::VeryLow", (int64)EAuracronPlacementPriority::VeryLow },
		{ "EAuracronPlacementPriority::Low", (int64)EAuracronPlacementPriority::Low },
		{ "EAuracronPlacementPriority::Normal", (int64)EAuracronPlacementPriority::Normal },
		{ "EAuracronPlacementPriority::High", (int64)EAuracronPlacementPriority::High },
		{ "EAuracronPlacementPriority::VeryHigh", (int64)EAuracronPlacementPriority::VeryHigh },
		{ "EAuracronPlacementPriority::Critical", (int64)EAuracronPlacementPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronPlacementPriority",
	"EAuracronPlacementPriority",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority()
{
	if (!Z_Registration_Info_UEnum_EAuracronPlacementPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPlacementPriority.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPlacementPriority.InnerSingleton;
}
// ********** End Enum EAuracronPlacementPriority **************************************************

// ********** Begin ScriptStruct FAuracronDensityMapConfiguration **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration;
class UScriptStruct* FAuracronDensityMapConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronDensityMapConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Density Map Configuration\n * Configuration for density-based procedural placement\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Density Map Configuration\nConfiguration for density-based procedural placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityMapType_MetaData[] = {
		{ "Category", "Density Map" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDensity_MetaData[] = {
		{ "Category", "Density Map" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityMultiplier_MetaData[] = {
		{ "Category", "Density Map" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityRange_MetaData[] = {
		{ "Category", "Density Map" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityTexture_MetaData[] = {
		{ "Category", "Texture" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureScale_MetaData[] = {
		{ "Category", "Texture" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureOffset_MetaData[] = {
		{ "Category", "Texture" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTextureAlpha_MetaData[] = {
		{ "Category", "Texture" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseLacunarity_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseGain_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseSeed_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLandscape_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeLayers_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerWeights_MetaData[] = {
		{ "Category", "Landscape" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InfluenceSplines_MetaData[] = {
		{ "Category", "Spline" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineInfluenceRadius_MetaData[] = {
		{ "Category", "Spline" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineInfluenceStrength_MetaData[] = {
		{ "Category", "Spline" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSplineInvertInfluence_MetaData[] = {
		{ "Category", "Spline" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DensityMapType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DensityMapType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityRange;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DensityTexture;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureOffset;
	static void NewProp_bUseTextureAlpha_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTextureAlpha;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseLacunarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseGain;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseSeed;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TargetLandscape;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LandscapeLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LandscapeLayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerWeights;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InfluenceSplines_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InfluenceSplines;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SplineInfluenceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SplineInfluenceStrength;
	static void NewProp_bSplineInvertInfluence_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSplineInvertInfluence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDensityMapConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMapType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMapType = { "DensityMapType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, DensityMapType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronDensityMapType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityMapType_MetaData), NewProp_DensityMapType_MetaData) }; // 454692608
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_BaseDensity = { "BaseDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, BaseDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDensity_MetaData), NewProp_BaseDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMultiplier = { "DensityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, DensityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityMultiplier_MetaData), NewProp_DensityMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityRange = { "DensityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, DensityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityRange_MetaData), NewProp_DensityRange_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityTexture = { "DensityTexture", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, DensityTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityTexture_MetaData), NewProp_DensityTexture_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TextureScale = { "TextureScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, TextureScale), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureScale_MetaData), NewProp_TextureScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TextureOffset = { "TextureOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, TextureOffset), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureOffset_MetaData), NewProp_TextureOffset_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bUseTextureAlpha_SetBit(void* Obj)
{
	((FAuracronDensityMapConfiguration*)Obj)->bUseTextureAlpha = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bUseTextureAlpha = { "bUseTextureAlpha", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDensityMapConfiguration), &Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bUseTextureAlpha_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTextureAlpha_MetaData), NewProp_bUseTextureAlpha_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronNoiseDistributionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 822148285
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseLacunarity = { "NoiseLacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseLacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseLacunarity_MetaData), NewProp_NoiseLacunarity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseGain = { "NoiseGain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseGain), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseGain_MetaData), NewProp_NoiseGain_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseSeed = { "NoiseSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, NoiseSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseSeed_MetaData), NewProp_NoiseSeed_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TargetLandscape = { "TargetLandscape", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, TargetLandscape), Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLandscape_MetaData), NewProp_TargetLandscape_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LandscapeLayers_Inner = { "LandscapeLayers", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ULandscapeLayerInfoObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LandscapeLayers = { "LandscapeLayers", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, LandscapeLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeLayers_MetaData), NewProp_LandscapeLayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LayerWeights_Inner = { "LayerWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LayerWeights = { "LayerWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, LayerWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerWeights_MetaData), NewProp_LayerWeights_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_InfluenceSplines_Inner = { "InfluenceSplines", nullptr, (EPropertyFlags)0x0004000000080008, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_InfluenceSplines = { "InfluenceSplines", nullptr, (EPropertyFlags)0x001400800000000d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, InfluenceSplines), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InfluenceSplines_MetaData), NewProp_InfluenceSplines_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_SplineInfluenceRadius = { "SplineInfluenceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, SplineInfluenceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineInfluenceRadius_MetaData), NewProp_SplineInfluenceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_SplineInfluenceStrength = { "SplineInfluenceStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDensityMapConfiguration, SplineInfluenceStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineInfluenceStrength_MetaData), NewProp_SplineInfluenceStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bSplineInvertInfluence_SetBit(void* Obj)
{
	((FAuracronDensityMapConfiguration*)Obj)->bSplineInvertInfluence = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bSplineInvertInfluence = { "bSplineInvertInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDensityMapConfiguration), &Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bSplineInvertInfluence_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSplineInvertInfluence_MetaData), NewProp_bSplineInvertInfluence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMapType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMapType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_BaseDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_DensityTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TextureScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TextureOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bUseTextureAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseLacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseGain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_NoiseSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_TargetLandscape,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LandscapeLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LandscapeLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LayerWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_LayerWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_InfluenceSplines_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_InfluenceSplines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_SplineInfluenceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_SplineInfluenceStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewProp_bSplineInvertInfluence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronDensityMapConfiguration",
	Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::PropPointers),
	sizeof(FAuracronDensityMapConfiguration),
	alignof(FAuracronDensityMapConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDensityMapConfiguration ************************************

// ********** Begin ScriptStruct FAuracronSlopeFilterConfiguration *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration;
class UScriptStruct* FAuracronSlopeFilterConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSlopeFilterConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Slope Filter Configuration\n * Configuration for slope-based filtering\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slope Filter Configuration\nConfiguration for slope-based filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterMode_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSlopeFiltering_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSlopeAngle_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlopeAngle_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeFalloffRange_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeCurve_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertSlope_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWorldSpaceNormal_MetaData[] = {
		{ "Category", "Slope Filter" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveSlope_MetaData[] = {
		{ "Category", "Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveSlopeRadius_MetaData[] = {
		{ "Category", "Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveSlopeStrength_MetaData[] = {
		{ "Category", "Adaptive" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FilterMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FilterMode;
	static void NewProp_bEnableSlopeFiltering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSlopeFiltering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSlopeAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlopeAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SlopeFalloffRange;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SlopeCurve;
	static void NewProp_bInvertSlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertSlope;
	static void NewProp_bUseWorldSpaceNormal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWorldSpaceNormal;
	static void NewProp_bEnableAdaptiveSlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveSlopeRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptiveSlopeStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSlopeFilterConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_FilterMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_FilterMode = { "FilterMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, FilterMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSlopeFilterMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterMode_MetaData), NewProp_FilterMode_MetaData) }; // 316499568
void Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableSlopeFiltering_SetBit(void* Obj)
{
	((FAuracronSlopeFilterConfiguration*)Obj)->bEnableSlopeFiltering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableSlopeFiltering = { "bEnableSlopeFiltering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSlopeFilterConfiguration), &Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableSlopeFiltering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSlopeFiltering_MetaData), NewProp_bEnableSlopeFiltering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_MinSlopeAngle = { "MinSlopeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, MinSlopeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSlopeAngle_MetaData), NewProp_MinSlopeAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_MaxSlopeAngle = { "MaxSlopeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, MaxSlopeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlopeAngle_MetaData), NewProp_MaxSlopeAngle_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_SlopeFalloffRange = { "SlopeFalloffRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, SlopeFalloffRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeFalloffRange_MetaData), NewProp_SlopeFalloffRange_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_SlopeCurve = { "SlopeCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, SlopeCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeCurve_MetaData), NewProp_SlopeCurve_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bInvertSlope_SetBit(void* Obj)
{
	((FAuracronSlopeFilterConfiguration*)Obj)->bInvertSlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bInvertSlope = { "bInvertSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSlopeFilterConfiguration), &Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bInvertSlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertSlope_MetaData), NewProp_bInvertSlope_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bUseWorldSpaceNormal_SetBit(void* Obj)
{
	((FAuracronSlopeFilterConfiguration*)Obj)->bUseWorldSpaceNormal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bUseWorldSpaceNormal = { "bUseWorldSpaceNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSlopeFilterConfiguration), &Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bUseWorldSpaceNormal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWorldSpaceNormal_MetaData), NewProp_bUseWorldSpaceNormal_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableAdaptiveSlope_SetBit(void* Obj)
{
	((FAuracronSlopeFilterConfiguration*)Obj)->bEnableAdaptiveSlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableAdaptiveSlope = { "bEnableAdaptiveSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSlopeFilterConfiguration), &Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableAdaptiveSlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveSlope_MetaData), NewProp_bEnableAdaptiveSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_AdaptiveSlopeRadius = { "AdaptiveSlopeRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, AdaptiveSlopeRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveSlopeRadius_MetaData), NewProp_AdaptiveSlopeRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_AdaptiveSlopeStrength = { "AdaptiveSlopeStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSlopeFilterConfiguration, AdaptiveSlopeStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveSlopeStrength_MetaData), NewProp_AdaptiveSlopeStrength_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_FilterMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_FilterMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableSlopeFiltering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_MinSlopeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_MaxSlopeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_SlopeFalloffRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_SlopeCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bInvertSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bUseWorldSpaceNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_bEnableAdaptiveSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_AdaptiveSlopeRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewProp_AdaptiveSlopeStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSlopeFilterConfiguration",
	Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::PropPointers),
	sizeof(FAuracronSlopeFilterConfiguration),
	alignof(FAuracronSlopeFilterConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSlopeFilterConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronHeightConstraintConfiguration ****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration;
class UScriptStruct* FAuracronHeightConstraintConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronHeightConstraintConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Height Constraint Configuration\n * Configuration for height-based constraints\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height Constraint Configuration\nConfiguration for height-based constraints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintMode_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHeightConstraints_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHeight_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHeight_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightFalloffRange_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeaLevel_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeHeightBase_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertHeight_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightCurve_MetaData[] = {
		{ "Category", "Height Constraint" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseTerrainRelativeHeight_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainSampleRadius_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ConstraintMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ConstraintMode;
	static void NewProp_bEnableHeightConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHeightConstraints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightFalloffRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeaLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RelativeHeightBase;
	static void NewProp_bInvertHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertHeight;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_HeightCurve;
	static void NewProp_bUseTerrainRelativeHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseTerrainRelativeHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainSampleRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronHeightConstraintConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_ConstraintMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_ConstraintMode = { "ConstraintMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, ConstraintMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronHeightConstraintMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintMode_MetaData), NewProp_ConstraintMode_MetaData) }; // 411645759
void Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bEnableHeightConstraints_SetBit(void* Obj)
{
	((FAuracronHeightConstraintConfiguration*)Obj)->bEnableHeightConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bEnableHeightConstraints = { "bEnableHeightConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHeightConstraintConfiguration), &Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bEnableHeightConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHeightConstraints_MetaData), NewProp_bEnableHeightConstraints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_MinHeight = { "MinHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, MinHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHeight_MetaData), NewProp_MinHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_MaxHeight = { "MaxHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, MaxHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHeight_MetaData), NewProp_MaxHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_HeightFalloffRange = { "HeightFalloffRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, HeightFalloffRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightFalloffRange_MetaData), NewProp_HeightFalloffRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_SeaLevel = { "SeaLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, SeaLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeaLevel_MetaData), NewProp_SeaLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_RelativeHeightBase = { "RelativeHeightBase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, RelativeHeightBase), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeHeightBase_MetaData), NewProp_RelativeHeightBase_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bInvertHeight_SetBit(void* Obj)
{
	((FAuracronHeightConstraintConfiguration*)Obj)->bInvertHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bInvertHeight = { "bInvertHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHeightConstraintConfiguration), &Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bInvertHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertHeight_MetaData), NewProp_bInvertHeight_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_HeightCurve = { "HeightCurve", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, HeightCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightCurve_MetaData), NewProp_HeightCurve_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bUseTerrainRelativeHeight_SetBit(void* Obj)
{
	((FAuracronHeightConstraintConfiguration*)Obj)->bUseTerrainRelativeHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bUseTerrainRelativeHeight = { "bUseTerrainRelativeHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHeightConstraintConfiguration), &Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bUseTerrainRelativeHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseTerrainRelativeHeight_MetaData), NewProp_bUseTerrainRelativeHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_TerrainSampleRadius = { "TerrainSampleRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHeightConstraintConfiguration, TerrainSampleRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainSampleRadius_MetaData), NewProp_TerrainSampleRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_ConstraintMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_ConstraintMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bEnableHeightConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_MinHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_MaxHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_HeightFalloffRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_SeaLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_RelativeHeightBase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bInvertHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_HeightCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_bUseTerrainRelativeHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewProp_TerrainSampleRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronHeightConstraintConfiguration",
	Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::PropPointers),
	sizeof(FAuracronHeightConstraintConfiguration),
	alignof(FAuracronHeightConstraintConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronHeightConstraintConfiguration ******************************

// ********** Begin ScriptStruct FAuracronPlacementRuleConfiguration *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration;
class UScriptStruct* FAuracronPlacementRuleConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronPlacementRuleConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Placement Rule Configuration\n * Configuration for rule-based placement\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement Rule Configuration\nConfiguration for rule-based placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleId_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleType_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnabled_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleWeight_MetaData[] = {
		{ "Category", "Placement Rule" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse3DDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetDensity_MetaData[] = {
		{ "Category", "Density" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityTolerance_MetaData[] = {
		{ "Category", "Density" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedBiomes_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExcludedBiomes_MetaData[] = {
		{ "Category", "Biome" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedMaterials_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExcludedMaterials_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionBoxes_MetaData[] = {
		{ "Category", "Exclusion" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionSpheres_MetaData[] = {
		{ "Category", "Exclusion" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionSplines_MetaData[] = {
		{ "Category", "Exclusion" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomParameters_MetaData[] = {
		{ "Category", "Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RuleType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RuleType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RuleWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDistance;
	static void NewProp_bUse3DDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse3DDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityTolerance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AllowedBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedBiomes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExcludedBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExcludedBiomes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AllowedMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedMaterials;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ExcludedMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExcludedMaterials;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExclusionBoxes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionBoxes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExclusionSpheres_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionSpheres;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ExclusionSplines_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionSplines;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPlacementRuleConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, RuleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleId_MetaData), NewProp_RuleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleType = { "RuleType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, RuleType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementRuleType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleType_MetaData), NewProp_RuleType_MetaData) }; // 1822528937
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, Priority), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPlacementPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 413917681
void Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((FAuracronPlacementRuleConfiguration*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPlacementRuleConfiguration), &Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnabled_MetaData), NewProp_bEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleWeight = { "RuleWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, RuleWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleWeight_MetaData), NewProp_RuleWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, MinDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistance_MetaData), NewProp_MinDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_MaxDistance = { "MaxDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, MaxDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDistance_MetaData), NewProp_MaxDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bUse3DDistance_SetBit(void* Obj)
{
	((FAuracronPlacementRuleConfiguration*)Obj)->bUse3DDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bUse3DDistance = { "bUse3DDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPlacementRuleConfiguration), &Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bUse3DDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse3DDistance_MetaData), NewProp_bUse3DDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_TargetDensity = { "TargetDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, TargetDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetDensity_MetaData), NewProp_TargetDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_DensityTolerance = { "DensityTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, DensityTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityTolerance_MetaData), NewProp_DensityTolerance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedBiomes_Inner = { "AllowedBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedBiomes = { "AllowedBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, AllowedBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedBiomes_MetaData), NewProp_AllowedBiomes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedBiomes_Inner = { "ExcludedBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedBiomes = { "ExcludedBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, ExcludedBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExcludedBiomes_MetaData), NewProp_ExcludedBiomes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedMaterials_Inner = { "AllowedMaterials", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedMaterials = { "AllowedMaterials", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, AllowedMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedMaterials_MetaData), NewProp_AllowedMaterials_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedMaterials_Inner = { "ExcludedMaterials", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedMaterials = { "ExcludedMaterials", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, ExcludedMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExcludedMaterials_MetaData), NewProp_ExcludedMaterials_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionBoxes_Inner = { "ExclusionBoxes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionBoxes = { "ExclusionBoxes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, ExclusionBoxes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionBoxes_MetaData), NewProp_ExclusionBoxes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSpheres_Inner = { "ExclusionSpheres", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSphere, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSpheres = { "ExclusionSpheres", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, ExclusionSpheres), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionSpheres_MetaData), NewProp_ExclusionSpheres_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSplines_Inner = { "ExclusionSplines", nullptr, (EPropertyFlags)0x0004000000080008, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSplines = { "ExclusionSplines", nullptr, (EPropertyFlags)0x001400800000000d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, ExclusionSplines), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionSplines_MetaData), NewProp_ExclusionSplines_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters_ValueProp = { "CustomParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters_Key_KeyProp = { "CustomParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters = { "CustomParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlacementRuleConfiguration, CustomParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomParameters_MetaData), NewProp_CustomParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_RuleWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_MaxDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_bUse3DDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_TargetDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_DensityTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_AllowedMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExcludedMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionBoxes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionBoxes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSpheres_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSpheres,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSplines_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_ExclusionSplines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewProp_CustomParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronPlacementRuleConfiguration",
	Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::PropPointers),
	sizeof(FAuracronPlacementRuleConfiguration),
	alignof(FAuracronPlacementRuleConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPlacementRuleConfiguration *********************************

// ********** Begin ScriptStruct FAuracronProceduralPlacementConfiguration *************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration;
class UScriptStruct* FAuracronProceduralPlacementConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronProceduralPlacementConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural Placement Configuration\n * Main configuration for procedural foliage placement\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural Placement Configuration\nMain configuration for procedural foliage placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProceduralPlacement_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDensityMaps_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSlopeFiltering_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHeightConstraints_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNoiseDistribution_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRuleBasedPlacement_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityMapConfig_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeFilterConfig_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightConstraintConfig_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementRules_MetaData[] = {
		{ "Category", "Procedural Placement" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPointsPerGeneration_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationRadius_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncGeneration_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODGeneration_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentGenerations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDensityMap_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowSlopeFilter_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowHeightConstraints_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPlacementRules_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableProceduralPlacement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProceduralPlacement;
	static void NewProp_bEnableDensityMaps_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDensityMaps;
	static void NewProp_bEnableSlopeFiltering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSlopeFiltering;
	static void NewProp_bEnableHeightConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHeightConstraints;
	static void NewProp_bEnableNoiseDistribution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNoiseDistribution;
	static void NewProp_bEnableRuleBasedPlacement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRuleBasedPlacement;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityMapConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlopeFilterConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightConstraintConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlacementRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlacementRules;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPointsPerGeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationRadius;
	static void NewProp_bEnableAsyncGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncGeneration;
	static void NewProp_bEnableLODGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODGeneration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentGenerations;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bShowDensityMap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDensityMap;
	static void NewProp_bShowSlopeFilter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowSlopeFilter;
	static void NewProp_bShowHeightConstraints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowHeightConstraints;
	static void NewProp_bShowPlacementRules_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPlacementRules;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralPlacementConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableProceduralPlacement_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableProceduralPlacement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableProceduralPlacement = { "bEnableProceduralPlacement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableProceduralPlacement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProceduralPlacement_MetaData), NewProp_bEnableProceduralPlacement_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDensityMaps_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableDensityMaps = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDensityMaps = { "bEnableDensityMaps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDensityMaps_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDensityMaps_MetaData), NewProp_bEnableDensityMaps_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableSlopeFiltering_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableSlopeFiltering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableSlopeFiltering = { "bEnableSlopeFiltering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableSlopeFiltering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSlopeFiltering_MetaData), NewProp_bEnableSlopeFiltering_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableHeightConstraints_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableHeightConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableHeightConstraints = { "bEnableHeightConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableHeightConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHeightConstraints_MetaData), NewProp_bEnableHeightConstraints_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableNoiseDistribution_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableNoiseDistribution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableNoiseDistribution = { "bEnableNoiseDistribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableNoiseDistribution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNoiseDistribution_MetaData), NewProp_bEnableNoiseDistribution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableRuleBasedPlacement_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableRuleBasedPlacement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableRuleBasedPlacement = { "bEnableRuleBasedPlacement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableRuleBasedPlacement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRuleBasedPlacement_MetaData), NewProp_bEnableRuleBasedPlacement_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_DensityMapConfig = { "DensityMapConfig", nullptr, (EPropertyFlags)0x0010008000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, DensityMapConfig), Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityMapConfig_MetaData), NewProp_DensityMapConfig_MetaData) }; // 3924111466
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_SlopeFilterConfig = { "SlopeFilterConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, SlopeFilterConfig), Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeFilterConfig_MetaData), NewProp_SlopeFilterConfig_MetaData) }; // 3727459798
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_HeightConstraintConfig = { "HeightConstraintConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, HeightConstraintConfig), Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightConstraintConfig_MetaData), NewProp_HeightConstraintConfig_MetaData) }; // 2904131641
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_PlacementRules_Inner = { "PlacementRules", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, METADATA_PARAMS(0, nullptr) }; // 1795639906
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_PlacementRules = { "PlacementRules", nullptr, (EPropertyFlags)0x0010008000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, PlacementRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementRules_MetaData), NewProp_PlacementRules_MetaData) }; // 1795639906
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_MaxPointsPerGeneration = { "MaxPointsPerGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, MaxPointsPerGeneration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPointsPerGeneration_MetaData), NewProp_MaxPointsPerGeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_GenerationRadius = { "GenerationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, GenerationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationRadius_MetaData), NewProp_GenerationRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableAsyncGeneration_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableAsyncGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableAsyncGeneration = { "bEnableAsyncGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableAsyncGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncGeneration_MetaData), NewProp_bEnableAsyncGeneration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableLODGeneration_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableLODGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableLODGeneration = { "bEnableLODGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableLODGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODGeneration_MetaData), NewProp_bEnableLODGeneration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_MaxConcurrentGenerations = { "MaxConcurrentGenerations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementConfiguration, MaxConcurrentGenerations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentGenerations_MetaData), NewProp_MaxConcurrentGenerations_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowDensityMap_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bShowDensityMap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowDensityMap = { "bShowDensityMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowDensityMap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDensityMap_MetaData), NewProp_bShowDensityMap_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowSlopeFilter_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bShowSlopeFilter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowSlopeFilter = { "bShowSlopeFilter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowSlopeFilter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowSlopeFilter_MetaData), NewProp_bShowSlopeFilter_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowHeightConstraints_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bShowHeightConstraints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowHeightConstraints = { "bShowHeightConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowHeightConstraints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowHeightConstraints_MetaData), NewProp_bShowHeightConstraints_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowPlacementRules_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementConfiguration*)Obj)->bShowPlacementRules = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowPlacementRules = { "bShowPlacementRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementConfiguration), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowPlacementRules_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPlacementRules_MetaData), NewProp_bShowPlacementRules_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableProceduralPlacement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDensityMaps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableSlopeFiltering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableHeightConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableNoiseDistribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableRuleBasedPlacement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_DensityMapConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_SlopeFilterConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_HeightConstraintConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_PlacementRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_PlacementRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_MaxPointsPerGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_GenerationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableAsyncGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableLODGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_MaxConcurrentGenerations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowDensityMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowSlopeFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowHeightConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewProp_bShowPlacementRules,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronProceduralPlacementConfiguration",
	Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::PropPointers),
	sizeof(FAuracronProceduralPlacementConfiguration),
	alignof(FAuracronProceduralPlacementConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralPlacementConfiguration ***************************

// ********** Begin ScriptStruct FAuracronProceduralPlacementResult ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult;
class UScriptStruct* FAuracronProceduralPlacementResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronProceduralPlacementResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural Placement Result\n * Result data from procedural placement generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural Placement Result\nResult data from procedural placement generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationId_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedPointsCount_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilteredPointsCount_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationBounds_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacedTransforms_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityValues_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeValues_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightValues_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_DateTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GeneratedPointsCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FilteredPointsCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlacedTransforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlacedTransforms;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DensityValues;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SlopeValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SlopeValues;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HeightValues;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime_DateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralPlacementResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, GenerationId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationId_MetaData), NewProp_GenerationId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronProceduralPlacementResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralPlacementResult), &Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GeneratedPointsCount = { "GeneratedPointsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, GeneratedPointsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedPointsCount_MetaData), NewProp_GeneratedPointsCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_FilteredPointsCount = { "FilteredPointsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, FilteredPointsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilteredPointsCount_MetaData), NewProp_FilteredPointsCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, GenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationBounds = { "GenerationBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, GenerationBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationBounds_MetaData), NewProp_GenerationBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_PlacedTransforms_Inner = { "PlacedTransforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_PlacedTransforms = { "PlacedTransforms", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, PlacedTransforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacedTransforms_MetaData), NewProp_PlacedTransforms_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_DensityValues_Inner = { "DensityValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_DensityValues = { "DensityValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, DensityValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityValues_MetaData), NewProp_DensityValues_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_SlopeValues_Inner = { "SlopeValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_SlopeValues = { "SlopeValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, SlopeValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeValues_MetaData), NewProp_SlopeValues_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_HeightValues_Inner = { "HeightValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_HeightValues = { "HeightValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, HeightValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightValues_MetaData), NewProp_HeightValues_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationTime_DateTime = { "GenerationTime_DateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralPlacementResult, GenerationTime_DateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_DateTime_MetaData), NewProp_GenerationTime_DateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GeneratedPointsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_FilteredPointsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_PlacedTransforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_PlacedTransforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_DensityValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_DensityValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_SlopeValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_SlopeValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_HeightValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_HeightValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewProp_GenerationTime_DateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronProceduralPlacementResult",
	Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::PropPointers),
	sizeof(FAuracronProceduralPlacementResult),
	alignof(FAuracronProceduralPlacementResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralPlacementResult **********************************

// ********** Begin Delegate FOnProceduralGenerationCompleted **************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics
{
	struct AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms
	{
		FString GenerationId;
		FAuracronProceduralPlacementResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_GenerationId = { "GenerationId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms, GenerationId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms, Result), Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult, METADATA_PARAMS(0, nullptr) }; // 1676447307
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_GenerationId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "OnProceduralGenerationCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageProceduralManager::FOnProceduralGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnProceduralGenerationCompleted, const FString& GenerationId, FAuracronProceduralPlacementResult Result)
{
	struct AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms
	{
		FString GenerationId;
		FAuracronProceduralPlacementResult Result;
	};
	AuracronFoliageProceduralManager_eventOnProceduralGenerationCompleted_Parms Parms;
	Parms.GenerationId=GenerationId;
	Parms.Result=Result;
	OnProceduralGenerationCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnProceduralGenerationCompleted ****************************************

// ********** Begin Delegate FOnDensityMapUpdated **************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics
{
	struct AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms
	{
		FBox UpdatedArea;
		float AverageDensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_UpdatedArea;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageDensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::NewProp_UpdatedArea = { "UpdatedArea", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms, UpdatedArea), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::NewProp_AverageDensity = { "AverageDensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms, AverageDensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::NewProp_UpdatedArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::NewProp_AverageDensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "OnDensityMapUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageProceduralManager::FOnDensityMapUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnDensityMapUpdated, FBox UpdatedArea, float AverageDensity)
{
	struct AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms
	{
		FBox UpdatedArea;
		float AverageDensity;
	};
	AuracronFoliageProceduralManager_eventOnDensityMapUpdated_Parms Parms;
	Parms.UpdatedArea=UpdatedArea;
	Parms.AverageDensity=AverageDensity;
	OnDensityMapUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDensityMapUpdated ****************************************************

// ********** Begin Delegate FOnPlacementRuleAdded *************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics
{
	struct AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms
	{
		FString RuleId;
		FAuracronPlacementRuleConfiguration Rule;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rule;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms, RuleId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::NewProp_Rule = { "Rule", nullptr, (EPropertyFlags)0x0010008000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms, Rule), Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, METADATA_PARAMS(0, nullptr) }; // 1795639906
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::NewProp_Rule,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "OnPlacementRuleAdded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageProceduralManager::FOnPlacementRuleAdded_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementRuleAdded, const FString& RuleId, FAuracronPlacementRuleConfiguration Rule)
{
	struct AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms
	{
		FString RuleId;
		FAuracronPlacementRuleConfiguration Rule;
	};
	AuracronFoliageProceduralManager_eventOnPlacementRuleAdded_Parms Parms;
	Parms.RuleId=RuleId;
	Parms.Rule=Rule;
	OnPlacementRuleAdded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlacementRuleAdded ***************************************************

// ********** Begin Delegate FOnPlacementRuleRemoved ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms
	{
		FString RuleId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms, RuleId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::NewProp_RuleId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "OnPlacementRuleRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageProceduralManager::FOnPlacementRuleRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementRuleRemoved, const FString& RuleId)
{
	struct AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms
	{
		FString RuleId;
	};
	AuracronFoliageProceduralManager_eventOnPlacementRuleRemoved_Parms Parms;
	Parms.RuleId=RuleId;
	OnPlacementRuleRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlacementRuleRemoved *************************************************

// ********** Begin Class UAuracronFoliageProceduralManager Function AddPlacementRule **************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics
{
	struct AuracronFoliageProceduralManager_eventAddPlacementRule_Parms
	{
		FAuracronPlacementRuleConfiguration Rule;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rule-based placement operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rule-based placement operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rule_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rule;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_Rule = { "Rule", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventAddPlacementRule_Parms, Rule), Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rule_MetaData), NewProp_Rule_MetaData) }; // 1795639906
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventAddPlacementRule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventAddPlacementRule_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_Rule,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "AddPlacementRule", Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::AuracronFoliageProceduralManager_eventAddPlacementRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::AuracronFoliageProceduralManager_eventAddPlacementRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execAddPlacementRule)
{
	P_GET_STRUCT_REF(FAuracronPlacementRuleConfiguration,Z_Param_Out_Rule);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddPlacementRule(Z_Param_Out_Rule);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function AddPlacementRule ****************

// ********** Begin Class UAuracronFoliageProceduralManager Function ApplyNoiseToPoints ************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics
{
	struct AuracronFoliageProceduralManager_eventApplyNoiseToPoints_Parms
	{
		TArray<FVector> Points;
		TArray<float> Densities;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Densities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Densities;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventApplyNoiseToPoints_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Densities_Inner = { "Densities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Densities = { "Densities", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventApplyNoiseToPoints_Parms, Densities), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Densities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::NewProp_Densities,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "ApplyNoiseToPoints", Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::AuracronFoliageProceduralManager_eventApplyNoiseToPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::AuracronFoliageProceduralManager_eventApplyNoiseToPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execApplyNoiseToPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_TARRAY_REF(float,Z_Param_Out_Densities);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyNoiseToPoints(Z_Param_Out_Points,Z_Param_Out_Densities);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function ApplyNoiseToPoints **************

// ********** Begin Class UAuracronFoliageProceduralManager Function CalculateSlopeAtLocation ******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics
{
	struct AuracronFoliageProceduralManager_eventCalculateSlopeAtLocation_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Slope filtering operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slope filtering operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventCalculateSlopeAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventCalculateSlopeAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "CalculateSlopeAtLocation", Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::AuracronFoliageProceduralManager_eventCalculateSlopeAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::AuracronFoliageProceduralManager_eventCalculateSlopeAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execCalculateSlopeAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateSlopeAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function CalculateSlopeAtLocation ********

// ********** Begin Class UAuracronFoliageProceduralManager Function ClearDensityMap ***************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "ClearDensityMap", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execClearDensityMap)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearDensityMap();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function ClearDensityMap *****************

// ********** Begin Class UAuracronFoliageProceduralManager Function ClearProceduralPlacement ******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics
{
	struct AuracronFoliageProceduralManager_eventClearProceduralPlacement_Parms
	{
		FBox Area;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventClearProceduralPlacement_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::NewProp_Area,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "ClearProceduralPlacement", Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::AuracronFoliageProceduralManager_eventClearProceduralPlacement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::AuracronFoliageProceduralManager_eventClearProceduralPlacement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execClearProceduralPlacement)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearProceduralPlacement(Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function ClearProceduralPlacement ********

// ********** Begin Class UAuracronFoliageProceduralManager Function CreatePCGComponent ************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics
{
	struct AuracronFoliageProceduralManager_eventCreatePCGComponent_Parms
	{
		FString ComponentId;
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::NewProp_ComponentId = { "ComponentId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventCreatePCGComponent_Parms, ComponentId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentId_MetaData), NewProp_ComponentId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventCreatePCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::NewProp_ComponentId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "CreatePCGComponent", Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::AuracronFoliageProceduralManager_eventCreatePCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::AuracronFoliageProceduralManager_eventCreatePCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execCreatePCGComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->CreatePCGComponent(Z_Param_ComponentId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function CreatePCGComponent **************

// ********** Begin Class UAuracronFoliageProceduralManager Function DrawDebugVisualization ********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics
{
	struct AuracronFoliageProceduralManager_eventDrawDebugVisualization_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventDrawDebugVisualization_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "DrawDebugVisualization", Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::AuracronFoliageProceduralManager_eventDrawDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::AuracronFoliageProceduralManager_eventDrawDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execDrawDebugVisualization)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugVisualization(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function DrawDebugVisualization **********

// ********** Begin Class UAuracronFoliageProceduralManager Function EnableDebugVisualization ******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageProceduralManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::AuracronFoliageProceduralManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::AuracronFoliageProceduralManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function EnableDebugVisualization ********

// ********** Begin Class UAuracronFoliageProceduralManager Function EvaluatePlacementRules ********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics
{
	struct AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms
	{
		FVector Location;
		FString FoliageTypeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "EvaluatePlacementRules", Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::AuracronFoliageProceduralManager_eventEvaluatePlacementRules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execEvaluatePlacementRules)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EvaluatePlacementRules(Z_Param_Out_Location,Z_Param_FoliageTypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function EvaluatePlacementRules **********

// ********** Begin Class UAuracronFoliageProceduralManager Function ExecutePCGGraph ***************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics
{
	struct AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms
	{
		FString ComponentId;
		UPCGGraph* Graph;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ComponentId = { "ComponentId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms, ComponentId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentId_MetaData), NewProp_ComponentId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ComponentId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_Graph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "ExecutePCGGraph", Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::AuracronFoliageProceduralManager_eventExecutePCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execExecutePCGGraph)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentId);
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecutePCGGraph(Z_Param_ComponentId,Z_Param_Graph);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function ExecutePCGGraph *****************

// ********** Begin Class UAuracronFoliageProceduralManager Function FilterPointsByHeight **********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics
{
	struct AuracronFoliageProceduralManager_eventFilterPointsByHeight_Parms
	{
		TArray<FVector> Points;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventFilterPointsByHeight_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventFilterPointsByHeight_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "FilterPointsByHeight", Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::AuracronFoliageProceduralManager_eventFilterPointsByHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::AuracronFoliageProceduralManager_eventFilterPointsByHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execFilterPointsByHeight)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FilterPointsByHeight(Z_Param_Out_Points);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function FilterPointsByHeight ************

// ********** Begin Class UAuracronFoliageProceduralManager Function FilterPointsBySlope ***********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics
{
	struct AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms
	{
		TArray<FVector> Points;
		TArray<FVector> Normals;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normals_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Normals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Normals_Inner = { "Normals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Normals = { "Normals", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms, Normals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normals_MetaData), NewProp_Normals_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Normals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_Normals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "FilterPointsBySlope", Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::AuracronFoliageProceduralManager_eventFilterPointsBySlope_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execFilterPointsBySlope)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Normals);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FilterPointsBySlope(Z_Param_Out_Points,Z_Param_Out_Normals);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function FilterPointsBySlope *************

// ********** Begin Class UAuracronFoliageProceduralManager Function GenerateNoiseBasedPoints ******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics
{
	struct AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms
	{
		FBox Area;
		int32 TargetCount;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_TargetCount = { "TargetCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms, TargetCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_TargetCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GenerateNoiseBasedPoints", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::AuracronFoliageProceduralManager_eventGenerateNoiseBasedPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGenerateNoiseBasedPoints)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateNoiseBasedPoints(Z_Param_Out_Area,Z_Param_TargetCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GenerateNoiseBasedPoints ********

// ********** Begin Class UAuracronFoliageProceduralManager Function GenerateProceduralPlacement ***
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics
{
	struct AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms
	{
		FString FoliageTypeId;
		FBox Area;
		FAuracronProceduralPlacementResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult, METADATA_PARAMS(0, nullptr) }; // 1676447307
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GenerateProceduralPlacement", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::AuracronFoliageProceduralManager_eventGenerateProceduralPlacement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGenerateProceduralPlacement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronProceduralPlacementResult*)Z_Param__Result=P_THIS->GenerateProceduralPlacement(Z_Param_FoliageTypeId,Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GenerateProceduralPlacement *****

// ********** Begin Class UAuracronFoliageProceduralManager Function GenerateProceduralPlacementAsync 
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics
{
	struct AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms
	{
		FString FoliageTypeId;
		FBox Area;
		FString CallbackId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CallbackId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CallbackId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_CallbackId = { "CallbackId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms, CallbackId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CallbackId_MetaData), NewProp_CallbackId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::NewProp_CallbackId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GenerateProceduralPlacementAsync", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::AuracronFoliageProceduralManager_eventGenerateProceduralPlacementAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGenerateProceduralPlacementAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_GET_PROPERTY(FStrProperty,Z_Param_CallbackId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateProceduralPlacementAsync(Z_Param_FoliageTypeId,Z_Param_Out_Area,Z_Param_CallbackId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GenerateProceduralPlacementAsync 

// ********** Begin Class UAuracronFoliageProceduralManager Function GenerateTransformsInArea ******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics
{
	struct AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms
	{
		FBox Area;
		float Density;
		TArray<FTransform> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "CPP_Default_Density", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GenerateTransformsInArea", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::AuracronFoliageProceduralManager_eventGenerateTransformsInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGenerateTransformsInArea)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTransform>*)Z_Param__Result=P_THIS->GenerateTransformsInArea(Z_Param_Out_Area,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GenerateTransformsInArea ********

// ********** Begin Class UAuracronFoliageProceduralManager Function GetAllPlacementRules **********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics
{
	struct AuracronFoliageProceduralManager_eventGetAllPlacementRules_Parms
	{
		TArray<FAuracronPlacementRuleConfiguration> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, METADATA_PARAMS(0, nullptr) }; // 1795639906
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetAllPlacementRules_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1795639906
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetAllPlacementRules", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::AuracronFoliageProceduralManager_eventGetAllPlacementRules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::AuracronFoliageProceduralManager_eventGetAllPlacementRules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetAllPlacementRules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPlacementRuleConfiguration>*)Z_Param__Result=P_THIS->GetAllPlacementRules();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetAllPlacementRules ************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetAverageDensity *************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics
{
	struct AuracronFoliageProceduralManager_eventGetAverageDensity_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetAverageDensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetAverageDensity", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::AuracronFoliageProceduralManager_eventGetAverageDensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::AuracronFoliageProceduralManager_eventGetAverageDensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetAverageDensity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageDensity();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetAverageDensity ***************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetConfiguration **************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics
{
	struct AuracronFoliageProceduralManager_eventGetConfiguration_Parms
	{
		FAuracronProceduralPlacementConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration, METADATA_PARAMS(0, nullptr) }; // 470973062
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::AuracronFoliageProceduralManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::AuracronFoliageProceduralManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronProceduralPlacementConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetConfiguration ****************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetGenerationStatistics *******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics
{
	struct AuracronFoliageProceduralManager_eventGetGenerationStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetGenerationStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetGenerationStatistics", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::AuracronFoliageProceduralManager_eventGetGenerationStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::AuracronFoliageProceduralManager_eventGetGenerationStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetGenerationStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetGenerationStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetGenerationStatistics *********

// ********** Begin Class UAuracronFoliageProceduralManager Function GetInstance *******************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics
{
	struct AuracronFoliageProceduralManager_eventGetInstance_Parms
	{
		UAuracronFoliageProceduralManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::AuracronFoliageProceduralManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::AuracronFoliageProceduralManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageProceduralManager**)Z_Param__Result=UAuracronFoliageProceduralManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetInstance *********************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetPCGPointData ***************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics
{
	struct AuracronFoliageProceduralManager_eventGetPCGPointData_Parms
	{
		FString ComponentId;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ComponentId = { "ComponentId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetPCGPointData_Parms, ComponentId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentId_MetaData), NewProp_ComponentId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetPCGPointData_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ComponentId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetPCGPointData", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::AuracronFoliageProceduralManager_eventGetPCGPointData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::AuracronFoliageProceduralManager_eventGetPCGPointData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetPCGPointData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetPCGPointData(Z_Param_ComponentId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetPCGPointData *****************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetPlacementRule **************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics
{
	struct AuracronFoliageProceduralManager_eventGetPlacementRule_Parms
	{
		FString RuleId;
		FAuracronPlacementRuleConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetPlacementRule_Parms, RuleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleId_MetaData), NewProp_RuleId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetPlacementRule_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration, METADATA_PARAMS(0, nullptr) }; // 1795639906
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetPlacementRule", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::AuracronFoliageProceduralManager_eventGetPlacementRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::AuracronFoliageProceduralManager_eventGetPlacementRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetPlacementRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RuleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPlacementRuleConfiguration*)Z_Param__Result=P_THIS->GetPlacementRule(Z_Param_RuleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetPlacementRule ****************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetRelativeHeight *************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics
{
	struct AuracronFoliageProceduralManager_eventGetRelativeHeight_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetRelativeHeight_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetRelativeHeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetRelativeHeight", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::AuracronFoliageProceduralManager_eventGetRelativeHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::AuracronFoliageProceduralManager_eventGetRelativeHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetRelativeHeight)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetRelativeHeight(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetRelativeHeight ***************

// ********** Begin Class UAuracronFoliageProceduralManager Function GetTotalGeneratedPoints *******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics
{
	struct AuracronFoliageProceduralManager_eventGetTotalGeneratedPoints_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventGetTotalGeneratedPoints_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "GetTotalGeneratedPoints", Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::AuracronFoliageProceduralManager_eventGetTotalGeneratedPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::AuracronFoliageProceduralManager_eventGetTotalGeneratedPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execGetTotalGeneratedPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalGeneratedPoints();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function GetTotalGeneratedPoints *********

// ********** Begin Class UAuracronFoliageProceduralManager Function Initialize ********************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics
{
	struct AuracronFoliageProceduralManager_eventInitialize_Parms
	{
		FAuracronProceduralPlacementConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 470973062
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::AuracronFoliageProceduralManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::AuracronFoliageProceduralManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronProceduralPlacementConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function Initialize **********************

// ********** Begin Class UAuracronFoliageProceduralManager Function IsDebugVisualizationEnabled ***
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageProceduralManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageProceduralManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageProceduralManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function IsDebugVisualizationEnabled *****

// ********** Begin Class UAuracronFoliageProceduralManager Function IsInitialized *****************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics
{
	struct AuracronFoliageProceduralManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::AuracronFoliageProceduralManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::AuracronFoliageProceduralManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function IsInitialized *******************

// ********** Begin Class UAuracronFoliageProceduralManager Function PassesHeightConstraints *******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics
{
	struct AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height constraint operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height constraint operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "PassesHeightConstraints", Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::AuracronFoliageProceduralManager_eventPassesHeightConstraints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execPassesHeightConstraints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PassesHeightConstraints(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function PassesHeightConstraints *********

// ********** Begin Class UAuracronFoliageProceduralManager Function PassesSlopeFilter *************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics
{
	struct AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms
	{
		FVector Location;
		FVector Normal;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normal_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normal;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_Normal = { "Normal", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms, Normal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normal_MetaData), NewProp_Normal_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_Normal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "PassesSlopeFilter", Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::AuracronFoliageProceduralManager_eventPassesSlopeFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execPassesSlopeFilter)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Normal);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PassesSlopeFilter(Z_Param_Out_Location,Z_Param_Out_Normal);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function PassesSlopeFilter ***************

// ********** Begin Class UAuracronFoliageProceduralManager Function RemovePlacementRule ***********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics
{
	struct AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms
	{
		FString RuleId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_RuleId = { "RuleId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms, RuleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleId_MetaData), NewProp_RuleId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms), &Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_RuleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "RemovePlacementRule", Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::AuracronFoliageProceduralManager_eventRemovePlacementRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execRemovePlacementRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RuleId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemovePlacementRule(Z_Param_RuleId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function RemovePlacementRule *************

// ********** Begin Class UAuracronFoliageProceduralManager Function SampleDensityAtLocation *******
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics
{
	struct AuracronFoliageProceduralManager_eventSampleDensityAtLocation_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Density map operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Density map operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleDensityAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleDensityAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "SampleDensityAtLocation", Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::AuracronFoliageProceduralManager_eventSampleDensityAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::AuracronFoliageProceduralManager_eventSampleDensityAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execSampleDensityAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->SampleDensityAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function SampleDensityAtLocation *********

// ********** Begin Class UAuracronFoliageProceduralManager Function SampleDensityInArea ***********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics
{
	struct AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms
	{
		FBox Area;
		int32 SampleCount;
		TArray<float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "CPP_Default_SampleCount", "100" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SampleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_SampleCount = { "SampleCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms, SampleCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_SampleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "SampleDensityInArea", Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::AuracronFoliageProceduralManager_eventSampleDensityInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execSampleDensityInArea)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_GET_PROPERTY(FIntProperty,Z_Param_SampleCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<float>*)Z_Param__Result=P_THIS->SampleDensityInArea(Z_Param_Out_Area,Z_Param_SampleCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function SampleDensityInArea *************

// ********** Begin Class UAuracronFoliageProceduralManager Function SampleNoiseAtLocation *********
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics
{
	struct AuracronFoliageProceduralManager_eventSampleNoiseAtLocation_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise distribution operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise distribution operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleNoiseAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSampleNoiseAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "SampleNoiseAtLocation", Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::AuracronFoliageProceduralManager_eventSampleNoiseAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::AuracronFoliageProceduralManager_eventSampleNoiseAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execSampleNoiseAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->SampleNoiseAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function SampleNoiseAtLocation ***********

// ********** Begin Class UAuracronFoliageProceduralManager Function SetConfiguration **************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics
{
	struct AuracronFoliageProceduralManager_eventSetConfiguration_Parms
	{
		FAuracronProceduralPlacementConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 470973062
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::AuracronFoliageProceduralManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::AuracronFoliageProceduralManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronProceduralPlacementConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function SetConfiguration ****************

// ********** Begin Class UAuracronFoliageProceduralManager Function Shutdown **********************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function Shutdown ************************

// ********** Begin Class UAuracronFoliageProceduralManager Function Tick **************************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics
{
	struct AuracronFoliageProceduralManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::AuracronFoliageProceduralManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::AuracronFoliageProceduralManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function Tick ****************************

// ********** Begin Class UAuracronFoliageProceduralManager Function UpdateDensityMap **************
struct Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics
{
	struct AuracronFoliageProceduralManager_eventUpdateDensityMap_Parms
	{
		FAuracronDensityMapConfiguration DensityConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::NewProp_DensityConfig = { "DensityConfig", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageProceduralManager_eventUpdateDensityMap_Parms, DensityConfig), Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityConfig_MetaData), NewProp_DensityConfig_MetaData) }; // 3924111466
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::NewProp_DensityConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageProceduralManager, nullptr, "UpdateDensityMap", Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::AuracronFoliageProceduralManager_eventUpdateDensityMap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::AuracronFoliageProceduralManager_eventUpdateDensityMap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageProceduralManager::execUpdateDensityMap)
{
	P_GET_STRUCT_REF(FAuracronDensityMapConfiguration,Z_Param_Out_DensityConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDensityMap(Z_Param_Out_DensityConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageProceduralManager Function UpdateDensityMap ****************

// ********** Begin Class UAuracronFoliageProceduralManager ****************************************
void UAuracronFoliageProceduralManager::StaticRegisterNativesUAuracronFoliageProceduralManager()
{
	UClass* Class = UAuracronFoliageProceduralManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddPlacementRule", &UAuracronFoliageProceduralManager::execAddPlacementRule },
		{ "ApplyNoiseToPoints", &UAuracronFoliageProceduralManager::execApplyNoiseToPoints },
		{ "CalculateSlopeAtLocation", &UAuracronFoliageProceduralManager::execCalculateSlopeAtLocation },
		{ "ClearDensityMap", &UAuracronFoliageProceduralManager::execClearDensityMap },
		{ "ClearProceduralPlacement", &UAuracronFoliageProceduralManager::execClearProceduralPlacement },
		{ "CreatePCGComponent", &UAuracronFoliageProceduralManager::execCreatePCGComponent },
		{ "DrawDebugVisualization", &UAuracronFoliageProceduralManager::execDrawDebugVisualization },
		{ "EnableDebugVisualization", &UAuracronFoliageProceduralManager::execEnableDebugVisualization },
		{ "EvaluatePlacementRules", &UAuracronFoliageProceduralManager::execEvaluatePlacementRules },
		{ "ExecutePCGGraph", &UAuracronFoliageProceduralManager::execExecutePCGGraph },
		{ "FilterPointsByHeight", &UAuracronFoliageProceduralManager::execFilterPointsByHeight },
		{ "FilterPointsBySlope", &UAuracronFoliageProceduralManager::execFilterPointsBySlope },
		{ "GenerateNoiseBasedPoints", &UAuracronFoliageProceduralManager::execGenerateNoiseBasedPoints },
		{ "GenerateProceduralPlacement", &UAuracronFoliageProceduralManager::execGenerateProceduralPlacement },
		{ "GenerateProceduralPlacementAsync", &UAuracronFoliageProceduralManager::execGenerateProceduralPlacementAsync },
		{ "GenerateTransformsInArea", &UAuracronFoliageProceduralManager::execGenerateTransformsInArea },
		{ "GetAllPlacementRules", &UAuracronFoliageProceduralManager::execGetAllPlacementRules },
		{ "GetAverageDensity", &UAuracronFoliageProceduralManager::execGetAverageDensity },
		{ "GetConfiguration", &UAuracronFoliageProceduralManager::execGetConfiguration },
		{ "GetGenerationStatistics", &UAuracronFoliageProceduralManager::execGetGenerationStatistics },
		{ "GetInstance", &UAuracronFoliageProceduralManager::execGetInstance },
		{ "GetPCGPointData", &UAuracronFoliageProceduralManager::execGetPCGPointData },
		{ "GetPlacementRule", &UAuracronFoliageProceduralManager::execGetPlacementRule },
		{ "GetRelativeHeight", &UAuracronFoliageProceduralManager::execGetRelativeHeight },
		{ "GetTotalGeneratedPoints", &UAuracronFoliageProceduralManager::execGetTotalGeneratedPoints },
		{ "Initialize", &UAuracronFoliageProceduralManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageProceduralManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageProceduralManager::execIsInitialized },
		{ "PassesHeightConstraints", &UAuracronFoliageProceduralManager::execPassesHeightConstraints },
		{ "PassesSlopeFilter", &UAuracronFoliageProceduralManager::execPassesSlopeFilter },
		{ "RemovePlacementRule", &UAuracronFoliageProceduralManager::execRemovePlacementRule },
		{ "SampleDensityAtLocation", &UAuracronFoliageProceduralManager::execSampleDensityAtLocation },
		{ "SampleDensityInArea", &UAuracronFoliageProceduralManager::execSampleDensityInArea },
		{ "SampleNoiseAtLocation", &UAuracronFoliageProceduralManager::execSampleNoiseAtLocation },
		{ "SetConfiguration", &UAuracronFoliageProceduralManager::execSetConfiguration },
		{ "Shutdown", &UAuracronFoliageProceduralManager::execShutdown },
		{ "Tick", &UAuracronFoliageProceduralManager::execTick },
		{ "UpdateDensityMap", &UAuracronFoliageProceduralManager::execUpdateDensityMap },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageProceduralManager;
UClass* UAuracronFoliageProceduralManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageProceduralManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageProceduralManager"),
			Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageProceduralManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager_NoRegister()
{
	return UAuracronFoliageProceduralManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Procedural Manager\n * Manager for procedural foliage placement system\n */" },
#endif
		{ "IncludePath", "AuracronFoliageProcedural.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Procedural Manager\nManager for procedural foliage placement system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProceduralGenerationCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDensityMapUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlacementRuleAdded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlacementRuleRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageProcedural.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProceduralGenerationCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDensityMapUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlacementRuleAdded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlacementRuleRemoved;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_AddPlacementRule, "AddPlacementRule" }, // 1018310104
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_ApplyNoiseToPoints, "ApplyNoiseToPoints" }, // 282961901
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_CalculateSlopeAtLocation, "CalculateSlopeAtLocation" }, // 3202309394
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearDensityMap, "ClearDensityMap" }, // 734595227
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_ClearProceduralPlacement, "ClearProceduralPlacement" }, // 422645064
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_CreatePCGComponent, "CreatePCGComponent" }, // 741499550
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_DrawDebugVisualization, "DrawDebugVisualization" }, // 584610439
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 1823594286
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_EvaluatePlacementRules, "EvaluatePlacementRules" }, // 313318708
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_ExecutePCGGraph, "ExecutePCGGraph" }, // 2597807025
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsByHeight, "FilterPointsByHeight" }, // 1890148089
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_FilterPointsBySlope, "FilterPointsBySlope" }, // 2553222669
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateNoiseBasedPoints, "GenerateNoiseBasedPoints" }, // 3436074167
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacement, "GenerateProceduralPlacement" }, // 1547713498
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateProceduralPlacementAsync, "GenerateProceduralPlacementAsync" }, // 2999082399
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GenerateTransformsInArea, "GenerateTransformsInArea" }, // 2939984559
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAllPlacementRules, "GetAllPlacementRules" }, // 404372069
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetAverageDensity, "GetAverageDensity" }, // 4022506042
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetConfiguration, "GetConfiguration" }, // 1512881259
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetGenerationStatistics, "GetGenerationStatistics" }, // 4017236314
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetInstance, "GetInstance" }, // 3295241465
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPCGPointData, "GetPCGPointData" }, // 564071101
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetPlacementRule, "GetPlacementRule" }, // 1989563178
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetRelativeHeight, "GetRelativeHeight" }, // 2562709721
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_GetTotalGeneratedPoints, "GetTotalGeneratedPoints" }, // 929158743
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_Initialize, "Initialize" }, // 1370538372
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 1619836956
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_IsInitialized, "IsInitialized" }, // 1393129363
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature, "OnDensityMapUpdated__DelegateSignature" }, // 933375933
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature, "OnPlacementRuleAdded__DelegateSignature" }, // 364561001
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature, "OnPlacementRuleRemoved__DelegateSignature" }, // 3774393449
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature, "OnProceduralGenerationCompleted__DelegateSignature" }, // 1373406014
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesHeightConstraints, "PassesHeightConstraints" }, // 931550412
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_PassesSlopeFilter, "PassesSlopeFilter" }, // 3430586208
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_RemovePlacementRule, "RemovePlacementRule" }, // 924569617
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityAtLocation, "SampleDensityAtLocation" }, // 4230387745
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleDensityInArea, "SampleDensityInArea" }, // 1899034171
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_SampleNoiseAtLocation, "SampleNoiseAtLocation" }, // 4145001993
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_SetConfiguration, "SetConfiguration" }, // 261793068
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_Shutdown, "Shutdown" }, // 318074880
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_Tick, "Tick" }, // 3723634372
		{ &Z_Construct_UFunction_UAuracronFoliageProceduralManager_UpdateDensityMap, "UpdateDensityMap" }, // 441378325
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageProceduralManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnProceduralGenerationCompleted = { "OnProceduralGenerationCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, OnProceduralGenerationCompleted), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnProceduralGenerationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProceduralGenerationCompleted_MetaData), NewProp_OnProceduralGenerationCompleted_MetaData) }; // 1373406014
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnDensityMapUpdated = { "OnDensityMapUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, OnDensityMapUpdated), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnDensityMapUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDensityMapUpdated_MetaData), NewProp_OnDensityMapUpdated_MetaData) }; // 933375933
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnPlacementRuleAdded = { "OnPlacementRuleAdded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, OnPlacementRuleAdded), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleAdded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlacementRuleAdded_MetaData), NewProp_OnPlacementRuleAdded_MetaData) }; // 364561001
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnPlacementRuleRemoved = { "OnPlacementRuleRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, OnPlacementRuleRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageProceduralManager_OnPlacementRuleRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlacementRuleRemoved_MetaData), NewProp_OnPlacementRuleRemoved_MetaData) }; // 3774393449
void Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageProceduralManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageProceduralManager), &Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, Configuration), Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 470973062
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageProceduralManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnProceduralGenerationCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnDensityMapUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnPlacementRuleAdded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_OnPlacementRuleRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::ClassParams = {
	&UAuracronFoliageProceduralManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageProceduralManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageProceduralManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageProceduralManager.OuterSingleton;
}
UAuracronFoliageProceduralManager::UAuracronFoliageProceduralManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageProceduralManager);
UAuracronFoliageProceduralManager::~UAuracronFoliageProceduralManager() {}
// ********** End Class UAuracronFoliageProceduralManager ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronDensityMapType_StaticEnum, TEXT("EAuracronDensityMapType"), &Z_Registration_Info_UEnum_EAuracronDensityMapType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 454692608U) },
		{ EAuracronSlopeFilterMode_StaticEnum, TEXT("EAuracronSlopeFilterMode"), &Z_Registration_Info_UEnum_EAuracronSlopeFilterMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 316499568U) },
		{ EAuracronHeightConstraintMode_StaticEnum, TEXT("EAuracronHeightConstraintMode"), &Z_Registration_Info_UEnum_EAuracronHeightConstraintMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 411645759U) },
		{ EAuracronNoiseDistributionType_StaticEnum, TEXT("EAuracronNoiseDistributionType"), &Z_Registration_Info_UEnum_EAuracronNoiseDistributionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 822148285U) },
		{ EAuracronPlacementRuleType_StaticEnum, TEXT("EAuracronPlacementRuleType"), &Z_Registration_Info_UEnum_EAuracronPlacementRuleType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1822528937U) },
		{ EAuracronPlacementPriority_StaticEnum, TEXT("EAuracronPlacementPriority"), &Z_Registration_Info_UEnum_EAuracronPlacementPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 413917681U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDensityMapConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDensityMapConfiguration_Statics::NewStructOps, TEXT("AuracronDensityMapConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDensityMapConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDensityMapConfiguration), 3924111466U) },
		{ FAuracronSlopeFilterConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronSlopeFilterConfiguration_Statics::NewStructOps, TEXT("AuracronSlopeFilterConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronSlopeFilterConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSlopeFilterConfiguration), 3727459798U) },
		{ FAuracronHeightConstraintConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronHeightConstraintConfiguration_Statics::NewStructOps, TEXT("AuracronHeightConstraintConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronHeightConstraintConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronHeightConstraintConfiguration), 2904131641U) },
		{ FAuracronPlacementRuleConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronPlacementRuleConfiguration_Statics::NewStructOps, TEXT("AuracronPlacementRuleConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronPlacementRuleConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPlacementRuleConfiguration), 1795639906U) },
		{ FAuracronProceduralPlacementConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralPlacementConfiguration_Statics::NewStructOps, TEXT("AuracronProceduralPlacementConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralPlacementConfiguration), 470973062U) },
		{ FAuracronProceduralPlacementResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralPlacementResult_Statics::NewStructOps, TEXT("AuracronProceduralPlacementResult"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralPlacementResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralPlacementResult), 1676447307U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageProceduralManager, UAuracronFoliageProceduralManager::StaticClass, TEXT("UAuracronFoliageProceduralManager"), &Z_Registration_Info_UClass_UAuracronFoliageProceduralManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageProceduralManager), 3514469461U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_3643144340(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageProcedural_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
