// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Async Processing Header
// Bridge 2.16: PCG Framework - Async Processing

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"
#include "PCGComponent.h"
#include "PCGContext.h"

// Engine includes for async processing
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Async/Async.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/Event.h"
#include "Misc/DateTime.h"
#include "Templates/Atomic.h"

#include "AuracronPCGAsyncProcessing.generated.h"

// Forward declarations
class UAuracronPCGAsyncTaskManager;
class UAuracronPCGProgressTracker;

// =============================================================================
// ASYNC PROCESSING TYPES
// =============================================================================

// Async processing modes
UENUM(BlueprintType)
enum class EAuracronPCGAsyncProcessingMode : uint8
{
    Sequential              UMETA(DisplayName = "Sequential"),
    Parallel                UMETA(DisplayName = "Parallel"),
    TaskGraph               UMETA(DisplayName = "Task Graph"),
    AsyncTask               UMETA(DisplayName = "Async Task"),
    ParallelFor             UMETA(DisplayName = "Parallel For"),
    Hybrid                  UMETA(DisplayName = "Hybrid")
};

// Task priorities
UENUM(BlueprintType)
enum class EAuracronPCGTaskPriority : uint8
{
    Low                     UMETA(DisplayName = "Low"),
    Normal                  UMETA(DisplayName = "Normal"),
    High                    UMETA(DisplayName = "High"),
    Critical                UMETA(DisplayName = "Critical"),
    Immediate               UMETA(DisplayName = "Immediate")
};

// Task states
UENUM(BlueprintType)
enum class EAuracronPCGTaskState : uint8
{
    Pending                 UMETA(DisplayName = "Pending"),
    Running                 UMETA(DisplayName = "Running"),
    Completed               UMETA(DisplayName = "Completed"),
    Failed                  UMETA(DisplayName = "Failed"),
    Cancelled               UMETA(DisplayName = "Cancelled"),
    Paused                  UMETA(DisplayName = "Paused")
};

// Thread types
UENUM(BlueprintType)
enum class EAuracronPCGThreadType : uint8
{
    GameThread              UMETA(DisplayName = "Game Thread"),
    RenderThread            UMETA(DisplayName = "Render Thread"),
    AnyThread               UMETA(DisplayName = "Any Thread"),
    BackgroundThread        UMETA(DisplayName = "Background Thread"),
    WorkerThread            UMETA(DisplayName = "Worker Thread"),
    HighPriorityThread      UMETA(DisplayName = "High Priority Thread")
};

// =============================================================================
// ASYNC TASK DESCRIPTOR
// =============================================================================

/**
 * Async Task Descriptor
 * Describes configuration for async task execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGAsyncTaskDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Task")
    FString TaskName = TEXT("PCGAsyncTask");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Task")
    FString TaskDescription = TEXT("PCG Async Processing Task");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    EAuracronPCGAsyncProcessingMode ProcessingMode = EAuracronPCGAsyncProcessingMode::Parallel;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    EAuracronPCGTaskPriority Priority = EAuracronPCGTaskPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    EAuracronPCGThreadType ThreadType = EAuracronPCGThreadType::AnyThread;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    int32 MaxConcurrentTasks = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    float TimeoutSeconds = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    bool bAllowCancellation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    bool bTrackProgress = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    bool bReportProgress = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    float ProgressUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bUseMemoryPooling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MemoryPoolSize = 1024; // MB

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bLimitMemoryUsage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxMemoryUsageMB = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    bool bUseBatching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    int32 BatchSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    int32 MinBatchSize = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batching")
    int32 MaxBatchSize = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bRetryOnFailure = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    int32 MaxRetryAttempts = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    float RetryDelaySeconds = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bEnableDetailedLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bEnablePerformanceProfiling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bEnableMemoryTracking = true;

    FAuracronPCGAsyncTaskDescriptor()
    {
        TaskName = TEXT("PCGAsyncTask");
        TaskDescription = TEXT("PCG Async Processing Task");
        ProcessingMode = EAuracronPCGAsyncProcessingMode::Parallel;
        Priority = EAuracronPCGTaskPriority::Normal;
        ThreadType = EAuracronPCGThreadType::AnyThread;
        MaxConcurrentTasks = 4;
        TimeoutSeconds = 300.0f;
        bAllowCancellation = true;
        bTrackProgress = true;
        bReportProgress = true;
        ProgressUpdateInterval = 0.1f;
        bUseMemoryPooling = true;
        MemoryPoolSize = 1024;
        bLimitMemoryUsage = true;
        MaxMemoryUsageMB = 2048;
        bUseBatching = true;
        BatchSize = 1000;
        MinBatchSize = 100;
        MaxBatchSize = 10000;
        bRetryOnFailure = true;
        MaxRetryAttempts = 3;
        RetryDelaySeconds = 1.0f;
        bEnableDetailedLogging = false;
        bEnablePerformanceProfiling = true;
        bEnableMemoryTracking = true;
    }
};

// =============================================================================
// PROGRESS TRACKING
// =============================================================================

/**
 * Progress Info
 * Information about task progress
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGProgressInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    FString TaskId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    FString TaskName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    EAuracronPCGTaskState TaskState = EAuracronPCGTaskState::Pending;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    float ProgressPercentage = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    int32 ItemsProcessed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    int32 TotalItems = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    float ElapsedTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    float EstimatedTimeRemaining = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    FString CurrentOperation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
    FString StatusMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ItemsPerSecond = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveThreads = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error")
    bool bHasErrors = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error")
    TArray<FString> ErrorMessages;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error")
    TArray<FString> WarningMessages;

    FAuracronPCGProgressInfo()
    {
        TaskState = EAuracronPCGTaskState::Pending;
        ProgressPercentage = 0.0f;
        ItemsProcessed = 0;
        TotalItems = 0;
        ElapsedTime = 0.0f;
        EstimatedTimeRemaining = 0.0f;
        ItemsPerSecond = 0.0f;
        MemoryUsageMB = 0.0f;
        ActiveThreads = 0;
        bHasErrors = false;
    }
};

// =============================================================================
// ASYNC TASK RESULT
// =============================================================================

/**
 * Async Task Result
 * Result of async task execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGAsyncTaskResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString TaskId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    EAuracronPCGTaskState FinalState = EAuracronPCGTaskState::Pending;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> WarningMessages;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float TotalExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 ItemsProcessed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 ItemsFailed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float AverageProcessingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float PeakMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 RetryAttempts = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TMap<FString, FString> CustomData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    TMap<FString, float> PerformanceMetrics;

    FAuracronPCGAsyncTaskResult()
    {
        bSuccess = false;
        FinalState = EAuracronPCGTaskState::Pending;
        TotalExecutionTime = 0.0f;
        ItemsProcessed = 0;
        ItemsFailed = 0;
        AverageProcessingTime = 0.0f;
        PeakMemoryUsageMB = 0.0f;
        RetryAttempts = 0;
    }
};

// =============================================================================
// PROGRESS TRACKER
// =============================================================================

/**
 * Progress Tracker
 * Tracks progress of async tasks
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGProgressTracker : public UObject
{
    GENERATED_BODY()

public:
    // Progress tracking
    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void StartTracking(const FString& TaskId, const FString& TaskName, int32 TotalItems);

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void UpdateProgress(const FString& TaskId, int32 ItemsProcessed, const FString& CurrentOperation = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void SetTaskState(const FString& TaskId, EAuracronPCGTaskState NewState);

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void AddError(const FString& TaskId, const FString& ErrorMessage);

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void AddWarning(const FString& TaskId, const FString& WarningMessage);

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void FinishTracking(const FString& TaskId, bool bSuccess);

    // Progress retrieval
    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    FAuracronPCGProgressInfo GetProgressInfo(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    TArray<FAuracronPCGProgressInfo> GetAllProgressInfo() const;

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    bool IsTaskActive(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    float GetOverallProgress() const;

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    int32 GetActiveTaskCount() const;

    // Cleanup
    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void ClearCompletedTasks();

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void ClearAllTasks();

    UFUNCTION(BlueprintCallable, Category = "Progress Tracker")
    void RemoveTask(const FString& TaskId);

private:
    UPROPERTY()
    TMap<FString, FAuracronPCGProgressInfo> ProgressMap;

    mutable FCriticalSection ProgressMapLock;

    void UpdatePerformanceMetrics(const FString& TaskId);
    float CalculateEstimatedTimeRemaining(const FAuracronPCGProgressInfo& ProgressInfo) const;
};

// =============================================================================
// ASYNC TASK BASE
// =============================================================================

/**
 * Async Task Base
 * Base class for async PCG tasks
 */
class AURACRONPCGFRAMEWORK_API FAuracronPCGAsyncTaskBase
{
public:
    FAuracronPCGAsyncTaskBase(const FAuracronPCGAsyncTaskDescriptor& InDescriptor);
    virtual ~FAuracronPCGAsyncTaskBase();

    // Task execution
    virtual void Execute() = 0;
    virtual void Cancel();
    virtual void Pause();
    virtual void Resume();

    // Task state
    FString GetTaskId() const { return TaskId; }
    EAuracronPCGTaskState GetTaskState() const { return TaskState; }
    bool IsCancelled() const { return bIsCancelled.Load(); }
    bool IsPaused() const { return bIsPaused.Load(); }
    bool IsCompleted() const { return TaskState == EAuracronPCGTaskState::Completed || TaskState == EAuracronPCGTaskState::Failed; }

    // Progress
    void UpdateProgress(int32 ItemsProcessed, const FString& CurrentOperation = TEXT(""));
    FAuracronPCGProgressInfo GetProgressInfo() const;

    // Result
    FAuracronPCGAsyncTaskResult GetResult() const { return TaskResult; }

protected:
    FString TaskId;
    FAuracronPCGAsyncTaskDescriptor Descriptor;
    TAtomic<EAuracronPCGTaskState> TaskState;
    FThreadSafeBool bIsCancelled;
    FThreadSafeBool bIsPaused;

    FAuracronPCGAsyncTaskResult TaskResult;
    UAuracronPCGProgressTracker* ProgressTracker;

    FDateTime StartTime;
    FDateTime EndTime;

    mutable FCriticalSection TaskLock;

    // Helper functions
    void SetTaskState(EAuracronPCGTaskState NewState);
    void AddError(const FString& ErrorMessage);
    void AddWarning(const FString& WarningMessage);
    bool ShouldContinueExecution() const;
    void WaitIfPaused();
};

// =============================================================================
// PARALLEL FOR TASK
// =============================================================================

/**
 * Parallel For Task
 * Executes operations in parallel using ParallelFor
 */
template<typename DataType, typename FunctionType>
class AURACRONPCGFRAMEWORK_API FAuracronPCGParallelForTask : public FAuracronPCGAsyncTaskBase
{
public:
    FAuracronPCGParallelForTask(
        const FAuracronPCGAsyncTaskDescriptor& InDescriptor,
        const TArray<DataType>& InData,
        FunctionType InFunction
    );

    virtual void Execute() override;

private:
    TArray<DataType> Data;
    FunctionType Function;
    TAtomic<int32> ProcessedCount;
};

// =============================================================================
// TASK GRAPH TASK
// =============================================================================

/**
 * Task Graph Task
 * Executes operations using TaskGraph system
 */
class AURACRONPCGFRAMEWORK_API FAuracronPCGTaskGraphTask : public FAuracronPCGAsyncTaskBase
{
public:
    FAuracronPCGTaskGraphTask(const FAuracronPCGAsyncTaskDescriptor& InDescriptor);

    virtual void Execute() override;

    // Task dependencies
    void AddDependency(TSharedPtr<FAuracronPCGTaskGraphTask> Dependency);
    void SetCompletionCallback(TFunction<void(const FAuracronPCGAsyncTaskResult&)> Callback);

protected:
    TArray<TSharedPtr<FAuracronPCGTaskGraphTask>> Dependencies;
    TFunction<void(const FAuracronPCGAsyncTaskResult&)> CompletionCallback;
    FGraphEventRef TaskGraphEvent;

    virtual void DoWork() = 0;
    void ExecuteTaskGraph();
};

// =============================================================================
// ASYNC WORK TASK
// =============================================================================

/**
 * Async Work Task
 * Executes operations using AsyncWork system
 */
template<typename WorkerType>
class AURACRONPCGFRAMEWORK_API FAuracronPCGAsyncWorkTask : public FAuracronPCGAsyncTaskBase, public FAsyncTask<WorkerType>
{
public:
    FAuracronPCGAsyncWorkTask(const FAuracronPCGAsyncTaskDescriptor& InDescriptor);

    virtual void Execute() override;
    virtual void Cancel() override;

    // AsyncWork interface
    void StartBackgroundTask();
    void EnsureCompletion();
    bool IsDone() const;

private:
    TUniquePtr<FAsyncTask<WorkerType>> AsyncWork;
};

// =============================================================================
// BATCH PROCESSOR
// =============================================================================

/**
 * Batch Processor
 * Processes data in batches for better performance
 */
template<typename DataType>
class AURACRONPCGFRAMEWORK_API FAuracronPCGBatchProcessor
{
public:
    FAuracronPCGBatchProcessor(const FAuracronPCGAsyncTaskDescriptor& InDescriptor);

    void ProcessBatches(
        const TArray<DataType>& Data,
        TFunction<void(const TArray<DataType>&, int32)> BatchFunction,
        UAuracronPCGProgressTracker* ProgressTracker = nullptr
    );

    void ProcessBatchesParallel(
        const TArray<DataType>& Data,
        TFunction<void(const TArray<DataType>&, int32)> BatchFunction,
        UAuracronPCGProgressTracker* ProgressTracker = nullptr
    );

private:
    FAuracronPCGAsyncTaskDescriptor Descriptor;
    TAtomic<bool> bIsCancelled;

    TArray<TArray<DataType>> CreateBatches(const TArray<DataType>& Data) const;
    int32 CalculateOptimalBatchSize(int32 DataSize) const;
};

// =============================================================================
// MEMORY POOL MANAGER
// =============================================================================

/**
 * Memory Pool Manager
 * Manages memory pools for async processing
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGMemoryPoolManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    static UAuracronPCGMemoryPoolManager* GetInstance();

    // Pool management
    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    void InitializePool(int32 PoolSizeMB);

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    void ShutdownPool();

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    bool IsPoolInitialized() const;

    // Memory allocation
    // Memory allocation functions (C++ only - void* cannot be exposed to Blueprints)
    void* AllocateMemory(int32 SizeBytes);
    void DeallocateMemory(void* Memory);
    void* ReallocateMemory(void* Memory, int32 NewSizeBytes);

    // Pool statistics
    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    int32 GetTotalPoolSize() const;

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    int32 GetUsedMemory() const;

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    int32 GetFreeMemory() const;

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    float GetMemoryUsagePercentage() const;

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    int32 GetAllocationCount() const;

    // Memory tracking
    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    void EnableMemoryTracking(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    TMap<FString, int32> GetMemoryTrackingInfo() const;

    UFUNCTION(BlueprintCallable, Category = "Memory Pool Manager")
    void ClearMemoryTrackingInfo();

private:
    static UAuracronPCGMemoryPoolManager* Instance;

    UPROPERTY()
    int32 PoolSizeMB = 0;

    UPROPERTY()
    int32 UsedMemoryBytes = 0;

    UPROPERTY()
    int32 AllocationCount = 0;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    bool bMemoryTrackingEnabled = false;

    void* MemoryPool = nullptr;
    TMap<void*, int32> FreeBlocks;
    TMap<void*, int32> AllocatedBlocks;
    TMap<FString, int32> MemoryTrackingMap;

    mutable FCriticalSection PoolLock;

    void InitializeMemoryPool();
    void CleanupMemoryPool();
};

// =============================================================================
// CANCELLATION TOKEN
// =============================================================================

/**
 * Cancellation Token
 * Provides cancellation support for async tasks
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGCancellationToken : public UObject
{
    GENERATED_BODY()

public:
    // Token management
    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    static UAuracronPCGCancellationToken* CreateToken();

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void Cancel();

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void Reset();

    // Token state
    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    bool IsCancelled() const;

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void ThrowIfCancelled() const;

    // Callbacks
    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void RegisterCancellationCallback(const FString& CallbackId);

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void UnregisterCancellationCallback(const FString& CallbackId);

    // Timeout support
    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void SetTimeout(float TimeoutSeconds);

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    void ClearTimeout();

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    bool HasTimeout() const;

    UFUNCTION(BlueprintCallable, Category = "Cancellation Token")
    float GetRemainingTime() const;

private:
    FThreadSafeBool bIsCancelled;
    TArray<FString> CancellationCallbacks;
    FDateTime CreationTime;
    float TimeoutSeconds = 0.0f;
    bool bHasTimeout = false;

    mutable FCriticalSection TokenLock;

    void CheckTimeout();
    void ExecuteCancellationCallbacks();
};

// =============================================================================
// ASYNC TASK MANAGER
// =============================================================================

/**
 * Async Task Manager
 * Central manager for all async PCG processing
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAsyncTaskManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    static UAuracronPCGAsyncTaskManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void Initialize();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    bool IsInitialized() const;

    // Task execution
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FString ExecutePointProcessingAsync(
        UPCGPointData* PointData,
        const FAuracronPCGAsyncTaskDescriptor& Descriptor
    );

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FString ExecuteGraphAsync(
        UPCGGraph* Graph,
        const FAuracronPCGAsyncTaskDescriptor& Descriptor
    );

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FString ExecuteCustomTaskAsync(
        const FAuracronPCGAsyncTaskDescriptor& Descriptor,
        const FString& TaskType
    );

    // Task management
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void CancelTask(const FString& TaskId);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void CancelAllTasks();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void PauseTask(const FString& TaskId);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void ResumeTask(const FString& TaskId);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void PauseAllTasks();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void ResumeAllTasks();

    // Task state queries
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    bool IsTaskActive(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    EAuracronPCGTaskState GetTaskState(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FAuracronPCGProgressInfo GetTaskProgress(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FAuracronPCGAsyncTaskResult GetTaskResult(const FString& TaskId) const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    TArray<FString> GetActiveTasks() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    TArray<FString> GetCompletedTasks() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    int32 GetActiveTaskCount() const;

    // Batch operations
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    TArray<FString> ExecuteBatchPointProcessing(
        const TArray<UPCGPointData*>& PointDataArray,
        const FAuracronPCGAsyncTaskDescriptor& Descriptor
    );

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void WaitForBatchCompletion(const TArray<FString>& TaskIds, float TimeoutSeconds = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    bool AreBatchTasksCompleted(const TArray<FString>& TaskIds) const;

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    TMap<FString, float> GetPerformanceMetrics() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    float GetAverageTaskExecutionTime() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    int32 GetTotalTasksExecuted() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    float GetSystemLoadPercentage() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void SetMaxConcurrentTasks(int32 MaxTasks);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    int32 GetMaxConcurrentTasks() const;

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void SetDefaultTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    FAuracronPCGAsyncTaskDescriptor GetDefaultTaskDescriptor() const;

    // Cleanup
    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void CleanupCompletedTasks();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void CleanupFailedTasks();

    UFUNCTION(BlueprintCallable, Category = "Async Task Manager")
    void CleanupAllTasks();

    // Events and callbacks
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTaskCompleted, FString, TaskId, FAuracronPCGAsyncTaskResult, Result);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTaskFailed, FString, TaskId, FString, ErrorMessage);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTaskProgress, FString, TaskId, FAuracronPCGProgressInfo, ProgressInfo);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTaskCompleted OnTaskCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTaskFailed OnTaskFailed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTaskProgress OnTaskProgress;

private:
    static UAuracronPCGAsyncTaskManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    int32 MaxConcurrentTasks = 8;

    UPROPERTY()
    FAuracronPCGAsyncTaskDescriptor DefaultTaskDescriptor;

    UPROPERTY()
    UAuracronPCGProgressTracker* ProgressTracker;

    UPROPERTY()
    UAuracronPCGMemoryPoolManager* MemoryPoolManager;

    // Task storage
    TMap<FString, TSharedPtr<FAuracronPCGAsyncTaskBase>> ActiveTasks;
    TMap<FString, FAuracronPCGAsyncTaskResult> CompletedTasks;
    TMap<FString, UAuracronPCGCancellationToken*> CancellationTokens;

    // Thread safety
    mutable FCriticalSection TaskMapLock;
    mutable FCriticalSection CompletedTasksLock;

    // Performance tracking
    TArray<float> ExecutionTimeHistory;
    int32 TotalTasksExecuted = 0;
    float TotalExecutionTime = 0.0f;

    // Internal functions
    FString GenerateTaskId() const;
    void RegisterTask(const FString& TaskId, TSharedPtr<FAuracronPCGAsyncTaskBase> Task);
    void UnregisterTask(const FString& TaskId);
    void OnTaskCompletedInternal(const FString& TaskId, const FAuracronPCGAsyncTaskResult& Result);
    void UpdatePerformanceMetrics(const FAuracronPCGAsyncTaskResult& Result);
    bool CanExecuteNewTask() const;
    void CleanupTaskInternal(const FString& TaskId);
};

// =============================================================================
// ASYNC PROCESSING UTILITIES
// =============================================================================

/**
 * Async Processing Utilities
 * Utility functions for async processing
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAsyncProcessingUtils : public UObject
{
    GENERATED_BODY()

public:
    // System information
    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static int32 GetOptimalThreadCount();

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static int32 GetAvailableMemoryMB();

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static float GetCPUUsagePercentage();

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static bool IsMultithreadingSupported();

    // Task optimization
    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static int32 CalculateOptimalBatchSize(int32 DataSize, int32 ThreadCount);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static EAuracronPCGAsyncProcessingMode RecommendProcessingMode(int32 DataSize, int32 Complexity);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static FAuracronPCGAsyncTaskDescriptor CreateOptimizedDescriptor(int32 DataSize, EAuracronPCGTaskPriority Priority);

    // Performance analysis
    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static float EstimateExecutionTime(int32 DataSize, EAuracronPCGAsyncProcessingMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static int32 EstimateMemoryUsage(int32 DataSize, EAuracronPCGAsyncProcessingMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static bool WillTaskFitInMemory(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize);

    // Debugging utilities
    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static void EnableAsyncDebugging(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static bool IsAsyncDebuggingEnabled();

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static FString GeneratePerformanceReport();

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static void LogSystemInfo();

    // Validation
    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static bool ValidateTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor, TArray<FString>& OutErrors);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static bool IsTaskDescriptorOptimal(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize);

    UFUNCTION(BlueprintCallable, Category = "Async Processing Utils")
    static FAuracronPCGAsyncTaskDescriptor OptimizeTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize);
};
