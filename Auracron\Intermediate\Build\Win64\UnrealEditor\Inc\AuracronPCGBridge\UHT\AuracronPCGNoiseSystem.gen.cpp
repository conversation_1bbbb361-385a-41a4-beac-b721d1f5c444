// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGNoiseSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGNoiseSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGAdvancedNoiseType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType;
static UEnum* EAuracronPCGAdvancedNoiseType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGAdvancedNoiseType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAdvancedNoiseType>()
{
	return EAuracronPCGAdvancedNoiseType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Billow.DisplayName", "Billow Noise" },
		{ "Billow.Name", "EAuracronPCGAdvancedNoiseType::Billow" },
		{ "BlueprintType", "true" },
		{ "Cellular.DisplayName", "Cellular Noise" },
		{ "Cellular.Name", "EAuracronPCGAdvancedNoiseType::Cellular" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise types\n" },
#endif
		{ "Curl.DisplayName", "Curl Noise" },
		{ "Curl.Name", "EAuracronPCGAdvancedNoiseType::Curl" },
		{ "Custom.DisplayName", "Custom Noise" },
		{ "Custom.Name", "EAuracronPCGAdvancedNoiseType::Custom" },
		{ "Fractal.DisplayName", "Fractal Noise" },
		{ "Fractal.Name", "EAuracronPCGAdvancedNoiseType::Fractal" },
		{ "Gradient.DisplayName", "Gradient Noise" },
		{ "Gradient.Name", "EAuracronPCGAdvancedNoiseType::Gradient" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "Perlin.DisplayName", "Perlin Noise" },
		{ "Perlin.Name", "EAuracronPCGAdvancedNoiseType::Perlin" },
		{ "Ridge.DisplayName", "Ridge Noise" },
		{ "Ridge.Name", "EAuracronPCGAdvancedNoiseType::Ridge" },
		{ "Simplex.DisplayName", "Simplex Noise" },
		{ "Simplex.Name", "EAuracronPCGAdvancedNoiseType::Simplex" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise types" },
#endif
		{ "Value.DisplayName", "Value Noise" },
		{ "Value.Name", "EAuracronPCGAdvancedNoiseType::Value" },
		{ "Voronoi.DisplayName", "Voronoi Noise" },
		{ "Voronoi.Name", "EAuracronPCGAdvancedNoiseType::Voronoi" },
		{ "Worley.DisplayName", "Worley Noise" },
		{ "Worley.Name", "EAuracronPCGAdvancedNoiseType::Worley" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGAdvancedNoiseType::Perlin", (int64)EAuracronPCGAdvancedNoiseType::Perlin },
		{ "EAuracronPCGAdvancedNoiseType::Simplex", (int64)EAuracronPCGAdvancedNoiseType::Simplex },
		{ "EAuracronPCGAdvancedNoiseType::Worley", (int64)EAuracronPCGAdvancedNoiseType::Worley },
		{ "EAuracronPCGAdvancedNoiseType::Ridge", (int64)EAuracronPCGAdvancedNoiseType::Ridge },
		{ "EAuracronPCGAdvancedNoiseType::Billow", (int64)EAuracronPCGAdvancedNoiseType::Billow },
		{ "EAuracronPCGAdvancedNoiseType::Voronoi", (int64)EAuracronPCGAdvancedNoiseType::Voronoi },
		{ "EAuracronPCGAdvancedNoiseType::Cellular", (int64)EAuracronPCGAdvancedNoiseType::Cellular },
		{ "EAuracronPCGAdvancedNoiseType::Value", (int64)EAuracronPCGAdvancedNoiseType::Value },
		{ "EAuracronPCGAdvancedNoiseType::Gradient", (int64)EAuracronPCGAdvancedNoiseType::Gradient },
		{ "EAuracronPCGAdvancedNoiseType::Curl", (int64)EAuracronPCGAdvancedNoiseType::Curl },
		{ "EAuracronPCGAdvancedNoiseType::Fractal", (int64)EAuracronPCGAdvancedNoiseType::Fractal },
		{ "EAuracronPCGAdvancedNoiseType::Custom", (int64)EAuracronPCGAdvancedNoiseType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGAdvancedNoiseType",
	"EAuracronPCGAdvancedNoiseType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType.InnerSingleton;
}
// ********** End Enum EAuracronPCGAdvancedNoiseType ***********************************************

// ********** Begin Enum EAuracronPCGNoiseDimension ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension;
static UEnum* EAuracronPCGNoiseDimension_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNoiseDimension"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseDimension>()
{
	return EAuracronPCGNoiseDimension_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise dimensions\n" },
#endif
		{ "FourDimensional.DisplayName", "4D" },
		{ "FourDimensional.Name", "EAuracronPCGNoiseDimension::FourDimensional" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "OneDimensional.DisplayName", "1D" },
		{ "OneDimensional.Name", "EAuracronPCGNoiseDimension::OneDimensional" },
		{ "ThreeDimensional.DisplayName", "3D" },
		{ "ThreeDimensional.Name", "EAuracronPCGNoiseDimension::ThreeDimensional" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise dimensions" },
#endif
		{ "TwoDimensional.DisplayName", "2D" },
		{ "TwoDimensional.Name", "EAuracronPCGNoiseDimension::TwoDimensional" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNoiseDimension::OneDimensional", (int64)EAuracronPCGNoiseDimension::OneDimensional },
		{ "EAuracronPCGNoiseDimension::TwoDimensional", (int64)EAuracronPCGNoiseDimension::TwoDimensional },
		{ "EAuracronPCGNoiseDimension::ThreeDimensional", (int64)EAuracronPCGNoiseDimension::ThreeDimensional },
		{ "EAuracronPCGNoiseDimension::FourDimensional", (int64)EAuracronPCGNoiseDimension::FourDimensional },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNoiseDimension",
	"EAuracronPCGNoiseDimension",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension.InnerSingleton;
}
// ********** End Enum EAuracronPCGNoiseDimension **************************************************

// ********** Begin Enum EAuracronPCGFractalType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGFractalType;
static UEnum* EAuracronPCGFractalType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGFractalType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGFractalType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGFractalType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGFractalType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGFractalType>()
{
	return EAuracronPCGFractalType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Billow.DisplayName", "Billow" },
		{ "Billow.Name", "EAuracronPCGFractalType::Billow" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fractal types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGFractalType::Custom" },
		{ "DomainWarp.DisplayName", "Domain Warp" },
		{ "DomainWarp.Name", "EAuracronPCGFractalType::DomainWarp" },
		{ "FBM.DisplayName", "Fractional Brownian Motion" },
		{ "FBM.Name", "EAuracronPCGFractalType::FBM" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronPCGFractalType::None" },
		{ "PingPong.DisplayName", "Ping Pong" },
		{ "PingPong.Name", "EAuracronPCGFractalType::PingPong" },
		{ "RidgedMulti.DisplayName", "Ridged Multifractal" },
		{ "RidgedMulti.Name", "EAuracronPCGFractalType::RidgedMulti" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fractal types" },
#endif
		{ "Turbulence.DisplayName", "Turbulence" },
		{ "Turbulence.Name", "EAuracronPCGFractalType::Turbulence" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGFractalType::None", (int64)EAuracronPCGFractalType::None },
		{ "EAuracronPCGFractalType::FBM", (int64)EAuracronPCGFractalType::FBM },
		{ "EAuracronPCGFractalType::Turbulence", (int64)EAuracronPCGFractalType::Turbulence },
		{ "EAuracronPCGFractalType::RidgedMulti", (int64)EAuracronPCGFractalType::RidgedMulti },
		{ "EAuracronPCGFractalType::Billow", (int64)EAuracronPCGFractalType::Billow },
		{ "EAuracronPCGFractalType::PingPong", (int64)EAuracronPCGFractalType::PingPong },
		{ "EAuracronPCGFractalType::DomainWarp", (int64)EAuracronPCGFractalType::DomainWarp },
		{ "EAuracronPCGFractalType::Custom", (int64)EAuracronPCGFractalType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGFractalType",
	"EAuracronPCGFractalType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGFractalType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGFractalType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGFractalType.InnerSingleton;
}
// ********** End Enum EAuracronPCGFractalType *****************************************************

// ********** Begin Enum EAuracronPCGNoiseInterpolation ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation;
static UEnum* EAuracronPCGNoiseInterpolation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNoiseInterpolation"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseInterpolation>()
{
	return EAuracronPCGNoiseInterpolation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interpolation types\n" },
#endif
		{ "Cosine.DisplayName", "Cosine" },
		{ "Cosine.Name", "EAuracronPCGNoiseInterpolation::Cosine" },
		{ "Cubic.DisplayName", "Cubic" },
		{ "Cubic.Name", "EAuracronPCGNoiseInterpolation::Cubic" },
		{ "Hermite.DisplayName", "Hermite" },
		{ "Hermite.Name", "EAuracronPCGNoiseInterpolation::Hermite" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EAuracronPCGNoiseInterpolation::Linear" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "Quintic.DisplayName", "Quintic" },
		{ "Quintic.Name", "EAuracronPCGNoiseInterpolation::Quintic" },
		{ "Smootherstep.DisplayName", "Smootherstep" },
		{ "Smootherstep.Name", "EAuracronPCGNoiseInterpolation::Smootherstep" },
		{ "Smoothstep.DisplayName", "Smoothstep" },
		{ "Smoothstep.Name", "EAuracronPCGNoiseInterpolation::Smoothstep" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interpolation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNoiseInterpolation::Linear", (int64)EAuracronPCGNoiseInterpolation::Linear },
		{ "EAuracronPCGNoiseInterpolation::Hermite", (int64)EAuracronPCGNoiseInterpolation::Hermite },
		{ "EAuracronPCGNoiseInterpolation::Quintic", (int64)EAuracronPCGNoiseInterpolation::Quintic },
		{ "EAuracronPCGNoiseInterpolation::Cosine", (int64)EAuracronPCGNoiseInterpolation::Cosine },
		{ "EAuracronPCGNoiseInterpolation::Cubic", (int64)EAuracronPCGNoiseInterpolation::Cubic },
		{ "EAuracronPCGNoiseInterpolation::Smoothstep", (int64)EAuracronPCGNoiseInterpolation::Smoothstep },
		{ "EAuracronPCGNoiseInterpolation::Smootherstep", (int64)EAuracronPCGNoiseInterpolation::Smootherstep },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNoiseInterpolation",
	"EAuracronPCGNoiseInterpolation",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation.InnerSingleton;
}
// ********** End Enum EAuracronPCGNoiseInterpolation **********************************************

// ********** Begin Enum EAuracronPCGWorleyDistanceFunction ****************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction;
static UEnum* EAuracronPCGWorleyDistanceFunction_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGWorleyDistanceFunction"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGWorleyDistanceFunction>()
{
	return EAuracronPCGWorleyDistanceFunction_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chebyshev.DisplayName", "Chebyshev" },
		{ "Chebyshev.Name", "EAuracronPCGWorleyDistanceFunction::Chebyshev" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance functions for Worley noise\n" },
#endif
		{ "Euclidean.DisplayName", "Euclidean" },
		{ "Euclidean.Name", "EAuracronPCGWorleyDistanceFunction::Euclidean" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronPCGWorleyDistanceFunction::Hybrid" },
		{ "Manhattan.DisplayName", "Manhattan" },
		{ "Manhattan.Name", "EAuracronPCGWorleyDistanceFunction::Manhattan" },
		{ "Minkowski.DisplayName", "Minkowski" },
		{ "Minkowski.Name", "EAuracronPCGWorleyDistanceFunction::Minkowski" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "Natural.DisplayName", "Natural" },
		{ "Natural.Name", "EAuracronPCGWorleyDistanceFunction::Natural" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance functions for Worley noise" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGWorleyDistanceFunction::Euclidean", (int64)EAuracronPCGWorleyDistanceFunction::Euclidean },
		{ "EAuracronPCGWorleyDistanceFunction::Manhattan", (int64)EAuracronPCGWorleyDistanceFunction::Manhattan },
		{ "EAuracronPCGWorleyDistanceFunction::Chebyshev", (int64)EAuracronPCGWorleyDistanceFunction::Chebyshev },
		{ "EAuracronPCGWorleyDistanceFunction::Minkowski", (int64)EAuracronPCGWorleyDistanceFunction::Minkowski },
		{ "EAuracronPCGWorleyDistanceFunction::Natural", (int64)EAuracronPCGWorleyDistanceFunction::Natural },
		{ "EAuracronPCGWorleyDistanceFunction::Hybrid", (int64)EAuracronPCGWorleyDistanceFunction::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGWorleyDistanceFunction",
	"EAuracronPCGWorleyDistanceFunction",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction.InnerSingleton;
}
// ********** End Enum EAuracronPCGWorleyDistanceFunction ******************************************

// ********** Begin Enum EAuracronPCGRandomizationMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode;
static UEnum* EAuracronPCGRandomizationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGRandomizationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGRandomizationMode>()
{
	return EAuracronPCGRandomizationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AttributeBased.DisplayName", "Attribute Based" },
		{ "AttributeBased.Name", "EAuracronPCGRandomizationMode::AttributeBased" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Randomization modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGRandomizationMode::Custom" },
		{ "Deterministic.DisplayName", "Deterministic" },
		{ "Deterministic.Name", "EAuracronPCGRandomizationMode::Deterministic" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronPCGRandomizationMode::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "PositionBased.DisplayName", "Position Based" },
		{ "PositionBased.Name", "EAuracronPCGRandomizationMode::PositionBased" },
		{ "SeedBased.DisplayName", "Seed Based" },
		{ "SeedBased.Name", "EAuracronPCGRandomizationMode::SeedBased" },
		{ "TimeBased.DisplayName", "Time Based" },
		{ "TimeBased.Name", "EAuracronPCGRandomizationMode::TimeBased" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Randomization modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGRandomizationMode::Deterministic", (int64)EAuracronPCGRandomizationMode::Deterministic },
		{ "EAuracronPCGRandomizationMode::SeedBased", (int64)EAuracronPCGRandomizationMode::SeedBased },
		{ "EAuracronPCGRandomizationMode::TimeBased", (int64)EAuracronPCGRandomizationMode::TimeBased },
		{ "EAuracronPCGRandomizationMode::PositionBased", (int64)EAuracronPCGRandomizationMode::PositionBased },
		{ "EAuracronPCGRandomizationMode::AttributeBased", (int64)EAuracronPCGRandomizationMode::AttributeBased },
		{ "EAuracronPCGRandomizationMode::Hybrid", (int64)EAuracronPCGRandomizationMode::Hybrid },
		{ "EAuracronPCGRandomizationMode::Custom", (int64)EAuracronPCGRandomizationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGRandomizationMode",
	"EAuracronPCGRandomizationMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGRandomizationMode ***********************************************

// ********** Begin ScriptStruct FAuracronPCGNoiseDescriptor ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor;
class UScriptStruct* FAuracronPCGNoiseDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGNoiseDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Noise Descriptor\n * Describes parameters for noise generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise Descriptor\nDescribes parameters for noise generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dimension_MetaData[] = {
		{ "Category", "Noise" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Frequency_MetaData[] = {
		{ "Category", "Basic Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Amplitude_MetaData[] = {
		{ "Category", "Basic Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Basic Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "Category", "Basic Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FractalType_MetaData[] = {
		{ "Category", "Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Octaves_MetaData[] = {
		{ "Category", "Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Lacunarity_MetaData[] = {
		{ "Category", "Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Persistence_MetaData[] = {
		{ "Category", "Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gain_MetaData[] = {
		{ "Category", "Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterpolationType_MetaData[] = {
		{ "Category", "Interpolation" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceFunction_MetaData[] = {
		{ "Category", "Worley" },
		{ "EditCondition", "NoiseType == EAuracronPCGAdvancedNoiseType::Worley" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Jitter_MetaData[] = {
		{ "Category", "Worley" },
		{ "EditCondition", "NoiseType == EAuracronPCGAdvancedNoiseType::Worley" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputMin_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputMax_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvert_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAbsolute_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTiling_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileSize_MetaData[] = {
		{ "Category", "Advanced" },
		{ "EditCondition", "bTiling" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalize_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Dimension_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Dimension;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FractalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FractalType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Lacunarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Gain;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterpolationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterpolationType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistanceFunction_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistanceFunction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Jitter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputMin;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputMax;
	static void NewProp_bInvert_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvert;
	static void NewProp_bAbsolute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAbsolute;
	static void NewProp_bTiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTiling;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TileSize;
	static void NewProp_bNormalize_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGNoiseDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 1494480122
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Dimension_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Dimension = { "Dimension", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Dimension), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseDimension, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dimension_MetaData), NewProp_Dimension_MetaData) }; // 747764361
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Frequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Frequency_MetaData), NewProp_Frequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Amplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Amplitude_MetaData), NewProp_Amplitude_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_FractalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_FractalType = { "FractalType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, FractalType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGFractalType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FractalType_MetaData), NewProp_FractalType_MetaData) }; // 2136469556
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Octaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Octaves_MetaData), NewProp_Octaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Lacunarity = { "Lacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Lacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Lacunarity_MetaData), NewProp_Lacunarity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Persistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Persistence_MetaData), NewProp_Persistence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Gain = { "Gain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Gain), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gain_MetaData), NewProp_Gain_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_InterpolationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_InterpolationType = { "InterpolationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, InterpolationType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterpolationType_MetaData), NewProp_InterpolationType_MetaData) }; // 2594972691
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_DistanceFunction_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_DistanceFunction = { "DistanceFunction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, DistanceFunction), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceFunction_MetaData), NewProp_DistanceFunction_MetaData) }; // 3186219053
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Jitter = { "Jitter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, Jitter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Jitter_MetaData), NewProp_Jitter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_OutputMin = { "OutputMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, OutputMin), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputMin_MetaData), NewProp_OutputMin_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_OutputMax = { "OutputMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, OutputMax), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputMax_MetaData), NewProp_OutputMax_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bInvert_SetBit(void* Obj)
{
	((FAuracronPCGNoiseDescriptor*)Obj)->bInvert = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bInvert = { "bInvert", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNoiseDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bInvert_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvert_MetaData), NewProp_bInvert_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bAbsolute_SetBit(void* Obj)
{
	((FAuracronPCGNoiseDescriptor*)Obj)->bAbsolute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bAbsolute = { "bAbsolute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNoiseDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bAbsolute_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAbsolute_MetaData), NewProp_bAbsolute_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bTiling_SetBit(void* Obj)
{
	((FAuracronPCGNoiseDescriptor*)Obj)->bTiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bTiling = { "bTiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNoiseDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bTiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTiling_MetaData), NewProp_bTiling_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_TileSize = { "TileSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGNoiseDescriptor, TileSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileSize_MetaData), NewProp_TileSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bNormalize_SetBit(void* Obj)
{
	((FAuracronPCGNoiseDescriptor*)Obj)->bNormalize = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bNormalize = { "bNormalize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGNoiseDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bNormalize_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalize_MetaData), NewProp_bNormalize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Dimension_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Dimension,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_FractalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_FractalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Lacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Gain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_InterpolationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_InterpolationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_DistanceFunction_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_DistanceFunction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_Jitter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_OutputMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_OutputMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bInvert,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bAbsolute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bTiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_TileSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewProp_bNormalize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGNoiseDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGNoiseDescriptor),
	alignof(FAuracronPCGNoiseDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGNoiseDescriptor *****************************************

// ********** Begin ScriptStruct FAuracronPCGRandomizationDescriptor *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor;
class UScriptStruct* FAuracronPCGRandomizationDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGRandomizationDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Randomization Descriptor\n * Describes parameters for randomization operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Randomization Descriptor\nDescribes parameters for randomization operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomizationMode_MetaData[] = {
		{ "Category", "Randomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseSeed_MetaData[] = {
		{ "Category", "Randomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGlobalSeed_MetaData[] = {
		{ "Category", "Randomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPerPointSeed_MetaData[] = {
		{ "Category", "Randomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUniformDistribution_MetaData[] = {
		{ "Category", "Distribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGaussianDistribution_MetaData[] = {
		{ "Category", "Distribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GaussianMean_MetaData[] = {
		{ "Category", "Distribution" },
		{ "EditCondition", "bGaussianDistribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GaussianStdDev_MetaData[] = {
		{ "Category", "Distribution" },
		{ "EditCondition", "bGaussianDistribution" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomRange_MetaData[] = {
		{ "Category", "Range" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bClampOutput_MetaData[] = {
		{ "Category", "Range" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDeterministicOrder_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeedAttribute_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSeedAttribute_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RandomizationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RandomizationMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BaseSeed;
	static void NewProp_bUseGlobalSeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGlobalSeed;
	static void NewProp_bPerPointSeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPerPointSeed;
	static void NewProp_bUniformDistribution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUniformDistribution;
	static void NewProp_bGaussianDistribution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGaussianDistribution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GaussianMean;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GaussianStdDev;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RandomRange;
	static void NewProp_bClampOutput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClampOutput;
	static void NewProp_bDeterministicOrder_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDeterministicOrder;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SeedAttribute;
	static void NewProp_bUseSeedAttribute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSeedAttribute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGRandomizationDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomizationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomizationMode = { "RandomizationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, RandomizationMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGRandomizationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomizationMode_MetaData), NewProp_RandomizationMode_MetaData) }; // 442684380
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_BaseSeed = { "BaseSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, BaseSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseSeed_MetaData), NewProp_BaseSeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseGlobalSeed_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bUseGlobalSeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseGlobalSeed = { "bUseGlobalSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseGlobalSeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGlobalSeed_MetaData), NewProp_bUseGlobalSeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bPerPointSeed_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bPerPointSeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bPerPointSeed = { "bPerPointSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bPerPointSeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPerPointSeed_MetaData), NewProp_bPerPointSeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUniformDistribution_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bUniformDistribution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUniformDistribution = { "bUniformDistribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUniformDistribution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUniformDistribution_MetaData), NewProp_bUniformDistribution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bGaussianDistribution_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bGaussianDistribution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bGaussianDistribution = { "bGaussianDistribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bGaussianDistribution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGaussianDistribution_MetaData), NewProp_bGaussianDistribution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_GaussianMean = { "GaussianMean", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, GaussianMean), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GaussianMean_MetaData), NewProp_GaussianMean_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_GaussianStdDev = { "GaussianStdDev", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, GaussianStdDev), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GaussianStdDev_MetaData), NewProp_GaussianStdDev_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomRange = { "RandomRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, RandomRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomRange_MetaData), NewProp_RandomRange_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bClampOutput_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bClampOutput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bClampOutput = { "bClampOutput", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bClampOutput_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bClampOutput_MetaData), NewProp_bClampOutput_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bDeterministicOrder_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bDeterministicOrder = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bDeterministicOrder = { "bDeterministicOrder", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bDeterministicOrder_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDeterministicOrder_MetaData), NewProp_bDeterministicOrder_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_SeedAttribute = { "SeedAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGRandomizationDescriptor, SeedAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeedAttribute_MetaData), NewProp_SeedAttribute_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseSeedAttribute_SetBit(void* Obj)
{
	((FAuracronPCGRandomizationDescriptor*)Obj)->bUseSeedAttribute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseSeedAttribute = { "bUseSeedAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGRandomizationDescriptor), &Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseSeedAttribute_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSeedAttribute_MetaData), NewProp_bUseSeedAttribute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomizationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomizationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_BaseSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseGlobalSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bPerPointSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUniformDistribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bGaussianDistribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_GaussianMean,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_GaussianStdDev,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_RandomRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bClampOutput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bDeterministicOrder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_SeedAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewProp_bUseSeedAttribute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGRandomizationDescriptor",
	Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::PropPointers),
	sizeof(FAuracronPCGRandomizationDescriptor),
	alignof(FAuracronPCGRandomizationDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGRandomizationDescriptor *********************************

// ********** Begin Class UAuracronPCGAdvancedNoiseGeneratorSettings *******************************
void UAuracronPCGAdvancedNoiseGeneratorSettings::StaticRegisterNativesUAuracronPCGAdvancedNoiseGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings;
UClass* UAuracronPCGAdvancedNoiseGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedNoiseGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedNoiseGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedNoiseGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_NoRegister()
{
	return UAuracronPCGAdvancedNoiseGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGNoiseSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptor_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputAttribute_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputToPosition_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputToScale_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputToRotation_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputToColor_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputToDensity_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateVectorNoise_MetaData[] = {
		{ "Category", "Multi-Channel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Multi-channel output\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multi-channel output" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptorY_MetaData[] = {
		{ "Category", "Multi-Channel" },
		{ "EditCondition", "bGenerateVectorNoise" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptorZ_MetaData[] = {
		{ "Category", "Multi-Channel" },
		{ "EditCondition", "bGenerateVectorNoise" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDomainWarp_MetaData[] = {
		{ "Category", "Domain Warp" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Domain warping\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Domain warping" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DomainWarpDescriptor_MetaData[] = {
		{ "Category", "Domain Warp" },
		{ "EditCondition", "bUseDomainWarp" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DomainWarpStrength_MetaData[] = {
		{ "Category", "Domain Warp" },
		{ "EditCondition", "bUseDomainWarp" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUAcceleration_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheSize_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// bCacheResults is inherited from UAuracronPCGNodeSettings, no need to redeclare\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "bCacheResults is inherited from UAuracronPCGNodeSettings, no need to redeclare" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputAttribute;
	static void NewProp_bOutputToPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputToPosition;
	static void NewProp_bOutputToScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputToScale;
	static void NewProp_bOutputToRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputToRotation;
	static void NewProp_bOutputToColor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputToColor;
	static void NewProp_bOutputToDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputToDensity;
	static void NewProp_bGenerateVectorNoise_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateVectorNoise;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptorY;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptorZ;
	static void NewProp_bUseDomainWarp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDomainWarp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DomainWarpDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DomainWarpStrength;
	static void NewProp_bUseGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUAcceleration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedNoiseGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptor = { "NoiseDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, NoiseDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptor_MetaData), NewProp_NoiseDescriptor_MetaData) }; // 218319303
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_OutputAttribute = { "OutputAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, OutputAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputAttribute_MetaData), NewProp_OutputAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToPosition_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bOutputToPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToPosition = { "bOutputToPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputToPosition_MetaData), NewProp_bOutputToPosition_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToScale_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bOutputToScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToScale = { "bOutputToScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputToScale_MetaData), NewProp_bOutputToScale_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToRotation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bOutputToRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToRotation = { "bOutputToRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputToRotation_MetaData), NewProp_bOutputToRotation_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToColor_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bOutputToColor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToColor = { "bOutputToColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToColor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputToColor_MetaData), NewProp_bOutputToColor_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bOutputToDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToDensity = { "bOutputToDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputToDensity_MetaData), NewProp_bOutputToDensity_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bGenerateVectorNoise_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bGenerateVectorNoise = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bGenerateVectorNoise = { "bGenerateVectorNoise", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bGenerateVectorNoise_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateVectorNoise_MetaData), NewProp_bGenerateVectorNoise_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptorY = { "NoiseDescriptorY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, NoiseDescriptorY), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptorY_MetaData), NewProp_NoiseDescriptorY_MetaData) }; // 218319303
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptorZ = { "NoiseDescriptorZ", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, NoiseDescriptorZ), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptorZ_MetaData), NewProp_NoiseDescriptorZ_MetaData) }; // 218319303
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseDomainWarp_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bUseDomainWarp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseDomainWarp = { "bUseDomainWarp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseDomainWarp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDomainWarp_MetaData), NewProp_bUseDomainWarp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_DomainWarpDescriptor = { "DomainWarpDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, DomainWarpDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DomainWarpDescriptor_MetaData), NewProp_DomainWarpDescriptor_MetaData) }; // 218319303
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_DomainWarpStrength = { "DomainWarpStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, DomainWarpStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DomainWarpStrength_MetaData), NewProp_DomainWarpStrength_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseGPUAcceleration_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedNoiseGeneratorSettings*)Obj)->bUseGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseGPUAcceleration = { "bUseGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), &Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUAcceleration_MetaData), NewProp_bUseGPUAcceleration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_CacheSize = { "CacheSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedNoiseGeneratorSettings, CacheSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheSize_MetaData), NewProp_CacheSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_OutputAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bOutputToDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bGenerateVectorNoise,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptorY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_NoiseDescriptorZ,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseDomainWarp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_DomainWarpDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_DomainWarpStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_bUseGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::NewProp_CacheSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedNoiseGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedNoiseGeneratorSettings);
UAuracronPCGAdvancedNoiseGeneratorSettings::~UAuracronPCGAdvancedNoiseGeneratorSettings() {}
// ********** End Class UAuracronPCGAdvancedNoiseGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGAdvancedRandomizerSettings ***********************************
void UAuracronPCGAdvancedRandomizerSettings::StaticRegisterNativesUAuracronPCGAdvancedRandomizerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings;
UClass* UAuracronPCGAdvancedRandomizerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedRandomizerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedRandomizerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedRandomizerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_NoRegister()
{
	return UAuracronPCGAdvancedRandomizerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGNoiseSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomizationDescriptor_MetaData[] = {
		{ "Category", "Randomization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Randomization configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Randomization configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetAttributes_MetaData[] = {
		{ "Category", "Target Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target attributes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target attributes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizePosition_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionRange_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "EditCondition", "bRandomizePosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeRotation_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationRange_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "EditCondition", "bRandomizeRotation" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeScale_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleRange_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "EditCondition", "bRandomizeScale" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeColor_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeDensity_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityRange_MetaData[] = {
		{ "Category", "Target Attributes" },
		{ "EditCondition", "bRandomizeDensity" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWeightedRandomization_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced randomization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced randomization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeightAttribute_MetaData[] = {
		{ "Category", "Advanced" },
		{ "EditCondition", "bUseWeightedRandomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseConditionalRandomization_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionAttribute_MetaData[] = {
		{ "Category", "Advanced" },
		{ "EditCondition", "bUseConditionalRandomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConditionRange_MetaData[] = {
		{ "Category", "Advanced" },
		{ "EditCondition", "bUseConditionalRandomization" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputRandomSeed_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputStatistics_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RandomizationDescriptor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetAttributes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetAttributes;
	static void NewProp_bRandomizePosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizePosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionRange;
	static void NewProp_bRandomizeRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationRange;
	static void NewProp_bRandomizeScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleRange;
	static void NewProp_bRandomizeColor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeColor;
	static void NewProp_bRandomizeDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityRange;
	static void NewProp_bUseWeightedRandomization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWeightedRandomization;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeightAttribute;
	static void NewProp_bUseConditionalRandomization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseConditionalRandomization;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConditionAttribute;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConditionRange;
	static void NewProp_bOutputRandomSeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputRandomSeed;
	static void NewProp_bOutputStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputStatistics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedRandomizerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_RandomizationDescriptor = { "RandomizationDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, RandomizationDescriptor), Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomizationDescriptor_MetaData), NewProp_RandomizationDescriptor_MetaData) }; // 40374615
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_TargetAttributes_Inner = { "TargetAttributes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_TargetAttributes = { "TargetAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, TargetAttributes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetAttributes_MetaData), NewProp_TargetAttributes_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizePosition_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bRandomizePosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizePosition = { "bRandomizePosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizePosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizePosition_MetaData), NewProp_bRandomizePosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_PositionRange = { "PositionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, PositionRange), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionRange_MetaData), NewProp_PositionRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeRotation_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bRandomizeRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeRotation = { "bRandomizeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeRotation_MetaData), NewProp_bRandomizeRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_RotationRange = { "RotationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, RotationRange), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationRange_MetaData), NewProp_RotationRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeScale_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bRandomizeScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeScale = { "bRandomizeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeScale_MetaData), NewProp_bRandomizeScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ScaleRange = { "ScaleRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, ScaleRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleRange_MetaData), NewProp_ScaleRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeColor_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bRandomizeColor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeColor = { "bRandomizeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeColor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeColor_MetaData), NewProp_bRandomizeColor_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bRandomizeDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeDensity = { "bRandomizeDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeDensity_MetaData), NewProp_bRandomizeDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_DensityRange = { "DensityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, DensityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityRange_MetaData), NewProp_DensityRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseWeightedRandomization_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bUseWeightedRandomization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseWeightedRandomization = { "bUseWeightedRandomization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseWeightedRandomization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWeightedRandomization_MetaData), NewProp_bUseWeightedRandomization_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_WeightAttribute = { "WeightAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, WeightAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeightAttribute_MetaData), NewProp_WeightAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseConditionalRandomization_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bUseConditionalRandomization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseConditionalRandomization = { "bUseConditionalRandomization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseConditionalRandomization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseConditionalRandomization_MetaData), NewProp_bUseConditionalRandomization_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ConditionAttribute = { "ConditionAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, ConditionAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionAttribute_MetaData), NewProp_ConditionAttribute_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ConditionRange = { "ConditionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedRandomizerSettings, ConditionRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConditionRange_MetaData), NewProp_ConditionRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputRandomSeed_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bOutputRandomSeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputRandomSeed = { "bOutputRandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputRandomSeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputRandomSeed_MetaData), NewProp_bOutputRandomSeed_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputStatistics_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedRandomizerSettings*)Obj)->bOutputStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputStatistics = { "bOutputStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedRandomizerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputStatistics_MetaData), NewProp_bOutputStatistics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_RandomizationDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_TargetAttributes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_TargetAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_PositionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_RotationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ScaleRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bRandomizeDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_DensityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseWeightedRandomization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_WeightAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bUseConditionalRandomization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ConditionAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_ConditionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputRandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::NewProp_bOutputStatistics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedRandomizerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedRandomizerSettings);
UAuracronPCGAdvancedRandomizerSettings::~UAuracronPCGAdvancedRandomizerSettings() {}
// ********** End Class UAuracronPCGAdvancedRandomizerSettings *************************************

// ********** Begin Enum EAuracronPCGNoiseCombineMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode;
static UEnum* EAuracronPCGNoiseCombineMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNoiseCombineMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseCombineMode>()
{
	return EAuracronPCGNoiseCombineMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Add.DisplayName", "Add" },
		{ "Add.Name", "EAuracronPCGNoiseCombineMode::Add" },
		{ "Average.DisplayName", "Average" },
		{ "Average.Name", "EAuracronPCGNoiseCombineMode::Average" },
		{ "BlueprintType", "true" },
		{ "ColorBurn.DisplayName", "Color Burn" },
		{ "ColorBurn.Name", "EAuracronPCGNoiseCombineMode::ColorBurn" },
		{ "ColorDodge.DisplayName", "Color Dodge" },
		{ "ColorDodge.Name", "EAuracronPCGNoiseCombineMode::ColorDodge" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGNoiseCombineMode::Custom" },
		{ "Difference.DisplayName", "Difference" },
		{ "Difference.Name", "EAuracronPCGNoiseCombineMode::Difference" },
		{ "Divide.DisplayName", "Divide" },
		{ "Divide.Name", "EAuracronPCGNoiseCombineMode::Divide" },
		{ "Exclusion.DisplayName", "Exclusion" },
		{ "Exclusion.Name", "EAuracronPCGNoiseCombineMode::Exclusion" },
		{ "HardLight.DisplayName", "Hard Light" },
		{ "HardLight.Name", "EAuracronPCGNoiseCombineMode::HardLight" },
		{ "Max.DisplayName", "Maximum" },
		{ "Max.Name", "EAuracronPCGNoiseCombineMode::Max" },
		{ "Min.DisplayName", "Minimum" },
		{ "Min.Name", "EAuracronPCGNoiseCombineMode::Min" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
		{ "Multiply.DisplayName", "Multiply" },
		{ "Multiply.Name", "EAuracronPCGNoiseCombineMode::Multiply" },
		{ "Overlay.DisplayName", "Overlay" },
		{ "Overlay.Name", "EAuracronPCGNoiseCombineMode::Overlay" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "EAuracronPCGNoiseCombineMode::Screen" },
		{ "SoftLight.DisplayName", "Soft Light" },
		{ "SoftLight.Name", "EAuracronPCGNoiseCombineMode::SoftLight" },
		{ "Subtract.DisplayName", "Subtract" },
		{ "Subtract.Name", "EAuracronPCGNoiseCombineMode::Subtract" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNoiseCombineMode::Add", (int64)EAuracronPCGNoiseCombineMode::Add },
		{ "EAuracronPCGNoiseCombineMode::Subtract", (int64)EAuracronPCGNoiseCombineMode::Subtract },
		{ "EAuracronPCGNoiseCombineMode::Multiply", (int64)EAuracronPCGNoiseCombineMode::Multiply },
		{ "EAuracronPCGNoiseCombineMode::Divide", (int64)EAuracronPCGNoiseCombineMode::Divide },
		{ "EAuracronPCGNoiseCombineMode::Min", (int64)EAuracronPCGNoiseCombineMode::Min },
		{ "EAuracronPCGNoiseCombineMode::Max", (int64)EAuracronPCGNoiseCombineMode::Max },
		{ "EAuracronPCGNoiseCombineMode::Average", (int64)EAuracronPCGNoiseCombineMode::Average },
		{ "EAuracronPCGNoiseCombineMode::Screen", (int64)EAuracronPCGNoiseCombineMode::Screen },
		{ "EAuracronPCGNoiseCombineMode::Overlay", (int64)EAuracronPCGNoiseCombineMode::Overlay },
		{ "EAuracronPCGNoiseCombineMode::SoftLight", (int64)EAuracronPCGNoiseCombineMode::SoftLight },
		{ "EAuracronPCGNoiseCombineMode::HardLight", (int64)EAuracronPCGNoiseCombineMode::HardLight },
		{ "EAuracronPCGNoiseCombineMode::ColorDodge", (int64)EAuracronPCGNoiseCombineMode::ColorDodge },
		{ "EAuracronPCGNoiseCombineMode::ColorBurn", (int64)EAuracronPCGNoiseCombineMode::ColorBurn },
		{ "EAuracronPCGNoiseCombineMode::Difference", (int64)EAuracronPCGNoiseCombineMode::Difference },
		{ "EAuracronPCGNoiseCombineMode::Exclusion", (int64)EAuracronPCGNoiseCombineMode::Exclusion },
		{ "EAuracronPCGNoiseCombineMode::Custom", (int64)EAuracronPCGNoiseCombineMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNoiseCombineMode",
	"EAuracronPCGNoiseCombineMode",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode.InnerSingleton;
}
// ********** End Enum EAuracronPCGNoiseCombineMode ************************************************

// ********** Begin Class UAuracronPCGNoiseCombinerSettings ****************************************
void UAuracronPCGNoiseCombinerSettings::StaticRegisterNativesUAuracronPCGNoiseCombinerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings;
UClass* UAuracronPCGNoiseCombinerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGNoiseCombinerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGNoiseCombinerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGNoiseCombinerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_NoRegister()
{
	return UAuracronPCGNoiseCombinerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGNoiseSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptors_MetaData[] = {
		{ "Category", "Noise Sources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise sources\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise sources" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseWeights_MetaData[] = {
		{ "Category", "Noise Sources" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombineMode_MetaData[] = {
		{ "Category", "Combination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combination settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combination settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNormalizeResult_MetaData[] = {
		{ "Category", "Combination" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombinationStrength_MetaData[] = {
		{ "Category", "Combination" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMask_MetaData[] = {
		{ "Category", "Masking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Masking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Masking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaskDescriptor_MetaData[] = {
		{ "Category", "Masking" },
		{ "EditCondition", "bUseMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertMask_MetaData[] = {
		{ "Category", "Masking" },
		{ "EditCondition", "bUseMask" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputAttribute_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputIndividualNoises_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NoiseDescriptors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NoiseWeights;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CombineMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CombineMode;
	static void NewProp_bNormalizeResult_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNormalizeResult;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CombinationStrength;
	static void NewProp_bUseMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMask;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaskDescriptor;
	static void NewProp_bInvertMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertMask;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputAttribute;
	static void NewProp_bOutputIndividualNoises_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputIndividualNoises;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGNoiseCombinerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseDescriptors_Inner = { "NoiseDescriptors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(0, nullptr) }; // 218319303
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseDescriptors = { "NoiseDescriptors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, NoiseDescriptors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptors_MetaData), NewProp_NoiseDescriptors_MetaData) }; // 218319303
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseWeights_Inner = { "NoiseWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseWeights = { "NoiseWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, NoiseWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseWeights_MetaData), NewProp_NoiseWeights_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombineMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombineMode = { "CombineMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, CombineMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombineMode_MetaData), NewProp_CombineMode_MetaData) }; // 4176871008
void Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bNormalizeResult_SetBit(void* Obj)
{
	((UAuracronPCGNoiseCombinerSettings*)Obj)->bNormalizeResult = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bNormalizeResult = { "bNormalizeResult", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseCombinerSettings), &Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bNormalizeResult_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNormalizeResult_MetaData), NewProp_bNormalizeResult_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombinationStrength = { "CombinationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, CombinationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombinationStrength_MetaData), NewProp_CombinationStrength_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bUseMask_SetBit(void* Obj)
{
	((UAuracronPCGNoiseCombinerSettings*)Obj)->bUseMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bUseMask = { "bUseMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseCombinerSettings), &Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bUseMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMask_MetaData), NewProp_bUseMask_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_MaskDescriptor = { "MaskDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, MaskDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaskDescriptor_MetaData), NewProp_MaskDescriptor_MetaData) }; // 218319303
void Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bInvertMask_SetBit(void* Obj)
{
	((UAuracronPCGNoiseCombinerSettings*)Obj)->bInvertMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bInvertMask = { "bInvertMask", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseCombinerSettings), &Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bInvertMask_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertMask_MetaData), NewProp_bInvertMask_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_OutputAttribute = { "OutputAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseCombinerSettings, OutputAttribute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputAttribute_MetaData), NewProp_OutputAttribute_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bOutputIndividualNoises_SetBit(void* Obj)
{
	((UAuracronPCGNoiseCombinerSettings*)Obj)->bOutputIndividualNoises = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bOutputIndividualNoises = { "bOutputIndividualNoises", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseCombinerSettings), &Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bOutputIndividualNoises_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputIndividualNoises_MetaData), NewProp_bOutputIndividualNoises_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseDescriptors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseDescriptors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_NoiseWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombineMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombineMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bNormalizeResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_CombinationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bUseMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_MaskDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bInvertMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_OutputAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::NewProp_bOutputIndividualNoises,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::ClassParams = {
	&UAuracronPCGNoiseCombinerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGNoiseCombinerSettings);
UAuracronPCGNoiseCombinerSettings::~UAuracronPCGNoiseCombinerSettings() {}
// ********** End Class UAuracronPCGNoiseCombinerSettings ******************************************

// ********** Begin Class UAuracronPCGNoiseFieldGeneratorSettings **********************************
void UAuracronPCGNoiseFieldGeneratorSettings::StaticRegisterNativesUAuracronPCGNoiseFieldGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings;
UClass* UAuracronPCGNoiseFieldGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGNoiseFieldGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGNoiseFieldGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGNoiseFieldGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_NoRegister()
{
	return UAuracronPCGNoiseFieldGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGNoiseSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSize_MetaData[] = {
		{ "Category", "Field" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Field settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Field settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Resolution_MetaData[] = {
		{ "Category", "Field" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldCenter_MetaData[] = {
		{ "Category", "Field" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptor_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputAsTexture_MetaData[] = {
		{ "Category", "Output" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Output format\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output format" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputAsVolumeTexture_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputAsPointCloud_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOutputAsHeightmap_MetaData[] = {
		{ "Category", "Output" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByThreshold_MetaData[] = {
		{ "Category", "Filtering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Threshold_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByThreshold" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertThreshold_MetaData[] = {
		{ "Category", "Filtering" },
		{ "EditCondition", "bFilterByThreshold" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FieldSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Resolution;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FieldCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptor;
	static void NewProp_bOutputAsTexture_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputAsTexture;
	static void NewProp_bOutputAsVolumeTexture_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputAsVolumeTexture;
	static void NewProp_bOutputAsPointCloud_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputAsPointCloud;
	static void NewProp_bOutputAsHeightmap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOutputAsHeightmap;
	static void NewProp_bFilterByThreshold_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static void NewProp_bInvertThreshold_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertThreshold;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGNoiseFieldGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_FieldSize = { "FieldSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, FieldSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSize_MetaData), NewProp_FieldSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_Resolution = { "Resolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, Resolution), Z_Construct_UScriptStruct_FIntVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Resolution_MetaData), NewProp_Resolution_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_FieldCenter = { "FieldCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, FieldCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldCenter_MetaData), NewProp_FieldCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_NoiseDescriptor = { "NoiseDescriptor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, NoiseDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptor_MetaData), NewProp_NoiseDescriptor_MetaData) }; // 218319303
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsTexture_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bOutputAsTexture = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsTexture = { "bOutputAsTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsTexture_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputAsTexture_MetaData), NewProp_bOutputAsTexture_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsVolumeTexture_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bOutputAsVolumeTexture = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsVolumeTexture = { "bOutputAsVolumeTexture", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsVolumeTexture_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputAsVolumeTexture_MetaData), NewProp_bOutputAsVolumeTexture_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsPointCloud_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bOutputAsPointCloud = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsPointCloud = { "bOutputAsPointCloud", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsPointCloud_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputAsPointCloud_MetaData), NewProp_bOutputAsPointCloud_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsHeightmap_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bOutputAsHeightmap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsHeightmap = { "bOutputAsHeightmap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsHeightmap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOutputAsHeightmap_MetaData), NewProp_bOutputAsHeightmap_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bFilterByThreshold_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bFilterByThreshold = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bFilterByThreshold = { "bFilterByThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bFilterByThreshold_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByThreshold_MetaData), NewProp_bFilterByThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, Threshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Threshold_MetaData), NewProp_Threshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bInvertThreshold_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bInvertThreshold = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bInvertThreshold = { "bInvertThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bInvertThreshold_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertThreshold_MetaData), NewProp_bInvertThreshold_MetaData) };
void Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((UAuracronPCGNoiseFieldGeneratorSettings*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGNoiseFieldGeneratorSettings), &Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGNoiseFieldGeneratorSettings, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_FieldSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_Resolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_FieldCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_NoiseDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsVolumeTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsPointCloud,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bOutputAsHeightmap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bFilterByThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bInvertThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::NewProp_ThreadCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGNoiseFieldGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGNoiseFieldGeneratorSettings);
UAuracronPCGNoiseFieldGeneratorSettings::~UAuracronPCGNoiseFieldGeneratorSettings() {}
// ********** End Class UAuracronPCGNoiseFieldGeneratorSettings ************************************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function ApplyNoiseInterpolation ************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms
	{
		float Value;
		EAuracronPCGNoiseInterpolation InterpolationType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterpolationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterpolationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_InterpolationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_InterpolationType = { "InterpolationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms, InterpolationType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseInterpolation, METADATA_PARAMS(0, nullptr) }; // 2594972691
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_InterpolationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_InterpolationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "ApplyNoiseInterpolation", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::AuracronPCGNoiseSystemUtils_eventApplyNoiseInterpolation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execApplyNoiseInterpolation)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_GET_ENUM(EAuracronPCGNoiseInterpolation,Z_Param_InterpolationType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::ApplyNoiseInterpolation(Z_Param_Value,EAuracronPCGNoiseInterpolation(Z_Param_InterpolationType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function ApplyNoiseInterpolation **************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function ApplyNoiseMask *********************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms
	{
		float NoiseValue;
		float MaskValue;
		bool bInvertMask;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "CPP_Default_bInvertMask", "false" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaskValue;
	static void NewProp_bInvertMask_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertMask;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_NoiseValue = { "NoiseValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms, NoiseValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_MaskValue = { "MaskValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms, MaskValue), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_bInvertMask_SetBit(void* Obj)
{
	((AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms*)Obj)->bInvertMask = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_bInvertMask = { "bInvertMask", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms), &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_bInvertMask_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_NoiseValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_MaskValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_bInvertMask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "ApplyNoiseMask", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::AuracronPCGNoiseSystemUtils_eventApplyNoiseMask_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execApplyNoiseMask)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NoiseValue);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaskValue);
	P_GET_UBOOL(Z_Param_bInvertMask);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::ApplyNoiseMask(Z_Param_NoiseValue,Z_Param_MaskValue,Z_Param_bInvertMask);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function ApplyNoiseMask ***********************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function CombineNoiseValues *****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms
	{
		TArray<float> NoiseValues;
		TArray<float> Weights;
		EAuracronPCGNoiseCombineMode CombineMode;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise combination utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise combination utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseValues_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weights_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseValues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NoiseValues;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Weights;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CombineMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CombineMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_NoiseValues_Inner = { "NoiseValues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_NoiseValues = { "NoiseValues", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms, NoiseValues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseValues_MetaData), NewProp_NoiseValues_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_Weights_Inner = { "Weights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_Weights = { "Weights", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms, Weights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weights_MetaData), NewProp_Weights_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_CombineMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_CombineMode = { "CombineMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms, CombineMode), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseCombineMode, METADATA_PARAMS(0, nullptr) }; // 4176871008
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_NoiseValues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_NoiseValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_Weights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_Weights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_CombineMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_CombineMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "CombineNoiseValues", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::AuracronPCGNoiseSystemUtils_eventCombineNoiseValues_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execCombineNoiseValues)
{
	P_GET_TARRAY_REF(float,Z_Param_Out_NoiseValues);
	P_GET_TARRAY_REF(float,Z_Param_Out_Weights);
	P_GET_ENUM(EAuracronPCGNoiseCombineMode,Z_Param_CombineMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::CombineNoiseValues(Z_Param_Out_NoiseValues,Z_Param_Out_Weights,EAuracronPCGNoiseCombineMode(Z_Param_CombineMode));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function CombineNoiseValues *******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function CreateDefaultNoiseDescriptor *******
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventCreateDefaultNoiseDescriptor_Parms
	{
		EAuracronPCGAdvancedNoiseType NoiseType;
		FAuracronPCGNoiseDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCreateDefaultNoiseDescriptor_Parms, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGAdvancedNoiseType, METADATA_PARAMS(0, nullptr) }; // 1494480122
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventCreateDefaultNoiseDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(0, nullptr) }; // 218319303
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "CreateDefaultNoiseDescriptor", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::AuracronPCGNoiseSystemUtils_eventCreateDefaultNoiseDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::AuracronPCGNoiseSystemUtils_eventCreateDefaultNoiseDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execCreateDefaultNoiseDescriptor)
{
	P_GET_ENUM(EAuracronPCGAdvancedNoiseType,Z_Param_NoiseType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGNoiseDescriptor*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::CreateDefaultNoiseDescriptor(EAuracronPCGAdvancedNoiseType(Z_Param_NoiseType));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function CreateDefaultNoiseDescriptor *********

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateGaussianRandom *************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms
	{
		int32 Seed;
		float Mean;
		float StdDev;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "CPP_Default_Mean", "0.000000" },
		{ "CPP_Default_StdDev", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mean;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StdDev;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_Mean = { "Mean", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms, Mean), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_StdDev = { "StdDev", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms, StdDev), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_Mean,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_StdDev,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateGaussianRandom", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::AuracronPCGNoiseSystemUtils_eventGenerateGaussianRandom_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateGaussianRandom)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Mean);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StdDev);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateGaussianRandom(Z_Param_Seed,Z_Param_Mean,Z_Param_StdDev);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateGaussianRandom ***************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateNoise **********************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms
	{
		FVector Position;
		FAuracronPCGNoiseDescriptor NoiseDescriptor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise generation utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise generation utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_NoiseDescriptor = { "NoiseDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms, NoiseDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptor_MetaData), NewProp_NoiseDescriptor_MetaData) }; // 218319303
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_NoiseDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FAuracronPCGNoiseDescriptor,Z_Param_Out_NoiseDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateNoise(Z_Param_Out_Position,Z_Param_Out_NoiseDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateNoise ************************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GeneratePerlinNoise ****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms
	{
		FVector Position;
		float Frequency;
		int32 Octaves;
		float Persistence;
		int32 Seed;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, Octaves), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, Persistence), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GeneratePerlinNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::AuracronPCGNoiseSystemUtils_eventGeneratePerlinNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGeneratePerlinNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_PROPERTY(FIntProperty,Z_Param_Octaves);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Persistence);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GeneratePerlinNoise(Z_Param_Out_Position,Z_Param_Frequency,Z_Param_Octaves,Z_Param_Persistence,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GeneratePerlinNoise ******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateRandomFloat ****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms
	{
		int32 Seed;
		float Min;
		float Max;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Randomization utilities\n" },
#endif
		{ "CPP_Default_Max", "1.000000" },
		{ "CPP_Default_Min", "0.000000" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Randomization utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Min;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Max;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Min = { "Min", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms, Min), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Max = { "Max", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms, Max), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Min,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_Max,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateRandomFloat", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomFloat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateRandomFloat)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Min);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Max);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateRandomFloat(Z_Param_Seed,Z_Param_Min,Z_Param_Max);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateRandomFloat ******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateRandomInt ******************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms
	{
		int32 Seed;
		int32 Min;
		int32 Max;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "CPP_Default_Max", "100" },
		{ "CPP_Default_Min", "0" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Min;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Max;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Min = { "Min", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms, Min), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Max = { "Max", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms, Max), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Min,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_Max,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateRandomInt", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomInt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateRandomInt)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_GET_PROPERTY(FIntProperty,Z_Param_Min);
	P_GET_PROPERTY(FIntProperty,Z_Param_Max);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateRandomInt(Z_Param_Seed,Z_Param_Min,Z_Param_Max);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateRandomInt ********************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateRandomVector ***************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms
	{
		int32 Seed;
		FVector Min;
		FVector Max;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Min_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Max_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Min;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Max;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Min = { "Min", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms, Min), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Min_MetaData), NewProp_Min_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Max = { "Max", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms, Max), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Max_MetaData), NewProp_Max_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Min,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_Max,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateRandomVector", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomVector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateRandomVector)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Min);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Max);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateRandomVector(Z_Param_Seed,Z_Param_Out_Min,Z_Param_Out_Max);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateRandomVector *****************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateRandomVectorSimple *********
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateRandomVectorSimple_Parms
	{
		int32 Seed;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVectorSimple_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRandomVectorSimple_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateRandomVectorSimple", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomVectorSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRandomVectorSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateRandomVectorSimple)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateRandomVectorSimple(Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateRandomVectorSimple ***********

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateRidgeNoise *****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms
	{
		FVector Position;
		float Frequency;
		int32 Octaves;
		float Persistence;
		int32 Seed;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, Octaves), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, Persistence), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateRidgeNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateRidgeNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateRidgeNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_PROPERTY(FIntProperty,Z_Param_Octaves);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Persistence);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateRidgeNoise(Z_Param_Out_Position,Z_Param_Frequency,Z_Param_Octaves,Z_Param_Persistence,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateRidgeNoise *******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateSimplexNoise ***************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms
	{
		FVector Position;
		float Frequency;
		int32 Octaves;
		float Persistence;
		int32 Seed;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, Octaves), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, Persistence), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateSimplexNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateSimplexNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateSimplexNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_PROPERTY(FIntProperty,Z_Param_Octaves);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Persistence);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateSimplexNoise(Z_Param_Out_Position,Z_Param_Frequency,Z_Param_Octaves,Z_Param_Persistence,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateSimplexNoise *****************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateVectorNoise ****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms
	{
		FVector Position;
		FAuracronPCGNoiseDescriptor NoiseDescriptorX;
		FAuracronPCGNoiseDescriptor NoiseDescriptorY;
		FAuracronPCGNoiseDescriptor NoiseDescriptorZ;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptorX_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptorY_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptorZ_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptorX;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptorY;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptorZ;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorX = { "NoiseDescriptorX", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms, NoiseDescriptorX), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptorX_MetaData), NewProp_NoiseDescriptorX_MetaData) }; // 218319303
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorY = { "NoiseDescriptorY", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms, NoiseDescriptorY), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptorY_MetaData), NewProp_NoiseDescriptorY_MetaData) }; // 218319303
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorZ = { "NoiseDescriptorZ", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms, NoiseDescriptorZ), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptorZ_MetaData), NewProp_NoiseDescriptorZ_MetaData) }; // 218319303
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_NoiseDescriptorZ,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateVectorNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateVectorNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateVectorNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FAuracronPCGNoiseDescriptor,Z_Param_Out_NoiseDescriptorX);
	P_GET_STRUCT_REF(FAuracronPCGNoiseDescriptor,Z_Param_Out_NoiseDescriptorY);
	P_GET_STRUCT_REF(FAuracronPCGNoiseDescriptor,Z_Param_Out_NoiseDescriptorZ);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateVectorNoise(Z_Param_Out_Position,Z_Param_Out_NoiseDescriptorX,Z_Param_Out_NoiseDescriptorY,Z_Param_Out_NoiseDescriptorZ);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateVectorNoise ******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function GenerateWorleyNoise ****************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms
	{
		FVector Position;
		float Frequency;
		EAuracronPCGWorleyDistanceFunction DistanceFunction;
		float Jitter;
		int32 Seed;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistanceFunction_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistanceFunction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Jitter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_DistanceFunction_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_DistanceFunction = { "DistanceFunction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, DistanceFunction), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGWorleyDistanceFunction, METADATA_PARAMS(0, nullptr) }; // 3186219053
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Jitter = { "Jitter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, Jitter), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, Seed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_DistanceFunction_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_DistanceFunction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Jitter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "GenerateWorleyNoise", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::AuracronPCGNoiseSystemUtils_eventGenerateWorleyNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execGenerateWorleyNoise)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_ENUM(EAuracronPCGWorleyDistanceFunction,Z_Param_DistanceFunction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Jitter);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::GenerateWorleyNoise(Z_Param_Out_Position,Z_Param_Frequency,EAuracronPCGWorleyDistanceFunction(Z_Param_DistanceFunction),Z_Param_Jitter,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function GenerateWorleyNoise ******************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function RemapNoiseValue ********************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms
	{
		float NoiseValue;
		float InputMin;
		float InputMax;
		float OutputMin;
		float OutputMax;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InputMin;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InputMax;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputMin;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutputMax;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_NoiseValue = { "NoiseValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, NoiseValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_InputMin = { "InputMin", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, InputMin), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_InputMax = { "InputMax", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, InputMax), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_OutputMin = { "OutputMin", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, OutputMin), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_OutputMax = { "OutputMax", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, OutputMax), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_NoiseValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_InputMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_InputMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_OutputMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_OutputMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "RemapNoiseValue", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::AuracronPCGNoiseSystemUtils_eventRemapNoiseValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execRemapNoiseValue)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NoiseValue);
	P_GET_PROPERTY(FFloatProperty,Z_Param_InputMin);
	P_GET_PROPERTY(FFloatProperty,Z_Param_InputMax);
	P_GET_PROPERTY(FFloatProperty,Z_Param_OutputMin);
	P_GET_PROPERTY(FFloatProperty,Z_Param_OutputMax);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::RemapNoiseValue(Z_Param_NoiseValue,Z_Param_InputMin,Z_Param_InputMax,Z_Param_OutputMin,Z_Param_OutputMax);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function RemapNoiseValue **********************

// ********** Begin Class UAuracronPCGNoiseSystemUtils Function ValidateNoiseDescriptor ************
struct Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics
{
	struct AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms
	{
		FAuracronPCGNoiseDescriptor NoiseDescriptor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Noise System Utils" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseDescriptor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseDescriptor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_NoiseDescriptor = { "NoiseDescriptor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms, NoiseDescriptor), Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseDescriptor_MetaData), NewProp_NoiseDescriptor_MetaData) }; // 218319303
void Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms), &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_NoiseDescriptor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, nullptr, "ValidateNoiseDescriptor", Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::AuracronPCGNoiseSystemUtils_eventValidateNoiseDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGNoiseSystemUtils::execValidateNoiseDescriptor)
{
	P_GET_STRUCT_REF(FAuracronPCGNoiseDescriptor,Z_Param_Out_NoiseDescriptor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGNoiseSystemUtils::ValidateNoiseDescriptor(Z_Param_Out_NoiseDescriptor);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGNoiseSystemUtils Function ValidateNoiseDescriptor **************

// ********** Begin Class UAuracronPCGNoiseSystemUtils *********************************************
void UAuracronPCGNoiseSystemUtils::StaticRegisterNativesUAuracronPCGNoiseSystemUtils()
{
	UClass* Class = UAuracronPCGNoiseSystemUtils::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyNoiseInterpolation", &UAuracronPCGNoiseSystemUtils::execApplyNoiseInterpolation },
		{ "ApplyNoiseMask", &UAuracronPCGNoiseSystemUtils::execApplyNoiseMask },
		{ "CombineNoiseValues", &UAuracronPCGNoiseSystemUtils::execCombineNoiseValues },
		{ "CreateDefaultNoiseDescriptor", &UAuracronPCGNoiseSystemUtils::execCreateDefaultNoiseDescriptor },
		{ "GenerateGaussianRandom", &UAuracronPCGNoiseSystemUtils::execGenerateGaussianRandom },
		{ "GenerateNoise", &UAuracronPCGNoiseSystemUtils::execGenerateNoise },
		{ "GeneratePerlinNoise", &UAuracronPCGNoiseSystemUtils::execGeneratePerlinNoise },
		{ "GenerateRandomFloat", &UAuracronPCGNoiseSystemUtils::execGenerateRandomFloat },
		{ "GenerateRandomInt", &UAuracronPCGNoiseSystemUtils::execGenerateRandomInt },
		{ "GenerateRandomVector", &UAuracronPCGNoiseSystemUtils::execGenerateRandomVector },
		{ "GenerateRandomVectorSimple", &UAuracronPCGNoiseSystemUtils::execGenerateRandomVectorSimple },
		{ "GenerateRidgeNoise", &UAuracronPCGNoiseSystemUtils::execGenerateRidgeNoise },
		{ "GenerateSimplexNoise", &UAuracronPCGNoiseSystemUtils::execGenerateSimplexNoise },
		{ "GenerateVectorNoise", &UAuracronPCGNoiseSystemUtils::execGenerateVectorNoise },
		{ "GenerateWorleyNoise", &UAuracronPCGNoiseSystemUtils::execGenerateWorleyNoise },
		{ "RemapNoiseValue", &UAuracronPCGNoiseSystemUtils::execRemapNoiseValue },
		{ "ValidateNoiseDescriptor", &UAuracronPCGNoiseSystemUtils::execValidateNoiseDescriptor },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils;
UClass* UAuracronPCGNoiseSystemUtils::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGNoiseSystemUtils;
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGNoiseSystemUtils"),
			Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.InnerSingleton,
			StaticRegisterNativesUAuracronPCGNoiseSystemUtils,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_NoRegister()
{
	return UAuracronPCGNoiseSystemUtils::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Noise System Utilities\n * Utility functions for advanced noise system operations\n */" },
#endif
		{ "IncludePath", "AuracronPCGNoiseSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGNoiseSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise System Utilities\nUtility functions for advanced noise system operations" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseInterpolation, "ApplyNoiseInterpolation" }, // 1616268579
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ApplyNoiseMask, "ApplyNoiseMask" }, // 548875551
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CombineNoiseValues, "CombineNoiseValues" }, // 127699790
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_CreateDefaultNoiseDescriptor, "CreateDefaultNoiseDescriptor" }, // 3788577154
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateGaussianRandom, "GenerateGaussianRandom" }, // 2634421764
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateNoise, "GenerateNoise" }, // 751060934
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GeneratePerlinNoise, "GeneratePerlinNoise" }, // 924365218
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomFloat, "GenerateRandomFloat" }, // 4027487989
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomInt, "GenerateRandomInt" }, // 1375263220
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVector, "GenerateRandomVector" }, // 3927878775
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRandomVectorSimple, "GenerateRandomVectorSimple" }, // 4041655221
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateRidgeNoise, "GenerateRidgeNoise" }, // 1260832224
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateSimplexNoise, "GenerateSimplexNoise" }, // 838422907
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateVectorNoise, "GenerateVectorNoise" }, // 3925713780
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_GenerateWorleyNoise, "GenerateWorleyNoise" }, // 1907323745
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_RemapNoiseValue, "RemapNoiseValue" }, // 3833308699
		{ &Z_Construct_UFunction_UAuracronPCGNoiseSystemUtils_ValidateNoiseDescriptor, "ValidateNoiseDescriptor" }, // 3059424210
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGNoiseSystemUtils>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::ClassParams = {
	&UAuracronPCGNoiseSystemUtils::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.OuterSingleton, Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils.OuterSingleton;
}
UAuracronPCGNoiseSystemUtils::UAuracronPCGNoiseSystemUtils(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGNoiseSystemUtils);
UAuracronPCGNoiseSystemUtils::~UAuracronPCGNoiseSystemUtils() {}
// ********** End Class UAuracronPCGNoiseSystemUtils ***********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGAdvancedNoiseType_StaticEnum, TEXT("EAuracronPCGAdvancedNoiseType"), &Z_Registration_Info_UEnum_EAuracronPCGAdvancedNoiseType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1494480122U) },
		{ EAuracronPCGNoiseDimension_StaticEnum, TEXT("EAuracronPCGNoiseDimension"), &Z_Registration_Info_UEnum_EAuracronPCGNoiseDimension, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 747764361U) },
		{ EAuracronPCGFractalType_StaticEnum, TEXT("EAuracronPCGFractalType"), &Z_Registration_Info_UEnum_EAuracronPCGFractalType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2136469556U) },
		{ EAuracronPCGNoiseInterpolation_StaticEnum, TEXT("EAuracronPCGNoiseInterpolation"), &Z_Registration_Info_UEnum_EAuracronPCGNoiseInterpolation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2594972691U) },
		{ EAuracronPCGWorleyDistanceFunction_StaticEnum, TEXT("EAuracronPCGWorleyDistanceFunction"), &Z_Registration_Info_UEnum_EAuracronPCGWorleyDistanceFunction, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3186219053U) },
		{ EAuracronPCGRandomizationMode_StaticEnum, TEXT("EAuracronPCGRandomizationMode"), &Z_Registration_Info_UEnum_EAuracronPCGRandomizationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 442684380U) },
		{ EAuracronPCGNoiseCombineMode_StaticEnum, TEXT("EAuracronPCGNoiseCombineMode"), &Z_Registration_Info_UEnum_EAuracronPCGNoiseCombineMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4176871008U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGNoiseDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics::NewStructOps, TEXT("AuracronPCGNoiseDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGNoiseDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGNoiseDescriptor), 218319303U) },
		{ FAuracronPCGRandomizationDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics::NewStructOps, TEXT("AuracronPCGRandomizationDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronPCGRandomizationDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGRandomizationDescriptor), 40374615U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings, UAuracronPCGAdvancedNoiseGeneratorSettings::StaticClass, TEXT("UAuracronPCGAdvancedNoiseGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedNoiseGeneratorSettings), 2069374670U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings, UAuracronPCGAdvancedRandomizerSettings::StaticClass, TEXT("UAuracronPCGAdvancedRandomizerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedRandomizerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedRandomizerSettings), 480088104U) },
		{ Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings, UAuracronPCGNoiseCombinerSettings::StaticClass, TEXT("UAuracronPCGNoiseCombinerSettings"), &Z_Registration_Info_UClass_UAuracronPCGNoiseCombinerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGNoiseCombinerSettings), 3869900028U) },
		{ Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings, UAuracronPCGNoiseFieldGeneratorSettings::StaticClass, TEXT("UAuracronPCGNoiseFieldGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGNoiseFieldGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGNoiseFieldGeneratorSettings), 2304212172U) },
		{ Z_Construct_UClass_UAuracronPCGNoiseSystemUtils, UAuracronPCGNoiseSystemUtils::StaticClass, TEXT("UAuracronPCGNoiseSystemUtils"), &Z_Registration_Info_UClass_UAuracronPCGNoiseSystemUtils, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGNoiseSystemUtils), 2571741810U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_1856209045(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
