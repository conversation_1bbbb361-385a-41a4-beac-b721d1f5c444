// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronHairGeneration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronHairGeneration() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairCardData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairColorData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairGenerationParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairLODData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairNoiseParameters();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairPhysicsData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairStrandData();
AURACRONMETAHUMANBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHairStylingData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EHairType *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairType;
static UEnum* EHairType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairType"));
	}
	return Z_Registration_Info_UEnum_EHairType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairType>()
{
	return EHairType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Coily.DisplayName", "Coily" },
		{ "Coily.Name", "EHairType::Coily" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for hair generation\n" },
#endif
		{ "Curly.DisplayName", "Curly" },
		{ "Curly.Name", "EHairType::Curly" },
		{ "Kinky.DisplayName", "Kinky" },
		{ "Kinky.Name", "EHairType::Kinky" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
		{ "Straight.DisplayName", "Straight" },
		{ "Straight.Name", "EHairType::Straight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for hair generation" },
#endif
		{ "Wavy.DisplayName", "Wavy" },
		{ "Wavy.Name", "EHairType::Wavy" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairType::Straight", (int64)EHairType::Straight },
		{ "EHairType::Wavy", (int64)EHairType::Wavy },
		{ "EHairType::Curly", (int64)EHairType::Curly },
		{ "EHairType::Coily", (int64)EHairType::Coily },
		{ "EHairType::Kinky", (int64)EHairType::Kinky },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairType",
	"EHairType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType()
{
	if (!Z_Registration_Info_UEnum_EHairType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairType.InnerSingleton;
}
// ********** End Enum EHairType *******************************************************************

// ********** Begin Enum EHairLength ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairLength;
static UEnum* EHairLength_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairLength.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairLength.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairLength"));
	}
	return Z_Registration_Info_UEnum_EHairLength.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairLength>()
{
	return EHairLength_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Long.DisplayName", "Long" },
		{ "Long.Name", "EHairLength::Long" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EHairLength::Medium" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
		{ "Short.DisplayName", "Short" },
		{ "Short.Name", "EHairLength::Short" },
		{ "VeryLong.DisplayName", "Very Long" },
		{ "VeryLong.Name", "EHairLength::VeryLong" },
		{ "VeryShort.DisplayName", "Very Short" },
		{ "VeryShort.Name", "EHairLength::VeryShort" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairLength::VeryShort", (int64)EHairLength::VeryShort },
		{ "EHairLength::Short", (int64)EHairLength::Short },
		{ "EHairLength::Medium", (int64)EHairLength::Medium },
		{ "EHairLength::Long", (int64)EHairLength::Long },
		{ "EHairLength::VeryLong", (int64)EHairLength::VeryLong },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairLength",
	"EHairLength",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength()
{
	if (!Z_Registration_Info_UEnum_EHairLength.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairLength.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairLength.InnerSingleton;
}
// ********** End Enum EHairLength *****************************************************************

// ********** Begin Enum EHairDensity **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairDensity;
static UEnum* EHairDensity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairDensity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairDensity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairDensity"));
	}
	return Z_Registration_Info_UEnum_EHairDensity.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairDensity>()
{
	return EHairDensity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EHairDensity::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EHairDensity::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EHairDensity::Medium" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
		{ "Sparse.DisplayName", "Sparse" },
		{ "Sparse.Name", "EHairDensity::Sparse" },
		{ "VeryHigh.DisplayName", "Very High" },
		{ "VeryHigh.Name", "EHairDensity::VeryHigh" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairDensity::Sparse", (int64)EHairDensity::Sparse },
		{ "EHairDensity::Low", (int64)EHairDensity::Low },
		{ "EHairDensity::Medium", (int64)EHairDensity::Medium },
		{ "EHairDensity::High", (int64)EHairDensity::High },
		{ "EHairDensity::VeryHigh", (int64)EHairDensity::VeryHigh },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairDensity",
	"EHairDensity",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity()
{
	if (!Z_Registration_Info_UEnum_EHairDensity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairDensity.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairDensity.InnerSingleton;
}
// ********** End Enum EHairDensity ****************************************************************

// ********** Begin Enum EHairRenderingMode ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairRenderingMode;
static UEnum* EHairRenderingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairRenderingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairRenderingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairRenderingMode"));
	}
	return Z_Registration_Info_UEnum_EHairRenderingMode.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairRenderingMode>()
{
	return EHairRenderingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cards.DisplayName", "Hair Cards" },
		{ "Cards.Name", "EHairRenderingMode::Cards" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EHairRenderingMode::Hybrid" },
		{ "Meshes.DisplayName", "Hair Meshes" },
		{ "Meshes.Name", "EHairRenderingMode::Meshes" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
		{ "Strands.DisplayName", "Hair Strands" },
		{ "Strands.Name", "EHairRenderingMode::Strands" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairRenderingMode::Strands", (int64)EHairRenderingMode::Strands },
		{ "EHairRenderingMode::Cards", (int64)EHairRenderingMode::Cards },
		{ "EHairRenderingMode::Meshes", (int64)EHairRenderingMode::Meshes },
		{ "EHairRenderingMode::Hybrid", (int64)EHairRenderingMode::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairRenderingMode",
	"EHairRenderingMode",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode()
{
	if (!Z_Registration_Info_UEnum_EHairRenderingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairRenderingMode.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairRenderingMode.InnerSingleton;
}
// ********** End Enum EHairRenderingMode **********************************************************

// ********** Begin Enum EHairPhysicsType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairPhysicsType;
static UEnum* EHairPhysicsType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairPhysicsType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairPhysicsType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairPhysicsType"));
	}
	return Z_Registration_Info_UEnum_EHairPhysicsType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairPhysicsType>()
{
	return EHairPhysicsType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EHairPhysicsType::Advanced" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
		{ "Niagara.DisplayName", "Niagara Physics" },
		{ "Niagara.Name", "EHairPhysicsType::Niagara" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EHairPhysicsType::None" },
		{ "Simple.DisplayName", "Simple" },
		{ "Simple.Name", "EHairPhysicsType::Simple" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairPhysicsType::None", (int64)EHairPhysicsType::None },
		{ "EHairPhysicsType::Simple", (int64)EHairPhysicsType::Simple },
		{ "EHairPhysicsType::Advanced", (int64)EHairPhysicsType::Advanced },
		{ "EHairPhysicsType::Niagara", (int64)EHairPhysicsType::Niagara },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairPhysicsType",
	"EHairPhysicsType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType()
{
	if (!Z_Registration_Info_UEnum_EHairPhysicsType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairPhysicsType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairPhysicsType.InnerSingleton;
}
// ********** End Enum EHairPhysicsType ************************************************************

// ********** Begin ScriptStruct FHairStrandData ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairStrandData;
class UScriptStruct* FHairStrandData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairStrandData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairStrandData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairStrandData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairStrandData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairStrandData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairStrandData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for hair generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for hair generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrandCount_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsPerStrand_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrandLength_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrandWidth_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootWidth_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TipWidth_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HairType_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HairLength_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HairDensity_MetaData[] = {
		{ "Category", "Hair Strand" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StrandCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsPerStrand;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrandLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrandWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RootWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TipWidth;
	static const UECodeGen_Private::FBytePropertyParams NewProp_HairType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_HairType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_HairLength_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_HairLength;
	static const UECodeGen_Private::FBytePropertyParams NewProp_HairDensity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_HairDensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairStrandData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandCount = { "StrandCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, StrandCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrandCount_MetaData), NewProp_StrandCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_PointsPerStrand = { "PointsPerStrand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, PointsPerStrand), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsPerStrand_MetaData), NewProp_PointsPerStrand_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandLength = { "StrandLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, StrandLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrandLength_MetaData), NewProp_StrandLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandWidth = { "StrandWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, StrandWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrandWidth_MetaData), NewProp_StrandWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_RootWidth = { "RootWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, RootWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootWidth_MetaData), NewProp_RootWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_TipWidth = { "TipWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, TipWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TipWidth_MetaData), NewProp_TipWidth_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairType = { "HairType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, HairType), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HairType_MetaData), NewProp_HairType_MetaData) }; // 763618913
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairLength_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairLength = { "HairLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, HairLength), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairLength, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HairLength_MetaData), NewProp_HairLength_MetaData) }; // 3810186742
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairDensity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairDensity = { "HairDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStrandData, HairDensity), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairDensity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HairDensity_MetaData), NewProp_HairDensity_MetaData) }; // 1095901844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairStrandData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_PointsPerStrand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_StrandWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_RootWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_TipWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairLength_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairDensity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStrandData_Statics::NewProp_HairDensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStrandData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairStrandData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairStrandData",
	Z_Construct_UScriptStruct_FHairStrandData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStrandData_Statics::PropPointers),
	sizeof(FHairStrandData),
	alignof(FHairStrandData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStrandData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairStrandData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairStrandData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairStrandData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairStrandData.InnerSingleton, Z_Construct_UScriptStruct_FHairStrandData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairStrandData.InnerSingleton;
}
// ********** End ScriptStruct FHairStrandData *****************************************************

// ********** Begin ScriptStruct FHairStylingData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairStylingData;
class UScriptStruct* FHairStylingData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairStylingData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairStylingData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairStylingData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairStylingData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairStylingData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairStylingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurlIntensity_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurlFrequency_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveIntensity_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveFrequency_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityDirection_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityStrength_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindStrength_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseIntensity_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseFrequency_MetaData[] = {
		{ "Category", "Hair Styling" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurlIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurlFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveFrequency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GravityDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindStrength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairStylingData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_CurlIntensity = { "CurlIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, CurlIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurlIntensity_MetaData), NewProp_CurlIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_CurlFrequency = { "CurlFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, CurlFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurlFrequency_MetaData), NewProp_CurlFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WaveIntensity = { "WaveIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, WaveIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveIntensity_MetaData), NewProp_WaveIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WaveFrequency = { "WaveFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, WaveFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveFrequency_MetaData), NewProp_WaveFrequency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_GravityDirection = { "GravityDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, GravityDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityDirection_MetaData), NewProp_GravityDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_GravityStrength = { "GravityStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, GravityStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityStrength_MetaData), NewProp_GravityStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, WindStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindStrength_MetaData), NewProp_WindStrength_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_NoiseIntensity = { "NoiseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, NoiseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseIntensity_MetaData), NewProp_NoiseIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_NoiseFrequency = { "NoiseFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairStylingData, NoiseFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseFrequency_MetaData), NewProp_NoiseFrequency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairStylingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_CurlIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_CurlFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WaveIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WaveFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_GravityDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_GravityStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_NoiseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairStylingData_Statics::NewProp_NoiseFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStylingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairStylingData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairStylingData",
	Z_Construct_UScriptStruct_FHairStylingData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStylingData_Statics::PropPointers),
	sizeof(FHairStylingData),
	alignof(FHairStylingData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairStylingData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairStylingData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairStylingData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairStylingData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairStylingData.InnerSingleton, Z_Construct_UScriptStruct_FHairStylingData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairStylingData.InnerSingleton;
}
// ********** End ScriptStruct FHairStylingData ****************************************************

// ********** Begin ScriptStruct FHairColorData ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairColorData;
class UScriptStruct* FHairColorData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairColorData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairColorData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairColorData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairColorData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairColorData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairColorData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootColor_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TipColor_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorVariation_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metallic_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specular_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transmission_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scattering_MetaData[] = {
		{ "Category", "Hair Color" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RootColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TipColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metallic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Specular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Transmission;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scattering;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairColorData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_RootColor = { "RootColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, RootColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootColor_MetaData), NewProp_RootColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_TipColor = { "TipColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, TipColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TipColor_MetaData), NewProp_TipColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_ColorVariation = { "ColorVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, ColorVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorVariation_MetaData), NewProp_ColorVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Metallic = { "Metallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, Metallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metallic_MetaData), NewProp_Metallic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Specular = { "Specular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, Specular), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specular_MetaData), NewProp_Specular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Transmission = { "Transmission", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, Transmission), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transmission_MetaData), NewProp_Transmission_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Scattering = { "Scattering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairColorData, Scattering), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scattering_MetaData), NewProp_Scattering_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairColorData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_RootColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_TipColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_ColorVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Metallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Specular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Transmission,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairColorData_Statics::NewProp_Scattering,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairColorData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairColorData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairColorData",
	Z_Construct_UScriptStruct_FHairColorData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairColorData_Statics::PropPointers),
	sizeof(FHairColorData),
	alignof(FHairColorData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairColorData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairColorData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairColorData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairColorData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairColorData.InnerSingleton, Z_Construct_UScriptStruct_FHairColorData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairColorData.InnerSingleton;
}
// ********** End ScriptStruct FHairColorData ******************************************************

// ********** Begin ScriptStruct FHairPhysicsData **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairPhysicsData;
class UScriptStruct* FHairPhysicsData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairPhysicsData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairPhysicsData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairPhysicsData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairPhysicsData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairPhysicsData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairPhysicsData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsType_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stiffness_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damping_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Friction_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionRadius_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverIterations_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubSteps_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollision_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSelfCollision_MetaData[] = {
		{ "Category", "Hair Physics" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhysicsType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhysicsType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Stiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Friction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SolverIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SubSteps;
	static void NewProp_bEnableCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollision;
	static void NewProp_bEnableSelfCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSelfCollision;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairPhysicsData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_PhysicsType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_PhysicsType = { "PhysicsType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, PhysicsType), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairPhysicsType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsType_MetaData), NewProp_PhysicsType_MetaData) }; // 3523931398
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Stiffness = { "Stiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, Stiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stiffness_MetaData), NewProp_Stiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Damping = { "Damping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, Damping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damping_MetaData), NewProp_Damping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Friction = { "Friction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, Friction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Friction_MetaData), NewProp_Friction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_CollisionRadius = { "CollisionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, CollisionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionRadius_MetaData), NewProp_CollisionRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_SolverIterations = { "SolverIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, SolverIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverIterations_MetaData), NewProp_SolverIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_SubSteps = { "SubSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairPhysicsData, SubSteps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubSteps_MetaData), NewProp_SubSteps_MetaData) };
void Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableCollision_SetBit(void* Obj)
{
	((FHairPhysicsData*)Obj)->bEnableCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableCollision = { "bEnableCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHairPhysicsData), &Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollision_MetaData), NewProp_bEnableCollision_MetaData) };
void Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableSelfCollision_SetBit(void* Obj)
{
	((FHairPhysicsData*)Obj)->bEnableSelfCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableSelfCollision = { "bEnableSelfCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHairPhysicsData), &Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableSelfCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSelfCollision_MetaData), NewProp_bEnableSelfCollision_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairPhysicsData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_PhysicsType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_PhysicsType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Stiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Damping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_Friction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_CollisionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_SolverIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_SubSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewProp_bEnableSelfCollision,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairPhysicsData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairPhysicsData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairPhysicsData",
	Z_Construct_UScriptStruct_FHairPhysicsData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairPhysicsData_Statics::PropPointers),
	sizeof(FHairPhysicsData),
	alignof(FHairPhysicsData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairPhysicsData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairPhysicsData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairPhysicsData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairPhysicsData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairPhysicsData.InnerSingleton, Z_Construct_UScriptStruct_FHairPhysicsData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairPhysicsData.InnerSingleton;
}
// ********** End ScriptStruct FHairPhysicsData ****************************************************

// ********** Begin ScriptStruct FHairCardData *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairCardData;
class UScriptStruct* FHairCardData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairCardData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairCardData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairCardData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairCardData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairCardData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairCardData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CardCount_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CardSize_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SegmentsPerCard_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CardWidth_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateAtlas_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AtlasResolution_MetaData[] = {
		{ "Category", "Hair Cards" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CardCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CardSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SegmentsPerCard;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CardWidth;
	static void NewProp_bGenerateAtlas_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateAtlas;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AtlasResolution;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairCardData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardCount = { "CardCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairCardData, CardCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CardCount_MetaData), NewProp_CardCount_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardSize = { "CardSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairCardData, CardSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CardSize_MetaData), NewProp_CardSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_SegmentsPerCard = { "SegmentsPerCard", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairCardData, SegmentsPerCard), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SegmentsPerCard_MetaData), NewProp_SegmentsPerCard_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardWidth = { "CardWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairCardData, CardWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CardWidth_MetaData), NewProp_CardWidth_MetaData) };
void Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_bGenerateAtlas_SetBit(void* Obj)
{
	((FHairCardData*)Obj)->bGenerateAtlas = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_bGenerateAtlas = { "bGenerateAtlas", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHairCardData), &Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_bGenerateAtlas_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateAtlas_MetaData), NewProp_bGenerateAtlas_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_AtlasResolution = { "AtlasResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairCardData, AtlasResolution), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AtlasResolution_MetaData), NewProp_AtlasResolution_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairCardData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_SegmentsPerCard,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_CardWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_bGenerateAtlas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairCardData_Statics::NewProp_AtlasResolution,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairCardData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairCardData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairCardData",
	Z_Construct_UScriptStruct_FHairCardData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairCardData_Statics::PropPointers),
	sizeof(FHairCardData),
	alignof(FHairCardData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairCardData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairCardData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairCardData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairCardData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairCardData.InnerSingleton, Z_Construct_UScriptStruct_FHairCardData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairCardData.InnerSingleton;
}
// ********** End ScriptStruct FHairCardData *******************************************************

// ********** Begin ScriptStruct FHairLODData ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairLODData;
class UScriptStruct* FHairLODData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairLODData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairLODData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairLODData, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairLODData"));
	}
	return Z_Registration_Info_UScriptStruct_FHairLODData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairLODData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumLODs_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrandReductions_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderingModes_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableScreenSizeBasedLOD_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenSizeThreshold_MetaData[] = {
		{ "Category", "Hair LOD" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumLODs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrandReductions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StrandReductions;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RenderingModes_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RenderingModes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RenderingModes;
	static void NewProp_bEnableScreenSizeBasedLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableScreenSizeBasedLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenSizeThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairLODData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_NumLODs = { "NumLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairLODData, NumLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumLODs_MetaData), NewProp_NumLODs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairLODData, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_StrandReductions_Inner = { "StrandReductions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_StrandReductions = { "StrandReductions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairLODData, StrandReductions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrandReductions_MetaData), NewProp_StrandReductions_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes_Inner = { "RenderingModes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode, METADATA_PARAMS(0, nullptr) }; // 2463531630
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes = { "RenderingModes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairLODData, RenderingModes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderingModes_MetaData), NewProp_RenderingModes_MetaData) }; // 2463531630
void Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_bEnableScreenSizeBasedLOD_SetBit(void* Obj)
{
	((FHairLODData*)Obj)->bEnableScreenSizeBasedLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_bEnableScreenSizeBasedLOD = { "bEnableScreenSizeBasedLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHairLODData), &Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_bEnableScreenSizeBasedLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableScreenSizeBasedLOD_MetaData), NewProp_bEnableScreenSizeBasedLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_ScreenSizeThreshold = { "ScreenSizeThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairLODData, ScreenSizeThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenSizeThreshold_MetaData), NewProp_ScreenSizeThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairLODData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_NumLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_StrandReductions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_StrandReductions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_RenderingModes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_bEnableScreenSizeBasedLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairLODData_Statics::NewProp_ScreenSizeThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairLODData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairLODData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairLODData",
	Z_Construct_UScriptStruct_FHairLODData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairLODData_Statics::PropPointers),
	sizeof(FHairLODData),
	alignof(FHairLODData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairLODData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairLODData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairLODData()
{
	if (!Z_Registration_Info_UScriptStruct_FHairLODData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairLODData.InnerSingleton, Z_Construct_UScriptStruct_FHairLODData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairLODData.InnerSingleton;
}
// ********** End ScriptStruct FHairLODData ********************************************************

// ********** Begin ScriptStruct FHairNoiseParameters **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairNoiseParameters;
class UScriptStruct* FHairNoiseParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairNoiseParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairNoiseParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairNoiseParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairNoiseParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FHairNoiseParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairNoiseParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseIntensity_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseLacunarity_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoisePersistence_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseSeed_MetaData[] = {
		{ "Category", "Hair Noise" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseIntensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseLacunarity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoisePersistence;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseSeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairNoiseParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseIntensity = { "NoiseIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoiseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseIntensity_MetaData), NewProp_NoiseIntensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseLacunarity = { "NoiseLacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoiseLacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseLacunarity_MetaData), NewProp_NoiseLacunarity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoisePersistence = { "NoisePersistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoisePersistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoisePersistence_MetaData), NewProp_NoisePersistence_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseSeed = { "NoiseSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairNoiseParameters, NoiseSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseSeed_MetaData), NewProp_NoiseSeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseLacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoisePersistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewProp_NoiseSeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairNoiseParameters",
	Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::PropPointers),
	sizeof(FHairNoiseParameters),
	alignof(FHairNoiseParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairNoiseParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FHairNoiseParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairNoiseParameters.InnerSingleton, Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairNoiseParameters.InnerSingleton;
}
// ********** End ScriptStruct FHairNoiseParameters ************************************************

// ********** Begin ScriptStruct FHairGenerationParameters *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHairGenerationParameters;
class UScriptStruct* FHairGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHairGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHairGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHairGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("HairGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FHairGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHairGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrandData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StylingData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CardData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODData_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseParams_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderingMode_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateBindingAsset_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Hair Generation" },
		{ "ModuleRelativePath", "Public/AuracronHairGeneration.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StrandData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StylingData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CardData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LODData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NoiseParams;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RenderingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RenderingMode;
	static void NewProp_bGenerateBindingAsset_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateBindingAsset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHairGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_StrandData = { "StrandData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, StrandData), Z_Construct_UScriptStruct_FHairStrandData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrandData_MetaData), NewProp_StrandData_MetaData) }; // 3943069970
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_StylingData = { "StylingData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, StylingData), Z_Construct_UScriptStruct_FHairStylingData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StylingData_MetaData), NewProp_StylingData_MetaData) }; // 64282272
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_ColorData = { "ColorData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, ColorData), Z_Construct_UScriptStruct_FHairColorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorData_MetaData), NewProp_ColorData_MetaData) }; // 1731264690
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_PhysicsData = { "PhysicsData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, PhysicsData), Z_Construct_UScriptStruct_FHairPhysicsData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsData_MetaData), NewProp_PhysicsData_MetaData) }; // 581615237
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_CardData = { "CardData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, CardData), Z_Construct_UScriptStruct_FHairCardData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CardData_MetaData), NewProp_CardData_MetaData) }; // 1428635899
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_LODData = { "LODData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, LODData), Z_Construct_UScriptStruct_FHairLODData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODData_MetaData), NewProp_LODData_MetaData) }; // 4221076713
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_NoiseParams = { "NoiseParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, NoiseParams), Z_Construct_UScriptStruct_FHairNoiseParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseParams_MetaData), NewProp_NoiseParams_MetaData) }; // 1247011029
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_RenderingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_RenderingMode = { "RenderingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, RenderingMode), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairRenderingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderingMode_MetaData), NewProp_RenderingMode_MetaData) }; // 2463531630
void Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_bGenerateBindingAsset_SetBit(void* Obj)
{
	((FHairGenerationParameters*)Obj)->bGenerateBindingAsset = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_bGenerateBindingAsset = { "bGenerateBindingAsset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHairGenerationParameters), &Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_bGenerateBindingAsset_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateBindingAsset_MetaData), NewProp_bGenerateBindingAsset_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHairGenerationParameters, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_StrandData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_StylingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_ColorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_PhysicsData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_CardData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_LODData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_NoiseParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_RenderingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_RenderingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_bGenerateBindingAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	&NewStructOps,
	"HairGenerationParameters",
	Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::PropPointers),
	sizeof(FHairGenerationParameters),
	alignof(FHairGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHairGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FHairGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHairGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHairGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FHairGenerationParameters *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EHairType_StaticEnum, TEXT("EHairType"), &Z_Registration_Info_UEnum_EHairType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 763618913U) },
		{ EHairLength_StaticEnum, TEXT("EHairLength"), &Z_Registration_Info_UEnum_EHairLength, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3810186742U) },
		{ EHairDensity_StaticEnum, TEXT("EHairDensity"), &Z_Registration_Info_UEnum_EHairDensity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1095901844U) },
		{ EHairRenderingMode_StaticEnum, TEXT("EHairRenderingMode"), &Z_Registration_Info_UEnum_EHairRenderingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2463531630U) },
		{ EHairPhysicsType_StaticEnum, TEXT("EHairPhysicsType"), &Z_Registration_Info_UEnum_EHairPhysicsType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3523931398U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FHairStrandData::StaticStruct, Z_Construct_UScriptStruct_FHairStrandData_Statics::NewStructOps, TEXT("HairStrandData"), &Z_Registration_Info_UScriptStruct_FHairStrandData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairStrandData), 3943069970U) },
		{ FHairStylingData::StaticStruct, Z_Construct_UScriptStruct_FHairStylingData_Statics::NewStructOps, TEXT("HairStylingData"), &Z_Registration_Info_UScriptStruct_FHairStylingData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairStylingData), 64282272U) },
		{ FHairColorData::StaticStruct, Z_Construct_UScriptStruct_FHairColorData_Statics::NewStructOps, TEXT("HairColorData"), &Z_Registration_Info_UScriptStruct_FHairColorData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairColorData), 1731264690U) },
		{ FHairPhysicsData::StaticStruct, Z_Construct_UScriptStruct_FHairPhysicsData_Statics::NewStructOps, TEXT("HairPhysicsData"), &Z_Registration_Info_UScriptStruct_FHairPhysicsData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairPhysicsData), 581615237U) },
		{ FHairCardData::StaticStruct, Z_Construct_UScriptStruct_FHairCardData_Statics::NewStructOps, TEXT("HairCardData"), &Z_Registration_Info_UScriptStruct_FHairCardData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairCardData), 1428635899U) },
		{ FHairLODData::StaticStruct, Z_Construct_UScriptStruct_FHairLODData_Statics::NewStructOps, TEXT("HairLODData"), &Z_Registration_Info_UScriptStruct_FHairLODData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairLODData), 4221076713U) },
		{ FHairNoiseParameters::StaticStruct, Z_Construct_UScriptStruct_FHairNoiseParameters_Statics::NewStructOps, TEXT("HairNoiseParameters"), &Z_Registration_Info_UScriptStruct_FHairNoiseParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairNoiseParameters), 1247011029U) },
		{ FHairGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FHairGenerationParameters_Statics::NewStructOps, TEXT("HairGenerationParameters"), &Z_Registration_Info_UScriptStruct_FHairGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHairGenerationParameters), 45600431U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_1257709680(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronHairGeneration_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
