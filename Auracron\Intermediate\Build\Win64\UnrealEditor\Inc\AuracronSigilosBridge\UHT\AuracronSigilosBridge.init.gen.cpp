// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronSigilosBridge_init() {}
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronSigilosBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronSigilosBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronSigilosBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x9FD53A16,
				0xC197B289,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronSigilosBridge(Z_Construct_UPackage__Script_AuracronSigilosBridge, TEXT("/Script/AuracronSigilosBridge"), Z_Registration_Info_UPackage__Script_AuracronSigilosBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x9FD53A16, 0xC197B289));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
