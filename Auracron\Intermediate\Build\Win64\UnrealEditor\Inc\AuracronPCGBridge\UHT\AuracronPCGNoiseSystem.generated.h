// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGNoiseSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGNoiseSystem_generated_h
#error "AuracronPCGNoiseSystem.generated.h already included, missing '#pragma once' in AuracronPCGNoiseSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGNoiseSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronPCGAdvancedNoiseType : uint8;
enum class EAuracronPCGNoiseCombineMode : uint8;
enum class EAuracronPCGNoiseInterpolation : uint8;
enum class EAuracronPCGWorleyDistanceFunction : uint8;
struct FAuracronPCGNoiseDescriptor;

// ********** Begin ScriptStruct FAuracronPCGNoiseDescriptor ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_120_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGNoiseDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGNoiseDescriptor;
// ********** End ScriptStruct FAuracronPCGNoiseDescriptor *****************************************

// ********** Begin ScriptStruct FAuracronPCGRandomizationDescriptor *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_222_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGRandomizationDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGRandomizationDescriptor;
// ********** End ScriptStruct FAuracronPCGRandomizationDescriptor *********************************

// ********** Begin Class UAuracronPCGAdvancedNoiseGeneratorSettings *******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_294_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedNoiseGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedNoiseGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedNoiseGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedNoiseGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_294_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedNoiseGeneratorSettings(UAuracronPCGAdvancedNoiseGeneratorSettings&&) = delete; \
	UAuracronPCGAdvancedNoiseGeneratorSettings(const UAuracronPCGAdvancedNoiseGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedNoiseGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedNoiseGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedNoiseGeneratorSettings) \
	NO_API virtual ~UAuracronPCGAdvancedNoiseGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_291_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_294_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_294_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_294_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedNoiseGeneratorSettings;

// ********** End Class UAuracronPCGAdvancedNoiseGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGAdvancedRandomizerSettings ***********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_371_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedRandomizerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedRandomizerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedRandomizerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedRandomizerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_371_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedRandomizerSettings(UAuracronPCGAdvancedRandomizerSettings&&) = delete; \
	UAuracronPCGAdvancedRandomizerSettings(const UAuracronPCGAdvancedRandomizerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedRandomizerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedRandomizerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedRandomizerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedRandomizerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_368_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_371_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_371_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_371_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedRandomizerSettings;

// ********** End Class UAuracronPCGAdvancedRandomizerSettings *************************************

// ********** Begin Class UAuracronPCGNoiseCombinerSettings ****************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_475_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGNoiseCombinerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGNoiseCombinerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGNoiseCombinerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGNoiseCombinerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_475_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGNoiseCombinerSettings(UAuracronPCGNoiseCombinerSettings&&) = delete; \
	UAuracronPCGNoiseCombinerSettings(const UAuracronPCGNoiseCombinerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGNoiseCombinerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGNoiseCombinerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGNoiseCombinerSettings) \
	NO_API virtual ~UAuracronPCGNoiseCombinerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_472_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_475_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_475_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_475_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGNoiseCombinerSettings;

// ********** End Class UAuracronPCGNoiseCombinerSettings ******************************************

// ********** Begin Class UAuracronPCGNoiseFieldGeneratorSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_534_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGNoiseFieldGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGNoiseFieldGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGNoiseFieldGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGNoiseFieldGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_534_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGNoiseFieldGeneratorSettings(UAuracronPCGNoiseFieldGeneratorSettings&&) = delete; \
	UAuracronPCGNoiseFieldGeneratorSettings(const UAuracronPCGNoiseFieldGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGNoiseFieldGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGNoiseFieldGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGNoiseFieldGeneratorSettings) \
	NO_API virtual ~UAuracronPCGNoiseFieldGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_531_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_534_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_534_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_534_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGNoiseFieldGeneratorSettings;

// ********** End Class UAuracronPCGNoiseFieldGeneratorSettings ************************************

// ********** Begin Class UAuracronPCGNoiseSystemUtils *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateDefaultNoiseDescriptor); \
	DECLARE_FUNCTION(execValidateNoiseDescriptor); \
	DECLARE_FUNCTION(execApplyNoiseInterpolation); \
	DECLARE_FUNCTION(execRemapNoiseValue); \
	DECLARE_FUNCTION(execApplyNoiseMask); \
	DECLARE_FUNCTION(execCombineNoiseValues); \
	DECLARE_FUNCTION(execGenerateGaussianRandom); \
	DECLARE_FUNCTION(execGenerateRandomInt); \
	DECLARE_FUNCTION(execGenerateRandomVectorSimple); \
	DECLARE_FUNCTION(execGenerateRandomVector); \
	DECLARE_FUNCTION(execGenerateRandomFloat); \
	DECLARE_FUNCTION(execGenerateRidgeNoise); \
	DECLARE_FUNCTION(execGenerateWorleyNoise); \
	DECLARE_FUNCTION(execGenerateSimplexNoise); \
	DECLARE_FUNCTION(execGeneratePerlinNoise); \
	DECLARE_FUNCTION(execGenerateVectorNoise); \
	DECLARE_FUNCTION(execGenerateNoise);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGNoiseSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGNoiseSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGNoiseSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGNoiseSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGNoiseSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGNoiseSystemUtils(UAuracronPCGNoiseSystemUtils&&) = delete; \
	UAuracronPCGNoiseSystemUtils(const UAuracronPCGNoiseSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGNoiseSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGNoiseSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGNoiseSystemUtils) \
	NO_API virtual ~UAuracronPCGNoiseSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_598_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h_601_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGNoiseSystemUtils;

// ********** End Class UAuracronPCGNoiseSystemUtils ***********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGNoiseSystem_h

// ********** Begin Enum EAuracronPCGAdvancedNoiseType *********************************************
#define FOREACH_ENUM_EAURACRONPCGADVANCEDNOISETYPE(op) \
	op(EAuracronPCGAdvancedNoiseType::Perlin) \
	op(EAuracronPCGAdvancedNoiseType::Simplex) \
	op(EAuracronPCGAdvancedNoiseType::Worley) \
	op(EAuracronPCGAdvancedNoiseType::Ridge) \
	op(EAuracronPCGAdvancedNoiseType::Billow) \
	op(EAuracronPCGAdvancedNoiseType::Voronoi) \
	op(EAuracronPCGAdvancedNoiseType::Cellular) \
	op(EAuracronPCGAdvancedNoiseType::Value) \
	op(EAuracronPCGAdvancedNoiseType::Gradient) \
	op(EAuracronPCGAdvancedNoiseType::Curl) \
	op(EAuracronPCGAdvancedNoiseType::Fractal) \
	op(EAuracronPCGAdvancedNoiseType::Custom) 

enum class EAuracronPCGAdvancedNoiseType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAdvancedNoiseType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAdvancedNoiseType>();
// ********** End Enum EAuracronPCGAdvancedNoiseType ***********************************************

// ********** Begin Enum EAuracronPCGNoiseDimension ************************************************
#define FOREACH_ENUM_EAURACRONPCGNOISEDIMENSION(op) \
	op(EAuracronPCGNoiseDimension::OneDimensional) \
	op(EAuracronPCGNoiseDimension::TwoDimensional) \
	op(EAuracronPCGNoiseDimension::ThreeDimensional) \
	op(EAuracronPCGNoiseDimension::FourDimensional) 

enum class EAuracronPCGNoiseDimension : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNoiseDimension> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseDimension>();
// ********** End Enum EAuracronPCGNoiseDimension **************************************************

// ********** Begin Enum EAuracronPCGFractalType ***************************************************
#define FOREACH_ENUM_EAURACRONPCGFRACTALTYPE(op) \
	op(EAuracronPCGFractalType::None) \
	op(EAuracronPCGFractalType::FBM) \
	op(EAuracronPCGFractalType::Turbulence) \
	op(EAuracronPCGFractalType::RidgedMulti) \
	op(EAuracronPCGFractalType::Billow) \
	op(EAuracronPCGFractalType::PingPong) \
	op(EAuracronPCGFractalType::DomainWarp) \
	op(EAuracronPCGFractalType::Custom) 

enum class EAuracronPCGFractalType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGFractalType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGFractalType>();
// ********** End Enum EAuracronPCGFractalType *****************************************************

// ********** Begin Enum EAuracronPCGNoiseInterpolation ********************************************
#define FOREACH_ENUM_EAURACRONPCGNOISEINTERPOLATION(op) \
	op(EAuracronPCGNoiseInterpolation::Linear) \
	op(EAuracronPCGNoiseInterpolation::Hermite) \
	op(EAuracronPCGNoiseInterpolation::Quintic) \
	op(EAuracronPCGNoiseInterpolation::Cosine) \
	op(EAuracronPCGNoiseInterpolation::Cubic) \
	op(EAuracronPCGNoiseInterpolation::Smoothstep) \
	op(EAuracronPCGNoiseInterpolation::Smootherstep) 

enum class EAuracronPCGNoiseInterpolation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNoiseInterpolation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseInterpolation>();
// ********** End Enum EAuracronPCGNoiseInterpolation **********************************************

// ********** Begin Enum EAuracronPCGWorleyDistanceFunction ****************************************
#define FOREACH_ENUM_EAURACRONPCGWORLEYDISTANCEFUNCTION(op) \
	op(EAuracronPCGWorleyDistanceFunction::Euclidean) \
	op(EAuracronPCGWorleyDistanceFunction::Manhattan) \
	op(EAuracronPCGWorleyDistanceFunction::Chebyshev) \
	op(EAuracronPCGWorleyDistanceFunction::Minkowski) \
	op(EAuracronPCGWorleyDistanceFunction::Natural) \
	op(EAuracronPCGWorleyDistanceFunction::Hybrid) 

enum class EAuracronPCGWorleyDistanceFunction : uint8;
template<> struct TIsUEnumClass<EAuracronPCGWorleyDistanceFunction> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGWorleyDistanceFunction>();
// ********** End Enum EAuracronPCGWorleyDistanceFunction ******************************************

// ********** Begin Enum EAuracronPCGRandomizationMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGRANDOMIZATIONMODE(op) \
	op(EAuracronPCGRandomizationMode::Deterministic) \
	op(EAuracronPCGRandomizationMode::SeedBased) \
	op(EAuracronPCGRandomizationMode::TimeBased) \
	op(EAuracronPCGRandomizationMode::PositionBased) \
	op(EAuracronPCGRandomizationMode::AttributeBased) \
	op(EAuracronPCGRandomizationMode::Hybrid) \
	op(EAuracronPCGRandomizationMode::Custom) 

enum class EAuracronPCGRandomizationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGRandomizationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGRandomizationMode>();
// ********** End Enum EAuracronPCGRandomizationMode ***********************************************

// ********** Begin Enum EAuracronPCGNoiseCombineMode **********************************************
#define FOREACH_ENUM_EAURACRONPCGNOISECOMBINEMODE(op) \
	op(EAuracronPCGNoiseCombineMode::Add) \
	op(EAuracronPCGNoiseCombineMode::Subtract) \
	op(EAuracronPCGNoiseCombineMode::Multiply) \
	op(EAuracronPCGNoiseCombineMode::Divide) \
	op(EAuracronPCGNoiseCombineMode::Min) \
	op(EAuracronPCGNoiseCombineMode::Max) \
	op(EAuracronPCGNoiseCombineMode::Average) \
	op(EAuracronPCGNoiseCombineMode::Screen) \
	op(EAuracronPCGNoiseCombineMode::Overlay) \
	op(EAuracronPCGNoiseCombineMode::SoftLight) \
	op(EAuracronPCGNoiseCombineMode::HardLight) \
	op(EAuracronPCGNoiseCombineMode::ColorDodge) \
	op(EAuracronPCGNoiseCombineMode::ColorBurn) \
	op(EAuracronPCGNoiseCombineMode::Difference) \
	op(EAuracronPCGNoiseCombineMode::Exclusion) \
	op(EAuracronPCGNoiseCombineMode::Custom) 

enum class EAuracronPCGNoiseCombineMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGNoiseCombineMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseCombineMode>();
// ********** End Enum EAuracronPCGNoiseCombineMode ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
