# AURACRON - Unreal Engine 5.6 .gitignore
# Configuração otimizada para desenvolvimento do jogo AURACRON

# Unreal Engine Build Files
Binaries/
Build/
Intermediate/
DerivedDataCache/
.vs/
.vscode/

# Unreal Engine Generated Files
*.generated.h
*.generated.cpp
*.uhtmanifest
*.uproject.DotSettings.user

# Unreal Engine Temporary Files
Saved/
!Saved/Config/
!Saved/Logs/
Saved/Logs/*
!Saved/Logs/.gitkeep

# Unreal Engine Cache
DerivedDataCache/
.DDC/

# Visual Studio Files
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
*.vcxproj.user
*.vcxproj.filters
*.VC.db
*.VC.VC.opendb

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Rider
.idea/
*.sln.iml

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Windows
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Unreal Engine Plugins (Third Party)
Plugins/*/Binaries/
Plugins/*/Intermediate/
Plugins/*/Build/

# Unreal Engine Content (Large Files)
# Uncomment these if you want to exclude large content files
# Content/**/*.uasset
# Content/**/*.umap
# Content/**/*.fbx
# Content/**/*.png
# Content/**/*.jpg
# Content/**/*.tga
# Content/**/*.wav
# Content/**/*.mp3

# Unreal Engine Specific Excludes
*.tmp
*.bak
*.swp
*.swo
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.target
.metadata
.classpath
.project

# Package Files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs
*.log
logs/
*.log.*

# Runtime Crash Dumps
*.dmp

# Unreal Engine Automation
TestResults/
AutomationTool/
Engine/Programs/AutomationTool/Saved/

# Unreal Engine Documentation
Documentation/

# Unreal Engine Localization
Content/Localization/

# Unreal Engine Marketplace
MarketplacePlugins/

# Unreal Engine Perforce
.p4config
.p4ignore

# Unreal Engine Plastic SCM
plastic.workspace
plastic.wktree

# Custom AURACRON Excludes
# Temporary generation files
Temp/
*.tmp
*.temp

# Build artifacts
BuildArtifacts/
Artifacts/

# Test results
TestResults/
Coverage/

# Performance profiling
*.prof
*.trace

# Development tools
Tools/Temp/
Tools/Cache/

# CI/CD
.github/workflows/cache/
.gitlab-ci-cache/

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration overrides (keep templates)
Config/Local*
!Config/LocalTemplate*

# User-specific content
Content/Developers/
Content/Collections/

# Large binary assets (if needed)
# Content/Audio/Music/
# Content/Video/

# Backup files
*.backup
*.bak
*_backup.*

# Keep important directories
!Content/
!Source/
!Config/
!Scripts/
!Documentation/

# Keep important files
!README.md
!LICENSE
!*.md
!*.txt

# AURACRON Specific
# Keep procedural generation templates
!Content/PCG/Templates/
!Scripts/Python/Templates/

# Keep core game assets
!Content/Core/
!Content/Characters/Heroes/Templates/
!Content/Maps/Templates/

# Exclude generated procedural content
Content/Generated/
Content/Procedural/Generated/

# Exclude large test assets
Content/Testing/LargeAssets/
Content/Testing/Performance/
