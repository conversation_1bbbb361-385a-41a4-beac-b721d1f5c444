// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGCustomNodeExamples.h"
#include "AuracronPCGCustomNodeSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGCustomNodeExamples() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronPCGExampleCustomPointProcessorSettings **************************
void UAuracronPCGExampleCustomPointProcessorSettings::StaticRegisterNativesUAuracronPCGExampleCustomPointProcessorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings;
UClass* UAuracronPCGExampleCustomPointProcessorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGExampleCustomPointProcessorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGExampleCustomPointProcessorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGExampleCustomPointProcessorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_NoRegister()
{
	return UAuracronPCGExampleCustomPointProcessorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCustomNodeExamples.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingStrength_MetaData[] = {
		{ "Category", "Processing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom parameters defined through the custom node system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom parameters defined through the custom node system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingIterations_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedProcessing_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingMode_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingOffset_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingColor_MetaData[] = {
		{ "Category", "Processing" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateInputs_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValidValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValidValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncExecution_MetaData[] = {
		{ "Category", "Execution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom execution settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom execution settings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingStrength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProcessingIterations;
	static void NewProp_bEnableAdvancedProcessing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedProcessing;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProcessingMode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProcessingOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProcessingColor;
	static void NewProp_bValidateInputs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateInputs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValidValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValidValue;
	static void NewProp_bUseAsyncExecution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncExecution;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGExampleCustomPointProcessorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingStrength = { "ProcessingStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, ProcessingStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingStrength_MetaData), NewProp_ProcessingStrength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingIterations = { "ProcessingIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, ProcessingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingIterations_MetaData), NewProp_ProcessingIterations_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bEnableAdvancedProcessing_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomPointProcessorSettings*)Obj)->bEnableAdvancedProcessing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bEnableAdvancedProcessing = { "bEnableAdvancedProcessing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomPointProcessorSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bEnableAdvancedProcessing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedProcessing_MetaData), NewProp_bEnableAdvancedProcessing_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingMode = { "ProcessingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, ProcessingMode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingMode_MetaData), NewProp_ProcessingMode_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingOffset = { "ProcessingOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, ProcessingOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingOffset_MetaData), NewProp_ProcessingOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingColor = { "ProcessingColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, ProcessingColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingColor_MetaData), NewProp_ProcessingColor_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bValidateInputs_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomPointProcessorSettings*)Obj)->bValidateInputs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bValidateInputs = { "bValidateInputs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomPointProcessorSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bValidateInputs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateInputs_MetaData), NewProp_bValidateInputs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_MinValidValue = { "MinValidValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, MinValidValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValidValue_MetaData), NewProp_MinValidValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_MaxValidValue = { "MaxValidValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomPointProcessorSettings, MaxValidValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValidValue_MetaData), NewProp_MaxValidValue_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bUseAsyncExecution_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomPointProcessorSettings*)Obj)->bUseAsyncExecution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bUseAsyncExecution = { "bUseAsyncExecution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomPointProcessorSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bUseAsyncExecution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncExecution_MetaData), NewProp_bUseAsyncExecution_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bEnableAdvancedProcessing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_ProcessingColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bValidateInputs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_MinValidValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_MaxValidValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::NewProp_bUseAsyncExecution,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::ClassParams = {
	&UAuracronPCGExampleCustomPointProcessorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGExampleCustomPointProcessorSettings);
UAuracronPCGExampleCustomPointProcessorSettings::~UAuracronPCGExampleCustomPointProcessorSettings() {}
// ********** End Class UAuracronPCGExampleCustomPointProcessorSettings ****************************

// ********** Begin Class UAuracronPCGExampleCustomGeneratorSettings *******************************
void UAuracronPCGExampleCustomGeneratorSettings::StaticRegisterNativesUAuracronPCGExampleCustomGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings;
UClass* UAuracronPCGExampleCustomGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGExampleCustomGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGExampleCustomGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGExampleCustomGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_NoRegister()
{
	return UAuracronPCGExampleCustomGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCustomNodeExamples.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointCount_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generation parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationBounds_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRandomSeed_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationModes_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Seed is inherited from UPCGSettings, no need to redeclare\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed is inherited from UPCGSettings, no need to redeclare" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedMode_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePattern_MetaData[] = {
		{ "Category", "Pattern" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pattern parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pattern parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatternScale_MetaData[] = {
		{ "Category", "Pattern" },
		{ "EditCondition", "bUsePattern" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatternOffset_MetaData[] = {
		{ "Category", "Pattern" },
		{ "EditCondition", "bUsePattern" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationBounds;
	static void NewProp_bUseRandomSeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRandomSeed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationModes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GenerationModes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectedMode;
	static void NewProp_bUsePattern_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatternScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatternOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGExampleCustomGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PointCount = { "PointCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, PointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointCount_MetaData), NewProp_PointCount_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationBounds = { "GenerationBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, GenerationBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationBounds_MetaData), NewProp_GenerationBounds_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUseRandomSeed_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomGeneratorSettings*)Obj)->bUseRandomSeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUseRandomSeed = { "bUseRandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomGeneratorSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUseRandomSeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRandomSeed_MetaData), NewProp_bUseRandomSeed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationModes_Inner = { "GenerationModes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationModes = { "GenerationModes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, GenerationModes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationModes_MetaData), NewProp_GenerationModes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_SelectedMode = { "SelectedMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, SelectedMode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedMode_MetaData), NewProp_SelectedMode_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUsePattern_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomGeneratorSettings*)Obj)->bUsePattern = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUsePattern = { "bUsePattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomGeneratorSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUsePattern_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePattern_MetaData), NewProp_bUsePattern_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PatternScale = { "PatternScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, PatternScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatternScale_MetaData), NewProp_PatternScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PatternOffset = { "PatternOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomGeneratorSettings, PatternOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatternOffset_MetaData), NewProp_PatternOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUseRandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationModes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_GenerationModes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_SelectedMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_bUsePattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PatternScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::NewProp_PatternOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGExampleCustomGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGExampleCustomGeneratorSettings);
UAuracronPCGExampleCustomGeneratorSettings::~UAuracronPCGExampleCustomGeneratorSettings() {}
// ********** End Class UAuracronPCGExampleCustomGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGExampleCustomFilterSettings **********************************
void UAuracronPCGExampleCustomFilterSettings::StaticRegisterNativesUAuracronPCGExampleCustomFilterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings;
UClass* UAuracronPCGExampleCustomFilterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGExampleCustomFilterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGExampleCustomFilterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGExampleCustomFilterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_NoRegister()
{
	return UAuracronPCGExampleCustomFilterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGCustomNodeExamples.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByPosition_MetaData[] = {
		{ "Category", "Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filter criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filter criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterCenter_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterRadius_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByPosition" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByDensity_MetaData[] = {
		{ "Category", "Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDensity_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByDensity" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDensity_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByDensity" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByColor_MetaData[] = {
		{ "Category", "Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetColor_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByColor" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorTolerance_MetaData[] = {
		{ "Category", "Filter" },
		{ "EditCondition", "bFilterByColor" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertFilter_MetaData[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filter behavior\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filter behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSoftFilter_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoftFilterFalloff_MetaData[] = {
		{ "Category", "Behavior" },
		{ "EditCondition", "bSoftFilter" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bFilterByPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FilterCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FilterRadius;
	static void NewProp_bFilterByDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDensity;
	static void NewProp_bFilterByColor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ColorTolerance;
	static void NewProp_bInvertFilter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertFilter;
	static void NewProp_bSoftFilter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSoftFilter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoftFilterFalloff;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGExampleCustomFilterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByPosition_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomFilterSettings*)Obj)->bFilterByPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByPosition = { "bFilterByPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomFilterSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByPosition_MetaData), NewProp_bFilterByPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_FilterCenter = { "FilterCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, FilterCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterCenter_MetaData), NewProp_FilterCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_FilterRadius = { "FilterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, FilterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterRadius_MetaData), NewProp_FilterRadius_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByDensity_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomFilterSettings*)Obj)->bFilterByDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByDensity = { "bFilterByDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomFilterSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByDensity_MetaData), NewProp_bFilterByDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_MinDensity = { "MinDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, MinDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDensity_MetaData), NewProp_MinDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_MaxDensity = { "MaxDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, MaxDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDensity_MetaData), NewProp_MaxDensity_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByColor_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomFilterSettings*)Obj)->bFilterByColor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByColor = { "bFilterByColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomFilterSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByColor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByColor_MetaData), NewProp_bFilterByColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_TargetColor = { "TargetColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, TargetColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetColor_MetaData), NewProp_TargetColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_ColorTolerance = { "ColorTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, ColorTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorTolerance_MetaData), NewProp_ColorTolerance_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bInvertFilter_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomFilterSettings*)Obj)->bInvertFilter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bInvertFilter = { "bInvertFilter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomFilterSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bInvertFilter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertFilter_MetaData), NewProp_bInvertFilter_MetaData) };
void Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bSoftFilter_SetBit(void* Obj)
{
	((UAuracronPCGExampleCustomFilterSettings*)Obj)->bSoftFilter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bSoftFilter = { "bSoftFilter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGExampleCustomFilterSettings), &Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bSoftFilter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSoftFilter_MetaData), NewProp_bSoftFilter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_SoftFilterFalloff = { "SoftFilterFalloff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleCustomFilterSettings, SoftFilterFalloff), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoftFilterFalloff_MetaData), NewProp_SoftFilterFalloff_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_FilterCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_FilterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_MinDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_MaxDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bFilterByColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_TargetColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_ColorTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bInvertFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_bSoftFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::NewProp_SoftFilterFalloff,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::ClassParams = {
	&UAuracronPCGExampleCustomFilterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGExampleCustomFilterSettings);
UAuracronPCGExampleCustomFilterSettings::~UAuracronPCGExampleCustomFilterSettings() {}
// ********** End Class UAuracronPCGExampleCustomFilterSettings ************************************

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function CreateExampleFilterTemplate 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventCreateExampleFilterTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventCreateExampleFilterTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "CreateExampleFilterTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExampleFilterTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExampleFilterTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execCreateExampleFilterTemplate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::CreateExampleFilterTemplate();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function CreateExampleFilterTemplate *

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function CreateExampleGeneratorTemplate 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventCreateExampleGeneratorTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventCreateExampleGeneratorTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "CreateExampleGeneratorTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExampleGeneratorTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExampleGeneratorTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execCreateExampleGeneratorTemplate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::CreateExampleGeneratorTemplate();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function CreateExampleGeneratorTemplate 

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function CreateExamplePointProcessorTemplate 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventCreateExamplePointProcessorTemplate_Parms
	{
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventCreateExamplePointProcessorTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "CreateExamplePointProcessorTemplate", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExamplePointProcessorTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::AuracronPCGCustomNodeExamplesManager_eventCreateExamplePointProcessorTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execCreateExamplePointProcessorTemplate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::CreateExamplePointProcessorTemplate();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function CreateExamplePointProcessorTemplate 

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function GetAllExampleTemplates ****
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventGetAllExampleTemplates_Parms
	{
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template creation functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template creation functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventGetAllExampleTemplates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "GetAllExampleTemplates", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::AuracronPCGCustomNodeExamplesManager_eventGetAllExampleTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::AuracronPCGCustomNodeExamplesManager_eventGetAllExampleTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execGetAllExampleTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::GetAllExampleTemplates();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function GetAllExampleTemplates ******

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function LoadExampleTemplatesFromFile 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventLoadExampleTemplatesFromFile_Parms
	{
		FString Directory;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Directory_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Directory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::NewProp_Directory = { "Directory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventLoadExampleTemplatesFromFile_Parms, Directory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Directory_MetaData), NewProp_Directory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::NewProp_Directory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "LoadExampleTemplatesFromFile", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::AuracronPCGCustomNodeExamplesManager_eventLoadExampleTemplatesFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::AuracronPCGCustomNodeExamplesManager_eventLoadExampleTemplatesFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execLoadExampleTemplatesFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Directory);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGCustomNodeExamplesManager::LoadExampleTemplatesFromFile(Z_Param_Directory);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function LoadExampleTemplatesFromFile 

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function RegisterAllExampleNodes ***
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Registration functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registration functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "RegisterAllExampleNodes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execRegisterAllExampleNodes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGCustomNodeExamplesManager::RegisterAllExampleNodes();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function RegisterAllExampleNodes *****

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function RegisterExampleFilter *****
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventRegisterExampleFilter_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventRegisterExampleFilter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventRegisterExampleFilter_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "RegisterExampleFilter", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExampleFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExampleFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execRegisterExampleFilter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::RegisterExampleFilter();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function RegisterExampleFilter *******

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function RegisterExampleGenerator **
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventRegisterExampleGenerator_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventRegisterExampleGenerator_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventRegisterExampleGenerator_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "RegisterExampleGenerator", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExampleGenerator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExampleGenerator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execRegisterExampleGenerator)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::RegisterExampleGenerator();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function RegisterExampleGenerator ****

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function RegisterExamplePointProcessor 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventRegisterExamplePointProcessor_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventRegisterExamplePointProcessor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventRegisterExamplePointProcessor_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "RegisterExamplePointProcessor", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExamplePointProcessor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::AuracronPCGCustomNodeExamplesManager_eventRegisterExamplePointProcessor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execRegisterExamplePointProcessor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::RegisterExamplePointProcessor();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function RegisterExamplePointProcessor 

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function SaveExampleTemplatesToFile 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventSaveExampleTemplatesToFile_Parms
	{
		FString Directory;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Directory_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Directory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::NewProp_Directory = { "Directory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventSaveExampleTemplatesToFile_Parms, Directory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Directory_MetaData), NewProp_Directory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::NewProp_Directory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "SaveExampleTemplatesToFile", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::AuracronPCGCustomNodeExamplesManager_eventSaveExampleTemplatesToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::AuracronPCGCustomNodeExamplesManager_eventSaveExampleTemplatesToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execSaveExampleTemplatesToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Directory);
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGCustomNodeExamplesManager::SaveExampleTemplatesToFile(Z_Param_Directory);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function SaveExampleTemplatesToFile **

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeCreation ***
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeCreation_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Testing functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Testing functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeCreation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeCreation_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "TestExampleNodeCreation", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeCreation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeCreation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeCreation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::TestExampleNodeCreation();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeCreation *****

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeExecution **
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeExecution_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeExecution_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeExecution_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "TestExampleNodeExecution", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeExecution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeExecution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeExecution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::TestExampleNodeExecution();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeExecution ****

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeValidation *
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeValidation_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeValidation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeValidation_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "TestExampleNodeValidation", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeValidation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::AuracronPCGCustomNodeExamplesManager_eventTestExampleNodeValidation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::TestExampleNodeValidation();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function TestExampleNodeValidation ***

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function UnregisterAllExampleNodes *
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "UnregisterAllExampleNodes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execUnregisterAllExampleNodes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGCustomNodeExamplesManager::UnregisterAllExampleNodes();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function UnregisterAllExampleNodes ***

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager Function ValidateAllExampleTemplates 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics
{
	struct AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms
	{
		TArray<FString> OutErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Examples" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_OutErrors_Inner = { "OutErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_OutErrors = { "OutErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms, OutErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_OutErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_OutErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, nullptr, "ValidateAllExampleTemplates", Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::AuracronPCGCustomNodeExamplesManager_eventValidateAllExampleTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeExamplesManager::execValidateAllExampleTemplates)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_OutErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeExamplesManager::ValidateAllExampleTemplates(Z_Param_Out_OutErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeExamplesManager Function ValidateAllExampleTemplates *

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager ************************************
void UAuracronPCGCustomNodeExamplesManager::StaticRegisterNativesUAuracronPCGCustomNodeExamplesManager()
{
	UClass* Class = UAuracronPCGCustomNodeExamplesManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateExampleFilterTemplate", &UAuracronPCGCustomNodeExamplesManager::execCreateExampleFilterTemplate },
		{ "CreateExampleGeneratorTemplate", &UAuracronPCGCustomNodeExamplesManager::execCreateExampleGeneratorTemplate },
		{ "CreateExamplePointProcessorTemplate", &UAuracronPCGCustomNodeExamplesManager::execCreateExamplePointProcessorTemplate },
		{ "GetAllExampleTemplates", &UAuracronPCGCustomNodeExamplesManager::execGetAllExampleTemplates },
		{ "LoadExampleTemplatesFromFile", &UAuracronPCGCustomNodeExamplesManager::execLoadExampleTemplatesFromFile },
		{ "RegisterAllExampleNodes", &UAuracronPCGCustomNodeExamplesManager::execRegisterAllExampleNodes },
		{ "RegisterExampleFilter", &UAuracronPCGCustomNodeExamplesManager::execRegisterExampleFilter },
		{ "RegisterExampleGenerator", &UAuracronPCGCustomNodeExamplesManager::execRegisterExampleGenerator },
		{ "RegisterExamplePointProcessor", &UAuracronPCGCustomNodeExamplesManager::execRegisterExamplePointProcessor },
		{ "SaveExampleTemplatesToFile", &UAuracronPCGCustomNodeExamplesManager::execSaveExampleTemplatesToFile },
		{ "TestExampleNodeCreation", &UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeCreation },
		{ "TestExampleNodeExecution", &UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeExecution },
		{ "TestExampleNodeValidation", &UAuracronPCGCustomNodeExamplesManager::execTestExampleNodeValidation },
		{ "UnregisterAllExampleNodes", &UAuracronPCGCustomNodeExamplesManager::execUnregisterAllExampleNodes },
		{ "ValidateAllExampleTemplates", &UAuracronPCGCustomNodeExamplesManager::execValidateAllExampleTemplates },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager;
UClass* UAuracronPCGCustomNodeExamplesManager::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeExamplesManager;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeExamplesManager"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeExamplesManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_NoRegister()
{
	return UAuracronPCGCustomNodeExamplesManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Examples Manager\n * Manages and registers example custom nodes\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeExamples.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Examples Manager\nManages and registers example custom nodes" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleFilterTemplate, "CreateExampleFilterTemplate" }, // 1459072767
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExampleGeneratorTemplate, "CreateExampleGeneratorTemplate" }, // 1830600545
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_CreateExamplePointProcessorTemplate, "CreateExamplePointProcessorTemplate" }, // 1588181668
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_GetAllExampleTemplates, "GetAllExampleTemplates" }, // 458439227
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_LoadExampleTemplatesFromFile, "LoadExampleTemplatesFromFile" }, // 2818838596
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterAllExampleNodes, "RegisterAllExampleNodes" }, // 3078278898
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleFilter, "RegisterExampleFilter" }, // 3954096277
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExampleGenerator, "RegisterExampleGenerator" }, // 2192076506
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_RegisterExamplePointProcessor, "RegisterExamplePointProcessor" }, // 3200075071
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_SaveExampleTemplatesToFile, "SaveExampleTemplatesToFile" }, // 1031033595
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeCreation, "TestExampleNodeCreation" }, // 2864612015
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeExecution, "TestExampleNodeExecution" }, // 936182272
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_TestExampleNodeValidation, "TestExampleNodeValidation" }, // 1462320629
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_UnregisterAllExampleNodes, "UnregisterAllExampleNodes" }, // 1930433752
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeExamplesManager_ValidateAllExampleTemplates, "ValidateAllExampleTemplates" }, // 2839899985
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeExamplesManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::ClassParams = {
	&UAuracronPCGCustomNodeExamplesManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager.OuterSingleton;
}
UAuracronPCGCustomNodeExamplesManager::UAuracronPCGCustomNodeExamplesManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeExamplesManager);
UAuracronPCGCustomNodeExamplesManager::~UAuracronPCGCustomNodeExamplesManager() {}
// ********** End Class UAuracronPCGCustomNodeExamplesManager **************************************

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function AddParametersToNode ********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventAddParametersToNode_Parms
	{
		FAuracronPCGCustomNodeTemplate BaseTemplate;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseTemplate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseTemplate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::NewProp_BaseTemplate = { "BaseTemplate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventAddParametersToNode_Parms, BaseTemplate), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseTemplate_MetaData), NewProp_BaseTemplate_MetaData) }; // 3551892101
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventAddParametersToNode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::NewProp_BaseTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "AddParametersToNode", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventAddParametersToNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventAddParametersToNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execAddParametersToNode)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_BaseTemplate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::AddParametersToNode(Z_Param_Out_BaseTemplate);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function AddParametersToNode **********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function AddPinsToNode **************
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventAddPinsToNode_Parms
	{
		FAuracronPCGCustomNodeTemplate BaseTemplate;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseTemplate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseTemplate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::NewProp_BaseTemplate = { "BaseTemplate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventAddPinsToNode_Parms, BaseTemplate), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseTemplate_MetaData), NewProp_BaseTemplate_MetaData) }; // 3551892101
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventAddPinsToNode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::NewProp_BaseTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "AddPinsToNode", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventAddPinsToNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventAddPinsToNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execAddPinsToNode)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_BaseTemplate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::AddPinsToNode(Z_Param_Out_BaseTemplate);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function AddPinsToNode ****************

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function ConfigureNodeExecution *****
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventConfigureNodeExecution_Parms
	{
		FAuracronPCGCustomNodeTemplate BaseTemplate;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseTemplate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseTemplate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::NewProp_BaseTemplate = { "BaseTemplate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventConfigureNodeExecution_Parms, BaseTemplate), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseTemplate_MetaData), NewProp_BaseTemplate_MetaData) }; // 3551892101
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventConfigureNodeExecution_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::NewProp_BaseTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "ConfigureNodeExecution", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::AuracronPCGCustomNodeTutorialHelper_eventConfigureNodeExecution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::AuracronPCGCustomNodeTutorialHelper_eventConfigureNodeExecution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execConfigureNodeExecution)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_BaseTemplate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::ConfigureNodeExecution(Z_Param_Out_BaseTemplate);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function ConfigureNodeExecution *******

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function CreateBasicCustomNode ******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventCreateBasicCustomNode_Parms
	{
		FString NodeName;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tutorial steps\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tutorial steps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventCreateBasicCustomNode_Parms, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventCreateBasicCustomNode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "CreateBasicCustomNode", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventCreateBasicCustomNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventCreateBasicCustomNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execCreateBasicCustomNode)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::CreateBasicCustomNode(Z_Param_NodeName);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function CreateBasicCustomNode ********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function FinalizeCustomNode *********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventFinalizeCustomNode_Parms
	{
		FAuracronPCGCustomNodeTemplate BaseTemplate;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseTemplate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseTemplate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::NewProp_BaseTemplate = { "BaseTemplate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventFinalizeCustomNode_Parms, BaseTemplate), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseTemplate_MetaData), NewProp_BaseTemplate_MetaData) }; // 3551892101
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventFinalizeCustomNode_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::NewProp_BaseTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "FinalizeCustomNode", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventFinalizeCustomNode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::AuracronPCGCustomNodeTutorialHelper_eventFinalizeCustomNode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execFinalizeCustomNode)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_BaseTemplate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::FinalizeCustomNode(Z_Param_Out_BaseTemplate);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function FinalizeCustomNode ***********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function GetTotalTutorialSteps ******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventGetTotalTutorialSteps_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTotalTutorialSteps_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "GetTotalTutorialSteps", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTotalTutorialSteps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTotalTutorialSteps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execGetTotalTutorialSteps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::GetTotalTutorialSteps();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function GetTotalTutorialSteps ********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExample *********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExample_Parms
	{
		int32 ExampleIndex;
		FAuracronPCGCustomNodeTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExampleIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::NewProp_ExampleIndex = { "ExampleIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExample_Parms, ExampleIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExample_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::NewProp_ExampleIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "GetTutorialExample", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExample)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ExampleIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPCGCustomNodeTemplate*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::GetTutorialExample(Z_Param_ExampleIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExample ***********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExampleDescription 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExampleDescription_Parms
	{
		int32 ExampleIndex;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExampleIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::NewProp_ExampleIndex = { "ExampleIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExampleDescription_Parms, ExampleIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExampleDescription_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::NewProp_ExampleIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "GetTutorialExampleDescription", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExampleDescription_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExampleDescription_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExampleDescription)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ExampleIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::GetTutorialExampleDescription(Z_Param_ExampleIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExampleDescription 

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExamples ********
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExamples_Parms
	{
		TArray<FAuracronPCGCustomNodeTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tutorial examples\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tutorial examples" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExamples_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3551892101
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "GetTutorialExamples", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExamples_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialExamples_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExamples)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPCGCustomNodeTemplate>*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::GetTutorialExamples();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialExamples **********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialStepInstructions 
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventGetTutorialStepInstructions_Parms
	{
		int32 StepNumber;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepNumber;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_StepNumber = { "StepNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialStepInstructions_Parms, StepNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventGetTutorialStepInstructions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_StepNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "GetTutorialStepInstructions", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialStepInstructions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::AuracronPCGCustomNodeTutorialHelper_eventGetTutorialStepInstructions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execGetTutorialStepInstructions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepNumber);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::GetTutorialStepInstructions(Z_Param_StepNumber);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function GetTutorialStepInstructions **

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper Function ValidateTutorialStep *******
struct Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics
{
	struct AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms
	{
		FAuracronPCGCustomNodeTemplate Template;
		int32 StepNumber;
		FString OutFeedback;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Node Tutorial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tutorial validation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tutorial validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepNumber;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutFeedback;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms, Template), Z_Construct_UScriptStruct_FAuracronPCGCustomNodeTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 3551892101
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_StepNumber = { "StepNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms, StepNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_OutFeedback = { "OutFeedback", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms, OutFeedback), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms), &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_StepNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_OutFeedback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, nullptr, "ValidateTutorialStep", Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::AuracronPCGCustomNodeTutorialHelper_eventValidateTutorialStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGCustomNodeTutorialHelper::execValidateTutorialStep)
{
	P_GET_STRUCT_REF(FAuracronPCGCustomNodeTemplate,Z_Param_Out_Template);
	P_GET_PROPERTY(FIntProperty,Z_Param_StepNumber);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutFeedback);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGCustomNodeTutorialHelper::ValidateTutorialStep(Z_Param_Out_Template,Z_Param_StepNumber,Z_Param_Out_OutFeedback);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper Function ValidateTutorialStep *********

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper *************************************
void UAuracronPCGCustomNodeTutorialHelper::StaticRegisterNativesUAuracronPCGCustomNodeTutorialHelper()
{
	UClass* Class = UAuracronPCGCustomNodeTutorialHelper::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddParametersToNode", &UAuracronPCGCustomNodeTutorialHelper::execAddParametersToNode },
		{ "AddPinsToNode", &UAuracronPCGCustomNodeTutorialHelper::execAddPinsToNode },
		{ "ConfigureNodeExecution", &UAuracronPCGCustomNodeTutorialHelper::execConfigureNodeExecution },
		{ "CreateBasicCustomNode", &UAuracronPCGCustomNodeTutorialHelper::execCreateBasicCustomNode },
		{ "FinalizeCustomNode", &UAuracronPCGCustomNodeTutorialHelper::execFinalizeCustomNode },
		{ "GetTotalTutorialSteps", &UAuracronPCGCustomNodeTutorialHelper::execGetTotalTutorialSteps },
		{ "GetTutorialExample", &UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExample },
		{ "GetTutorialExampleDescription", &UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExampleDescription },
		{ "GetTutorialExamples", &UAuracronPCGCustomNodeTutorialHelper::execGetTutorialExamples },
		{ "GetTutorialStepInstructions", &UAuracronPCGCustomNodeTutorialHelper::execGetTutorialStepInstructions },
		{ "ValidateTutorialStep", &UAuracronPCGCustomNodeTutorialHelper::execValidateTutorialStep },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper;
UClass* UAuracronPCGCustomNodeTutorialHelper::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGCustomNodeTutorialHelper;
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGCustomNodeTutorialHelper"),
			Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.InnerSingleton,
			StaticRegisterNativesUAuracronPCGCustomNodeTutorialHelper,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_NoRegister()
{
	return UAuracronPCGCustomNodeTutorialHelper::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom Node Tutorial Helper\n * Provides step-by-step guidance for creating custom nodes\n */" },
#endif
		{ "IncludePath", "AuracronPCGCustomNodeExamples.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGCustomNodeExamples.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Node Tutorial Helper\nProvides step-by-step guidance for creating custom nodes" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddParametersToNode, "AddParametersToNode" }, // 3447908036
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_AddPinsToNode, "AddPinsToNode" }, // 967500689
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ConfigureNodeExecution, "ConfigureNodeExecution" }, // 2869116055
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_CreateBasicCustomNode, "CreateBasicCustomNode" }, // 3665810562
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_FinalizeCustomNode, "FinalizeCustomNode" }, // 1749764022
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTotalTutorialSteps, "GetTotalTutorialSteps" }, // 1225804678
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExample, "GetTutorialExample" }, // 1867163241
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExampleDescription, "GetTutorialExampleDescription" }, // 431593049
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialExamples, "GetTutorialExamples" }, // 182178770
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_GetTutorialStepInstructions, "GetTutorialStepInstructions" }, // 3813741494
		{ &Z_Construct_UFunction_UAuracronPCGCustomNodeTutorialHelper_ValidateTutorialStep, "ValidateTutorialStep" }, // 2871411423
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGCustomNodeTutorialHelper>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::ClassParams = {
	&UAuracronPCGCustomNodeTutorialHelper::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.OuterSingleton, Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper.OuterSingleton;
}
UAuracronPCGCustomNodeTutorialHelper::UAuracronPCGCustomNodeTutorialHelper(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGCustomNodeTutorialHelper);
UAuracronPCGCustomNodeTutorialHelper::~UAuracronPCGCustomNodeTutorialHelper() {}
// ********** End Class UAuracronPCGCustomNodeTutorialHelper ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings, UAuracronPCGExampleCustomPointProcessorSettings::StaticClass, TEXT("UAuracronPCGExampleCustomPointProcessorSettings"), &Z_Registration_Info_UClass_UAuracronPCGExampleCustomPointProcessorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGExampleCustomPointProcessorSettings), 2665330897U) },
		{ Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings, UAuracronPCGExampleCustomGeneratorSettings::StaticClass, TEXT("UAuracronPCGExampleCustomGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGExampleCustomGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGExampleCustomGeneratorSettings), 3471509224U) },
		{ Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings, UAuracronPCGExampleCustomFilterSettings::StaticClass, TEXT("UAuracronPCGExampleCustomFilterSettings"), &Z_Registration_Info_UClass_UAuracronPCGExampleCustomFilterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGExampleCustomFilterSettings), 200198855U) },
		{ Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager, UAuracronPCGCustomNodeExamplesManager::StaticClass, TEXT("UAuracronPCGCustomNodeExamplesManager"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeExamplesManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeExamplesManager), 2080497654U) },
		{ Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper, UAuracronPCGCustomNodeTutorialHelper::StaticClass, TEXT("UAuracronPCGCustomNodeTutorialHelper"), &Z_Registration_Info_UClass_UAuracronPCGCustomNodeTutorialHelper, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGCustomNodeTutorialHelper), 2187732616U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h__Script_AuracronPCGBridge_88202998(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
