// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageLOD.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageLOD_generated_h
#error "AuracronFoliageLOD.generated.h already included, missing '#pragma once' in AuracronFoliageLOD.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageLOD_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronFoliageLODManager;
class UHierarchicalInstancedStaticMeshComponent;
class UStaticMesh;
class UWorld;
enum class EAuracronLODQualityLevel : uint8;
struct FAuracronBillboardData;
struct FAuracronImpostorData;
struct FAuracronLODConfiguration;
struct FAuracronLODInstanceData;
struct FAuracronLODPerformanceData;

// ********** Begin ScriptStruct FAuracronFoliageLODConfiguration **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_121_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFoliageLODConfiguration;
// ********** End ScriptStruct FAuracronFoliageLODConfiguration ************************************

// ********** Begin ScriptStruct FAuracronImpostorData *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_259_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronImpostorData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronImpostorData;
// ********** End ScriptStruct FAuracronImpostorData ***********************************************

// ********** Begin ScriptStruct FAuracronBillboardData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_348_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBillboardData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBillboardData;
// ********** End ScriptStruct FAuracronBillboardData **********************************************

// ********** Begin ScriptStruct FAuracronLODPerformanceData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_418_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLODPerformanceData;
// ********** End ScriptStruct FAuracronLODPerformanceData *****************************************

// ********** Begin ScriptStruct FAuracronLODInstanceData ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_504_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLODInstanceData;
// ********** End ScriptStruct FAuracronLODInstanceData ********************************************

// ********** Begin Delegate FOnLODChanged *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_760_DELEGATE \
static void FOnLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLODChanged, const FString& InstanceId, int32 OldLOD, int32 NewLOD);


// ********** End Delegate FOnLODChanged ***********************************************************

// ********** Begin Delegate FOnInstanceCulled *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_761_DELEGATE \
static void FOnInstanceCulled_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceCulled, const FString& InstanceId, bool bCulled);


// ********** End Delegate FOnInstanceCulled *******************************************************

// ********** Begin Delegate FOnImpostorGenerated **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_762_DELEGATE \
static void FOnImpostorGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnImpostorGenerated, const FString& ImpostorId, bool bSuccess);


// ********** End Delegate FOnImpostorGenerated ****************************************************

// ********** Begin Delegate FOnBillboardGenerated *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_763_DELEGATE \
static void FOnBillboardGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnBillboardGenerated, const FString& BillboardId, bool bSuccess);


// ********** End Delegate FOnBillboardGenerated ***************************************************

// ********** Begin Class UAuracronFoliageLODManager ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogPerformanceStatistics); \
	DECLARE_FUNCTION(execDrawDebugLODInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execIsInstanceTransitioning); \
	DECLARE_FUNCTION(execUpdateLODTransitions); \
	DECLARE_FUNCTION(execStartLODTransition); \
	DECLARE_FUNCTION(execApplyBiomeLODToInstances); \
	DECLARE_FUNCTION(execGetBiomeLODSettings); \
	DECLARE_FUNCTION(execSetBiomeLODSettings); \
	DECLARE_FUNCTION(execGetLODDistribution); \
	DECLARE_FUNCTION(execGetVisibleInstanceCount); \
	DECLARE_FUNCTION(execGetTotalInstanceCount); \
	DECLARE_FUNCTION(execGetAverageFrameTime); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execSetHISMCullingDistance); \
	DECLARE_FUNCTION(execUpdateHISMInstances); \
	DECLARE_FUNCTION(execGetOrCreateHISMComponent); \
	DECLARE_FUNCTION(execOptimizeHISMComponents); \
	DECLARE_FUNCTION(execUpdateInstanceVisibility); \
	DECLARE_FUNCTION(execUpdateInstanceLOD); \
	DECLARE_FUNCTION(execGetInstancesInRadius); \
	DECLARE_FUNCTION(execGetLODInstance); \
	DECLARE_FUNCTION(execUnregisterInstance); \
	DECLARE_FUNCTION(execRegisterInstance); \
	DECLARE_FUNCTION(execGetAllBillboards); \
	DECLARE_FUNCTION(execGetBillboard); \
	DECLARE_FUNCTION(execUnregisterBillboard); \
	DECLARE_FUNCTION(execRegisterBillboard); \
	DECLARE_FUNCTION(execGenerateBillboard); \
	DECLARE_FUNCTION(execBatchGenerateImpostors); \
	DECLARE_FUNCTION(execGetAllImpostors); \
	DECLARE_FUNCTION(execGetImpostor); \
	DECLARE_FUNCTION(execUnregisterImpostor); \
	DECLARE_FUNCTION(execRegisterImpostor); \
	DECLARE_FUNCTION(execGenerateImpostor); \
	DECLARE_FUNCTION(execGetCullDistances); \
	DECLARE_FUNCTION(execSetCullDistances); \
	DECLARE_FUNCTION(execShouldCullInstance); \
	DECLARE_FUNCTION(execUpdateCulling); \
	DECLARE_FUNCTION(execGetLODDistances); \
	DECLARE_FUNCTION(execSetLODDistances); \
	DECLARE_FUNCTION(execCalculateFadeAmount); \
	DECLARE_FUNCTION(execCalculateLODLevel); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLOD); \
	DECLARE_FUNCTION(execGetQualityLevel); \
	DECLARE_FUNCTION(execSetQualityLevel); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageLODManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageLODManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageLODManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageLODManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageLODManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageLODManager(UAuracronFoliageLODManager&&) = delete; \
	UAuracronFoliageLODManager(const UAuracronFoliageLODManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageLODManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageLODManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageLODManager) \
	NO_API virtual ~UAuracronFoliageLODManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_575_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h_578_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageLODManager;

// ********** End Class UAuracronFoliageLODManager *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h

// ********** Begin Enum EAuracronFoliageLODType ***************************************************
#define FOREACH_ENUM_EAURACRONFOLIAGELODTYPE(op) \
	op(EAuracronFoliageLODType::DistanceBased) \
	op(EAuracronFoliageLODType::Impostor) \
	op(EAuracronFoliageLODType::Billboard) \
	op(EAuracronFoliageLODType::Nanite) \
	op(EAuracronFoliageLODType::Hybrid) \
	op(EAuracronFoliageLODType::Custom) 

enum class EAuracronFoliageLODType : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageLODType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLODType>();
// ********** End Enum EAuracronFoliageLODType *****************************************************

// ********** Begin Enum EAuracronImpostorType *****************************************************
#define FOREACH_ENUM_EAURACRONIMPOSTORTYPE(op) \
	op(EAuracronImpostorType::FullSphere) \
	op(EAuracronImpostorType::UpperHemisphere) \
	op(EAuracronImpostorType::TraditionalBillboard) \
	op(EAuracronImpostorType::Custom) 

enum class EAuracronImpostorType : uint8;
template<> struct TIsUEnumClass<EAuracronImpostorType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronImpostorType>();
// ********** End Enum EAuracronImpostorType *******************************************************

// ********** Begin Enum EAuracronLODTransitionType ************************************************
#define FOREACH_ENUM_EAURACRONLODTRANSITIONTYPE(op) \
	op(EAuracronLODTransitionType::Instant) \
	op(EAuracronLODTransitionType::Fade) \
	op(EAuracronLODTransitionType::Dither) \
	op(EAuracronLODTransitionType::Crossfade) \
	op(EAuracronLODTransitionType::Custom) 

enum class EAuracronLODTransitionType : uint8;
template<> struct TIsUEnumClass<EAuracronLODTransitionType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLODTransitionType>();
// ********** End Enum EAuracronLODTransitionType **************************************************

// ********** Begin Enum EAuracronCullingType ******************************************************
#define FOREACH_ENUM_EAURACRONCULLINGTYPE(op) \
	op(EAuracronCullingType::Distance) \
	op(EAuracronCullingType::Frustum) \
	op(EAuracronCullingType::Occlusion) \
	op(EAuracronCullingType::Combined) \
	op(EAuracronCullingType::Adaptive) \
	op(EAuracronCullingType::Custom) 

enum class EAuracronCullingType : uint8;
template<> struct TIsUEnumClass<EAuracronCullingType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronCullingType>();
// ********** End Enum EAuracronCullingType ********************************************************

// ********** Begin Enum EAuracronLODQualityLevel **************************************************
#define FOREACH_ENUM_EAURACRONLODQUALITYLEVEL(op) \
	op(EAuracronLODQualityLevel::Low) \
	op(EAuracronLODQualityLevel::Medium) \
	op(EAuracronLODQualityLevel::High) \
	op(EAuracronLODQualityLevel::Epic) \
	op(EAuracronLODQualityLevel::Cinematic) \
	op(EAuracronLODQualityLevel::Custom) 

enum class EAuracronLODQualityLevel : uint8;
template<> struct TIsUEnumClass<EAuracronLODQualityLevel> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLODQualityLevel>();
// ********** End Enum EAuracronLODQualityLevel ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
