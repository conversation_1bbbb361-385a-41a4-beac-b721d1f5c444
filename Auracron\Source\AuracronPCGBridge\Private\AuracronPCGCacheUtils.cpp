// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Cache Utilities Implementation
// Bridge 2.17: PCG Framework - Caching System

#include "AuracronPCGCachingSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Misc/SecureHash.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Serialization/ObjectAndNameAsStringProxyArchive.h"

// =============================================================================
// CACHE UTILITIES IMPLEMENTATION
// =============================================================================

FAuracronPCGCacheKey UAuracronPCGCacheUtils::GenerateKeyForNode(const FString& NodeId, UPCGSettings* Settings)
{
    FAuracronPCGCacheKey Key;
    Key.NodeId = NodeId;
    Key.EntryType = EAuracronPCGCacheEntryType::NodeResult;
    Key.Version = 1;
    
    if (Settings)
    {
        Key.ParametersHash = GenerateHashForSettings(Settings);
        Key.GraphId = Settings->GetOuter() ? Settings->GetOuter()->GetName() : TEXT("");
    }
    
    return Key;
}

FAuracronPCGCacheKey UAuracronPCGCacheUtils::GenerateKeyForGraph(const FString& GraphId, const TMap<FString, FString>& Parameters)
{
    FAuracronPCGCacheKey Key;
    Key.GraphId = GraphId;
    Key.EntryType = EAuracronPCGCacheEntryType::GraphResult;
    Key.Version = 1;
    
    // Generate hash from parameters
    FString ParametersString;
    for (const auto& ParamPair : Parameters)
    {
        ParametersString += FString::Printf(TEXT("%s=%s;"), *ParamPair.Key, *ParamPair.Value);
    }
    Key.ParametersHash = FMD5::HashAnsiString(*ParametersString);
    
    return Key;
}

FString UAuracronPCGCacheUtils::GenerateHashForData(UPCGData* Data)
{
    if (!Data)
    {
        return TEXT("");
    }
    
    // Serialize the data to generate a hash
    TArray<uint8> SerializedData;
    FMemoryWriter Writer(SerializedData);
    FObjectAndNameAsStringProxyArchive ProxyWriter(Writer, false);
    
    // Serialize key properties that affect the data
    if (UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        ProxyWriter << const_cast<TArray<FPCGPoint>&>(Points);
        
        if (const UPCGMetadata* Metadata = PointData->Metadata)
        {
            // Serialize metadata attributes
            TArray<FName> AttributeNames;
            TArray<EPCGMetadataTypes> AttributeTypes;
            Metadata->GetAttributes(AttributeNames, AttributeTypes);
            ProxyWriter << AttributeNames;
            ProxyWriter << AttributeTypes;
        }
    }
    else if (UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        FBox Bounds = SpatialData->GetBounds();
        ProxyWriter << Bounds;
        
        FTransform Transform = SpatialData->GetTransform();
        ProxyWriter << Transform;
    }
    
    return FMD5::HashBytes(SerializedData.GetData(), SerializedData.Num());
}

FString UAuracronPCGCacheUtils::GenerateHashForSettings(UPCGSettings* Settings)
{
    if (!Settings)
    {
        return TEXT("");
    }
    
    // Serialize the settings to generate a hash
    TArray<uint8> SerializedData;
    FMemoryWriter Writer(SerializedData);
    FObjectAndNameAsStringProxyArchive ProxyWriter(Writer, false);
    
    // Serialize the settings object
    Settings->Serialize(ProxyWriter);
    
    return FMD5::HashBytes(SerializedData.GetData(), SerializedData.Num());
}

TArray<uint8> UAuracronPCGCacheUtils::SerializePointData(UPCGPointData* PointData)
{
    TArray<uint8> SerializedData;
    
    if (!PointData)
    {
        return SerializedData;
    }
    
    FMemoryWriter Writer(SerializedData);
    FObjectAndNameAsStringProxyArchive ProxyWriter(Writer, false);
    
    // Serialize points
    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    ProxyWriter << const_cast<TArray<FPCGPoint>&>(Points);
    
    // Serialize metadata
    if (PointData->Metadata)
    {
        bool bHasMetadata = true;
        ProxyWriter << bHasMetadata;
        
        TArray<FName> AttributeNames;
        TArray<EPCGMetadataTypes> AttributeTypes;
        PointData->Metadata->GetAttributes(AttributeNames, AttributeTypes);
        ProxyWriter << AttributeNames;
        ProxyWriter << AttributeTypes;
        
        // Serialize attribute values for each point
        for (int32 PointIndex = 0; PointIndex < Points.Num(); PointIndex++)
        {
            for (int32 AttrIndex = 0; AttrIndex < AttributeNames.Num(); AttrIndex++)
            {
                const FName& AttrName = AttributeNames[AttrIndex];
                EPCGMetadataTypes AttrType = AttributeTypes[AttrIndex];
                
                // Serialize based on attribute type
                switch (AttrType)
                {
                    case EPCGMetadataTypes::Float:
                    {
                        float Value = PointData->Metadata->GetFloatAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Double:
                    {
                        double Value = PointData->Metadata->GetDoubleAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Integer32:
                    {
                        int32 Value = PointData->Metadata->GetIntAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Integer64:
                    {
                        int64 Value = PointData->Metadata->GetInt64Attribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Vector2:
                    {
                        FVector2D Value = PointData->Metadata->GetVector2Attribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Vector:
                    {
                        FVector Value = PointData->Metadata->GetVectorAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Vector4:
                    {
                        FVector4 Value = PointData->Metadata->GetVector4Attribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Quaternion:
                    {
                        FQuat Value = PointData->Metadata->GetQuatAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Transform:
                    {
                        FTransform Value = PointData->Metadata->GetTransformAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::String:
                    {
                        FString Value = PointData->Metadata->GetStringAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Boolean:
                    {
                        bool Value = PointData->Metadata->GetBoolAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Rotator:
                    {
                        FRotator Value = PointData->Metadata->GetRotatorAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    case EPCGMetadataTypes::Name:
                    {
                        FName Value = PointData->Metadata->GetNameAttribute(PointIndex, AttrName);
                        ProxyWriter << Value;
                        break;
                    }
                    default:
                        break;
                }
            }
        }
    }
    else
    {
        bool bHasMetadata = false;
        ProxyWriter << bHasMetadata;
    }
    
    return SerializedData;
}

UPCGPointData* UAuracronPCGCacheUtils::DeserializePointData(const TArray<uint8>& Data)
{
    if (Data.Num() == 0)
    {
        return nullptr;
    }
    
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    
    FMemoryReader Reader(Data);
    FObjectAndNameAsStringProxyArchive ProxyReader(Reader, false);
    
    // Deserialize points
    TArray<FPCGPoint> Points;
    ProxyReader << Points;
    PointData->GetMutablePoints() = Points;
    
    // Deserialize metadata
    bool bHasMetadata;
    ProxyReader << bHasMetadata;
    
    if (bHasMetadata)
    {
        PointData->InitializeFromData(PointData);
        
        TArray<FName> AttributeNames;
        TArray<EPCGMetadataTypes> AttributeTypes;
        ProxyReader << AttributeNames;
        ProxyReader << AttributeTypes;
        
        // Create attributes
        for (int32 AttrIndex = 0; AttrIndex < AttributeNames.Num(); AttrIndex++)
        {
            const FName& AttrName = AttributeNames[AttrIndex];
            EPCGMetadataTypes AttrType = AttributeTypes[AttrIndex];
            
            PointData->Metadata->CreateAttribute(AttrName, AttrType, false, false);
        }
        
        // Deserialize attribute values
        for (int32 PointIndex = 0; PointIndex < Points.Num(); PointIndex++)
        {
            for (int32 AttrIndex = 0; AttrIndex < AttributeNames.Num(); AttrIndex++)
            {
                const FName& AttrName = AttributeNames[AttrIndex];
                EPCGMetadataTypes AttrType = AttributeTypes[AttrIndex];
                
                // Deserialize based on attribute type
                switch (AttrType)
                {
                    case EPCGMetadataTypes::Float:
                    {
                        float Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetFloatAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Double:
                    {
                        double Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetDoubleAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Integer32:
                    {
                        int32 Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetIntAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Integer64:
                    {
                        int64 Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetInt64Attribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Vector2:
                    {
                        FVector2D Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetVector2Attribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Vector:
                    {
                        FVector Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetVectorAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Vector4:
                    {
                        FVector4 Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetVector4Attribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Quaternion:
                    {
                        FQuat Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetQuatAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Transform:
                    {
                        FTransform Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetTransformAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::String:
                    {
                        FString Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetStringAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Boolean:
                    {
                        bool Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetBoolAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Rotator:
                    {
                        FRotator Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetRotatorAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    case EPCGMetadataTypes::Name:
                    {
                        FName Value;
                        ProxyReader << Value;
                        PointData->Metadata->SetNameAttribute(PointIndex, AttrName, Value);
                        break;
                    }
                    default:
                        break;
                }
            }
        }
    }
    
    return PointData;
}

bool UAuracronPCGCacheUtils::ValidateCacheEntry(const FAuracronPCGCacheEntry& Entry)
{
    // Check if entry has valid key
    if (!ValidateCacheKey(Entry.Key))
    {
        return false;
    }
    
    // Check if entry has data
    if (Entry.SerializedData.Num() == 0)
    {
        return false;
    }
    
    // Check if entry is not expired
    if (Entry.IsExpired())
    {
        return false;
    }
    
    // Check data size consistency
    if (Entry.DataSizeBytes != Entry.SerializedData.Num())
    {
        return false;
    }
    
    return true;
}

bool UAuracronPCGCacheUtils::ValidateCacheKey(const FAuracronPCGCacheKey& Key)
{
    // Check if key has required fields
    if (Key.NodeId.IsEmpty() && Key.GraphId.IsEmpty())
    {
        return false;
    }
    
    // Check version
    if (Key.Version <= 0)
    {
        return false;
    }
    
    return true;
}

bool UAuracronPCGCacheUtils::ShouldCacheResult(float ComputationTime, int32 DataSize)
{
    // Don't cache very small computations (< 10ms)
    if (ComputationTime < 0.01f)
    {
        return false;
    }
    
    // Don't cache very large data (> 100MB)
    if (DataSize > 100 * 1024 * 1024)
    {
        return false;
    }
    
    // Cache if computation time is significant or data is substantial
    return ComputationTime > 0.1f || DataSize > 1024; // 100ms or 1KB
}

float UAuracronPCGCacheUtils::EstimateCacheValue(float ComputationTime, int32 DataSize, int32 AccessFrequency)
{
    // Simple cache value estimation
    float TimeValue = ComputationTime * AccessFrequency;
    float SizePenalty = static_cast<float>(DataSize) / (1024.0f * 1024.0f); // MB
    
    return FMath::Max(0.0f, TimeValue - SizePenalty);
}

FString UAuracronPCGCacheUtils::CacheKeyToString(const FAuracronPCGCacheKey& Key)
{
    return FString::Printf(TEXT("CacheKey[Node:%s, Graph:%s, Params:%s, Input:%s, Type:%d, Ver:%d]"),
        *Key.NodeId, *Key.GraphId, *Key.ParametersHash, *Key.InputDataHash, (int32)Key.EntryType, Key.Version);
}

FString UAuracronPCGCacheUtils::CacheEntryToString(const FAuracronPCGCacheEntry& Entry)
{
    return FString::Printf(TEXT("CacheEntry[Key:%s, Size:%d, Created:%s, Accessed:%s, Count:%d]"),
        *CacheKeyToString(Entry.Key), Entry.DataSizeBytes, 
        *Entry.CreationTime.ToString(), *Entry.LastAccessTime.ToString(), Entry.AccessCount);
}
