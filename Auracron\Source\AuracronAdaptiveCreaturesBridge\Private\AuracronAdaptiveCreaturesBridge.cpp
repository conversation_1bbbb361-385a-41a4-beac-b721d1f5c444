// AURACRON - Implementação do Bridge C++ para Sistema de Criaturas Neutras Adaptativas
// Integração com Unreal Engine 5.6 Mass Entity, State Trees e AI
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#include "AuracronAdaptiveCreaturesBridge.h"
#include "MassEntitySubsystem.h"
#include "MassEntityTemplateRegistry.h"
#include "StateTree.h"
#include "StateTreeExecutionContext.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "NavigationSystem.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/KismetMathLibrary.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronAdaptiveCreaturesBridge::UAuracronAdaptiveCreaturesBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.2f; // Tick a cada 200ms para performance

    // Configurações padrão
    MaxSimultaneousCreatures = 1000;
    bUseMultiThreading = true;
    AdaptationUpdateInterval = 1.0f;
    PlayerDetectionRadius = 1000.0f;
    GenerationSeed = 54321;
    
    // Inicializar configurações padrão
    InitializeDefaultConfigurations();
}

void UAuracronAdaptiveCreaturesBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Criaturas Neutras Adaptativas"));

    // Obter subsistema Mass Entity
    if (UWorld* World = GetWorld())
    {
        MassEntitySubsystem = World->GetSubsystem<UMassEntitySubsystem>();
        if (!MassEntitySubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao obter MassEntitySubsystem"));
            return;
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeCreatureSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Criaturas Adaptativas inicializado com sucesso"));
        
        // Configurar timer de atualização de adaptações
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(AdaptationUpdateTimer, this, 
                &UAuracronAdaptiveCreaturesBridge::UpdateAllAdaptations, AdaptationUpdateInterval, true, AdaptationUpdateInterval);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Criaturas Adaptativas"));
    }
}

void UAuracronAdaptiveCreaturesBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (GetWorld() && AdaptationUpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(AdaptationUpdateTimer);
    }
    
    // Remover todas as criaturas
    RemoveAllCreatures();
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronAdaptiveCreaturesBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bSystemInitialized || !MassEntitySubsystem)
    {
        return;
    }
    
    // Atualização leve por tick
    if (GetWorld())
    {
        // Detectar jogador próximo
        APawn* PlayerPawn = GetWorld()->GetFirstPlayerController()->GetPawn();
        if (PlayerPawn)
        {
            FVector PlayerLocation = PlayerPawn->GetActorLocation();
            
            // Atualizar posição do jogador se mudou significativamente
            if (FVector::Dist(PlayerLocation, LastKnownPlayerLocation) > 100.0f)
            {
                LastKnownPlayerLocation = PlayerLocation;
                
                // Calcular nível de ameaça baseado na velocidade e ações do jogador
                float PlayerThreatLevel = CalculatePlayerThreatLevel(PlayerPawn);
                if (FMath::Abs(PlayerThreatLevel - LastPlayerThreatLevel) > 0.1f)
                {
                    LastPlayerThreatLevel = PlayerThreatLevel;
                    ProcessPlayerBehaviorAdaptations(PlayerLocation, PlayerThreatLevel);
                }
            }
        }
    }
}

// === Core Creature Management ===

bool UAuracronAdaptiveCreaturesBridge::SpawnCreatures(const TArray<FCreatureSpawnData>& SpawnData)
{
    if (!MassEntitySubsystem || SpawnData.Num() == 0)
    {
        return false;
    }
    
    FScopeLock Lock(&CreatureManagementMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawnando %d criaturas"), SpawnData.Num());
    
    int32 SuccessfulSpawns = 0;
    
    for (const FCreatureSpawnData& Data : SpawnData)
    {
        if (ActiveCreatures.Num() >= MaxSimultaneousCreatures)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Limite máximo de criaturas atingido (%d)"), MaxSimultaneousCreatures);
            break;
        }
        
        if (Data.PackSize > 1)
        {
            // Spawnar pack
            TArray<FMassEntityHandle> PackEntities = SpawnCreaturePack(Data);
            SuccessfulSpawns += PackEntities.Num();
        }
        else
        {
            // Spawnar criatura individual
            FMassEntityHandle EntityHandle = SpawnSingleCreature(Data);
            if (EntityHandle.IsValid())
            {
                SuccessfulSpawns++;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d criaturas spawnadas com sucesso"), SuccessfulSpawns);
    return SuccessfulSpawns > 0;
}

FMassEntityHandle UAuracronAdaptiveCreaturesBridge::SpawnSingleCreature(const FCreatureSpawnData& SpawnData)
{
    if (!MassEntitySubsystem)
    {
        return FMassEntityHandle();
    }
    
    // Criar entidade Mass
    FMassEntityHandle EntityHandle = MassEntitySubsystem->CreateEntity();
    if (!EntityHandle.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar entidade Mass"));
        return FMassEntityHandle();
    }
    
    // Configurar fragment da criatura
    FMassCreatureFragment CreatureFragment;
    CreatureFragment.CreatureType = SpawnData.CreatureType;
    CreatureFragment.RealmType = SpawnData.RealmType;
    CreatureFragment.CurrentBehaviorState = EBehaviorState::Passive;
    CreatureFragment.Properties = SpawnData.Properties;
    CreatureFragment.ActiveAdaptations = SpawnData.InitialAdaptations;
    CreatureFragment.HomeLocation = SpawnData.SpawnLocation;
    CreatureFragment.TargetLocation = SpawnData.SpawnLocation;
    CreatureFragment.LastPlayerInteractionTime = 0.0f;
    CreatureFragment.ThreatLevel = 0.0f;
    CreatureFragment.PackID = -1;
    CreatureFragment.bIsPackLeader = false;
    
    // Adicionar fragment à entidade
    MassEntitySubsystem->GetMutableEntityManager().AddFragmentToEntity(EntityHandle, CreatureFragment);
    
    // Adicionar tag
    FMassCreatureTag CreatureTag;
    MassEntitySubsystem->GetMutableEntityManager().AddTagToEntity(EntityHandle, CreatureTag);
    
    // Configurar transform
    FTransform SpawnTransform(SpawnData.SpawnRotation, SpawnData.SpawnLocation, FVector::OneVector);
    MassEntitySubsystem->GetMutableEntityManager().AddFragmentToEntity(EntityHandle, FTransformFragment(SpawnTransform));
    
    // Adicionar à lista de criaturas ativas
    ActiveCreatures.Add(EntityHandle);
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Criatura %d spawnada em %s"), 
           (int32)SpawnData.CreatureType, *SpawnData.SpawnLocation.ToString());
    
    return EntityHandle;
}

TArray<FMassEntityHandle> UAuracronAdaptiveCreaturesBridge::SpawnCreaturePack(const FCreatureSpawnData& LeaderSpawnData)
{
    TArray<FMassEntityHandle> PackEntities;
    
    if (LeaderSpawnData.PackSize <= 1)
    {
        return PackEntities;
    }
    
    int32 PackID = NextPackID++;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawnando pack %d com %d criaturas"), PackID, LeaderSpawnData.PackSize);
    
    // Spawnar líder do pack
    FCreatureSpawnData LeaderData = LeaderSpawnData;
    FMassEntityHandle LeaderHandle = SpawnSingleCreature(LeaderData);
    
    if (LeaderHandle.IsValid())
    {
        // Configurar como líder
        if (FMassCreatureFragment* CreatureFragment = MassEntitySubsystem->GetMutableEntityManager().GetFragmentDataPtr<FMassCreatureFragment>(LeaderHandle))
        {
            CreatureFragment->PackID = PackID;
            CreatureFragment->bIsPackLeader = true;
        }
        
        PackEntities.Add(LeaderHandle);
        
        // Spawnar membros do pack
        for (int32 i = 1; i < LeaderSpawnData.PackSize; i++)
        {
            // Posição aleatória ao redor do líder
            FVector MemberLocation = LeaderSpawnData.SpawnLocation + FVector(
                FMath::RandRange(-LeaderSpawnData.SpawnRadius, LeaderSpawnData.SpawnRadius),
                FMath::RandRange(-LeaderSpawnData.SpawnRadius, LeaderSpawnData.SpawnRadius),
                0.0f
            );
            
            FCreatureSpawnData MemberData = LeaderSpawnData;
            MemberData.SpawnLocation = MemberLocation;
            MemberData.PackSize = 1; // Evitar recursão
            
            FMassEntityHandle MemberHandle = SpawnSingleCreature(MemberData);
            if (MemberHandle.IsValid())
            {
                // Configurar como membro do pack
                if (FMassCreatureFragment* CreatureFragment = MassEntitySubsystem->GetMutableEntityManager().GetFragmentDataPtr<FMassCreatureFragment>(MemberHandle))
                {
                    CreatureFragment->PackID = PackID;
                    CreatureFragment->bIsPackLeader = false;
                }
                
                PackEntities.Add(MemberHandle);
            }
        }
        
        // Registrar pack
        CreaturePacks.Add(PackID, PackEntities);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pack %d criado com %d criaturas"), PackID, PackEntities.Num());
    return PackEntities;
}

bool UAuracronAdaptiveCreaturesBridge::RemoveCreature(FMassEntityHandle EntityHandle)
{
    if (!MassEntitySubsystem || !EntityHandle.IsValid())
    {
        return false;
    }
    
    FScopeLock Lock(&CreatureManagementMutex);
    
    // Remover da lista de criaturas ativas
    ActiveCreatures.Remove(EntityHandle);
    
    // Remover de packs se necessário
    if (FMassCreatureFragment* CreatureFragment = MassEntitySubsystem->GetMutableEntityManager().GetFragmentDataPtr<FMassCreatureFragment>(EntityHandle))
    {
        if (CreatureFragment->PackID >= 0)
        {
            if (TArray<FMassEntityHandle>* Pack = CreaturePacks.Find(CreatureFragment->PackID))
            {
                Pack->Remove(EntityHandle);
                
                // Se pack ficou vazio, remover
                if (Pack->Num() == 0)
                {
                    CreaturePacks.Remove(CreatureFragment->PackID);
                }
            }
        }
    }
    
    // Destruir entidade
    MassEntitySubsystem->GetMutableEntityManager().DestroyEntity(EntityHandle);
    
    return true;
}

void UAuracronAdaptiveCreaturesBridge::RemoveAllCreatures()
{
    FScopeLock Lock(&CreatureManagementMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo todas as %d criaturas"), ActiveCreatures.Num());
    
    if (MassEntitySubsystem)
    {
        for (const FMassEntityHandle& EntityHandle : ActiveCreatures)
        {
            if (EntityHandle.IsValid())
            {
                MassEntitySubsystem->GetMutableEntityManager().DestroyEntity(EntityHandle);
            }
        }
    }
    
    ActiveCreatures.Empty();
    CreaturePacks.Empty();
    NextPackID = 0;
}

// ========================================
// Python Integration Implementation
// ========================================

bool UAuracronAdaptiveCreaturesBridge::InitializePythonBindings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Python bindings for Adaptive Creatures Bridge"));

#ifdef WITH_PYTHON
    try
    {
        // Initialize Python interpreter if not already done
        if (!Py_IsInitialized())
        {
            Py_Initialize();
        }

        // Create Python module for Adaptive Creatures
        PyObject* pModule = PyModule_New("auracron_adaptive_creatures");
        if (!pModule)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create Python module"));
            return false;
        }

        // Add module functions
        PyObject* pDict = PyModule_GetDict(pModule);

        // Bind creature functions
        PyDict_SetItemString(pDict, "spawn_creature",
            PyCFunction_New(&SpawnCreaturePython, nullptr));
        PyDict_SetItemString(pDict, "create_creature_pack",
            PyCFunction_New(&CreateCreaturePackPython, nullptr));
        PyDict_SetItemString(pDict, "update_creature_behavior",
            PyCFunction_New(&UpdateCreatureBehaviorPython, nullptr));
        PyDict_SetItemString(pDict, "get_creature_stats",
            PyCFunction_New(&GetCreatureStatsPython, nullptr));
        PyDict_SetItemString(pDict, "set_adaptation_parameters",
            PyCFunction_New(&SetAdaptationParametersPython, nullptr));
        PyDict_SetItemString(pDict, "get_system_metrics",
            PyCFunction_New(&GetSystemMetricsPython, nullptr));

        // Register module in Python
        PyObject* pSysModules = PyImport_GetModuleDict();
        PyDict_SetItemString(pSysModules, "auracron_adaptive_creatures", pModule);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python bindings initialized successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception initializing Python bindings: %s"),
               UTF8_TO_TCHAR(e.what()));
        return false;
    }
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Python support not compiled in"));
    return false;
#endif
}

bool UAuracronAdaptiveCreaturesBridge::ExecutePythonScript(const FString& ScriptPath)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executing Python script: %s"), *ScriptPath);

#ifdef WITH_PYTHON
    try
    {
        // Check if Python is initialized
        if (!Py_IsInitialized())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Python not initialized"));
            return false;
        }

        // Read script file
        FString ScriptContent;
        if (!FFileHelper::LoadFileToString(ScriptContent, *ScriptPath))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to read script file: %s"), *ScriptPath);
            return false;
        }

        // Execute Python script
        int Result = PyRun_SimpleString(TCHAR_TO_UTF8(*ScriptContent));
        if (Result != 0)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Python script execution failed"));
            return false;
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python script executed successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception executing Python script: %s"),
               UTF8_TO_TCHAR(e.what()));
        return false;
    }
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Python support not compiled in"));
    return false;
#endif
}

FString UAuracronAdaptiveCreaturesBridge::GetCreatureDataForPython() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Getting creature data for Python"));

    // Create JSON object with creature data
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

    // Basic system info
    JsonObject->SetNumberField(TEXT("max_simultaneous_creatures"), MaxSimultaneousCreatures);
    JsonObject->SetBoolField(TEXT("use_multithreading"), bUseMultiThreading);
    JsonObject->SetNumberField(TEXT("adaptation_update_interval"), AdaptationUpdateInterval);
    JsonObject->SetNumberField(TEXT("player_detection_radius"), PlayerDetectionRadius);
    JsonObject->SetNumberField(TEXT("generation_seed"), GenerationSeed);

    // Active creatures info
    JsonObject->SetNumberField(TEXT("active_creatures_count"), ActiveCreatures.Num());
    JsonObject->SetNumberField(TEXT("creature_packs_count"), CreaturePacks.Num());
    JsonObject->SetNumberField(TEXT("next_pack_id"), NextPackID);

    // Creature types
    TArray<TSharedPtr<FJsonValue>> CreatureTypesArray;
    for (const auto& CreatureType : CreatureTypes)
    {
        TSharedPtr<FJsonObject> CreatureTypeJson = MakeShareable(new FJsonObject);
        CreatureTypeJson->SetStringField(TEXT("name"), CreatureType.Key);
        CreatureTypeJson->SetNumberField(TEXT("base_health"), CreatureType.Value.BaseHealth);
        CreatureTypeJson->SetNumberField(TEXT("base_speed"), CreatureType.Value.BaseSpeed);
        CreatureTypeJson->SetNumberField(TEXT("base_damage"), CreatureType.Value.BaseDamage);
        CreatureTypeJson->SetNumberField(TEXT("adaptation_rate"), CreatureType.Value.AdaptationRate);
        CreatureTypeJson->SetBoolField(TEXT("can_fly"), CreatureType.Value.bCanFly);
        CreatureTypeJson->SetBoolField(TEXT("is_aquatic"), CreatureType.Value.bIsAquatic);
        CreatureTypeJson->SetBoolField(TEXT("is_nocturnal"), CreatureType.Value.bIsNocturnal);

        CreatureTypesArray.Add(MakeShareable(new FJsonValueObject(CreatureTypeJson)));
    }
    JsonObject->SetArrayField(TEXT("creature_types"), CreatureTypesArray);

    // Convert to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}
