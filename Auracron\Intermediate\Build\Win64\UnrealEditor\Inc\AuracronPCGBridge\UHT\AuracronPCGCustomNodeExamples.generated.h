// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGCustomNodeExamples.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGCustomNodeExamples_generated_h
#error "AuracronPCGCustomNodeExamples.generated.h already included, missing '#pragma once' in AuracronPCGCustomNodeExamples.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGCustomNodeExamples_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FAuracronPCGCustomNodeTemplate;

// ********** Begin Class UAuracronPCGExampleCustomPointProcessorSettings **************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_32_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGExampleCustomPointProcessorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGExampleCustomPointProcessorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGExampleCustomPointProcessorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGExampleCustomPointProcessorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_32_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGExampleCustomPointProcessorSettings(UAuracronPCGExampleCustomPointProcessorSettings&&) = delete; \
	UAuracronPCGExampleCustomPointProcessorSettings(const UAuracronPCGExampleCustomPointProcessorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGExampleCustomPointProcessorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGExampleCustomPointProcessorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGExampleCustomPointProcessorSettings) \
	NO_API virtual ~UAuracronPCGExampleCustomPointProcessorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_29_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_32_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_32_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_32_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGExampleCustomPointProcessorSettings;

// ********** End Class UAuracronPCGExampleCustomPointProcessorSettings ****************************

// ********** Begin Class UAuracronPCGExampleCustomGeneratorSettings *******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_96_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGExampleCustomGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGExampleCustomGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGExampleCustomGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGExampleCustomGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_96_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGExampleCustomGeneratorSettings(UAuracronPCGExampleCustomGeneratorSettings&&) = delete; \
	UAuracronPCGExampleCustomGeneratorSettings(const UAuracronPCGExampleCustomGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGExampleCustomGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGExampleCustomGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGExampleCustomGeneratorSettings) \
	NO_API virtual ~UAuracronPCGExampleCustomGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_93_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_96_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_96_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_96_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGExampleCustomGeneratorSettings;

// ********** End Class UAuracronPCGExampleCustomGeneratorSettings *********************************

// ********** Begin Class UAuracronPCGExampleCustomFilterSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_153_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGExampleCustomFilterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGExampleCustomFilterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGExampleCustomFilterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGExampleCustomFilterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_153_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGExampleCustomFilterSettings(UAuracronPCGExampleCustomFilterSettings&&) = delete; \
	UAuracronPCGExampleCustomFilterSettings(const UAuracronPCGExampleCustomFilterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGExampleCustomFilterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGExampleCustomFilterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGExampleCustomFilterSettings) \
	NO_API virtual ~UAuracronPCGExampleCustomFilterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_150_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_153_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_153_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_153_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGExampleCustomFilterSettings;

// ********** End Class UAuracronPCGExampleCustomFilterSettings ************************************

// ********** Begin Class UAuracronPCGCustomNodeExamplesManager ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execTestExampleNodeValidation); \
	DECLARE_FUNCTION(execTestExampleNodeExecution); \
	DECLARE_FUNCTION(execTestExampleNodeCreation); \
	DECLARE_FUNCTION(execLoadExampleTemplatesFromFile); \
	DECLARE_FUNCTION(execSaveExampleTemplatesToFile); \
	DECLARE_FUNCTION(execValidateAllExampleTemplates); \
	DECLARE_FUNCTION(execCreateExampleFilterTemplate); \
	DECLARE_FUNCTION(execCreateExampleGeneratorTemplate); \
	DECLARE_FUNCTION(execCreateExamplePointProcessorTemplate); \
	DECLARE_FUNCTION(execGetAllExampleTemplates); \
	DECLARE_FUNCTION(execRegisterExampleFilter); \
	DECLARE_FUNCTION(execRegisterExampleGenerator); \
	DECLARE_FUNCTION(execRegisterExamplePointProcessor); \
	DECLARE_FUNCTION(execUnregisterAllExampleNodes); \
	DECLARE_FUNCTION(execRegisterAllExampleNodes);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeExamplesManager(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeExamplesManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeExamplesManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeExamplesManager)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCustomNodeExamplesManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeExamplesManager(UAuracronPCGCustomNodeExamplesManager&&) = delete; \
	UAuracronPCGCustomNodeExamplesManager(const UAuracronPCGCustomNodeExamplesManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeExamplesManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeExamplesManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeExamplesManager) \
	NO_API virtual ~UAuracronPCGCustomNodeExamplesManager();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_215_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_218_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeExamplesManager;

// ********** End Class UAuracronPCGCustomNodeExamplesManager **************************************

// ********** Begin Class UAuracronPCGCustomNodeTutorialHelper *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetTutorialExampleDescription); \
	DECLARE_FUNCTION(execGetTutorialExample); \
	DECLARE_FUNCTION(execGetTutorialExamples); \
	DECLARE_FUNCTION(execGetTotalTutorialSteps); \
	DECLARE_FUNCTION(execGetTutorialStepInstructions); \
	DECLARE_FUNCTION(execValidateTutorialStep); \
	DECLARE_FUNCTION(execFinalizeCustomNode); \
	DECLARE_FUNCTION(execConfigureNodeExecution); \
	DECLARE_FUNCTION(execAddPinsToNode); \
	DECLARE_FUNCTION(execAddParametersToNode); \
	DECLARE_FUNCTION(execCreateBasicCustomNode);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCustomNodeTutorialHelper(); \
	friend struct Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCustomNodeTutorialHelper, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCustomNodeTutorialHelper_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCustomNodeTutorialHelper)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCustomNodeTutorialHelper(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCustomNodeTutorialHelper(UAuracronPCGCustomNodeTutorialHelper&&) = delete; \
	UAuracronPCGCustomNodeTutorialHelper(const UAuracronPCGCustomNodeTutorialHelper&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCustomNodeTutorialHelper); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCustomNodeTutorialHelper); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCustomNodeTutorialHelper) \
	NO_API virtual ~UAuracronPCGCustomNodeTutorialHelper();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_282_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h_285_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCustomNodeTutorialHelper;

// ********** End Class UAuracronPCGCustomNodeTutorialHelper ***************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCustomNodeExamples_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
