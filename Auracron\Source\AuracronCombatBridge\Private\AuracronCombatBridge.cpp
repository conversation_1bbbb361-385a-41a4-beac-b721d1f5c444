// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge Implementation

#include "AuracronCombatBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "GameplayAbilities/Public/GameplayEffectSpec.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"
#include "DrawDebugHelpers.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/BodyInstance.h"
#include "FieldSystem/FieldSystemComponent.h"
#include "FieldSystem/FieldSystemActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronCombatBridge::UAuracronCombatBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.05f; // 20 FPS para combate responsivo
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão
    DefaultTargetingConfig.TargetingType = EAuracronTargetingType::SingleTarget;
    DefaultTargetingConfig.MaxRange = 800.0f;
    DefaultTargetingConfig.bRequiresLineOfSight = true;
    DefaultTargetingConfig.bTargetEnemiesOnly = true;
    DefaultTargetingConfig.AllowedTargetingLayers.Add(EAuracronCombatLayer::Surface);
    
    DefaultDamageConfig.DamageType = EAuracronDamageType::Physical;
    DefaultDamageConfig.BaseDamage = 100.0f;
    DefaultDamageConfig.AttackDamageScaling = 1.0f;
    DefaultDamageConfig.bCanCrit = true;
    DefaultDamageConfig.AffectedLayers.Add(EAuracronCombatLayer::Surface);
    
    DefaultEffectsConfig.EffectDuration = 2.0f;
    DefaultEffectsConfig.EffectScale = 1.0f;
    DefaultEffectsConfig.EffectColor = FLinearColor::White;
}

void UAuracronCombatBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Combate 3D"));

    // Obter referências aos componentes
    if (AActor* Owner = GetOwner())
    {
        AbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Owner);
        if (!AbilitySystemComponent)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: AbilitySystemComponent não encontrado no Owner"));
        }

        FieldSystemComponent = Owner->FindComponentByClass<UFieldSystemComponent>();
        if (!FieldSystemComponent)
        {
            // Criar FieldSystemComponent se não existir
            FieldSystemComponent = NewObject<UFieldSystemComponent>(Owner);
            if (FieldSystemComponent)
            {
                Owner->AddInstanceComponent(FieldSystemComponent);
                FieldSystemComponent->RegisterComponent();
                UE_LOG(LogTemp, Log, TEXT("AURACRON: FieldSystemComponent criado"));
            }
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeCombatSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Combate 3D inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Combate 3D"));
    }
}

void UAuracronCombatBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar efeitos ativos
    for (UNiagaraComponent* Component : ActiveCombatEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveCombatEffects.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAuracronCombatBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronCombatBridge, CurrentCombatLayer);
}

void UAuracronCombatBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar efeitos ativos
    for (int32 i = ActiveCombatEffects.Num() - 1; i >= 0; i--)
    {
        if (!IsValid(ActiveCombatEffects[i]) || !ActiveCombatEffects[i]->IsActive())
        {
            ActiveCombatEffects.RemoveAt(i);
        }
    }
}

// === Core Combat Management ===

FAuracronTargetingResult UAuracronCombatBridge::ExecuteTargeting(const FAuracronTargetingConfiguration& TargetingConfig, const FVector& SourceLocation, const FVector& TargetDirection)
{
    FAuracronTargetingResult Result;
    
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de combate não inicializado"));
        return Result;
    }

    if (!ValidateTargetingConfiguration(TargetingConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de targeting inválida"));
        return Result;
    }

    TArray<AActor*> PotentialTargets;

    switch (TargetingConfig.TargetingType)
    {
        case EAuracronTargetingType::SingleTarget:
        {
            FHitResult HitResult;
            FVector EndLocation = SourceLocation + (TargetDirection * TargetingConfig.MaxRange);
            
            if (ExecuteLineTrace(SourceLocation, EndLocation, HitResult, TargetingConfig.TargetingChannels))
            {
                if (AActor* HitActor = HitResult.GetActor())
                {
                    if (IsValidTarget(HitActor, TargetingConfig, GetOwner()))
                    {
                        Result.TargetActors.Add(HitActor);
                        Result.TargetLocations.Add(HitResult.Location);
                        Result.HitResults.Add(HitResult);
                        Result.bSuccessful = true;
                        Result.DistanceToTarget = FVector::Dist(SourceLocation, HitResult.Location);
                        Result.bHasLineOfSight = CheckLineOfSight(SourceLocation, HitResult.Location, TargetingConfig.LineOfSightChannels);
                    }
                }
            }
            break;
        }
        
        case EAuracronTargetingType::AreaOfEffect:
        {
            FVector TargetLocation = SourceLocation + (TargetDirection * TargetingConfig.MaxRange);
            PotentialTargets = GetTargetsInRadius(TargetLocation, TargetingConfig.AreaOfEffectRadius, TargetingConfig);
            break;
        }
        
        case EAuracronTargetingType::Cone:
        {
            PotentialTargets = GetTargetsInCone(SourceLocation, TargetDirection, TargetingConfig.MaxRange, TargetingConfig.ConeAngle, TargetingConfig);
            break;
        }
        
        case EAuracronTargetingType::VerticalColumn:
        {
            FVector TargetLocation = SourceLocation + (TargetDirection * TargetingConfig.MaxRange);
            PotentialTargets = GetTargetsInVerticalColumn(TargetLocation, TargetingConfig.AreaOfEffectRadius, TargetingConfig.AreaOfEffectHeight, TargetingConfig);
            break;
        }
        
        default:
            break;
    }

    // Filtrar alvos
    if (PotentialTargets.Num() > 0)
    {
        TArray<AActor*> ValidTargets = FilterTargetsByConfiguration(PotentialTargets, TargetingConfig, GetOwner());
        
        for (AActor* Target : ValidTargets)
        {
            Result.TargetActors.Add(Target);
            Result.TargetLocations.Add(Target->GetActorLocation());
            
            FHitResult DummyHit;
            DummyHit.Location = Target->GetActorLocation();
            DummyHit.Actor = Target;
            Result.HitResults.Add(DummyHit);
        }
        
        Result.bSuccessful = ValidTargets.Num() > 0;
        
        if (Result.bSuccessful && ValidTargets.Num() > 0)
        {
            Result.DistanceToTarget = FVector::Dist(SourceLocation, ValidTargets[0]->GetActorLocation());
            Result.bHasLineOfSight = CheckLineOfSight(SourceLocation, ValidTargets[0]->GetActorLocation(), TargetingConfig.LineOfSightChannels);
        }
    }

    // Determinar camada de targeting
    Result.TargetLayer = GetActorCombatLayer(GetOwner());

    // Armazenar resultado
    LastTargetingResult = Result;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Targeting executado - %d alvos encontrados"), Result.TargetActors.Num());

    // Broadcast evento
    OnTargetingExecuted.Broadcast(Result, TargetingConfig);

    return Result;
}

bool UAuracronCombatBridge::ApplyDamageToTarget(AActor* TargetActor, const FAuracronDamageConfiguration& DamageConfig, AActor* SourceActor)
{
    if (!bSystemInitialized || !TargetActor)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado ou alvo inválido"));
        return false;
    }

    if (!ValidateDamageConfiguration(DamageConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de dano inválida"));
        return false;
    }

    // Calcular dano final
    float FinalDamage = CalculateFinalDamage(DamageConfig, SourceActor, TargetActor);

    // Obter AbilitySystemComponent do alvo
    UAbilitySystemComponent* TargetASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(TargetActor);
    if (TargetASC)
    {
        // Criar GameplayEffect para aplicar dano
        // Por simplicidade, aplicando dano direto aqui
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Aplicando %.2f de dano a %s"), FinalDamage, *TargetActor->GetName());

        // Broadcast evento
        OnDamageApplied.Broadcast(TargetActor, FinalDamage, DamageConfig.DamageType);

        return true;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Alvo não possui AbilitySystemComponent"));
    return false;
}

bool UAuracronCombatBridge::ApplyAreaDamage(const FVector& Location, const FAuracronDamageConfiguration& DamageConfig, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema não inicializado"));
        return false;
    }

    // Obter alvos na área
    TArray<AActor*> TargetsInArea = GetTargetsInRadius(Location, TargetingConfig.AreaOfEffectRadius, TargetingConfig);

    bool bAnyDamageApplied = false;
    for (AActor* Target : TargetsInArea)
    {
        if (ApplyDamageToTarget(Target, DamageConfig, SourceActor))
        {
            bAnyDamageApplied = true;
        }
    }

    // Spawnar efeitos visuais
    SpawnCombatEffects(Location, DefaultEffectsConfig);

    // Aplicar Field System se configurado
    if (DamageConfig.DamageType == EAuracronDamageType::Physical && DefaultEffectsConfig.bUseFieldSystemDestruction)
    {
        ApplyFieldSystemDestruction(Location, DefaultEffectsConfig.FieldSystemForce, DefaultEffectsConfig.FieldSystemRadius);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dano em área aplicado em %d alvos"), TargetsInArea.Num());

    return bAnyDamageApplied;
}

bool UAuracronCombatBridge::CheckLineOfSight(const FVector& StartLocation, const FVector& EndLocation, const TArray<TEnumAsByte<ECollisionChannel>>& CollisionChannels)
{
    if (!GetWorld())
    {
        return false;
    }

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;
    QueryParams.AddIgnoredActor(GetOwner());

    // Usar o primeiro canal de colisão disponível
    ECollisionChannel Channel = CollisionChannels.Num() > 0 ? CollisionChannels[0] : ECC_Visibility;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        Channel,
        QueryParams
    );

    // Se não houve hit, há line of sight
    // Se houve hit mas foi no alvo desejado, também há line of sight
    return !bHit || (HitResult.Location - EndLocation).SizeSquared() < 100.0f; // Tolerância de 10 unidades
}

TArray<AActor*> UAuracronCombatBridge::GetTargetsInRadius(const FVector& Location, float Radius, const FAuracronTargetingConfiguration& TargetingConfig)
{
    TArray<AActor*> FoundTargets;

    if (!GetWorld())
    {
        return FoundTargets;
    }

    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.AddIgnoredActor(GetOwner());

    // Usar o primeiro canal de targeting disponível
    ECollisionChannel Channel = TargetingConfig.TargetingChannels.Num() > 0 ?
                                TargetingConfig.TargetingChannels[0] : ECC_Pawn;

    bool bFoundOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        Location,
        FQuat::Identity,
        Channel,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bFoundOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* OverlapActor = Overlap.GetActor())
            {
                if (IsValidTarget(OverlapActor, TargetingConfig, GetOwner()))
                {
                    FoundTargets.Add(OverlapActor);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Encontrados %d alvos em raio de %.2f"), FoundTargets.Num(), Radius);

    return FoundTargets;
}

TArray<AActor*> UAuracronCombatBridge::GetTargetsInCone(const FVector& SourceLocation, const FVector& Direction, float Range, float ConeAngle, const FAuracronTargetingConfiguration& TargetingConfig)
{
    TArray<AActor*> FoundTargets;

    // Primeiro obter todos os alvos em raio
    TArray<AActor*> RadiusTargets = GetTargetsInRadius(SourceLocation, Range, TargetingConfig);

    // Filtrar por ângulo do cone
    FVector NormalizedDirection = Direction.GetSafeNormal();
    float ConeAngleRadians = FMath::DegreesToRadians(ConeAngle * 0.5f); // Metade do ângulo total

    for (AActor* Target : RadiusTargets)
    {
        FVector ToTarget = (Target->GetActorLocation() - SourceLocation).GetSafeNormal();
        float DotProduct = FVector::DotProduct(NormalizedDirection, ToTarget);
        float AngleToTarget = FMath::Acos(DotProduct);

        if (AngleToTarget <= ConeAngleRadians)
        {
            FoundTargets.Add(Target);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Encontrados %d alvos em cone"), FoundTargets.Num());

    return FoundTargets;
}

TArray<AActor*> UAuracronCombatBridge::GetTargetsInVerticalColumn(const FVector& Location, float Radius, float Height, const FAuracronTargetingConfiguration& TargetingConfig)
{
    TArray<AActor*> FoundTargets;

    if (!GetWorld())
    {
        return FoundTargets;
    }

    // Executar overlap em formato de cilindro
    FVector StartLocation = Location - FVector(0, 0, Height * 0.5f);
    FVector EndLocation = Location + FVector(0, 0, Height * 0.5f);

    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.AddIgnoredActor(GetOwner());

    ECollisionChannel Channel = TargetingConfig.TargetingChannels.Num() > 0 ?
                                TargetingConfig.TargetingChannels[0] : ECC_Pawn;

    // Usar capsule para simular cilindro
    bool bFoundOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        Location,
        FQuat::Identity,
        Channel,
        FCollisionShape::MakeCapsule(Radius, Height * 0.5f),
        QueryParams
    );

    if (bFoundOverlaps)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* OverlapActor = Overlap.GetActor())
            {
                // Verificar se está dentro da altura especificada
                float ActorZ = OverlapActor->GetActorLocation().Z;
                float MinZ = StartLocation.Z;
                float MaxZ = EndLocation.Z;

                if (ActorZ >= MinZ && ActorZ <= MaxZ)
                {
                    if (IsValidTarget(OverlapActor, TargetingConfig, GetOwner()))
                    {
                        FoundTargets.Add(OverlapActor);
                    }
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Encontrados %d alvos em coluna vertical"), FoundTargets.Num());

    return FoundTargets;
}
