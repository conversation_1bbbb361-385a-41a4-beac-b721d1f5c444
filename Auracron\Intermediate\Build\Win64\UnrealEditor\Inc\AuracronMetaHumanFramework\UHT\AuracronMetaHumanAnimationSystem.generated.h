// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanAnimationSystem.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanAnimationSystem_generated_h
#error "AuracronMetaHumanAnimationSystem.generated.h already included, missing '#pragma once' in AuracronMetaHumanAnimationSystem.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanAnimationSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAnimationAsset;
class UAnimSequence;
class UAuracronMetaHumanFramework;
class USkeletalMesh;
class USkeletalMeshComponent;
class USkeleton;
struct FAuracronAnimationParams;
struct FAuracronAnimationResult;
struct FAuracronFacialAnimConfig;

// ********** Begin ScriptStruct FAuracronAnimationParams ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_72_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAnimationParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAnimationParams;
// ********** End ScriptStruct FAuracronAnimationParams ********************************************

// ********** Begin ScriptStruct FAuracronAnimationResult ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_122_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAnimationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAnimationResult;
// ********** End ScriptStruct FAuracronAnimationResult ********************************************

// ********** Begin ScriptStruct FAuracronFacialAnimConfig *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_166_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFacialAnimConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFacialAnimConfig;
// ********** End ScriptStruct FAuracronFacialAnimConfig *******************************************

// ********** Begin Delegate FAuracronAnimationComplete ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_198_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronAnimationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronAnimationComplete, FAuracronAnimationResult const& Result, const FString& OperationID);


// ********** End Delegate FAuracronAnimationComplete **********************************************

// ********** Begin Class UAuracronMetaHumanAnimationSystem ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSupportedAnimationTypes); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execTriggerFacialExpression); \
	DECLARE_FUNCTION(execUpdateEyeLookTarget); \
	DECLARE_FUNCTION(execApplyRealtimeFacialAnimation); \
	DECLARE_FUNCTION(execValidateMetaHumanAnimation); \
	DECLARE_FUNCTION(execGetAnimationStatistics); \
	DECLARE_FUNCTION(execAnalyzeAnimationQuality); \
	DECLARE_FUNCTION(execBlendAnimations); \
	DECLARE_FUNCTION(execCreateAnimationMontage); \
	DECLARE_FUNCTION(execCreateBlendSpace); \
	DECLARE_FUNCTION(execGetAsyncOperationResult); \
	DECLARE_FUNCTION(execIsAsyncOperationComplete); \
	DECLARE_FUNCTION(execGenerateFacialAnimationAsync); \
	DECLARE_FUNCTION(execGenerateAnimationAsync); \
	DECLARE_FUNCTION(execRetargetAnimation); \
	DECLARE_FUNCTION(execOptimizeAnimation); \
	DECLARE_FUNCTION(execGenerateFacialAnimation); \
	DECLARE_FUNCTION(execGenerateAnimation);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanAnimationSystem(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanAnimationSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanAnimationSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanAnimationSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanAnimationSystem(UAuracronMetaHumanAnimationSystem&&) = delete; \
	UAuracronMetaHumanAnimationSystem(const UAuracronMetaHumanAnimationSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanAnimationSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanAnimationSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanAnimationSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_204_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h_207_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanAnimationSystem;

// ********** End Class UAuracronMetaHumanAnimationSystem ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanAnimationSystem_h

// ********** Begin Enum EAuracronAnimationType ****************************************************
#define FOREACH_ENUM_EAURACRONANIMATIONTYPE(op) \
	op(EAuracronAnimationType::Facial) \
	op(EAuracronAnimationType::Body) \
	op(EAuracronAnimationType::Hand) \
	op(EAuracronAnimationType::Eye) \
	op(EAuracronAnimationType::Lip) \
	op(EAuracronAnimationType::Emotion) \
	op(EAuracronAnimationType::Gesture) \
	op(EAuracronAnimationType::Locomotion) \
	op(EAuracronAnimationType::Idle) \
	op(EAuracronAnimationType::Transition) 

enum class EAuracronAnimationType : uint8;
template<> struct TIsUEnumClass<EAuracronAnimationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronAnimationType>();
// ********** End Enum EAuracronAnimationType ******************************************************

// ********** Begin Enum EAuracronAnimationQuality *************************************************
#define FOREACH_ENUM_EAURACRONANIMATIONQUALITY(op) \
	op(EAuracronAnimationQuality::Draft) \
	op(EAuracronAnimationQuality::Preview) \
	op(EAuracronAnimationQuality::Production) \
	op(EAuracronAnimationQuality::Cinematic) \
	op(EAuracronAnimationQuality::Custom) 

enum class EAuracronAnimationQuality : uint8;
template<> struct TIsUEnumClass<EAuracronAnimationQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronAnimationQuality>();
// ********** End Enum EAuracronAnimationQuality ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
