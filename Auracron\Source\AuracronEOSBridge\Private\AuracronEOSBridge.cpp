// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Epic Online Services Bridge Implementation

#include "AuracronEOSBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemEOS.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineFriendsInterface.h"
#include "Interfaces/OnlinePresenceInterface.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronEOSBridge::UAuracronEOSBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.5f; // 2 FPS para EOS
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Estado inicial
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    bIsInSession = false;
}

void UAuracronEOSBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Epic Online Services"));

    // Inicializar EOS
    bSystemInitialized = InitializeEOS();
    
    if (bSystemInitialized)
    {
        // Configurar timer para callbacks
        GetWorld()->GetTimerManager().SetTimer(
            CallbackTimer,
            [this]()
            {
                ProcessEOSCallbacks(0.5f);
            },
            0.5f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Epic Online Services inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Epic Online Services"));
    }
}

void UAuracronEOSBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Sair da sessão se estiver em uma
    if (bIsInSession)
    {
        LeaveSession();
    }
    
    // Fazer logout
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        LogoutFromEOS();
    }
    
    // Limpar dados
    FriendsList.Empty();
    PlayerAchievements.Empty();

    // Limpar timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(CallbackTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronEOSBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronEOSBridge, CurrentConnectionStatus);
    DOREPLIFETIME(UAuracronEOSBridge, CurrentUserID);
    DOREPLIFETIME(UAuracronEOSBridge, CurrentDisplayName);
    DOREPLIFETIME(UAuracronEOSBridge, FriendsList);
    DOREPLIFETIME(UAuracronEOSBridge, PlayerAchievements);
    DOREPLIFETIME(UAuracronEOSBridge, CurrentSession);
    DOREPLIFETIME(UAuracronEOSBridge, bIsInSession);
}

void UAuracronEOSBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar callbacks EOS
    ProcessEOSCallbacks(DeltaTime);
}

// === Authentication ===

bool UAuracronEOSBridge::LoginWithEOS(const FString& LoginType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema EOS não inicializado"));
        return false;
    }

    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Já autenticado com EOS"));
        return true;
    }

    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Connecting;

    // Obter interface de identidade
    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem EOS não encontrado"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return false;
    }

    IOnlineIdentityPtr IdentityInterface = OnlineSubsystem->GetIdentityInterface();
    if (!IdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Interface de identidade EOS não encontrada"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return false;
    }

    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Authenticating;

    // Configurar credenciais de login
    FOnlineAccountCredentials Credentials;
    Credentials.Type = LoginType;
    Credentials.Id = TEXT("");
    Credentials.Token = TEXT("");

    // Fazer login
    IdentityInterface->OnLoginCompleteDelegates->AddUObject(this, &UAuracronEOSBridge::OnLoginComplete);
    
    if (IdentityInterface->Login(0, Credentials))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando login com EOS"));
        return true;
    }
    else
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao iniciar login com EOS"));
        return false;
    }
}

bool UAuracronEOSBridge::LogoutFromEOS()
{
    if (CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return true;
    }

    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (OnlineSubsystem)
    {
        IOnlineIdentityPtr IdentityInterface = OnlineSubsystem->GetIdentityInterface();
        if (IdentityInterface.IsValid())
        {
            IdentityInterface->Logout(0);
        }
    }

    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    CurrentUserID.Empty();
    CurrentDisplayName.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Logout do EOS realizado"));

    return true;
}

EAuracronEOSConnectionStatus UAuracronEOSBridge::GetAuthenticationStatus() const
{
    return CurrentConnectionStatus;
}

FString UAuracronEOSBridge::GetUserID() const
{
    return CurrentUserID;
}

FString UAuracronEOSBridge::GetDisplayName() const
{
    return CurrentDisplayName;
}

// === Sessions ===

bool UAuracronEOSBridge::CreateSession(const FAuracronSessionConfiguration& SessionConfig)
{
    if (!bSystemInitialized || CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Não autenticado com EOS"));
        return false;
    }

    if (!ValidateSessionConfiguration(SessionConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de sessão inválida"));
        return false;
    }

    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        return false;
    }

    IOnlineSessionPtr SessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!SessionInterface.IsValid())
    {
        return false;
    }

    // Configurar settings da sessão
    FOnlineSessionSettings SessionSettings;
    SessionSettings.NumPublicConnections = SessionConfig.MaxPlayers;
    SessionSettings.NumPrivateConnections = 0;
    SessionSettings.bIsLANMatch = false;
    SessionSettings.bShouldAdvertise = !SessionConfig.bIsPrivate;
    SessionSettings.bAllowJoinInProgress = true;
    SessionSettings.bAllowInvites = true;
    SessionSettings.bUsesPresence = true;
    SessionSettings.bUseLobbiesIfAvailable = true;
    SessionSettings.bUseLobbiesVoiceChatIfAvailable = true;

    // Adicionar configurações customizadas
    SessionSettings.Set(SETTING_GAMEMODE, SessionConfig.GameMode, EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings.Set(SETTING_MAPNAME, SessionConfig.MapName, EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings.Set(TEXT("SessionType"), UEnum::GetValueAsString(SessionConfig.SessionType), EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings.Set(TEXT("Region"), SessionConfig.PreferredRegion, EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings.Set(TEXT("AntiCheat"), SessionConfig.bUseAntiCheat, EOnlineDataAdvertisementType::ViaOnlineService);

    for (const auto& CustomSetting : SessionConfig.CustomSettings)
    {
        SessionSettings.Set(FName(*CustomSetting.Key), CustomSetting.Value, EOnlineDataAdvertisementType::ViaOnlineService);
    }

    // Criar sessão
    SessionInterface->OnCreateSessionCompleteDelegates.AddUObject(this, &UAuracronEOSBridge::OnCreateSessionComplete);
    
    if (SessionInterface->CreateSession(0, FName(*SessionConfig.SessionName), SessionSettings))
    {
        CurrentSession = SessionConfig;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando sessão EOS: %s"), *SessionConfig.SessionName);
        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar sessão EOS"));
    return false;
}

// === Internal Callback Methods ===

void UAuracronEOSBridge::OnLoginComplete(int32 LocalUserNum, bool bWasSuccessful, const FUniqueNetId& UserId, const FString& Error)
{
    if (bWasSuccessful)
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Authenticated;
        CurrentUserID = UserId.ToString();
        
        // Obter nome de exibição
        IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
        if (OnlineSubsystem)
        {
            IOnlineIdentityPtr IdentityInterface = OnlineSubsystem->GetIdentityInterface();
            if (IdentityInterface.IsValid())
            {
                CurrentDisplayName = IdentityInterface->GetPlayerNickname(0);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Login EOS bem-sucedido - Usuário: %s"), *CurrentDisplayName);

        // Carregar dados do jogador
        LoadFriendsList();
    }
    else
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha no login EOS: %s"), *Error);
    }

    // Broadcast evento
    OnEOSLoginCompleted.Broadcast(bWasSuccessful, Error);
}

void UAuracronEOSBridge::OnCreateSessionComplete(FName SessionName, bool bWasSuccessful)
{
    if (bWasSuccessful)
    {
        bIsInSession = true;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sessão EOS criada com sucesso: %s"), *SessionName.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar sessão EOS: %s"), *SessionName.ToString());
    }

    // Broadcast evento
    OnSessionCreated.Broadcast(bWasSuccessful, SessionName.ToString());
}

void UAuracronEOSBridge::OnRep_ConnectionStatus()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Status de conexão EOS atualizado: %s"), *UEnum::GetValueAsString(CurrentConnectionStatus));
}

// === Friends ===

bool UAuracronEOSBridge::LoadFriendsList()
{
    if (!bSystemInitialized || CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }

    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        return false;
    }

    IOnlineFriendsPtr FriendsInterface = OnlineSubsystem->GetFriendsInterface();
    if (!FriendsInterface.IsValid())
    {
        return false;
    }

    // Carregar lista de amigos
    FriendsInterface->ReadFriendsList(0, EFriendsLists::ToString(EFriendsLists::Default),
        FOnReadFriendsListComplete::CreateUObject(this, &UAuracronEOSBridge::OnReadFriendsListComplete));

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Carregando lista de amigos EOS"));

    return true;
}

TArray<FAuracronEOSFriend> UAuracronEOSBridge::GetFriendsList() const
{
    return FriendsList;
}

bool UAuracronEOSBridge::AddFriend(const FString& UserID)
{
    if (!bSystemInitialized || UserID.IsEmpty() || CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }

    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        return false;
    }

    IOnlineFriendsPtr FriendsInterface = OnlineSubsystem->GetFriendsInterface();
    if (!FriendsInterface.IsValid())
    {
        return false;
    }

    // Criar FUniqueNetId a partir do UserID
    TSharedPtr<const FUniqueNetId> FriendId = OnlineSubsystem->GetIdentityInterface()->CreateUniquePlayerId(UserID);
    if (!FriendId.IsValid())
    {
        return false;
    }

    // Enviar solicitação de amizade
    FriendsInterface->SendInvite(0, *FriendId, EFriendsLists::ToString(EFriendsLists::Default),
        FOnSendInviteComplete::CreateUObject(this, &UAuracronEOSBridge::OnSendInviteComplete));

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Solicitação de amizade enviada para: %s"), *UserID);

    return true;
}

// === Achievements ===

bool UAuracronEOSBridge::UnlockAchievement(const FString& AchievementID)
{
    if (!bSystemInitialized || AchievementID.IsEmpty() || CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }

    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        return false;
    }

    IOnlineAchievementsPtr AchievementsInterface = OnlineSubsystem->GetAchievementsInterface();
    if (!AchievementsInterface.IsValid())
    {
        return false;
    }

    // Desbloquear achievement
    FOnlineAchievementsWriteRef WriteObject = MakeShareable(new FOnlineAchievementsWrite());
    WriteObject->SetFloatStat(*AchievementID, 100.0f); // 100% de progresso

    AchievementsInterface->WriteAchievements(0, *WriteObject,
        FOnAchievementsWrittenDelegate::CreateUObject(this, &UAuracronEOSBridge::OnAchievementWritten));

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Achievement desbloqueado: %s"), *AchievementID);

    return true;
}

bool UAuracronEOSBridge::UpdateAchievementProgress(const FString& AchievementID, float Progress)
{
    if (!bSystemInitialized || AchievementID.IsEmpty() || CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }

    Progress = FMath::Clamp(Progress, 0.0f, 1.0f);

    // Atualizar progresso local
    for (FAuracronEOSAchievement& Achievement : PlayerAchievements)
    {
        if (Achievement.AchievementID == AchievementID)
        {
            Achievement.Progress = Progress;

            if (Progress >= 1.0f && !Achievement.bIsUnlocked)
            {
                Achievement.bIsUnlocked = true;
                Achievement.UnlockDate = FDateTime::Now();

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Achievement completado: %s"), *Achievement.AchievementName.ToString());

                // Broadcast evento
                OnAchievementUnlocked.Broadcast(Achievement);
            }

            return true;
        }
    }

    return false;
}

// === Internal Callback Methods ===

void UAuracronEOSBridge::OnReadFriendsListComplete(int32 LocalUserNum, bool bWasSuccessful, const FString& ListName, const FString& ErrorStr)
{
    if (bWasSuccessful)
    {
        IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
        if (OnlineSubsystem)
        {
            IOnlineFriendsPtr FriendsInterface = OnlineSubsystem->GetFriendsInterface();
            if (FriendsInterface.IsValid())
            {
                TArray<TSharedRef<FOnlineFriend>> Friends;
                FriendsInterface->GetFriendsList(0, ListName, Friends);

                FScopeLock Lock(&EOSMutex);
                FriendsList.Empty();

                for (const TSharedRef<FOnlineFriend>& Friend : Friends)
                {
                    FAuracronEOSFriend AuracronFriend;
                    AuracronFriend.UserID = Friend->GetUserId()->ToString();
                    AuracronFriend.DisplayName = Friend->GetDisplayName();
                    AuracronFriend.bIsOnline = Friend->GetPresence().bIsOnline;
                    AuracronFriend.PresenceStatus = Friend->GetPresence().Status.StatusStr;
                    AuracronFriend.LastOnlineTime = Friend->GetPresence().LastOnline;

                    FriendsList.Add(AuracronFriend);
                }

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Lista de amigos carregada - %d amigos"), FriendsList.Num());
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar lista de amigos: %s"), *ErrorStr);
    }
}

void UAuracronEOSBridge::OnSendInviteComplete(int32 LocalUserNum, bool bWasSuccessful, const FUniqueNetId& FriendId, const FString& ListName, const FString& ErrorStr)
{
    if (bWasSuccessful)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Solicitação de amizade enviada com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao enviar solicitação de amizade: %s"), *ErrorStr);
    }
}

void UAuracronEOSBridge::OnAchievementWritten(const FUniqueNetId& PlayerId, bool bWasSuccessful)
{
    if (bWasSuccessful)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Achievement escrito com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao escrever achievement"));
    }
}

// === Internal Methods ===

bool UAuracronEOSBridge::InitializeEOS()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Verificar se EOS está disponível
    IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get(EOS_SUBSYSTEM);
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem EOS não disponível"));
        return false;
    }

    // Configurar callbacks
    if (!SetupEOSCallbacks())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar callbacks EOS"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: EOS inicializado"));

    return true;
}

bool UAuracronEOSBridge::SetupEOSCallbacks()
{
    // Configurar callbacks necessários
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Callbacks EOS configurados"));

    return true;
}

void UAuracronEOSBridge::ProcessEOSCallbacks(float DeltaTime)
{
    // Processar callbacks e atualizações EOS
    // Em produção, processar eventos de presença, amigos, etc.
}

bool UAuracronEOSBridge::ValidateSessionConfiguration(const FAuracronSessionConfiguration& Config) const
{
    if (Config.SessionName.IsEmpty() || Config.MaxPlayers <= 0)
    {
        return false;
    }

    if (Config.MapName.IsEmpty() || Config.GameMode.IsEmpty())
    {
        return false;
    }

    return true;
}
