// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionLOD.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionLOD() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronLODGenerationState ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLODGenerationState;
static UEnum* EAuracronLODGenerationState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODGenerationState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLODGenerationState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLODGenerationState"));
	}
	return Z_Registration_Info_UEnum_EAuracronLODGenerationState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODGenerationState>()
{
	return EAuracronLODGenerationState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD generation states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronLODGenerationState::Failed" },
		{ "Generated.DisplayName", "Generated" },
		{ "Generated.Name", "EAuracronLODGenerationState::Generated" },
		{ "Generating.DisplayName", "Generating" },
		{ "Generating.Name", "EAuracronLODGenerationState::Generating" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
		{ "NotGenerated.DisplayName", "Not Generated" },
		{ "NotGenerated.Name", "EAuracronLODGenerationState::NotGenerated" },
		{ "Outdated.DisplayName", "Outdated" },
		{ "Outdated.Name", "EAuracronLODGenerationState::Outdated" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD generation states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLODGenerationState::NotGenerated", (int64)EAuracronLODGenerationState::NotGenerated },
		{ "EAuracronLODGenerationState::Generating", (int64)EAuracronLODGenerationState::Generating },
		{ "EAuracronLODGenerationState::Generated", (int64)EAuracronLODGenerationState::Generated },
		{ "EAuracronLODGenerationState::Failed", (int64)EAuracronLODGenerationState::Failed },
		{ "EAuracronLODGenerationState::Outdated", (int64)EAuracronLODGenerationState::Outdated },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLODGenerationState",
	"EAuracronLODGenerationState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODGenerationState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLODGenerationState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLODGenerationState.InnerSingleton;
}
// ********** End Enum EAuracronLODGenerationState *************************************************

// ********** Begin Enum EAuracronLODType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLODType;
static UEnum* EAuracronLODType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLODType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLODType"));
	}
	return Z_Registration_Info_UEnum_EAuracronLODType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODType>()
{
	return EAuracronLODType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Billboard.DisplayName", "Billboard" },
		{ "Billboard.Name", "EAuracronLODType::Billboard" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD types\n" },
#endif
		{ "HLOD.DisplayName", "Hierarchical LOD" },
		{ "HLOD.Name", "EAuracronLODType::HLOD" },
		{ "Impostor.DisplayName", "Impostor" },
		{ "Impostor.Name", "EAuracronLODType::Impostor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EAuracronLODType::Nanite" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EAuracronLODType::StaticMesh" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLODType::StaticMesh", (int64)EAuracronLODType::StaticMesh },
		{ "EAuracronLODType::HLOD", (int64)EAuracronLODType::HLOD },
		{ "EAuracronLODType::Nanite", (int64)EAuracronLODType::Nanite },
		{ "EAuracronLODType::Impostor", (int64)EAuracronLODType::Impostor },
		{ "EAuracronLODType::Billboard", (int64)EAuracronLODType::Billboard },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLODType",
	"EAuracronLODType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLODType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLODType.InnerSingleton;
}
// ********** End Enum EAuracronLODType ************************************************************

// ********** Begin Enum EAuracronLODQuality *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLODQuality;
static UEnum* EAuracronLODQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLODQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronLODQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronLODQuality.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODQuality>()
{
	return EAuracronLODQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD quality levels\n" },
#endif
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronLODQuality::High" },
		{ "Highest.DisplayName", "Highest" },
		{ "Highest.Name", "EAuracronLODQuality::Highest" },
		{ "Lossless.DisplayName", "Lossless" },
		{ "Lossless.Name", "EAuracronLODQuality::Lossless" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronLODQuality::Low" },
		{ "Lowest.DisplayName", "Lowest" },
		{ "Lowest.Name", "EAuracronLODQuality::Lowest" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronLODQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD quality levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLODQuality::Lowest", (int64)EAuracronLODQuality::Lowest },
		{ "EAuracronLODQuality::Low", (int64)EAuracronLODQuality::Low },
		{ "EAuracronLODQuality::Medium", (int64)EAuracronLODQuality::Medium },
		{ "EAuracronLODQuality::High", (int64)EAuracronLODQuality::High },
		{ "EAuracronLODQuality::Highest", (int64)EAuracronLODQuality::Highest },
		{ "EAuracronLODQuality::Lossless", (int64)EAuracronLODQuality::Lossless },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronLODQuality",
	"EAuracronLODQuality",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLODQuality.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLODQuality.InnerSingleton;
}
// ********** End Enum EAuracronLODQuality *********************************************************

// ********** Begin Enum EAuracronWorldPartitionLODTransitionType **********************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType;
static UEnum* EAuracronWorldPartitionLODTransitionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronWorldPartitionLODTransitionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionLODTransitionType>()
{
	return EAuracronWorldPartitionLODTransitionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD transition types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronWorldPartitionLODTransitionType::Custom" },
		{ "Dither.DisplayName", "Dither" },
		{ "Dither.Name", "EAuracronWorldPartitionLODTransitionType::Dither" },
		{ "Fade.DisplayName", "Fade" },
		{ "Fade.Name", "EAuracronWorldPartitionLODTransitionType::Fade" },
		{ "Instant.DisplayName", "Instant" },
		{ "Instant.Name", "EAuracronWorldPartitionLODTransitionType::Instant" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
		{ "Smooth.DisplayName", "Smooth" },
		{ "Smooth.Name", "EAuracronWorldPartitionLODTransitionType::Smooth" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD transition types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWorldPartitionLODTransitionType::Instant", (int64)EAuracronWorldPartitionLODTransitionType::Instant },
		{ "EAuracronWorldPartitionLODTransitionType::Fade", (int64)EAuracronWorldPartitionLODTransitionType::Fade },
		{ "EAuracronWorldPartitionLODTransitionType::Dither", (int64)EAuracronWorldPartitionLODTransitionType::Dither },
		{ "EAuracronWorldPartitionLODTransitionType::Smooth", (int64)EAuracronWorldPartitionLODTransitionType::Smooth },
		{ "EAuracronWorldPartitionLODTransitionType::Custom", (int64)EAuracronWorldPartitionLODTransitionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronWorldPartitionLODTransitionType",
	"EAuracronWorldPartitionLODTransitionType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType.InnerSingleton;
}
// ********** End Enum EAuracronWorldPartitionLODTransitionType ************************************

// ********** Begin ScriptStruct FAuracronLODConfiguration *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration;
class UScriptStruct* FAuracronLODConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLODConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLODConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Configuration\n * Configuration settings for LOD management in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Configuration\nConfiguration settings for LOD management in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODSystem_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHLODGeneration_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticLODTransitions_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNaniteSupport_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistanceMultiplier_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLODQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshSimplificationRatio_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevels_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLODOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODGenerationTimeout_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionType_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODCaching_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogLODOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableLODSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODSystem;
	static void NewProp_bEnableHLODGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHLODGeneration;
	static void NewProp_bEnableAutomaticLODTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticLODTransitions;
	static void NewProp_bEnableNaniteSupport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNaniteSupport;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistanceMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLODDistance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultLODQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultLODQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MeshSimplificationRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevels;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLODOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODGenerationTimeout;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxLODMemoryUsageMB;
	static void NewProp_bEnableLODCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODCaching;
	static void NewProp_bEnableLODDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODDebug;
	static void NewProp_bLogLODOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogLODOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLODConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODSystem_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableLODSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODSystem = { "bEnableLODSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODSystem_MetaData), NewProp_bEnableLODSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableHLODGeneration_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableHLODGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableHLODGeneration = { "bEnableHLODGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableHLODGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHLODGeneration_MetaData), NewProp_bEnableHLODGeneration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableAutomaticLODTransitions_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableAutomaticLODTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableAutomaticLODTransitions = { "bEnableAutomaticLODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableAutomaticLODTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticLODTransitions_MetaData), NewProp_bEnableAutomaticLODTransitions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableNaniteSupport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableNaniteSupport = { "bEnableNaniteSupport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNaniteSupport_MetaData), NewProp_bEnableNaniteSupport_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_BaseLODDistance = { "BaseLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, BaseLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODDistance_MetaData), NewProp_BaseLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_LODDistanceMultiplier = { "LODDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, LODDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistanceMultiplier_MetaData), NewProp_LODDistanceMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODDistance = { "MaxLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, MaxLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODDistance_MetaData), NewProp_MaxLODDistance_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_DefaultLODQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_DefaultLODQuality = { "DefaultLODQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, DefaultLODQuality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLODQuality_MetaData), NewProp_DefaultLODQuality_MetaData) }; // 1289815305
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MeshSimplificationRatio = { "MeshSimplificationRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, MeshSimplificationRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshSimplificationRatio_MetaData), NewProp_MeshSimplificationRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODLevels = { "MaxLODLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, MaxLODLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevels_MetaData), NewProp_MaxLODLevels_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxConcurrentLODOperations = { "MaxConcurrentLODOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, MaxConcurrentLODOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLODOperations_MetaData), NewProp_MaxConcurrentLODOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_LODGenerationTimeout = { "LODGenerationTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, LODGenerationTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODGenerationTimeout_MetaData), NewProp_LODGenerationTimeout_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, TransitionType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionLODTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionType_MetaData), NewProp_TransitionType_MetaData) }; // 3236420588
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODMemoryUsageMB = { "MaxLODMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODConfiguration, MaxLODMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODMemoryUsageMB_MetaData), NewProp_MaxLODMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODCaching_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableLODCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODCaching = { "bEnableLODCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODCaching_MetaData), NewProp_bEnableLODCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODDebug_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bEnableLODDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODDebug = { "bEnableLODDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODDebug_MetaData), NewProp_bEnableLODDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bLogLODOperations_SetBit(void* Obj)
{
	((FAuracronLODConfiguration*)Obj)->bLogLODOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bLogLODOperations = { "bLogLODOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODConfiguration), &Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bLogLODOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogLODOperations_MetaData), NewProp_bLogLODOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableHLODGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableAutomaticLODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableNaniteSupport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_BaseLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_LODDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_DefaultLODQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_DefaultLODQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MeshSimplificationRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxConcurrentLODOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_LODGenerationTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_MaxLODMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bEnableLODDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewProp_bLogLODOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLODConfiguration",
	Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::PropPointers),
	sizeof(FAuracronLODConfiguration),
	alignof(FAuracronLODConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLODConfiguration *******************************************

// ********** Begin ScriptStruct FAuracronWorldPartitionLODConfiguration ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration;
class UScriptStruct* FAuracronWorldPartitionLODConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronWorldPartitionLODConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition LOD Configuration\n * Specific configuration for World Partition LOD management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition LOD Configuration\nSpecific configuration for World Partition LOD management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODConfig_MetaData[] = {
		{ "Category", "World Partition LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWorldPartitionLOD_MetaData[] = {
		{ "Category", "World Partition LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellLODDistance_MetaData[] = {
		{ "Category", "World Partition LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveLODCells_MetaData[] = {
		{ "Category", "World Partition LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseLODConfig;
	static void NewProp_bEnableWorldPartitionLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWorldPartitionLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CellLODDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveLODCells;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWorldPartitionLODConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_BaseLODConfig = { "BaseLODConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionLODConfiguration, BaseLODConfig), Z_Construct_UScriptStruct_FAuracronLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODConfig_MetaData), NewProp_BaseLODConfig_MetaData) }; // 2575558362
void Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_bEnableWorldPartitionLOD_SetBit(void* Obj)
{
	((FAuracronWorldPartitionLODConfiguration*)Obj)->bEnableWorldPartitionLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_bEnableWorldPartitionLOD = { "bEnableWorldPartitionLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionLODConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_bEnableWorldPartitionLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWorldPartitionLOD_MetaData), NewProp_bEnableWorldPartitionLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_CellLODDistance = { "CellLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionLODConfiguration, CellLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellLODDistance_MetaData), NewProp_CellLODDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_MaxActiveLODCells = { "MaxActiveLODCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionLODConfiguration, MaxActiveLODCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveLODCells_MetaData), NewProp_MaxActiveLODCells_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_BaseLODConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_bEnableWorldPartitionLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_CellLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewProp_MaxActiveLODCells,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronWorldPartitionLODConfiguration",
	Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::PropPointers),
	sizeof(FAuracronWorldPartitionLODConfiguration),
	alignof(FAuracronWorldPartitionLODConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWorldPartitionLODConfiguration *****************************

// ********** Begin ScriptStruct FAuracronLODDescriptor ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor;
class UScriptStruct* FAuracronLODDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLODDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLODDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Descriptor\n * Descriptor for LOD levels and their properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Descriptor\nDescriptor for LOD levels and their properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODId_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODName_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODType_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationState_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationRatio_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCount_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexCount_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorIds_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bounds_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerated_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "LOD Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VertexCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SourceActorIds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bounds;
	static void NewProp_bIsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerated;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLODDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LODId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODId_MetaData), NewProp_LODId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODName = { "LODName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LODName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODName_MetaData), NewProp_LODName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODType = { "LODType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LODType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODType_MetaData), NewProp_LODType_MetaData) }; // 2321154563
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODDistance = { "LODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance_MetaData), NewProp_LODDistance_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, Quality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 1289815305
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_GenerationState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_GenerationState = { "GenerationState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, GenerationState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationState_MetaData), NewProp_GenerationState_MetaData) }; // 2678835244
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SimplificationRatio = { "SimplificationRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, SimplificationRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationRatio_MetaData), NewProp_SimplificationRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, TriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCount_MetaData), NewProp_TriangleCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_VertexCount = { "VertexCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, VertexCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexCount_MetaData), NewProp_VertexCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SourceActorIds_Inner = { "SourceActorIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SourceActorIds = { "SourceActorIds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, SourceActorIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorIds_MetaData), NewProp_SourceActorIds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Bounds = { "Bounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, Bounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bounds_MetaData), NewProp_Bounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsGenerated_SetBit(void* Obj)
{
	((FAuracronLODDescriptor*)Obj)->bIsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsGenerated = { "bIsGenerated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODDescriptor), &Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerated_MetaData), NewProp_bIsGenerated_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronLODDescriptor*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODDescriptor), &Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODDescriptor, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_GenerationState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_GenerationState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SimplificationRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_VertexCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SourceActorIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_SourceActorIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_Bounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLODDescriptor",
	Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::PropPointers),
	sizeof(FAuracronLODDescriptor),
	alignof(FAuracronLODDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLODDescriptor **********************************************

// ********** Begin ScriptStruct FAuracronHLODGenerationParameters *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters;
class UScriptStruct* FAuracronHLODGenerationParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronHLODGenerationParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * HLOD Generation Parameters\n * Parameters for HLOD generation process\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HLOD Generation Parameters\nParameters for HLOD generation process" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorIds_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetQuality_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimplificationRatio_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTriangleCount_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMergeMaterials_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateCollision_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeForDistance_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureResolution_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNanite_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SourceActorIds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetTriangleCount;
	static void NewProp_bMergeMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMergeMaterials;
	static void NewProp_bGenerateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateCollision;
	static void NewProp_bOptimizeForDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeForDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureResolution;
	static void NewProp_bUseNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNanite;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronHLODGenerationParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SourceActorIds_Inner = { "SourceActorIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SourceActorIds = { "SourceActorIds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHLODGenerationParameters, SourceActorIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorIds_MetaData), NewProp_SourceActorIds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetQuality = { "TargetQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHLODGenerationParameters, TargetQuality), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetQuality_MetaData), NewProp_TargetQuality_MetaData) }; // 1289815305
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SimplificationRatio = { "SimplificationRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHLODGenerationParameters, SimplificationRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimplificationRatio_MetaData), NewProp_SimplificationRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetTriangleCount = { "TargetTriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHLODGenerationParameters, TargetTriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTriangleCount_MetaData), NewProp_TargetTriangleCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bMergeMaterials_SetBit(void* Obj)
{
	((FAuracronHLODGenerationParameters*)Obj)->bMergeMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bMergeMaterials = { "bMergeMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHLODGenerationParameters), &Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bMergeMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMergeMaterials_MetaData), NewProp_bMergeMaterials_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bGenerateCollision_SetBit(void* Obj)
{
	((FAuracronHLODGenerationParameters*)Obj)->bGenerateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bGenerateCollision = { "bGenerateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHLODGenerationParameters), &Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bGenerateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateCollision_MetaData), NewProp_bGenerateCollision_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bOptimizeForDistance_SetBit(void* Obj)
{
	((FAuracronHLODGenerationParameters*)Obj)->bOptimizeForDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bOptimizeForDistance = { "bOptimizeForDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHLODGenerationParameters), &Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bOptimizeForDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeForDistance_MetaData), NewProp_bOptimizeForDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TextureResolution = { "TextureResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronHLODGenerationParameters, TextureResolution), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureResolution_MetaData), NewProp_TextureResolution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bUseNanite_SetBit(void* Obj)
{
	((FAuracronHLODGenerationParameters*)Obj)->bUseNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bUseNanite = { "bUseNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronHLODGenerationParameters), &Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bUseNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNanite_MetaData), NewProp_bUseNanite_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SourceActorIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SourceActorIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_SimplificationRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TargetTriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bMergeMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bGenerateCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bOptimizeForDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_TextureResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewProp_bUseNanite,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronHLODGenerationParameters",
	Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::PropPointers),
	sizeof(FAuracronHLODGenerationParameters),
	alignof(FAuracronHLODGenerationParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.InnerSingleton, Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters.InnerSingleton;
}
// ********** End ScriptStruct FAuracronHLODGenerationParameters ***********************************

// ********** Begin ScriptStruct FAuracronLODStatistics ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLODStatistics;
class UScriptStruct* FAuracronLODStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLODStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronLODStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Statistics\n * Performance and usage statistics for LOD system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Statistics\nPerformance and usage statistics for LOD system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLODs_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedLODs_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveLODs_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HLODCount_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageGenerationTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitions_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedGenerations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleReduction_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GeneratedLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveLODs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HLODCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageGenerationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODTransitions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedGenerations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODEfficiency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TriangleReduction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLODStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TotalLODs = { "TotalLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, TotalLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLODs_MetaData), NewProp_TotalLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_GeneratedLODs = { "GeneratedLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, GeneratedLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedLODs_MetaData), NewProp_GeneratedLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_ActiveLODs = { "ActiveLODs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, ActiveLODs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveLODs_MetaData), NewProp_ActiveLODs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_HLODCount = { "HLODCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, HLODCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HLODCount_MetaData), NewProp_HLODCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_AverageGenerationTime = { "AverageGenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, AverageGenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageGenerationTime_MetaData), NewProp_AverageGenerationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LODTransitions = { "LODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, LODTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitions_MetaData), NewProp_LODTransitions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_FailedGenerations = { "FailedGenerations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, FailedGenerations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedGenerations_MetaData), NewProp_FailedGenerations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LODEfficiency = { "LODEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, LODEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODEfficiency_MetaData), NewProp_LODEfficiency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TriangleReduction = { "TriangleReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, TriangleReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleReduction_MetaData), NewProp_TriangleReduction_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TotalLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_GeneratedLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_ActiveLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_HLODCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_AverageGenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_FailedGenerations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LODEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_TriangleReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronLODStatistics",
	Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::PropPointers),
	sizeof(FAuracronLODStatistics),
	alignof(FAuracronLODStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLODStatistics **********************************************

// ********** Begin Delegate FOnLODGenerated *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms
	{
		FString LODId;
		EAuracronLODType LODType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms, LODId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODType = { "LODType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms, LODType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType, METADATA_PARAMS(0, nullptr) }; // 2321154563
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::NewProp_LODType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "OnLODGenerated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLODManager::FOnLODGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnLODGenerated, const FString& LODId, EAuracronLODType LODType)
{
	struct AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms
	{
		FString LODId;
		EAuracronLODType LODType;
	};
	AuracronWorldPartitionLODManager_eventOnLODGenerated_Parms Parms;
	Parms.LODId=LODId;
	Parms.LODType=LODType;
	OnLODGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLODGenerated *********************************************************

// ********** Begin Delegate FOnLODRemoved *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms
	{
		FString LODId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms, LODId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::NewProp_LODId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "OnLODRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLODManager::FOnLODRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnLODRemoved, const FString& LODId)
{
	struct AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms
	{
		FString LODId;
	};
	AuracronWorldPartitionLODManager_eventOnLODRemoved_Parms Parms;
	Parms.LODId=LODId;
	OnLODRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLODRemoved ***********************************************************

// ********** Begin Delegate FOnLODTransition ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLODManager_eventOnLODTransition_Parms
	{
		FString ActorId;
		int32 FromLOD;
		int32 ToLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FromLOD;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ToLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODTransition_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_FromLOD = { "FromLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODTransition_Parms, FromLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_ToLOD = { "ToLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnLODTransition_Parms, ToLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_FromLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::NewProp_ToLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "OnLODTransition__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnLODTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLODManager::FOnLODTransition_DelegateWrapper(const FMulticastScriptDelegate& OnLODTransition, const FString& ActorId, int32 FromLOD, int32 ToLOD)
{
	struct AuracronWorldPartitionLODManager_eventOnLODTransition_Parms
	{
		FString ActorId;
		int32 FromLOD;
		int32 ToLOD;
	};
	AuracronWorldPartitionLODManager_eventOnLODTransition_Parms Parms;
	Parms.ActorId=ActorId;
	Parms.FromLOD=FromLOD;
	Parms.ToLOD=ToLOD;
	OnLODTransition.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLODTransition ********************************************************

// ********** Begin Delegate FOnHLODGenerationCompleted ********************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms
	{
		FString LODId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms, LODId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "OnHLODGenerationCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionLODManager::FOnHLODGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnHLODGenerationCompleted, const FString& LODId, bool bSuccess)
{
	struct AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms
	{
		FString LODId;
		bool bSuccess;
	};
	AuracronWorldPartitionLODManager_eventOnHLODGenerationCompleted_Parms Parms;
	Parms.LODId=LODId;
	Parms.bSuccess=bSuccess ? true : false;
	OnHLODGenerationCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHLODGenerationCompleted **********************************************

// ********** Begin Class UAuracronWorldPartitionLODManager Function CalculateLODLevelForDistance **
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics
{
	struct AuracronWorldPartitionLODManager_eventCalculateLODLevelForDistance_Parms
	{
		float Distance;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCalculateLODLevelForDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCalculateLODLevelForDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "CalculateLODLevelForDistance", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::AuracronWorldPartitionLODManager_eventCalculateLODLevelForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::AuracronWorldPartitionLODManager_eventCalculateLODLevelForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execCalculateLODLevelForDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->CalculateLODLevelForDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function CalculateLODLevelForDistance ****

// ********** Begin Class UAuracronWorldPartitionLODManager Function CreateLOD *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics
{
	struct AuracronWorldPartitionLODManager_eventCreateLOD_Parms
	{
		FString SourceActorId;
		int32 LODLevel;
		EAuracronLODType LODType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD creation and management\n" },
#endif
		{ "CPP_Default_LODType", "StaticMesh" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_SourceActorId = { "SourceActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCreateLOD_Parms, SourceActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceActorId_MetaData), NewProp_SourceActorId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCreateLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODType = { "LODType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCreateLOD_Parms, LODType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType, METADATA_PARAMS(0, nullptr) }; // 2321154563
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventCreateLOD_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_SourceActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_LODType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "CreateLOD", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::AuracronWorldPartitionLODManager_eventCreateLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::AuracronWorldPartitionLODManager_eventCreateLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execCreateLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SourceActorId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_GET_ENUM(EAuracronLODType,Z_Param_LODType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateLOD(Z_Param_SourceActorId,Z_Param_LODLevel,EAuracronLODType(Z_Param_LODType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function CreateLOD ***********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function DoesLODExist ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics
{
	struct AuracronWorldPartitionLODManager_eventDoesLODExist_Parms
	{
		FString LODId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventDoesLODExist_Parms, LODId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODId_MetaData), NewProp_LODId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventDoesLODExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventDoesLODExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "DoesLODExist", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::AuracronWorldPartitionLODManager_eventDoesLODExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::AuracronWorldPartitionLODManager_eventDoesLODExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execDoesLODExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LODId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesLODExist(Z_Param_LODId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function DoesLODExist ********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function DrawDebugLODInfo **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics
{
	struct AuracronWorldPartitionLODManager_eventDrawDebugLODInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventDrawDebugLODInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "DrawDebugLODInfo", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::AuracronWorldPartitionLODManager_eventDrawDebugLODInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::AuracronWorldPartitionLODManager_eventDrawDebugLODInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execDrawDebugLODInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLODInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function DrawDebugLODInfo ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function EnableLODDebug ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics
{
	struct AuracronWorldPartitionLODManager_eventEnableLODDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventEnableLODDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventEnableLODDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "EnableLODDebug", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::AuracronWorldPartitionLODManager_eventEnableLODDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::AuracronWorldPartitionLODManager_eventEnableLODDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execEnableLODDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLODDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function EnableLODDebug ******************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GenerateHLOD ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics
{
	struct AuracronWorldPartitionLODManager_eventGenerateHLOD_Parms
	{
		FAuracronHLODGenerationParameters Parameters;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// HLOD generation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HLOD generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLOD_Parms, Parameters), Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) }; // 4266970203
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLOD_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GenerateHLOD", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::AuracronWorldPartitionLODManager_eventGenerateHLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::AuracronWorldPartitionLODManager_eventGenerateHLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGenerateHLOD)
{
	P_GET_STRUCT_REF(FAuracronHLODGenerationParameters,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateHLOD(Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GenerateHLOD ********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GenerateHLODForActors *********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics
{
	struct AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms
	{
		TArray<FString> ActorIds;
		FAuracronHLODGenerationParameters Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorIds_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActorIds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ActorIds_Inner = { "ActorIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ActorIds = { "ActorIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms, ActorIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorIds_MetaData), NewProp_ActorIds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms, Parameters), Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) }; // 4266970203
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ActorIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ActorIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GenerateHLODForActors", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::AuracronWorldPartitionLODManager_eventGenerateHLODForActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGenerateHLODForActors)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_ActorIds);
	P_GET_STRUCT_REF(FAuracronHLODGenerationParameters,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateHLODForActors(Z_Param_Out_ActorIds,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GenerateHLODForActors ***********

// ********** Begin Class UAuracronWorldPartitionLODManager Function GenerateHLODForCell ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics
{
	struct AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms
	{
		FString CellId;
		FAuracronHLODGenerationParameters Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms, Parameters), Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) }; // 4266970203
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GenerateHLODForCell", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::AuracronWorldPartitionLODManager_eventGenerateHLODForCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGenerateHLODForCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_STRUCT_REF(FAuracronHLODGenerationParameters,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateHLODForCell(Z_Param_CellId,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GenerateHLODForCell *************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GenerateImpostorLOD ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics
{
	struct AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms
	{
		FString ActorId;
		FVector2D TextureResolution;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "CPP_Default_TextureResolution", "(X=512.000,Y=512.000)" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureResolution_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TextureResolution;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_TextureResolution = { "TextureResolution", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms, TextureResolution), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureResolution_MetaData), NewProp_TextureResolution_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_TextureResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GenerateImpostorLOD", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::AuracronWorldPartitionLODManager_eventGenerateImpostorLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGenerateImpostorLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_TextureResolution);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateImpostorLOD(Z_Param_ActorId,Z_Param_Out_TextureResolution);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GenerateImpostorLOD *************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetActiveLODs *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetActiveLODs_Parms
	{
		TArray<FAuracronLODDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetActiveLODs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetActiveLODs", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::AuracronWorldPartitionLODManager_eventGetActiveLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::AuracronWorldPartitionLODManager_eventGetActiveLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetActiveLODs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODDescriptor>*)Z_Param__Result=P_THIS->GetActiveLODs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetActiveLODs *******************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetAllLODs ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetAllLODs_Parms
	{
		TArray<FAuracronLODDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetAllLODs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetAllLODs", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::AuracronWorldPartitionLODManager_eventGetAllLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::AuracronWorldPartitionLODManager_eventGetAllLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetAllLODs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODDescriptor>*)Z_Param__Result=P_THIS->GetAllLODs();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetAllLODs **********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetConfiguration **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetConfiguration_Parms
	{
		FAuracronWorldPartitionLODConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration, METADATA_PARAMS(0, nullptr) }; // 1928479845
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::AuracronWorldPartitionLODManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::AuracronWorldPartitionLODManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWorldPartitionLODConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetConfiguration ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetCurrentLODLevel ************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetCurrentLODLevel_Parms
	{
		FString ActorId;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetCurrentLODLevel_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetCurrentLODLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetCurrentLODLevel", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::AuracronWorldPartitionLODManager_eventGetCurrentLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::AuracronWorldPartitionLODManager_eventGetCurrentLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetCurrentLODLevel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrentLODLevel(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetCurrentLODLevel **************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetGeneratedLODCount **********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetGeneratedLODCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetGeneratedLODCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetGeneratedLODCount", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::AuracronWorldPartitionLODManager_eventGetGeneratedLODCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::AuracronWorldPartitionLODManager_eventGetGeneratedLODCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetGeneratedLODCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGeneratedLODCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetGeneratedLODCount ************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetHLODGenerationState ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetHLODGenerationState_Parms
	{
		FString LODId;
		EAuracronLODGenerationState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetHLODGenerationState_Parms, LODId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODId_MetaData), NewProp_LODId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetHLODGenerationState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODGenerationState, METADATA_PARAMS(0, nullptr) }; // 2678835244
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetHLODGenerationState", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::AuracronWorldPartitionLODManager_eventGetHLODGenerationState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::AuracronWorldPartitionLODManager_eventGetHLODGenerationState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetHLODGenerationState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LODId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLODGenerationState*)Z_Param__Result=P_THIS->GetHLODGenerationState(Z_Param_LODId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetHLODGenerationState **********

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetInstance *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionLODManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::AuracronWorldPartitionLODManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::AuracronWorldPartitionLODManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionLODManager**)Z_Param__Result=UAuracronWorldPartitionLODManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetInstance *********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODDescriptor **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODDescriptor_Parms
	{
		FString LODId;
		FAuracronLODDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODDescriptor_Parms, LODId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODId_MetaData), NewProp_LODId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::AuracronWorldPartitionLODManager_eventGetLODDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::AuracronWorldPartitionLODManager_eventGetLODDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LODId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLODDescriptor*)Z_Param__Result=P_THIS->GetLODDescriptor(Z_Param_LODId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODDescriptor ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODDistanceForLevel ********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODDistanceForLevel_Parms
	{
		int32 LODLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODDistanceForLevel_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODDistanceForLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODDistanceForLevel", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::AuracronWorldPartitionLODManager_eventGetLODDistanceForLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::AuracronWorldPartitionLODManager_eventGetLODDistanceForLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODDistanceForLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetLODDistanceForLevel(Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODDistanceForLevel **********

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODIds *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODIds", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::AuracronWorldPartitionLODManager_eventGetLODIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::AuracronWorldPartitionLODManager_eventGetLODIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLODIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODIds ***********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODsByType *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODsByType_Parms
	{
		EAuracronLODType LODType;
		TArray<FAuracronLODDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_LODType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_LODType = { "LODType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsByType_Parms, LODType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronLODType, METADATA_PARAMS(0, nullptr) }; // 2321154563
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_LODType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_LODType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODsByType", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::AuracronWorldPartitionLODManager_eventGetLODsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::AuracronWorldPartitionLODManager_eventGetLODsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODsByType)
{
	P_GET_ENUM(EAuracronLODType,Z_Param_LODType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODDescriptor>*)Z_Param__Result=P_THIS->GetLODsByType(EAuracronLODType(Z_Param_LODType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODsByType *******************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODsForActor ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODsForActor_Parms
	{
		FString ActorId;
		TArray<FAuracronLODDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsForActor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsForActor_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODsForActor", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::AuracronWorldPartitionLODManager_eventGetLODsForActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::AuracronWorldPartitionLODManager_eventGetLODsForActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODsForActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODDescriptor>*)Z_Param__Result=P_THIS->GetLODsForActor(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODsForActor *****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODsInCell *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODsInCell_Parms
	{
		FString CellId;
		TArray<FAuracronLODDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODDescriptor, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODsInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3719631234
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODsInCell", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::AuracronWorldPartitionLODManager_eventGetLODsInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::AuracronWorldPartitionLODManager_eventGetLODsInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODsInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODDescriptor>*)Z_Param__Result=P_THIS->GetLODsInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODsInCell *******************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetLODStatistics **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetLODStatistics_Parms
	{
		FAuracronLODStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetLODStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLODStatistics, METADATA_PARAMS(0, nullptr) }; // 3619174704
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetLODStatistics", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::AuracronWorldPartitionLODManager_eventGetLODStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::AuracronWorldPartitionLODManager_eventGetLODStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetLODStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLODStatistics*)Z_Param__Result=P_THIS->GetLODStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetLODStatistics ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetTotalLODCount **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetTotalLODCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetTotalLODCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetTotalLODCount", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::AuracronWorldPartitionLODManager_eventGetTotalLODCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::AuracronWorldPartitionLODManager_eventGetTotalLODCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetTotalLODCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalLODCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetTotalLODCount ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function GetTotalMemoryUsage ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionLODManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLODManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionLODManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function GetTotalMemoryUsage *************

// ********** Begin Class UAuracronWorldPartitionLODManager Function Initialize ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics
{
	struct AuracronWorldPartitionLODManager_eventInitialize_Parms
	{
		FAuracronLODConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2575558362
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::AuracronWorldPartitionLODManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::AuracronWorldPartitionLODManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronLODConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function Initialize **********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function IsInitialized *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionLODManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::AuracronWorldPartitionLODManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::AuracronWorldPartitionLODManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function IsInitialized *******************

// ********** Begin Class UAuracronWorldPartitionLODManager Function IsLODDebugEnabled *************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics
{
	struct AuracronWorldPartitionLODManager_eventIsLODDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventIsLODDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventIsLODDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "IsLODDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::AuracronWorldPartitionLODManager_eventIsLODDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::AuracronWorldPartitionLODManager_eventIsLODDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execIsLODDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLODDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function IsLODDebugEnabled ***************

// ********** Begin Class UAuracronWorldPartitionLODManager Function LogLODState *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "LogLODState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execLogLODState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLODState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function LogLODState *********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function RemoveLOD *********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics
{
	struct AuracronWorldPartitionLODManager_eventRemoveLOD_Parms
	{
		FString LODId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LODId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_LODId = { "LODId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventRemoveLOD_Parms, LODId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODId_MetaData), NewProp_LODId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventRemoveLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventRemoveLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_LODId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "RemoveLOD", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::AuracronWorldPartitionLODManager_eventRemoveLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::AuracronWorldPartitionLODManager_eventRemoveLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execRemoveLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LODId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveLOD(Z_Param_LODId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function RemoveLOD ***********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function ResetStatistics ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function ResetStatistics *****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function SetConfiguration **************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionLODManager_eventSetConfiguration_Parms
	{
		FAuracronLODConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2575558362
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::AuracronWorldPartitionLODManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::AuracronWorldPartitionLODManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronLODConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function SetConfiguration ****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function SetLODLevel *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics
{
	struct AuracronWorldPartitionLODManager_eventSetLODLevel_Parms
	{
		FString ActorId;
		int32 LODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD transitions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD transitions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSetLODLevel_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSetLODLevel_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventSetLODLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventSetLODLevel_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "SetLODLevel", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::AuracronWorldPartitionLODManager_eventSetLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::AuracronWorldPartitionLODManager_eventSetLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execSetLODLevel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLODLevel(Z_Param_ActorId,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function SetLODLevel *********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function Shutdown **********************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function Shutdown ************************

// ********** Begin Class UAuracronWorldPartitionLODManager Function SimplifyMesh ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics
{
	struct AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms
	{
		FString ActorId;
		float SimplificationRatio;
		int32 TargetLODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh simplification\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh simplification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimplificationRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetLODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_SimplificationRatio = { "SimplificationRatio", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms, SimplificationRatio), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_TargetLODLevel = { "TargetLODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms, TargetLODLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_SimplificationRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_TargetLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "SimplifyMesh", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::AuracronWorldPartitionLODManager_eventSimplifyMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execSimplifyMesh)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SimplificationRatio);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetLODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SimplifyMesh(Z_Param_ActorId,Z_Param_SimplificationRatio,Z_Param_TargetLODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function SimplifyMesh ********************

// ********** Begin Class UAuracronWorldPartitionLODManager Function SimplifyMeshToTriangleCount ***
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics
{
	struct AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms
	{
		FString ActorId;
		int32 TargetTriangleCount;
		int32 TargetLODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetTriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetLODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_TargetTriangleCount = { "TargetTriangleCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms, TargetTriangleCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_TargetLODLevel = { "TargetLODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms, TargetLODLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_TargetTriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_TargetLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "SimplifyMeshToTriangleCount", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::AuracronWorldPartitionLODManager_eventSimplifyMeshToTriangleCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execSimplifyMeshToTriangleCount)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetTriangleCount);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetLODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SimplifyMeshToTriangleCount(Z_Param_ActorId,Z_Param_TargetTriangleCount,Z_Param_TargetLODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function SimplifyMeshToTriangleCount *****

// ********** Begin Class UAuracronWorldPartitionLODManager Function Tick **************************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics
{
	struct AuracronWorldPartitionLODManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::AuracronWorldPartitionLODManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::AuracronWorldPartitionLODManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function Tick ****************************

// ********** Begin Class UAuracronWorldPartitionLODManager Function TransitionToLOD ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics
{
	struct AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms
	{
		FString ActorId;
		int32 TargetLODLevel;
		float TransitionTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "CPP_Default_TransitionTime", "-1.000000" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetLODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_TargetLODLevel = { "TargetLODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms, TargetLODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms, TransitionTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_TargetLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "TransitionToLOD", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::AuracronWorldPartitionLODManager_eventTransitionToLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execTransitionToLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetLODLevel);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TransitionToLOD(Z_Param_ActorId,Z_Param_TargetLODLevel,Z_Param_TransitionTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function TransitionToLOD *****************

// ********** Begin Class UAuracronWorldPartitionLODManager Function UpdateDistanceBasedLODs *******
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics
{
	struct AuracronWorldPartitionLODManager_eventUpdateDistanceBasedLODs_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance-based LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance-based LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventUpdateDistanceBasedLODs_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "UpdateDistanceBasedLODs", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLODManager_eventUpdateDistanceBasedLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionLODManager_eventUpdateDistanceBasedLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execUpdateDistanceBasedLODs)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLODs(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function UpdateDistanceBasedLODs *********

// ********** Begin Class UAuracronWorldPartitionLODManager Function UpdateLODTransitions **********
struct Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics
{
	struct AuracronWorldPartitionLODManager_eventUpdateLODTransitions_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionLODManager_eventUpdateLODTransitions_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionLODManager, nullptr, "UpdateLODTransitions", Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::AuracronWorldPartitionLODManager_eventUpdateLODTransitions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::AuracronWorldPartitionLODManager_eventUpdateLODTransitions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionLODManager::execUpdateLODTransitions)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLODTransitions(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionLODManager Function UpdateLODTransitions ************

// ********** Begin Class UAuracronWorldPartitionLODManager ****************************************
void UAuracronWorldPartitionLODManager::StaticRegisterNativesUAuracronWorldPartitionLODManager()
{
	UClass* Class = UAuracronWorldPartitionLODManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLODLevelForDistance", &UAuracronWorldPartitionLODManager::execCalculateLODLevelForDistance },
		{ "CreateLOD", &UAuracronWorldPartitionLODManager::execCreateLOD },
		{ "DoesLODExist", &UAuracronWorldPartitionLODManager::execDoesLODExist },
		{ "DrawDebugLODInfo", &UAuracronWorldPartitionLODManager::execDrawDebugLODInfo },
		{ "EnableLODDebug", &UAuracronWorldPartitionLODManager::execEnableLODDebug },
		{ "GenerateHLOD", &UAuracronWorldPartitionLODManager::execGenerateHLOD },
		{ "GenerateHLODForActors", &UAuracronWorldPartitionLODManager::execGenerateHLODForActors },
		{ "GenerateHLODForCell", &UAuracronWorldPartitionLODManager::execGenerateHLODForCell },
		{ "GenerateImpostorLOD", &UAuracronWorldPartitionLODManager::execGenerateImpostorLOD },
		{ "GetActiveLODs", &UAuracronWorldPartitionLODManager::execGetActiveLODs },
		{ "GetAllLODs", &UAuracronWorldPartitionLODManager::execGetAllLODs },
		{ "GetConfiguration", &UAuracronWorldPartitionLODManager::execGetConfiguration },
		{ "GetCurrentLODLevel", &UAuracronWorldPartitionLODManager::execGetCurrentLODLevel },
		{ "GetGeneratedLODCount", &UAuracronWorldPartitionLODManager::execGetGeneratedLODCount },
		{ "GetHLODGenerationState", &UAuracronWorldPartitionLODManager::execGetHLODGenerationState },
		{ "GetInstance", &UAuracronWorldPartitionLODManager::execGetInstance },
		{ "GetLODDescriptor", &UAuracronWorldPartitionLODManager::execGetLODDescriptor },
		{ "GetLODDistanceForLevel", &UAuracronWorldPartitionLODManager::execGetLODDistanceForLevel },
		{ "GetLODIds", &UAuracronWorldPartitionLODManager::execGetLODIds },
		{ "GetLODsByType", &UAuracronWorldPartitionLODManager::execGetLODsByType },
		{ "GetLODsForActor", &UAuracronWorldPartitionLODManager::execGetLODsForActor },
		{ "GetLODsInCell", &UAuracronWorldPartitionLODManager::execGetLODsInCell },
		{ "GetLODStatistics", &UAuracronWorldPartitionLODManager::execGetLODStatistics },
		{ "GetTotalLODCount", &UAuracronWorldPartitionLODManager::execGetTotalLODCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionLODManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionLODManager::execInitialize },
		{ "IsInitialized", &UAuracronWorldPartitionLODManager::execIsInitialized },
		{ "IsLODDebugEnabled", &UAuracronWorldPartitionLODManager::execIsLODDebugEnabled },
		{ "LogLODState", &UAuracronWorldPartitionLODManager::execLogLODState },
		{ "RemoveLOD", &UAuracronWorldPartitionLODManager::execRemoveLOD },
		{ "ResetStatistics", &UAuracronWorldPartitionLODManager::execResetStatistics },
		{ "SetConfiguration", &UAuracronWorldPartitionLODManager::execSetConfiguration },
		{ "SetLODLevel", &UAuracronWorldPartitionLODManager::execSetLODLevel },
		{ "Shutdown", &UAuracronWorldPartitionLODManager::execShutdown },
		{ "SimplifyMesh", &UAuracronWorldPartitionLODManager::execSimplifyMesh },
		{ "SimplifyMeshToTriangleCount", &UAuracronWorldPartitionLODManager::execSimplifyMeshToTriangleCount },
		{ "Tick", &UAuracronWorldPartitionLODManager::execTick },
		{ "TransitionToLOD", &UAuracronWorldPartitionLODManager::execTransitionToLOD },
		{ "UpdateDistanceBasedLODs", &UAuracronWorldPartitionLODManager::execUpdateDistanceBasedLODs },
		{ "UpdateLODTransitions", &UAuracronWorldPartitionLODManager::execUpdateLODTransitions },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager;
UClass* UAuracronWorldPartitionLODManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionLODManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionLODManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionLODManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister()
{
	return UAuracronWorldPartitionLODManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition LOD Manager\n * Central manager for LOD operations in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionLOD.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition LOD Manager\nCentral manager for LOD operations in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLODGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLODRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLODTransition_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHLODGenerationCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLODGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLODRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLODTransition;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHLODGenerationCompleted;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CalculateLODLevelForDistance, "CalculateLODLevelForDistance" }, // 4139716546
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_CreateLOD, "CreateLOD" }, // 1294027671
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DoesLODExist, "DoesLODExist" }, // 3719652444
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_DrawDebugLODInfo, "DrawDebugLODInfo" }, // 3527034610
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_EnableLODDebug, "EnableLODDebug" }, // 4151578602
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLOD, "GenerateHLOD" }, // 33780139
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForActors, "GenerateHLODForActors" }, // 1141037715
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateHLODForCell, "GenerateHLODForCell" }, // 1951153994
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GenerateImpostorLOD, "GenerateImpostorLOD" }, // 3954898437
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetActiveLODs, "GetActiveLODs" }, // 2477105040
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetAllLODs, "GetAllLODs" }, // 2891653085
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetConfiguration, "GetConfiguration" }, // 1024298531
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetCurrentLODLevel, "GetCurrentLODLevel" }, // 855468210
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetGeneratedLODCount, "GetGeneratedLODCount" }, // 4180653133
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetHLODGenerationState, "GetHLODGenerationState" }, // 3087234885
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetInstance, "GetInstance" }, // 2493063455
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDescriptor, "GetLODDescriptor" }, // 3117752047
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODDistanceForLevel, "GetLODDistanceForLevel" }, // 3893670221
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODIds, "GetLODIds" }, // 3294567892
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsByType, "GetLODsByType" }, // 3167142383
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsForActor, "GetLODsForActor" }, // 2035105074
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODsInCell, "GetLODsInCell" }, // 3660145366
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetLODStatistics, "GetLODStatistics" }, // 305796721
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalLODCount, "GetTotalLODCount" }, // 1636464165
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 2759056431
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Initialize, "Initialize" }, // 1210853849
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsInitialized, "IsInitialized" }, // 1806349562
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_IsLODDebugEnabled, "IsLODDebugEnabled" }, // 3597268685
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_LogLODState, "LogLODState" }, // 3217992485
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature, "OnHLODGenerationCompleted__DelegateSignature" }, // 2591060286
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature, "OnLODGenerated__DelegateSignature" }, // 3299411413
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature, "OnLODRemoved__DelegateSignature" }, // 3002965379
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature, "OnLODTransition__DelegateSignature" }, // 3180534176
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_RemoveLOD, "RemoveLOD" }, // 1219957625
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_ResetStatistics, "ResetStatistics" }, // 1403134209
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetConfiguration, "SetConfiguration" }, // 1174462583
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SetLODLevel, "SetLODLevel" }, // 3914422703
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Shutdown, "Shutdown" }, // 982955940
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMesh, "SimplifyMesh" }, // 1997449204
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_SimplifyMeshToTriangleCount, "SimplifyMeshToTriangleCount" }, // 3420554938
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_Tick, "Tick" }, // 3997307332
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_TransitionToLOD, "TransitionToLOD" }, // 1667253066
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateDistanceBasedLODs, "UpdateDistanceBasedLODs" }, // 3174833172
		{ &Z_Construct_UFunction_UAuracronWorldPartitionLODManager_UpdateLODTransitions, "UpdateLODTransitions" }, // 777193896
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionLODManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODGenerated = { "OnLODGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, OnLODGenerated), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLODGenerated_MetaData), NewProp_OnLODGenerated_MetaData) }; // 3299411413
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODRemoved = { "OnLODRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, OnLODRemoved), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLODRemoved_MetaData), NewProp_OnLODRemoved_MetaData) }; // 3002965379
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODTransition = { "OnLODTransition", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, OnLODTransition), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnLODTransition__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLODTransition_MetaData), NewProp_OnLODTransition_MetaData) }; // 3180534176
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnHLODGenerationCompleted = { "OnHLODGenerationCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, OnHLODGenerationCompleted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionLODManager_OnHLODGenerationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHLODGenerationCompleted_MetaData), NewProp_OnHLODGenerationCompleted_MetaData) }; // 2591060286
void Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionLODManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionLODManager), &Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 1928479845
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionLODManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnLODTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_OnHLODGenerationCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::ClassParams = {
	&UAuracronWorldPartitionLODManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager.OuterSingleton;
}
UAuracronWorldPartitionLODManager::UAuracronWorldPartitionLODManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionLODManager);
UAuracronWorldPartitionLODManager::~UAuracronWorldPartitionLODManager() {}
// ********** End Class UAuracronWorldPartitionLODManager ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronLODGenerationState_StaticEnum, TEXT("EAuracronLODGenerationState"), &Z_Registration_Info_UEnum_EAuracronLODGenerationState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2678835244U) },
		{ EAuracronLODType_StaticEnum, TEXT("EAuracronLODType"), &Z_Registration_Info_UEnum_EAuracronLODType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2321154563U) },
		{ EAuracronLODQuality_StaticEnum, TEXT("EAuracronLODQuality"), &Z_Registration_Info_UEnum_EAuracronLODQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1289815305U) },
		{ EAuracronWorldPartitionLODTransitionType_StaticEnum, TEXT("EAuracronWorldPartitionLODTransitionType"), &Z_Registration_Info_UEnum_EAuracronWorldPartitionLODTransitionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3236420588U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLODConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics::NewStructOps, TEXT("AuracronLODConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronLODConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLODConfiguration), 2575558362U) },
		{ FAuracronWorldPartitionLODConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics::NewStructOps, TEXT("AuracronWorldPartitionLODConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionLODConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWorldPartitionLODConfiguration), 1928479845U) },
		{ FAuracronLODDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics::NewStructOps, TEXT("AuracronLODDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronLODDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLODDescriptor), 3719631234U) },
		{ FAuracronHLODGenerationParameters::StaticStruct, Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics::NewStructOps, TEXT("AuracronHLODGenerationParameters"), &Z_Registration_Info_UScriptStruct_FAuracronHLODGenerationParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronHLODGenerationParameters), 4266970203U) },
		{ FAuracronLODStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics::NewStructOps, TEXT("AuracronLODStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronLODStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLODStatistics), 3619174704U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionLODManager, UAuracronWorldPartitionLODManager::StaticClass, TEXT("UAuracronWorldPartitionLODManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionLODManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionLODManager), 1082506193U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_3947004064(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
