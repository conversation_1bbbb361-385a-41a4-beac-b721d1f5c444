// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronEyeGeneration.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronEyeGeneration_generated_h
#error "AuracronEyeGeneration.generated.h already included, missing '#pragma once' in AuracronEyeGeneration.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronEyeGeneration_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FEyeGeometryData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEyeGeometryData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEyeGeometryData;
// ********** End ScriptStruct FEyeGeometryData ****************************************************

// ********** Begin ScriptStruct FEyeMaterialData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEyeMaterialData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEyeMaterialData;
// ********** End ScriptStruct FEyeMaterialData ****************************************************

// ********** Begin ScriptStruct FEyeAnimationData *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h_160_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEyeAnimationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEyeAnimationData;
// ********** End ScriptStruct FEyeAnimationData ***************************************************

// ********** Begin ScriptStruct FEyeGenerationParameters ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h_213_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEyeGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEyeGenerationParameters;
// ********** End ScriptStruct FEyeGenerationParameters ********************************************

// ********** Begin ScriptStruct FGeneratedEyeData *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h_259_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGeneratedEyeData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FGeneratedEyeData;
// ********** End ScriptStruct FGeneratedEyeData ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronEyeGeneration_h

// ********** Begin Enum EEyeType ******************************************************************
#define FOREACH_ENUM_EEYETYPE(op) \
	op(EEyeType::Human) \
	op(EEyeType::Creature) \
	op(EEyeType::Robotic) \
	op(EEyeType::Fantasy) \
	op(EEyeType::Custom) 

enum class EEyeType : uint8;
template<> struct TIsUEnumClass<EEyeType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEyeType>();
// ********** End Enum EEyeType ********************************************************************

// ********** Begin Enum EEyeColor *****************************************************************
#define FOREACH_ENUM_EEYECOLOR(op) \
	op(EEyeColor::Brown) \
	op(EEyeColor::Blue) \
	op(EEyeColor::Green) \
	op(EEyeColor::Hazel) \
	op(EEyeColor::Gray) \
	op(EEyeColor::Amber) \
	op(EEyeColor::Red) \
	op(EEyeColor::Purple) \
	op(EEyeColor::Custom) 

enum class EEyeColor : uint8;
template<> struct TIsUEnumClass<EEyeColor> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEyeColor>();
// ********** End Enum EEyeColor *******************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
