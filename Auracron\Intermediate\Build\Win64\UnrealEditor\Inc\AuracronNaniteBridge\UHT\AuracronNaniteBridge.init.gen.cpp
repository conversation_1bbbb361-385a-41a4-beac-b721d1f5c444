// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronNaniteBridge_init() {}
	AURACRONNANITEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature();
	AURACRONNANITEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronNaniteBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronNaniteBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronNaniteBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronNaniteBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x459ABD2D,
				0x723CD077,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronNaniteBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronNaniteBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronNaniteBridge(Z_Construct_UPackage__Script_AuracronNaniteBridge, TEXT("/Script/AuracronNaniteBridge"), Z_Registration_Info_UPackage__Script_AuracronNaniteBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x459ABD2D, 0x723CD077));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
