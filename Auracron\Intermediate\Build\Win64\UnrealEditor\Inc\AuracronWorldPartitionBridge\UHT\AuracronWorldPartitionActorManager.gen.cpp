// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionActorManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionActorManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronActorStreamingState **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronActorStreamingState;
static UEnum* EAuracronActorStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronActorStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronActorStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronActorStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorStreamingState>()
{
	return EAuracronActorStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronActorStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronActorStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronActorStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EAuracronActorStreamingState::Pending" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronActorStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronActorStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronActorStreamingState::Unloaded", (int64)EAuracronActorStreamingState::Unloaded },
		{ "EAuracronActorStreamingState::Loading", (int64)EAuracronActorStreamingState::Loading },
		{ "EAuracronActorStreamingState::Loaded", (int64)EAuracronActorStreamingState::Loaded },
		{ "EAuracronActorStreamingState::Unloading", (int64)EAuracronActorStreamingState::Unloading },
		{ "EAuracronActorStreamingState::Failed", (int64)EAuracronActorStreamingState::Failed },
		{ "EAuracronActorStreamingState::Pending", (int64)EAuracronActorStreamingState::Pending },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronActorStreamingState",
	"EAuracronActorStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronActorStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronActorStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronActorStreamingState ************************************************

// ********** Begin Enum EAuracronActorPlacementType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronActorPlacementType;
static UEnum* EAuracronActorPlacementType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorPlacementType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronActorPlacementType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronActorPlacementType"));
	}
	return Z_Registration_Info_UEnum_EAuracronActorPlacementType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorPlacementType>()
{
	return EAuracronActorPlacementType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor placement types\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EAuracronActorPlacementType::Dynamic" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
		{ "Persistent.DisplayName", "Persistent" },
		{ "Persistent.Name", "EAuracronActorPlacementType::Persistent" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EAuracronActorPlacementType::Static" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EAuracronActorPlacementType::Streaming" },
		{ "Temporary.DisplayName", "Temporary" },
		{ "Temporary.Name", "EAuracronActorPlacementType::Temporary" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor placement types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronActorPlacementType::Static", (int64)EAuracronActorPlacementType::Static },
		{ "EAuracronActorPlacementType::Dynamic", (int64)EAuracronActorPlacementType::Dynamic },
		{ "EAuracronActorPlacementType::Streaming", (int64)EAuracronActorPlacementType::Streaming },
		{ "EAuracronActorPlacementType::Persistent", (int64)EAuracronActorPlacementType::Persistent },
		{ "EAuracronActorPlacementType::Temporary", (int64)EAuracronActorPlacementType::Temporary },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronActorPlacementType",
	"EAuracronActorPlacementType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorPlacementType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronActorPlacementType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronActorPlacementType.InnerSingleton;
}
// ********** End Enum EAuracronActorPlacementType *************************************************

// ********** Begin Enum EAuracronSpatialQueryType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSpatialQueryType;
static UEnum* EAuracronSpatialQueryType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSpatialQueryType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSpatialQueryType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronSpatialQueryType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSpatialQueryType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronSpatialQueryType>()
{
	return EAuracronSpatialQueryType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EAuracronSpatialQueryType::Box" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial query types\n" },
#endif
		{ "Cone.DisplayName", "Cone" },
		{ "Cone.Name", "EAuracronSpatialQueryType::Cone" },
		{ "Cylinder.DisplayName", "Cylinder" },
		{ "Cylinder.Name", "EAuracronSpatialQueryType::Cylinder" },
		{ "Frustum.DisplayName", "Frustum" },
		{ "Frustum.Name", "EAuracronSpatialQueryType::Frustum" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
		{ "Point.DisplayName", "Point" },
		{ "Point.Name", "EAuracronSpatialQueryType::Point" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EAuracronSpatialQueryType::Sphere" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial query types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSpatialQueryType::Point", (int64)EAuracronSpatialQueryType::Point },
		{ "EAuracronSpatialQueryType::Sphere", (int64)EAuracronSpatialQueryType::Sphere },
		{ "EAuracronSpatialQueryType::Box", (int64)EAuracronSpatialQueryType::Box },
		{ "EAuracronSpatialQueryType::Cylinder", (int64)EAuracronSpatialQueryType::Cylinder },
		{ "EAuracronSpatialQueryType::Cone", (int64)EAuracronSpatialQueryType::Cone },
		{ "EAuracronSpatialQueryType::Frustum", (int64)EAuracronSpatialQueryType::Frustum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronSpatialQueryType",
	"EAuracronSpatialQueryType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSpatialQueryType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSpatialQueryType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSpatialQueryType.InnerSingleton;
}
// ********** End Enum EAuracronSpatialQueryType ***************************************************

// ********** Begin Enum EAuracronActorLifecycleState **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronActorLifecycleState;
static UEnum* EAuracronActorLifecycleState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorLifecycleState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronActorLifecycleState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronActorLifecycleState"));
	}
	return Z_Registration_Info_UEnum_EAuracronActorLifecycleState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorLifecycleState>()
{
	return EAuracronActorLifecycleState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronActorLifecycleState::Active" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor lifecycle states\n" },
#endif
		{ "Created.DisplayName", "Created" },
		{ "Created.Name", "EAuracronActorLifecycleState::Created" },
		{ "Destroyed.DisplayName", "Destroyed" },
		{ "Destroyed.Name", "EAuracronActorLifecycleState::Destroyed" },
		{ "Destroying.DisplayName", "Destroying" },
		{ "Destroying.Name", "EAuracronActorLifecycleState::Destroying" },
		{ "Inactive.DisplayName", "Inactive" },
		{ "Inactive.Name", "EAuracronActorLifecycleState::Inactive" },
		{ "Initialized.DisplayName", "Initialized" },
		{ "Initialized.Name", "EAuracronActorLifecycleState::Initialized" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor lifecycle states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronActorLifecycleState::Created", (int64)EAuracronActorLifecycleState::Created },
		{ "EAuracronActorLifecycleState::Initialized", (int64)EAuracronActorLifecycleState::Initialized },
		{ "EAuracronActorLifecycleState::Active", (int64)EAuracronActorLifecycleState::Active },
		{ "EAuracronActorLifecycleState::Inactive", (int64)EAuracronActorLifecycleState::Inactive },
		{ "EAuracronActorLifecycleState::Destroying", (int64)EAuracronActorLifecycleState::Destroying },
		{ "EAuracronActorLifecycleState::Destroyed", (int64)EAuracronActorLifecycleState::Destroyed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronActorLifecycleState",
	"EAuracronActorLifecycleState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState()
{
	if (!Z_Registration_Info_UEnum_EAuracronActorLifecycleState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronActorLifecycleState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronActorLifecycleState.InnerSingleton;
}
// ********** End Enum EAuracronActorLifecycleState ************************************************

// ********** Begin ScriptStruct FAuracronActorManagementConfiguration *****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration;
class UScriptStruct* FAuracronActorManagementConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronActorManagementConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Actor Management Configuration\n * Configuration settings for actor management in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor Management Configuration\nConfiguration settings for actor management in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableActorManagement_MetaData[] = {
		{ "Category", "Actor Management" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableActorStreaming_MetaData[] = {
		{ "Category", "Actor Management" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSpatialQueries_MetaData[] = {
		{ "Category", "Actor Management" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActorsPerCell_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentActorOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorStreamingDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorUnloadingDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActorMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableActorPooling_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialQueryRadius_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpatialQueryResults_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableActorDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogActorOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableActorManagement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableActorManagement;
	static void NewProp_bEnableActorStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableActorStreaming;
	static void NewProp_bEnableSpatialQueries_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSpatialQueries;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActorsPerCell;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentActorOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActorStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActorUnloadingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxActorMemoryUsageMB;
	static void NewProp_bEnableActorPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableActorPooling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpatialQueryRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSpatialQueryResults;
	static void NewProp_bEnableActorDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableActorDebug;
	static void NewProp_bLogActorOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogActorOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronActorManagementConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorManagement_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bEnableActorManagement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorManagement = { "bEnableActorManagement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorManagement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableActorManagement_MetaData), NewProp_bEnableActorManagement_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorStreaming_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bEnableActorStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorStreaming = { "bEnableActorStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableActorStreaming_MetaData), NewProp_bEnableActorStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableSpatialQueries_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bEnableSpatialQueries = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableSpatialQueries = { "bEnableSpatialQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableSpatialQueries_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSpatialQueries_MetaData), NewProp_bEnableSpatialQueries_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxActorsPerCell = { "MaxActorsPerCell", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, MaxActorsPerCell), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActorsPerCell_MetaData), NewProp_MaxActorsPerCell_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxConcurrentActorOperations = { "MaxConcurrentActorOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, MaxConcurrentActorOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentActorOperations_MetaData), NewProp_MaxConcurrentActorOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_ActorStreamingDistance = { "ActorStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, ActorStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorStreamingDistance_MetaData), NewProp_ActorStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_ActorUnloadingDistance = { "ActorUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, ActorUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorUnloadingDistance_MetaData), NewProp_ActorUnloadingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxActorMemoryUsageMB = { "MaxActorMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, MaxActorMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActorMemoryUsageMB_MetaData), NewProp_MaxActorMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorPooling_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bEnableActorPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorPooling = { "bEnableActorPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableActorPooling_MetaData), NewProp_bEnableActorPooling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_SpatialQueryRadius = { "SpatialQueryRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, SpatialQueryRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialQueryRadius_MetaData), NewProp_SpatialQueryRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxSpatialQueryResults = { "MaxSpatialQueryResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorManagementConfiguration, MaxSpatialQueryResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpatialQueryResults_MetaData), NewProp_MaxSpatialQueryResults_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorDebug_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bEnableActorDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorDebug = { "bEnableActorDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableActorDebug_MetaData), NewProp_bEnableActorDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bLogActorOperations_SetBit(void* Obj)
{
	((FAuracronActorManagementConfiguration*)Obj)->bLogActorOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bLogActorOperations = { "bLogActorOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorManagementConfiguration), &Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bLogActorOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogActorOperations_MetaData), NewProp_bLogActorOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorManagement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableSpatialQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxActorsPerCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxConcurrentActorOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_ActorStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_ActorUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxActorMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_SpatialQueryRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_MaxSpatialQueryResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bEnableActorDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewProp_bLogActorOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronActorManagementConfiguration",
	Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::PropPointers),
	sizeof(FAuracronActorManagementConfiguration),
	alignof(FAuracronActorManagementConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronActorManagementConfiguration *******************************

// ********** Begin ScriptStruct FAuracronActorDescriptor ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor;
class UScriptStruct* FAuracronActorDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronActorDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronActorDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Actor Descriptor\n * Descriptor for actors in world partition system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor Descriptor\nDescriptor for actors in world partition system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorName_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bounds_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementType_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifecycleState_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayers_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSpatiallyLoaded_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRuntimeOnly_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Actor Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlacementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlacementType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LifecycleState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LifecycleState;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DataLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DataLayers;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Properties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Properties;
	static void NewProp_bIsSpatiallyLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSpatiallyLoaded;
	static void NewProp_bIsRuntimeOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRuntimeOnly;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronActorDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorName = { "ActorName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, ActorName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorName_MetaData), NewProp_ActorName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, ActorClass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Bounds = { "Bounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Bounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bounds_MetaData), NewProp_Bounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_PlacementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_PlacementType = { "PlacementType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, PlacementType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementType_MetaData), NewProp_PlacementType_MetaData) }; // 3625038873
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 3750643624
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LifecycleState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LifecycleState = { "LifecycleState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, LifecycleState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifecycleState_MetaData), NewProp_LifecycleState_MetaData) }; // 3675356152
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_DataLayers_Inner = { "DataLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_DataLayers = { "DataLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, DataLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayers_MetaData), NewProp_DataLayers_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Tags_Inner = { "Tags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Tags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties_ValueProp = { "Properties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties_Key_KeyProp = { "Properties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, Properties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsSpatiallyLoaded_SetBit(void* Obj)
{
	((FAuracronActorDescriptor*)Obj)->bIsSpatiallyLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsSpatiallyLoaded = { "bIsSpatiallyLoaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorDescriptor), &Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsSpatiallyLoaded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSpatiallyLoaded_MetaData), NewProp_bIsSpatiallyLoaded_MetaData) };
void Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsRuntimeOnly_SetBit(void* Obj)
{
	((FAuracronActorDescriptor*)Obj)->bIsRuntimeOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsRuntimeOnly = { "bIsRuntimeOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronActorDescriptor), &Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsRuntimeOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRuntimeOnly_MetaData), NewProp_bIsRuntimeOnly_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorDescriptor, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Bounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_PlacementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_PlacementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LifecycleState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LifecycleState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_DataLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_DataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Tags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsSpatiallyLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_bIsRuntimeOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronActorDescriptor",
	Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::PropPointers),
	sizeof(FAuracronActorDescriptor),
	alignof(FAuracronActorDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronActorDescriptor ********************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryParameters ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters;
class UScriptStruct* FAuracronSpatialQueryParameters::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronSpatialQueryParameters"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spatial Query Parameters\n * Parameters for spatial queries of actors\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial Query Parameters\nParameters for spatial queries of actors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryType_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryLocation_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryRadius_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryBox_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDirection_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryDistance_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryAngle_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterClasses_MetaData[] = {
		{ "Category", "Query" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// For cone queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For cone queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilterTags_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeUnloadedActors_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxResults_MetaData[] = {
		{ "Category", "Query" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QueryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QueryType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryBox;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryAngle;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterClasses_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FilterClasses;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilterTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FilterTags;
	static void NewProp_bIncludeUnloadedActors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeUnloadedActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSpatialQueryParameters>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryType = { "QueryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronSpatialQueryType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryType_MetaData), NewProp_QueryType_MetaData) }; // 89800959
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryLocation = { "QueryLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryLocation_MetaData), NewProp_QueryLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryRadius = { "QueryRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryRadius_MetaData), NewProp_QueryRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryBox = { "QueryBox", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryBox), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryBox_MetaData), NewProp_QueryBox_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryDirection = { "QueryDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDirection_MetaData), NewProp_QueryDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryDistance = { "QueryDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryDistance_MetaData), NewProp_QueryDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryAngle = { "QueryAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, QueryAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryAngle_MetaData), NewProp_QueryAngle_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterClasses_Inner = { "FilterClasses", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterClasses = { "FilterClasses", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, FilterClasses), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterClasses_MetaData), NewProp_FilterClasses_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterTags_Inner = { "FilterTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterTags = { "FilterTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, FilterTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilterTags_MetaData), NewProp_FilterTags_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_bIncludeUnloadedActors_SetBit(void* Obj)
{
	((FAuracronSpatialQueryParameters*)Obj)->bIncludeUnloadedActors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_bIncludeUnloadedActors = { "bIncludeUnloadedActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSpatialQueryParameters), &Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_bIncludeUnloadedActors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeUnloadedActors_MetaData), NewProp_bIncludeUnloadedActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryParameters, MaxResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxResults_MetaData), NewProp_MaxResults_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_QueryAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterClasses_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterClasses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_FilterTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_bIncludeUnloadedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewProp_MaxResults,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronSpatialQueryParameters",
	Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::PropPointers),
	sizeof(FAuracronSpatialQueryParameters),
	alignof(FAuracronSpatialQueryParameters),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSpatialQueryParameters *************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryResult ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult;
class UScriptStruct* FAuracronSpatialQueryResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSpatialQueryResult, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronSpatialQueryResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spatial Query Result\n * Result of a spatial query operation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial Query Result\nResult of a spatial query operation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoundActors_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorIds_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalResults_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bQuerySuccessful_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FoundActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoundActors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActorIds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalResults;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QueryTime;
	static void NewProp_bQuerySuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bQuerySuccessful;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSpatialQueryResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_FoundActors_Inner = { "FoundActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_FoundActors = { "FoundActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryResult, FoundActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoundActors_MetaData), NewProp_FoundActors_MetaData) }; // 486931340
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ActorIds_Inner = { "ActorIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ActorIds = { "ActorIds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryResult, ActorIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorIds_MetaData), NewProp_ActorIds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_TotalResults = { "TotalResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryResult, TotalResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalResults_MetaData), NewProp_TotalResults_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_QueryTime = { "QueryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryResult, QueryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryTime_MetaData), NewProp_QueryTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_bQuerySuccessful_SetBit(void* Obj)
{
	((FAuracronSpatialQueryResult*)Obj)->bQuerySuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_bQuerySuccessful = { "bQuerySuccessful", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSpatialQueryResult), &Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_bQuerySuccessful_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bQuerySuccessful_MetaData), NewProp_bQuerySuccessful_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSpatialQueryResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_FoundActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_FoundActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ActorIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ActorIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_TotalResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_QueryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_bQuerySuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronSpatialQueryResult",
	Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::PropPointers),
	sizeof(FAuracronSpatialQueryResult),
	alignof(FAuracronSpatialQueryResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSpatialQueryResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSpatialQueryResult *****************************************

// ********** Begin ScriptStruct FAuracronActorStatistics ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronActorStatistics;
class UScriptStruct* FAuracronActorStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronActorStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronActorStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Actor Statistics\n * Performance and usage statistics for actor management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor Statistics\nPerformance and usage statistics for actor management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalActors_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedActors_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingActors_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveActors_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageActorLoadTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialQueries_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageQueryTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingActors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveActors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageActorLoadTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SpatialQueries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageQueryTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActorEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronActorStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_TotalActors = { "TotalActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, TotalActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalActors_MetaData), NewProp_TotalActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_LoadedActors = { "LoadedActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, LoadedActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedActors_MetaData), NewProp_LoadedActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_StreamingActors = { "StreamingActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, StreamingActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingActors_MetaData), NewProp_StreamingActors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_ActiveActors = { "ActiveActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, ActiveActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveActors_MetaData), NewProp_ActiveActors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_AverageActorLoadTime = { "AverageActorLoadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, AverageActorLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageActorLoadTime_MetaData), NewProp_AverageActorLoadTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_SpatialQueries = { "SpatialQueries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, SpatialQueries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialQueries_MetaData), NewProp_SpatialQueries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_AverageQueryTime = { "AverageQueryTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, AverageQueryTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageQueryTime_MetaData), NewProp_AverageQueryTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_ActorEfficiency = { "ActorEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, ActorEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorEfficiency_MetaData), NewProp_ActorEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronActorStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_TotalActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_LoadedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_StreamingActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_ActiveActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_AverageActorLoadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_SpatialQueries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_AverageQueryTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_ActorEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronActorStatistics",
	Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::PropPointers),
	sizeof(FAuracronActorStatistics),
	alignof(FAuracronActorStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronActorStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronActorStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronActorStatistics ********************************************

// ********** Begin Delegate FOnActorPlaced ********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics
{
	struct AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms
	{
		FString ActorId;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "OnActorPlaced__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionActorManager::FOnActorPlaced_DelegateWrapper(const FMulticastScriptDelegate& OnActorPlaced, const FString& ActorId, FVector Location)
{
	struct AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms
	{
		FString ActorId;
		FVector Location;
	};
	AuracronWorldPartitionActorManager_eventOnActorPlaced_Parms Parms;
	Parms.ActorId=ActorId;
	Parms.Location=Location;
	OnActorPlaced.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorPlaced **********************************************************

// ********** Begin Delegate FOnActorRemoved *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics
{
	struct AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms
	{
		FString ActorId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::NewProp_ActorId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "OnActorRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionActorManager::FOnActorRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorRemoved, const FString& ActorId)
{
	struct AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms
	{
		FString ActorId;
	};
	AuracronWorldPartitionActorManager_eventOnActorRemoved_Parms Parms;
	Parms.ActorId=ActorId;
	OnActorRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorRemoved *********************************************************

// ********** Begin Delegate FOnActorMoved *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics
{
	struct AuracronWorldPartitionActorManager_eventOnActorMoved_Parms
	{
		FString ActorId;
		FVector NewLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorMoved_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::NewProp_NewLocation = { "NewLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorMoved_Parms, NewLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::NewProp_NewLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "OnActorMoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorMoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorMoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionActorManager::FOnActorMoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorMoved, const FString& ActorId, FVector NewLocation)
{
	struct AuracronWorldPartitionActorManager_eventOnActorMoved_Parms
	{
		FString ActorId;
		FVector NewLocation;
	};
	AuracronWorldPartitionActorManager_eventOnActorMoved_Parms Parms;
	Parms.ActorId=ActorId;
	Parms.NewLocation=NewLocation;
	OnActorMoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorMoved ***********************************************************

// ********** Begin Delegate FOnActorStreamingStateChanged *****************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms
	{
		FString ActorId;
		EAuracronActorStreamingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms, ActorId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState, METADATA_PARAMS(0, nullptr) }; // 3750643624
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "OnActorStreamingStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionActorManager::FOnActorStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnActorStreamingStateChanged, const FString& ActorId, EAuracronActorStreamingState NewState)
{
	struct AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms
	{
		FString ActorId;
		EAuracronActorStreamingState NewState;
	};
	AuracronWorldPartitionActorManager_eventOnActorStreamingStateChanged_Parms Parms;
	Parms.ActorId=ActorId;
	Parms.NewState=NewState;
	OnActorStreamingStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorStreamingStateChanged *******************************************

// ********** Begin Class UAuracronWorldPartitionActorManager Function AddActorReference ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics
{
	struct AuracronWorldPartitionActorManager_eventAddActorReference_Parms
	{
		FString ActorId;
		FString ReferencedActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cross-cell references\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cross-cell references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencedActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReferencedActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventAddActorReference_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReferencedActorId = { "ReferencedActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventAddActorReference_Parms, ReferencedActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencedActorId_MetaData), NewProp_ReferencedActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventAddActorReference_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventAddActorReference_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReferencedActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "AddActorReference", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::AuracronWorldPartitionActorManager_eventAddActorReference_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::AuracronWorldPartitionActorManager_eventAddActorReference_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execAddActorReference)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ReferencedActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddActorReference(Z_Param_ActorId,Z_Param_ReferencedActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function AddActorReference *************

// ********** Begin Class UAuracronWorldPartitionActorManager Function DoesActorExist **************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics
{
	struct AuracronWorldPartitionActorManager_eventDoesActorExist_Parms
	{
		FString ActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventDoesActorExist_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventDoesActorExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventDoesActorExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "DoesActorExist", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::AuracronWorldPartitionActorManager_eventDoesActorExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::AuracronWorldPartitionActorManager_eventDoesActorExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execDoesActorExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesActorExist(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function DoesActorExist ****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function DrawDebugActorInfo **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics
{
	struct AuracronWorldPartitionActorManager_eventDrawDebugActorInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventDrawDebugActorInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "DrawDebugActorInfo", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::AuracronWorldPartitionActorManager_eventDrawDebugActorInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::AuracronWorldPartitionActorManager_eventDrawDebugActorInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execDrawDebugActorInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugActorInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function DrawDebugActorInfo ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function EnableActorDebug ************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics
{
	struct AuracronWorldPartitionActorManager_eventEnableActorDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventEnableActorDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventEnableActorDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "EnableActorDebug", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::AuracronWorldPartitionActorManager_eventEnableActorDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::AuracronWorldPartitionActorManager_eventEnableActorDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execEnableActorDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableActorDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function EnableActorDebug **************

// ********** Begin Class UAuracronWorldPartitionActorManager Function ExecuteSpatialQuery *********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics
{
	struct AuracronWorldPartitionActorManager_eventExecuteSpatialQuery_Parms
	{
		FAuracronSpatialQueryParameters QueryParams;
		FAuracronSpatialQueryResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QueryParams_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_QueryParams;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::NewProp_QueryParams = { "QueryParams", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventExecuteSpatialQuery_Parms, QueryParams), Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QueryParams_MetaData), NewProp_QueryParams_MetaData) }; // 3110690507
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventExecuteSpatialQuery_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSpatialQueryResult, METADATA_PARAMS(0, nullptr) }; // 3660514229
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::NewProp_QueryParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "ExecuteSpatialQuery", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::AuracronWorldPartitionActorManager_eventExecuteSpatialQuery_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::AuracronWorldPartitionActorManager_eventExecuteSpatialQuery_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execExecuteSpatialQuery)
{
	P_GET_STRUCT_REF(FAuracronSpatialQueryParameters,Z_Param_Out_QueryParams);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSpatialQueryResult*)Z_Param__Result=P_THIS->ExecuteSpatialQuery(Z_Param_Out_QueryParams);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function ExecuteSpatialQuery ***********

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorCell ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorCell_Parms
	{
		FString ActorId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorCell_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorCell_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorCell", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::AuracronWorldPartitionActorManager_eventGetActorCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::AuracronWorldPartitionActorManager_eventGetActorCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetActorCell(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorCell ******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorDescriptor **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorDescriptor_Parms
	{
		FString ActorId;
		FAuracronActorDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor queries and information\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor queries and information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorDescriptor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::AuracronWorldPartitionActorManager_eventGetActorDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::AuracronWorldPartitionActorManager_eventGetActorDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronActorDescriptor*)Z_Param__Result=P_THIS->GetActorDescriptor(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorDescriptor ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorIds *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorIds", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::AuracronWorldPartitionActorManager_eventGetActorIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::AuracronWorldPartitionActorManager_eventGetActorIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorIds *******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorLifecycleState ******
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorLifecycleState_Parms
	{
		FString ActorId;
		EAuracronActorLifecycleState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorLifecycleState_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorLifecycleState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState, METADATA_PARAMS(0, nullptr) }; // 3675356152
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorLifecycleState", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::AuracronWorldPartitionActorManager_eventGetActorLifecycleState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::AuracronWorldPartitionActorManager_eventGetActorLifecycleState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorLifecycleState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronActorLifecycleState*)Z_Param__Result=P_THIS->GetActorLifecycleState(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorLifecycleState ********

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorReference ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorReference_Parms
	{
		FString ActorId;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReference_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReference_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorReference", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::AuracronWorldPartitionActorManager_eventGetActorReference_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::AuracronWorldPartitionActorManager_eventGetActorReference_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorReference)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->GetActorReference(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorReference *************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorReferencedBy ********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorReferencedBy_Parms
	{
		FString ActorId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReferencedBy_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReferencedBy_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorReferencedBy", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::AuracronWorldPartitionActorManager_eventGetActorReferencedBy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::AuracronWorldPartitionActorManager_eventGetActorReferencedBy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorReferencedBy)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorReferencedBy(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorReferencedBy **********

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorReferences **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorReferences_Parms
	{
		FString ActorId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReferences_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorReferences_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorReferences", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::AuracronWorldPartitionActorManager_eventGetActorReferences_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::AuracronWorldPartitionActorManager_eventGetActorReferences_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorReferences)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorReferences(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorReferences ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorsByClass ************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorsByClass_Parms
	{
		FString ActorClass;
		TArray<FAuracronActorDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsByClass_Parms, ActorClass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsByClass_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorsByClass", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::AuracronWorldPartitionActorManager_eventGetActorsByClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::AuracronWorldPartitionActorManager_eventGetActorsByClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorsByClass)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronActorDescriptor>*)Z_Param__Result=P_THIS->GetActorsByClass(Z_Param_ActorClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorsByClass **************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorsByTag **************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorsByTag_Parms
	{
		FString Tag;
		TArray<FAuracronActorDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsByTag_Parms, Tag), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorsByTag", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::AuracronWorldPartitionActorManager_eventGetActorsByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::AuracronWorldPartitionActorManager_eventGetActorsByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorsByTag)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronActorDescriptor>*)Z_Param__Result=P_THIS->GetActorsByTag(Z_Param_Tag);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorsByTag ****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorsInBox **************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorsInBox_Parms
	{
		FBox Box;
		TArray<FAuracronActorDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Box_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Box;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_Box = { "Box", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInBox_Parms, Box), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Box_MetaData), NewProp_Box_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_Box,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorsInBox", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::AuracronWorldPartitionActorManager_eventGetActorsInBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::AuracronWorldPartitionActorManager_eventGetActorsInBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorsInBox)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Box);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronActorDescriptor>*)Z_Param__Result=P_THIS->GetActorsInBox(Z_Param_Out_Box);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorsInBox ****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorsInCell *************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorsInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorsInCell", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::AuracronWorldPartitionActorManager_eventGetActorsInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::AuracronWorldPartitionActorManager_eventGetActorsInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorsInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActorsInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorsInCell ***************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorsInRadius ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronActorDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorsInRadius", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::AuracronWorldPartitionActorManager_eventGetActorsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronActorDescriptor>*)Z_Param__Result=P_THIS->GetActorsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorsInRadius *************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorStatistics **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorStatistics_Parms
	{
		FAuracronActorStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronActorStatistics, METADATA_PARAMS(0, nullptr) }; // 2598508776
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorStatistics", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::AuracronWorldPartitionActorManager_eventGetActorStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::AuracronWorldPartitionActorManager_eventGetActorStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronActorStatistics*)Z_Param__Result=P_THIS->GetActorStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorStatistics ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetActorStreamingState ******
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetActorStreamingState_Parms
	{
		FString ActorId;
		EAuracronActorStreamingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorStreamingState_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetActorStreamingState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorStreamingState, METADATA_PARAMS(0, nullptr) }; // 3750643624
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetActorStreamingState", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::AuracronWorldPartitionActorManager_eventGetActorStreamingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::AuracronWorldPartitionActorManager_eventGetActorStreamingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetActorStreamingState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronActorStreamingState*)Z_Param__Result=P_THIS->GetActorStreamingState(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetActorStreamingState ********

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetAllActors ****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetAllActors_Parms
	{
		TArray<FAuracronActorDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronActorDescriptor, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetAllActors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 486931340
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetAllActors", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::AuracronWorldPartitionActorManager_eventGetAllActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::AuracronWorldPartitionActorManager_eventGetAllActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetAllActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronActorDescriptor>*)Z_Param__Result=P_THIS->GetAllActors();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetAllActors ******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetConfiguration_Parms
	{
		FAuracronActorManagementConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration, METADATA_PARAMS(0, nullptr) }; // 3723996950
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::AuracronWorldPartitionActorManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::AuracronWorldPartitionActorManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronActorManagementConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetInstance *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionActorManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::AuracronWorldPartitionActorManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::AuracronWorldPartitionActorManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionActorManager**)Z_Param__Result=UAuracronWorldPartitionActorManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetInstance *******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetLoadedActorCount *********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetLoadedActorCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetLoadedActorCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetLoadedActorCount", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::AuracronWorldPartitionActorManager_eventGetLoadedActorCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::AuracronWorldPartitionActorManager_eventGetLoadedActorCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetLoadedActorCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedActorCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetLoadedActorCount ***********

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetLoadedActors *************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetLoadedActors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetLoadedActors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetLoadedActors", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::AuracronWorldPartitionActorManager_eventGetLoadedActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::AuracronWorldPartitionActorManager_eventGetLoadedActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetLoadedActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedActors();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetLoadedActors ***************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetStreamingActors **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetStreamingActors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetStreamingActors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetStreamingActors", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::AuracronWorldPartitionActorManager_eventGetStreamingActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::AuracronWorldPartitionActorManager_eventGetStreamingActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetStreamingActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingActors();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetStreamingActors ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetTotalActorCount **********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetTotalActorCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetTotalActorCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetTotalActorCount", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::AuracronWorldPartitionActorManager_eventGetTotalActorCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::AuracronWorldPartitionActorManager_eventGetTotalActorCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetTotalActorCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalActorCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetTotalActorCount ************

// ********** Begin Class UAuracronWorldPartitionActorManager Function GetTotalMemoryUsage *********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionActorManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionActorManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionActorManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function GetTotalMemoryUsage ***********

// ********** Begin Class UAuracronWorldPartitionActorManager Function Initialize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics
{
	struct AuracronWorldPartitionActorManager_eventInitialize_Parms
	{
		FAuracronActorManagementConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3723996950
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::AuracronWorldPartitionActorManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::AuracronWorldPartitionActorManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronActorManagementConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function Initialize ********************

// ********** Begin Class UAuracronWorldPartitionActorManager Function IsActorDebugEnabled *********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics
{
	struct AuracronWorldPartitionActorManager_eventIsActorDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventIsActorDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventIsActorDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "IsActorDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::AuracronWorldPartitionActorManager_eventIsActorDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::AuracronWorldPartitionActorManager_eventIsActorDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execIsActorDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActorDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function IsActorDebugEnabled ***********

// ********** Begin Class UAuracronWorldPartitionActorManager Function IsInitialized ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionActorManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::AuracronWorldPartitionActorManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::AuracronWorldPartitionActorManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function IsInitialized *****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function LoadActor *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics
{
	struct AuracronWorldPartitionActorManager_eventLoadActor_Parms
	{
		FString ActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventLoadActor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventLoadActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventLoadActor_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "LoadActor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::AuracronWorldPartitionActorManager_eventLoadActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::AuracronWorldPartitionActorManager_eventLoadActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execLoadActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadActor(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function LoadActor *********************

// ********** Begin Class UAuracronWorldPartitionActorManager Function LogActorState ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "LogActorState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execLogActorState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogActorState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function LogActorState *****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function MoveActor *******************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics
{
	struct AuracronWorldPartitionActorManager_eventMoveActor_Parms
	{
		FString ActorId;
		FVector NewLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventMoveActor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_NewLocation = { "NewLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventMoveActor_Parms, NewLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewLocation_MetaData), NewProp_NewLocation_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventMoveActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventMoveActor_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_NewLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "MoveActor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::AuracronWorldPartitionActorManager_eventMoveActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::AuracronWorldPartitionActorManager_eventMoveActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execMoveActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveActor(Z_Param_ActorId,Z_Param_Out_NewLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function MoveActor *********************

// ********** Begin Class UAuracronWorldPartitionActorManager Function MoveActorToCell *************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics
{
	struct AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms
	{
		FString ActorId;
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "MoveActorToCell", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::AuracronWorldPartitionActorManager_eventMoveActorToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execMoveActorToCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveActorToCell(Z_Param_ActorId,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function MoveActorToCell ***************

// ********** Begin Class UAuracronWorldPartitionActorManager Function PlaceActor ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics
{
	struct AuracronWorldPartitionActorManager_eventPlaceActor_Parms
	{
		FString ActorClass;
		FVector Location;
		FRotator Rotation;
		EAuracronActorPlacementType PlacementType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor placement and creation\n" },
#endif
		{ "CPP_Default_PlacementType", "Static" },
		{ "CPP_Default_Rotation", "" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor placement and creation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlacementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlacementType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventPlaceActor_Parms, ActorClass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventPlaceActor_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventPlaceActor_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_PlacementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_PlacementType = { "PlacementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventPlaceActor_Parms, PlacementType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorPlacementType, METADATA_PARAMS(0, nullptr) }; // 3625038873
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventPlaceActor_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_PlacementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_PlacementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "PlaceActor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::AuracronWorldPartitionActorManager_eventPlaceActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::AuracronWorldPartitionActorManager_eventPlaceActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execPlaceActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorClass);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_GET_ENUM(EAuracronActorPlacementType,Z_Param_PlacementType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->PlaceActor(Z_Param_ActorClass,Z_Param_Out_Location,Z_Param_Out_Rotation,EAuracronActorPlacementType(Z_Param_PlacementType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function PlaceActor ********************

// ********** Begin Class UAuracronWorldPartitionActorManager Function RemoveActor *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics
{
	struct AuracronWorldPartitionActorManager_eventRemoveActor_Parms
	{
		FString ActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventRemoveActor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventRemoveActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventRemoveActor_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "RemoveActor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::AuracronWorldPartitionActorManager_eventRemoveActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::AuracronWorldPartitionActorManager_eventRemoveActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execRemoveActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveActor(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function RemoveActor *******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function RemoveActorReference ********
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics
{
	struct AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms
	{
		FString ActorId;
		FString ReferencedActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencedActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReferencedActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReferencedActorId = { "ReferencedActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms, ReferencedActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencedActorId_MetaData), NewProp_ReferencedActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReferencedActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "RemoveActorReference", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::AuracronWorldPartitionActorManager_eventRemoveActorReference_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execRemoveActorReference)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ReferencedActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveActorReference(Z_Param_ActorId,Z_Param_ReferencedActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function RemoveActorReference **********

// ********** Begin Class UAuracronWorldPartitionActorManager Function ResetStatistics *************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function ResetStatistics ***************

// ********** Begin Class UAuracronWorldPartitionActorManager Function SetActorLifecycleState ******
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics
{
	struct AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms
	{
		FString ActorId;
		EAuracronActorLifecycleState NewState;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms, NewState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronActorLifecycleState, METADATA_PARAMS(0, nullptr) }; // 3675356152
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_NewState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "SetActorLifecycleState", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::AuracronWorldPartitionActorManager_eventSetActorLifecycleState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execSetActorLifecycleState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_ENUM(EAuracronActorLifecycleState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetActorLifecycleState(Z_Param_ActorId,EAuracronActorLifecycleState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function SetActorLifecycleState ********

// ********** Begin Class UAuracronWorldPartitionActorManager Function SetActorRotation ************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics
{
	struct AuracronWorldPartitionActorManager_eventSetActorRotation_Parms
	{
		FString ActorId;
		FRotator NewRotation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewRotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewRotation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorRotation_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_NewRotation = { "NewRotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorRotation_Parms, NewRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewRotation_MetaData), NewProp_NewRotation_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventSetActorRotation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventSetActorRotation_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_NewRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "SetActorRotation", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::AuracronWorldPartitionActorManager_eventSetActorRotation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::AuracronWorldPartitionActorManager_eventSetActorRotation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execSetActorRotation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_NewRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetActorRotation(Z_Param_ActorId,Z_Param_Out_NewRotation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function SetActorRotation **************

// ********** Begin Class UAuracronWorldPartitionActorManager Function SetActorScale ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics
{
	struct AuracronWorldPartitionActorManager_eventSetActorScale_Parms
	{
		FString ActorId;
		FVector NewScale;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewScale_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewScale;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorScale_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_NewScale = { "NewScale", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetActorScale_Parms, NewScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewScale_MetaData), NewProp_NewScale_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventSetActorScale_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventSetActorScale_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_NewScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "SetActorScale", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::AuracronWorldPartitionActorManager_eventSetActorScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::AuracronWorldPartitionActorManager_eventSetActorScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execSetActorScale)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetActorScale(Z_Param_ActorId,Z_Param_Out_NewScale);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function SetActorScale *****************

// ********** Begin Class UAuracronWorldPartitionActorManager Function SetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionActorManager_eventSetConfiguration_Parms
	{
		FAuracronActorManagementConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3723996950
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::AuracronWorldPartitionActorManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::AuracronWorldPartitionActorManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronActorManagementConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function SetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionActorManager Function Shutdown ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function Shutdown **********************

// ********** Begin Class UAuracronWorldPartitionActorManager Function Tick ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics
{
	struct AuracronWorldPartitionActorManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::AuracronWorldPartitionActorManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::AuracronWorldPartitionActorManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function Tick **************************

// ********** Begin Class UAuracronWorldPartitionActorManager Function UnloadActor *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics
{
	struct AuracronWorldPartitionActorManager_eventUnloadActor_Parms
	{
		FString ActorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ActorId = { "ActorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionActorManager_eventUnloadActor_Parms, ActorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorId_MetaData), NewProp_ActorId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionActorManager_eventUnloadActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionActorManager_eventUnloadActor_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ActorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "UnloadActor", Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::AuracronWorldPartitionActorManager_eventUnloadActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::AuracronWorldPartitionActorManager_eventUnloadActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execUnloadActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadActor(Z_Param_ActorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function UnloadActor *******************

// ********** Begin Class UAuracronWorldPartitionActorManager Function UpdateActorLifecycles *******
struct Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Actor Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionActorManager, nullptr, "UpdateActorLifecycles", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionActorManager::execUpdateActorLifecycles)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateActorLifecycles();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionActorManager Function UpdateActorLifecycles *********

// ********** Begin Class UAuracronWorldPartitionActorManager **************************************
void UAuracronWorldPartitionActorManager::StaticRegisterNativesUAuracronWorldPartitionActorManager()
{
	UClass* Class = UAuracronWorldPartitionActorManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddActorReference", &UAuracronWorldPartitionActorManager::execAddActorReference },
		{ "DoesActorExist", &UAuracronWorldPartitionActorManager::execDoesActorExist },
		{ "DrawDebugActorInfo", &UAuracronWorldPartitionActorManager::execDrawDebugActorInfo },
		{ "EnableActorDebug", &UAuracronWorldPartitionActorManager::execEnableActorDebug },
		{ "ExecuteSpatialQuery", &UAuracronWorldPartitionActorManager::execExecuteSpatialQuery },
		{ "GetActorCell", &UAuracronWorldPartitionActorManager::execGetActorCell },
		{ "GetActorDescriptor", &UAuracronWorldPartitionActorManager::execGetActorDescriptor },
		{ "GetActorIds", &UAuracronWorldPartitionActorManager::execGetActorIds },
		{ "GetActorLifecycleState", &UAuracronWorldPartitionActorManager::execGetActorLifecycleState },
		{ "GetActorReference", &UAuracronWorldPartitionActorManager::execGetActorReference },
		{ "GetActorReferencedBy", &UAuracronWorldPartitionActorManager::execGetActorReferencedBy },
		{ "GetActorReferences", &UAuracronWorldPartitionActorManager::execGetActorReferences },
		{ "GetActorsByClass", &UAuracronWorldPartitionActorManager::execGetActorsByClass },
		{ "GetActorsByTag", &UAuracronWorldPartitionActorManager::execGetActorsByTag },
		{ "GetActorsInBox", &UAuracronWorldPartitionActorManager::execGetActorsInBox },
		{ "GetActorsInCell", &UAuracronWorldPartitionActorManager::execGetActorsInCell },
		{ "GetActorsInRadius", &UAuracronWorldPartitionActorManager::execGetActorsInRadius },
		{ "GetActorStatistics", &UAuracronWorldPartitionActorManager::execGetActorStatistics },
		{ "GetActorStreamingState", &UAuracronWorldPartitionActorManager::execGetActorStreamingState },
		{ "GetAllActors", &UAuracronWorldPartitionActorManager::execGetAllActors },
		{ "GetConfiguration", &UAuracronWorldPartitionActorManager::execGetConfiguration },
		{ "GetInstance", &UAuracronWorldPartitionActorManager::execGetInstance },
		{ "GetLoadedActorCount", &UAuracronWorldPartitionActorManager::execGetLoadedActorCount },
		{ "GetLoadedActors", &UAuracronWorldPartitionActorManager::execGetLoadedActors },
		{ "GetStreamingActors", &UAuracronWorldPartitionActorManager::execGetStreamingActors },
		{ "GetTotalActorCount", &UAuracronWorldPartitionActorManager::execGetTotalActorCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionActorManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionActorManager::execInitialize },
		{ "IsActorDebugEnabled", &UAuracronWorldPartitionActorManager::execIsActorDebugEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionActorManager::execIsInitialized },
		{ "LoadActor", &UAuracronWorldPartitionActorManager::execLoadActor },
		{ "LogActorState", &UAuracronWorldPartitionActorManager::execLogActorState },
		{ "MoveActor", &UAuracronWorldPartitionActorManager::execMoveActor },
		{ "MoveActorToCell", &UAuracronWorldPartitionActorManager::execMoveActorToCell },
		{ "PlaceActor", &UAuracronWorldPartitionActorManager::execPlaceActor },
		{ "RemoveActor", &UAuracronWorldPartitionActorManager::execRemoveActor },
		{ "RemoveActorReference", &UAuracronWorldPartitionActorManager::execRemoveActorReference },
		{ "ResetStatistics", &UAuracronWorldPartitionActorManager::execResetStatistics },
		{ "SetActorLifecycleState", &UAuracronWorldPartitionActorManager::execSetActorLifecycleState },
		{ "SetActorRotation", &UAuracronWorldPartitionActorManager::execSetActorRotation },
		{ "SetActorScale", &UAuracronWorldPartitionActorManager::execSetActorScale },
		{ "SetConfiguration", &UAuracronWorldPartitionActorManager::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionActorManager::execShutdown },
		{ "Tick", &UAuracronWorldPartitionActorManager::execTick },
		{ "UnloadActor", &UAuracronWorldPartitionActorManager::execUnloadActor },
		{ "UpdateActorLifecycles", &UAuracronWorldPartitionActorManager::execUpdateActorLifecycles },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager;
UClass* UAuracronWorldPartitionActorManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionActorManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionActorManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionActorManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister()
{
	return UAuracronWorldPartitionActorManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Actor Manager\n * Central manager for actor management in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionActorManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Actor Manager\nCentral manager for actor management in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorPlaced_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorMoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorStreamingStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionActorManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorPlaced;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorMoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorStreamingStateChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_AddActorReference, "AddActorReference" }, // 449775015
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DoesActorExist, "DoesActorExist" }, // 3855944807
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_DrawDebugActorInfo, "DrawDebugActorInfo" }, // 4238987085
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_EnableActorDebug, "EnableActorDebug" }, // 211511595
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ExecuteSpatialQuery, "ExecuteSpatialQuery" }, // 823234919
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorCell, "GetActorCell" }, // 360102935
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorDescriptor, "GetActorDescriptor" }, // 2303235639
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorIds, "GetActorIds" }, // 702061680
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorLifecycleState, "GetActorLifecycleState" }, // 717404466
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReference, "GetActorReference" }, // 3374011063
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferencedBy, "GetActorReferencedBy" }, // 3828350316
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorReferences, "GetActorReferences" }, // 4045451880
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByClass, "GetActorsByClass" }, // 2488438682
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsByTag, "GetActorsByTag" }, // 1523059297
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInBox, "GetActorsInBox" }, // 1677197118
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInCell, "GetActorsInCell" }, // 4251248081
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorsInRadius, "GetActorsInRadius" }, // 1007421395
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStatistics, "GetActorStatistics" }, // 2566836487
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetActorStreamingState, "GetActorStreamingState" }, // 2231707051
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetAllActors, "GetAllActors" }, // 802226813
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetConfiguration, "GetConfiguration" }, // 3454835868
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetInstance, "GetInstance" }, // 2900213212
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActorCount, "GetLoadedActorCount" }, // 3240824004
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetLoadedActors, "GetLoadedActors" }, // 3665158336
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetStreamingActors, "GetStreamingActors" }, // 3811079879
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalActorCount, "GetTotalActorCount" }, // 1072494267
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 3533854183
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Initialize, "Initialize" }, // 4228884508
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsActorDebugEnabled, "IsActorDebugEnabled" }, // 1029348455
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_IsInitialized, "IsInitialized" }, // 3375595462
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LoadActor, "LoadActor" }, // 2322702749
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_LogActorState, "LogActorState" }, // 4253019934
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActor, "MoveActor" }, // 2912385147
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_MoveActorToCell, "MoveActorToCell" }, // 2091208131
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature, "OnActorMoved__DelegateSignature" }, // 1865296567
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature, "OnActorPlaced__DelegateSignature" }, // 692176059
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature, "OnActorRemoved__DelegateSignature" }, // 1201069720
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature, "OnActorStreamingStateChanged__DelegateSignature" }, // 2055015440
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_PlaceActor, "PlaceActor" }, // 3613314760
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActor, "RemoveActor" }, // 441254582
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_RemoveActorReference, "RemoveActorReference" }, // 2164363893
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_ResetStatistics, "ResetStatistics" }, // 2355735294
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorLifecycleState, "SetActorLifecycleState" }, // 512353792
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorRotation, "SetActorRotation" }, // 376639854
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetActorScale, "SetActorScale" }, // 916822743
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_SetConfiguration, "SetConfiguration" }, // 2828953947
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Shutdown, "Shutdown" }, // 4025495694
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_Tick, "Tick" }, // 1793795400
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UnloadActor, "UnloadActor" }, // 4011778576
		{ &Z_Construct_UFunction_UAuracronWorldPartitionActorManager_UpdateActorLifecycles, "UpdateActorLifecycles" }, // 3270742160
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionActorManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorPlaced = { "OnActorPlaced", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, OnActorPlaced), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorPlaced__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorPlaced_MetaData), NewProp_OnActorPlaced_MetaData) }; // 692176059
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorRemoved = { "OnActorRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, OnActorRemoved), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorRemoved_MetaData), NewProp_OnActorRemoved_MetaData) }; // 1201069720
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorMoved = { "OnActorMoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, OnActorMoved), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorMoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorMoved_MetaData), NewProp_OnActorMoved_MetaData) }; // 1865296567
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorStreamingStateChanged = { "OnActorStreamingStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, OnActorStreamingStateChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionActorManager_OnActorStreamingStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorStreamingStateChanged_MetaData), NewProp_OnActorStreamingStateChanged_MetaData) }; // 2055015440
void Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionActorManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionActorManager), &Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, Configuration), Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3723996950
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionActorManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorPlaced,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorMoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_OnActorStreamingStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::ClassParams = {
	&UAuracronWorldPartitionActorManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager.OuterSingleton;
}
UAuracronWorldPartitionActorManager::UAuracronWorldPartitionActorManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionActorManager);
UAuracronWorldPartitionActorManager::~UAuracronWorldPartitionActorManager() {}
// ********** End Class UAuracronWorldPartitionActorManager ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronActorStreamingState_StaticEnum, TEXT("EAuracronActorStreamingState"), &Z_Registration_Info_UEnum_EAuracronActorStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3750643624U) },
		{ EAuracronActorPlacementType_StaticEnum, TEXT("EAuracronActorPlacementType"), &Z_Registration_Info_UEnum_EAuracronActorPlacementType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3625038873U) },
		{ EAuracronSpatialQueryType_StaticEnum, TEXT("EAuracronSpatialQueryType"), &Z_Registration_Info_UEnum_EAuracronSpatialQueryType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 89800959U) },
		{ EAuracronActorLifecycleState_StaticEnum, TEXT("EAuracronActorLifecycleState"), &Z_Registration_Info_UEnum_EAuracronActorLifecycleState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3675356152U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronActorManagementConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics::NewStructOps, TEXT("AuracronActorManagementConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronActorManagementConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronActorManagementConfiguration), 3723996950U) },
		{ FAuracronActorDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics::NewStructOps, TEXT("AuracronActorDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronActorDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronActorDescriptor), 486931340U) },
		{ FAuracronSpatialQueryParameters::StaticStruct, Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics::NewStructOps, TEXT("AuracronSpatialQueryParameters"), &Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryParameters, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSpatialQueryParameters), 3110690507U) },
		{ FAuracronSpatialQueryResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics::NewStructOps, TEXT("AuracronSpatialQueryResult"), &Z_Registration_Info_UScriptStruct_FAuracronSpatialQueryResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSpatialQueryResult), 3660514229U) },
		{ FAuracronActorStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics::NewStructOps, TEXT("AuracronActorStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronActorStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronActorStatistics), 2598508776U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionActorManager, UAuracronWorldPartitionActorManager::StaticClass, TEXT("UAuracronWorldPartitionActorManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionActorManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionActorManager), 936536597U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_625700606(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
