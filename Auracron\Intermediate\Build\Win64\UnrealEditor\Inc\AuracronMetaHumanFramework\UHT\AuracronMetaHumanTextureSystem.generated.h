// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanTextureSystem.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanTextureSystem_generated_h
#error "AuracronMetaHumanTextureSystem.generated.h already included, missing '#pragma once' in AuracronMetaHumanTextureSystem.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanTextureSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronMetaHumanFramework;
class UMaterialInstanceDynamic;
class UTexture2D;
class UTexture;
class UTextureRenderTarget2D;
enum class EAuracronMetaHumanTextureType : uint8;
struct FAuracronSkinTextureConfig;
struct FAuracronTextureParams;
struct FAuracronTextureResult;

// ********** Begin ScriptStruct FAuracronTextureParams ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_76_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTextureParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTextureParams;
// ********** End ScriptStruct FAuracronTextureParams **********************************************

// ********** Begin ScriptStruct FAuracronTextureResult ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_126_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTextureResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTextureResult;
// ********** End ScriptStruct FAuracronTextureResult **********************************************

// ********** Begin ScriptStruct FAuracronSkinTextureConfig ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_170_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSkinTextureConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSkinTextureConfig;
// ********** End ScriptStruct FAuracronSkinTextureConfig ******************************************

// ********** Begin Delegate FAuracronTextureComplete **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_210_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronTextureComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronTextureComplete, FAuracronTextureResult const& Result, const FString& OperationID);


// ********** End Delegate FAuracronTextureComplete ************************************************

// ********** Begin Class UAuracronMetaHumanTextureSystem ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSupportedTextureFormats); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execCreateRenderTargetTexture); \
	DECLARE_FUNCTION(execUpdateDynamicTextureParameter); \
	DECLARE_FUNCTION(execUpdateDynamicMaterialParameter); \
	DECLARE_FUNCTION(execValidateMetaHumanTexture); \
	DECLARE_FUNCTION(execGetTextureStatistics); \
	DECLARE_FUNCTION(execAnalyzeTextureQuality); \
	DECLARE_FUNCTION(execApplyTextureFilterGPU); \
	DECLARE_FUNCTION(execBlendTexturesGPU); \
	DECLARE_FUNCTION(execProcessTextureWithGPU); \
	DECLARE_FUNCTION(execGetAsyncOperationResult); \
	DECLARE_FUNCTION(execIsAsyncOperationComplete); \
	DECLARE_FUNCTION(execGenerateSkinTextureAsync); \
	DECLARE_FUNCTION(execGenerateTextureAsync); \
	DECLARE_FUNCTION(execConvertTextureFormat); \
	DECLARE_FUNCTION(execOptimizeTexture); \
	DECLARE_FUNCTION(execGenerateSkinTexture); \
	DECLARE_FUNCTION(execGenerateTexture);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanTextureSystem(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanTextureSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanTextureSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanTextureSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanTextureSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanTextureSystem(UAuracronMetaHumanTextureSystem&&) = delete; \
	UAuracronMetaHumanTextureSystem(const UAuracronMetaHumanTextureSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanTextureSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanTextureSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanTextureSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_216_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h_219_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanTextureSystem;

// ********** End Class UAuracronMetaHumanTextureSystem ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanTextureSystem_h

// ********** Begin Enum EAuracronMetaHumanTextureType *********************************************
#define FOREACH_ENUM_EAURACRONMETAHUMANTEXTURETYPE(op) \
	op(EAuracronMetaHumanTextureType::Diffuse) \
	op(EAuracronMetaHumanTextureType::Normal) \
	op(EAuracronMetaHumanTextureType::Roughness) \
	op(EAuracronMetaHumanTextureType::Metallic) \
	op(EAuracronMetaHumanTextureType::Specular) \
	op(EAuracronMetaHumanTextureType::Emissive) \
	op(EAuracronMetaHumanTextureType::Opacity) \
	op(EAuracronMetaHumanTextureType::SubsurfaceColor) \
	op(EAuracronMetaHumanTextureType::SubsurfaceProfile) \
	op(EAuracronMetaHumanTextureType::Displacement) \
	op(EAuracronMetaHumanTextureType::AmbientOcclusion) \
	op(EAuracronMetaHumanTextureType::Cavity) \
	op(EAuracronMetaHumanTextureType::Curvature) \
	op(EAuracronMetaHumanTextureType::Thickness) \
	op(EAuracronMetaHumanTextureType::WorldPositionOffset) \
	op(EAuracronMetaHumanTextureType::Custom) 

enum class EAuracronMetaHumanTextureType : uint8;
template<> struct TIsUEnumClass<EAuracronMetaHumanTextureType> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronMetaHumanTextureType>();
// ********** End Enum EAuracronMetaHumanTextureType ***********************************************

// ********** Begin Enum EAuracronTextureQuality ***************************************************
#define FOREACH_ENUM_EAURACRONTEXTUREQUALITY(op) \
	op(EAuracronTextureQuality::Low) \
	op(EAuracronTextureQuality::Medium) \
	op(EAuracronTextureQuality::High) \
	op(EAuracronTextureQuality::Ultra) \
	op(EAuracronTextureQuality::Custom) 

enum class EAuracronTextureQuality : uint8;
template<> struct TIsUEnumClass<EAuracronTextureQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronTextureQuality>();
// ********** End Enum EAuracronTextureQuality *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
