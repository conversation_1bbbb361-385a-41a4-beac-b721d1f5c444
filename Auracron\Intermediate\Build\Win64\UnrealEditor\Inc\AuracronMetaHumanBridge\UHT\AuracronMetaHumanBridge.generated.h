// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMetaHumanBridge.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronMetaHumanBridge_generated_h
#error "AuracronMetaHumanBridge.generated.h already included, missing '#pragma once' in AuracronMetaHumanBridge.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronMetaHumanBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAnimBlueprint;
class UClothingAssetBase;
class UGroomAsset;
class UGroomBindingAsset;
class UMaterialInstanceDynamic;
class UMaterialInterface;
class UMaterialParameterCollection;
class UPoseAsset;
class USkeletalMesh;
class UStaticMesh;
class UTexture2D;
enum class EAnimationBlueprintQuality : uint8;
enum class EAsyncProcessingPriority : uint8;
enum class EBatchOperationType : uint8;
enum class EBlendShapeInterpolationType : uint8;
enum class EBlendShapeValidationType : uint8;
enum class EBoneScalingType : uint8;
enum class EClothingCollisionType : uint8;
enum class EClothingQualityLevel : uint8;
enum class EDNAValidationType : uint8;
enum class EFacialExpressionType : uint8;
enum class EHairCardQuality : uint8;
enum class EJointConstraintType : uint8;
enum class EJointTransformSpace : uint8;
enum class ELipSyncType : uint8;
enum class EMemoryPoolType : uint8;
enum class EMeshValidationType : uint8;
enum class EMetaHumanDNADataLayer : uint8;
enum class ENormalRecalculationType : uint8;
enum class EPhonemeType : uint8; struct FBlendShapeWeightArray;
enum class ERigTransformationType : uint8;
enum class ERigValidationType : uint8;
enum class ETextureQuality : uint8;
enum class EUVPreservationType : uint8;
enum class EVertexManipulationType : uint8;
struct FAgingEffectData;
struct FAnimationBlueprintGenerationParameters;
struct FAnimationBlueprintLODData;
struct FAsyncProcessingConfiguration;
struct FAuracronMetaHumanMeshData;
struct FBatchProcessingConfiguration;
struct FBlendShapeInterpolationData;
struct FBlendShapeValidationResult;
struct FBlendShapeWeight;
struct FBoneScalingData;
struct FClothingFittingData;
struct FClothingGenerationParameters;
struct FClothingLODData;
struct FClothingMaskData;
struct FClothingMaterialData;
struct FClothingPhysicsData;
struct FDNAValidationResult;
struct FEmotionMappingData;
struct FErrorInfo;
struct FFacialAnimationData;
struct FFacialExpressionPreset;
struct FGPUAccelerationConfiguration;
struct FHairCardData;
struct FHairColorData;
struct FHairGenerationParameters;
struct FHairLODData;
struct FHairPhysicsData;
struct FHairStylingData;
struct FIKChainData;
struct FJointConstraint;
struct FJointTransform;
struct FJointValidationResult;
struct FLipSyncData;
struct FLODGenerationSettings;
struct FMakeupData;
struct FMemoryPoolConfiguration;
struct FMeshDeformationData;
struct FMeshValidationResult;
struct FMetaHumanBehaviorData;
struct FMetaHumanBlendShapeTarget;
struct FMetaHumanControlRigData;
struct FMetaHumanDNADescriptor;
struct FMetaHumanJointData;
struct FNoiseParameters;
struct FPerformanceMetrics;
struct FPerformanceOptimizationConfiguration;
struct FPoseGenerationData;
struct FRetargetingData;
struct FRigValidationResult;
struct FScarData;
struct FSkinVariationData;
struct FStringMapWrapper;
struct FTattooData;
struct FTextureBlendingData;
struct FTextureGenerationParameters;
struct FVertexData;

// ********** Begin ScriptStruct FFloatArrayWrapper ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_42_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFloatArrayWrapper_Statics; \
	static class UScriptStruct* StaticStruct();


struct FFloatArrayWrapper;
// ********** End ScriptStruct FFloatArrayWrapper **************************************************

// ********** Begin ScriptStruct FStringMapWrapper *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStringMapWrapper_Statics; \
	static class UScriptStruct* StaticStruct();


struct FStringMapWrapper;
// ********** End ScriptStruct FStringMapWrapper ***************************************************

// ********** Begin Delegate FOnMetaHumanDNALoaded *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_207_DELEGATE \
AURACRONMETAHUMANBRIDGE_API void FOnMetaHumanDNALoaded_DelegateWrapper(const FMulticastScriptDelegate& OnMetaHumanDNALoaded, bool bSuccess, const FString& ErrorMessage);


// ********** End Delegate FOnMetaHumanDNALoaded ***************************************************

// ********** Begin Delegate FOnMetaHumanDNAModified ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_208_DELEGATE \
AURACRONMETAHUMANBRIDGE_API void FOnMetaHumanDNAModified_DelegateWrapper(const FMulticastScriptDelegate& OnMetaHumanDNAModified, bool bSuccess, const FString& ErrorMessage);


// ********** End Delegate FOnMetaHumanDNAModified *************************************************

// ********** Begin Delegate FOnMetaHumanCharacterGenerated ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_209_DELEGATE \
AURACRONMETAHUMANBRIDGE_API void FOnMetaHumanCharacterGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnMetaHumanCharacterGenerated, bool bSuccess, const FString& ErrorMessage);


// ********** End Delegate FOnMetaHumanCharacterGenerated ******************************************

// ********** Begin Delegate FOnMetaHumanRigCreated ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_210_DELEGATE \
AURACRONMETAHUMANBRIDGE_API void FOnMetaHumanRigCreated_DelegateWrapper(const FMulticastScriptDelegate& OnMetaHumanRigCreated, bool bSuccess, const FString& ErrorMessage);


// ********** End Delegate FOnMetaHumanRigCreated **************************************************

// ********** Begin ScriptStruct FMetaHumanDNADescriptor *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_268_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMetaHumanDNADescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMetaHumanDNADescriptor;
// ********** End ScriptStruct FMetaHumanDNADescriptor *********************************************

// ********** Begin ScriptStruct FBlendShapeWeight *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_357_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBlendShapeWeight_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBlendShapeWeight;
// ********** End ScriptStruct FBlendShapeWeight ***************************************************

// ********** Begin ScriptStruct FFacialExpressionPreset *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_388_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFacialExpressionPreset_Statics; \
	static class UScriptStruct* StaticStruct();


struct FFacialExpressionPreset;
// ********** End ScriptStruct FFacialExpressionPreset *********************************************

// ********** Begin ScriptStruct FBlendShapeInterpolationData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_421_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBlendShapeInterpolationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBlendShapeInterpolationData;
// ********** End ScriptStruct FBlendShapeInterpolationData ****************************************

// ********** Begin ScriptStruct FBlendShapeValidationResult ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_450_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBlendShapeValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBlendShapeValidationResult;
// ********** End ScriptStruct FBlendShapeValidationResult *****************************************

// ********** Begin ScriptStruct FMetaHumanBlendShapeTarget ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_482_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMetaHumanBlendShapeTarget_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMetaHumanBlendShapeTarget;
// ********** End ScriptStruct FMetaHumanBlendShapeTarget ******************************************

// ********** Begin ScriptStruct FMetaHumanJointData ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_512_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMetaHumanJointData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMetaHumanJointData;
// ********** End ScriptStruct FMetaHumanJointData *************************************************

// ********** Begin ScriptStruct FAuracronMetaHumanMeshData ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_542_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMetaHumanMeshData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMetaHumanMeshData;
// ********** End ScriptStruct FAuracronMetaHumanMeshData ******************************************

// ********** Begin ScriptStruct FMetaHumanBehaviorData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_569_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMetaHumanBehaviorData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMetaHumanBehaviorData;
// ********** End ScriptStruct FMetaHumanBehaviorData **********************************************

// ********** Begin ScriptStruct FMetaHumanControlRigData ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_595_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMetaHumanControlRigData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMetaHumanControlRigData;
// ********** End ScriptStruct FMetaHumanControlRigData ********************************************

// ********** Begin ScriptStruct FJointConstraint **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_645_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FJointConstraint_Statics; \
	static class UScriptStruct* StaticStruct();


struct FJointConstraint;
// ********** End ScriptStruct FJointConstraint ****************************************************

// ********** Begin ScriptStruct FJointTransform ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_679_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FJointTransform_Statics; \
	static class UScriptStruct* StaticStruct();


struct FJointTransform;
// ********** End ScriptStruct FJointTransform *****************************************************

// ********** Begin ScriptStruct FJointValidationResult ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_705_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FJointValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FJointValidationResult;
// ********** End ScriptStruct FJointValidationResult **********************************************

// ********** Begin ScriptStruct FBlendShapeWeightArray ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_983_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBlendShapeWeightArray_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBlendShapeWeightArray;
// ********** End ScriptStruct FBlendShapeWeightArray **********************************************

// ********** Begin ScriptStruct FPhonemeBlendShapeMap *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_992_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPhonemeBlendShapeMap_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPhonemeBlendShapeMap;
// ********** End ScriptStruct FPhonemeBlendShapeMap ***********************************************

// ********** Begin ScriptStruct FEmotionPresetMap *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1001_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEmotionPresetMap_Statics; \
	static class UScriptStruct* StaticStruct();


struct FEmotionPresetMap;
// ********** End ScriptStruct FEmotionPresetMap ***************************************************

// ********** Begin ScriptStruct FTextureBlendingData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1011_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureBlendingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureBlendingData;
// ********** End ScriptStruct FTextureBlendingData ************************************************

// ********** Begin ScriptStruct FClothingPhysicsData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1026_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingPhysicsData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingPhysicsData;
// ********** End ScriptStruct FClothingPhysicsData ************************************************

// ********** Begin ScriptStruct FClothingFittingData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1071_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingFittingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingFittingData;
// ********** End ScriptStruct FClothingFittingData ************************************************

// ********** Begin ScriptStruct FClothingMaterialData *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1083_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingMaterialData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingMaterialData;
// ********** End ScriptStruct FClothingMaterialData ***********************************************

// ********** Begin ScriptStruct FClothingMaskData *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1095_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingMaskData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingMaskData;
// ********** End ScriptStruct FClothingMaskData ***************************************************

// ********** Begin ScriptStruct FClothingLODData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1107_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FClothingLODData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FClothingLODData;
// ********** End ScriptStruct FClothingLODData ****************************************************

// ********** Begin ScriptStruct FPoseGenerationData ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPoseGenerationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPoseGenerationData;
// ********** End ScriptStruct FPoseGenerationData *************************************************

// ********** Begin Delegate FOnAsyncOperationCompleted ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5712_DELEGATE \
static void FOnAsyncOperationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnAsyncOperationCompleted, const FString& OperationName, bool bSuccess);


// ********** End Delegate FOnAsyncOperationCompleted **********************************************

// ********** Begin Delegate FOnPerformanceMetricsUpdated ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5716_DELEGATE \
static void FOnPerformanceMetricsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceMetricsUpdated, FPerformanceMetrics const& Metrics);


// ********** End Delegate FOnPerformanceMetricsUpdated ********************************************

// ********** Begin Delegate FOnMemoryPoolStatusChanged ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5720_DELEGATE \
static void FOnMemoryPoolStatusChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMemoryPoolStatusChanged, EMemoryPoolType PoolType, float UsagePercentage);


// ********** End Delegate FOnMemoryPoolStatusChanged **********************************************

// ********** Begin Delegate FOnErrorOccurred ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5766_DELEGATE \
static void FOnErrorOccurred_DelegateWrapper(const FMulticastScriptDelegate& OnErrorOccurred, FErrorInfo const& ErrorInfo);


// ********** End Delegate FOnErrorOccurred ********************************************************

// ********** Begin Delegate FOnErrorRecovered *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5770_DELEGATE \
static void FOnErrorRecovered_DelegateWrapper(const FMulticastScriptDelegate& OnErrorRecovered, FErrorInfo const& ErrorInfo, bool bSuccess);


// ********** End Delegate FOnErrorRecovered *******************************************************

// ********** Begin Delegate FOnSystemHealthChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_5774_DELEGATE \
static void FOnSystemHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSystemHealthChanged, float HealthScore);


// ********** End Delegate FOnSystemHealthChanged **************************************************

// ********** Begin Class UAuracronMetaHumanBridgeAPI **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetNeutralJointRotationsPython); \
	DECLARE_FUNCTION(execGetNeutralJointTranslationsPython); \
	DECLARE_FUNCTION(execSetNeutralJointRotationsPython); \
	DECLARE_FUNCTION(execSetNeutralJointTranslationsPython); \
	DECLARE_FUNCTION(execSetBlendShapeTargetDeltasPython); \
	DECLARE_FUNCTION(execGetBridgeInformation); \
	DECLARE_FUNCTION(execIsBridgeInitializedForPython); \
	DECLARE_FUNCTION(execGetSupportedDNAVersions); \
	DECLARE_FUNCTION(execGetBridgeVersionString); \
	DECLARE_FUNCTION(execGetErrorHandlingStats); \
	DECLARE_FUNCTION(execImportAnimationBlueprintFromFile); \
	DECLARE_FUNCTION(execExportAnimationBlueprintToFile); \
	DECLARE_FUNCTION(execBatchProcessAnimationBlueprints); \
	DECLARE_FUNCTION(execCreateAnimationBlueprintFromTemplate); \
	DECLARE_FUNCTION(execValidateAnimationBlueprintParameters); \
	DECLARE_FUNCTION(execOptimizeAnimationBlueprintPerformance); \
	DECLARE_FUNCTION(execGeneratePhonemeMapping); \
	DECLARE_FUNCTION(execApplyEmotionToAnimationBlueprint); \
	DECLARE_FUNCTION(execCreatePoseFromBlendShapes); \
	DECLARE_FUNCTION(execProcessAudioForLipSync); \
	DECLARE_FUNCTION(execGenerateAnimationBlueprintLODs); \
	DECLARE_FUNCTION(execSetupEmotionMappingSystem); \
	DECLARE_FUNCTION(execSetupLipSyncSystem); \
	DECLARE_FUNCTION(execSetupFacialAnimationSystem); \
	DECLARE_FUNCTION(execGenerateProceduralPoses); \
	DECLARE_FUNCTION(execGetSystemHealthScore); \
	DECLARE_FUNCTION(execExportErrorLog); \
	DECLARE_FUNCTION(execSetErrorReportingEnabled); \
	DECLARE_FUNCTION(execGetRecoverySuggestions); \
	DECLARE_FUNCTION(execCanRetryOperation); \
	DECLARE_FUNCTION(execAttemptErrorRecovery); \
	DECLARE_FUNCTION(execRunSystemDiagnostics); \
	DECLARE_FUNCTION(execValidateSystemConfiguration); \
	DECLARE_FUNCTION(execSetErrorHandlingMode); \
	DECLARE_FUNCTION(execClearErrorHistory); \
	DECLARE_FUNCTION(execGetErrorHistory); \
	DECLARE_FUNCTION(execGetLastError); \
	DECLARE_FUNCTION(execRestoreDNAFromBackup); \
	DECLARE_FUNCTION(execCreateDNABackup); \
	DECLARE_FUNCTION(execRepairCorruptedDNA); \
	DECLARE_FUNCTION(execDetectDNACorruption); \
	DECLARE_FUNCTION(execValidateDNAFile); \
	DECLARE_FUNCTION(execProfileOperationPerformance); \
	DECLARE_FUNCTION(execApplyAutomaticOptimizations); \
	DECLARE_FUNCTION(execGetOptimizationRecommendations); \
	DECLARE_FUNCTION(execClearPerformanceCaches); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execSetPerformanceMonitoringEnabled); \
	DECLARE_FUNCTION(execGetPerformanceHistory); \
	DECLARE_FUNCTION(execGetCurrentPerformanceMetrics); \
	DECLARE_FUNCTION(execExecuteBatchTextureOperations); \
	DECLARE_FUNCTION(execExecuteBatchDNAOperations); \
	DECLARE_FUNCTION(execConfigureBatchProcessing); \
	DECLARE_FUNCTION(execExecuteGPUTextureProcessing); \
	DECLARE_FUNCTION(execExecuteGPUMeshDeformation); \
	DECLARE_FUNCTION(execConfigureGPUAcceleration); \
	DECLARE_FUNCTION(execExecuteAsyncTextureGeneration); \
	DECLARE_FUNCTION(execExecuteAsyncDNAProcessing); \
	DECLARE_FUNCTION(execConfigureAsyncProcessing); \
	DECLARE_FUNCTION(execGetMemoryPoolStatus); \
	DECLARE_FUNCTION(execConfigureMemoryPools); \
	DECLARE_FUNCTION(execShutdownPerformanceOptimization); \
	DECLARE_FUNCTION(execInitializePerformanceOptimization); \
	DECLARE_FUNCTION(execGenerateAnimationBlueprint); \
	DECLARE_FUNCTION(execImportClothingFromFile); \
	DECLARE_FUNCTION(execExportClothingToFile); \
	DECLARE_FUNCTION(execBatchProcessClothing); \
	DECLARE_FUNCTION(execCreateClothingFromTemplate); \
	DECLARE_FUNCTION(execValidateClothingParameters); \
	DECLARE_FUNCTION(execOptimizeClothingPerformance); \
	DECLARE_FUNCTION(execGenerateClothingLODs); \
	DECLARE_FUNCTION(execPaintClothingMask); \
	DECLARE_FUNCTION(execSetupClothingCollision); \
	DECLARE_FUNCTION(execApplyClothingMaterial); \
	DECLARE_FUNCTION(execSetupClothingPhysics); \
	DECLARE_FUNCTION(execFitClothingToMesh); \
	DECLARE_FUNCTION(execGenerateProceduralClothing); \
	DECLARE_FUNCTION(execApplyHairPreset); \
	DECLARE_FUNCTION(execCreateHairPreset); \
	DECLARE_FUNCTION(execBatchGenerateHairVariations); \
	DECLARE_FUNCTION(execExportHairToAlembic); \
	DECLARE_FUNCTION(execImportHairFromAlembic); \
	DECLARE_FUNCTION(execGetHairGenerationStats); \
	DECLARE_FUNCTION(execValidateHairGenerationParameters); \
	DECLARE_FUNCTION(execCreateHairMaterialInstance); \
	DECLARE_FUNCTION(execGenerateHairTextureAtlas); \
	DECLARE_FUNCTION(execOptimizeHairLOD); \
	DECLARE_FUNCTION(execCreateGroomBinding); \
	DECLARE_FUNCTION(execGenerateHairColorVariation); \
	DECLARE_FUNCTION(execApplyHairStyling); \
	DECLARE_FUNCTION(execSetupHairPhysics); \
	DECLARE_FUNCTION(execCreateHairCards); \
	DECLARE_FUNCTION(execGenerateProceduralHair); \
	DECLARE_FUNCTION(execGetTextureGenerationStats); \
	DECLARE_FUNCTION(execClearTextureCache); \
	DECLARE_FUNCTION(execGetCachedTexture); \
	DECLARE_FUNCTION(execValidateTextureGenerationParameters); \
	DECLARE_FUNCTION(execGetQualityResolution); \
	DECLARE_FUNCTION(execUpdateMaterialParameterCollection); \
	DECLARE_FUNCTION(execCreateMaterialWithTexture); \
	DECLARE_FUNCTION(execApplyNoisePattern); \
	DECLARE_FUNCTION(execBlendTextures); \
	DECLARE_FUNCTION(execGenerateAgingEffect); \
	DECLARE_FUNCTION(execGenerateMakeup); \
	DECLARE_FUNCTION(execGenerateScar); \
	DECLARE_FUNCTION(execGenerateTattoo); \
	DECLARE_FUNCTION(execGenerateSkinVariation); \
	DECLARE_FUNCTION(execGenerateProceduralTexture); \
	DECLARE_FUNCTION(execCalculateBoneChainLength); \
	DECLARE_FUNCTION(execIsBoneInChain); \
	DECLARE_FUNCTION(execFindBoneByName); \
	DECLARE_FUNCTION(execGetBoneName); \
	DECLARE_FUNCTION(execGetBoneParent); \
	DECLARE_FUNCTION(execGetBoneChildren); \
	DECLARE_FUNCTION(execRestoreRigConfiguration); \
	DECLARE_FUNCTION(execBackupRigConfiguration); \
	DECLARE_FUNCTION(execValidateRigIntegrity); \
	DECLARE_FUNCTION(execOptimizeRigPerformance); \
	DECLARE_FUNCTION(execBatchApplyRigTransformations); \
	DECLARE_FUNCTION(execApplyRigTransformation); \
	DECLARE_FUNCTION(execValidateRetargeting); \
	DECLARE_FUNCTION(execCreateBoneMapping); \
	DECLARE_FUNCTION(execRetargetPose); \
	DECLARE_FUNCTION(execRetargetAnimation); \
	DECLARE_FUNCTION(execSetupRetargeting); \
	DECLARE_FUNCTION(execResetFKChain); \
	DECLARE_FUNCTION(execGetFKBoneRotation); \
	DECLARE_FUNCTION(execSetFKBoneRotation); \
	DECLARE_FUNCTION(execSetupFKChain); \
	DECLARE_FUNCTION(execToggleIKChain); \
	DECLARE_FUNCTION(execSetIKChainWeight); \
	DECLARE_FUNCTION(execSolveIKChain); \
	DECLARE_FUNCTION(execGetAllIKChains); \
	DECLARE_FUNCTION(execGetIKChainData); \
	DECLARE_FUNCTION(execRemoveIKChain); \
	DECLARE_FUNCTION(execModifyIKChain); \
	DECLARE_FUNCTION(execCreateIKChain); \
	DECLARE_FUNCTION(execToggleConstraint); \
	DECLARE_FUNCTION(execSetConstraintWeight); \
	DECLARE_FUNCTION(execRemoveConstraint); \
	DECLARE_FUNCTION(execScaleBoneHierarchy); \
	DECLARE_FUNCTION(execResetBoneScale); \
	DECLARE_FUNCTION(execApplyMultipleBoneScaling); \
	DECLARE_FUNCTION(execApplyBoneScalingData); \
	DECLARE_FUNCTION(execGetBoneScale); \
	DECLARE_FUNCTION(execSetBoneScale); \
	DECLARE_FUNCTION(execCalculateMeshBounds); \
	DECLARE_FUNCTION(execGetMeshStatistics); \
	DECLARE_FUNCTION(execRestoreMeshFromBackup); \
	DECLARE_FUNCTION(execBakeMeshDeformation); \
	DECLARE_FUNCTION(execCreateDeformationFromBlendShape); \
	DECLARE_FUNCTION(execApplySmoothDeformation); \
	DECLARE_FUNCTION(execSetVertexData); \
	DECLARE_FUNCTION(execGetVertexData); \
	DECLARE_FUNCTION(execValidateMeshIntegrity); \
	DECLARE_FUNCTION(execOptimizeMeshDeformation); \
	DECLARE_FUNCTION(execGenerateMeshLODs); \
	DECLARE_FUNCTION(execPreserveUVMapping); \
	DECLARE_FUNCTION(execRecalculateNormals); \
	DECLARE_FUNCTION(execDeformMeshRegion); \
	DECLARE_FUNCTION(execTransformVertices); \
	DECLARE_FUNCTION(execGetSpecificVertexPositions); \
	DECLARE_FUNCTION(execSetSpecificVertexPositions); \
	DECLARE_FUNCTION(execGetControlRigData); \
	DECLARE_FUNCTION(execGetBehaviorData); \
	DECLARE_FUNCTION(execApplyJointConstraints); \
	DECLARE_FUNCTION(execValidateJointTransform); \
	DECLARE_FUNCTION(execGetJointConstraints); \
	DECLARE_FUNCTION(execRemoveJointConstraint); \
	DECLARE_FUNCTION(execAddJointConstraint); \
	DECLARE_FUNCTION(execGetRootJoints); \
	DECLARE_FUNCTION(execGetJointDepth); \
	DECLARE_FUNCTION(execGetJointChildren); \
	DECLARE_FUNCTION(execHasCircularDependencies); \
	DECLARE_FUNCTION(execValidateJointHierarchy); \
	DECLARE_FUNCTION(execGetJointScale); \
	DECLARE_FUNCTION(execGetJointTranslation); \
	DECLARE_FUNCTION(execGetJointRotation); \
	DECLARE_FUNCTION(execSetJointScale); \
	DECLARE_FUNCTION(execSetJointTranslation); \
	DECLARE_FUNCTION(execSetJointRotation); \
	DECLARE_FUNCTION(execGetJointTransform); \
	DECLARE_FUNCTION(execSetJointTransform); \
	DECLARE_FUNCTION(execSetJointHierarchy); \
	DECLARE_FUNCTION(execGetJointHierarchy); \
	DECLARE_FUNCTION(execSetNeutralJointRotations); \
	DECLARE_FUNCTION(execGetNeutralJointRotations); \
	DECLARE_FUNCTION(execSetNeutralJointTranslations); \
	DECLARE_FUNCTION(execGetNeutralJointTranslations); \
	DECLARE_FUNCTION(execGetJointData); \
	DECLARE_FUNCTION(execGetJointName); \
	DECLARE_FUNCTION(execGetJointCount); \
	DECLARE_FUNCTION(execGenerateProceduralFacialExpression); \
	DECLARE_FUNCTION(execRemoveFacialExpressionPreset); \
	DECLARE_FUNCTION(execGetAvailableFacialExpressionPresets); \
	DECLARE_FUNCTION(execApplyFacialExpressionPreset); \
	DECLARE_FUNCTION(execCreateFacialExpressionPreset); \
	DECLARE_FUNCTION(execOptimizeBlendShapeTargets); \
	DECLARE_FUNCTION(execValidateBlendShapeTargets); \
	DECLARE_FUNCTION(execApplyBlendShapeWeights); \
	DECLARE_FUNCTION(execInterpolateBlendShapeTargets); \
	DECLARE_FUNCTION(execDuplicateBlendShapeTarget); \
	DECLARE_FUNCTION(execRemoveBlendShapeTarget); \
	DECLARE_FUNCTION(execCreateBlendShapeTarget); \
	DECLARE_FUNCTION(execSetBlendShapeTargetVertexIndices); \
	DECLARE_FUNCTION(execSetBlendShapeTargetDeltas); \
	DECLARE_FUNCTION(execGetBlendShapeTarget); \
	DECLARE_FUNCTION(execGetBlendShapeChannelName); \
	DECLARE_FUNCTION(execGetBlendShapeTargetCount); \
	DECLARE_FUNCTION(execSetVertexPositions); \
	DECLARE_FUNCTION(execGetVertexPositions); \
	DECLARE_FUNCTION(execGetMeshData); \
	DECLARE_FUNCTION(execGetMeshName); \
	DECLARE_FUNCTION(execGetMeshCount); \
	DECLARE_FUNCTION(execSetDNADescriptor); \
	DECLARE_FUNCTION(execGetDNADescriptor); \
	DECLARE_FUNCTION(execSaveDNAFile); \
	DECLARE_FUNCTION(execLoadDNAFile);


AURACRONMETAHUMANBRIDGE_API UClass* Z_Construct_UClass_UAuracronMetaHumanBridgeAPI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanBridgeAPI(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanBridgeAPI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANBRIDGE_API UClass* Z_Construct_UClass_UAuracronMetaHumanBridgeAPI_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanBridgeAPI, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanBridge"), Z_Construct_UClass_UAuracronMetaHumanBridgeAPI_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanBridgeAPI(UAuracronMetaHumanBridgeAPI&&) = delete; \
	UAuracronMetaHumanBridgeAPI(const UAuracronMetaHumanBridgeAPI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanBridgeAPI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanBridgeAPI); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanBridgeAPI) \
	NO_API virtual ~UAuracronMetaHumanBridgeAPI();


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1190_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h_1193_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanBridgeAPI;

// ********** End Class UAuracronMetaHumanBridgeAPI ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h

// ********** Begin Enum EMetaHumanDNADataLayer ****************************************************
#define FOREACH_ENUM_EMETAHUMANDNADATALAYER(op) \
	op(EMetaHumanDNADataLayer::All) \
	op(EMetaHumanDNADataLayer::Descriptor) \
	op(EMetaHumanDNADataLayer::Definition) \
	op(EMetaHumanDNADataLayer::Behavior) \
	op(EMetaHumanDNADataLayer::Geometry) \
	op(EMetaHumanDNADataLayer::GeometryWithoutBlendShapes) \
	op(EMetaHumanDNADataLayer::MachineLearnedBehavior) 

enum class EMetaHumanDNADataLayer : uint8;
template<> struct TIsUEnumClass<EMetaHumanDNADataLayer> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanDNADataLayer>();
// ********** End Enum EMetaHumanDNADataLayer ******************************************************

// ********** Begin Enum EMetaHumanGender **********************************************************
#define FOREACH_ENUM_EMETAHUMANGENDER(op) \
	op(EMetaHumanGender::Male) \
	op(EMetaHumanGender::Female) \
	op(EMetaHumanGender::Other) 

enum class EMetaHumanGender : uint8;
template<> struct TIsUEnumClass<EMetaHumanGender> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanGender>();
// ********** End Enum EMetaHumanGender ************************************************************

// ********** Begin Enum EMetaHumanArchetype *******************************************************
#define FOREACH_ENUM_EMETAHUMANARCHETYPE(op) \
	op(EMetaHumanArchetype::Standard) \
	op(EMetaHumanArchetype::Athletic) \
	op(EMetaHumanArchetype::Heavy) \
	op(EMetaHumanArchetype::Slim) \
	op(EMetaHumanArchetype::Custom) 

enum class EMetaHumanArchetype : uint8;
template<> struct TIsUEnumClass<EMetaHumanArchetype> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanArchetype>();
// ********** End Enum EMetaHumanArchetype *********************************************************

// ********** Begin Enum EMetaHumanCoordinateSystem ************************************************
#define FOREACH_ENUM_EMETAHUMANCOORDINATESYSTEM(op) \
	op(EMetaHumanCoordinateSystem::LeftHanded) \
	op(EMetaHumanCoordinateSystem::RightHanded) 

enum class EMetaHumanCoordinateSystem : uint8;
template<> struct TIsUEnumClass<EMetaHumanCoordinateSystem> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanCoordinateSystem>();
// ********** End Enum EMetaHumanCoordinateSystem **************************************************

// ********** Begin Enum EMetaHumanRotationUnit ****************************************************
#define FOREACH_ENUM_EMETAHUMANROTATIONUNIT(op) \
	op(EMetaHumanRotationUnit::Degrees) \
	op(EMetaHumanRotationUnit::Radians) 

enum class EMetaHumanRotationUnit : uint8;
template<> struct TIsUEnumClass<EMetaHumanRotationUnit> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanRotationUnit>();
// ********** End Enum EMetaHumanRotationUnit ******************************************************

// ********** Begin Enum EMetaHumanTranslationUnit *************************************************
#define FOREACH_ENUM_EMETAHUMANTRANSLATIONUNIT(op) \
	op(EMetaHumanTranslationUnit::Centimeters) \
	op(EMetaHumanTranslationUnit::Meters) 

enum class EMetaHumanTranslationUnit : uint8;
template<> struct TIsUEnumClass<EMetaHumanTranslationUnit> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanTranslationUnit>();
// ********** End Enum EMetaHumanTranslationUnit ***************************************************

// ********** Begin Enum EBlendShapeInterpolationType **********************************************
#define FOREACH_ENUM_EBLENDSHAPEINTERPOLATIONTYPE(op) \
	op(EBlendShapeInterpolationType::Linear) \
	op(EBlendShapeInterpolationType::Cubic) \
	op(EBlendShapeInterpolationType::Smoothstep) \
	op(EBlendShapeInterpolationType::Hermite) \
	op(EBlendShapeInterpolationType::Bezier) 

enum class EBlendShapeInterpolationType : uint8;
template<> struct TIsUEnumClass<EBlendShapeInterpolationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeInterpolationType>();
// ********** End Enum EBlendShapeInterpolationType ************************************************

// ********** Begin Enum EFacialExpressionType *****************************************************
#define FOREACH_ENUM_EFACIALEXPRESSIONTYPE(op) \
	op(EFacialExpressionType::Neutral) \
	op(EFacialExpressionType::Happy) \
	op(EFacialExpressionType::Sad) \
	op(EFacialExpressionType::Angry) \
	op(EFacialExpressionType::Surprised) \
	op(EFacialExpressionType::Disgusted) \
	op(EFacialExpressionType::Fearful) \
	op(EFacialExpressionType::Contempt) \
	op(EFacialExpressionType::Custom) 

enum class EFacialExpressionType : uint8;
template<> struct TIsUEnumClass<EFacialExpressionType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EFacialExpressionType>();
// ********** End Enum EFacialExpressionType *******************************************************

// ********** Begin Enum EBlendShapeValidationType *************************************************
#define FOREACH_ENUM_EBLENDSHAPEVALIDATIONTYPE(op) \
	op(EBlendShapeValidationType::None) \
	op(EBlendShapeValidationType::Basic) \
	op(EBlendShapeValidationType::Comprehensive) \
	op(EBlendShapeValidationType::Performance) 

enum class EBlendShapeValidationType : uint8;
template<> struct TIsUEnumClass<EBlendShapeValidationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeValidationType>();
// ********** End Enum EBlendShapeValidationType ***************************************************

// ********** Begin Enum EJointConstraintType ******************************************************
#define FOREACH_ENUM_EJOINTCONSTRAINTTYPE(op) \
	op(EJointConstraintType::None) \
	op(EJointConstraintType::Position) \
	op(EJointConstraintType::Rotation) \
	op(EJointConstraintType::Scale) \
	op(EJointConstraintType::LookAt) \
	op(EJointConstraintType::Aim) \
	op(EJointConstraintType::Parent) \
	op(EJointConstraintType::Point) \
	op(EJointConstraintType::Orient) 

enum class EJointConstraintType : uint8;
template<> struct TIsUEnumClass<EJointConstraintType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointConstraintType>();
// ********** End Enum EJointConstraintType ********************************************************

// ********** Begin Enum EJointTransformSpace ******************************************************
#define FOREACH_ENUM_EJOINTTRANSFORMSPACE(op) \
	op(EJointTransformSpace::Local) \
	op(EJointTransformSpace::World) \
	op(EJointTransformSpace::Parent) \
	op(EJointTransformSpace::Component) 

enum class EJointTransformSpace : uint8;
template<> struct TIsUEnumClass<EJointTransformSpace> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointTransformSpace>();
// ********** End Enum EJointTransformSpace ********************************************************

// ********** Begin Enum EClothingMaterialType *****************************************************
#define FOREACH_ENUM_ECLOTHINGMATERIALTYPE(op) \
	op(EClothingMaterialType::Cotton) \
	op(EClothingMaterialType::Silk) \
	op(EClothingMaterialType::Leather) \
	op(EClothingMaterialType::Chainmail) \
	op(EClothingMaterialType::Plate) \
	op(EClothingMaterialType::Fabric) \
	op(EClothingMaterialType::Denim) \
	op(EClothingMaterialType::Wool) \
	op(EClothingMaterialType::Linen) \
	op(EClothingMaterialType::Synthetic) 

enum class EClothingMaterialType : uint8;
template<> struct TIsUEnumClass<EClothingMaterialType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaterialType>();
// ********** End Enum EClothingMaterialType *******************************************************

// ********** Begin Enum ELipSyncType **************************************************************
#define FOREACH_ENUM_ELIPSYNCTYPE(op) \
	op(ELipSyncType::Phoneme) \
	op(ELipSyncType::Viseme) \
	op(ELipSyncType::Audio2Face) \
	op(ELipSyncType::Procedural) \
	op(ELipSyncType::MachineLearning) \
	op(ELipSyncType::Hybrid) 

enum class ELipSyncType : uint8;
template<> struct TIsUEnumClass<ELipSyncType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ELipSyncType>();
// ********** End Enum ELipSyncType ****************************************************************

// ********** Begin Enum EEmotionMappingType *******************************************************
#define FOREACH_ENUM_EEMOTIONMAPPINGTYPE(op) \
	op(EEmotionMappingType::Basic) \
	op(EEmotionMappingType::Extended) \
	op(EEmotionMappingType::FACS) \
	op(EEmotionMappingType::Custom) \
	op(EEmotionMappingType::Procedural) \
	op(EEmotionMappingType::MachineLearning) 

enum class EEmotionMappingType : uint8;
template<> struct TIsUEnumClass<EEmotionMappingType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEmotionMappingType>();
// ********** End Enum EEmotionMappingType *********************************************************

// ********** Begin Enum EPoseGenerationMethod *****************************************************
#define FOREACH_ENUM_EPOSEGENERATIONMETHOD(op) \
	op(EPoseGenerationMethod::Manual) \
	op(EPoseGenerationMethod::Procedural) \
	op(EPoseGenerationMethod::MotionCapture) \
	op(EPoseGenerationMethod::MachineLearning) \
	op(EPoseGenerationMethod::Hybrid) \
	op(EPoseGenerationMethod::Template) 

enum class EPoseGenerationMethod : uint8;
template<> struct TIsUEnumClass<EPoseGenerationMethod> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPoseGenerationMethod>();
// ********** End Enum EPoseGenerationMethod *******************************************************

// ********** Begin Enum EAnimationBlueprintQuality ************************************************
#define FOREACH_ENUM_EANIMATIONBLUEPRINTQUALITY(op) \
	op(EAnimationBlueprintQuality::Low) \
	op(EAnimationBlueprintQuality::Medium) \
	op(EAnimationBlueprintQuality::High) \
	op(EAnimationBlueprintQuality::Ultra) \
	op(EAnimationBlueprintQuality::Custom) \
	op(EAnimationBlueprintQuality::Adaptive) \
	op(EAnimationBlueprintQuality::PerformanceBased) \
	op(EAnimationBlueprintQuality::QualityBased) 

enum class EAnimationBlueprintQuality : uint8;
template<> struct TIsUEnumClass<EAnimationBlueprintQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAnimationBlueprintQuality>();
// ********** End Enum EAnimationBlueprintQuality **************************************************

// ********** Begin Enum EPhonemeType **************************************************************
#define FOREACH_ENUM_EPHONEMETYPE(op) \
	op(EPhonemeType::A) \
	op(EPhonemeType::E) \
	op(EPhonemeType::I) \
	op(EPhonemeType::O) \
	op(EPhonemeType::U) \
	op(EPhonemeType::B) \
	op(EPhonemeType::C) \
	op(EPhonemeType::D) \
	op(EPhonemeType::F) \
	op(EPhonemeType::G) \
	op(EPhonemeType::H) \
	op(EPhonemeType::J) \
	op(EPhonemeType::K) \
	op(EPhonemeType::L) \
	op(EPhonemeType::M) \
	op(EPhonemeType::N) \
	op(EPhonemeType::P) \
	op(EPhonemeType::Q) \
	op(EPhonemeType::R) \
	op(EPhonemeType::S) \
	op(EPhonemeType::T) \
	op(EPhonemeType::V) \
	op(EPhonemeType::W) \
	op(EPhonemeType::X) \
	op(EPhonemeType::Y) \
	op(EPhonemeType::Z) \
	op(EPhonemeType::TH) \
	op(EPhonemeType::SH) \
	op(EPhonemeType::CH) \
	op(EPhonemeType::Silent) 

enum class EPhonemeType : uint8;
template<> struct TIsUEnumClass<EPhonemeType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPhonemeType>();
// ********** End Enum EPhonemeType ****************************************************************

// ********** Begin Enum EClothingFittingMethod ****************************************************
#define FOREACH_ENUM_ECLOTHINGFITTINGMETHOD(op) \
	op(EClothingFittingMethod::Automatic) \
	op(EClothingFittingMethod::Manual) \
	op(EClothingFittingMethod::Procedural) \
	op(EClothingFittingMethod::TemplateBase) \
	op(EClothingFittingMethod::MorphTarget) 

enum class EClothingFittingMethod : uint8;
template<> struct TIsUEnumClass<EClothingFittingMethod> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingFittingMethod>();
// ********** End Enum EClothingFittingMethod ******************************************************

// ********** Begin Enum EClothingLODMode **********************************************************
#define FOREACH_ENUM_ECLOTHINGLODMODE(op) \
	op(EClothingLODMode::Automatic) \
	op(EClothingLODMode::Manual) \
	op(EClothingLODMode::DistanceBased) \
	op(EClothingLODMode::PerformanceBased) 

enum class EClothingLODMode : uint8;
template<> struct TIsUEnumClass<EClothingLODMode> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingLODMode>();
// ********** End Enum EClothingLODMode ************************************************************

// ********** Begin Enum EClothingMaskTarget *******************************************************
#define FOREACH_ENUM_ECLOTHINGMASKTARGET(op) \
	op(EClothingMaskTarget::MaxDistance) \
	op(EClothingMaskTarget::BackstopDistance) \
	op(EClothingMaskTarget::BackstopRadius) \
	op(EClothingMaskTarget::AnimDriveStiffness) \
	op(EClothingMaskTarget::AnimDriveDamping) 

enum class EClothingMaskTarget : uint8;
template<> struct TIsUEnumClass<EClothingMaskTarget> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaskTarget>();
// ********** End Enum EClothingMaskTarget *********************************************************

// ********** Begin Enum EMeshDeformationType ******************************************************
#define FOREACH_ENUM_EMESHDEFORMATIONTYPE(op) \
	op(EMeshDeformationType::Vertex) \
	op(EMeshDeformationType::Normal) \
	op(EMeshDeformationType::UV) \
	op(EMeshDeformationType::LOD) \
	op(EMeshDeformationType::Combined) 

enum class EMeshDeformationType : uint8;
template<> struct TIsUEnumClass<EMeshDeformationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMeshDeformationType>();
// ********** End Enum EMeshDeformationType ********************************************************

// ********** Begin Enum EHairCardQuality **********************************************************
#define FOREACH_ENUM_EHAIRCARDQUALITY(op) \
	op(EHairCardQuality::Low) \
	op(EHairCardQuality::Medium) \
	op(EHairCardQuality::High) \
	op(EHairCardQuality::Ultra) 

enum class EHairCardQuality : uint8;
template<> struct TIsUEnumClass<EHairCardQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairCardQuality>();
// ********** End Enum EHairCardQuality ************************************************************

// ********** Begin Enum EClothingCollisionType ****************************************************
#define FOREACH_ENUM_ECLOTHINGCOLLISIONTYPE(op) \
	op(EClothingCollisionType::None) \
	op(EClothingCollisionType::Basic) \
	op(EClothingCollisionType::Precise) \
	op(EClothingCollisionType::Convex) \
	op(EClothingCollisionType::TriangleMesh) 

enum class EClothingCollisionType : uint8;
template<> struct TIsUEnumClass<EClothingCollisionType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingCollisionType>();
// ********** End Enum EClothingCollisionType ******************************************************

// ********** Begin Enum EClothingQualityLevel *****************************************************
#define FOREACH_ENUM_ECLOTHINGQUALITYLEVEL(op) \
	op(EClothingQualityLevel::Low) \
	op(EClothingQualityLevel::Medium) \
	op(EClothingQualityLevel::High) \
	op(EClothingQualityLevel::Ultra) \
	op(EClothingQualityLevel::Cinematic) 

enum class EClothingQualityLevel : uint8;
template<> struct TIsUEnumClass<EClothingQualityLevel> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingQualityLevel>();
// ********** End Enum EClothingQualityLevel *******************************************************

// ********** Begin Enum ERigTransformationType ****************************************************
#define FOREACH_ENUM_ERIGTRANSFORMATIONTYPE(op) \
	op(ERigTransformationType::BoneScaling) \
	op(ERigTransformationType::Constraint) \
	op(ERigTransformationType::IKSetup) \
	op(ERigTransformationType::FKSetup) \
	op(ERigTransformationType::Retargeting) \
	op(ERigTransformationType::Combined) 

enum class ERigTransformationType : uint8;
template<> struct TIsUEnumClass<ERigTransformationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERigTransformationType>();
// ********** End Enum ERigTransformationType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
