// AuracronPCGBridgeTests.cpp
// Unit Tests for AURACRON PCG Bridge
// Production-ready comprehensive test suite

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGCore.h"
#include "AuracronPCGNodes.h"
#include "AuracronPCGData.h"
#include "AuracronPCGGraph.h"
#include "AuracronPCGExecution.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronPCGTests, Log, All);

// ========================================
// Core PCG Bridge Tests
// ========================================

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGBridgeInitializationTest, 
    "Auracron.PCGBridge.Core.Initialization",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGBridgeInitializationTest::RunTest(const FString& Parameters)
{
    // Test PCG Bridge initialization
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        bool bInitialized = PCGBridge->InitializePCGBridge();
        TestTrue("PCG Bridge should initialize successfully", bInitialized);
        
        bool bIsReady = PCGBridge->IsPCGBridgeReady();
        TestTrue("PCG Bridge should be ready after initialization", bIsReady);
        
        FString Version = PCGBridge->GetPCGBridgeVersion();
        TestFalse("Version should not be empty", Version.IsEmpty());
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGGraphCreationTest, 
    "Auracron.PCGBridge.Graph.Creation",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGGraphCreationTest::RunTest(const FString& Parameters)
{
    // Test PCG Graph creation
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        PCGBridge->InitializePCGBridge();
        
        // Create a test graph
        UPCGGraph* TestGraph = PCGBridge->CreatePCGGraph(TEXT("TestGraph"));
        TestNotNull("PCG Graph should be created", TestGraph);
        
        if (TestGraph)
        {
            FString GraphName = TestGraph->GetName();
            TestTrue("Graph name should contain TestGraph", GraphName.Contains(TEXT("TestGraph")));
        }
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGNodeCreationTest, 
    "Auracron.PCGBridge.Nodes.Creation",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGNodeCreationTest::RunTest(const FString& Parameters)
{
    // Test PCG Node creation
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        PCGBridge->InitializePCGBridge();
        
        UPCGGraph* TestGraph = PCGBridge->CreatePCGGraph(TEXT("TestGraph"));
        TestNotNull("PCG Graph should be created", TestGraph);
        
        if (TestGraph)
        {
            // Test different node types
            UPCGNode* InputNode = PCGBridge->CreatePCGNode(TestGraph, TEXT("PCGInputNode"));
            TestNotNull("Input node should be created", InputNode);
            
            UPCGNode* TransformNode = PCGBridge->CreatePCGNode(TestGraph, TEXT("PCGTransformNode"));
            TestNotNull("Transform node should be created", TransformNode);
            
            UPCGNode* OutputNode = PCGBridge->CreatePCGNode(TestGraph, TEXT("PCGOutputNode"));
            TestNotNull("Output node should be created", OutputNode);
            
            // Test node connections
            if (InputNode && TransformNode && OutputNode)
            {
                bool bConnected1 = PCGBridge->ConnectPCGNodes(InputNode, TransformNode, TEXT("Output"), TEXT("Input"));
                TestTrue("Should connect input to transform node", bConnected1);
                
                bool bConnected2 = PCGBridge->ConnectPCGNodes(TransformNode, OutputNode, TEXT("Output"), TEXT("Input"));
                TestTrue("Should connect transform to output node", bConnected2);
            }
        }
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGExecutionTest, 
    "Auracron.PCGBridge.Execution.Basic",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGExecutionTest::RunTest(const FString& Parameters)
{
    // Test PCG Graph execution
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        PCGBridge->InitializePCGBridge();
        
        UPCGGraph* TestGraph = PCGBridge->CreatePCGGraph(TEXT("ExecutionTestGraph"));
        TestNotNull("PCG Graph should be created", TestGraph);
        
        if (TestGraph)
        {
            // Create a simple graph
            UPCGNode* InputNode = PCGBridge->CreatePCGNode(TestGraph, TEXT("PCGInputNode"));
            UPCGNode* OutputNode = PCGBridge->CreatePCGNode(TestGraph, TEXT("PCGOutputNode"));
            
            if (InputNode && OutputNode)
            {
                PCGBridge->ConnectPCGNodes(InputNode, OutputNode, TEXT("Output"), TEXT("Input"));
                
                // Execute the graph
                bool bExecuted = PCGBridge->ExecutePCGGraph(TestGraph);
                TestTrue("PCG Graph should execute successfully", bExecuted);
                
                // Check execution status
                bool bIsExecuting = PCGBridge->IsPCGGraphExecuting(TestGraph);
                // Note: Execution might complete immediately for simple graphs
                
                // Wait for completion if needed
                float TimeoutSeconds = 5.0f;
                float ElapsedTime = 0.0f;
                while (PCGBridge->IsPCGGraphExecuting(TestGraph) && ElapsedTime < TimeoutSeconds)
                {
                    FPlatformProcess::Sleep(0.1f);
                    ElapsedTime += 0.1f;
                }
                
                bool bCompleted = !PCGBridge->IsPCGGraphExecuting(TestGraph);
                TestTrue("PCG Graph execution should complete", bCompleted);
            }
        }
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGDataManipulationTest, 
    "Auracron.PCGBridge.Data.Manipulation",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGDataManipulationTest::RunTest(const FString& Parameters)
{
    // Test PCG Data manipulation
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        PCGBridge->InitializePCGBridge();
        
        // Test point data creation
        UPCGPointData* PointData = PCGBridge->CreatePointData();
        TestNotNull("Point data should be created", PointData);
        
        if (PointData)
        {
            // Add test points
            TArray<FPCGPoint> TestPoints;
            for (int32 i = 0; i < 10; i++)
            {
                FPCGPoint Point;
                Point.Transform.SetLocation(FVector(i * 100.0f, 0.0f, 0.0f));
                Point.Density = 1.0f;
                TestPoints.Add(Point);
            }
            
            bool bPointsSet = PCGBridge->SetPointDataPoints(PointData, TestPoints);
            TestTrue("Should be able to set points", bPointsSet);
            
            TArray<FPCGPoint> RetrievedPoints = PCGBridge->GetPointDataPoints(PointData);
            TestEqual("Retrieved points count should match", RetrievedPoints.Num(), TestPoints.Num());
        }
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGPerformanceTest, 
    "Auracron.PCGBridge.Performance.Basic",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGPerformanceTest::RunTest(const FString& Parameters)
{
    // Test PCG Bridge performance
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        PCGBridge->InitializePCGBridge();
        
        // Measure initialization time
        double StartTime = FPlatformTime::Seconds();
        
        // Create multiple graphs to test performance
        const int32 GraphCount = 10;
        TArray<UPCGGraph*> TestGraphs;
        
        for (int32 i = 0; i < GraphCount; i++)
        {
            FString GraphName = FString::Printf(TEXT("PerfTestGraph_%d"), i);
            UPCGGraph* Graph = PCGBridge->CreatePCGGraph(GraphName);
            TestNotNull("Graph should be created", Graph);
            TestGraphs.Add(Graph);
        }
        
        double EndTime = FPlatformTime::Seconds();
        double ElapsedTime = EndTime - StartTime;
        
        TestTrue("Graph creation should be reasonably fast", ElapsedTime < 1.0); // Less than 1 second
        
        UE_LOG(LogAuracronPCGTests, Log, TEXT("Created %d graphs in %f seconds"), GraphCount, ElapsedTime);
        
        PCGBridge->ShutdownPCGBridge();
    }
    
    return true;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronPCGMemoryTest, 
    "Auracron.PCGBridge.Memory.LeakCheck",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FAuracronPCGMemoryTest::RunTest(const FString& Parameters)
{
    // Test for memory leaks
    UAuracronPCGBridgeAPI* PCGBridge = NewObject<UAuracronPCGBridgeAPI>();
    TestNotNull("PCG Bridge should be created", PCGBridge);
    
    if (PCGBridge)
    {
        // Get initial memory usage
        FPlatformMemoryStats InitialStats = FPlatformMemory::GetStats();
        
        PCGBridge->InitializePCGBridge();
        
        // Create and destroy multiple objects
        for (int32 i = 0; i < 100; i++)
        {
            UPCGGraph* Graph = PCGBridge->CreatePCGGraph(FString::Printf(TEXT("MemTestGraph_%d"), i));
            if (Graph)
            {
                UPCGNode* Node = PCGBridge->CreatePCGNode(Graph, TEXT("PCGInputNode"));
                // Objects will be garbage collected
            }
        }
        
        // Force garbage collection
        CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);
        
        PCGBridge->ShutdownPCGBridge();
        
        // Force another garbage collection
        CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);
        
        // Get final memory usage
        FPlatformMemoryStats FinalStats = FPlatformMemory::GetStats();
        
        // Check for significant memory increase (allowing for some variance)
        int64 MemoryDifference = FinalStats.UsedPhysical - InitialStats.UsedPhysical;
        int64 MaxAllowedIncrease = 50 * 1024 * 1024; // 50MB tolerance
        
        TestTrue("Memory usage should not increase significantly", MemoryDifference < MaxAllowedIncrease);
        
        UE_LOG(LogAuracronPCGTests, Log, TEXT("Memory difference: %lld bytes"), MemoryDifference);
    }
    
    return true;
}
