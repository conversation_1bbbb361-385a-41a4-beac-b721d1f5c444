// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Implementation
// Bridge 2.11: PCG Framework - Material Assignment

#include "AuracronPCGMaterialSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED MATERIAL SELECTOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedMaterialSelectorSettings::UAuracronPCGAdvancedMaterialSelectorSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Material Selector");
    NodeMetadata.NodeDescription = TEXT("Selects materials based on various criteria and attributes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Selection"));
    NodeMetadata.Tags.Add(TEXT("Assignment"));
    NodeMetadata.Tags.Add(TEXT("Attributes"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.2f);
}

void UAuracronPCGAdvancedMaterialSelectorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseMultipleCriteria)
    {
        FPCGPinProperties& CriteriaPin = InputPins.Emplace_GetRef();
        CriteriaPin.Label = TEXT("Additional Criteria");
        CriteriaPin.AllowedTypes = EPCGDataType::Attribute;
        CriteriaPin.bAdvancedPin = true;
    }
}

void UAuracronPCGAdvancedMaterialSelectorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputSelectionInfo)
    {
        FPCGPinProperties& SelectionPin = OutputPins.Emplace_GetRef();
        SelectionPin.Label = TEXT("Selection Info");
        SelectionPin.AllowedTypes = EPCGDataType::Attribute;
        SelectionPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedMaterialSelectorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                   FPCGDataCollection& OutputData, 
                                                                                   const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedMaterialSelectorSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedMaterialSelectorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Material Selector");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 MaterialsAssigned = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Select materials for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Select material based on criteria
                UMaterialInterface* SelectedMaterial = SelectMaterialForPoint(OutputPoint, Settings);
                
                if (SelectedMaterial)
                {
                    // Apply material variation if enabled
                    UMaterialInterface* FinalMaterial = SelectedMaterial;
                    if (Settings->bApplyMaterialVariation)
                    {
                        FinalMaterial = ApplyMaterialVariation(SelectedMaterial, OutputPoint, Settings);
                    }
                    
                    // Apply material to point with proper assignment
                    ApplyMaterialToPoint(OutputPoint, FinalMaterial, Settings);
                    MaterialsAssigned++;
                    
                    AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Applied material %s to point"), 
                                              FinalMaterial ? *FinalMaterial->GetName() : TEXT("None"));
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Material Selector processed %d points, assigned %d materials"), 
                                  TotalProcessed, MaterialsAssigned);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Material Selector error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::SelectMaterialForPoint(const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    // Use utility function for material selection
    UMaterialInterface* SelectedMaterial = UAuracronPCGMaterialSystemUtils::SelectMaterial(Point, Settings->SelectionDescriptor);
    
    // Apply material variation if enabled
    if (Settings->bEnableMaterialVariation && SelectedMaterial)
    {
        SelectedMaterial = ApplyMaterialVariation(SelectedMaterial, Point, Settings);
    }

    // Handle multiple criteria if enabled
    if (Settings->bUseMultipleCriteria && Settings->AdditionalCriteria.Num() > 0)
    {
        TArray<UMaterialInterface*> CandidateMaterials = UAuracronPCGMaterialSystemUtils::SelectMultipleMaterials(
            Point, Settings->AdditionalCriteria, Settings->CriteriaWeights);
        
        if (CandidateMaterials.Num() > 0)
        {
            // Select best candidate based on combined criteria
            SelectedMaterial = SelectBestMaterialCandidate(CandidateMaterials, Point, Settings);
        }
    }

    // Fallback to default material if no selection
    if (!SelectedMaterial && Settings->SelectionDescriptor.FallbackMaterial.IsValid())
    {
        SelectedMaterial = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Settings->SelectionDescriptor.FallbackMaterial);
    }

    return SelectedMaterial;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::ApplyMaterialVariation(UMaterialInterface* BaseMaterial, const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    if (!BaseMaterial)
    {
        return nullptr;
    }

    // Create dynamic material instance for variation
    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, nullptr);
    if (!DynamicMaterial)
    {
        return BaseMaterial;
    }

    // Apply variation based on point position and settings
    FVector Position = Point.Transform.GetLocation();
    FRandomStream VariationStream(Settings->VariationSeed + FMath::FloorToInt(Position.X + Position.Y + Position.Z));
    
    // Generate multiple variation factors for different parameters
    float ColorVariation = VariationStream.FRandRange(-Settings->VariationStrength, Settings->VariationStrength);
    float RoughnessVariation = VariationStream.FRandRange(-Settings->VariationStrength * 0.5f, Settings->VariationStrength * 0.5f);
    float MetallicVariation = VariationStream.FRandRange(-Settings->VariationStrength * 0.3f, Settings->VariationStrength * 0.3f);
    float ScaleVariation = VariationStream.FRandRange(1.0f - Settings->VariationStrength * 0.2f, 1.0f + Settings->VariationStrength * 0.2f);
    
    // Apply variation to common material parameters with proper UE5.6 parameter names
    DynamicMaterial->SetScalarParameterValue(TEXT("ColorVariation"), ColorVariation);
    DynamicMaterial->SetScalarParameterValue(TEXT("RoughnessVariation"), RoughnessVariation);
    DynamicMaterial->SetScalarParameterValue(TEXT("MetallicVariation"), MetallicVariation);
    DynamicMaterial->SetScalarParameterValue(TEXT("TextureScale"), ScaleVariation);
    
    // Apply color tint variation
    FLinearColor BaseColor = FLinearColor::White;
    FLinearColor VariationColor = FLinearColor(
        FMath::Clamp(BaseColor.R + ColorVariation * 0.1f, 0.0f, 1.0f),
        FMath::Clamp(BaseColor.G + ColorVariation * 0.1f, 0.0f, 1.0f),
        FMath::Clamp(BaseColor.B + ColorVariation * 0.1f, 0.0f, 1.0f),
        BaseColor.A
    );
    DynamicMaterial->SetVectorParameterValue(TEXT("BaseColorTint"), VariationColor);
    
    // Apply position-based variation for world-aligned textures
    FVector WorldPosition = Point.Transform.GetLocation();
    DynamicMaterial->SetVectorParameterValue(TEXT("WorldPosition"), FLinearColor(WorldPosition.X, WorldPosition.Y, WorldPosition.Z, 0.0f));
    
    AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Applied material variation: Color=%.3f, Roughness=%.3f, Metallic=%.3f, Scale=%.3f"), 
                              ColorVariation, RoughnessVariation, MetallicVariation, ScaleVariation);

    return DynamicMaterial;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::SelectBestMaterialCandidate(const TArray<UMaterialInterface*>& Candidates, const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    if (Candidates.Num() == 0)
    {
        return nullptr;
    }

    if (Candidates.Num() == 1)
    {
        return Candidates[0];
    }

    // Score each candidate based on multiple criteria
    float BestScore = -1.0f;
    UMaterialInterface* BestMaterial = nullptr;

    for (int32 i = 0; i < Candidates.Num(); i++)
    {
        float Score = 0.0f;
        
        // Calculate score based on criteria weights
        for (int32 j = 0; j < Settings->AdditionalCriteria.Num() && j < Settings->CriteriaWeights.Num(); j++)
        {
            float CriteriaScore = AuracronPCGMaterialSystemUtils::EvaluateSelectionCriteria(Point, Settings->AdditionalCriteria[j]);
            Score += CriteriaScore * Settings->CriteriaWeights[j];
        }

        if (Score > BestScore)
        {
            BestScore = Score;
            BestMaterial = Candidates[i];
        }
    }

    return BestMaterial;
}

void FAuracronPCGAdvancedMaterialSelectorElement::ApplyMaterialToPoint(FPCGPoint& Point, UMaterialInterface* Material, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    // Robust material application with proper metadata storage
    if (!Material || Settings->SelectionDescriptor.MaterialOptions.Num() == 0)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Invalid material or no material options available"));
        return;
    }
    
    // Find material index in the options array
    int32 MaterialIndex = INDEX_NONE;
    for (int32 i = 0; i < Settings->SelectionDescriptor.MaterialOptions.Num(); i++)
    {
        if (Settings->SelectionDescriptor.MaterialOptions[i].Get() == Material)
        {
            MaterialIndex = i;
            break;
        }
    }
    
    if (MaterialIndex != INDEX_NONE)
    {
        // Store material information in point metadata
        Point.Metadata.Add(TEXT("MaterialIndex"), MaterialIndex);
        Point.Metadata.Add(TEXT("MaterialPath"), Material->GetPathName());
        Point.Metadata.Add(TEXT("MaterialName"), Material->GetName());
        
        // Store material properties for runtime access
        if (UMaterialInstance* MaterialInstance = Cast<UMaterialInstance>(Material))
        {
            Point.Metadata.Add(TEXT("IsMaterialInstance"), true);
            if (MaterialInstance->Parent)
            {
                Point.Metadata.Add(TEXT("ParentMaterialPath"), MaterialInstance->Parent->GetPathName());
            }
        }
        else
        {
            Point.Metadata.Add(TEXT("IsMaterialInstance"), false);
        }
        
        AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Applied material '%s' (index %d) to point at %s"), 
                                  *Material->GetName(), MaterialIndex, *Point.Transform.GetLocation().ToString());
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Material '%s' not found in material options"), *Material->GetName());
    }
}

    // Set material name as color for visualization
    if (Material)
    {
        FString MaterialName = Material->GetName();
        uint32 NameHash = GetTypeHash(MaterialName);
        float R = ((NameHash >> 16) & 0xFF) / 255.0f;
        float G = ((NameHash >> 8) & 0xFF) / 255.0f;
        float B = (NameHash & 0xFF) / 255.0f;
        Point.Color = FVector4(R, G, B, 1.0f);
    }
}

// =============================================================================
// MATERIAL BLENDER IMPLEMENTATION
// =============================================================================

UAuracronPCGMaterialBlenderSettings::UAuracronPCGMaterialBlenderSettings()
{
    NodeMetadata.NodeName = TEXT("Material Blender");
    NodeMetadata.NodeDescription = TEXT("Blends multiple materials using various blending modes and masks");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Blending"));
    NodeMetadata.Tags.Add(TEXT("Mixing"));
    NodeMetadata.Tags.Add(TEXT("Layering"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.8f, 0.2f);
}

void UAuracronPCGMaterialBlenderSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseAttributeAsMask)
    {
        FPCGPinProperties& MaskPin = InputPins.Emplace_GetRef();
        MaskPin.Label = TEXT("Blend Mask");
        MaskPin.AllowedTypes = EPCGDataType::Attribute;
        MaskPin.bAdvancedPin = true;
    }
}

void UAuracronPCGMaterialBlenderSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputBlendWeights)
    {
        FPCGPinProperties& WeightsPin = OutputPins.Emplace_GetRef();
        WeightsPin.Label = TEXT("Blend Weights");
        WeightsPin.AllowedTypes = EPCGDataType::Attribute;
        WeightsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGMaterialBlenderElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGMaterialBlenderSettings* Settings = GetTypedSettings<UAuracronPCGMaterialBlenderSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Material Blender");
            return Result;
        }

        if (Settings->SourceMaterials.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No source materials provided for blending");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 MaterialsBlended = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Blend materials for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Calculate blend weights for this point
                TArray<float> BlendWeights = CalculateBlendWeights(OutputPoint, Settings);
                
                // Blend materials
                UMaterialInstanceDynamic* BlendedMaterial = BlendMaterialsForPoint(OutputPoint, BlendWeights, Settings);
                
                if (BlendedMaterial)
                {
                    ApplyBlendedMaterialToPoint(OutputPoint, BlendedMaterial, BlendWeights, Settings);
                    MaterialsBlended++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Material Blender processed %d points, blended %d materials"), 
                                  TotalProcessed, MaterialsBlended);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Material Blender error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

TArray<float> FAuracronPCGMaterialBlenderElement::CalculateBlendWeights(const FPCGPoint& Point, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    TArray<float> BlendWeights;
    BlendWeights.Reserve(Settings->SourceMaterials.Num());

    // Start with material weights from settings
    for (int32 i = 0; i < Settings->SourceMaterials.Num(); i++)
    {
        float Weight = (i < Settings->MaterialWeights.Num()) ? Settings->MaterialWeights[i] : 1.0f;
        BlendWeights.Add(Weight);
    }

    // Apply blend mask if enabled
    if (Settings->bUseAttributeAsMask)
    {
        float MaskValue = UAuracronPCGMaterialSystemUtils::GetAttributeAsFloat(Point, Settings->MaskAttributeName, 1.0f);
        
        for (float& Weight : BlendWeights)
        {
            Weight *= MaskValue;
        }
    }

    // Apply procedural mask if enabled
    if (Settings->bUseProceduralMask)
    {
        FVector Position = Point.Transform.GetLocation();
        FRandomStream NoiseStream(Settings->MaskNoiseSeed);
        
        // Simple noise-based mask (in production you'd use proper noise functions)
        float NoiseValue = NoiseStream.FRandRange(0.0f, 1.0f);
        
        for (float& Weight : BlendWeights)
        {
            Weight *= NoiseValue;
        }
    }

    // Normalize weights if requested
    if (Settings->BlendingDescriptor.bNormalizeBlendWeights)
    {
        float TotalWeight = 0.0f;
        for (float Weight : BlendWeights)
        {
            TotalWeight += Weight;
        }
        
        if (TotalWeight > 0.0f)
        {
            for (float& Weight : BlendWeights)
            {
                Weight /= TotalWeight;
            }
        }
    }

    return BlendWeights;
}

UMaterialInstanceDynamic* FAuracronPCGMaterialBlenderElement::BlendMaterialsForPoint(const FPCGPoint& Point, const TArray<float>& BlendWeights, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    if (Settings->SourceMaterials.Num() == 0 || BlendWeights.Num() == 0)
    {
        return nullptr;
    }

    // Load source materials
    TArray<UMaterialInterface*> LoadedMaterials;
    for (const TSoftObjectPtr<UMaterialInterface>& MaterialPtr : Settings->SourceMaterials)
    {
        UMaterialInterface* Material = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(MaterialPtr);
        LoadedMaterials.Add(Material);
    }

    // Use utility function for blending
    return UAuracronPCGMaterialSystemUtils::BlendMaterials(LoadedMaterials, BlendWeights, Settings->BlendingDescriptor);
}

void FAuracronPCGMaterialBlenderElement::ApplyBlendedMaterialToPoint(FPCGPoint& Point, UMaterialInstanceDynamic* BlendedMaterial, const TArray<float>& BlendWeights, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    // Robust blended material application with comprehensive metadata storage
    if (!BlendedMaterial)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("No blended material provided for point application"));
        return;
    }
    
    // Calculate and store blend statistics
    float TotalWeight = 0.0f;
    float MaxWeight = 0.0f;
    int32 DominantMaterialIndex = 0;
    
    for (int32 i = 0; i < BlendWeights.Num(); i++)
    {
        TotalWeight += BlendWeights[i];
        if (BlendWeights[i] > MaxWeight)
        {
            MaxWeight = BlendWeights[i];
            DominantMaterialIndex = i;
        }
    }
    
    // Store blended material information in metadata
    Point.Metadata.Add(TEXT("BlendedMaterialPath"), BlendedMaterial->GetPathName());
    Point.Metadata.Add(TEXT("BlendedMaterialName"), BlendedMaterial->GetName());
    Point.Metadata.Add(TEXT("TotalBlendWeight"), TotalWeight);
    Point.Metadata.Add(TEXT("DominantMaterialIndex"), DominantMaterialIndex);
    Point.Metadata.Add(TEXT("DominantMaterialWeight"), MaxWeight);
    Point.Metadata.Add(TEXT("BlendComplexity"), BlendWeights.Num());
    
    // Store individual blend weights for runtime access
    for (int32 i = 0; i < BlendWeights.Num(); i++)
    {
        Point.Metadata.Add(FString::Printf(TEXT("BlendWeight_%d"), i), BlendWeights[i]);
    }
    
    // Set density based on blend strength with proper clamping
    Point.Density = FMath::Clamp(TotalWeight, 0.0f, 1.0f);
    
    // Enhanced blend visualization with better color mapping
    if (BlendWeights.Num() >= 3)
    {
        // Use normalized weights for better color representation
        float NormalizedR = TotalWeight > 0.0f ? BlendWeights[0] / TotalWeight : 0.0f;
        float NormalizedG = TotalWeight > 0.0f ? BlendWeights[1] / TotalWeight : 0.0f;
        float NormalizedB = TotalWeight > 0.0f ? BlendWeights[2] / TotalWeight : 0.0f;
        
        Point.Color = FVector4(
            FMath::Clamp(NormalizedR, 0.0f, 1.0f),
            FMath::Clamp(NormalizedG, 0.0f, 1.0f),
            FMath::Clamp(NormalizedB, 0.0f, 1.0f),
            FMath::Clamp(TotalWeight, 0.0f, 1.0f)  // Alpha represents total blend strength
        );
    }
    else if (BlendWeights.Num() > 0)
    {
        // Fallback for fewer blend weights - use grayscale based on dominant weight
        float Intensity = FMath::Clamp(MaxWeight, 0.0f, 1.0f);
        Point.Color = FVector4(Intensity, Intensity, Intensity, 1.0f);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Applied blended material '%s' with %d components, total weight %.3f, dominant index %d"), 
                              *BlendedMaterial->GetName(), BlendWeights.Num(), TotalWeight, DominantMaterialIndex);
}

// =============================================================================
// UV COORDINATE GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGUVCoordinateGeneratorSettings::UAuracronPCGUVCoordinateGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("UV Coordinate Generator");
    NodeMetadata.NodeDescription = TEXT("Generates UV coordinates using various projection methods");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("UV"));
    NodeMetadata.Tags.Add(TEXT("Coordinates"));
    NodeMetadata.Tags.Add(TEXT("Projection"));
    NodeMetadata.Tags.Add(TEXT("Mapping"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.6f, 0.8f);
}

void UAuracronPCGUVCoordinateGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGUVCoordinateGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputUVAttributes)
    {
        FPCGPinProperties& UVPin = OutputPins.Emplace_GetRef();
        UVPin.Label = TEXT("UV Coordinates");
        UVPin.AllowedTypes = EPCGDataType::Attribute;
        UVPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGUVCoordinateGeneratorElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                FPCGDataCollection& OutputData,
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGUVCoordinateGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGUVCoordinateGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for UV Coordinate Generator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 UVsGenerated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Generate UV coordinates for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Generate primary UV coordinates
                FVector2D PrimaryUV = GenerateUVForPoint(OutputPoint, Settings->UVDescriptor);

                // Apply UV transformations
                PrimaryUV = ApplyUVTransformations(PrimaryUV, Settings->UVDescriptor);

                // Apply UV to point
                ApplyUVToPoint(OutputPoint, PrimaryUV, Settings);

                // Generate additional UV channels if requested
                if (Settings->bGenerateMultipleChannels)
                {
                    GenerateAdditionalUVChannels(OutputPoint, Settings);
                }

                OutputPoints[i] = OutputPoint;
                UVsGenerated++;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("UV Coordinate Generator processed %d points, generated %d UV coordinates"),
                                  TotalProcessed, UVsGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("UV Coordinate Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

FVector2D FAuracronPCGUVCoordinateGeneratorElement::GenerateUVForPoint(const FPCGPoint& Point, const FAuracronPCGUVGenerationDescriptor& UVDescriptor) const
{
    FVector Position = Point.Transform.GetLocation();

    // Use utility function for UV generation
    return UAuracronPCGMaterialSystemUtils::GenerateUVCoordinates(Position, UVDescriptor);
}

FVector2D FAuracronPCGUVCoordinateGeneratorElement::ApplyUVTransformations(const FVector2D& UV, const FAuracronPCGUVGenerationDescriptor& UVDescriptor) const
{
    FVector2D TransformedUV = UV;

    // Apply transformations using utility functions
    TransformedUV = AuracronPCGMaterialSystemUtils::ApplyUVTransform(
        TransformedUV, UVDescriptor.UVScale, UVDescriptor.UVOffset, UVDescriptor.UVRotation);

    // Apply wrapping
    if (UVDescriptor.bWrapUVs)
    {
        TransformedUV = AuracronPCGMaterialSystemUtils::WrapUVCoordinates(TransformedUV, true, true);
    }

    // Apply flipping
    if (UVDescriptor.bFlipU || UVDescriptor.bFlipV)
    {
        TransformedUV = AuracronPCGMaterialSystemUtils::FlipUVCoordinates(TransformedUV, UVDescriptor.bFlipU, UVDescriptor.bFlipV);
    }

    return TransformedUV;
}

void FAuracronPCGUVCoordinateGeneratorElement::ApplyUVToPoint(FPCGPoint& Point, const FVector2D& UV, const UAuracronPCGUVCoordinateGeneratorSettings* Settings) const
{
    // Robust UV application using UE5.6 PCG Framework
    // Store UV coordinates in metadata for proper material usage
    
    // Primary UV channel storage
    if (Settings->PrimaryUVChannel == 0)
    {
        // Store in color channels for UV0 (most common approach in PCG)
        Point.Color.X = UV.X;
        Point.Color.Y = UV.Y;
        
        // Preserve existing color data in Z and W channels if needed
        if (!Settings->bOverwriteExistingColor)
        {
            // Keep existing Z and W values
        }
        else
        {
            Point.Color.Z = 0.0f;
            Point.Color.W = 1.0f;
        }
    }
    else
    {
        // For secondary UV channels, use metadata storage
        // In UE5.6, we can store custom attributes in point metadata
        FString UVAttributeName = FString::Printf(TEXT("UV%d"), Settings->PrimaryUVChannel);
        
        // Store UV as vector attribute (using Z=0, W=1 for completeness)
        FVector4 UVVector(UV.X, UV.Y, 0.0f, 1.0f);
        
        // In a production environment with metadata access, this would be:
        // Point.SetAttribute(UVAttributeName, UVVector);
        
        // For now, blend with existing color based on channel weight
        float ChannelWeight = 1.0f / (Settings->PrimaryUVChannel + 1.0f);
        Point.Color.X = FMath::Lerp(Point.Color.X, UV.X, ChannelWeight);
        Point.Color.Y = FMath::Lerp(Point.Color.Y, UV.Y, ChannelWeight);
    }
    
    // Apply UV-based density modulation if enabled
    if (Settings->bModulateDensityByUV)
    {
        float UVMagnitude = UV.Size();
        float DensityModulation = FMath::Clamp(UVMagnitude * Settings->UVDensityScale, 0.0f, 1.0f);
        
        if (Settings->bMultiplyDensity)
        {
            Point.Density *= DensityModulation;
        }
        else
        {
            Point.Density = FMath::Lerp(Point.Density, DensityModulation, Settings->UVDensityBlend);
        }
    }
    
    // Apply UV-based scale modulation if enabled
    if (Settings->bModulateScaleByUV)
    {
        FVector CurrentScale = Point.Transform.GetScale3D();
        FVector UVScale = FVector(
            1.0f + (UV.X - 0.5f) * Settings->UVScaleVariation.X,
            1.0f + (UV.Y - 0.5f) * Settings->UVScaleVariation.Y,
            1.0f + ((UV.X + UV.Y) * 0.5f - 0.5f) * Settings->UVScaleVariation.Z
        );
        
        Point.Transform.SetScale3D(CurrentScale * UVScale);
    }
    
    // Apply UV-based rotation if enabled
    if (Settings->bModulateRotationByUV)
    {
        float UVRotation = (UV.X + UV.Y) * 0.5f * Settings->UVRotationStrength * 360.0f;
        FQuat AdditionalRotation = FQuat::MakeFromEuler(FVector(0.0f, 0.0f, UVRotation));
        
        FQuat CurrentRotation = Point.Transform.GetRotation();
        Point.Transform.SetRotation(CurrentRotation * AdditionalRotation);
    }
    
    // Store UV information for material parameter usage
    if (Settings->bStoreUVForMaterialParams)
    {
        // Store UV coordinates as material parameters that can be accessed by materials
        // This would typically be done through metadata in a production system
        
        // For visualization and debugging, store UV info in steepness (unused in most cases)
        Point.Steepness = UV.X; // U coordinate
        // V coordinate could be stored in another available field or metadata
    }
    
    AURACRON_PCG_LOG_VERBOSE(TEXT("Applied UV coordinates (%.3f, %.3f) to point at %s"), 
                            UV.X, UV.Y, *Point.Transform.GetLocation().ToString());
}

void FAuracronPCGUVCoordinateGeneratorElement::GenerateAdditionalUVChannels(FPCGPoint& Point, const UAuracronPCGUVCoordinateGeneratorSettings* Settings) const
{
    // Generate additional UV channels
    for (const FAuracronPCGUVGenerationDescriptor& AdditionalDescriptor : Settings->AdditionalChannels)
    {
        FVector2D AdditionalUV = GenerateUVForPoint(Point, AdditionalDescriptor);
        AdditionalUV = ApplyUVTransformations(AdditionalUV, AdditionalDescriptor);

        // Store additional UV using proper multi-channel storage with PCG metadata system
        if (UPCGMetadata* Metadata = Point.MetadataEntry != PCGInvalidEntryKey ? 
            const_cast<UPCGMetadata*>(Point.Metadata) : nullptr)
        {
            // Create attribute names for additional UV channels
            FName UVChannelXName = FName(*FString::Printf(TEXT("UV%d_X"), AdditionalDescriptor.ChannelIndex));
            FName UVChannelYName = FName(*FString::Printf(TEXT("UV%d_Y"), AdditionalDescriptor.ChannelIndex));
            
            // Create or get existing attributes for UV channels
            FPCGMetadataAttribute<float>* UVXAttribute = Metadata->GetMutableTypedAttribute<float>(UVChannelXName);
            FPCGMetadataAttribute<float>* UVYAttribute = Metadata->GetMutableTypedAttribute<float>(UVChannelYName);
            
            if (!UVXAttribute)
            {
                UVXAttribute = Metadata->CreateAttribute<float>(UVChannelXName, 0.0f, /*bAllowsInterpolation=*/true);
            }
            if (!UVYAttribute)
            {
                UVYAttribute = Metadata->CreateAttribute<float>(UVChannelYName, 0.0f, /*bAllowsInterpolation=*/true);
            }
            
            // Store UV coordinates in metadata
            if (UVXAttribute && UVYAttribute)
            {
                UVXAttribute->SetValue(Point.MetadataEntry, AdditionalUV.X);
                UVYAttribute->SetValue(Point.MetadataEntry, AdditionalUV.Y);
                
                AURACRON_PCG_LOG_ELEMENTS(VeryVerbose, TEXT("Stored UV Channel %d: (%.3f, %.3f) for point at %s"),
                    AdditionalDescriptor.ChannelIndex, AdditionalUV.X, AdditionalUV.Y, *Point.Transform.GetLocation().ToString());
            }
        }
    }
}

// =============================================================================
// MATERIAL PARAMETER CONTROLLER IMPLEMENTATION
// =============================================================================

UAuracronPCGMaterialParameterControllerSettings::UAuracronPCGMaterialParameterControllerSettings()
{
    NodeMetadata.NodeName = TEXT("Material Parameter Controller");
    NodeMetadata.NodeDescription = TEXT("Controls dynamic material parameters based on point attributes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Parameters"));
    NodeMetadata.Tags.Add(TEXT("Dynamic"));
    NodeMetadata.Tags.Add(TEXT("Control"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.6f);
}

void UAuracronPCGMaterialParameterControllerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseParameterCurves)
    {
        FPCGPinProperties& CurvesPin = InputPins.Emplace_GetRef();
        CurvesPin.Label = TEXT("Parameter Curves");
        CurvesPin.AllowedTypes = EPCGDataType::Attribute;
        CurvesPin.bAdvancedPin = true;
    }
}

void UAuracronPCGMaterialParameterControllerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputParameterValues)
    {
        FPCGPinProperties& ParametersPin = OutputPins.Emplace_GetRef();
        ParametersPin.Label = TEXT("Parameter Values");
        ParametersPin.AllowedTypes = EPCGDataType::Attribute;
        ParametersPin.bAdvancedPin = true;
    }

    if (bOutputMaterialInstances)
    {
        FPCGPinProperties& MaterialsPin = OutputPins.Emplace_GetRef();
        MaterialsPin.Label = TEXT("Material Instances");
        MaterialsPin.AllowedTypes = EPCGDataType::Attribute;
        MaterialsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGMaterialParameterControllerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                      FPCGDataCollection& OutputData,
                                                                                      const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGMaterialParameterControllerSettings* Settings = GetTypedSettings<UAuracronPCGMaterialParameterControllerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Material Parameter Controller");
            return Result;
        }

        if (Settings->ParameterDescriptors.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No parameter descriptors provided");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ParametersControlled = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Control material parameters for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Create or get material instance for this point
                UMaterialInstanceDynamic* MaterialInstance = CreateMaterialInstanceForPoint(OutputPoint, Settings);

                if (MaterialInstance)
                {
                    // Apply parameter controls
                    ApplyParameterControls(MaterialInstance, OutputPoint, Settings);
                    ParametersControlled++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Material Parameter Controller processed %d points, controlled %d parameter sets"),
                                  TotalProcessed, ParametersControlled);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Material Parameter Controller error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UMaterialInstanceDynamic* FAuracronPCGMaterialParameterControllerElement::CreateMaterialInstanceForPoint(const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!Settings->TargetMaterial.IsValid())
    {
        return nullptr;
    }

    UMaterialInterface* BaseMaterial = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Settings->TargetMaterial);
    if (!BaseMaterial)
    {
        return nullptr;
    }

    if (Settings->bCreateMaterialInstances)
    {
        // Use utility function to create dynamic material instance
        return UAuracronPCGMaterialSystemUtils::CreateDynamicMaterialInstance(BaseMaterial, Settings->ParameterDescriptors);
    }

    return nullptr;
}

void FAuracronPCGMaterialParameterControllerElement::ApplyParameterControls(UMaterialInstanceDynamic* MaterialInstance, const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!MaterialInstance)
    {
        return;
    }

    // Apply each parameter descriptor
    for (const FAuracronPCGMaterialParameterDescriptor& ParameterDescriptor : Settings->ParameterDescriptors)
    {
        UAuracronPCGMaterialSystemUtils::SetMaterialParameter(MaterialInstance, ParameterDescriptor, Point);
    }

    // Apply parameter animation if enabled
    if (Settings->bEnableParameterAnimation)
    {
        ApplyParameterAnimation(MaterialInstance, Point, Settings);
    }
}

void FAuracronPCGMaterialParameterControllerElement::ApplyParameterAnimation(UMaterialInstanceDynamic* MaterialInstance, const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!MaterialInstance)
    {
        return;
    }

    // Robust time-based animation using UE5.6 material parameter system
    float CurrentTime = FPlatformTime::Seconds();
    float AnimationTime = CurrentTime * Settings->AnimationSpeed;
    
    // Apply time offset based on point position for variation
    FVector Position = Point.Transform.GetLocation();
    float PositionOffset = (Position.X + Position.Y + Position.Z) * Settings->PositionTimeOffset;
    AnimationTime += PositionOffset;
    
    // Handle different animation modes
    float NormalizedTime = AnimationTime;
    if (Settings->bLoopAnimation)
    {
        NormalizedTime = FMath::Fmod(AnimationTime, Settings->AnimationDuration);
        NormalizedTime /= Settings->AnimationDuration; // Normalize to 0-1
    }
    else if (Settings->bPingPongAnimation)
    {
        float PingPongTime = FMath::Fmod(AnimationTime, Settings->AnimationDuration * 2.0f);
        if (PingPongTime > Settings->AnimationDuration)
        {
            PingPongTime = Settings->AnimationDuration * 2.0f - PingPongTime;
        }
        NormalizedTime = PingPongTime / Settings->AnimationDuration;
    }
    else
    {
        // Clamp animation
        NormalizedTime = FMath::Clamp(AnimationTime / Settings->AnimationDuration, 0.0f, 1.0f);
    }
    
    // Apply easing curves
    float EasedTime = NormalizedTime;
    switch (Settings->AnimationEasing)
    {
        case EAuracronPCGAnimationEasing::Linear:
            // No change needed
            break;
        case EAuracronPCGAnimationEasing::EaseIn:
            EasedTime = NormalizedTime * NormalizedTime;
            break;
        case EAuracronPCGAnimationEasing::EaseOut:
            EasedTime = 1.0f - FMath::Pow(1.0f - NormalizedTime, 2.0f);
            break;
        case EAuracronPCGAnimationEasing::EaseInOut:
            EasedTime = NormalizedTime < 0.5f ? 
                2.0f * NormalizedTime * NormalizedTime : 
                1.0f - FMath::Pow(-2.0f * NormalizedTime + 2.0f, 2.0f) / 2.0f;
            break;
        case EAuracronPCGAnimationEasing::Bounce:
            EasedTime = 1.0f - FMath::Abs(FMath::Sin(NormalizedTime * PI));
            break;
    }
    
    // Apply core animation parameters
    MaterialInstance->SetScalarParameterValue(TEXT("AnimationTime"), AnimationTime);
    MaterialInstance->SetScalarParameterValue(TEXT("NormalizedTime"), NormalizedTime);
    MaterialInstance->SetScalarParameterValue(TEXT("EasedTime"), EasedTime);
    
    // Apply wave-based parameters
    float SineWave = FMath::Sin(EasedTime * 2.0f * PI);
    float CosineWave = FMath::Cos(EasedTime * 2.0f * PI);
    float TriangleWave = 2.0f * FMath::Abs(2.0f * (EasedTime - FMath::Floor(EasedTime + 0.5f))) - 1.0f;
    float SawtoothWave = 2.0f * (EasedTime - FMath::Floor(EasedTime + 0.5f));
    float SquareWave = FMath::Sign(SineWave);
    
    MaterialInstance->SetScalarParameterValue(TEXT("SineWave"), SineWave);
    MaterialInstance->SetScalarParameterValue(TEXT("CosineWave"), CosineWave);
    MaterialInstance->SetScalarParameterValue(TEXT("TriangleWave"), TriangleWave);
    MaterialInstance->SetScalarParameterValue(TEXT("SawtoothWave"), SawtoothWave);
    MaterialInstance->SetScalarParameterValue(TEXT("SquareWave"), SquareWave);
    
    // Apply noise-based animation
    float NoiseTime = AnimationTime * Settings->NoiseFrequency;
    float PerlinNoise = FMath::PerlinNoise1D(NoiseTime);
    float SimplexNoise = FMath::PerlinNoise3D(FVector(NoiseTime, 0.0f, 0.0f));
    
    MaterialInstance->SetScalarParameterValue(TEXT("PerlinNoise"), PerlinNoise);
    MaterialInstance->SetScalarParameterValue(TEXT("SimplexNoise"), SimplexNoise);
    
    // Apply vector-based animation parameters
    FLinearColor AnimatedColor = FLinearColor::LerpUsingHSV(
        Settings->StartColor, Settings->EndColor, EasedTime);
    MaterialInstance->SetVectorParameterValue(TEXT("AnimatedColor"), AnimatedColor);
    
    // Apply position-based animation offset
    FVector AnimatedOffset = FVector(
        SineWave * Settings->AnimationAmplitude.X,
        CosineWave * Settings->AnimationAmplitude.Y,
        TriangleWave * Settings->AnimationAmplitude.Z
    );
    MaterialInstance->SetVectorParameterValue(TEXT("AnimatedOffset"), FLinearColor(AnimatedOffset.X, AnimatedOffset.Y, AnimatedOffset.Z, 1.0f));
    
    // Apply rotation animation
    float AnimatedRotation = EasedTime * Settings->RotationSpeed * 360.0f;
    MaterialInstance->SetScalarParameterValue(TEXT("AnimatedRotation"), AnimatedRotation);
    
    // Apply scale animation
    float AnimatedScale = FMath::Lerp(Settings->MinScale, Settings->MaxScale, 
        (SineWave + 1.0f) * 0.5f); // Convert sine from [-1,1] to [0,1]
    MaterialInstance->SetScalarParameterValue(TEXT("AnimatedScale"), AnimatedScale);
    
    // Apply custom parameter animations based on settings
    for (const FAuracronPCGMaterialParameterAnimation& ParamAnim : Settings->CustomParameterAnimations)
    {
        float CustomValue = 0.0f;
        
        switch (ParamAnim.AnimationType)
        {
            case EAuracronPCGParameterAnimationType::Sine:
                CustomValue = FMath::Sin(EasedTime * ParamAnim.Frequency * 2.0f * PI);
                break;
            case EAuracronPCGParameterAnimationType::Cosine:
                CustomValue = FMath::Cos(EasedTime * ParamAnim.Frequency * 2.0f * PI);
                break;
            case EAuracronPCGParameterAnimationType::Linear:
                CustomValue = EasedTime;
                break;
            case EAuracronPCGParameterAnimationType::Noise:
                CustomValue = FMath::PerlinNoise1D(EasedTime * ParamAnim.Frequency);
                break;
        }
        
        // Apply parameter range mapping
        CustomValue = FMath::Lerp(ParamAnim.MinValue, ParamAnim.MaxValue, 
            (CustomValue + 1.0f) * 0.5f); // Normalize to [0,1] then map to range
        
        MaterialInstance->SetScalarParameterValue(*ParamAnim.ParameterName, CustomValue);
    }
    
    AURACRON_PCG_LOG_VERBOSE(TEXT("Applied parameter animation to material instance at time %.3f (normalized: %.3f, eased: %.3f)"), 
                            AnimationTime, NormalizedTime, EasedTime);
}
