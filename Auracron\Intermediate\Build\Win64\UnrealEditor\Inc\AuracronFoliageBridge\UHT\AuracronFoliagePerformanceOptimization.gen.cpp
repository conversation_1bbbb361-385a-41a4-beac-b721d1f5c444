// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliagePerformanceOptimization.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliagePerformanceOptimization() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCullingPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGPUPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronOverallPerformanceData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronCullingStrategy **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCullingStrategy;
static UEnum* EAuracronCullingStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCullingStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCullingStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronCullingStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronCullingStrategy.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronCullingStrategy>()
{
	return EAuracronCullingStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive Culling" },
		{ "Adaptive.Name", "EAuracronCullingStrategy::Adaptive" },
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined Culling" },
		{ "Combined.Name", "EAuracronCullingStrategy::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling strategy\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronCullingStrategy::Custom" },
		{ "DistanceOnly.DisplayName", "Distance Only" },
		{ "DistanceOnly.Name", "EAuracronCullingStrategy::DistanceOnly" },
		{ "FrustumOnly.DisplayName", "Frustum Only" },
		{ "FrustumOnly.Name", "EAuracronCullingStrategy::FrustumOnly" },
		{ "GPU.DisplayName", "GPU Culling" },
		{ "GPU.Name", "EAuracronCullingStrategy::GPU" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronCullingStrategy::None" },
		{ "OcclusionOnly.DisplayName", "Occlusion Only" },
		{ "OcclusionOnly.Name", "EAuracronCullingStrategy::OcclusionOnly" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling strategy" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCullingStrategy::None", (int64)EAuracronCullingStrategy::None },
		{ "EAuracronCullingStrategy::FrustumOnly", (int64)EAuracronCullingStrategy::FrustumOnly },
		{ "EAuracronCullingStrategy::OcclusionOnly", (int64)EAuracronCullingStrategy::OcclusionOnly },
		{ "EAuracronCullingStrategy::DistanceOnly", (int64)EAuracronCullingStrategy::DistanceOnly },
		{ "EAuracronCullingStrategy::Combined", (int64)EAuracronCullingStrategy::Combined },
		{ "EAuracronCullingStrategy::Adaptive", (int64)EAuracronCullingStrategy::Adaptive },
		{ "EAuracronCullingStrategy::GPU", (int64)EAuracronCullingStrategy::GPU },
		{ "EAuracronCullingStrategy::Custom", (int64)EAuracronCullingStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronCullingStrategy",
	"EAuracronCullingStrategy",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronCullingStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCullingStrategy.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCullingStrategy.InnerSingleton;
}
// ********** End Enum EAuracronCullingStrategy ****************************************************

// ********** Begin Enum EAuracronBatchingStrategy *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronBatchingStrategy;
static UEnum* EAuracronBatchingStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronBatchingStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronBatchingStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronBatchingStrategy"));
	}
	return Z_Registration_Info_UEnum_EAuracronBatchingStrategy.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronBatchingStrategy>()
{
	return EAuracronBatchingStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batching strategy\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronBatchingStrategy::Custom" },
		{ "Dynamic.DisplayName", "Dynamic Batching" },
		{ "Dynamic.Name", "EAuracronBatchingStrategy::Dynamic" },
		{ "GPU.DisplayName", "GPU Batching" },
		{ "GPU.Name", "EAuracronBatchingStrategy::GPU" },
		{ "Hybrid.DisplayName", "Hybrid Batching" },
		{ "Hybrid.Name", "EAuracronBatchingStrategy::Hybrid" },
		{ "LOD.DisplayName", "LOD Batching" },
		{ "LOD.Name", "EAuracronBatchingStrategy::LOD" },
		{ "Material.DisplayName", "Material Batching" },
		{ "Material.Name", "EAuracronBatchingStrategy::Material" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronBatchingStrategy::None" },
		{ "Spatial.DisplayName", "Spatial Batching" },
		{ "Spatial.Name", "EAuracronBatchingStrategy::Spatial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batching strategy" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronBatchingStrategy::None", (int64)EAuracronBatchingStrategy::None },
		{ "EAuracronBatchingStrategy::Spatial", (int64)EAuracronBatchingStrategy::Spatial },
		{ "EAuracronBatchingStrategy::Material", (int64)EAuracronBatchingStrategy::Material },
		{ "EAuracronBatchingStrategy::LOD", (int64)EAuracronBatchingStrategy::LOD },
		{ "EAuracronBatchingStrategy::Hybrid", (int64)EAuracronBatchingStrategy::Hybrid },
		{ "EAuracronBatchingStrategy::Dynamic", (int64)EAuracronBatchingStrategy::Dynamic },
		{ "EAuracronBatchingStrategy::GPU", (int64)EAuracronBatchingStrategy::GPU },
		{ "EAuracronBatchingStrategy::Custom", (int64)EAuracronBatchingStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronBatchingStrategy",
	"EAuracronBatchingStrategy",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy()
{
	if (!Z_Registration_Info_UEnum_EAuracronBatchingStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronBatchingStrategy.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronBatchingStrategy.InnerSingleton;
}
// ********** End Enum EAuracronBatchingStrategy ***************************************************

// ********** Begin Enum EAuracronGPUInstancingMode ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGPUInstancingMode;
static UEnum* EAuracronGPUInstancingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronGPUInstancingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronGPUInstancingMode>()
{
	return EAuracronGPUInstancingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive Instancing" },
		{ "Adaptive.Name", "EAuracronGPUInstancingMode::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU instancing mode\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronGPUInstancingMode::Custom" },
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EAuracronGPUInstancingMode::Disabled" },
		{ "GPUDriven.DisplayName", "GPU Driven Rendering" },
		{ "GPUDriven.Name", "EAuracronGPUInstancingMode::GPUDriven" },
		{ "Hierarchical.DisplayName", "Hierarchical Instancing" },
		{ "Hierarchical.Name", "EAuracronGPUInstancingMode::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
		{ "Nanite.DisplayName", "Nanite Instancing" },
		{ "Nanite.Name", "EAuracronGPUInstancingMode::Nanite" },
		{ "Standard.DisplayName", "Standard Instancing" },
		{ "Standard.Name", "EAuracronGPUInstancingMode::Standard" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU instancing mode" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGPUInstancingMode::Disabled", (int64)EAuracronGPUInstancingMode::Disabled },
		{ "EAuracronGPUInstancingMode::Standard", (int64)EAuracronGPUInstancingMode::Standard },
		{ "EAuracronGPUInstancingMode::Hierarchical", (int64)EAuracronGPUInstancingMode::Hierarchical },
		{ "EAuracronGPUInstancingMode::GPUDriven", (int64)EAuracronGPUInstancingMode::GPUDriven },
		{ "EAuracronGPUInstancingMode::Nanite", (int64)EAuracronGPUInstancingMode::Nanite },
		{ "EAuracronGPUInstancingMode::Adaptive", (int64)EAuracronGPUInstancingMode::Adaptive },
		{ "EAuracronGPUInstancingMode::Custom", (int64)EAuracronGPUInstancingMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronGPUInstancingMode",
	"EAuracronGPUInstancingMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGPUInstancingMode.InnerSingleton;
}
// ********** End Enum EAuracronGPUInstancingMode **************************************************

// ********** Begin Enum EAuracronPerformanceTier **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPerformanceTier;
static UEnum* EAuracronPerformanceTier_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceTier.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPerformanceTier.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronPerformanceTier"));
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceTier.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronPerformanceTier>()
{
	return EAuracronPerformanceTier_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive Performance" },
		{ "Adaptive.Name", "EAuracronPerformanceTier::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance tier\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPerformanceTier::Custom" },
		{ "High.DisplayName", "High Performance" },
		{ "High.Name", "EAuracronPerformanceTier::High" },
		{ "Low.DisplayName", "Low Performance" },
		{ "Low.Name", "EAuracronPerformanceTier::Low" },
		{ "Medium.DisplayName", "Medium Performance" },
		{ "Medium.Name", "EAuracronPerformanceTier::Medium" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance tier" },
#endif
		{ "Ultra.DisplayName", "Ultra Performance" },
		{ "Ultra.Name", "EAuracronPerformanceTier::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPerformanceTier::Low", (int64)EAuracronPerformanceTier::Low },
		{ "EAuracronPerformanceTier::Medium", (int64)EAuracronPerformanceTier::Medium },
		{ "EAuracronPerformanceTier::High", (int64)EAuracronPerformanceTier::High },
		{ "EAuracronPerformanceTier::Ultra", (int64)EAuracronPerformanceTier::Ultra },
		{ "EAuracronPerformanceTier::Adaptive", (int64)EAuracronPerformanceTier::Adaptive },
		{ "EAuracronPerformanceTier::Custom", (int64)EAuracronPerformanceTier::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronPerformanceTier",
	"EAuracronPerformanceTier",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier()
{
	if (!Z_Registration_Info_UEnum_EAuracronPerformanceTier.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPerformanceTier.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPerformanceTier.InnerSingleton;
}
// ********** End Enum EAuracronPerformanceTier ****************************************************

// ********** Begin ScriptStruct FAuracronFoliagePerformanceOptimizationConfiguration **************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration;
class UScriptStruct* FAuracronFoliagePerformanceOptimizationConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliagePerformanceOptimizationConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Performance Optimization Configuration\n * Configuration for maximum performance optimization\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Performance Optimization Configuration\nConfiguration for maximum performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceOptimization_MetaData[] = {
		{ "Category", "Performance System" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceTier_MetaData[] = {
		{ "Category", "Performance System" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedCulling_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingStrategy_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFrustumCulling_MetaData[] = {
		{ "Category", "Frustum Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustumCullingMargin_MetaData[] = {
		{ "Category", "Frustum Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseConservativeFrustum_MetaData[] = {
		{ "Category", "Frustum Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusionCulling_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionCullingAccuracy_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionQueryBudget_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHierarchicalOcclusion_MetaData[] = {
		{ "Category", "Occlusion Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatchingOptimization_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchingStrategy_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerBatch_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchingRadius_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDynamicBatching_MetaData[] = {
		{ "Category", "Batching" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUInstancing_MetaData[] = {
		{ "Category", "GPU Instancing" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUInstancingMode_MetaData[] = {
		{ "Category", "GPU Instancing" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGPUInstances_MetaData[] = {
		{ "Category", "GPU Instancing" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUCulling_MetaData[] = {
		{ "Category", "GPU Instancing" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNaniteSupport_MetaData[] = {
		{ "Category", "GPU Instancing" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMemoryOptimization_MetaData[] = {
		{ "Category", "Memory Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryBudgetMB_MetaData[] = {
		{ "Category", "Memory Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceDataCompression_MetaData[] = {
		{ "Category", "Memory Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGeometryCompression_MetaData[] = {
		{ "Category", "Memory Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMultithreading_MetaData[] = {
		{ "Category", "Threading" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorkerThreadCount_MetaData[] = {
		{ "Category", "Threading" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncCulling_MetaData[] = {
		{ "Category", "Threading" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncBatching_MetaData[] = {
		{ "Category", "Threading" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptivePerformance_MetaData[] = {
		{ "Category", "Adaptive Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFrameRate_MetaData[] = {
		{ "Category", "Adaptive Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceThreshold_MetaData[] = {
		{ "Category", "Adaptive Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoAdjustQuality_MetaData[] = {
		{ "Category", "Adaptive Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingUpdateInterval_MetaData[] = {
		{ "Category", "Update Intervals" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchingUpdateInterval_MetaData[] = {
		{ "Category", "Update Intervals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 30 FPS\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "30 FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMonitoringInterval_MetaData[] = {
		{ "Category", "Update Intervals" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowCullingStats_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowBatchingStats_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowGPUStats_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnablePerformanceOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceOptimization;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PerformanceTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PerformanceTier;
	static void NewProp_bEnableAdvancedCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedCulling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CullingStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CullingStrategy;
	static void NewProp_bEnableFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFrustumCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrustumCullingMargin;
	static void NewProp_bUseConservativeFrustum_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseConservativeFrustum;
	static void NewProp_bEnableOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusionCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OcclusionCullingAccuracy;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OcclusionQueryBudget;
	static void NewProp_bUseHierarchicalOcclusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHierarchicalOcclusion;
	static void NewProp_bEnableBatchingOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatchingOptimization;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BatchingStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BatchingStrategy;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerBatch;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchingRadius;
	static void NewProp_bEnableDynamicBatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDynamicBatching;
	static void NewProp_bEnableGPUInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUInstancing;
	static const UECodeGen_Private::FBytePropertyParams NewProp_GPUInstancingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GPUInstancingMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxGPUInstances;
	static void NewProp_bUseGPUCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUCulling;
	static void NewProp_bEnableNaniteSupport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNaniteSupport;
	static void NewProp_bEnableMemoryOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMemoryOptimization;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryBudgetMB;
	static void NewProp_bEnableInstanceDataCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceDataCompression;
	static void NewProp_bEnableGeometryCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGeometryCompression;
	static void NewProp_bEnableMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WorkerThreadCount;
	static void NewProp_bEnableAsyncCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncCulling;
	static void NewProp_bEnableAsyncBatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncBatching;
	static void NewProp_bEnableAdaptivePerformance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptivePerformance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceThreshold;
	static void NewProp_bAutoAdjustQuality_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoAdjustQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchingUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMonitoringInterval;
	static void NewProp_bEnablePerformanceDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceDebug;
	static void NewProp_bShowCullingStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowCullingStats;
	static void NewProp_bShowBatchingStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowBatchingStats;
	static void NewProp_bShowGPUStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowGPUStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliagePerformanceOptimizationConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceOptimization_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnablePerformanceOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceOptimization = { "bEnablePerformanceOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceOptimization_MetaData), NewProp_bEnablePerformanceOptimization_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceTier = { "PerformanceTier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, PerformanceTier), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceTier_MetaData), NewProp_PerformanceTier_MetaData) }; // 1546457737
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdvancedCulling_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableAdvancedCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdvancedCulling = { "bEnableAdvancedCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdvancedCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedCulling_MetaData), NewProp_bEnableAdvancedCulling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingStrategy = { "CullingStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, CullingStrategy), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingStrategy_MetaData), NewProp_CullingStrategy_MetaData) }; // 2534998785
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableFrustumCulling = { "bEnableFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFrustumCulling_MetaData), NewProp_bEnableFrustumCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_FrustumCullingMargin = { "FrustumCullingMargin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, FrustumCullingMargin), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustumCullingMargin_MetaData), NewProp_FrustumCullingMargin_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseConservativeFrustum_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bUseConservativeFrustum = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseConservativeFrustum = { "bUseConservativeFrustum", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseConservativeFrustum_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseConservativeFrustum_MetaData), NewProp_bUseConservativeFrustum_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableOcclusionCulling = { "bEnableOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusionCulling_MetaData), NewProp_bEnableOcclusionCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_OcclusionCullingAccuracy = { "OcclusionCullingAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, OcclusionCullingAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionCullingAccuracy_MetaData), NewProp_OcclusionCullingAccuracy_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_OcclusionQueryBudget = { "OcclusionQueryBudget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, OcclusionQueryBudget), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionQueryBudget_MetaData), NewProp_OcclusionQueryBudget_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseHierarchicalOcclusion_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bUseHierarchicalOcclusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseHierarchicalOcclusion = { "bUseHierarchicalOcclusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseHierarchicalOcclusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHierarchicalOcclusion_MetaData), NewProp_bUseHierarchicalOcclusion_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableBatchingOptimization_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableBatchingOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableBatchingOptimization = { "bEnableBatchingOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableBatchingOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatchingOptimization_MetaData), NewProp_bEnableBatchingOptimization_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingStrategy = { "BatchingStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, BatchingStrategy), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronBatchingStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchingStrategy_MetaData), NewProp_BatchingStrategy_MetaData) }; // 1102691889
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MaxInstancesPerBatch = { "MaxInstancesPerBatch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, MaxInstancesPerBatch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerBatch_MetaData), NewProp_MaxInstancesPerBatch_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingRadius = { "BatchingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, BatchingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchingRadius_MetaData), NewProp_BatchingRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableDynamicBatching_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableDynamicBatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableDynamicBatching = { "bEnableDynamicBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableDynamicBatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDynamicBatching_MetaData), NewProp_bEnableDynamicBatching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGPUInstancing_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableGPUInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGPUInstancing = { "bEnableGPUInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGPUInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUInstancing_MetaData), NewProp_bEnableGPUInstancing_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_GPUInstancingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_GPUInstancingMode = { "GPUInstancingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, GPUInstancingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUInstancingMode_MetaData), NewProp_GPUInstancingMode_MetaData) }; // 1308130164
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MaxGPUInstances = { "MaxGPUInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, MaxGPUInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGPUInstances_MetaData), NewProp_MaxGPUInstances_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseGPUCulling_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bUseGPUCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseGPUCulling = { "bUseGPUCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseGPUCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUCulling_MetaData), NewProp_bUseGPUCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableNaniteSupport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableNaniteSupport = { "bEnableNaniteSupport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNaniteSupport_MetaData), NewProp_bEnableNaniteSupport_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMemoryOptimization_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableMemoryOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMemoryOptimization = { "bEnableMemoryOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMemoryOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMemoryOptimization_MetaData), NewProp_bEnableMemoryOptimization_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MemoryBudgetMB = { "MemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, MemoryBudgetMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryBudgetMB_MetaData), NewProp_MemoryBudgetMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableInstanceDataCompression_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableInstanceDataCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableInstanceDataCompression = { "bEnableInstanceDataCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableInstanceDataCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceDataCompression_MetaData), NewProp_bEnableInstanceDataCompression_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGeometryCompression_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableGeometryCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGeometryCompression = { "bEnableGeometryCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGeometryCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGeometryCompression_MetaData), NewProp_bEnableGeometryCompression_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMultithreading_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMultithreading = { "bEnableMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMultithreading_MetaData), NewProp_bEnableMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_WorkerThreadCount = { "WorkerThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, WorkerThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorkerThreadCount_MetaData), NewProp_WorkerThreadCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncCulling_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableAsyncCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncCulling = { "bEnableAsyncCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncCulling_MetaData), NewProp_bEnableAsyncCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncBatching_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableAsyncBatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncBatching = { "bEnableAsyncBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncBatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncBatching_MetaData), NewProp_bEnableAsyncBatching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdaptivePerformance_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnableAdaptivePerformance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdaptivePerformance = { "bEnableAdaptivePerformance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdaptivePerformance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptivePerformance_MetaData), NewProp_bEnableAdaptivePerformance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_TargetFrameRate = { "TargetFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, TargetFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFrameRate_MetaData), NewProp_TargetFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceThreshold = { "PerformanceThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, PerformanceThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceThreshold_MetaData), NewProp_PerformanceThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bAutoAdjustQuality_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bAutoAdjustQuality = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bAutoAdjustQuality = { "bAutoAdjustQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bAutoAdjustQuality_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoAdjustQuality_MetaData), NewProp_bAutoAdjustQuality_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingUpdateInterval = { "CullingUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, CullingUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingUpdateInterval_MetaData), NewProp_CullingUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingUpdateInterval = { "BatchingUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, BatchingUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchingUpdateInterval_MetaData), NewProp_BatchingUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceMonitoringInterval = { "PerformanceMonitoringInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliagePerformanceOptimizationConfiguration, PerformanceMonitoringInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMonitoringInterval_MetaData), NewProp_PerformanceMonitoringInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceDebug_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bEnablePerformanceDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceDebug = { "bEnablePerformanceDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceDebug_MetaData), NewProp_bEnablePerformanceDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowCullingStats_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bShowCullingStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowCullingStats = { "bShowCullingStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowCullingStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowCullingStats_MetaData), NewProp_bShowCullingStats_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowBatchingStats_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bShowBatchingStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowBatchingStats = { "bShowBatchingStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowBatchingStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowBatchingStats_MetaData), NewProp_bShowBatchingStats_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowGPUStats_SetBit(void* Obj)
{
	((FAuracronFoliagePerformanceOptimizationConfiguration*)Obj)->bShowGPUStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowGPUStats = { "bShowGPUStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowGPUStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowGPUStats_MetaData), NewProp_bShowGPUStats_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdvancedCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_FrustumCullingMargin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseConservativeFrustum,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_OcclusionCullingAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_OcclusionQueryBudget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseHierarchicalOcclusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableBatchingOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MaxInstancesPerBatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableDynamicBatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGPUInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_GPUInstancingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_GPUInstancingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MaxGPUInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bUseGPUCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableNaniteSupport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMemoryOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_MemoryBudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableInstanceDataCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableGeometryCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_WorkerThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAsyncBatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnableAdaptivePerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_TargetFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bAutoAdjustQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_CullingUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_BatchingUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_PerformanceMonitoringInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bEnablePerformanceDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowCullingStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowBatchingStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewProp_bShowGPUStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliagePerformanceOptimizationConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliagePerformanceOptimizationConfiguration),
	alignof(FAuracronFoliagePerformanceOptimizationConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliagePerformanceOptimizationConfiguration ****************

// ********** Begin ScriptStruct FAuracronCullingPerformanceData ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData;
class UScriptStruct* FAuracronCullingPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCullingPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronCullingPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Culling Performance Data\n * Performance metrics for culling operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling Performance Data\nPerformance metrics for culling operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalInstances_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibleInstances_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustumCulledInstances_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionCulledInstances_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceCulledInstances_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingTimeMs_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustumCullingTimeMs_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionCullingTimeMs_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OcclusionQueriesUsed_MetaData[] = {
		{ "Category", "Culling Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VisibleInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FrustumCulledInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OcclusionCulledInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DistanceCulledInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrustumCullingTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OcclusionCullingTimeMs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OcclusionQueriesUsed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCullingPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_TotalInstances = { "TotalInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, TotalInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalInstances_MetaData), NewProp_TotalInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_VisibleInstances = { "VisibleInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, VisibleInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibleInstances_MetaData), NewProp_VisibleInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_FrustumCulledInstances = { "FrustumCulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, FrustumCulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustumCulledInstances_MetaData), NewProp_FrustumCulledInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionCulledInstances = { "OcclusionCulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, OcclusionCulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionCulledInstances_MetaData), NewProp_OcclusionCulledInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_DistanceCulledInstances = { "DistanceCulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, DistanceCulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceCulledInstances_MetaData), NewProp_DistanceCulledInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_CullingTimeMs = { "CullingTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, CullingTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingTimeMs_MetaData), NewProp_CullingTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_FrustumCullingTimeMs = { "FrustumCullingTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, FrustumCullingTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustumCullingTimeMs_MetaData), NewProp_FrustumCullingTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionCullingTimeMs = { "OcclusionCullingTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, OcclusionCullingTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionCullingTimeMs_MetaData), NewProp_OcclusionCullingTimeMs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionQueriesUsed = { "OcclusionQueriesUsed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCullingPerformanceData, OcclusionQueriesUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OcclusionQueriesUsed_MetaData), NewProp_OcclusionQueriesUsed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_TotalInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_VisibleInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_FrustumCulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionCulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_DistanceCulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_CullingTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_FrustumCullingTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionCullingTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewProp_OcclusionQueriesUsed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronCullingPerformanceData",
	Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::PropPointers),
	sizeof(FAuracronCullingPerformanceData),
	alignof(FAuracronCullingPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCullingPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCullingPerformanceData *************************************

// ********** Begin ScriptStruct FAuracronBatchingPerformanceData **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData;
class UScriptStruct* FAuracronBatchingPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronBatchingPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Batching Performance Data\n * Performance metrics for batching operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batching Performance Data\nPerformance metrics for batching operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalBatches_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveBatches_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizedBatches_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageInstancesPerBatch_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchingTimeMs_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchOptimizationTimeMs_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancedDrawCalls_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Batching Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalBatches;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveBatches;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OptimizedBatches;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageInstancesPerBatch;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchingTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchOptimizationTimeMs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstancedDrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBatchingPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_TotalBatches = { "TotalBatches", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, TotalBatches), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalBatches_MetaData), NewProp_TotalBatches_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_ActiveBatches = { "ActiveBatches", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, ActiveBatches), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveBatches_MetaData), NewProp_ActiveBatches_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_OptimizedBatches = { "OptimizedBatches", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, OptimizedBatches), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizedBatches_MetaData), NewProp_OptimizedBatches_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_AverageInstancesPerBatch = { "AverageInstancesPerBatch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, AverageInstancesPerBatch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageInstancesPerBatch_MetaData), NewProp_AverageInstancesPerBatch_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_BatchingTimeMs = { "BatchingTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, BatchingTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchingTimeMs_MetaData), NewProp_BatchingTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_BatchOptimizationTimeMs = { "BatchOptimizationTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, BatchOptimizationTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchOptimizationTimeMs_MetaData), NewProp_BatchOptimizationTimeMs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_InstancedDrawCalls = { "InstancedDrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, InstancedDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancedDrawCalls_MetaData), NewProp_InstancedDrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBatchingPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_TotalBatches,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_ActiveBatches,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_OptimizedBatches,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_AverageInstancesPerBatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_BatchingTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_BatchOptimizationTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_InstancedDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronBatchingPerformanceData",
	Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::PropPointers),
	sizeof(FAuracronBatchingPerformanceData),
	alignof(FAuracronBatchingPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBatchingPerformanceData ************************************

// ********** Begin ScriptStruct FAuracronGPUPerformanceData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData;
class UScriptStruct* FAuracronGPUPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGPUPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronGPUPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * GPU Performance Data\n * Performance metrics for GPU operations\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU Performance Data\nPerformance metrics for GPU operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUInstances_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUCulledInstances_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUTimeMs_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUCullingTimeMs_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsageMB_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticesRendered_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrianglesRendered_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNaniteEnabled_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteTriangles_MetaData[] = {
		{ "Category", "GPU Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GPUInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GPUCulledInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUCullingTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VerticesRendered;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TrianglesRendered;
	static void NewProp_bNaniteEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNaniteEnabled;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NaniteTriangles;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGPUPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUInstances = { "GPUInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, GPUInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUInstances_MetaData), NewProp_GPUInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUCulledInstances = { "GPUCulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, GPUCulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUCulledInstances_MetaData), NewProp_GPUCulledInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUTimeMs = { "GPUTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, GPUTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUTimeMs_MetaData), NewProp_GPUTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUCullingTimeMs = { "GPUCullingTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, GPUCullingTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUCullingTimeMs_MetaData), NewProp_GPUCullingTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUMemoryUsageMB = { "GPUMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, GPUMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsageMB_MetaData), NewProp_GPUMemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_VerticesRendered = { "VerticesRendered", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, VerticesRendered), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticesRendered_MetaData), NewProp_VerticesRendered_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_TrianglesRendered = { "TrianglesRendered", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, TrianglesRendered), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrianglesRendered_MetaData), NewProp_TrianglesRendered_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_bNaniteEnabled_SetBit(void* Obj)
{
	((FAuracronGPUPerformanceData*)Obj)->bNaniteEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_bNaniteEnabled = { "bNaniteEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGPUPerformanceData), &Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_bNaniteEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNaniteEnabled_MetaData), NewProp_bNaniteEnabled_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_NaniteTriangles = { "NaniteTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGPUPerformanceData, NaniteTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteTriangles_MetaData), NewProp_NaniteTriangles_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUCulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUCullingTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_GPUMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_VerticesRendered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_TrianglesRendered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_bNaniteEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewProp_NaniteTriangles,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronGPUPerformanceData",
	Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::PropPointers),
	sizeof(FAuracronGPUPerformanceData),
	alignof(FAuracronGPUPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGPUPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGPUPerformanceData *****************************************

// ********** Begin ScriptStruct FAuracronOverallPerformanceData ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData;
class UScriptStruct* FAuracronOverallPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronOverallPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronOverallPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Overall Performance Data\n * Combined performance metrics\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Overall Performance Data\nCombined performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingData_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchingData_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUData_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalFrameTimeMs_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFPS_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFPS_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPerformanceTier_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPerformanceOptimal_MetaData[] = {
		{ "Category", "Overall Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CullingData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BatchingData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GPUData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalFrameTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPerformanceTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPerformanceTier;
	static void NewProp_bIsPerformanceOptimal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPerformanceOptimal;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronOverallPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CullingData = { "CullingData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, CullingData), Z_Construct_UScriptStruct_FAuracronCullingPerformanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingData_MetaData), NewProp_CullingData_MetaData) }; // 2725127186
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_BatchingData = { "BatchingData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, BatchingData), Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchingData_MetaData), NewProp_BatchingData_MetaData) }; // 3498980797
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_GPUData = { "GPUData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, GPUData), Z_Construct_UScriptStruct_FAuracronGPUPerformanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUData_MetaData), NewProp_GPUData_MetaData) }; // 1291392199
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_TotalFrameTimeMs = { "TotalFrameTimeMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, TotalFrameTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalFrameTimeMs_MetaData), NewProp_TotalFrameTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, CurrentFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFPS_MetaData), NewProp_CurrentFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_AverageFPS = { "AverageFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, AverageFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFPS_MetaData), NewProp_AverageFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentPerformanceTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentPerformanceTier = { "CurrentPerformanceTier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronOverallPerformanceData, CurrentPerformanceTier), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPerformanceTier_MetaData), NewProp_CurrentPerformanceTier_MetaData) }; // 1546457737
void Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_bIsPerformanceOptimal_SetBit(void* Obj)
{
	((FAuracronOverallPerformanceData*)Obj)->bIsPerformanceOptimal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_bIsPerformanceOptimal = { "bIsPerformanceOptimal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronOverallPerformanceData), &Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_bIsPerformanceOptimal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPerformanceOptimal_MetaData), NewProp_bIsPerformanceOptimal_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CullingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_BatchingData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_GPUData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_TotalFrameTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_AverageFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentPerformanceTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_CurrentPerformanceTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewProp_bIsPerformanceOptimal,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronOverallPerformanceData",
	Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::PropPointers),
	sizeof(FAuracronOverallPerformanceData),
	alignof(FAuracronOverallPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronOverallPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronOverallPerformanceData *************************************

// ********** Begin Delegate FOnPerformanceTierChanged *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms
	{
		EAuracronPerformanceTier OldTier;
		EAuracronPerformanceTier NewTier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldTier;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewTier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_OldTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_OldTier = { "OldTier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms, OldTier), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(0, nullptr) }; // 1546457737
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_NewTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_NewTier = { "NewTier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms, NewTier), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(0, nullptr) }; // 1546457737
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_OldTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_OldTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_NewTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::NewProp_NewTier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OnPerformanceTierChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliagePerformanceOptimizationManager::FOnPerformanceTierChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceTierChanged, EAuracronPerformanceTier OldTier, EAuracronPerformanceTier NewTier)
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms
	{
		EAuracronPerformanceTier OldTier;
		EAuracronPerformanceTier NewTier;
	};
	AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceTierChanged_Parms Parms;
	Parms.OldTier=OldTier;
	Parms.NewTier=NewTier;
	OnPerformanceTierChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceTierChanged ***********************************************

// ********** Begin Delegate FOnPerformanceOptimized ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms
	{
		FAuracronOverallPerformanceData PerformanceData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::NewProp_PerformanceData = { "PerformanceData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms, PerformanceData), Z_Construct_UScriptStruct_FAuracronOverallPerformanceData, METADATA_PARAMS(0, nullptr) }; // 3633857439
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::NewProp_PerformanceData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OnPerformanceOptimized__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliagePerformanceOptimizationManager::FOnPerformanceOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceOptimized, FAuracronOverallPerformanceData PerformanceData)
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms
	{
		FAuracronOverallPerformanceData PerformanceData;
	};
	AuracronFoliagePerformanceOptimizationManager_eventOnPerformanceOptimized_Parms Parms;
	Parms.PerformanceData=PerformanceData;
	OnPerformanceOptimized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceOptimized *************************************************

// ********** Begin Delegate FOnBatchOptimized *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms
	{
		int32 BatchCount;
		float OptimizationTimeMs;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OptimizationTimeMs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_BatchCount = { "BatchCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms, BatchCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_OptimizationTimeMs = { "OptimizationTimeMs", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms, OptimizationTimeMs), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_BatchCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_OptimizationTimeMs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OnBatchOptimized__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliagePerformanceOptimizationManager::FOnBatchOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnBatchOptimized, int32 BatchCount, float OptimizationTimeMs)
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms
	{
		int32 BatchCount;
		float OptimizationTimeMs;
	};
	AuracronFoliagePerformanceOptimizationManager_eventOnBatchOptimized_Parms Parms;
	Parms.BatchCount=BatchCount;
	Parms.OptimizationTimeMs=OptimizationTimeMs;
	OnBatchOptimized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBatchOptimized *******************************************************

// ********** Begin Delegate FOnMemoryOptimized ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms
	{
		float MemorySavedMB;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemorySavedMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::NewProp_MemorySavedMB = { "MemorySavedMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms, MemorySavedMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::NewProp_MemorySavedMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OnMemoryOptimized__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliagePerformanceOptimizationManager::FOnMemoryOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnMemoryOptimized, float MemorySavedMB)
{
	struct AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms
	{
		float MemorySavedMB;
	};
	AuracronFoliagePerformanceOptimizationManager_eventOnMemoryOptimized_Parms Parms;
	Parms.MemorySavedMB=MemorySavedMB;
	OnMemoryOptimized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMemoryOptimized ******************************************************

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function CompressInstanceData 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "CompressInstanceData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execCompressInstanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompressInstanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function CompressInstanceData 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function DrawDebugPerformanceInfo 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventDrawDebugPerformanceInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventDrawDebugPerformanceInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "DrawDebugPerformanceInfo", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::AuracronFoliagePerformanceOptimizationManager_eventDrawDebugPerformanceInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::AuracronFoliagePerformanceOptimizationManager_eventDrawDebugPerformanceInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execDrawDebugPerformanceInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugPerformanceInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function DrawDebugPerformanceInfo 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableAdaptivePerformance 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableAdaptivePerformance_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Adaptive performance\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptive performance" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableAdaptivePerformance_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableAdaptivePerformance_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableAdaptivePerformance", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableAdaptivePerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableAdaptivePerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableAdaptivePerformance)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableAdaptivePerformance(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableAdaptivePerformance 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableBatchingOptimization 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableBatchingOptimization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batching optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batching optimization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableBatchingOptimization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableBatchingOptimization_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableBatchingOptimization", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableBatchingOptimization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableBatchingOptimization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableBatchingOptimization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableBatchingOptimization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableBatchingOptimization 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableDebugVisualization 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableDebugVisualization 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableFrustumCulling 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableFrustumCulling_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Frustum culling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frustum culling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableFrustumCulling_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableFrustumCulling_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableFrustumCulling", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableFrustumCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableFrustumCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableFrustumCulling)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableFrustumCulling(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableFrustumCulling 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableGPUInstancing 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableGPUInstancing_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU instancing\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU instancing" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableGPUInstancing_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableGPUInstancing_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableGPUInstancing", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableGPUInstancing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableGPUInstancing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableGPUInstancing)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableGPUInstancing(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableGPUInstancing 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function EnableOcclusionCulling 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventEnableOcclusionCulling_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Occlusion culling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Occlusion culling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventEnableOcclusionCulling_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventEnableOcclusionCulling_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "EnableOcclusionCulling", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableOcclusionCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventEnableOcclusionCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execEnableOcclusionCulling)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableOcclusionCulling(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function EnableOcclusionCulling 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetAverageInstancesPerBatch 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetAverageInstancesPerBatch_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetAverageInstancesPerBatch_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetAverageInstancesPerBatch", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetAverageInstancesPerBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetAverageInstancesPerBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetAverageInstancesPerBatch)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageInstancesPerBatch();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetAverageInstancesPerBatch 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetBatchingPerformanceData 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetBatchingPerformanceData_Parms
	{
		FAuracronBatchingPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetBatchingPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData, METADATA_PARAMS(0, nullptr) }; // 3498980797
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetBatchingPerformanceData", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetBatchingPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetBatchingPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetBatchingPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronBatchingPerformanceData*)Z_Param__Result=P_THIS->GetBatchingPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetBatchingPerformanceData 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetConfiguration *
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetConfiguration_Parms
	{
		FAuracronFoliagePerformanceOptimizationConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, METADATA_PARAMS(0, nullptr) }; // 951894858
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliagePerformanceOptimizationConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetConfiguration ***

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetCullingPerformanceData 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetCullingPerformanceData_Parms
	{
		FAuracronCullingPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetCullingPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCullingPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2725127186
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetCullingPerformanceData", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetCullingPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetCullingPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetCullingPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCullingPerformanceData*)Z_Param__Result=P_THIS->GetCullingPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetCullingPerformanceData 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetCurrentPerformanceTier 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetCurrentPerformanceTier_Parms
	{
		EAuracronPerformanceTier ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetCurrentPerformanceTier_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(0, nullptr) }; // 1546457737
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetCurrentPerformanceTier", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetCurrentPerformanceTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetCurrentPerformanceTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetCurrentPerformanceTier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronPerformanceTier*)Z_Param__Result=P_THIS->GetCurrentPerformanceTier();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetCurrentPerformanceTier 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetFrustumCulledInstanceCount 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetFrustumCulledInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetFrustumCulledInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetFrustumCulledInstanceCount", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetFrustumCulledInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetFrustumCulledInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetFrustumCulledInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetFrustumCulledInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetFrustumCulledInstanceCount 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUInstanceCount 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetGPUInstanceCount", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetGPUInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGPUInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUInstanceCount 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUInstancingMode 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstancingMode_Parms
	{
		EAuracronGPUInstancingMode ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstancingMode_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode, METADATA_PARAMS(0, nullptr) }; // 1308130164
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetGPUInstancingMode", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstancingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUInstancingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetGPUInstancingMode)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronGPUInstancingMode*)Z_Param__Result=P_THIS->GetGPUInstancingMode();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUInstancingMode 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUPerformanceData 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetGPUPerformanceData_Parms
	{
		FAuracronGPUPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetGPUPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGPUPerformanceData, METADATA_PARAMS(0, nullptr) }; // 1291392199
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetGPUPerformanceData", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetGPUPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetGPUPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGPUPerformanceData*)Z_Param__Result=P_THIS->GetGPUPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetGPUPerformanceData 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetInstance ******
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetInstance_Parms
	{
		UAuracronFoliagePerformanceOptimizationManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliagePerformanceOptimizationManager**)Z_Param__Result=UAuracronFoliagePerformanceOptimizationManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetInstance ********

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetMemoryUsageMB *
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetMemoryUsageMB", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetMemoryUsageMB ***

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetOcclusionCulledInstanceCount 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetOcclusionCulledInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetOcclusionCulledInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetOcclusionCulledInstanceCount", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetOcclusionCulledInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetOcclusionCulledInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetOcclusionCulledInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetOcclusionCulledInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetOcclusionCulledInstanceCount 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetOptimizedBatchCount 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetOptimizedBatchCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetOptimizedBatchCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetOptimizedBatchCount", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetOptimizedBatchCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetOptimizedBatchCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetOptimizedBatchCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetOptimizedBatchCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetOptimizedBatchCount 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function GetPerformanceData 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventGetPerformanceData_Parms
	{
		FAuracronOverallPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronOverallPerformanceData, METADATA_PARAMS(0, nullptr) }; // 3633857439
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::AuracronFoliagePerformanceOptimizationManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronOverallPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function GetPerformanceData *

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function Initialize *******
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventInitialize_Parms
	{
		FAuracronFoliagePerformanceOptimizationConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 951894858
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::AuracronFoliagePerformanceOptimizationManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::AuracronFoliagePerformanceOptimizationManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliagePerformanceOptimizationConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function Initialize *********

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IntegrateWithLODSystem 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithLODSystem_Parms
	{
		UAuracronFoliageLODManager* LODManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LODManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::NewProp_LODManager = { "LODManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithLODSystem_Parms, LODManager), Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::NewProp_LODManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IntegrateWithLODSystem", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithLODSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithLODSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIntegrateWithLODSystem)
{
	P_GET_OBJECT(UAuracronFoliageLODManager,Z_Param_LODManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithLODSystem(Z_Param_LODManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IntegrateWithLODSystem 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IntegrateWithStreamingSystem 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithStreamingSystem_Parms
	{
		UAuracronFoliageStreamingManager* StreamingManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithStreamingSystem_Parms, StreamingManager), Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::NewProp_StreamingManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IntegrateWithStreamingSystem", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithStreamingSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventIntegrateWithStreamingSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIntegrateWithStreamingSystem)
{
	P_GET_OBJECT(UAuracronFoliageStreamingManager,Z_Param_StreamingManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithStreamingSystem(Z_Param_StreamingManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IntegrateWithStreamingSystem 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IsDebugVisualizationEnabled 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IsDebugVisualizationEnabled 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IsFrustumCullingEnabled 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIsFrustumCullingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventIsFrustumCullingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventIsFrustumCullingEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IsFrustumCullingEnabled", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsFrustumCullingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsFrustumCullingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIsFrustumCullingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFrustumCullingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IsFrustumCullingEnabled 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IsInitialized ****
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IsInitialized ******

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IsMemoryOptimal **
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIsMemoryOptimal_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventIsMemoryOptimal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventIsMemoryOptimal_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IsMemoryOptimal", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsMemoryOptimal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsMemoryOptimal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIsMemoryOptimal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMemoryOptimal();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IsMemoryOptimal ****

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function IsOcclusionCullingEnabled 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventIsOcclusionCullingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliagePerformanceOptimizationManager_eventIsOcclusionCullingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliagePerformanceOptimizationManager_eventIsOcclusionCullingEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "IsOcclusionCullingEnabled", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsOcclusionCullingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::AuracronFoliagePerformanceOptimizationManager_eventIsOcclusionCullingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execIsOcclusionCullingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsOcclusionCullingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function IsOcclusionCullingEnabled 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function LogPerformanceStatistics 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "LogPerformanceStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execLogPerformanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogPerformanceStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function LogPerformanceStatistics 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function OptimizeBatches **
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OptimizeBatches", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execOptimizeBatches)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeBatches();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function OptimizeBatches ****

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function OptimizeMemoryUsage 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "OptimizeMemoryUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function OptimizeMemoryUsage 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function RebuildBatches ***
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "RebuildBatches", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execRebuildBatches)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RebuildBatches();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function RebuildBatches *****

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function SetConfiguration *
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventSetConfiguration_Parms
	{
		FAuracronFoliagePerformanceOptimizationConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 951894858
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliagePerformanceOptimizationConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function SetConfiguration ***

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function SetGPUInstancingMode 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventSetGPUInstancingMode_Parms
	{
		EAuracronGPUInstancingMode Mode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventSetGPUInstancingMode_Parms, Mode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronGPUInstancingMode, METADATA_PARAMS(0, nullptr) }; // 1308130164
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::NewProp_Mode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "SetGPUInstancingMode", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetGPUInstancingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetGPUInstancingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execSetGPUInstancingMode)
{
	P_GET_ENUM(EAuracronGPUInstancingMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGPUInstancingMode(EAuracronGPUInstancingMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function SetGPUInstancingMode 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function SetPerformanceTier 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventSetPerformanceTier_Parms
	{
		EAuracronPerformanceTier Tier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Tier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Tier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::NewProp_Tier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::NewProp_Tier = { "Tier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventSetPerformanceTier_Parms, Tier), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronPerformanceTier, METADATA_PARAMS(0, nullptr) }; // 1546457737
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::NewProp_Tier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::NewProp_Tier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "SetPerformanceTier", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetPerformanceTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::AuracronFoliagePerformanceOptimizationManager_eventSetPerformanceTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execSetPerformanceTier)
{
	P_GET_ENUM(EAuracronPerformanceTier,Z_Param_Tier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPerformanceTier(EAuracronPerformanceTier(Z_Param_Tier));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function SetPerformanceTier *

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function Shutdown *********
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function Shutdown ***********

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function SynchronizeWithInstancedSystem 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventSynchronizeWithInstancedSystem_Parms
	{
		UAuracronFoliageInstancedManager* InstancedManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InstancedManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::NewProp_InstancedManager = { "InstancedManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventSynchronizeWithInstancedSystem_Parms, InstancedManager), Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::NewProp_InstancedManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "SynchronizeWithInstancedSystem", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventSynchronizeWithInstancedSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::AuracronFoliagePerformanceOptimizationManager_eventSynchronizeWithInstancedSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execSynchronizeWithInstancedSystem)
{
	P_GET_OBJECT(UAuracronFoliageInstancedManager,Z_Param_InstancedManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithInstancedSystem(Z_Param_InstancedManager);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function SynchronizeWithInstancedSystem 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function Tick *************
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::AuracronFoliagePerformanceOptimizationManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::AuracronFoliagePerformanceOptimizationManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function Tick ***************

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function UpdateAdaptivePerformance 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventUpdateAdaptivePerformance_Parms
	{
		float CurrentFPS;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventUpdateAdaptivePerformance_Parms, CurrentFPS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::NewProp_CurrentFPS,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "UpdateAdaptivePerformance", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateAdaptivePerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateAdaptivePerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execUpdateAdaptivePerformance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentFPS);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAdaptivePerformance(Z_Param_CurrentFPS);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function UpdateAdaptivePerformance 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function UpdateFrustumCulling 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms
	{
		FVector CameraLocation;
		FVector CameraDirection;
		float FOV;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FOV;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_CameraLocation = { "CameraLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms, CameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLocation_MetaData), NewProp_CameraLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_CameraDirection = { "CameraDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms, CameraDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraDirection_MetaData), NewProp_CameraDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_FOV = { "FOV", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms, FOV), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_CameraLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_CameraDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::NewProp_FOV,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "UpdateFrustumCulling", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateFrustumCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execUpdateFrustumCulling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraDirection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FOV);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFrustumCulling(Z_Param_Out_CameraLocation,Z_Param_Out_CameraDirection,Z_Param_FOV);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function UpdateFrustumCulling 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function UpdateOcclusionCulling 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics
{
	struct AuracronFoliagePerformanceOptimizationManager_eventUpdateOcclusionCulling_Parms
	{
		FVector CameraLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::NewProp_CameraLocation = { "CameraLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliagePerformanceOptimizationManager_eventUpdateOcclusionCulling_Parms, CameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLocation_MetaData), NewProp_CameraLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::NewProp_CameraLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "UpdateOcclusionCulling", Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateOcclusionCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::AuracronFoliagePerformanceOptimizationManager_eventUpdateOcclusionCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execUpdateOcclusionCulling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateOcclusionCulling(Z_Param_Out_CameraLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function UpdateOcclusionCulling 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager Function UpdatePerformanceMetrics 
struct Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliagePerformanceOptimizationManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager Function UpdatePerformanceMetrics 

// ********** Begin Class UAuracronFoliagePerformanceOptimizationManager ***************************
void UAuracronFoliagePerformanceOptimizationManager::StaticRegisterNativesUAuracronFoliagePerformanceOptimizationManager()
{
	UClass* Class = UAuracronFoliagePerformanceOptimizationManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CompressInstanceData", &UAuracronFoliagePerformanceOptimizationManager::execCompressInstanceData },
		{ "DrawDebugPerformanceInfo", &UAuracronFoliagePerformanceOptimizationManager::execDrawDebugPerformanceInfo },
		{ "EnableAdaptivePerformance", &UAuracronFoliagePerformanceOptimizationManager::execEnableAdaptivePerformance },
		{ "EnableBatchingOptimization", &UAuracronFoliagePerformanceOptimizationManager::execEnableBatchingOptimization },
		{ "EnableDebugVisualization", &UAuracronFoliagePerformanceOptimizationManager::execEnableDebugVisualization },
		{ "EnableFrustumCulling", &UAuracronFoliagePerformanceOptimizationManager::execEnableFrustumCulling },
		{ "EnableGPUInstancing", &UAuracronFoliagePerformanceOptimizationManager::execEnableGPUInstancing },
		{ "EnableOcclusionCulling", &UAuracronFoliagePerformanceOptimizationManager::execEnableOcclusionCulling },
		{ "GetAverageInstancesPerBatch", &UAuracronFoliagePerformanceOptimizationManager::execGetAverageInstancesPerBatch },
		{ "GetBatchingPerformanceData", &UAuracronFoliagePerformanceOptimizationManager::execGetBatchingPerformanceData },
		{ "GetConfiguration", &UAuracronFoliagePerformanceOptimizationManager::execGetConfiguration },
		{ "GetCullingPerformanceData", &UAuracronFoliagePerformanceOptimizationManager::execGetCullingPerformanceData },
		{ "GetCurrentPerformanceTier", &UAuracronFoliagePerformanceOptimizationManager::execGetCurrentPerformanceTier },
		{ "GetFrustumCulledInstanceCount", &UAuracronFoliagePerformanceOptimizationManager::execGetFrustumCulledInstanceCount },
		{ "GetGPUInstanceCount", &UAuracronFoliagePerformanceOptimizationManager::execGetGPUInstanceCount },
		{ "GetGPUInstancingMode", &UAuracronFoliagePerformanceOptimizationManager::execGetGPUInstancingMode },
		{ "GetGPUPerformanceData", &UAuracronFoliagePerformanceOptimizationManager::execGetGPUPerformanceData },
		{ "GetInstance", &UAuracronFoliagePerformanceOptimizationManager::execGetInstance },
		{ "GetMemoryUsageMB", &UAuracronFoliagePerformanceOptimizationManager::execGetMemoryUsageMB },
		{ "GetOcclusionCulledInstanceCount", &UAuracronFoliagePerformanceOptimizationManager::execGetOcclusionCulledInstanceCount },
		{ "GetOptimizedBatchCount", &UAuracronFoliagePerformanceOptimizationManager::execGetOptimizedBatchCount },
		{ "GetPerformanceData", &UAuracronFoliagePerformanceOptimizationManager::execGetPerformanceData },
		{ "Initialize", &UAuracronFoliagePerformanceOptimizationManager::execInitialize },
		{ "IntegrateWithLODSystem", &UAuracronFoliagePerformanceOptimizationManager::execIntegrateWithLODSystem },
		{ "IntegrateWithStreamingSystem", &UAuracronFoliagePerformanceOptimizationManager::execIntegrateWithStreamingSystem },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliagePerformanceOptimizationManager::execIsDebugVisualizationEnabled },
		{ "IsFrustumCullingEnabled", &UAuracronFoliagePerformanceOptimizationManager::execIsFrustumCullingEnabled },
		{ "IsInitialized", &UAuracronFoliagePerformanceOptimizationManager::execIsInitialized },
		{ "IsMemoryOptimal", &UAuracronFoliagePerformanceOptimizationManager::execIsMemoryOptimal },
		{ "IsOcclusionCullingEnabled", &UAuracronFoliagePerformanceOptimizationManager::execIsOcclusionCullingEnabled },
		{ "LogPerformanceStatistics", &UAuracronFoliagePerformanceOptimizationManager::execLogPerformanceStatistics },
		{ "OptimizeBatches", &UAuracronFoliagePerformanceOptimizationManager::execOptimizeBatches },
		{ "OptimizeMemoryUsage", &UAuracronFoliagePerformanceOptimizationManager::execOptimizeMemoryUsage },
		{ "RebuildBatches", &UAuracronFoliagePerformanceOptimizationManager::execRebuildBatches },
		{ "SetConfiguration", &UAuracronFoliagePerformanceOptimizationManager::execSetConfiguration },
		{ "SetGPUInstancingMode", &UAuracronFoliagePerformanceOptimizationManager::execSetGPUInstancingMode },
		{ "SetPerformanceTier", &UAuracronFoliagePerformanceOptimizationManager::execSetPerformanceTier },
		{ "Shutdown", &UAuracronFoliagePerformanceOptimizationManager::execShutdown },
		{ "SynchronizeWithInstancedSystem", &UAuracronFoliagePerformanceOptimizationManager::execSynchronizeWithInstancedSystem },
		{ "Tick", &UAuracronFoliagePerformanceOptimizationManager::execTick },
		{ "UpdateAdaptivePerformance", &UAuracronFoliagePerformanceOptimizationManager::execUpdateAdaptivePerformance },
		{ "UpdateFrustumCulling", &UAuracronFoliagePerformanceOptimizationManager::execUpdateFrustumCulling },
		{ "UpdateOcclusionCulling", &UAuracronFoliagePerformanceOptimizationManager::execUpdateOcclusionCulling },
		{ "UpdatePerformanceMetrics", &UAuracronFoliagePerformanceOptimizationManager::execUpdatePerformanceMetrics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager;
UClass* UAuracronFoliagePerformanceOptimizationManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliagePerformanceOptimizationManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliagePerformanceOptimizationManager"),
			Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliagePerformanceOptimizationManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_NoRegister()
{
	return UAuracronFoliagePerformanceOptimizationManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Performance Optimization Manager\n * Manager for maximum foliage performance optimization\n */" },
#endif
		{ "IncludePath", "AuracronFoliagePerformanceOptimization.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Performance Optimization Manager\nManager for maximum foliage performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceTierChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceOptimized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBatchOptimized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMemoryOptimized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancedManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliagePerformanceOptimization.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceTierChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceOptimized;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBatchOptimized;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMemoryOptimized;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LODManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_InstancedManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_CompressInstanceData, "CompressInstanceData" }, // 3709064980
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_DrawDebugPerformanceInfo, "DrawDebugPerformanceInfo" }, // 1740442274
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableAdaptivePerformance, "EnableAdaptivePerformance" }, // 1940090978
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableBatchingOptimization, "EnableBatchingOptimization" }, // 2253861287
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 4281053888
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableFrustumCulling, "EnableFrustumCulling" }, // 3700672909
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableGPUInstancing, "EnableGPUInstancing" }, // 810978982
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_EnableOcclusionCulling, "EnableOcclusionCulling" }, // 403898311
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetAverageInstancesPerBatch, "GetAverageInstancesPerBatch" }, // 2517494642
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetBatchingPerformanceData, "GetBatchingPerformanceData" }, // 1542342456
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetConfiguration, "GetConfiguration" }, // 1509617732
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCullingPerformanceData, "GetCullingPerformanceData" }, // 1701936251
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetCurrentPerformanceTier, "GetCurrentPerformanceTier" }, // 4167994428
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetFrustumCulledInstanceCount, "GetFrustumCulledInstanceCount" }, // 3754040459
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstanceCount, "GetGPUInstanceCount" }, // 3426234629
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUInstancingMode, "GetGPUInstancingMode" }, // 2183155684
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetGPUPerformanceData, "GetGPUPerformanceData" }, // 3407150790
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetInstance, "GetInstance" }, // 67253131
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetMemoryUsageMB, "GetMemoryUsageMB" }, // 2583236454
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOcclusionCulledInstanceCount, "GetOcclusionCulledInstanceCount" }, // 3079404359
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetOptimizedBatchCount, "GetOptimizedBatchCount" }, // 3149408842
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_GetPerformanceData, "GetPerformanceData" }, // 397755735
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Initialize, "Initialize" }, // 1957251117
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithLODSystem, "IntegrateWithLODSystem" }, // 3372116583
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IntegrateWithStreamingSystem, "IntegrateWithStreamingSystem" }, // 1242365969
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2817627829
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsFrustumCullingEnabled, "IsFrustumCullingEnabled" }, // 3577587256
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsInitialized, "IsInitialized" }, // 382577004
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsMemoryOptimal, "IsMemoryOptimal" }, // 3603083634
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_IsOcclusionCullingEnabled, "IsOcclusionCullingEnabled" }, // 3561178782
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_LogPerformanceStatistics, "LogPerformanceStatistics" }, // 3022430672
		{ &Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature, "OnBatchOptimized__DelegateSignature" }, // 3031436736
		{ &Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature, "OnMemoryOptimized__DelegateSignature" }, // 1158149155
		{ &Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature, "OnPerformanceOptimized__DelegateSignature" }, // 1296657318
		{ &Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature, "OnPerformanceTierChanged__DelegateSignature" }, // 1421913110
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeBatches, "OptimizeBatches" }, // 4180158336
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 3247453649
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_RebuildBatches, "RebuildBatches" }, // 3895280048
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetConfiguration, "SetConfiguration" }, // 1137953235
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetGPUInstancingMode, "SetGPUInstancingMode" }, // 949470601
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SetPerformanceTier, "SetPerformanceTier" }, // 169974778
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Shutdown, "Shutdown" }, // 2832872915
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_SynchronizeWithInstancedSystem, "SynchronizeWithInstancedSystem" }, // 1132157459
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_Tick, "Tick" }, // 1615493280
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateAdaptivePerformance, "UpdateAdaptivePerformance" }, // 2459074199
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateFrustumCulling, "UpdateFrustumCulling" }, // 1506527847
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdateOcclusionCulling, "UpdateOcclusionCulling" }, // 3764217724
		{ &Z_Construct_UFunction_UAuracronFoliagePerformanceOptimizationManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1135876958
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliagePerformanceOptimizationManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnPerformanceTierChanged = { "OnPerformanceTierChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, OnPerformanceTierChanged), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceTierChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceTierChanged_MetaData), NewProp_OnPerformanceTierChanged_MetaData) }; // 1421913110
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnPerformanceOptimized = { "OnPerformanceOptimized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, OnPerformanceOptimized), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnPerformanceOptimized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceOptimized_MetaData), NewProp_OnPerformanceOptimized_MetaData) }; // 1296657318
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnBatchOptimized = { "OnBatchOptimized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, OnBatchOptimized), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnBatchOptimized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBatchOptimized_MetaData), NewProp_OnBatchOptimized_MetaData) }; // 3031436736
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnMemoryOptimized = { "OnMemoryOptimized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, OnMemoryOptimized), Z_Construct_UDelegateFunction_UAuracronFoliagePerformanceOptimizationManager_OnMemoryOptimized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMemoryOptimized_MetaData), NewProp_OnMemoryOptimized_MetaData) }; // 1158149155
void Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliagePerformanceOptimizationManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliagePerformanceOptimizationManager), &Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 951894858
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_LODManager = { "LODManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, LODManager), Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODManager_MetaData), NewProp_LODManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, StreamingManager), Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingManager_MetaData), NewProp_StreamingManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_InstancedManager = { "InstancedManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliagePerformanceOptimizationManager, InstancedManager), Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancedManager_MetaData), NewProp_InstancedManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnPerformanceTierChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnPerformanceOptimized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnBatchOptimized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_OnMemoryOptimized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_LODManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_StreamingManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::NewProp_InstancedManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::ClassParams = {
	&UAuracronFoliagePerformanceOptimizationManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager.OuterSingleton;
}
UAuracronFoliagePerformanceOptimizationManager::UAuracronFoliagePerformanceOptimizationManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliagePerformanceOptimizationManager);
UAuracronFoliagePerformanceOptimizationManager::~UAuracronFoliagePerformanceOptimizationManager() {}
// ********** End Class UAuracronFoliagePerformanceOptimizationManager *****************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronCullingStrategy_StaticEnum, TEXT("EAuracronCullingStrategy"), &Z_Registration_Info_UEnum_EAuracronCullingStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2534998785U) },
		{ EAuracronBatchingStrategy_StaticEnum, TEXT("EAuracronBatchingStrategy"), &Z_Registration_Info_UEnum_EAuracronBatchingStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1102691889U) },
		{ EAuracronGPUInstancingMode_StaticEnum, TEXT("EAuracronGPUInstancingMode"), &Z_Registration_Info_UEnum_EAuracronGPUInstancingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1308130164U) },
		{ EAuracronPerformanceTier_StaticEnum, TEXT("EAuracronPerformanceTier"), &Z_Registration_Info_UEnum_EAuracronPerformanceTier, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1546457737U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliagePerformanceOptimizationConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration_Statics::NewStructOps, TEXT("AuracronFoliagePerformanceOptimizationConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliagePerformanceOptimizationConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliagePerformanceOptimizationConfiguration), 951894858U) },
		{ FAuracronCullingPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronCullingPerformanceData_Statics::NewStructOps, TEXT("AuracronCullingPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronCullingPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCullingPerformanceData), 2725127186U) },
		{ FAuracronBatchingPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronBatchingPerformanceData_Statics::NewStructOps, TEXT("AuracronBatchingPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronBatchingPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBatchingPerformanceData), 3498980797U) },
		{ FAuracronGPUPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronGPUPerformanceData_Statics::NewStructOps, TEXT("AuracronGPUPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronGPUPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGPUPerformanceData), 1291392199U) },
		{ FAuracronOverallPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronOverallPerformanceData_Statics::NewStructOps, TEXT("AuracronOverallPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronOverallPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronOverallPerformanceData), 3633857439U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliagePerformanceOptimizationManager, UAuracronFoliagePerformanceOptimizationManager::StaticClass, TEXT("UAuracronFoliagePerformanceOptimizationManager"), &Z_Registration_Info_UClass_UAuracronFoliagePerformanceOptimizationManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliagePerformanceOptimizationManager), 1370161509U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_853873150(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliagePerformanceOptimization_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
