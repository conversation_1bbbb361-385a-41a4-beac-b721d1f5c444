// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronRigTransformation.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronRigTransformation_generated_h
#error "AuracronRigTransformation.generated.h already included, missing '#pragma once' in AuracronRigTransformation.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronRigTransformation_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FBoneScalingData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h_70_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBoneScalingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBoneScalingData;
// ********** End ScriptStruct FBoneScalingData ****************************************************

// ********** Begin ScriptStruct FAuracronConstraintData *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h_91_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronConstraintData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronConstraintData;
// ********** End ScriptStruct FAuracronConstraintData *********************************************

// ********** Begin ScriptStruct FIKChainData ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h_121_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FIKChainData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FIKChainData;
// ********** End ScriptStruct FIKChainData ********************************************************

// ********** Begin ScriptStruct FRetargetingData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h_154_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRetargetingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRetargetingData;
// ********** End ScriptStruct FRetargetingData ****************************************************

// ********** Begin ScriptStruct FRigValidationResult **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h_181_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRigValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRigValidationResult;
// ********** End ScriptStruct FRigValidationResult ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronRigTransformation_h

// ********** Begin Enum EBoneScalingType **********************************************************
#define FOREACH_ENUM_EBONESCALINGTYPE(op) \
	op(EBoneScalingType::Uniform) \
	op(EBoneScalingType::NonUniform) \
	op(EBoneScalingType::Proportional) \
	op(EBoneScalingType::Hierarchical) 

enum class EBoneScalingType : uint8;
template<> struct TIsUEnumClass<EBoneScalingType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBoneScalingType>();
// ********** End Enum EBoneScalingType ************************************************************

// ********** Begin Enum EAuracronConstraintType ***************************************************
#define FOREACH_ENUM_EAURACRONCONSTRAINTTYPE(op) \
	op(EAuracronConstraintType::Position) \
	op(EAuracronConstraintType::Rotation) \
	op(EAuracronConstraintType::Scale) \
	op(EAuracronConstraintType::Parent) \
	op(EAuracronConstraintType::LookAt) \
	op(EAuracronConstraintType::TwoBoneIK) \
	op(EAuracronConstraintType::FABRIK) \
	op(EAuracronConstraintType::CCDIK) 

enum class EAuracronConstraintType : uint8;
template<> struct TIsUEnumClass<EAuracronConstraintType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAuracronConstraintType>();
// ********** End Enum EAuracronConstraintType *****************************************************

// ********** Begin Enum EIKSolverType *************************************************************
#define FOREACH_ENUM_EIKSOLVERTYPE(op) \
	op(EIKSolverType::TwoBone) \
	op(EIKSolverType::FABRIK) \
	op(EIKSolverType::CCDIK) \
	op(EIKSolverType::FullBody) 

enum class EIKSolverType : uint8;
template<> struct TIsUEnumClass<EIKSolverType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EIKSolverType>();
// ********** End Enum EIKSolverType ***************************************************************

// ********** Begin Enum ERigValidationType ********************************************************
#define FOREACH_ENUM_ERIGVALIDATIONTYPE(op) \
	op(ERigValidationType::Basic) \
	op(ERigValidationType::Hierarchy) \
	op(ERigValidationType::Constraints) \
	op(ERigValidationType::IKChains) \
	op(ERigValidationType::Comprehensive) 

enum class ERigValidationType : uint8;
template<> struct TIsUEnumClass<ERigValidationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERigValidationType>();
// ********** End Enum ERigValidationType **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
