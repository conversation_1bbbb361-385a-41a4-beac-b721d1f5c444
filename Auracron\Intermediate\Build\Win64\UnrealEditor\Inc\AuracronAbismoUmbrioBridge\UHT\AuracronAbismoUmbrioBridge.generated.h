// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAbismoUmbrioBridge.h"

#ifdef AURACRONABISMOUMBRIOBRIDGE_AuracronAbismoUmbrioBridge_generated_h
#error "AuracronAbismoUmbrioBridge.generated.h already included, missing '#pragma once' in AuracronAbismoUmbrioBridge.h"
#endif
#define AURACRONABISMOUMBRIOBRIDGE_AuracronAbismoUmbrioBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronUndergroundBiome : uint8;
struct FAuracronCaveProperties;
struct FAuracronGeologicalFormationConfig;
struct FAuracronUndergroundBiomeConfig;
struct FAuracronUndergroundLighting;

// ********** Begin ScriptStruct FAuracronCaveProperties *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_53_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCaveProperties;
// ********** End ScriptStruct FAuracronCaveProperties *********************************************

// ********** Begin ScriptStruct FAuracronUndergroundLighting **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_102_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronUndergroundLighting;
// ********** End ScriptStruct FAuracronUndergroundLighting ****************************************

// ********** Begin ScriptStruct FAuracronGeologicalFormationConfig ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_159_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGeologicalFormationConfig;
// ********** End ScriptStruct FAuracronGeologicalFormationConfig **********************************

// ********** Begin ScriptStruct FAuracronUndergroundBiomeConfig ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_216_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronUndergroundBiomeConfig;
// ********** End ScriptStruct FAuracronUndergroundBiomeConfig *************************************

// ********** Begin Class UAuracronAbismoUmbrioBridge **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateAdvancedCaveNavigation); \
	DECLARE_FUNCTION(execGenerateAdvancedCaveMaterials); \
	DECLARE_FUNCTION(execSetupAdvancedCavePhysics); \
	DECLARE_FUNCTION(execCreateProceduralCaveEcosystems); \
	DECLARE_FUNCTION(execGenerateAdvancedWaterSystems); \
	DECLARE_FUNCTION(execSetupDynamicWeatherEffects); \
	DECLARE_FUNCTION(execCreateAdvancedGeologicalFormations); \
	DECLARE_FUNCTION(execGenerateProceduralCaveAcoustics); \
	DECLARE_FUNCTION(execSetupDynamicCaveLighting); \
	DECLARE_FUNCTION(execConfigureAdvancedAtmosphericEffects); \
	DECLARE_FUNCTION(execValidateSystemIntegrity); \
	DECLARE_FUNCTION(execGetSystemStatistics); \
	DECLARE_FUNCTION(execClearGeneratedSystem); \
	DECLARE_FUNCTION(execGetSystemDataForPython); \
	DECLARE_FUNCTION(execExecutePythonScript); \
	DECLARE_FUNCTION(execInitializePythonBindings); \
	DECLARE_FUNCTION(execValidateTunnelConnectivity); \
	DECLARE_FUNCTION(execCreate3DNavigationPoints); \
	DECLARE_FUNCTION(execGenerateCaveNavMesh); \
	DECLARE_FUNCTION(execSetupVolumetricFog); \
	DECLARE_FUNCTION(execAddLuminousCrystals); \
	DECLARE_FUNCTION(execSetupUndergroundLighting); \
	DECLARE_FUNCTION(execSetBiomeConfiguration); \
	DECLARE_FUNCTION(execGetBiomeConfiguration); \
	DECLARE_FUNCTION(execApplyBiomeToCave); \
	DECLARE_FUNCTION(execGenerateGeologicalFormations); \
	DECLARE_FUNCTION(execGenerateTunnelNetwork); \
	DECLARE_FUNCTION(execGenerateCave); \
	DECLARE_FUNCTION(execGenerateUndergroundSystem);


AURACRONABISMOUMBRIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAbismoUmbrioBridge(); \
	friend struct Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONABISMOUMBRIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAbismoUmbrioBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronAbismoUmbrioBridge"), Z_Construct_UClass_UAuracronAbismoUmbrioBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAbismoUmbrioBridge)


#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAbismoUmbrioBridge(UAuracronAbismoUmbrioBridge&&) = delete; \
	UAuracronAbismoUmbrioBridge(const UAuracronAbismoUmbrioBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAbismoUmbrioBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAbismoUmbrioBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAbismoUmbrioBridge) \
	NO_API virtual ~UAuracronAbismoUmbrioBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_255_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h_258_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAbismoUmbrioBridge;

// ********** End Class UAuracronAbismoUmbrioBridge ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h

// ********** Begin Enum EAuracronGeologicalFormation **********************************************
#define FOREACH_ENUM_EAURACRONGEOLOGICALFORMATION(op) \
	op(EAuracronGeologicalFormation::Stalactite) \
	op(EAuracronGeologicalFormation::Stalagmite) \
	op(EAuracronGeologicalFormation::Column) \
	op(EAuracronGeologicalFormation::Flowstone) \
	op(EAuracronGeologicalFormation::Crystal) \
	op(EAuracronGeologicalFormation::UndergroundLake) \
	op(EAuracronGeologicalFormation::Chasm) \
	op(EAuracronGeologicalFormation::RockFormation) 

enum class EAuracronGeologicalFormation : uint8;
template<> struct TIsUEnumClass<EAuracronGeologicalFormation> { enum { Value = true }; };
template<> AURACRONABISMOUMBRIOBRIDGE_API UEnum* StaticEnum<EAuracronGeologicalFormation>();
// ********** End Enum EAuracronGeologicalFormation ************************************************

// ********** Begin Enum EAuracronUndergroundBiome *************************************************
#define FOREACH_ENUM_EAURACRONUNDERGROUNDBIOME(op) \
	op(EAuracronUndergroundBiome::DryCave) \
	op(EAuracronUndergroundBiome::WetCave) \
	op(EAuracronUndergroundBiome::CrystalCavern) \
	op(EAuracronUndergroundBiome::UndergroundRiver) \
	op(EAuracronUndergroundBiome::MushroomGrove) \
	op(EAuracronUndergroundBiome::LavaChasm) \
	op(EAuracronUndergroundBiome::IceCave) \
	op(EAuracronUndergroundBiome::AbyssalDepths) 

enum class EAuracronUndergroundBiome : uint8;
template<> struct TIsUEnumClass<EAuracronUndergroundBiome> { enum { Value = true }; };
template<> AURACRONABISMOUMBRIOBRIDGE_API UEnum* StaticEnum<EAuracronUndergroundBiome>();
// ********** End Enum EAuracronUndergroundBiome ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
