// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanPerformanceSystem.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanPerformanceSystem_generated_h
#error "AuracronMetaHumanPerformanceSystem.generated.h already included, missing '#pragma once' in AuracronMetaHumanPerformanceSystem.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanPerformanceSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class AActor;
class UAuracronMetaHumanFramework;
class USkeletalMeshComponent;
class UTexture;
struct FAuracronOptimizationConfig;
struct FAuracronPerformanceSample;
struct FAuracronPerformanceStats;

// ********** Begin ScriptStruct FAuracronPerformanceSample ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_69_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPerformanceSample_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPerformanceSample;
// ********** End ScriptStruct FAuracronPerformanceSample ******************************************

// ********** Begin ScriptStruct FAuracronPerformanceStats *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPerformanceStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPerformanceStats;
// ********** End ScriptStruct FAuracronPerformanceStats *******************************************

// ********** Begin ScriptStruct FAuracronOptimizationConfig ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_177_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronOptimizationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronOptimizationConfig;
// ********** End ScriptStruct FAuracronOptimizationConfig *****************************************

// ********** Begin Delegate FAuracronPerformanceAlert *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_217_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& AuracronPerformanceAlert, const FString& AlertMessage);


// ********** End Delegate FAuracronPerformanceAlert ***********************************************

// ********** Begin Delegate FAuracronOptimizationComplete *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_218_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronOptimizationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronOptimizationComplete, const FString& OptimizationResult);


// ********** End Delegate FAuracronOptimizationComplete *******************************************

// ********** Begin Class UAuracronMetaHumanPerformanceSystem **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSystemPerformanceCapabilities); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execEnablePerformanceAlerts); \
	DECLARE_FUNCTION(execSetPerformanceAlertThresholds); \
	DECLARE_FUNCTION(execStopMemoryProfiling); \
	DECLARE_FUNCTION(execStartMemoryProfiling); \
	DECLARE_FUNCTION(execStopCPUProfiling); \
	DECLARE_FUNCTION(execStartCPUProfiling); \
	DECLARE_FUNCTION(execSetGPUBudget); \
	DECLARE_FUNCTION(execOptimizeGPUPerformance); \
	DECLARE_FUNCTION(execGetGPUPerformanceMetrics); \
	DECLARE_FUNCTION(execSetMemoryBudget); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execForceGarbageCollection); \
	DECLARE_FUNCTION(execGetMemoryUsageBreakdown); \
	DECLARE_FUNCTION(execOptimizeAnimationPerformance); \
	DECLARE_FUNCTION(execOptimizeTextureStreaming); \
	DECLARE_FUNCTION(execOptimizeMetaHumanLODs); \
	DECLARE_FUNCTION(execApplyPerformanceOptimization); \
	DECLARE_FUNCTION(execResetPerformanceStatistics); \
	DECLARE_FUNCTION(execGetPerformanceStatistics); \
	DECLARE_FUNCTION(execGetCurrentPerformanceSample); \
	DECLARE_FUNCTION(execStopPerformanceMonitoring); \
	DECLARE_FUNCTION(execStartPerformanceMonitoring);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanPerformanceSystem(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanPerformanceSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanPerformanceSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanPerformanceSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanPerformanceSystem(UAuracronMetaHumanPerformanceSystem&&) = delete; \
	UAuracronMetaHumanPerformanceSystem(const UAuracronMetaHumanPerformanceSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanPerformanceSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanPerformanceSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanPerformanceSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_224_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h_227_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanPerformanceSystem;

// ********** End Class UAuracronMetaHumanPerformanceSystem ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPerformanceSystem_h

// ********** Begin Enum EAuracronPerformanceMetric ************************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCEMETRIC(op) \
	op(EAuracronPerformanceMetric::FrameRate) \
	op(EAuracronPerformanceMetric::MemoryUsage) \
	op(EAuracronPerformanceMetric::CPUUsage) \
	op(EAuracronPerformanceMetric::GPUUsage) \
	op(EAuracronPerformanceMetric::DrawCalls) \
	op(EAuracronPerformanceMetric::Triangles) \
	op(EAuracronPerformanceMetric::TextureMemory) \
	op(EAuracronPerformanceMetric::AnimationCost) \
	op(EAuracronPerformanceMetric::PhysicsCost) \
	op(EAuracronPerformanceMetric::RenderingCost) \
	op(EAuracronPerformanceMetric::LoadingTime) \
	op(EAuracronPerformanceMetric::NetworkLatency) 

enum class EAuracronPerformanceMetric : uint8;
template<> struct TIsUEnumClass<EAuracronPerformanceMetric> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronPerformanceMetric>();
// ********** End Enum EAuracronPerformanceMetric **************************************************

// ********** Begin Enum EAuracronOptimizationLevel ************************************************
#define FOREACH_ENUM_EAURACRONOPTIMIZATIONLEVEL(op) \
	op(EAuracronOptimizationLevel::None) \
	op(EAuracronOptimizationLevel::Conservative) \
	op(EAuracronOptimizationLevel::Balanced) \
	op(EAuracronOptimizationLevel::Aggressive) \
	op(EAuracronOptimizationLevel::Maximum) \
	op(EAuracronOptimizationLevel::Custom) 

enum class EAuracronOptimizationLevel : uint8;
template<> struct TIsUEnumClass<EAuracronOptimizationLevel> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronOptimizationLevel>();
// ********** End Enum EAuracronOptimizationLevel **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
