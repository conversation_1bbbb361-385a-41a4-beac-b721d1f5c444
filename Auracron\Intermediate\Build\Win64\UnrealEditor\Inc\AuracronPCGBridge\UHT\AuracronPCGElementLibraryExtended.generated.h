// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGElementLibraryExtended.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGElementLibraryExtended_generated_h
#error "AuracronPCGElementLibraryExtended.generated.h already included, missing '#pragma once' in AuracronPCGElementLibraryExtended.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGElementLibraryExtended_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UAuracronPCGPointTransformerSettings *************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_32_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPointTransformerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGPointTransformerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointTransformerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPointTransformerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPointTransformerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPointTransformerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_32_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPointTransformerSettings(UAuracronPCGPointTransformerSettings&&) = delete; \
	UAuracronPCGPointTransformerSettings(const UAuracronPCGPointTransformerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPointTransformerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPointTransformerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGPointTransformerSettings) \
	NO_API virtual ~UAuracronPCGPointTransformerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_29_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_32_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_32_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_32_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPointTransformerSettings;

// ********** End Class UAuracronPCGPointTransformerSettings ***************************************

// ********** Begin ScriptStruct FAuracronPCGMeshEntry *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_116_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGMeshEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGMeshEntry;
// ********** End ScriptStruct FAuracronPCGMeshEntry ***********************************************

// ********** Begin Class UAuracronPCGAdvancedMeshSpawnerSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_148_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedMeshSpawnerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedMeshSpawnerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedMeshSpawnerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedMeshSpawnerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_148_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedMeshSpawnerSettings(UAuracronPCGAdvancedMeshSpawnerSettings&&) = delete; \
	UAuracronPCGAdvancedMeshSpawnerSettings(const UAuracronPCGAdvancedMeshSpawnerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedMeshSpawnerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedMeshSpawnerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedMeshSpawnerSettings) \
	NO_API virtual ~UAuracronPCGAdvancedMeshSpawnerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_145_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_148_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_148_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_148_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedMeshSpawnerSettings;

// ********** End Class UAuracronPCGAdvancedMeshSpawnerSettings ************************************

// ********** Begin Class UAuracronPCGExtendedAttributeModifierSettings ****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_225_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGExtendedAttributeModifierSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGExtendedAttributeModifierSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGExtendedAttributeModifierSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGExtendedAttributeModifierSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_225_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGExtendedAttributeModifierSettings(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGExtendedAttributeModifierSettings(UAuracronPCGExtendedAttributeModifierSettings&&) = delete; \
	UAuracronPCGExtendedAttributeModifierSettings(const UAuracronPCGExtendedAttributeModifierSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGExtendedAttributeModifierSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGExtendedAttributeModifierSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGExtendedAttributeModifierSettings) \
	NO_API virtual ~UAuracronPCGExtendedAttributeModifierSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_222_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_225_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_225_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_225_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGExtendedAttributeModifierSettings;

// ********** End Class UAuracronPCGExtendedAttributeModifierSettings ******************************

// ********** Begin Class UAuracronPCGDebugVisualizerSettings **************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_293_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGDebugVisualizerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGDebugVisualizerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGDebugVisualizerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGDebugVisualizerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_293_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGDebugVisualizerSettings(UAuracronPCGDebugVisualizerSettings&&) = delete; \
	UAuracronPCGDebugVisualizerSettings(const UAuracronPCGDebugVisualizerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGDebugVisualizerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGDebugVisualizerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGDebugVisualizerSettings) \
	NO_API virtual ~UAuracronPCGDebugVisualizerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_290_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_293_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_293_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h_293_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGDebugVisualizerSettings;

// ********** End Class UAuracronPCGDebugVisualizerSettings ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibraryExtended_h

// ********** Begin Enum EAuracronPCGAttributeOperation ********************************************
#define FOREACH_ENUM_EAURACRONPCGATTRIBUTEOPERATION(op) \
	op(EAuracronPCGAttributeOperation::Set) \
	op(EAuracronPCGAttributeOperation::Add) \
	op(EAuracronPCGAttributeOperation::Multiply) \
	op(EAuracronPCGAttributeOperation::Min) \
	op(EAuracronPCGAttributeOperation::Max) \
	op(EAuracronPCGAttributeOperation::Lerp) \
	op(EAuracronPCGAttributeOperation::Clamp) \
	op(EAuracronPCGAttributeOperation::Normalize) \
	op(EAuracronPCGAttributeOperation::Invert) 

enum class EAuracronPCGAttributeOperation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGAttributeOperation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGAttributeOperation>();
// ********** End Enum EAuracronPCGAttributeOperation **********************************************

// ********** Begin Enum EAuracronPCGDebugVisualizationMode ****************************************
#define FOREACH_ENUM_EAURACRONPCGDEBUGVISUALIZATIONMODE(op) \
	op(EAuracronPCGDebugVisualizationMode::Points) \
	op(EAuracronPCGDebugVisualizationMode::Bounds) \
	op(EAuracronPCGDebugVisualizationMode::Normals) \
	op(EAuracronPCGDebugVisualizationMode::Density) \
	op(EAuracronPCGDebugVisualizationMode::Attributes) \
	op(EAuracronPCGDebugVisualizationMode::Connections) 

enum class EAuracronPCGDebugVisualizationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGDebugVisualizationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDebugVisualizationMode>();
// ********** End Enum EAuracronPCGDebugVisualizationMode ******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
