// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Header
// Bridge 2.14: PCG Framework - Debugging Tools

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"
#include "PCGComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "HAL/IConsoleManager.h"

#include "AuracronPCGDebugSystem.generated.h"

// Debug visualization modes
UENUM(BlueprintType)
enum class EAuracronPCGDebugSystemVisualizationMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    Points                  UMETA(DisplayName = "Points"),
    Connections             UMETA(DisplayName = "Connections"),
    BoundingBoxes           UMETA(DisplayName = "Bounding Boxes"),
    Attributes              UMETA(DisplayName = "Attributes"),
    Performance             UMETA(DisplayName = "Performance"),
    DataFlow                UMETA(DisplayName = "Data Flow"),
    Errors                  UMETA(DisplayName = "Errors"),
    All                     UMETA(DisplayName = "All")
};

// Debug profiling categories
UENUM(BlueprintType)
enum class EAuracronPCGDebugProfilingCategory : uint8
{
    Execution               UMETA(DisplayName = "Execution"),
    Memory                  UMETA(DisplayName = "Memory"),
    DataTransfer            UMETA(DisplayName = "Data Transfer"),
    NodeProcessing          UMETA(DisplayName = "Node Processing"),
    GraphValidation         UMETA(DisplayName = "Graph Validation"),
    Rendering               UMETA(DisplayName = "Rendering"),
    IO                      UMETA(DisplayName = "Input/Output"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Debug inspection levels
UENUM(BlueprintType)
enum class EAuracronPCGDebugInspectionLevel : uint8
{
    Basic                   UMETA(DisplayName = "Basic"),
    Detailed                UMETA(DisplayName = "Detailed"),
    Verbose                 UMETA(DisplayName = "Verbose"),
    Expert                  UMETA(DisplayName = "Expert")
};

// Debug execution modes
UENUM(BlueprintType)
enum class EAuracronPCGDebugExecutionMode : uint8
{
    Normal                  UMETA(DisplayName = "Normal"),
    StepByStep              UMETA(DisplayName = "Step by Step"),
    Breakpoints             UMETA(DisplayName = "Breakpoints"),
    SlowMotion              UMETA(DisplayName = "Slow Motion"),
    Replay                  UMETA(DisplayName = "Replay")
};

// =============================================================================
// DEBUG VISUALIZATION DESCRIPTOR
// =============================================================================

/**
 * Debug Visualization Descriptor
 * Describes parameters for debug visualization
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGDebugVisualizationDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    EAuracronPCGDebugSystemVisualizationMode VisualizationMode = EAuracronPCGDebugSystemVisualizationMode::Points;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Points")
    bool bShowPoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Points")
    float PointSize = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Points")
    FLinearColor PointColor = FLinearColor::Green;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Points")
    bool bShowPointIndices = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connections")
    bool bShowConnections = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connections")
    float ConnectionThickness = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connections")
    FLinearColor ConnectionColor = FLinearColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bounding Boxes")
    bool bShowBoundingBoxes = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bounding Boxes")
    FLinearColor BoundingBoxColor = FLinearColor::Yellow;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    bool bShowAttributes = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    TArray<FString> AttributesToShow;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bShowPerformanceInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bShowExecutionTimes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bShowMemoryUsage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Flow")
    bool bShowDataFlow = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Flow")
    bool bAnimateDataFlow = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Flow")
    float DataFlowSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Errors")
    bool bShowErrors = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Errors")
    FLinearColor ErrorColor = FLinearColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bPersistentDisplay = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float DisplayDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bWorldSpaceText = true;

    FAuracronPCGDebugVisualizationDescriptor()
    {
        VisualizationMode = EAuracronPCGDebugSystemVisualizationMode::Points;
        bShowPoints = true;
        PointSize = 5.0f;
        PointColor = FLinearColor::Green;
        bShowPointIndices = false;
        bShowConnections = true;
        ConnectionThickness = 2.0f;
        ConnectionColor = FLinearColor::Blue;
        bShowBoundingBoxes = false;
        BoundingBoxColor = FLinearColor::Yellow;
        bShowAttributes = false;
        bShowPerformanceInfo = false;
        bShowExecutionTimes = true;
        bShowMemoryUsage = true;
        bShowDataFlow = false;
        bAnimateDataFlow = true;
        DataFlowSpeed = 1.0f;
        bShowErrors = true;
        ErrorColor = FLinearColor::Red;
        bPersistentDisplay = false;
        DisplayDuration = 5.0f;
        bWorldSpaceText = true;
    }
};

// =============================================================================
// DEBUG PROFILING DESCRIPTOR
// =============================================================================

/**
 * Debug Profiling Descriptor
 * Describes parameters for performance profiling
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGDebugProfilingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    TArray<EAuracronPCGDebugProfilingCategory> CategoriesToProfile;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bEnableDetailedProfiling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bProfileMemoryAllocations = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bProfileGPUUsage = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    float ProfilingSampleRate = 60.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "History")
    bool bKeepProfilingHistory = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "History")
    int32 MaxHistoryEntries = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "History")
    bool bAutoSaveProfilingData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float SlowExecutionThreshold = 16.67f; // 60 FPS

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float HighMemoryThreshold = 100.0f; // MB

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    bool bAlertOnThresholdExceeded = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Export")
    bool bEnableCSVExport = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Export")
    bool bEnableJSONExport = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Export")
    FString ExportDirectory = TEXT("Saved/Profiling/PCG");

    FAuracronPCGDebugProfilingDescriptor()
    {
        CategoriesToProfile = {
            EAuracronPCGDebugProfilingCategory::Execution,
            EAuracronPCGDebugProfilingCategory::Memory,
            EAuracronPCGDebugProfilingCategory::NodeProcessing
        };
        bEnableDetailedProfiling = true;
        bProfileMemoryAllocations = true;
        bProfileGPUUsage = false;
        ProfilingSampleRate = 60.0f;
        bKeepProfilingHistory = true;
        MaxHistoryEntries = 1000;
        bAutoSaveProfilingData = false;
        SlowExecutionThreshold = 16.67f;
        HighMemoryThreshold = 100.0f;
        bAlertOnThresholdExceeded = true;
        bEnableCSVExport = false;
        bEnableJSONExport = false;
        ExportDirectory = TEXT("Saved/Profiling/PCG");
    }
};

// =============================================================================
// DEBUG INSPECTION DESCRIPTOR
// =============================================================================

/**
 * Debug Inspection Descriptor
 * Describes parameters for data inspection
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGDebugInspectionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inspection")
    EAuracronPCGDebugInspectionLevel InspectionLevel = EAuracronPCGDebugInspectionLevel::Detailed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
    bool bInspectPointData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
    bool bInspectSpatialData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
    bool bInspectMetadata = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
    bool bInspectAttributes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Graph")
    bool bInspectGraphStructure = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Graph")
    bool bInspectNodeConnections = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Graph")
    bool bInspectDataFlow = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bValidateDataIntegrity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bCheckForMemoryLeaks = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bValidateConnections = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    TArray<FString> NodeTypesToInspect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    TArray<FString> AttributesToInspect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bGenerateInspectionReport = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bLogInspectionResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString InspectionReportPath = TEXT("Saved/Debug/PCG/Inspection");

    FAuracronPCGDebugInspectionDescriptor()
    {
        InspectionLevel = EAuracronPCGDebugInspectionLevel::Detailed;
        bInspectPointData = true;
        bInspectSpatialData = true;
        bInspectMetadata = true;
        bInspectAttributes = true;
        bInspectGraphStructure = true;
        bInspectNodeConnections = true;
        bInspectDataFlow = true;
        bValidateDataIntegrity = true;
        bCheckForMemoryLeaks = true;
        bValidateConnections = true;
        bGenerateInspectionReport = true;
        bLogInspectionResults = true;
        InspectionReportPath = TEXT("Saved/Debug/PCG/Inspection");
    }
};

// =============================================================================
// DEBUG EXECUTION DESCRIPTOR
// =============================================================================

/**
 * Debug Execution Descriptor
 * Describes parameters for step-by-step execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGDebugExecutionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    EAuracronPCGDebugExecutionMode ExecutionMode = EAuracronPCGDebugExecutionMode::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step by Step")
    bool bPauseAtEachNode = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step by Step")
    bool bRequireManualContinue = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step by Step")
    float StepDelay = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breakpoints")
    TArray<FString> BreakpointNodeNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breakpoints")
    bool bBreakOnErrors = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breakpoints")
    bool bBreakOnWarnings = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slow Motion")
    float SlowMotionFactor = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replay")
    bool bRecordExecution = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replay")
    bool bAllowReplay = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Replay")
    int32 MaxReplaySteps = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogExecutionSteps = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogDataChanges = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogPerformanceMetrics = true;

    FAuracronPCGDebugExecutionDescriptor()
    {
        ExecutionMode = EAuracronPCGDebugExecutionMode::Normal;
        bPauseAtEachNode = false;
        bRequireManualContinue = false;
        StepDelay = 1.0f;
        bBreakOnErrors = true;
        bBreakOnWarnings = false;
        SlowMotionFactor = 0.5f;
        bRecordExecution = false;
        bAllowReplay = false;
        MaxReplaySteps = 1000;
        bLogExecutionSteps = true;
        bLogDataChanges = false;
        bLogPerformanceMetrics = true;
    }
};

// =============================================================================
// VISUAL DEBUGGER
// =============================================================================

/**
 * Visual Debugger
 * Provides visual debugging capabilities for PCG graphs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGVisualDebugger : public UObject
{
    GENERATED_BODY()

public:
    // Visualization functions
    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawPointData(UWorld* World, const UPCGPointData* PointData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawGraphConnections(UWorld* World, const UPCGGraph* Graph, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawBoundingBoxes(UWorld* World, const UPCGSpatialData* SpatialData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawAttributes(UWorld* World, const UPCGPointData* PointData, const TArray<FString>& AttributeNames, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawPerformanceInfo(UWorld* World, const FVector& Location, const TMap<FString, float>& PerformanceData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawDataFlow(UWorld* World, const UPCGGraph* Graph, float AnimationTime, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void DrawErrors(UWorld* World, const TArray<FString>& ErrorMessages, const TArray<FVector>& ErrorLocations, const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void ClearDebugDisplay(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void SetVisualizationMode(EAuracronPCGDebugSystemVisualizationMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static EAuracronPCGDebugSystemVisualizationMode GetVisualizationMode();

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static void ToggleVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Visual Debugger")
    static bool IsVisualizationEnabled();

private:
    static EAuracronPCGDebugSystemVisualizationMode CurrentVisualizationMode;
    static bool bVisualizationEnabled;
};

// =============================================================================
// PERFORMANCE PROFILER
// =============================================================================

/**
 * Performance Profiler
 * Provides performance profiling capabilities for PCG graphs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGPerformanceProfiler : public UObject
{
    GENERATED_BODY()

public:
    // Profiling functions
    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void StartProfiling(const FAuracronPCGDebugProfilingDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void StopProfiling();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void PauseProfiling();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void ResumeProfiling();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static bool IsProfilingActive();

    // Data collection
    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void RecordExecutionTime(const FString& NodeName, float ExecutionTime);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void RecordMemoryUsage(const FString& NodeName, float MemoryUsage);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void RecordDataTransfer(const FString& FromNode, const FString& ToNode, int32 DataSize);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void RecordCustomMetric(const FString& MetricName, float Value);

    // Data retrieval
    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static TMap<FString, float> GetExecutionTimes();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static TMap<FString, float> GetMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static TMap<FString, float> GetCustomMetrics();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static float GetTotalExecutionTime();

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static float GetPeakMemoryUsage();

    // Analysis
    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static TArray<FString> GetBottleneckNodes(float Threshold = 10.0f);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static TArray<FString> GetHighMemoryNodes(float Threshold = 50.0f);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static FString GeneratePerformanceReport();

    // Export
    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static bool ExportProfilingData(const FString& FilePath, bool bCSVFormat = true);

    UFUNCTION(BlueprintCallable, Category = "Performance Profiler")
    static void ClearProfilingData();

private:
    static bool bIsProfilingActive;
    static FAuracronPCGDebugProfilingDescriptor CurrentDescriptor;
    static TMap<FString, TArray<float>> ExecutionTimeHistory;
    static TMap<FString, TArray<float>> MemoryUsageHistory;
    static TMap<FString, float> CustomMetrics;
};

// =============================================================================
// DATA INSPECTOR
// =============================================================================

/**
 * Data Inspector
 * Provides data inspection capabilities for PCG graphs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGDataInspector : public UObject
{
    GENERATED_BODY()

public:
    // Inspection functions
    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString InspectPointData(const UPCGPointData* PointData, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString InspectSpatialData(const UPCGSpatialData* SpatialData, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString InspectMetadata(const UPCGMetadata* Metadata, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString InspectGraph(const UPCGGraph* Graph, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString InspectNode(const UPCGSettings* NodeSettings, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    // Validation functions
    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TArray<FString> ValidateDataIntegrity(const UPCGData* Data);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TArray<FString> ValidateGraphConnections(const UPCGGraph* Graph);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TArray<FString> CheckForMemoryLeaks(const UPCGGraph* Graph);

    // Analysis functions
    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TMap<FString, int32> AnalyzeDataTypes(const UPCGData* Data);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TMap<FString, float> AnalyzeAttributeDistribution(const UPCGPointData* PointData, const FString& AttributeName);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FString GenerateDataReport(const UPCGData* Data, const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static int32 CountPoints(const UPCGPointData* PointData);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static TArray<FString> GetAttributeNames(const UPCGPointData* PointData);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static FBox GetDataBounds(const UPCGSpatialData* SpatialData);

    UFUNCTION(BlueprintCallable, Category = "Data Inspector")
    static float CalculateDataSize(const UPCGData* Data);

private:
    // Helper functions for memory leak detection
    static bool HasCircularReference(const UPCGNode* Node, TSet<const UPCGNode*>& VisitedNodes, TSet<const UPCGNode*>& RecursionStack);

    // Helper function to get metadata type size
    static int32 GetMetadataTypeSize(EPCGMetadataTypes Type);
};

// =============================================================================
// GRAPH VISUALIZER
// =============================================================================

/**
 * Graph Visualizer
 * Provides graph visualization capabilities for PCG graphs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGGraphVisualizer : public UObject
{
    GENERATED_BODY()

public:
    // Visualization functions
    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void VisualizeGraph(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void VisualizeNodeHierarchy(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void VisualizeDataFlow(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation, float AnimationTime);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void VisualizeExecutionOrder(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation);

    // Node visualization
    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void DrawNode(UWorld* World, const UPCGSettings* NodeSettings, const FVector& Location, const FLinearColor& Color);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void DrawNodeConnections(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void DrawNodeLabels(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation);

    // Layout functions
    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static TMap<FString, FVector> CalculateNodeLayout(const UPCGGraph* Graph, const FVector& CenterLocation);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static FVector CalculateNodePosition(const UPCGSettings* NodeSettings, int32 NodeIndex, int32 TotalNodes, const FVector& CenterLocation);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static void ClearGraphVisualization(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static FLinearColor GetNodeColor(const UPCGSettings* NodeSettings);

    UFUNCTION(BlueprintCallable, Category = "Graph Visualizer")
    static FString GetNodeDisplayName(const UPCGSettings* NodeSettings);
};

// =============================================================================
// STEP BY STEP EXECUTOR
// =============================================================================

/**
 * Step by Step Executor
 * Provides step-by-step execution capabilities for PCG graphs
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGStepByStepExecutor : public UObject
{
    GENERATED_BODY()

public:
    // Execution control
    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StartStepByStepExecution(const UPCGGraph* Graph, const FAuracronPCGDebugExecutionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StopStepByStepExecution();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void PauseExecution();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void ResumeExecution();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StepForward();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StepBackward();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static bool IsExecutionActive();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static bool IsExecutionPaused();

    // Breakpoint management
    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void AddBreakpoint(const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void RemoveBreakpoint(const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void ClearAllBreakpoints();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static TArray<FString> GetBreakpoints();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static bool HasBreakpoint(const FString& NodeName);

    // Execution state
    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static FString GetCurrentNode();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static int32 GetCurrentStep();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static int32 GetTotalSteps();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static TArray<FString> GetExecutionHistory();

    // Replay functionality
    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StartReplay();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void StopReplay();

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static void SetReplaySpeed(float Speed);

    UFUNCTION(BlueprintCallable, Category = "Step by Step Executor")
    static bool IsReplayActive();

private:
    static bool bIsExecutionActive;
    static bool bIsExecutionPaused;
    static bool bIsReplayActive;
    static FString CurrentNodeName;
    static int32 CurrentStepIndex;
    static TArray<FString> ExecutionHistory;
    static TArray<FString> Breakpoints;
    static FAuracronPCGDebugExecutionDescriptor CurrentDescriptor;
};

// =============================================================================
// DEBUG CONSOLE COMMANDS
// =============================================================================

/**
 * Debug Console Commands
 * Provides console commands for PCG debugging
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGDebugConsoleCommands : public UObject
{
    GENERATED_BODY()

public:
    // Console command registration
    UFUNCTION(BlueprintCallable, Category = "Debug Console Commands")
    static void RegisterConsoleCommands();

    UFUNCTION(BlueprintCallable, Category = "Debug Console Commands")
    static void UnregisterConsoleCommands();

    // Command implementations
    static void ToggleVisualization(const TArray<FString>& Args);
    static void SetVisualizationMode(const TArray<FString>& Args);
    static void StartProfiling(const TArray<FString>& Args);
    static void StopProfiling(const TArray<FString>& Args);
    static void InspectGraph(const TArray<FString>& Args);
    static void StepByStepExecution(const TArray<FString>& Args);
    static void AddBreakpoint(const TArray<FString>& Args);
    static void RemoveBreakpoint(const TArray<FString>& Args);
    static void ClearDebugDisplay(const TArray<FString>& Args);
    static void ExportProfilingData(const TArray<FString>& Args);

private:
    static TArray<IConsoleCommand*> RegisteredCommands;
};

// =============================================================================
// DEBUG SYSTEM MANAGER
// =============================================================================

/**
 * Debug System Manager
 * Central manager for all PCG debugging functionality
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGDebugSystemManager : public UObject
{
    GENERATED_BODY()

public:
    // System management
    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    static UAuracronPCGDebugSystemManager* GetInstance();

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void InitializeDebugSystem();

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void ShutdownDebugSystem();

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    bool IsDebugSystemActive() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void SetVisualizationDescriptor(const FAuracronPCGDebugVisualizationDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void SetProfilingDescriptor(const FAuracronPCGDebugProfilingDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void SetInspectionDescriptor(const FAuracronPCGDebugInspectionDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void SetExecutionDescriptor(const FAuracronPCGDebugExecutionDescriptor& Descriptor);

    // Integrated debugging
    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void StartFullDebugging(const UPCGGraph* Graph);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void StopFullDebugging();

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    FString GenerateFullDebugReport(const UPCGGraph* Graph);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void SaveDebugConfiguration(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void LoadDebugConfiguration(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Debug System Manager")
    void ResetToDefaultConfiguration();

private:
    UPROPERTY()
    FAuracronPCGDebugVisualizationDescriptor VisualizationDescriptor;

    UPROPERTY()
    FAuracronPCGDebugProfilingDescriptor ProfilingDescriptor;

    UPROPERTY()
    FAuracronPCGDebugInspectionDescriptor InspectionDescriptor;

    UPROPERTY()
    FAuracronPCGDebugExecutionDescriptor ExecutionDescriptor;

    static UAuracronPCGDebugSystemManager* Instance;
    bool bIsSystemActive = false;
};
