// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGMaterialSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGMaterialSystem_generated_h
#error "AuracronPCGMaterialSystem.generated.h already included, missing '#pragma once' in AuracronPCGMaterialSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGMaterialSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UMaterialInstanceDynamic;
class UMaterialInterface;
enum class EAuracronPCGMaterialSelectionMode : uint8;
struct FAuracronPCGMaterialBlendingDescriptor;
struct FAuracronPCGMaterialParameterDescriptor;
struct FAuracronPCGMaterialSelectionDescriptor;
struct FAuracronPCGUVGenerationDescriptor;
struct FLinearColor;
struct FPCGPoint;

// ********** Begin ScriptStruct FAuracronPCGMaterialSelectionDescriptor ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_128_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGMaterialSelectionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGMaterialSelectionDescriptor;
// ********** End ScriptStruct FAuracronPCGMaterialSelectionDescriptor *****************************

// ********** Begin ScriptStruct FAuracronPCGMaterialBlendingDescriptor ****************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_195_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGMaterialBlendingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGMaterialBlendingDescriptor;
// ********** End ScriptStruct FAuracronPCGMaterialBlendingDescriptor ******************************

// ********** Begin ScriptStruct FAuracronPCGUVGenerationDescriptor ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_251_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGUVGenerationDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGUVGenerationDescriptor;
// ********** End ScriptStruct FAuracronPCGUVGenerationDescriptor **********************************

// ********** Begin ScriptStruct FAuracronPCGMaterialParameterDescriptor ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_333_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGMaterialParameterDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGMaterialParameterDescriptor;
// ********** End ScriptStruct FAuracronPCGMaterialParameterDescriptor *****************************

// ********** Begin Class UAuracronPCGAdvancedMaterialSelectorSettings *****************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_397_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedMaterialSelectorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedMaterialSelectorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedMaterialSelectorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedMaterialSelectorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_397_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedMaterialSelectorSettings(UAuracronPCGAdvancedMaterialSelectorSettings&&) = delete; \
	UAuracronPCGAdvancedMaterialSelectorSettings(const UAuracronPCGAdvancedMaterialSelectorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedMaterialSelectorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedMaterialSelectorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedMaterialSelectorSettings) \
	NO_API virtual ~UAuracronPCGAdvancedMaterialSelectorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_394_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_397_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_397_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_397_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedMaterialSelectorSettings;

// ********** End Class UAuracronPCGAdvancedMaterialSelectorSettings *******************************

// ********** Begin Class UAuracronPCGMaterialBlenderSettings **************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_456_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMaterialBlenderSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMaterialBlenderSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMaterialBlenderSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMaterialBlenderSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_456_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMaterialBlenderSettings(UAuracronPCGMaterialBlenderSettings&&) = delete; \
	UAuracronPCGMaterialBlenderSettings(const UAuracronPCGMaterialBlenderSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMaterialBlenderSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMaterialBlenderSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGMaterialBlenderSettings) \
	NO_API virtual ~UAuracronPCGMaterialBlenderSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_453_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_456_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_456_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_456_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMaterialBlenderSettings;

// ********** End Class UAuracronPCGMaterialBlenderSettings ****************************************

// ********** Begin Class UAuracronPCGUVCoordinateGeneratorSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_525_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGUVCoordinateGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGUVCoordinateGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGUVCoordinateGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGUVCoordinateGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_525_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGUVCoordinateGeneratorSettings(UAuracronPCGUVCoordinateGeneratorSettings&&) = delete; \
	UAuracronPCGUVCoordinateGeneratorSettings(const UAuracronPCGUVCoordinateGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGUVCoordinateGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGUVCoordinateGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGUVCoordinateGeneratorSettings) \
	NO_API virtual ~UAuracronPCGUVCoordinateGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_522_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_525_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_525_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_525_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGUVCoordinateGeneratorSettings;

// ********** End Class UAuracronPCGUVCoordinateGeneratorSettings **********************************

// ********** Begin Class UAuracronPCGMaterialParameterControllerSettings **************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_594_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMaterialParameterControllerSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMaterialParameterControllerSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMaterialParameterControllerSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMaterialParameterControllerSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_594_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMaterialParameterControllerSettings(UAuracronPCGMaterialParameterControllerSettings&&) = delete; \
	UAuracronPCGMaterialParameterControllerSettings(const UAuracronPCGMaterialParameterControllerSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMaterialParameterControllerSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMaterialParameterControllerSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGMaterialParameterControllerSettings) \
	NO_API virtual ~UAuracronPCGMaterialParameterControllerSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_591_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_594_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_594_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_594_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMaterialParameterControllerSettings;

// ********** End Class UAuracronPCGMaterialParameterControllerSettings ****************************

// ********** Begin Class UAuracronPCGMaterialSystemUtils ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateDefaultMaterialSelectionDescriptor); \
	DECLARE_FUNCTION(execValidateUVGenerationDescriptor); \
	DECLARE_FUNCTION(execValidateMaterialBlendingDescriptor); \
	DECLARE_FUNCTION(execValidateMaterialSelectionDescriptor); \
	DECLARE_FUNCTION(execGetAttributeAsColor); \
	DECLARE_FUNCTION(execGetAttributeAsVector); \
	DECLARE_FUNCTION(execGetAttributeAsFloat); \
	DECLARE_FUNCTION(execSetMaterialParameter); \
	DECLARE_FUNCTION(execGenerateTriplanarUV); \
	DECLARE_FUNCTION(execGenerateSphericalUV); \
	DECLARE_FUNCTION(execGenerateCylindricalUV); \
	DECLARE_FUNCTION(execGeneratePlanarUV); \
	DECLARE_FUNCTION(execGenerateWorldSpaceUV); \
	DECLARE_FUNCTION(execGenerateUVCoordinates); \
	DECLARE_FUNCTION(execCreateDynamicMaterialInstance); \
	DECLARE_FUNCTION(execCalculateBlendWeight); \
	DECLARE_FUNCTION(execBlendMaterials); \
	DECLARE_FUNCTION(execGetMaterialByDistance); \
	DECLARE_FUNCTION(execGetMaterialIndexByAttribute); \
	DECLARE_FUNCTION(execSelectMultipleMaterials); \
	DECLARE_FUNCTION(execSelectMaterial);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGMaterialSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGMaterialSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGMaterialSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGMaterialSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGMaterialSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGMaterialSystemUtils(UAuracronPCGMaterialSystemUtils&&) = delete; \
	UAuracronPCGMaterialSystemUtils(const UAuracronPCGMaterialSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGMaterialSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGMaterialSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGMaterialSystemUtils) \
	NO_API virtual ~UAuracronPCGMaterialSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_658_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h_661_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGMaterialSystemUtils;

// ********** End Class UAuracronPCGMaterialSystemUtils ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGMaterialSystem_h

// ********** Begin Enum EAuracronPCGMaterialSelectionMode *****************************************
#define FOREACH_ENUM_EAURACRONPCGMATERIALSELECTIONMODE(op) \
	op(EAuracronPCGMaterialSelectionMode::ByAttribute) \
	op(EAuracronPCGMaterialSelectionMode::ByDistance) \
	op(EAuracronPCGMaterialSelectionMode::ByHeight) \
	op(EAuracronPCGMaterialSelectionMode::BySlope) \
	op(EAuracronPCGMaterialSelectionMode::ByNoise) \
	op(EAuracronPCGMaterialSelectionMode::ByDensity) \
	op(EAuracronPCGMaterialSelectionMode::ByProximity) \
	op(EAuracronPCGMaterialSelectionMode::ByRandom) \
	op(EAuracronPCGMaterialSelectionMode::ByBiome) \
	op(EAuracronPCGMaterialSelectionMode::ByLighting) \
	op(EAuracronPCGMaterialSelectionMode::ByWeather) \
	op(EAuracronPCGMaterialSelectionMode::Custom) 

enum class EAuracronPCGMaterialSelectionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMaterialSelectionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialSelectionMode>();
// ********** End Enum EAuracronPCGMaterialSelectionMode *******************************************

// ********** Begin Enum EAuracronPCGMaterialBlendMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGMATERIALBLENDMODE(op) \
	op(EAuracronPCGMaterialBlendMode::Replace) \
	op(EAuracronPCGMaterialBlendMode::Additive) \
	op(EAuracronPCGMaterialBlendMode::Multiply) \
	op(EAuracronPCGMaterialBlendMode::Overlay) \
	op(EAuracronPCGMaterialBlendMode::SoftLight) \
	op(EAuracronPCGMaterialBlendMode::HardLight) \
	op(EAuracronPCGMaterialBlendMode::Screen) \
	op(EAuracronPCGMaterialBlendMode::ColorBurn) \
	op(EAuracronPCGMaterialBlendMode::ColorDodge) \
	op(EAuracronPCGMaterialBlendMode::Difference) \
	op(EAuracronPCGMaterialBlendMode::Exclusion) \
	op(EAuracronPCGMaterialBlendMode::LinearBurn) \
	op(EAuracronPCGMaterialBlendMode::LinearDodge) \
	op(EAuracronPCGMaterialBlendMode::VividLight) \
	op(EAuracronPCGMaterialBlendMode::LinearLight) \
	op(EAuracronPCGMaterialBlendMode::PinLight) \
	op(EAuracronPCGMaterialBlendMode::Custom) 

enum class EAuracronPCGMaterialBlendMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMaterialBlendMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialBlendMode>();
// ********** End Enum EAuracronPCGMaterialBlendMode ***********************************************

// ********** Begin Enum EAuracronPCGUVGenerationMode **********************************************
#define FOREACH_ENUM_EAURACRONPCGUVGENERATIONMODE(op) \
	op(EAuracronPCGUVGenerationMode::WorldSpace) \
	op(EAuracronPCGUVGenerationMode::LocalSpace) \
	op(EAuracronPCGUVGenerationMode::Planar) \
	op(EAuracronPCGUVGenerationMode::Cylindrical) \
	op(EAuracronPCGUVGenerationMode::Spherical) \
	op(EAuracronPCGUVGenerationMode::Box) \
	op(EAuracronPCGUVGenerationMode::Triplanar) \
	op(EAuracronPCGUVGenerationMode::FromAttribute) \
	op(EAuracronPCGUVGenerationMode::Procedural) \
	op(EAuracronPCGUVGenerationMode::Custom) 

enum class EAuracronPCGUVGenerationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGUVGenerationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGUVGenerationMode>();
// ********** End Enum EAuracronPCGUVGenerationMode ************************************************

// ********** Begin Enum EAuracronPCGTextureCoordinateChannel **************************************
#define FOREACH_ENUM_EAURACRONPCGTEXTURECOORDINATECHANNEL(op) \
	op(EAuracronPCGTextureCoordinateChannel::UV0) \
	op(EAuracronPCGTextureCoordinateChannel::UV1) \
	op(EAuracronPCGTextureCoordinateChannel::UV2) \
	op(EAuracronPCGTextureCoordinateChannel::UV3) \
	op(EAuracronPCGTextureCoordinateChannel::UV4) \
	op(EAuracronPCGTextureCoordinateChannel::UV5) \
	op(EAuracronPCGTextureCoordinateChannel::UV6) \
	op(EAuracronPCGTextureCoordinateChannel::UV7) 

enum class EAuracronPCGTextureCoordinateChannel : uint8;
template<> struct TIsUEnumClass<EAuracronPCGTextureCoordinateChannel> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGTextureCoordinateChannel>();
// ********** End Enum EAuracronPCGTextureCoordinateChannel ****************************************

// ********** Begin Enum EAuracronPCGMaterialParameterType *****************************************
#define FOREACH_ENUM_EAURACRONPCGMATERIALPARAMETERTYPE(op) \
	op(EAuracronPCGMaterialParameterType::Scalar) \
	op(EAuracronPCGMaterialParameterType::Vector) \
	op(EAuracronPCGMaterialParameterType::Color) \
	op(EAuracronPCGMaterialParameterType::Texture) \
	op(EAuracronPCGMaterialParameterType::Boolean) \
	op(EAuracronPCGMaterialParameterType::Custom) 

enum class EAuracronPCGMaterialParameterType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGMaterialParameterType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGMaterialParameterType>();
// ********** End Enum EAuracronPCGMaterialParameterType *******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
