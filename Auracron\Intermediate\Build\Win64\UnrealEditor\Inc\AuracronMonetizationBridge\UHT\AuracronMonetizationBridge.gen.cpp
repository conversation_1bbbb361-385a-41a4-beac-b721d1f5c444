// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMonetizationBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMonetizationBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMONETIZATIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronMonetizationBridge();
AURACRONMONETIZATIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronMonetizationBridge_NoRegister();
AURACRONMONETIZATIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType();
AURACRONMONETIZATIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity();
AURACRONMONETIZATIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType();
AURACRONMONETIZATIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature();
AURACRONMONETIZATIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature();
AURACRONMONETIZATIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBattlePass();
AURACRONMONETIZATIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry();
AURACRONMONETIZATIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics();
AURACRONMONETIZATIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStoreProduct();
AURACRONMONETIZATIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransaction();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
MEDIAASSETS_API UClass* Z_Construct_UClass_UMediaSource_NoRegister();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronMonetizationBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronCurrencyType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCurrencyType;
static UEnum* EAuracronCurrencyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCurrencyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCurrencyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("EAuracronCurrencyType"));
	}
	return Z_Registration_Info_UEnum_EAuracronCurrencyType.OuterSingleton;
}
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronCurrencyType>()
{
	return EAuracronCurrencyType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AuraCoins.DisplayName", "Aura Coins (Premium)" },
		{ "AuraCoins.Name", "EAuracronCurrencyType::AuraCoins" },
		{ "BlueprintType", "true" },
		{ "ChronosShards.DisplayName", "Chronos Shards (Free)" },
		{ "ChronosShards.Name", "EAuracronCurrencyType::ChronosShards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de moeda\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronCurrencyType::None" },
		{ "RealmEssence.DisplayName", "Realm Essence (Earned)" },
		{ "RealmEssence.Name", "EAuracronCurrencyType::RealmEssence" },
		{ "SeasonTokens.DisplayName", "Season Tokens (Limited)" },
		{ "SeasonTokens.Name", "EAuracronCurrencyType::SeasonTokens" },
		{ "SigiloFragments.DisplayName", "Sigilo Fragments (Special)" },
		{ "SigiloFragments.Name", "EAuracronCurrencyType::SigiloFragments" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de moeda" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCurrencyType::None", (int64)EAuracronCurrencyType::None },
		{ "EAuracronCurrencyType::AuraCoins", (int64)EAuracronCurrencyType::AuraCoins },
		{ "EAuracronCurrencyType::ChronosShards", (int64)EAuracronCurrencyType::ChronosShards },
		{ "EAuracronCurrencyType::RealmEssence", (int64)EAuracronCurrencyType::RealmEssence },
		{ "EAuracronCurrencyType::SigiloFragments", (int64)EAuracronCurrencyType::SigiloFragments },
		{ "EAuracronCurrencyType::SeasonTokens", (int64)EAuracronCurrencyType::SeasonTokens },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	"EAuracronCurrencyType",
	"EAuracronCurrencyType",
	Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType()
{
	if (!Z_Registration_Info_UEnum_EAuracronCurrencyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCurrencyType.InnerSingleton, Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCurrencyType.InnerSingleton;
}
// ********** End Enum EAuracronCurrencyType *******************************************************

// ********** Begin Enum EAuracronProductType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronProductType;
static UEnum* EAuracronProductType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronProductType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronProductType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("EAuracronProductType"));
	}
	return Z_Registration_Info_UEnum_EAuracronProductType.OuterSingleton;
}
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronProductType>()
{
	return EAuracronProductType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BattlePass.DisplayName", "Battle Pass" },
		{ "BattlePass.Name", "EAuracronProductType::BattlePass" },
		{ "BlueprintType", "true" },
		{ "Bundle.DisplayName", "Bundle" },
		{ "Bundle.Name", "EAuracronProductType::Bundle" },
		{ "Champion.DisplayName", "Champion" },
		{ "Champion.Name", "EAuracronProductType::Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de produto\n */" },
#endif
		{ "Cosmetic.DisplayName", "Cosmetic" },
		{ "Cosmetic.Name", "EAuracronProductType::Cosmetic" },
		{ "CurrencyPack.DisplayName", "Currency Pack" },
		{ "CurrencyPack.Name", "EAuracronProductType::CurrencyPack" },
		{ "Emote.DisplayName", "Emote" },
		{ "Emote.Name", "EAuracronProductType::Emote" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronProductType::None" },
		{ "SeasonalItem.DisplayName", "Seasonal Item" },
		{ "SeasonalItem.Name", "EAuracronProductType::SeasonalItem" },
		{ "Skin.DisplayName", "Skin" },
		{ "Skin.Name", "EAuracronProductType::Skin" },
		{ "Subscription.DisplayName", "Subscription" },
		{ "Subscription.Name", "EAuracronProductType::Subscription" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de produto" },
#endif
		{ "VoicePack.DisplayName", "Voice Pack" },
		{ "VoicePack.Name", "EAuracronProductType::VoicePack" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronProductType::None", (int64)EAuracronProductType::None },
		{ "EAuracronProductType::Champion", (int64)EAuracronProductType::Champion },
		{ "EAuracronProductType::Skin", (int64)EAuracronProductType::Skin },
		{ "EAuracronProductType::BattlePass", (int64)EAuracronProductType::BattlePass },
		{ "EAuracronProductType::CurrencyPack", (int64)EAuracronProductType::CurrencyPack },
		{ "EAuracronProductType::Subscription", (int64)EAuracronProductType::Subscription },
		{ "EAuracronProductType::Cosmetic", (int64)EAuracronProductType::Cosmetic },
		{ "EAuracronProductType::Emote", (int64)EAuracronProductType::Emote },
		{ "EAuracronProductType::VoicePack", (int64)EAuracronProductType::VoicePack },
		{ "EAuracronProductType::Bundle", (int64)EAuracronProductType::Bundle },
		{ "EAuracronProductType::SeasonalItem", (int64)EAuracronProductType::SeasonalItem },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	"EAuracronProductType",
	"EAuracronProductType",
	Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType()
{
	if (!Z_Registration_Info_UEnum_EAuracronProductType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronProductType.InnerSingleton, Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronProductType.InnerSingleton;
}
// ********** End Enum EAuracronProductType ********************************************************

// ********** Begin Enum EAuracronItemRarity *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronItemRarity;
static UEnum* EAuracronItemRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronItemRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronItemRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("EAuracronItemRarity"));
	}
	return Z_Registration_Info_UEnum_EAuracronItemRarity.OuterSingleton;
}
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronItemRarity>()
{
	return EAuracronItemRarity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de item\n */" },
#endif
		{ "Common.DisplayName", "Common" },
		{ "Common.Name", "EAuracronItemRarity::Common" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronItemRarity::Epic" },
		{ "Exclusive.DisplayName", "Exclusive" },
		{ "Exclusive.Name", "EAuracronItemRarity::Exclusive" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EAuracronItemRarity::Legendary" },
		{ "Limited.DisplayName", "Limited Edition" },
		{ "Limited.Name", "EAuracronItemRarity::Limited" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
		{ "Mythic.DisplayName", "Mythic" },
		{ "Mythic.Name", "EAuracronItemRarity::Mythic" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EAuracronItemRarity::Rare" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de item" },
#endif
		{ "Uncommon.DisplayName", "Uncommon" },
		{ "Uncommon.Name", "EAuracronItemRarity::Uncommon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronItemRarity::Common", (int64)EAuracronItemRarity::Common },
		{ "EAuracronItemRarity::Uncommon", (int64)EAuracronItemRarity::Uncommon },
		{ "EAuracronItemRarity::Rare", (int64)EAuracronItemRarity::Rare },
		{ "EAuracronItemRarity::Epic", (int64)EAuracronItemRarity::Epic },
		{ "EAuracronItemRarity::Legendary", (int64)EAuracronItemRarity::Legendary },
		{ "EAuracronItemRarity::Mythic", (int64)EAuracronItemRarity::Mythic },
		{ "EAuracronItemRarity::Limited", (int64)EAuracronItemRarity::Limited },
		{ "EAuracronItemRarity::Exclusive", (int64)EAuracronItemRarity::Exclusive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	"EAuracronItemRarity",
	"EAuracronItemRarity",
	Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity()
{
	if (!Z_Registration_Info_UEnum_EAuracronItemRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronItemRarity.InnerSingleton, Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronItemRarity.InnerSingleton;
}
// ********** End Enum EAuracronItemRarity *********************************************************

// ********** Begin ScriptStruct FAuracronStoreProduct *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStoreProduct;
class UScriptStruct* FAuracronStoreProduct::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStoreProduct, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("AuracronStoreProduct"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para produto da loja\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para produto da loja" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductDescription_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductType_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemRarity_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade do item */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade do item" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PremiumPrice_MetaData[] = {
		{ "Category", "Store Product" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pre\xc3\x83\xc2\xa7o em moeda premium */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pre\xc3\x83\xc2\xa7o em moeda premium" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FreePrice_MetaData[] = {
		{ "Category", "Store Product" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pre\xc3\x83\xc2\xa7o em moeda gratuita */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pre\xc3\x83\xc2\xa7o em moeda gratuita" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealMoneyPrice_MetaData[] = {
		{ "Category", "Store Product" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pre\xc3\x83\xc2\xa7o em dinheiro real (centavos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pre\xc3\x83\xc2\xa7o em dinheiro real (centavos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AcceptedCurrency_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de moeda aceita */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de moeda aceita" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAvailable_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produto est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produto est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTimeLimited_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produto \xc3\x83\xc2\xa9 limitado por tempo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produto \xc3\x83\xc2\xa9 limitado por tempo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpirationDate_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de expira\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiscountPercentage_MetaData[] = {
		{ "Category", "Store Product" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desconto aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desconto aplicado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFeatured_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produto \xc3\x83\xc2\xa9 destaque */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produto \xc3\x83\xc2\xa9 destaque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsNew_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produto \xc3\x83\xc2\xa9 novo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produto \xc3\x83\xc2\xa9 novo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductIcon_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc2\x8d""cone do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc2\x8d""cone do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewImage_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Imagem de preview */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Imagem de preview" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewVideo_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** V\xc3\x83\xc2\xad""deo de preview */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "V\xc3\x83\xc2\xad""deo de preview" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductCategory_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Categoria do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductTags_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductMetadata_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Metadados do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Metadados do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumAge_MetaData[] = {
		{ "Category", "Store Product" },
		{ "ClampMax", "21" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer idade m\xc3\x83\xc2\xadnima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer idade m\xc3\x83\xc2\xadnima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableRegions_MetaData[] = {
		{ "Category", "Store Product" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\x83\xc2\xb5""es onde est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\x83\xc2\xb5""es onde est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ProductName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ProductDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProductType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProductType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ItemRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ItemRarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PremiumPrice;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FreePrice;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealMoneyPrice;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AcceptedCurrency_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AcceptedCurrency;
	static void NewProp_bIsAvailable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAvailable;
	static void NewProp_bIsTimeLimited_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTimeLimited;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExpirationDate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DiscountPercentage;
	static void NewProp_bIsFeatured_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFeatured;
	static void NewProp_bIsNew_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNew;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProductIcon;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewImage;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewVideo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductCategory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProductTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductMetadata_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductMetadata_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ProductMetadata;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinimumAge;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AvailableRegions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableRegions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStoreProduct>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductName_MetaData), NewProp_ProductName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductDescription = { "ProductDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductDescription_MetaData), NewProp_ProductDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductType = { "ProductType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronProductType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductType_MetaData), NewProp_ProductType_MetaData) }; // 1955226746
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ItemRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ItemRarity = { "ItemRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ItemRarity), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronItemRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemRarity_MetaData), NewProp_ItemRarity_MetaData) }; // 421683325
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PremiumPrice = { "PremiumPrice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, PremiumPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PremiumPrice_MetaData), NewProp_PremiumPrice_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_FreePrice = { "FreePrice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, FreePrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FreePrice_MetaData), NewProp_FreePrice_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_RealMoneyPrice = { "RealMoneyPrice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, RealMoneyPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealMoneyPrice_MetaData), NewProp_RealMoneyPrice_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AcceptedCurrency_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AcceptedCurrency = { "AcceptedCurrency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, AcceptedCurrency), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AcceptedCurrency_MetaData), NewProp_AcceptedCurrency_MetaData) }; // 776395100
void Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsAvailable_SetBit(void* Obj)
{
	((FAuracronStoreProduct*)Obj)->bIsAvailable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsAvailable = { "bIsAvailable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStoreProduct), &Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsAvailable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAvailable_MetaData), NewProp_bIsAvailable_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsTimeLimited_SetBit(void* Obj)
{
	((FAuracronStoreProduct*)Obj)->bIsTimeLimited = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsTimeLimited = { "bIsTimeLimited", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStoreProduct), &Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsTimeLimited_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTimeLimited_MetaData), NewProp_bIsTimeLimited_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ExpirationDate = { "ExpirationDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ExpirationDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpirationDate_MetaData), NewProp_ExpirationDate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_DiscountPercentage = { "DiscountPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, DiscountPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiscountPercentage_MetaData), NewProp_DiscountPercentage_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsFeatured_SetBit(void* Obj)
{
	((FAuracronStoreProduct*)Obj)->bIsFeatured = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsFeatured = { "bIsFeatured", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStoreProduct), &Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsFeatured_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFeatured_MetaData), NewProp_bIsFeatured_MetaData) };
void Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsNew_SetBit(void* Obj)
{
	((FAuracronStoreProduct*)Obj)->bIsNew = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsNew = { "bIsNew", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronStoreProduct), &Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsNew_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsNew_MetaData), NewProp_bIsNew_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductIcon = { "ProductIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductIcon_MetaData), NewProp_ProductIcon_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PreviewImage = { "PreviewImage", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, PreviewImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewImage_MetaData), NewProp_PreviewImage_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PreviewVideo = { "PreviewVideo", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, PreviewVideo), Z_Construct_UClass_UMediaSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewVideo_MetaData), NewProp_PreviewVideo_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductCategory = { "ProductCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductCategory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductCategory_MetaData), NewProp_ProductCategory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductTags = { "ProductTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductTags_MetaData), NewProp_ProductTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata_ValueProp = { "ProductMetadata", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata_Key_KeyProp = { "ProductMetadata_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata = { "ProductMetadata", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, ProductMetadata), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductMetadata_MetaData), NewProp_ProductMetadata_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_MinimumAge = { "MinimumAge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, MinimumAge), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumAge_MetaData), NewProp_MinimumAge_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AvailableRegions_Inner = { "AvailableRegions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AvailableRegions = { "AvailableRegions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStoreProduct, AvailableRegions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableRegions_MetaData), NewProp_AvailableRegions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ItemRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ItemRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PremiumPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_FreePrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_RealMoneyPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AcceptedCurrency_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AcceptedCurrency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsAvailable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsTimeLimited,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ExpirationDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_DiscountPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsFeatured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_bIsNew,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PreviewImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_PreviewVideo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_ProductMetadata,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_MinimumAge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AvailableRegions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewProp_AvailableRegions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	&NewStructOps,
	"AuracronStoreProduct",
	Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::PropPointers),
	sizeof(FAuracronStoreProduct),
	alignof(FAuracronStoreProduct),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStoreProduct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStoreProduct.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStoreProduct ***********************************************

// ********** Begin ScriptStruct FAuracronBattlePass ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBattlePass;
class UScriptStruct* FAuracronBattlePass::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBattlePass.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBattlePass.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBattlePass, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("AuracronBattlePass"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBattlePass.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBattlePass_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para Battle Pass\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BattlePassID_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do Battle Pass */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BattlePassName_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do Battle Pass */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Season_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temporada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temporada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Battle Pass" },
		{ "ClampMax", "100" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentXP_MetaData[] = {
		{ "Category", "Battle Pass" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** XP atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "XP atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_XPToNextLevel_MetaData[] = {
		{ "Category", "Battle Pass" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** XP necess\xc3\x83\xc2\xa1rio para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "XP necess\xc3\x83\xc2\xa1rio para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPremiumPurchased_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Battle Pass premium foi comprado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Battle Pass premium foi comprado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartDate_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de in\xc3\x83\xc2\xad""cio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de in\xc3\x83\xc2\xad""cio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndDate_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de fim */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de fim" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FreeRewards_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas gratuitas (n\xc3\xa3o pode ser replicado) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas gratuitas (n\xc3\xa3o pode ser replicado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PremiumRewards_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas premium (n\xc3\xa3o pode ser replicado) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas premium (n\xc3\xa3o pode ser replicado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectedRewards_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas coletadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas coletadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PremiumPrice_MetaData[] = {
		{ "Category", "Battle Pass" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pre\xc3\x83\xc2\xa7o do Battle Pass premium */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pre\xc3\x83\xc2\xa7o do Battle Pass premium" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bXPBoostActive_MetaData[] = {
		{ "Category", "Battle Pass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Boost de XP ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Boost de XP ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_XPMultiplier_MetaData[] = {
		{ "Category", "Battle Pass" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de XP */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de XP" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BattlePassID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_BattlePassName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Season;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentXP;
	static const UECodeGen_Private::FIntPropertyParams NewProp_XPToNextLevel;
	static void NewProp_bPremiumPurchased_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPremiumPurchased;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndDate;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FreeRewards_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FreeRewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FreeRewards;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PremiumRewards_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PremiumRewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PremiumRewards;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollectedRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CollectedRewards;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PremiumPrice;
	static void NewProp_bXPBoostActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bXPBoostActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_XPMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBattlePass>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_BattlePassID = { "BattlePassID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, BattlePassID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BattlePassID_MetaData), NewProp_BattlePassID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_BattlePassName = { "BattlePassName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, BattlePassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BattlePassName_MetaData), NewProp_BattlePassName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, Season), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Season_MetaData), NewProp_Season_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CurrentXP = { "CurrentXP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, CurrentXP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentXP_MetaData), NewProp_CurrentXP_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_XPToNextLevel = { "XPToNextLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, XPToNextLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_XPToNextLevel_MetaData), NewProp_XPToNextLevel_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bPremiumPurchased_SetBit(void* Obj)
{
	((FAuracronBattlePass*)Obj)->bPremiumPurchased = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bPremiumPurchased = { "bPremiumPurchased", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBattlePass), &Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bPremiumPurchased_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPremiumPurchased_MetaData), NewProp_bPremiumPurchased_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_StartDate = { "StartDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, StartDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartDate_MetaData), NewProp_StartDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_EndDate = { "EndDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, EndDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndDate_MetaData), NewProp_EndDate_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards_ValueProp = { "FreeRewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards_Key_KeyProp = { "FreeRewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards = { "FreeRewards", nullptr, (EPropertyFlags)0x0010000080000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, FreeRewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FreeRewards_MetaData), NewProp_FreeRewards_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards_ValueProp = { "PremiumRewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards_Key_KeyProp = { "PremiumRewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards = { "PremiumRewards", nullptr, (EPropertyFlags)0x0010000080000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, PremiumRewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PremiumRewards_MetaData), NewProp_PremiumRewards_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CollectedRewards_Inner = { "CollectedRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CollectedRewards = { "CollectedRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, CollectedRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectedRewards_MetaData), NewProp_CollectedRewards_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumPrice = { "PremiumPrice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, PremiumPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PremiumPrice_MetaData), NewProp_PremiumPrice_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bXPBoostActive_SetBit(void* Obj)
{
	((FAuracronBattlePass*)Obj)->bXPBoostActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bXPBoostActive = { "bXPBoostActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBattlePass), &Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bXPBoostActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bXPBoostActive_MetaData), NewProp_bXPBoostActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_XPMultiplier = { "XPMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBattlePass, XPMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_XPMultiplier_MetaData), NewProp_XPMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_BattlePassID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_BattlePassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CurrentXP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_XPToNextLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bPremiumPurchased,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_StartDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_EndDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_FreeRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CollectedRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_CollectedRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_PremiumPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_bXPBoostActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewProp_XPMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	&NewStructOps,
	"AuracronBattlePass",
	Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::PropPointers),
	sizeof(FAuracronBattlePass),
	alignof(FAuracronBattlePass),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBattlePass()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBattlePass.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBattlePass.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBattlePass.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBattlePass *************************************************

// ********** Begin ScriptStruct FAuracronPurchaseAnalytics ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics;
class UScriptStruct* FAuracronPurchaseAnalytics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("AuracronPurchaseAnalytics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para analytics de compras\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para analytics de compras" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionID_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductCategory_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PricePaid_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrencyType_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurchaseTimestamp_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLevel_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionDuration_MetaData[] = {
		{ "Category", "Purchase Analytics" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductCategory;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PricePaid;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PurchaseTimestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SessionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPurchaseAnalytics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_TransactionID = { "TransactionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, TransactionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionID_MetaData), NewProp_TransactionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, ProductName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductName_MetaData), NewProp_ProductName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductCategory = { "ProductCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, ProductCategory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductCategory_MetaData), NewProp_ProductCategory_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PricePaid = { "PricePaid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, PricePaid), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PricePaid_MetaData), NewProp_PricePaid_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrencyType_MetaData), NewProp_CurrencyType_MetaData) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PurchaseTimestamp = { "PurchaseTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, PurchaseTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurchaseTimestamp_MetaData), NewProp_PurchaseTimestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PlayerLevel = { "PlayerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, PlayerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLevel_MetaData), NewProp_PlayerLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_SessionDuration = { "SessionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPurchaseAnalytics, SessionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionDuration_MetaData), NewProp_SessionDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_TransactionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_ProductCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PricePaid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PurchaseTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_PlayerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewProp_SessionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	&NewStructOps,
	"AuracronPurchaseAnalytics",
	Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::PropPointers),
	sizeof(FAuracronPurchaseAnalytics),
	alignof(FAuracronPurchaseAnalytics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPurchaseAnalytics ******************************************

// ********** Begin ScriptStruct FAuracronCurrencyBalanceEntry *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry;
class UScriptStruct* FAuracronCurrencyBalanceEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("AuracronCurrencyBalanceEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de saldo de moeda (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de saldo de moeda (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrencyType_MetaData[] = {
		{ "Category", "Currency Balance" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Balance_MetaData[] = {
		{ "Category", "Currency Balance" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Balance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCurrencyBalanceEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCurrencyBalanceEntry, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrencyType_MetaData), NewProp_CurrencyType_MetaData) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_Balance = { "Balance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCurrencyBalanceEntry, Balance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Balance_MetaData), NewProp_Balance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewProp_Balance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	&NewStructOps,
	"AuracronCurrencyBalanceEntry",
	Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::PropPointers),
	sizeof(FAuracronCurrencyBalanceEntry),
	alignof(FAuracronCurrencyBalanceEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCurrencyBalanceEntry ***************************************

// ********** Begin ScriptStruct FAuracronTransaction **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTransaction;
class UScriptStruct* FAuracronTransaction::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransaction.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTransaction.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTransaction, (UObject*)Z_Construct_UPackage__Script_AuracronMonetizationBridge(), TEXT("AuracronTransaction"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransaction.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTransaction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para transa\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para transa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionID_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do produto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Transaction" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quantidade comprada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantidade comprada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PricePaid_MetaData[] = {
		{ "Category", "Transaction" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pre\xc3\x83\xc2\xa7o pago */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pre\xc3\x83\xc2\xa7o pago" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrencyUsed_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de moeda usada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de moeda usada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionTime_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionStatus_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status da transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurchasePlatform_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Plataforma da compra */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Plataforma da compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlatformReceipt_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Receipt da plataforma */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Receipt da plataforma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValidated_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi validada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi validada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsProcessed_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi processada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi processada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalData_MetaData[] = {
		{ "Category", "Transaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dados adicionais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados adicionais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PricePaid;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyUsed_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyUsed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransactionTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionStatus;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PurchasePlatform;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlatformReceipt;
	static void NewProp_bIsValidated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValidated;
	static void NewProp_bIsProcessed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsProcessed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AdditionalData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTransaction>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionID = { "TransactionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, TransactionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionID_MetaData), NewProp_TransactionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PricePaid = { "PricePaid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, PricePaid), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PricePaid_MetaData), NewProp_PricePaid_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_CurrencyUsed_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_CurrencyUsed = { "CurrencyUsed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, CurrencyUsed), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrencyUsed_MetaData), NewProp_CurrencyUsed_MetaData) }; // 776395100
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionTime = { "TransactionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, TransactionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionTime_MetaData), NewProp_TransactionTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionStatus = { "TransactionStatus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, TransactionStatus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionStatus_MetaData), NewProp_TransactionStatus_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PurchasePlatform = { "PurchasePlatform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, PurchasePlatform), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurchasePlatform_MetaData), NewProp_PurchasePlatform_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PlatformReceipt = { "PlatformReceipt", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, PlatformReceipt), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlatformReceipt_MetaData), NewProp_PlatformReceipt_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsValidated_SetBit(void* Obj)
{
	((FAuracronTransaction*)Obj)->bIsValidated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsValidated = { "bIsValidated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTransaction), &Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsValidated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValidated_MetaData), NewProp_bIsValidated_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsProcessed_SetBit(void* Obj)
{
	((FAuracronTransaction*)Obj)->bIsProcessed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsProcessed = { "bIsProcessed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTransaction), &Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsProcessed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsProcessed_MetaData), NewProp_bIsProcessed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData_ValueProp = { "AdditionalData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData_Key_KeyProp = { "AdditionalData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData = { "AdditionalData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransaction, AdditionalData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalData_MetaData), NewProp_AdditionalData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTransaction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PricePaid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_CurrencyUsed_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_CurrencyUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_TransactionStatus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PurchasePlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_PlatformReceipt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsValidated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_bIsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewProp_AdditionalData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransaction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTransaction_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
	nullptr,
	&NewStructOps,
	"AuracronTransaction",
	Z_Construct_UScriptStruct_FAuracronTransaction_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransaction_Statics::PropPointers),
	sizeof(FAuracronTransaction),
	alignof(FAuracronTransaction),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransaction_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTransaction_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransaction()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransaction.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTransaction.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTransaction_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransaction.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTransaction ************************************************

// ********** Begin Delegate FOnPurchaseCompleted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics
{
	struct AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms
	{
		FAuracronTransaction Transaction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando compra \xc3\x83\xc2\xa9 completada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando compra \xc3\x83\xc2\xa9 completada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transaction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::NewProp_Transaction = { "Transaction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms, Transaction), Z_Construct_UScriptStruct_FAuracronTransaction, METADATA_PARAMS(0, nullptr) }; // 3792508869
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::NewProp_Transaction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "OnPurchaseCompleted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronMonetizationBridge::FOnPurchaseCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPurchaseCompleted, FAuracronTransaction Transaction)
{
	struct AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms
	{
		FAuracronTransaction Transaction;
	};
	AuracronMonetizationBridge_eventOnPurchaseCompleted_Parms Parms;
	Parms.Transaction=Transaction;
	OnPurchaseCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPurchaseCompleted ****************************************************

// ********** Begin Delegate FOnBattlePassLevelUp **************************************************
struct Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics
{
	struct AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms
	{
		int32 OldLevel;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Battle Pass level up */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Battle Pass level up" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms, OldLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::NewProp_OldLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "OnBattlePassLevelUp__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronMonetizationBridge::FOnBattlePassLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnBattlePassLevelUp, int32 OldLevel, int32 NewLevel)
{
	struct AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms
	{
		int32 OldLevel;
		int32 NewLevel;
	};
	AuracronMonetizationBridge_eventOnBattlePassLevelUp_Parms Parms;
	Parms.OldLevel=OldLevel;
	Parms.NewLevel=NewLevel;
	OnBattlePassLevelUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBattlePassLevelUp ****************************************************

// ********** Begin Class UAuracronMonetizationBridge Function ActivateSubscription ****************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics
{
	struct AuracronMonetizationBridge_eventActivateSubscription_Parms
	{
		FString SubscriptionType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Subscription" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar assinatura\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar assinatura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubscriptionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SubscriptionType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_SubscriptionType = { "SubscriptionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventActivateSubscription_Parms, SubscriptionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubscriptionType_MetaData), NewProp_SubscriptionType_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventActivateSubscription_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventActivateSubscription_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_SubscriptionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "ActivateSubscription", Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::AuracronMonetizationBridge_eventActivateSubscription_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::AuracronMonetizationBridge_eventActivateSubscription_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execActivateSubscription)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SubscriptionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateSubscription(Z_Param_SubscriptionType);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function ActivateSubscription ******************

// ********** Begin Class UAuracronMonetizationBridge Function AddBattlePassXP *********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics
{
	struct AuracronMonetizationBridge_eventAddBattlePassXP_Parms
	{
		int32 XPAmount;
		FString Source;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|BattlePass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar XP ao Battle Pass\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar XP ao Battle Pass" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_XPAmount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Source;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_XPAmount = { "XPAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventAddBattlePassXP_Parms, XPAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventAddBattlePassXP_Parms, Source), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventAddBattlePassXP_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventAddBattlePassXP_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_XPAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "AddBattlePassXP", Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::AuracronMonetizationBridge_eventAddBattlePassXP_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::AuracronMonetizationBridge_eventAddBattlePassXP_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execAddBattlePassXP)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_XPAmount);
	P_GET_PROPERTY(FStrProperty,Z_Param_Source);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddBattlePassXP(Z_Param_XPAmount,Z_Param_Source);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function AddBattlePassXP ***********************

// ********** Begin Class UAuracronMonetizationBridge Function AddCurrency *************************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics
{
	struct AuracronMonetizationBridge_eventAddCurrency_Parms
	{
		EAuracronCurrencyType CurrencyType;
		int32 Amount;
		FString Source;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar moeda\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar moeda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Source;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventAddCurrency_Parms, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(0, nullptr) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventAddCurrency_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventAddCurrency_Parms, Source), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventAddCurrency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventAddCurrency_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "AddCurrency", Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::AuracronMonetizationBridge_eventAddCurrency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::AuracronMonetizationBridge_eventAddCurrency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execAddCurrency)
{
	P_GET_ENUM(EAuracronCurrencyType,Z_Param_CurrencyType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_GET_PROPERTY(FStrProperty,Z_Param_Source);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddCurrency(EAuracronCurrencyType(Z_Param_CurrencyType),Z_Param_Amount,Z_Param_Source);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function AddCurrency ***************************

// ********** Begin Class UAuracronMonetizationBridge Function CancelPurchase **********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics
{
	struct AuracronMonetizationBridge_eventCancelPurchase_Parms
	{
		FString TransactionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Purchase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar compra\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_TransactionID = { "TransactionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventCancelPurchase_Parms, TransactionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionID_MetaData), NewProp_TransactionID_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventCancelPurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventCancelPurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_TransactionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "CancelPurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::AuracronMonetizationBridge_eventCancelPurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::AuracronMonetizationBridge_eventCancelPurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execCancelPurchase)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransactionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelPurchase(Z_Param_TransactionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function CancelPurchase ************************

// ********** Begin Class UAuracronMonetizationBridge Function CancelSubscription ******************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics
{
	struct AuracronMonetizationBridge_eventCancelSubscription_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Subscription" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar assinatura\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar assinatura" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventCancelSubscription_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventCancelSubscription_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "CancelSubscription", Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::AuracronMonetizationBridge_eventCancelSubscription_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::AuracronMonetizationBridge_eventCancelSubscription_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execCancelSubscription)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelSubscription();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function CancelSubscription ********************

// ********** Begin Class UAuracronMonetizationBridge Function ClaimBattlePassReward ***************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics
{
	struct AuracronMonetizationBridge_eventClaimBattlePassReward_Parms
	{
		int32 Level;
		bool bPremiumReward;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|BattlePass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar recompensa do Battle Pass\n     */" },
#endif
		{ "CPP_Default_bPremiumReward", "false" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar recompensa do Battle Pass" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static void NewProp_bPremiumReward_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPremiumReward;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventClaimBattlePassReward_Parms, Level), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_bPremiumReward_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventClaimBattlePassReward_Parms*)Obj)->bPremiumReward = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_bPremiumReward = { "bPremiumReward", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventClaimBattlePassReward_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_bPremiumReward_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventClaimBattlePassReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventClaimBattlePassReward_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_bPremiumReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "ClaimBattlePassReward", Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::AuracronMonetizationBridge_eventClaimBattlePassReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::AuracronMonetizationBridge_eventClaimBattlePassReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execClaimBattlePassReward)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Level);
	P_GET_UBOOL(Z_Param_bPremiumReward);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ClaimBattlePassReward(Z_Param_Level,Z_Param_bPremiumReward);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function ClaimBattlePassReward *****************

// ********** Begin Class UAuracronMonetizationBridge Function FinalizePurchase ********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics
{
	struct AuracronMonetizationBridge_eventFinalizePurchase_Parms
	{
		FString TransactionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Purchase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Finalizar compra\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Finalizar compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_TransactionID = { "TransactionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventFinalizePurchase_Parms, TransactionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionID_MetaData), NewProp_TransactionID_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventFinalizePurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventFinalizePurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_TransactionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "FinalizePurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::AuracronMonetizationBridge_eventFinalizePurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::AuracronMonetizationBridge_eventFinalizePurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execFinalizePurchase)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransactionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FinalizePurchase(Z_Param_TransactionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function FinalizePurchase **********************

// ********** Begin Class UAuracronMonetizationBridge Function GetBattlePassProgress ***************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics
{
	struct AuracronMonetizationBridge_eventGetBattlePassProgress_Parms
	{
		FAuracronBattlePass ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|BattlePass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter progresso do Battle Pass\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter progresso do Battle Pass" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetBattlePassProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronBattlePass, METADATA_PARAMS(0, nullptr) }; // 110463762
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetBattlePassProgress", Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::AuracronMonetizationBridge_eventGetBattlePassProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::AuracronMonetizationBridge_eventGetBattlePassProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetBattlePassProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronBattlePass*)Z_Param__Result=P_THIS->GetBattlePassProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetBattlePassProgress *****************

// ********** Begin Class UAuracronMonetizationBridge Function GetCurrencyBalance ******************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics
{
	struct AuracronMonetizationBridge_eventGetCurrencyBalance_Parms
	{
		EAuracronCurrencyType CurrencyType;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter saldo de moeda\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter saldo de moeda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetCurrencyBalance_Parms, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(0, nullptr) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetCurrencyBalance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetCurrencyBalance", Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::AuracronMonetizationBridge_eventGetCurrencyBalance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::AuracronMonetizationBridge_eventGetCurrencyBalance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetCurrencyBalance)
{
	P_GET_ENUM(EAuracronCurrencyType,Z_Param_CurrencyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrencyBalance(EAuracronCurrencyType(Z_Param_CurrencyType));
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetCurrencyBalance ********************

// ********** Begin Class UAuracronMonetizationBridge Function GetFeaturedProducts *****************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics
{
	struct AuracronMonetizationBridge_eventGetFeaturedProducts_Parms
	{
		TArray<FAuracronStoreProduct> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Store" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter produtos em destaque\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter produtos em destaque" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStoreProduct, METADATA_PARAMS(0, nullptr) }; // 2515431398
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetFeaturedProducts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2515431398
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetFeaturedProducts", Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::AuracronMonetizationBridge_eventGetFeaturedProducts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::AuracronMonetizationBridge_eventGetFeaturedProducts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetFeaturedProducts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStoreProduct>*)Z_Param__Result=P_THIS->GetFeaturedProducts();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetFeaturedProducts *******************

// ********** Begin Class UAuracronMonetizationBridge Function GetMonetizationStatistics ***********
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics
{
	struct AuracronMonetizationBridge_eventGetMonetizationStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\x83\xc2\xadsticas de monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\x83\xc2\xadsticas de monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetMonetizationStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetMonetizationStatistics", Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::AuracronMonetizationBridge_eventGetMonetizationStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::AuracronMonetizationBridge_eventGetMonetizationStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetMonetizationStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetMonetizationStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetMonetizationStatistics *************

// ********** Begin Class UAuracronMonetizationBridge Function GetPendingGifts *********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics
{
	struct AuracronMonetizationBridge_eventGetPendingGifts_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Gifting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter presentes pendentes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter presentes pendentes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetPendingGifts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetPendingGifts", Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::AuracronMonetizationBridge_eventGetPendingGifts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::AuracronMonetizationBridge_eventGetPendingGifts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetPendingGifts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPendingGifts();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetPendingGifts ***********************

// ********** Begin Class UAuracronMonetizationBridge Function GetProductPrice *********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics
{
	struct AuracronMonetizationBridge_eventGetProductPrice_Parms
	{
		FString ProductID;
		EAuracronCurrencyType CurrencyType;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Store" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter pre\xc3\x83\xc2\xa7o do produto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter pre\xc3\x83\xc2\xa7o do produto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetProductPrice_Parms, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetProductPrice_Parms, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(0, nullptr) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetProductPrice_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetProductPrice", Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::AuracronMonetizationBridge_eventGetProductPrice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::AuracronMonetizationBridge_eventGetProductPrice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetProductPrice)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ProductID);
	P_GET_ENUM(EAuracronCurrencyType,Z_Param_CurrencyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetProductPrice(Z_Param_ProductID,EAuracronCurrencyType(Z_Param_CurrencyType));
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetProductPrice ***********************

// ********** Begin Class UAuracronMonetizationBridge Function GetProductsByCategory ***************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics
{
	struct AuracronMonetizationBridge_eventGetProductsByCategory_Parms
	{
		FString Category;
		TArray<FAuracronStoreProduct> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Store" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter produtos por categoria\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter produtos por categoria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetProductsByCategory_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStoreProduct, METADATA_PARAMS(0, nullptr) }; // 2515431398
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventGetProductsByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2515431398
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "GetProductsByCategory", Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::AuracronMonetizationBridge_eventGetProductsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::AuracronMonetizationBridge_eventGetProductsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execGetProductsByCategory)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronStoreProduct>*)Z_Param__Result=P_THIS->GetProductsByCategory(Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function GetProductsByCategory *****************

// ********** Begin Class UAuracronMonetizationBridge Function InitiatePurchase ********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics
{
	struct AuracronMonetizationBridge_eventInitiatePurchase_Parms
	{
		FString ProductID;
		int32 Quantity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Purchase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar compra\n     */" },
#endif
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventInitiatePurchase_Parms, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventInitiatePurchase_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventInitiatePurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventInitiatePurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "InitiatePurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::AuracronMonetizationBridge_eventInitiatePurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::AuracronMonetizationBridge_eventInitiatePurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execInitiatePurchase)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ProductID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitiatePurchase(Z_Param_ProductID,Z_Param_Quantity);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function InitiatePurchase **********************

// ********** Begin Class UAuracronMonetizationBridge Function IsProductAvailable ******************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics
{
	struct AuracronMonetizationBridge_eventIsProductAvailable_Parms
	{
		FString ProductID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Store" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se produto est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se produto est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventIsProductAvailable_Parms, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventIsProductAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventIsProductAvailable_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "IsProductAvailable", Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::AuracronMonetizationBridge_eventIsProductAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::AuracronMonetizationBridge_eventIsProductAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execIsProductAvailable)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ProductID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsProductAvailable(Z_Param_ProductID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function IsProductAvailable ********************

// ********** Begin Class UAuracronMonetizationBridge Function IsSubscriptionActive ****************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics
{
	struct AuracronMonetizationBridge_eventIsSubscriptionActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Subscription" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar status da assinatura\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar status da assinatura" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventIsSubscriptionActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventIsSubscriptionActive_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "IsSubscriptionActive", Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::AuracronMonetizationBridge_eventIsSubscriptionActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::AuracronMonetizationBridge_eventIsSubscriptionActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execIsSubscriptionActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSubscriptionActive();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function IsSubscriptionActive ******************

// ********** Begin Class UAuracronMonetizationBridge Function LoadStoreProducts *******************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics
{
	struct AuracronMonetizationBridge_eventLoadStoreProducts_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Store" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar produtos da loja\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar produtos da loja" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventLoadStoreProducts_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventLoadStoreProducts_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "LoadStoreProducts", Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::AuracronMonetizationBridge_eventLoadStoreProducts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::AuracronMonetizationBridge_eventLoadStoreProducts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execLoadStoreProducts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadStoreProducts();
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function LoadStoreProducts *********************

// ********** Begin Class UAuracronMonetizationBridge Function ProcessPurchase *********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics
{
	struct AuracronMonetizationBridge_eventProcessPurchase_Parms
	{
		FAuracronTransaction Transaction;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Purchase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar compra\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transaction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transaction;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_Transaction = { "Transaction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventProcessPurchase_Parms, Transaction), Z_Construct_UScriptStruct_FAuracronTransaction, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transaction_MetaData), NewProp_Transaction_MetaData) }; // 3792508869
void Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventProcessPurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventProcessPurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_Transaction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "ProcessPurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::AuracronMonetizationBridge_eventProcessPurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::AuracronMonetizationBridge_eventProcessPurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execProcessPurchase)
{
	P_GET_STRUCT_REF(FAuracronTransaction,Z_Param_Out_Transaction);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ProcessPurchase(Z_Param_Out_Transaction);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function ProcessPurchase ***********************

// ********** Begin Class UAuracronMonetizationBridge Function PurchaseBattlePass ******************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics
{
	struct AuracronMonetizationBridge_eventPurchaseBattlePass_Parms
	{
		int32 Season;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|BattlePass" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Comprar Battle Pass\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Comprar Battle Pass" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Season;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_Season = { "Season", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventPurchaseBattlePass_Parms, Season), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventPurchaseBattlePass_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventPurchaseBattlePass_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_Season,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "PurchaseBattlePass", Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::AuracronMonetizationBridge_eventPurchaseBattlePass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::AuracronMonetizationBridge_eventPurchaseBattlePass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execPurchaseBattlePass)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Season);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PurchaseBattlePass(Z_Param_Season);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function PurchaseBattlePass ********************

// ********** Begin Class UAuracronMonetizationBridge Function ReceiveGift *************************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics
{
	struct AuracronMonetizationBridge_eventReceiveGift_Parms
	{
		FString GiftID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Gifting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Receber presente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Receber presente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GiftID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GiftID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_GiftID = { "GiftID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventReceiveGift_Parms, GiftID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GiftID_MetaData), NewProp_GiftID_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventReceiveGift_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventReceiveGift_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_GiftID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "ReceiveGift", Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::AuracronMonetizationBridge_eventReceiveGift_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::AuracronMonetizationBridge_eventReceiveGift_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execReceiveGift)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GiftID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReceiveGift(Z_Param_GiftID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function ReceiveGift ***************************

// ********** Begin Class UAuracronMonetizationBridge Function SendGift ****************************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics
{
	struct AuracronMonetizationBridge_eventSendGift_Parms
	{
		FString RecipientID;
		FString ProductID;
		FText Message;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Gifting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enviar presente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enviar presente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecipientID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecipientID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Message;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_RecipientID = { "RecipientID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSendGift_Parms, RecipientID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecipientID_MetaData), NewProp_RecipientID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ProductID = { "ProductID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSendGift_Parms, ProductID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductID_MetaData), NewProp_ProductID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSendGift_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventSendGift_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventSendGift_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_RecipientID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ProductID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "SendGift", Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::AuracronMonetizationBridge_eventSendGift_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::AuracronMonetizationBridge_eventSendGift_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execSendGift)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RecipientID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ProductID);
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SendGift(Z_Param_RecipientID,Z_Param_ProductID,Z_Param_Out_Message);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function SendGift ******************************

// ********** Begin Class UAuracronMonetizationBridge Function SpendCurrency ***********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics
{
	struct AuracronMonetizationBridge_eventSpendCurrency_Parms
	{
		EAuracronCurrencyType CurrencyType;
		int32 Amount;
		FString Purpose;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gastar moeda\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gastar moeda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Purpose_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Purpose;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSpendCurrency_Parms, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(0, nullptr) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSpendCurrency_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_Purpose = { "Purpose", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventSpendCurrency_Parms, Purpose), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Purpose_MetaData), NewProp_Purpose_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventSpendCurrency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventSpendCurrency_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_Purpose,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "SpendCurrency", Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::AuracronMonetizationBridge_eventSpendCurrency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::AuracronMonetizationBridge_eventSpendCurrency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execSpendCurrency)
{
	P_GET_ENUM(EAuracronCurrencyType,Z_Param_CurrencyType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_GET_PROPERTY(FStrProperty,Z_Param_Purpose);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpendCurrency(EAuracronCurrencyType(Z_Param_CurrencyType),Z_Param_Amount,Z_Param_Purpose);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function SpendCurrency *************************

// ********** Begin Class UAuracronMonetizationBridge Function TrackPurchase ***********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics
{
	struct AuracronMonetizationBridge_eventTrackPurchase_Parms
	{
		FAuracronTransaction Transaction;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear compra\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transaction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transaction;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_Transaction = { "Transaction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventTrackPurchase_Parms, Transaction), Z_Construct_UScriptStruct_FAuracronTransaction, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transaction_MetaData), NewProp_Transaction_MetaData) }; // 3792508869
void Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventTrackPurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventTrackPurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_Transaction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "TrackPurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::AuracronMonetizationBridge_eventTrackPurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::AuracronMonetizationBridge_eventTrackPurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execTrackPurchase)
{
	P_GET_STRUCT_REF(FAuracronTransaction,Z_Param_Out_Transaction);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackPurchase(Z_Param_Out_Transaction);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function TrackPurchase *************************

// ********** Begin Class UAuracronMonetizationBridge Function TransferCurrency ********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics
{
	struct AuracronMonetizationBridge_eventTransferCurrency_Parms
	{
		FString ToPlayerID;
		EAuracronCurrencyType CurrencyType;
		int32 Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Transferir moeda\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transferir moeda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ToPlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrencyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrencyType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ToPlayerID = { "ToPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventTransferCurrency_Parms, ToPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToPlayerID_MetaData), NewProp_ToPlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_CurrencyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_CurrencyType = { "CurrencyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventTransferCurrency_Parms, CurrencyType), Z_Construct_UEnum_AuracronMonetizationBridge_EAuracronCurrencyType, METADATA_PARAMS(0, nullptr) }; // 776395100
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventTransferCurrency_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventTransferCurrency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventTransferCurrency_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ToPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_CurrencyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_CurrencyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "TransferCurrency", Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::AuracronMonetizationBridge_eventTransferCurrency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::AuracronMonetizationBridge_eventTransferCurrency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execTransferCurrency)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ToPlayerID);
	P_GET_ENUM(EAuracronCurrencyType,Z_Param_CurrencyType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TransferCurrency(Z_Param_ToPlayerID,EAuracronCurrencyType(Z_Param_CurrencyType),Z_Param_Amount);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function TransferCurrency **********************

// ********** Begin Class UAuracronMonetizationBridge Function ValidatePurchase ********************
struct Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics
{
	struct AuracronMonetizationBridge_eventValidatePurchase_Parms
	{
		FString TransactionID;
		FString Receipt;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Monetization|Purchase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar compra\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar compra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransactionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Receipt_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransactionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Receipt;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_TransactionID = { "TransactionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventValidatePurchase_Parms, TransactionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransactionID_MetaData), NewProp_TransactionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_Receipt = { "Receipt", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMonetizationBridge_eventValidatePurchase_Parms, Receipt), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Receipt_MetaData), NewProp_Receipt_MetaData) };
void Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMonetizationBridge_eventValidatePurchase_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMonetizationBridge_eventValidatePurchase_Parms), &Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_TransactionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_Receipt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMonetizationBridge, nullptr, "ValidatePurchase", Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::AuracronMonetizationBridge_eventValidatePurchase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::AuracronMonetizationBridge_eventValidatePurchase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMonetizationBridge::execValidatePurchase)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TransactionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Receipt);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePurchase(Z_Param_TransactionID,Z_Param_Receipt);
	P_NATIVE_END;
}
// ********** End Class UAuracronMonetizationBridge Function ValidatePurchase **********************

// ********** Begin Class UAuracronMonetizationBridge **********************************************
void UAuracronMonetizationBridge::StaticRegisterNativesUAuracronMonetizationBridge()
{
	UClass* Class = UAuracronMonetizationBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateSubscription", &UAuracronMonetizationBridge::execActivateSubscription },
		{ "AddBattlePassXP", &UAuracronMonetizationBridge::execAddBattlePassXP },
		{ "AddCurrency", &UAuracronMonetizationBridge::execAddCurrency },
		{ "CancelPurchase", &UAuracronMonetizationBridge::execCancelPurchase },
		{ "CancelSubscription", &UAuracronMonetizationBridge::execCancelSubscription },
		{ "ClaimBattlePassReward", &UAuracronMonetizationBridge::execClaimBattlePassReward },
		{ "FinalizePurchase", &UAuracronMonetizationBridge::execFinalizePurchase },
		{ "GetBattlePassProgress", &UAuracronMonetizationBridge::execGetBattlePassProgress },
		{ "GetCurrencyBalance", &UAuracronMonetizationBridge::execGetCurrencyBalance },
		{ "GetFeaturedProducts", &UAuracronMonetizationBridge::execGetFeaturedProducts },
		{ "GetMonetizationStatistics", &UAuracronMonetizationBridge::execGetMonetizationStatistics },
		{ "GetPendingGifts", &UAuracronMonetizationBridge::execGetPendingGifts },
		{ "GetProductPrice", &UAuracronMonetizationBridge::execGetProductPrice },
		{ "GetProductsByCategory", &UAuracronMonetizationBridge::execGetProductsByCategory },
		{ "InitiatePurchase", &UAuracronMonetizationBridge::execInitiatePurchase },
		{ "IsProductAvailable", &UAuracronMonetizationBridge::execIsProductAvailable },
		{ "IsSubscriptionActive", &UAuracronMonetizationBridge::execIsSubscriptionActive },
		{ "LoadStoreProducts", &UAuracronMonetizationBridge::execLoadStoreProducts },
		{ "ProcessPurchase", &UAuracronMonetizationBridge::execProcessPurchase },
		{ "PurchaseBattlePass", &UAuracronMonetizationBridge::execPurchaseBattlePass },
		{ "ReceiveGift", &UAuracronMonetizationBridge::execReceiveGift },
		{ "SendGift", &UAuracronMonetizationBridge::execSendGift },
		{ "SpendCurrency", &UAuracronMonetizationBridge::execSpendCurrency },
		{ "TrackPurchase", &UAuracronMonetizationBridge::execTrackPurchase },
		{ "TransferCurrency", &UAuracronMonetizationBridge::execTransferCurrency },
		{ "ValidatePurchase", &UAuracronMonetizationBridge::execValidatePurchase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMonetizationBridge;
UClass* UAuracronMonetizationBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronMonetizationBridge;
	if (!Z_Registration_Info_UClass_UAuracronMonetizationBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMonetizationBridge"),
			Z_Registration_Info_UClass_UAuracronMonetizationBridge.InnerSingleton,
			StaticRegisterNativesUAuracronMonetizationBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMonetizationBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMonetizationBridge_NoRegister()
{
	return UAuracronMonetizationBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMonetizationBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Monetization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9tica\n */" },
#endif
		{ "DisplayName", "AURACRON Monetization Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronMonetizationBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de monetiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StoreProducts_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produtos da loja */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produtos da loja" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBattlePass_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Battle Pass atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Battle Pass atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrencyBalances_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Saldos de moeda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Saldos de moeda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTransactions_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es ativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnedProducts_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Produtos possu\xc3\x83\xc2\xad""dos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Produtos possu\xc3\x83\xc2\xad""dos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPurchaseCompleted_MetaData[] = {
		{ "Category", "AURACRON Monetization|Events" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBattlePassLevelUp_MetaData[] = {
		{ "Category", "AURACRON Monetization|Events" },
		{ "ModuleRelativePath", "Public/AuracronMonetizationBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StoreProducts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StoreProducts;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentBattlePass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrencyBalances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CurrencyBalances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveTransactions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveTransactions;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OwnedProducts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OwnedProducts;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPurchaseCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBattlePassLevelUp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_ActivateSubscription, "ActivateSubscription" }, // 3421464701
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_AddBattlePassXP, "AddBattlePassXP" }, // 2250602533
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_AddCurrency, "AddCurrency" }, // 1601490994
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_CancelPurchase, "CancelPurchase" }, // 3403677848
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_CancelSubscription, "CancelSubscription" }, // 43345616
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_ClaimBattlePassReward, "ClaimBattlePassReward" }, // 435199761
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_FinalizePurchase, "FinalizePurchase" }, // 3227575493
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetBattlePassProgress, "GetBattlePassProgress" }, // 3268217018
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetCurrencyBalance, "GetCurrencyBalance" }, // 2527207247
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetFeaturedProducts, "GetFeaturedProducts" }, // 2337638055
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetMonetizationStatistics, "GetMonetizationStatistics" }, // 3780509142
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetPendingGifts, "GetPendingGifts" }, // 464213342
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductPrice, "GetProductPrice" }, // 2211736887
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_GetProductsByCategory, "GetProductsByCategory" }, // 210638299
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_InitiatePurchase, "InitiatePurchase" }, // 2714089736
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_IsProductAvailable, "IsProductAvailable" }, // 1070356303
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_IsSubscriptionActive, "IsSubscriptionActive" }, // 3603755408
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_LoadStoreProducts, "LoadStoreProducts" }, // 4021962990
		{ &Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature, "OnBattlePassLevelUp__DelegateSignature" }, // 262351441
		{ &Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature, "OnPurchaseCompleted__DelegateSignature" }, // 3778491385
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_ProcessPurchase, "ProcessPurchase" }, // 3612409632
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_PurchaseBattlePass, "PurchaseBattlePass" }, // 4120512444
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_ReceiveGift, "ReceiveGift" }, // 2908499442
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_SendGift, "SendGift" }, // 1943029946
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_SpendCurrency, "SpendCurrency" }, // 3876683733
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_TrackPurchase, "TrackPurchase" }, // 456794732
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_TransferCurrency, "TransferCurrency" }, // 3496679011
		{ &Z_Construct_UFunction_UAuracronMonetizationBridge_ValidatePurchase, "ValidatePurchase" }, // 635318577
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMonetizationBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_StoreProducts_Inner = { "StoreProducts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStoreProduct, METADATA_PARAMS(0, nullptr) }; // 2515431398
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_StoreProducts = { "StoreProducts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, StoreProducts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StoreProducts_MetaData), NewProp_StoreProducts_MetaData) }; // 2515431398
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrentBattlePass = { "CurrentBattlePass", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, CurrentBattlePass), Z_Construct_UScriptStruct_FAuracronBattlePass, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBattlePass_MetaData), NewProp_CurrentBattlePass_MetaData) }; // 110463762
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrencyBalances_Inner = { "CurrencyBalances", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry, METADATA_PARAMS(0, nullptr) }; // 3094849085
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrencyBalances = { "CurrencyBalances", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, CurrencyBalances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrencyBalances_MetaData), NewProp_CurrencyBalances_MetaData) }; // 3094849085
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_ActiveTransactions_Inner = { "ActiveTransactions", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronTransaction, METADATA_PARAMS(0, nullptr) }; // 3792508869
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_ActiveTransactions = { "ActiveTransactions", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, ActiveTransactions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTransactions_MetaData), NewProp_ActiveTransactions_MetaData) }; // 3792508869
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OwnedProducts_Inner = { "OwnedProducts", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OwnedProducts = { "OwnedProducts", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, OwnedProducts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnedProducts_MetaData), NewProp_OwnedProducts_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OnPurchaseCompleted = { "OnPurchaseCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, OnPurchaseCompleted), Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnPurchaseCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPurchaseCompleted_MetaData), NewProp_OnPurchaseCompleted_MetaData) }; // 3778491385
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OnBattlePassLevelUp = { "OnBattlePassLevelUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMonetizationBridge, OnBattlePassLevelUp), Z_Construct_UDelegateFunction_UAuracronMonetizationBridge_OnBattlePassLevelUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBattlePassLevelUp_MetaData), NewProp_OnBattlePassLevelUp_MetaData) }; // 262351441
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMonetizationBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_StoreProducts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_StoreProducts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrentBattlePass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrencyBalances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_CurrencyBalances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_ActiveTransactions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_ActiveTransactions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OwnedProducts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OwnedProducts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OnPurchaseCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMonetizationBridge_Statics::NewProp_OnBattlePassLevelUp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMonetizationBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMonetizationBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMonetizationBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMonetizationBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMonetizationBridge_Statics::ClassParams = {
	&UAuracronMonetizationBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMonetizationBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMonetizationBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMonetizationBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMonetizationBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMonetizationBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronMonetizationBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMonetizationBridge.OuterSingleton, Z_Construct_UClass_UAuracronMonetizationBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMonetizationBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronMonetizationBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentBattlePass(TEXT("CurrentBattlePass"));
	static FName Name_CurrencyBalances(TEXT("CurrencyBalances"));
	static FName Name_OwnedProducts(TEXT("OwnedProducts"));
	const bool bIsValid = true
		&& Name_CurrentBattlePass == ClassReps[(int32)ENetFields_Private::CurrentBattlePass].Property->GetFName()
		&& Name_CurrencyBalances == ClassReps[(int32)ENetFields_Private::CurrencyBalances].Property->GetFName()
		&& Name_OwnedProducts == ClassReps[(int32)ENetFields_Private::OwnedProducts].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronMonetizationBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMonetizationBridge);
UAuracronMonetizationBridge::~UAuracronMonetizationBridge() {}
// ********** End Class UAuracronMonetizationBridge ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronCurrencyType_StaticEnum, TEXT("EAuracronCurrencyType"), &Z_Registration_Info_UEnum_EAuracronCurrencyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 776395100U) },
		{ EAuracronProductType_StaticEnum, TEXT("EAuracronProductType"), &Z_Registration_Info_UEnum_EAuracronProductType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1955226746U) },
		{ EAuracronItemRarity_StaticEnum, TEXT("EAuracronItemRarity"), &Z_Registration_Info_UEnum_EAuracronItemRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 421683325U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronStoreProduct::StaticStruct, Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics::NewStructOps, TEXT("AuracronStoreProduct"), &Z_Registration_Info_UScriptStruct_FAuracronStoreProduct, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStoreProduct), 2515431398U) },
		{ FAuracronBattlePass::StaticStruct, Z_Construct_UScriptStruct_FAuracronBattlePass_Statics::NewStructOps, TEXT("AuracronBattlePass"), &Z_Registration_Info_UScriptStruct_FAuracronBattlePass, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBattlePass), 110463762U) },
		{ FAuracronPurchaseAnalytics::StaticStruct, Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics::NewStructOps, TEXT("AuracronPurchaseAnalytics"), &Z_Registration_Info_UScriptStruct_FAuracronPurchaseAnalytics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPurchaseAnalytics), 524178764U) },
		{ FAuracronCurrencyBalanceEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics::NewStructOps, TEXT("AuracronCurrencyBalanceEntry"), &Z_Registration_Info_UScriptStruct_FAuracronCurrencyBalanceEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCurrencyBalanceEntry), 3094849085U) },
		{ FAuracronTransaction::StaticStruct, Z_Construct_UScriptStruct_FAuracronTransaction_Statics::NewStructOps, TEXT("AuracronTransaction"), &Z_Registration_Info_UScriptStruct_FAuracronTransaction, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTransaction), 3792508869U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMonetizationBridge, UAuracronMonetizationBridge::StaticClass, TEXT("UAuracronMonetizationBridge"), &Z_Registration_Info_UClass_UAuracronMonetizationBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMonetizationBridge), 3325160045U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_4106517106(TEXT("/Script/AuracronMonetizationBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h__Script_AuracronMonetizationBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
