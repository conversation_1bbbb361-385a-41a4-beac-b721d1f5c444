// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGCollisionSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGCollisionSystem_generated_h
#error "AuracronPCGCollisionSystem.generated.h already included, missing '#pragma once' in AuracronPCGCollisionSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGCollisionSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UWorld;
enum class EAuracronPCGPlacementValidation : uint8;
struct FAuracronPCGCollisionDescriptor;
struct FAuracronPCGPhysicsDescriptor;
struct FAuracronPCGSpatialQueryDescriptor;
struct FHitResult;

// ********** Begin ScriptStruct FAuracronPCGCollisionDescriptor ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGCollisionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGCollisionDescriptor;
// ********** End ScriptStruct FAuracronPCGCollisionDescriptor *************************************

// ********** Begin ScriptStruct FAuracronPCGPhysicsDescriptor *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_198_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPhysicsDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPhysicsDescriptor;
// ********** End ScriptStruct FAuracronPCGPhysicsDescriptor ***************************************

// ********** Begin ScriptStruct FAuracronPCGSpatialQueryDescriptor ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_288_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGSpatialQueryDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGSpatialQueryDescriptor;
// ********** End ScriptStruct FAuracronPCGSpatialQueryDescriptor **********************************

// ********** Begin Class UAuracronPCGCollisionTesterSettings **************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_363_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCollisionTesterSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCollisionTesterSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCollisionTesterSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCollisionTesterSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_363_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCollisionTesterSettings(UAuracronPCGCollisionTesterSettings&&) = delete; \
	UAuracronPCGCollisionTesterSettings(const UAuracronPCGCollisionTesterSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCollisionTesterSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCollisionTesterSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGCollisionTesterSettings) \
	NO_API virtual ~UAuracronPCGCollisionTesterSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_360_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_363_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_363_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_363_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCollisionTesterSettings;

// ********** End Class UAuracronPCGCollisionTesterSettings ****************************************

// ********** Begin Class UAuracronPCGPhysicsSimulatorSettings *************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_433_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPhysicsSimulatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPhysicsSimulatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPhysicsSimulatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPhysicsSimulatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_433_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPhysicsSimulatorSettings(UAuracronPCGPhysicsSimulatorSettings&&) = delete; \
	UAuracronPCGPhysicsSimulatorSettings(const UAuracronPCGPhysicsSimulatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPhysicsSimulatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPhysicsSimulatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGPhysicsSimulatorSettings) \
	NO_API virtual ~UAuracronPCGPhysicsSimulatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_430_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_433_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_433_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_433_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPhysicsSimulatorSettings;

// ********** End Class UAuracronPCGPhysicsSimulatorSettings ***************************************

// ********** Begin Class UAuracronPCGSpatialQuerySettings *****************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_526_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSpatialQuerySettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSpatialQuerySettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSpatialQuerySettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSpatialQuerySettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_526_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSpatialQuerySettings(UAuracronPCGSpatialQuerySettings&&) = delete; \
	UAuracronPCGSpatialQuerySettings(const UAuracronPCGSpatialQuerySettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSpatialQuerySettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSpatialQuerySettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGSpatialQuerySettings) \
	NO_API virtual ~UAuracronPCGSpatialQuerySettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_523_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_526_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_526_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_526_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSpatialQuerySettings;

// ********** End Class UAuracronPCGSpatialQuerySettings *******************************************

// ********** Begin Class UAuracronPCGPlacementValidatorSettings ***********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_613_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGPlacementValidatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGPlacementValidatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGPlacementValidatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGPlacementValidatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_613_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGPlacementValidatorSettings(UAuracronPCGPlacementValidatorSettings&&) = delete; \
	UAuracronPCGPlacementValidatorSettings(const UAuracronPCGPlacementValidatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGPlacementValidatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGPlacementValidatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGPlacementValidatorSettings) \
	NO_API virtual ~UAuracronPCGPlacementValidatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_610_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_613_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_613_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_613_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGPlacementValidatorSettings;

// ********** End Class UAuracronPCGPlacementValidatorSettings *************************************

// ********** Begin Class UAuracronPCGCollisionSystemUtils *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidatePhysicsDescriptor); \
	DECLARE_FUNCTION(execValidateCollisionDescriptor); \
	DECLARE_FUNCTION(execCheckClearance); \
	DECLARE_FUNCTION(execCalculateStabilityScore); \
	DECLARE_FUNCTION(execCalculateGroundAngle); \
	DECLARE_FUNCTION(execIsPhysicsStable); \
	DECLARE_FUNCTION(execCalculateGravityForce); \
	DECLARE_FUNCTION(execSimulatePhysicsStep); \
	DECLARE_FUNCTION(execCheckLineOfSight); \
	DECLARE_FUNCTION(execCalculateAngle); \
	DECLARE_FUNCTION(execCalculateDistance); \
	DECLARE_FUNCTION(execPerformSpatialQuery); \
	DECLARE_FUNCTION(execProjectToSurface); \
	DECLARE_FUNCTION(execValidatePlacement); \
	DECLARE_FUNCTION(execPerformCollisionTest);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGCollisionSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGCollisionSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGCollisionSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGCollisionSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGCollisionSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGCollisionSystemUtils(UAuracronPCGCollisionSystemUtils&&) = delete; \
	UAuracronPCGCollisionSystemUtils(const UAuracronPCGCollisionSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGCollisionSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGCollisionSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGCollisionSystemUtils) \
	NO_API virtual ~UAuracronPCGCollisionSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_708_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h_711_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGCollisionSystemUtils;

// ********** End Class UAuracronPCGCollisionSystemUtils *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGCollisionSystem_h

// ********** Begin Enum EAuracronPCGCollisionTestType *********************************************
#define FOREACH_ENUM_EAURACRONPCGCOLLISIONTESTTYPE(op) \
	op(EAuracronPCGCollisionTestType::LineTrace) \
	op(EAuracronPCGCollisionTestType::SphereTrace) \
	op(EAuracronPCGCollisionTestType::BoxTrace) \
	op(EAuracronPCGCollisionTestType::CapsuleTrace) \
	op(EAuracronPCGCollisionTestType::ConvexTrace) \
	op(EAuracronPCGCollisionTestType::OverlapSphere) \
	op(EAuracronPCGCollisionTestType::OverlapBox) \
	op(EAuracronPCGCollisionTestType::OverlapCapsule) \
	op(EAuracronPCGCollisionTestType::OverlapConvex) \
	op(EAuracronPCGCollisionTestType::RaycastSingle) \
	op(EAuracronPCGCollisionTestType::RaycastMultiple) \
	op(EAuracronPCGCollisionTestType::Custom) 

enum class EAuracronPCGCollisionTestType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCollisionTestType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionTestType>();
// ********** End Enum EAuracronPCGCollisionTestType ***********************************************

// ********** Begin Enum EAuracronPCGCollisionResponse *********************************************
#define FOREACH_ENUM_EAURACRONPCGCOLLISIONRESPONSE(op) \
	op(EAuracronPCGCollisionResponse::Ignore) \
	op(EAuracronPCGCollisionResponse::Overlap) \
	op(EAuracronPCGCollisionResponse::Block) \
	op(EAuracronPCGCollisionResponse::Custom) 

enum class EAuracronPCGCollisionResponse : uint8;
template<> struct TIsUEnumClass<EAuracronPCGCollisionResponse> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGCollisionResponse>();
// ********** End Enum EAuracronPCGCollisionResponse ***********************************************

// ********** Begin Enum EAuracronPCGPhysicsSimulationType *****************************************
#define FOREACH_ENUM_EAURACRONPCGPHYSICSSIMULATIONTYPE(op) \
	op(EAuracronPCGPhysicsSimulationType::None) \
	op(EAuracronPCGPhysicsSimulationType::Static) \
	op(EAuracronPCGPhysicsSimulationType::Kinematic) \
	op(EAuracronPCGPhysicsSimulationType::Dynamic) \
	op(EAuracronPCGPhysicsSimulationType::Simulated) \
	op(EAuracronPCGPhysicsSimulationType::Custom) 

enum class EAuracronPCGPhysicsSimulationType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPhysicsSimulationType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPhysicsSimulationType>();
// ********** End Enum EAuracronPCGPhysicsSimulationType *******************************************

// ********** Begin Enum EAuracronPCGSpatialQueryType **********************************************
#define FOREACH_ENUM_EAURACRONPCGSPATIALQUERYTYPE(op) \
	op(EAuracronPCGSpatialQueryType::Nearest) \
	op(EAuracronPCGSpatialQueryType::WithinRadius) \
	op(EAuracronPCGSpatialQueryType::WithinBox) \
	op(EAuracronPCGSpatialQueryType::WithinCone) \
	op(EAuracronPCGSpatialQueryType::LineOfSight) \
	op(EAuracronPCGSpatialQueryType::Visibility) \
	op(EAuracronPCGSpatialQueryType::Accessibility) \
	op(EAuracronPCGSpatialQueryType::Custom) 

enum class EAuracronPCGSpatialQueryType : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSpatialQueryType> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSpatialQueryType>();
// ********** End Enum EAuracronPCGSpatialQueryType ************************************************

// ********** Begin Enum EAuracronPCGPlacementValidation *******************************************
#define FOREACH_ENUM_EAURACRONPCGPLACEMENTVALIDATION(op) \
	op(EAuracronPCGPlacementValidation::None) \
	op(EAuracronPCGPlacementValidation::GroundCheck) \
	op(EAuracronPCGPlacementValidation::ClearanceCheck) \
	op(EAuracronPCGPlacementValidation::StabilityCheck) \
	op(EAuracronPCGPlacementValidation::AccessibilityCheck) \
	op(EAuracronPCGPlacementValidation::VisibilityCheck) \
	op(EAuracronPCGPlacementValidation::ProximityCheck) \
	op(EAuracronPCGPlacementValidation::CustomCheck) \
	op(EAuracronPCGPlacementValidation::AllChecks) 

enum class EAuracronPCGPlacementValidation : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPlacementValidation> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPlacementValidation>();
// ********** End Enum EAuracronPCGPlacementValidation *********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
