// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageWind.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageWind() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageWindManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalWindData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindPerformanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindZoneData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollectionInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronWindType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWindType;
static UEnum* EAuracronWindType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWindType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronWindType"));
	}
	return Z_Registration_Info_UEnum_EAuracronWindType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindType>()
{
	return EAuracronWindType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind types\n" },
#endif
		{ "Custom.DisplayName", "Custom Wind" },
		{ "Custom.Name", "EAuracronWindType::Custom" },
		{ "Directional.DisplayName", "Directional Wind" },
		{ "Directional.Name", "EAuracronWindType::Directional" },
		{ "Global.DisplayName", "Global Wind" },
		{ "Global.Name", "EAuracronWindType::Global" },
		{ "Interactive.DisplayName", "Interactive Wind" },
		{ "Interactive.Name", "EAuracronWindType::Interactive" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
		{ "Radial.DisplayName", "Radial Wind" },
		{ "Radial.Name", "EAuracronWindType::Radial" },
		{ "Seasonal.DisplayName", "Seasonal Wind" },
		{ "Seasonal.Name", "EAuracronWindType::Seasonal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind types" },
#endif
		{ "Turbulence.DisplayName", "Turbulence" },
		{ "Turbulence.Name", "EAuracronWindType::Turbulence" },
		{ "Vortex.DisplayName", "Vortex Wind" },
		{ "Vortex.Name", "EAuracronWindType::Vortex" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWindType::Global", (int64)EAuracronWindType::Global },
		{ "EAuracronWindType::Directional", (int64)EAuracronWindType::Directional },
		{ "EAuracronWindType::Radial", (int64)EAuracronWindType::Radial },
		{ "EAuracronWindType::Vortex", (int64)EAuracronWindType::Vortex },
		{ "EAuracronWindType::Turbulence", (int64)EAuracronWindType::Turbulence },
		{ "EAuracronWindType::Seasonal", (int64)EAuracronWindType::Seasonal },
		{ "EAuracronWindType::Interactive", (int64)EAuracronWindType::Interactive },
		{ "EAuracronWindType::Custom", (int64)EAuracronWindType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronWindType",
	"EAuracronWindType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWindType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWindType.InnerSingleton;
}
// ********** End Enum EAuracronWindType ***********************************************************

// ********** Begin Enum EAuracronWindStrength *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWindStrength;
static UEnum* EAuracronWindStrength_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindStrength.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWindStrength.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronWindStrength"));
	}
	return Z_Registration_Info_UEnum_EAuracronWindStrength.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindStrength>()
{
	return EAuracronWindStrength_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Calm.DisplayName", "Calm" },
		{ "Calm.Name", "EAuracronWindStrength::Calm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind strength levels\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronWindStrength::Custom" },
		{ "Gale.DisplayName", "Gale" },
		{ "Gale.Name", "EAuracronWindStrength::Gale" },
		{ "Hurricane.DisplayName", "Hurricane" },
		{ "Hurricane.Name", "EAuracronWindStrength::Hurricane" },
		{ "Light.DisplayName", "Light Breeze" },
		{ "Light.Name", "EAuracronWindStrength::Light" },
		{ "Moderate.DisplayName", "Moderate Breeze" },
		{ "Moderate.Name", "EAuracronWindStrength::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
		{ "Storm.DisplayName", "Storm" },
		{ "Storm.Name", "EAuracronWindStrength::Storm" },
		{ "Strong.DisplayName", "Strong Breeze" },
		{ "Strong.Name", "EAuracronWindStrength::Strong" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind strength levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWindStrength::Calm", (int64)EAuracronWindStrength::Calm },
		{ "EAuracronWindStrength::Light", (int64)EAuracronWindStrength::Light },
		{ "EAuracronWindStrength::Moderate", (int64)EAuracronWindStrength::Moderate },
		{ "EAuracronWindStrength::Strong", (int64)EAuracronWindStrength::Strong },
		{ "EAuracronWindStrength::Gale", (int64)EAuracronWindStrength::Gale },
		{ "EAuracronWindStrength::Storm", (int64)EAuracronWindStrength::Storm },
		{ "EAuracronWindStrength::Hurricane", (int64)EAuracronWindStrength::Hurricane },
		{ "EAuracronWindStrength::Custom", (int64)EAuracronWindStrength::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronWindStrength",
	"EAuracronWindStrength",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindStrength.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWindStrength.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWindStrength.InnerSingleton;
}
// ********** End Enum EAuracronWindStrength *******************************************************

// ********** Begin Enum EAuracronWindAnimationType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWindAnimationType;
static UEnum* EAuracronWindAnimationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindAnimationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWindAnimationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronWindAnimationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronWindAnimationType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronWindAnimationType>()
{
	return EAuracronWindAnimationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind animation types\n" },
#endif
		{ "ComplexWave.DisplayName", "Complex Wave" },
		{ "ComplexWave.Name", "EAuracronWindAnimationType::ComplexWave" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronWindAnimationType::Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
		{ "Noise.DisplayName", "Noise Based" },
		{ "Noise.Name", "EAuracronWindAnimationType::Noise" },
		{ "PhysicsBased.DisplayName", "Physics Based" },
		{ "PhysicsBased.Name", "EAuracronWindAnimationType::PhysicsBased" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EAuracronWindAnimationType::Procedural" },
		{ "SimpleWave.DisplayName", "Simple Wave" },
		{ "SimpleWave.Name", "EAuracronWindAnimationType::SimpleWave" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind animation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWindAnimationType::SimpleWave", (int64)EAuracronWindAnimationType::SimpleWave },
		{ "EAuracronWindAnimationType::ComplexWave", (int64)EAuracronWindAnimationType::ComplexWave },
		{ "EAuracronWindAnimationType::Noise", (int64)EAuracronWindAnimationType::Noise },
		{ "EAuracronWindAnimationType::Procedural", (int64)EAuracronWindAnimationType::Procedural },
		{ "EAuracronWindAnimationType::PhysicsBased", (int64)EAuracronWindAnimationType::PhysicsBased },
		{ "EAuracronWindAnimationType::Custom", (int64)EAuracronWindAnimationType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronWindAnimationType",
	"EAuracronWindAnimationType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronWindAnimationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWindAnimationType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWindAnimationType.InnerSingleton;
}
// ********** End Enum EAuracronWindAnimationType **************************************************

// ********** Begin Enum EAuracronSeasonalWindPattern **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern;
static UEnum* EAuracronSeasonalWindPattern_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronSeasonalWindPattern"));
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronSeasonalWindPattern>()
{
	return EAuracronSeasonalWindPattern_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Autumn.DisplayName", "Autumn Pattern" },
		{ "Autumn.Name", "EAuracronSeasonalWindPattern::Autumn" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Seasonal wind patterns\n" },
#endif
		{ "Custom.DisplayName", "Custom Pattern" },
		{ "Custom.Name", "EAuracronSeasonalWindPattern::Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
		{ "Monsoon.DisplayName", "Monsoon Pattern" },
		{ "Monsoon.Name", "EAuracronSeasonalWindPattern::Monsoon" },
		{ "Spring.DisplayName", "Spring Pattern" },
		{ "Spring.Name", "EAuracronSeasonalWindPattern::Spring" },
		{ "Summer.DisplayName", "Summer Pattern" },
		{ "Summer.Name", "EAuracronSeasonalWindPattern::Summer" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal wind patterns" },
#endif
		{ "Trade.DisplayName", "Trade Winds" },
		{ "Trade.Name", "EAuracronSeasonalWindPattern::Trade" },
		{ "Westerlies.DisplayName", "Westerlies" },
		{ "Westerlies.Name", "EAuracronSeasonalWindPattern::Westerlies" },
		{ "Winter.DisplayName", "Winter Pattern" },
		{ "Winter.Name", "EAuracronSeasonalWindPattern::Winter" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSeasonalWindPattern::Spring", (int64)EAuracronSeasonalWindPattern::Spring },
		{ "EAuracronSeasonalWindPattern::Summer", (int64)EAuracronSeasonalWindPattern::Summer },
		{ "EAuracronSeasonalWindPattern::Autumn", (int64)EAuracronSeasonalWindPattern::Autumn },
		{ "EAuracronSeasonalWindPattern::Winter", (int64)EAuracronSeasonalWindPattern::Winter },
		{ "EAuracronSeasonalWindPattern::Monsoon", (int64)EAuracronSeasonalWindPattern::Monsoon },
		{ "EAuracronSeasonalWindPattern::Trade", (int64)EAuracronSeasonalWindPattern::Trade },
		{ "EAuracronSeasonalWindPattern::Westerlies", (int64)EAuracronSeasonalWindPattern::Westerlies },
		{ "EAuracronSeasonalWindPattern::Custom", (int64)EAuracronSeasonalWindPattern::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronSeasonalWindPattern",
	"EAuracronSeasonalWindPattern",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern()
{
	if (!Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern.InnerSingleton;
}
// ********** End Enum EAuracronSeasonalWindPattern ************************************************

// ********** Begin ScriptStruct FAuracronWindConfiguration ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration;
class UScriptStruct* FAuracronWindConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWindConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronWindConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Wind Configuration Data\n * Configuration for wind system behavior\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind Configuration Data\nConfiguration for wind system behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWindSystem_MetaData[] = {
		{ "Category", "Wind System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultWindType_MetaData[] = {
		{ "Category", "Wind System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultWindStrength_MetaData[] = {
		{ "Category", "Wind System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalWindDirection_MetaData[] = {
		{ "Category", "Global Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalWindStrength_MetaData[] = {
		{ "Category", "Global Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalWindSpeed_MetaData[] = {
		{ "Category", "Global Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationType_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationIntensity_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWindVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationFrequency_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationAmplitude_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSeasonalWinds_MetaData[] = {
		{ "Category", "Seasonal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalPattern_MetaData[] = {
		{ "Category", "Seasonal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalIntensity_MetaData[] = {
		{ "Category", "Seasonal" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncWindUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWindZonesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWindDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindParameterCollection_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirectionParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindStrengthParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindSpeedParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindTimeParameterName_MetaData[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableWindSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWindSystem;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultWindType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultWindType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultWindStrength_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultWindStrength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GlobalWindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalWindStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalWindSpeed;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AnimationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AnimationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationIntensity;
	static void NewProp_bEnableWindVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWindVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationAmplitude;
	static void NewProp_bEnableSeasonalWinds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSeasonalWinds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SeasonalPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SeasonalPattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalIntensity;
	static void NewProp_bEnableAsyncWindUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncWindUpdates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxWindZonesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWindDistance;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WindParameterCollection;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WindDirectionParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WindStrengthParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WindSpeedParameterName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WindTimeParameterName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWindConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindSystem_SetBit(void* Obj)
{
	((FAuracronWindConfiguration*)Obj)->bEnableWindSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindSystem = { "bEnableWindSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindConfiguration), &Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWindSystem_MetaData), NewProp_bEnableWindSystem_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindType = { "DefaultWindType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, DefaultWindType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultWindType_MetaData), NewProp_DefaultWindType_MetaData) }; // 2460887349
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindStrength_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindStrength = { "DefaultWindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, DefaultWindStrength), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultWindStrength_MetaData), NewProp_DefaultWindStrength_MetaData) }; // 2839793549
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindDirection = { "GlobalWindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, GlobalWindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalWindDirection_MetaData), NewProp_GlobalWindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindStrength = { "GlobalWindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, GlobalWindStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalWindStrength_MetaData), NewProp_GlobalWindStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindSpeed = { "GlobalWindSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, GlobalWindSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalWindSpeed_MetaData), NewProp_GlobalWindSpeed_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationType = { "AnimationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, AnimationType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationType_MetaData), NewProp_AnimationType_MetaData) }; // 173128111
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, AnimationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationIntensity = { "AnimationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, AnimationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationIntensity_MetaData), NewProp_AnimationIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindVariation_SetBit(void* Obj)
{
	((FAuracronWindConfiguration*)Obj)->bEnableWindVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindVariation = { "bEnableWindVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindConfiguration), &Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWindVariation_MetaData), NewProp_bEnableWindVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_VariationFrequency = { "VariationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, VariationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationFrequency_MetaData), NewProp_VariationFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_VariationAmplitude = { "VariationAmplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, VariationAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationAmplitude_MetaData), NewProp_VariationAmplitude_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableSeasonalWinds_SetBit(void* Obj)
{
	((FAuracronWindConfiguration*)Obj)->bEnableSeasonalWinds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableSeasonalWinds = { "bEnableSeasonalWinds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindConfiguration), &Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableSeasonalWinds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSeasonalWinds_MetaData), NewProp_bEnableSeasonalWinds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalPattern = { "SeasonalPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, SeasonalPattern), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalPattern_MetaData), NewProp_SeasonalPattern_MetaData) }; // 778403172
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalIntensity = { "SeasonalIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, SeasonalIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalIntensity_MetaData), NewProp_SeasonalIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableAsyncWindUpdates_SetBit(void* Obj)
{
	((FAuracronWindConfiguration*)Obj)->bEnableAsyncWindUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableAsyncWindUpdates = { "bEnableAsyncWindUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindConfiguration), &Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableAsyncWindUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncWindUpdates_MetaData), NewProp_bEnableAsyncWindUpdates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_MaxWindZonesPerFrame = { "MaxWindZonesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, MaxWindZonesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWindZonesPerFrame_MetaData), NewProp_MaxWindZonesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindUpdateInterval = { "WindUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindUpdateInterval_MetaData), NewProp_WindUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_MaxWindDistance = { "MaxWindDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, MaxWindDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWindDistance_MetaData), NewProp_MaxWindDistance_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindParameterCollection = { "WindParameterCollection", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindParameterCollection_MetaData), NewProp_WindParameterCollection_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindDirectionParameterName = { "WindDirectionParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindDirectionParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirectionParameterName_MetaData), NewProp_WindDirectionParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindStrengthParameterName = { "WindStrengthParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindStrengthParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindStrengthParameterName_MetaData), NewProp_WindStrengthParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindSpeedParameterName = { "WindSpeedParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindSpeedParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindSpeedParameterName_MetaData), NewProp_WindSpeedParameterName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindTimeParameterName = { "WindTimeParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindConfiguration, WindTimeParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindTimeParameterName_MetaData), NewProp_WindTimeParameterName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindStrength_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_DefaultWindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_GlobalWindSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_AnimationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableWindVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_VariationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_VariationAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableSeasonalWinds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_SeasonalIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_bEnableAsyncWindUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_MaxWindZonesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_MaxWindDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindDirectionParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindStrengthParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindSpeedParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewProp_WindTimeParameterName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronWindConfiguration",
	Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::PropPointers),
	sizeof(FAuracronWindConfiguration),
	alignof(FAuracronWindConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWindConfiguration ******************************************

// ********** Begin ScriptStruct FAuracronWindZoneData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWindZoneData;
class UScriptStruct* FAuracronWindZoneData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWindZoneData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronWindZoneData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Wind Zone Data\n * Data for individual wind zones\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind Zone Data\nData for individual wind zones" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneId_MetaData[] = {
		{ "Category", "Wind Zone" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneName_MetaData[] = {
		{ "Category", "Wind Zone" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindType_MetaData[] = {
		{ "Category", "Wind Zone" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindStrength_MetaData[] = {
		{ "Category", "Wind Zone" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Spatial" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "Category", "Wind Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindForce_MetaData[] = {
		{ "Category", "Wind Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindSpeed_MetaData[] = {
		{ "Category", "Wind Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TurbulenceLevel_MetaData[] = {
		{ "Category", "Wind Properties" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationType_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationPhase_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationFrequency_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationSeed_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariationScale_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFalloff_MetaData[] = {
		{ "Category", "Falloff" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FalloffExponent_MetaData[] = {
		{ "Category", "Falloff" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FalloffStartDistance_MetaData[] = {
		{ "Category", "Falloff" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "Category", "Biome Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedFoliageTypes_MetaData[] = {
		{ "Category", "Biome Integration" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WindType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WindType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WindStrength_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WindStrength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TurbulenceLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AnimationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AnimationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationFrequency;
	static void NewProp_bEnableVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariationScale;
	static void NewProp_bUseFalloff_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFalloff;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FalloffExponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FalloffStartDistance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AffectedFoliageTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedFoliageTypes;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWindZoneData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, ZoneId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneId_MetaData), NewProp_ZoneId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_ZoneName = { "ZoneName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, ZoneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneName_MetaData), NewProp_ZoneName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindType = { "WindType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, WindType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindType_MetaData), NewProp_WindType_MetaData) }; // 2460887349
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindStrength_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, WindStrength), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindStrength_MetaData), NewProp_WindStrength_MetaData) }; // 2839793549
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindForce = { "WindForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, WindForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindForce_MetaData), NewProp_WindForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindSpeed = { "WindSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, WindSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindSpeed_MetaData), NewProp_WindSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_TurbulenceLevel = { "TurbulenceLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, TurbulenceLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TurbulenceLevel_MetaData), NewProp_TurbulenceLevel_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationType = { "AnimationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, AnimationType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationType_MetaData), NewProp_AnimationType_MetaData) }; // 173128111
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationPhase = { "AnimationPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, AnimationPhase), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationPhase_MetaData), NewProp_AnimationPhase_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationFrequency = { "AnimationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, AnimationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationFrequency_MetaData), NewProp_AnimationFrequency_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bEnableVariation_SetBit(void* Obj)
{
	((FAuracronWindZoneData*)Obj)->bEnableVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bEnableVariation = { "bEnableVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindZoneData), &Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bEnableVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVariation_MetaData), NewProp_bEnableVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_VariationSeed = { "VariationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, VariationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationSeed_MetaData), NewProp_VariationSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_VariationScale = { "VariationScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, VariationScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariationScale_MetaData), NewProp_VariationScale_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bUseFalloff_SetBit(void* Obj)
{
	((FAuracronWindZoneData*)Obj)->bUseFalloff = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bUseFalloff = { "bUseFalloff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindZoneData), &Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bUseFalloff_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFalloff_MetaData), NewProp_bUseFalloff_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_FalloffExponent = { "FalloffExponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, FalloffExponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FalloffExponent_MetaData), NewProp_FalloffExponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_FalloffStartDistance = { "FalloffStartDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, FalloffStartDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FalloffStartDistance_MetaData), NewProp_FalloffStartDistance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AffectedFoliageTypes_Inner = { "AffectedFoliageTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AffectedFoliageTypes = { "AffectedFoliageTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, AffectedFoliageTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedFoliageTypes_MetaData), NewProp_AffectedFoliageTypes_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronWindZoneData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWindZoneData), &Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindZoneData, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_ZoneId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_ZoneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindStrength_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_WindSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_TurbulenceLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AnimationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bEnableVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_VariationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_VariationScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bUseFalloff,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_FalloffExponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_FalloffStartDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AffectedFoliageTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_AffectedFoliageTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronWindZoneData",
	Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::PropPointers),
	sizeof(FAuracronWindZoneData),
	alignof(FAuracronWindZoneData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindZoneData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindZoneData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWindZoneData ***********************************************

// ********** Begin ScriptStruct FAuracronSeasonalWindData *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData;
class UScriptStruct* FAuracronSeasonalWindData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSeasonalWindData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronSeasonalWindData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Seasonal Wind Data\n * Data for seasonal wind patterns\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal Wind Data\nData for seasonal wind patterns" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "Category", "Seasonal Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDirection_MetaData[] = {
		{ "Category", "Seasonal Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseStrength_MetaData[] = {
		{ "Category", "Seasonal Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeasonalModifier_MetaData[] = {
		{ "Category", "Seasonal Wind" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DirectionVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrengthVariation_MetaData[] = {
		{ "Category", "Variation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// degrees\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "degrees" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CycleDuration_MetaData[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectTemperature_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectHumidity_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HumidityEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Pattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonalModifier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DirectionVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrengthVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CycleDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static void NewProp_bAffectTemperature_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectTemperature;
	static void NewProp_bAffectHumidity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectHumidity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemperatureEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HumidityEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSeasonalWindData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_Pattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, Pattern), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) }; // 778403172
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_BaseDirection = { "BaseDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, BaseDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDirection_MetaData), NewProp_BaseDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_BaseStrength = { "BaseStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, BaseStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseStrength_MetaData), NewProp_BaseStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_SeasonalModifier = { "SeasonalModifier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, SeasonalModifier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeasonalModifier_MetaData), NewProp_SeasonalModifier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_DirectionVariation = { "DirectionVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, DirectionVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DirectionVariation_MetaData), NewProp_DirectionVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_StrengthVariation = { "StrengthVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, StrengthVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrengthVariation_MetaData), NewProp_StrengthVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_CycleDuration = { "CycleDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, CycleDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CycleDuration_MetaData), NewProp_CycleDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectTemperature_SetBit(void* Obj)
{
	((FAuracronSeasonalWindData*)Obj)->bAffectTemperature = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectTemperature = { "bAffectTemperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalWindData), &Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectTemperature_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectTemperature_MetaData), NewProp_bAffectTemperature_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectHumidity_SetBit(void* Obj)
{
	((FAuracronSeasonalWindData*)Obj)->bAffectHumidity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectHumidity = { "bAffectHumidity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSeasonalWindData), &Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectHumidity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectHumidity_MetaData), NewProp_bAffectHumidity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_TemperatureEffect = { "TemperatureEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, TemperatureEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureEffect_MetaData), NewProp_TemperatureEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_HumidityEffect = { "HumidityEffect", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSeasonalWindData, HumidityEffect), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HumidityEffect_MetaData), NewProp_HumidityEffect_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_Pattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_Pattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_BaseDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_BaseStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_SeasonalModifier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_DirectionVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_StrengthVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_CycleDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_bAffectHumidity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_TemperatureEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewProp_HumidityEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronSeasonalWindData",
	Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::PropPointers),
	sizeof(FAuracronSeasonalWindData),
	alignof(FAuracronSeasonalWindData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSeasonalWindData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSeasonalWindData *******************************************

// ********** Begin ScriptStruct FAuracronWindPerformanceData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData;
class UScriptStruct* FAuracronWindPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWindPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronWindPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Wind Performance Data\n * Performance metrics for wind system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind Performance Data\nPerformance metrics for wind system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalWindZones_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveWindZones_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedFoliageInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialParameterUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalWindZones;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveWindZones;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AffectedFoliageInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaterialUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaterialParameterUpdates;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWindPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_TotalWindZones = { "TotalWindZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, TotalWindZones), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalWindZones_MetaData), NewProp_TotalWindZones_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_ActiveWindZones = { "ActiveWindZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, ActiveWindZones), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveWindZones_MetaData), NewProp_ActiveWindZones_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AffectedFoliageInstances = { "AffectedFoliageInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, AffectedFoliageInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedFoliageInstances_MetaData), NewProp_AffectedFoliageInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_WindUpdateTime = { "WindUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, WindUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindUpdateTime_MetaData), NewProp_WindUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MaterialUpdateTime = { "MaterialUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, MaterialUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialUpdateTime_MetaData), NewProp_MaterialUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AnimationUpdateTime = { "AnimationUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, AnimationUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationUpdateTime_MetaData), NewProp_AnimationUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MaterialParameterUpdates = { "MaterialParameterUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, MaterialParameterUpdates), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialParameterUpdates_MetaData), NewProp_MaterialParameterUpdates_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWindPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_TotalWindZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_ActiveWindZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AffectedFoliageInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_WindUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MaterialUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AnimationUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MaterialParameterUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronWindPerformanceData",
	Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::PropPointers),
	sizeof(FAuracronWindPerformanceData),
	alignof(FAuracronWindPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWindPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWindPerformanceData ****************************************

// ********** Begin Delegate FOnWindZoneCreated ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics
{
	struct AuracronFoliageWindManager_eventOnWindZoneCreated_Parms
	{
		FString ZoneId;
		FAuracronWindZoneData ZoneData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ZoneData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnWindZoneCreated_Parms, ZoneId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::NewProp_ZoneData = { "ZoneData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnWindZoneCreated_Parms, ZoneData), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(0, nullptr) }; // 1744194180
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::NewProp_ZoneId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::NewProp_ZoneData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "OnWindZoneCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnWindZoneCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnWindZoneCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageWindManager::FOnWindZoneCreated_DelegateWrapper(const FMulticastScriptDelegate& OnWindZoneCreated, const FString& ZoneId, FAuracronWindZoneData ZoneData)
{
	struct AuracronFoliageWindManager_eventOnWindZoneCreated_Parms
	{
		FString ZoneId;
		FAuracronWindZoneData ZoneData;
	};
	AuracronFoliageWindManager_eventOnWindZoneCreated_Parms Parms;
	Parms.ZoneId=ZoneId;
	Parms.ZoneData=ZoneData;
	OnWindZoneCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnWindZoneCreated ******************************************************

// ********** Begin Delegate FOnWindZoneRemoved ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms
	{
		FString ZoneId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms, ZoneId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::NewProp_ZoneId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "OnWindZoneRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageWindManager::FOnWindZoneRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnWindZoneRemoved, const FString& ZoneId)
{
	struct AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms
	{
		FString ZoneId;
	};
	AuracronFoliageWindManager_eventOnWindZoneRemoved_Parms Parms;
	Parms.ZoneId=ZoneId;
	OnWindZoneRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnWindZoneRemoved ******************************************************

// ********** Begin Delegate FOnGlobalWindChanged **************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics
{
	struct AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms
	{
		FVector Direction;
		float Strength;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::NewProp_Strength = { "Strength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms, Strength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::NewProp_Strength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "OnGlobalWindChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageWindManager::FOnGlobalWindChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGlobalWindChanged, FVector Direction, float Strength)
{
	struct AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms
	{
		FVector Direction;
		float Strength;
	};
	AuracronFoliageWindManager_eventOnGlobalWindChanged_Parms Parms;
	Parms.Direction=Direction;
	Parms.Strength=Strength;
	OnGlobalWindChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGlobalWindChanged ****************************************************

// ********** Begin Delegate FOnSeasonalWindChanged ************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics
{
	struct AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms
	{
		EAuracronSeasonalWindPattern Pattern;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Pattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::NewProp_Pattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms, Pattern), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, METADATA_PARAMS(0, nullptr) }; // 778403172
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::NewProp_Pattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::NewProp_Pattern,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "OnSeasonalWindChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageWindManager::FOnSeasonalWindChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSeasonalWindChanged, EAuracronSeasonalWindPattern Pattern)
{
	struct AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms
	{
		EAuracronSeasonalWindPattern Pattern;
	};
	AuracronFoliageWindManager_eventOnSeasonalWindChanged_Parms Parms;
	Parms.Pattern=Pattern;
	OnSeasonalWindChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSeasonalWindChanged **************************************************

// ********** Begin Class UAuracronFoliageWindManager Function ApplyBiomeWindToFoliage *************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics
{
	struct AuracronFoliageWindManager_eventApplyBiomeWindToFoliage_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventApplyBiomeWindToFoliage_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "ApplyBiomeWindToFoliage", Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::AuracronFoliageWindManager_eventApplyBiomeWindToFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::AuracronFoliageWindManager_eventApplyBiomeWindToFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execApplyBiomeWindToFoliage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyBiomeWindToFoliage(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function ApplyBiomeWindToFoliage ***************

// ********** Begin Class UAuracronFoliageWindManager Function CreateWindZone **********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics
{
	struct AuracronFoliageWindManager_eventCreateWindZone_Parms
	{
		FAuracronWindZoneData ZoneData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind zone management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind zone management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ZoneData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::NewProp_ZoneData = { "ZoneData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventCreateWindZone_Parms, ZoneData), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneData_MetaData), NewProp_ZoneData_MetaData) }; // 1744194180
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventCreateWindZone_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::NewProp_ZoneData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "CreateWindZone", Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::AuracronFoliageWindManager_eventCreateWindZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::AuracronFoliageWindManager_eventCreateWindZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execCreateWindZone)
{
	P_GET_STRUCT_REF(FAuracronWindZoneData,Z_Param_Out_ZoneData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateWindZone(Z_Param_Out_ZoneData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function CreateWindZone ************************

// ********** Begin Class UAuracronFoliageWindManager Function DrawDebugWindZones ******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics
{
	struct AuracronFoliageWindManager_eventDrawDebugWindZones_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventDrawDebugWindZones_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "DrawDebugWindZones", Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::AuracronFoliageWindManager_eventDrawDebugWindZones_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::AuracronFoliageWindManager_eventDrawDebugWindZones_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execDrawDebugWindZones)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugWindZones(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function DrawDebugWindZones ********************

// ********** Begin Class UAuracronFoliageWindManager Function EnableDebugVisualization ************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageWindManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::AuracronFoliageWindManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::AuracronFoliageWindManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function EnableDebugVisualization **************

// ********** Begin Class UAuracronFoliageWindManager Function EnableGlobalWind ********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics
{
	struct AuracronFoliageWindManager_eventEnableGlobalWind_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventEnableGlobalWind_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventEnableGlobalWind_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "EnableGlobalWind", Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::AuracronFoliageWindManager_eventEnableGlobalWind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::AuracronFoliageWindManager_eventEnableGlobalWind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execEnableGlobalWind)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableGlobalWind(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function EnableGlobalWind **********************

// ********** Begin Class UAuracronFoliageWindManager Function GetActiveWindZoneCount **************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics
{
	struct AuracronFoliageWindManager_eventGetActiveWindZoneCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetActiveWindZoneCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetActiveWindZoneCount", Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::AuracronFoliageWindManager_eventGetActiveWindZoneCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::AuracronFoliageWindManager_eventGetActiveWindZoneCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetActiveWindZoneCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveWindZoneCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetActiveWindZoneCount ****************

// ********** Begin Class UAuracronFoliageWindManager Function GetAffectedFoliageCount *************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics
{
	struct AuracronFoliageWindManager_eventGetAffectedFoliageCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetAffectedFoliageCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetAffectedFoliageCount", Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::AuracronFoliageWindManager_eventGetAffectedFoliageCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::AuracronFoliageWindManager_eventGetAffectedFoliageCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetAffectedFoliageCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAffectedFoliageCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetAffectedFoliageCount ***************

// ********** Begin Class UAuracronFoliageWindManager Function GetAllWindZones *********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics
{
	struct AuracronFoliageWindManager_eventGetAllWindZones_Parms
	{
		TArray<FAuracronWindZoneData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(0, nullptr) }; // 1744194180
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetAllWindZones_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1744194180
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetAllWindZones", Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::AuracronFoliageWindManager_eventGetAllWindZones_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::AuracronFoliageWindManager_eventGetAllWindZones_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetAllWindZones)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronWindZoneData>*)Z_Param__Result=P_THIS->GetAllWindZones();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetAllWindZones ***********************

// ********** Begin Class UAuracronFoliageWindManager Function GetAnimationSpeed *******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics
{
	struct AuracronFoliageWindManager_eventGetAnimationSpeed_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetAnimationSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetAnimationSpeed", Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::AuracronFoliageWindManager_eventGetAnimationSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::AuracronFoliageWindManager_eventGetAnimationSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetAnimationSpeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAnimationSpeed();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetAnimationSpeed *********************

// ********** Begin Class UAuracronFoliageWindManager Function GetBiomeWindSettings ****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics
{
	struct AuracronFoliageWindManager_eventGetBiomeWindSettings_Parms
	{
		FString BiomeId;
		FAuracronWindZoneData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetBiomeWindSettings_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetBiomeWindSettings_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(0, nullptr) }; // 1744194180
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetBiomeWindSettings", Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::AuracronFoliageWindManager_eventGetBiomeWindSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::AuracronFoliageWindManager_eventGetBiomeWindSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetBiomeWindSettings)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWindZoneData*)Z_Param__Result=P_THIS->GetBiomeWindSettings(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetBiomeWindSettings ******************

// ********** Begin Class UAuracronFoliageWindManager Function GetConfiguration ********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics
{
	struct AuracronFoliageWindManager_eventGetConfiguration_Parms
	{
		FAuracronWindConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWindConfiguration, METADATA_PARAMS(0, nullptr) }; // 4014270431
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::AuracronFoliageWindManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::AuracronFoliageWindManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWindConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetConfiguration **********************

// ********** Begin Class UAuracronFoliageWindManager Function GetGlobalWind ***********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics
{
	struct AuracronFoliageWindManager_eventGetGlobalWind_Parms
	{
		FVector Direction;
		float Strength;
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetGlobalWind_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Strength = { "Strength", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetGlobalWind_Parms, Strength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetGlobalWind_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Strength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetGlobalWind", Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::AuracronFoliageWindManager_eventGetGlobalWind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::AuracronFoliageWindManager_eventGetGlobalWind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetGlobalWind)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_Strength);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetGlobalWind(Z_Param_Out_Direction,Z_Param_Out_Strength,Z_Param_Out_Speed);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetGlobalWind *************************

// ********** Begin Class UAuracronFoliageWindManager Function GetInstance *************************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics
{
	struct AuracronFoliageWindManager_eventGetInstance_Parms
	{
		UAuracronFoliageWindManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::AuracronFoliageWindManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::AuracronFoliageWindManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageWindManager**)Z_Param__Result=UAuracronFoliageWindManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetInstance ***************************

// ********** Begin Class UAuracronFoliageWindManager Function GetMaterialParameterCollection ******
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics
{
	struct AuracronFoliageWindManager_eventGetMaterialParameterCollection_Parms
	{
		UMaterialParameterCollection* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetMaterialParameterCollection_Parms, ReturnValue), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetMaterialParameterCollection", Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::AuracronFoliageWindManager_eventGetMaterialParameterCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::AuracronFoliageWindManager_eventGetMaterialParameterCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetMaterialParameterCollection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialParameterCollection**)Z_Param__Result=P_THIS->GetMaterialParameterCollection();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetMaterialParameterCollection ********

// ********** Begin Class UAuracronFoliageWindManager Function GetPerformanceData ******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics
{
	struct AuracronFoliageWindManager_eventGetPerformanceData_Parms
	{
		FAuracronWindPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWindPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2440545672
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::AuracronFoliageWindManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::AuracronFoliageWindManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWindPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetPerformanceData ********************

// ********** Begin Class UAuracronFoliageWindManager Function GetSeasonalWindData *****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics
{
	struct AuracronFoliageWindManager_eventGetSeasonalWindData_Parms
	{
		FAuracronSeasonalWindData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetSeasonalWindData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronSeasonalWindData, METADATA_PARAMS(0, nullptr) }; // 3191022848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetSeasonalWindData", Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::AuracronFoliageWindManager_eventGetSeasonalWindData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::AuracronFoliageWindManager_eventGetSeasonalWindData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetSeasonalWindData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronSeasonalWindData*)Z_Param__Result=P_THIS->GetSeasonalWindData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetSeasonalWindData *******************

// ********** Begin Class UAuracronFoliageWindManager Function GetSeasonalWindPattern **************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics
{
	struct AuracronFoliageWindManager_eventGetSeasonalWindPattern_Parms
	{
		EAuracronSeasonalWindPattern ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetSeasonalWindPattern_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, METADATA_PARAMS(0, nullptr) }; // 778403172
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetSeasonalWindPattern", Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::AuracronFoliageWindManager_eventGetSeasonalWindPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::AuracronFoliageWindManager_eventGetSeasonalWindPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetSeasonalWindPattern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronSeasonalWindPattern*)Z_Param__Result=P_THIS->GetSeasonalWindPattern();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetSeasonalWindPattern ****************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindAtLocation *******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics
{
	struct AuracronFoliageWindManager_eventGetWindAtLocation_Parms
	{
		FVector Location;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindAtLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindAtLocation", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::AuracronFoliageWindManager_eventGetWindAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::AuracronFoliageWindManager_eventGetWindAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetWindAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindAtLocation *********************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindDirection ********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics
{
	struct AuracronFoliageWindManager_eventGetWindDirection_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindDirection_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindDirection", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::AuracronFoliageWindManager_eventGetWindDirection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::AuracronFoliageWindManager_eventGetWindDirection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindDirection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetWindDirection();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindDirection **********************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindStrength *********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics
{
	struct AuracronFoliageWindManager_eventGetWindStrength_Parms
	{
		EAuracronWindStrength ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindStrength_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, METADATA_PARAMS(0, nullptr) }; // 2839793549
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindStrength", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::AuracronFoliageWindManager_eventGetWindStrength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::AuracronFoliageWindManager_eventGetWindStrength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindStrength)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronWindStrength*)Z_Param__Result=P_THIS->GetWindStrength();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindStrength ***********************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindStrengthValue ****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics
{
	struct AuracronFoliageWindManager_eventGetWindStrengthValue_Parms
	{
		EAuracronWindStrength StrengthLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_StrengthLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StrengthLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_StrengthLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_StrengthLevel = { "StrengthLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindStrengthValue_Parms, StrengthLevel), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, METADATA_PARAMS(0, nullptr) }; // 2839793549
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindStrengthValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_StrengthLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_StrengthLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindStrengthValue", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::AuracronFoliageWindManager_eventGetWindStrengthValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::AuracronFoliageWindManager_eventGetWindStrengthValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindStrengthValue)
{
	P_GET_ENUM(EAuracronWindStrength,Z_Param_StrengthLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWindStrengthValue(EAuracronWindStrength(Z_Param_StrengthLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindStrengthValue ******************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindZone *************************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics
{
	struct AuracronFoliageWindManager_eventGetWindZone_Parms
	{
		FString ZoneId;
		FAuracronWindZoneData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindZone_Parms, ZoneId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneId_MetaData), NewProp_ZoneId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindZone_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(0, nullptr) }; // 1744194180
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::NewProp_ZoneId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindZone", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::AuracronFoliageWindManager_eventGetWindZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::AuracronFoliageWindManager_eventGetWindZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindZone)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ZoneId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWindZoneData*)Z_Param__Result=P_THIS->GetWindZone(Z_Param_ZoneId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindZone ***************************

// ********** Begin Class UAuracronFoliageWindManager Function GetWindZonesInArea ******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics
{
	struct AuracronFoliageWindManager_eventGetWindZonesInArea_Parms
	{
		FBox Area;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindZonesInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventGetWindZonesInArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "GetWindZonesInArea", Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::AuracronFoliageWindManager_eventGetWindZonesInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::AuracronFoliageWindManager_eventGetWindZonesInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execGetWindZonesInArea)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetWindZonesInArea(Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function GetWindZonesInArea ********************

// ********** Begin Class UAuracronFoliageWindManager Function Initialize **************************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics
{
	struct AuracronFoliageWindManager_eventInitialize_Parms
	{
		FAuracronWindConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWindConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4014270431
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::AuracronFoliageWindManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::AuracronFoliageWindManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronWindConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function Initialize ****************************

// ********** Begin Class UAuracronFoliageWindManager Function IsDebugVisualizationEnabled *********
struct Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageWindManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageWindManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageWindManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function IsDebugVisualizationEnabled ***********

// ********** Begin Class UAuracronFoliageWindManager Function IsGlobalWindEnabled *****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics
{
	struct AuracronFoliageWindManager_eventIsGlobalWindEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventIsGlobalWindEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventIsGlobalWindEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "IsGlobalWindEnabled", Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::AuracronFoliageWindManager_eventIsGlobalWindEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::AuracronFoliageWindManager_eventIsGlobalWindEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execIsGlobalWindEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGlobalWindEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function IsGlobalWindEnabled *******************

// ********** Begin Class UAuracronFoliageWindManager Function IsInitialized ***********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics
{
	struct AuracronFoliageWindManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::AuracronFoliageWindManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::AuracronFoliageWindManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function IsInitialized *************************

// ********** Begin Class UAuracronFoliageWindManager Function LogWindStatistics *******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "LogWindStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execLogWindStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogWindStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function LogWindStatistics *********************

// ********** Begin Class UAuracronFoliageWindManager Function RemoveWindZone **********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics
{
	struct AuracronFoliageWindManager_eventRemoveWindZone_Parms
	{
		FString ZoneId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventRemoveWindZone_Parms, ZoneId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneId_MetaData), NewProp_ZoneId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventRemoveWindZone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventRemoveWindZone_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ZoneId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "RemoveWindZone", Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::AuracronFoliageWindManager_eventRemoveWindZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::AuracronFoliageWindManager_eventRemoveWindZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execRemoveWindZone)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ZoneId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveWindZone(Z_Param_ZoneId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function RemoveWindZone ************************

// ********** Begin Class UAuracronFoliageWindManager Function SetAnimationSpeed *******************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics
{
	struct AuracronFoliageWindManager_eventSetAnimationSpeed_Parms
	{
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetAnimationSpeed_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetAnimationSpeed", Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::AuracronFoliageWindManager_eventSetAnimationSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::AuracronFoliageWindManager_eventSetAnimationSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetAnimationSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAnimationSpeed(Z_Param_Speed);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetAnimationSpeed *********************

// ********** Begin Class UAuracronFoliageWindManager Function SetBiomeWindSettings ****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics
{
	struct AuracronFoliageWindManager_eventSetBiomeWindSettings_Parms
	{
		FString BiomeId;
		FAuracronWindZoneData WindSettings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetBiomeWindSettings_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::NewProp_WindSettings = { "WindSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetBiomeWindSettings_Parms, WindSettings), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindSettings_MetaData), NewProp_WindSettings_MetaData) }; // 1744194180
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::NewProp_WindSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetBiomeWindSettings", Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::AuracronFoliageWindManager_eventSetBiomeWindSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::AuracronFoliageWindManager_eventSetBiomeWindSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetBiomeWindSettings)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FAuracronWindZoneData,Z_Param_Out_WindSettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBiomeWindSettings(Z_Param_BiomeId,Z_Param_Out_WindSettings);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetBiomeWindSettings ******************

// ********** Begin Class UAuracronFoliageWindManager Function SetConfiguration ********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics
{
	struct AuracronFoliageWindManager_eventSetConfiguration_Parms
	{
		FAuracronWindConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWindConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4014270431
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::AuracronFoliageWindManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::AuracronFoliageWindManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronWindConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetConfiguration **********************

// ********** Begin Class UAuracronFoliageWindManager Function SetFoliageWindAnimation *************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics
{
	struct AuracronFoliageWindManager_eventSetFoliageWindAnimation_Parms
	{
		FString FoliageTypeId;
		EAuracronWindAnimationType AnimationType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage animation\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AnimationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AnimationType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetFoliageWindAnimation_Parms, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_AnimationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_AnimationType = { "AnimationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetFoliageWindAnimation_Parms, AnimationType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindAnimationType, METADATA_PARAMS(0, nullptr) }; // 173128111
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_AnimationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::NewProp_AnimationType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetFoliageWindAnimation", Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::AuracronFoliageWindManager_eventSetFoliageWindAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::AuracronFoliageWindManager_eventSetFoliageWindAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetFoliageWindAnimation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FoliageTypeId);
	P_GET_ENUM(EAuracronWindAnimationType,Z_Param_AnimationType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFoliageWindAnimation(Z_Param_FoliageTypeId,EAuracronWindAnimationType(Z_Param_AnimationType));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetFoliageWindAnimation ***************

// ********** Begin Class UAuracronFoliageWindManager Function SetGlobalWind ***********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics
{
	struct AuracronFoliageWindManager_eventSetGlobalWind_Parms
	{
		FVector Direction;
		float Strength;
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Global wind control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global wind control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Strength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetGlobalWind_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Strength = { "Strength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetGlobalWind_Parms, Strength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetGlobalWind_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Strength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetGlobalWind", Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::AuracronFoliageWindManager_eventSetGlobalWind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::AuracronFoliageWindManager_eventSetGlobalWind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetGlobalWind)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Strength);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalWind(Z_Param_Out_Direction,Z_Param_Strength,Z_Param_Speed);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetGlobalWind *************************

// ********** Begin Class UAuracronFoliageWindManager Function SetMaterialParameterCollection ******
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics
{
	struct AuracronFoliageWindManager_eventSetMaterialParameterCollection_Parms
	{
		UMaterialParameterCollection* ParameterCollection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParameterCollection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::NewProp_ParameterCollection = { "ParameterCollection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetMaterialParameterCollection_Parms, ParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::NewProp_ParameterCollection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetMaterialParameterCollection", Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::AuracronFoliageWindManager_eventSetMaterialParameterCollection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::AuracronFoliageWindManager_eventSetMaterialParameterCollection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetMaterialParameterCollection)
{
	P_GET_OBJECT(UMaterialParameterCollection,Z_Param_ParameterCollection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaterialParameterCollection(Z_Param_ParameterCollection);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetMaterialParameterCollection ********

// ********** Begin Class UAuracronFoliageWindManager Function SetSeasonalWindPattern **************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics
{
	struct AuracronFoliageWindManager_eventSetSeasonalWindPattern_Parms
	{
		EAuracronSeasonalWindPattern Pattern;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Seasonal wind patterns\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal wind patterns" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Pattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::NewProp_Pattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetSeasonalWindPattern_Parms, Pattern), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronSeasonalWindPattern, METADATA_PARAMS(0, nullptr) }; // 778403172
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::NewProp_Pattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::NewProp_Pattern,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetSeasonalWindPattern", Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::AuracronFoliageWindManager_eventSetSeasonalWindPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::AuracronFoliageWindManager_eventSetSeasonalWindPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetSeasonalWindPattern)
{
	P_GET_ENUM(EAuracronSeasonalWindPattern,Z_Param_Pattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSeasonalWindPattern(EAuracronSeasonalWindPattern(Z_Param_Pattern));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetSeasonalWindPattern ****************

// ********** Begin Class UAuracronFoliageWindManager Function SetWindDirection ********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics
{
	struct AuracronFoliageWindManager_eventSetWindDirection_Parms
	{
		FVector Direction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind direction control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind direction control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetWindDirection_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::NewProp_Direction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetWindDirection", Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::AuracronFoliageWindManager_eventSetWindDirection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::AuracronFoliageWindManager_eventSetWindDirection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetWindDirection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWindDirection(Z_Param_Out_Direction);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetWindDirection **********************

// ********** Begin Class UAuracronFoliageWindManager Function SetWindStrength *********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics
{
	struct AuracronFoliageWindManager_eventSetWindStrength_Parms
	{
		EAuracronWindStrength StrengthLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Wind strength control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind strength control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_StrengthLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StrengthLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::NewProp_StrengthLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::NewProp_StrengthLevel = { "StrengthLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventSetWindStrength_Parms, StrengthLevel), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronWindStrength, METADATA_PARAMS(0, nullptr) }; // 2839793549
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::NewProp_StrengthLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::NewProp_StrengthLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "SetWindStrength", Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::AuracronFoliageWindManager_eventSetWindStrength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::AuracronFoliageWindManager_eventSetWindStrength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execSetWindStrength)
{
	P_GET_ENUM(EAuracronWindStrength,Z_Param_StrengthLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWindStrength(EAuracronWindStrength(Z_Param_StrengthLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function SetWindStrength ***********************

// ********** Begin Class UAuracronFoliageWindManager Function Shutdown ****************************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function Shutdown ******************************

// ********** Begin Class UAuracronFoliageWindManager Function Tick ********************************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics
{
	struct AuracronFoliageWindManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::AuracronFoliageWindManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::AuracronFoliageWindManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function Tick **********************************

// ********** Begin Class UAuracronFoliageWindManager Function UpdateFoliageWindAnimation **********
struct Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics
{
	struct AuracronFoliageWindManager_eventUpdateFoliageWindAnimation_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventUpdateFoliageWindAnimation_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "UpdateFoliageWindAnimation", Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::AuracronFoliageWindManager_eventUpdateFoliageWindAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::AuracronFoliageWindManager_eventUpdateFoliageWindAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execUpdateFoliageWindAnimation)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFoliageWindAnimation(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function UpdateFoliageWindAnimation ************

// ********** Begin Class UAuracronFoliageWindManager Function UpdateMaterialParameters ************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "UpdateMaterialParameters", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execUpdateMaterialParameters)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMaterialParameters();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function UpdateMaterialParameters **************

// ********** Begin Class UAuracronFoliageWindManager Function UpdatePerformanceMetrics ************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function UpdatePerformanceMetrics **************

// ********** Begin Class UAuracronFoliageWindManager Function UpdateSeasonalWinds *****************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics
{
	struct AuracronFoliageWindManager_eventUpdateSeasonalWinds_Parms
	{
		float SeasonProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeasonProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::NewProp_SeasonProgress = { "SeasonProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventUpdateSeasonalWinds_Parms, SeasonProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::NewProp_SeasonProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "UpdateSeasonalWinds", Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::AuracronFoliageWindManager_eventUpdateSeasonalWinds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::AuracronFoliageWindManager_eventUpdateSeasonalWinds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execUpdateSeasonalWinds)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SeasonProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSeasonalWinds(Z_Param_SeasonProgress);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function UpdateSeasonalWinds *******************

// ********** Begin Class UAuracronFoliageWindManager Function UpdateWindZone **********************
struct Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics
{
	struct AuracronFoliageWindManager_eventUpdateWindZone_Parms
	{
		FString ZoneId;
		FAuracronWindZoneData ZoneData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wind Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZoneData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ZoneId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ZoneData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ZoneId = { "ZoneId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventUpdateWindZone_Parms, ZoneId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneId_MetaData), NewProp_ZoneId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ZoneData = { "ZoneData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageWindManager_eventUpdateWindZone_Parms, ZoneData), Z_Construct_UScriptStruct_FAuracronWindZoneData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZoneData_MetaData), NewProp_ZoneData_MetaData) }; // 1744194180
void Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageWindManager_eventUpdateWindZone_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageWindManager_eventUpdateWindZone_Parms), &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ZoneId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ZoneData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageWindManager, nullptr, "UpdateWindZone", Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::AuracronFoliageWindManager_eventUpdateWindZone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::AuracronFoliageWindManager_eventUpdateWindZone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageWindManager::execUpdateWindZone)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ZoneId);
	P_GET_STRUCT_REF(FAuracronWindZoneData,Z_Param_Out_ZoneData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateWindZone(Z_Param_ZoneId,Z_Param_Out_ZoneData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageWindManager Function UpdateWindZone ************************

// ********** Begin Class UAuracronFoliageWindManager **********************************************
void UAuracronFoliageWindManager::StaticRegisterNativesUAuracronFoliageWindManager()
{
	UClass* Class = UAuracronFoliageWindManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyBiomeWindToFoliage", &UAuracronFoliageWindManager::execApplyBiomeWindToFoliage },
		{ "CreateWindZone", &UAuracronFoliageWindManager::execCreateWindZone },
		{ "DrawDebugWindZones", &UAuracronFoliageWindManager::execDrawDebugWindZones },
		{ "EnableDebugVisualization", &UAuracronFoliageWindManager::execEnableDebugVisualization },
		{ "EnableGlobalWind", &UAuracronFoliageWindManager::execEnableGlobalWind },
		{ "GetActiveWindZoneCount", &UAuracronFoliageWindManager::execGetActiveWindZoneCount },
		{ "GetAffectedFoliageCount", &UAuracronFoliageWindManager::execGetAffectedFoliageCount },
		{ "GetAllWindZones", &UAuracronFoliageWindManager::execGetAllWindZones },
		{ "GetAnimationSpeed", &UAuracronFoliageWindManager::execGetAnimationSpeed },
		{ "GetBiomeWindSettings", &UAuracronFoliageWindManager::execGetBiomeWindSettings },
		{ "GetConfiguration", &UAuracronFoliageWindManager::execGetConfiguration },
		{ "GetGlobalWind", &UAuracronFoliageWindManager::execGetGlobalWind },
		{ "GetInstance", &UAuracronFoliageWindManager::execGetInstance },
		{ "GetMaterialParameterCollection", &UAuracronFoliageWindManager::execGetMaterialParameterCollection },
		{ "GetPerformanceData", &UAuracronFoliageWindManager::execGetPerformanceData },
		{ "GetSeasonalWindData", &UAuracronFoliageWindManager::execGetSeasonalWindData },
		{ "GetSeasonalWindPattern", &UAuracronFoliageWindManager::execGetSeasonalWindPattern },
		{ "GetWindAtLocation", &UAuracronFoliageWindManager::execGetWindAtLocation },
		{ "GetWindDirection", &UAuracronFoliageWindManager::execGetWindDirection },
		{ "GetWindStrength", &UAuracronFoliageWindManager::execGetWindStrength },
		{ "GetWindStrengthValue", &UAuracronFoliageWindManager::execGetWindStrengthValue },
		{ "GetWindZone", &UAuracronFoliageWindManager::execGetWindZone },
		{ "GetWindZonesInArea", &UAuracronFoliageWindManager::execGetWindZonesInArea },
		{ "Initialize", &UAuracronFoliageWindManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageWindManager::execIsDebugVisualizationEnabled },
		{ "IsGlobalWindEnabled", &UAuracronFoliageWindManager::execIsGlobalWindEnabled },
		{ "IsInitialized", &UAuracronFoliageWindManager::execIsInitialized },
		{ "LogWindStatistics", &UAuracronFoliageWindManager::execLogWindStatistics },
		{ "RemoveWindZone", &UAuracronFoliageWindManager::execRemoveWindZone },
		{ "SetAnimationSpeed", &UAuracronFoliageWindManager::execSetAnimationSpeed },
		{ "SetBiomeWindSettings", &UAuracronFoliageWindManager::execSetBiomeWindSettings },
		{ "SetConfiguration", &UAuracronFoliageWindManager::execSetConfiguration },
		{ "SetFoliageWindAnimation", &UAuracronFoliageWindManager::execSetFoliageWindAnimation },
		{ "SetGlobalWind", &UAuracronFoliageWindManager::execSetGlobalWind },
		{ "SetMaterialParameterCollection", &UAuracronFoliageWindManager::execSetMaterialParameterCollection },
		{ "SetSeasonalWindPattern", &UAuracronFoliageWindManager::execSetSeasonalWindPattern },
		{ "SetWindDirection", &UAuracronFoliageWindManager::execSetWindDirection },
		{ "SetWindStrength", &UAuracronFoliageWindManager::execSetWindStrength },
		{ "Shutdown", &UAuracronFoliageWindManager::execShutdown },
		{ "Tick", &UAuracronFoliageWindManager::execTick },
		{ "UpdateFoliageWindAnimation", &UAuracronFoliageWindManager::execUpdateFoliageWindAnimation },
		{ "UpdateMaterialParameters", &UAuracronFoliageWindManager::execUpdateMaterialParameters },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageWindManager::execUpdatePerformanceMetrics },
		{ "UpdateSeasonalWinds", &UAuracronFoliageWindManager::execUpdateSeasonalWinds },
		{ "UpdateWindZone", &UAuracronFoliageWindManager::execUpdateWindZone },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageWindManager;
UClass* UAuracronFoliageWindManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageWindManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageWindManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageWindManager"),
			Z_Registration_Info_UClass_UAuracronFoliageWindManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageWindManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageWindManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageWindManager_NoRegister()
{
	return UAuracronFoliageWindManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageWindManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Wind Manager\n * Manager for the foliage wind system including wind zones, seasonal patterns, and animation\n */" },
#endif
		{ "IncludePath", "AuracronFoliageWind.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Wind Manager\nManager for the foliage wind system including wind zones, seasonal patterns, and animation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWindZoneCreated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWindZoneRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGlobalWindChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSeasonalWindChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindParameterCollection_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter collection\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter collection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindParameterInstance_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageWind.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWindZoneCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWindZoneRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGlobalWindChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSeasonalWindChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WindParameterCollection;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WindParameterInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_ApplyBiomeWindToFoliage, "ApplyBiomeWindToFoliage" }, // 3305203338
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_CreateWindZone, "CreateWindZone" }, // 745015465
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_DrawDebugWindZones, "DrawDebugWindZones" }, // 895255283
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 2444601107
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_EnableGlobalWind, "EnableGlobalWind" }, // 304527348
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetActiveWindZoneCount, "GetActiveWindZoneCount" }, // 3292906403
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetAffectedFoliageCount, "GetAffectedFoliageCount" }, // 2690890213
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetAllWindZones, "GetAllWindZones" }, // 4173492087
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetAnimationSpeed, "GetAnimationSpeed" }, // 403923557
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetBiomeWindSettings, "GetBiomeWindSettings" }, // 2933394693
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetConfiguration, "GetConfiguration" }, // 1253487229
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetGlobalWind, "GetGlobalWind" }, // 2393067017
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetInstance, "GetInstance" }, // 1143436049
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetMaterialParameterCollection, "GetMaterialParameterCollection" }, // 3026357668
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetPerformanceData, "GetPerformanceData" }, // 3988192876
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindData, "GetSeasonalWindData" }, // 2711033780
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetSeasonalWindPattern, "GetSeasonalWindPattern" }, // 1178920960
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindAtLocation, "GetWindAtLocation" }, // 775863761
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindDirection, "GetWindDirection" }, // 3472870471
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrength, "GetWindStrength" }, // 2878328948
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindStrengthValue, "GetWindStrengthValue" }, // 4174501857
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZone, "GetWindZone" }, // 3558739430
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_GetWindZonesInArea, "GetWindZonesInArea" }, // 2550864299
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_Initialize, "Initialize" }, // 2294222235
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2610637478
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_IsGlobalWindEnabled, "IsGlobalWindEnabled" }, // 3749954857
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_IsInitialized, "IsInitialized" }, // 3942042826
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_LogWindStatistics, "LogWindStatistics" }, // 3098664726
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature, "OnGlobalWindChanged__DelegateSignature" }, // 1462638333
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature, "OnSeasonalWindChanged__DelegateSignature" }, // 2973766993
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature, "OnWindZoneCreated__DelegateSignature" }, // 926884191
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature, "OnWindZoneRemoved__DelegateSignature" }, // 1348096733
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_RemoveWindZone, "RemoveWindZone" }, // 14627167
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetAnimationSpeed, "SetAnimationSpeed" }, // 3066298818
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetBiomeWindSettings, "SetBiomeWindSettings" }, // 4235423258
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetConfiguration, "SetConfiguration" }, // 1016054442
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetFoliageWindAnimation, "SetFoliageWindAnimation" }, // 2025808031
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetGlobalWind, "SetGlobalWind" }, // 138394658
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetMaterialParameterCollection, "SetMaterialParameterCollection" }, // 1099545274
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetSeasonalWindPattern, "SetSeasonalWindPattern" }, // 866199556
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindDirection, "SetWindDirection" }, // 4217514828
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_SetWindStrength, "SetWindStrength" }, // 991164761
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_Shutdown, "Shutdown" }, // 1031411054
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_Tick, "Tick" }, // 1840181799
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateFoliageWindAnimation, "UpdateFoliageWindAnimation" }, // 3914078925
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateMaterialParameters, "UpdateMaterialParameters" }, // 841255972
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1682698320
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateSeasonalWinds, "UpdateSeasonalWinds" }, // 2819738896
		{ &Z_Construct_UFunction_UAuracronFoliageWindManager_UpdateWindZone, "UpdateWindZone" }, // 4154237709
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageWindManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnWindZoneCreated = { "OnWindZoneCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, OnWindZoneCreated), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWindZoneCreated_MetaData), NewProp_OnWindZoneCreated_MetaData) }; // 926884191
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnWindZoneRemoved = { "OnWindZoneRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, OnWindZoneRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnWindZoneRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWindZoneRemoved_MetaData), NewProp_OnWindZoneRemoved_MetaData) }; // 1348096733
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnGlobalWindChanged = { "OnGlobalWindChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, OnGlobalWindChanged), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnGlobalWindChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGlobalWindChanged_MetaData), NewProp_OnGlobalWindChanged_MetaData) }; // 1462638333
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnSeasonalWindChanged = { "OnSeasonalWindChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, OnSeasonalWindChanged), Z_Construct_UDelegateFunction_UAuracronFoliageWindManager_OnSeasonalWindChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSeasonalWindChanged_MetaData), NewProp_OnSeasonalWindChanged_MetaData) }; // 2973766993
void Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageWindManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageWindManager), &Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, Configuration), Z_Construct_UScriptStruct_FAuracronWindConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4014270431
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_WindParameterCollection = { "WindParameterCollection", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, WindParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindParameterCollection_MetaData), NewProp_WindParameterCollection_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_WindParameterInstance = { "WindParameterInstance", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageWindManager, WindParameterInstance), Z_Construct_UClass_UMaterialParameterCollectionInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindParameterInstance_MetaData), NewProp_WindParameterInstance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageWindManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnWindZoneCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnWindZoneRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnGlobalWindChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_OnSeasonalWindChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_ManagedWorld,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_WindParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageWindManager_Statics::NewProp_WindParameterInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageWindManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageWindManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageWindManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageWindManager_Statics::ClassParams = {
	&UAuracronFoliageWindManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageWindManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageWindManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageWindManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageWindManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageWindManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageWindManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageWindManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageWindManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageWindManager.OuterSingleton;
}
UAuracronFoliageWindManager::UAuracronFoliageWindManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageWindManager);
UAuracronFoliageWindManager::~UAuracronFoliageWindManager() {}
// ********** End Class UAuracronFoliageWindManager ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronWindType_StaticEnum, TEXT("EAuracronWindType"), &Z_Registration_Info_UEnum_EAuracronWindType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2460887349U) },
		{ EAuracronWindStrength_StaticEnum, TEXT("EAuracronWindStrength"), &Z_Registration_Info_UEnum_EAuracronWindStrength, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2839793549U) },
		{ EAuracronWindAnimationType_StaticEnum, TEXT("EAuracronWindAnimationType"), &Z_Registration_Info_UEnum_EAuracronWindAnimationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 173128111U) },
		{ EAuracronSeasonalWindPattern_StaticEnum, TEXT("EAuracronSeasonalWindPattern"), &Z_Registration_Info_UEnum_EAuracronSeasonalWindPattern, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 778403172U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronWindConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronWindConfiguration_Statics::NewStructOps, TEXT("AuracronWindConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronWindConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWindConfiguration), 4014270431U) },
		{ FAuracronWindZoneData::StaticStruct, Z_Construct_UScriptStruct_FAuracronWindZoneData_Statics::NewStructOps, TEXT("AuracronWindZoneData"), &Z_Registration_Info_UScriptStruct_FAuracronWindZoneData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWindZoneData), 1744194180U) },
		{ FAuracronSeasonalWindData::StaticStruct, Z_Construct_UScriptStruct_FAuracronSeasonalWindData_Statics::NewStructOps, TEXT("AuracronSeasonalWindData"), &Z_Registration_Info_UScriptStruct_FAuracronSeasonalWindData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSeasonalWindData), 3191022848U) },
		{ FAuracronWindPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronWindPerformanceData_Statics::NewStructOps, TEXT("AuracronWindPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronWindPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWindPerformanceData), 2440545672U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageWindManager, UAuracronFoliageWindManager::StaticClass, TEXT("UAuracronFoliageWindManager"), &Z_Registration_Info_UClass_UAuracronFoliageWindManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageWindManager), 445975445U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_3642418004(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageWind_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
