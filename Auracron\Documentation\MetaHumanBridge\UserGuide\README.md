# MetaHuman Bridge User Guide

**Complete guide for using the AURACRON MetaHuman Bridge**

## Table of Contents

1. [Getting Started](#getting-started)
2. [Installation](#installation)
3. [First Steps](#first-steps)
4. [Basic Operations](#basic-operations)
5. [Advanced Features](#advanced-features)
6. [Python Scripting](#python-scripting)
7. [Blueprint Integration](#blueprint-integration)
8. [Performance Tips](#performance-tips)

## Getting Started

### Prerequisites

Before using the MetaHuman Bridge, ensure you have:

- **Unreal Engine 5.6** or later
- **MetaHuman Creator** account and DNA files
- **Visual Studio 2022** (Windows) or **Xcode 14+** (macOS)
- **Python 3.7+** (for Python bindings)
- **8GB RAM** minimum, 16GB recommended

### System Requirements

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| CPU | Intel i5-8400 / AMD Ryzen 5 2600 | Intel i7-10700K / AMD Ryzen 7 3700X |
| RAM | 8GB | 16GB+ |
| GPU | GTX 1060 / RX 580 | RTX 3070 / RX 6700 XT |
| Storage | 2GB free space | 10GB+ SSD |

## Installation

### Step 1: Copy Bridge Files

1. Copy the bridge source to your project:
   ```bash
   cp -r Source/AuracronMetaHumanBridge /path/to/your/project/Source/
   ```

2. Copy Python scripts:
   ```bash
   cp -r Scripts /path/to/your/project/
   ```

### Step 2: Update Project Configuration

1. Add to your project's `Build.cs` file:
   ```csharp
   PublicDependencyModuleNames.AddRange(new string[] {
       "AuracronMetaHumanBridge",
       "MetaHumanSDK",
       "DNAReader",
       "DNAWriter"
   });
   ```

2. Add to your project's `uproject` file:
   ```json
   {
       "Modules": [
           {
               "Name": "AuracronMetaHumanBridge",
               "Type": "Runtime"
           }
       ]
   }
   ```

### Step 3: Generate and Build

1. Generate project files:
   ```bash
   # Windows
   GenerateProjectFiles.bat
   
   # macOS/Linux
   ./GenerateProjectFiles.sh
   ```

2. Build the project in your IDE or command line:
   ```bash
   # Windows
   MSBuild YourProject.sln -p:Configuration=Development
   
   # macOS
   xcodebuild -project YourProject.xcodeproj -configuration Development
   ```

### Step 4: Verify Installation

1. Open your project in Unreal Editor
2. Open the Output Log (Window > Developer Tools > Output Log)
3. Run this command in the console:
   ```
   py exec("import MetaHuman; print('MetaHuman Bridge installed successfully!')")
   ```

## First Steps

### Loading Your First DNA File

1. **Prepare a DNA file** from MetaHuman Creator
2. **Place it in your project** (e.g., `/Content/MetaHuman/Characters/`)
3. **Create a simple test**:

```cpp
// In your C++ class
#include "AuracronMetaHumanBridge.h"

void AYourActor::TestMetaHumanBridge()
{
    // Create bridge instance
    UAuracronMetaHumanBridge* Bridge = NewObject<UAuracronMetaHumanBridge>();
    
    // Load DNA file
    FString DNAPath = TEXT("/Game/MetaHuman/Characters/MyCharacter.dna");
    if (Bridge->LoadDNAFromFile(DNAPath))
    {
        UE_LOG(LogTemp, Log, TEXT("DNA loaded successfully!"));
        UE_LOG(LogTemp, Log, TEXT("Mesh count: %d"), Bridge->GetMeshCount());
        UE_LOG(LogTemp, Log, TEXT("Joint count: %d"), Bridge->GetJointCount());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load DNA file"));
    }
}
```

### Your First Modification

```cpp
void AYourActor::ModifyCharacter()
{
    UAuracronMetaHumanBridge* Bridge = NewObject<UAuracronMetaHumanBridge>();
    
    if (Bridge->LoadDNAFromFile(TEXT("/Game/MetaHuman/Characters/MyCharacter.dna")))
    {
        // Move the head joint slightly
        FVector NewPosition = FVector(0.0f, 0.0f, 5.0f);
        Bridge->SetJointTranslation(0, NewPosition);
        
        // Adjust a facial blend shape
        Bridge->SetBlendShapeWeight(0, 0, 0.5f); // 50% weight
        
        // Save the modified character
        Bridge->SaveDNAToFile(TEXT("/Game/MetaHuman/Characters/ModifiedCharacter.dna"));
        
        UE_LOG(LogTemp, Log, TEXT("Character modified and saved!"));
    }
}
```

## Basic Operations

### DNA File Management

#### Loading DNA Files

```cpp
// Load from file
bool bSuccess = Bridge->LoadDNAFromFile(TEXT("/Game/MetaHuman/Character.dna"));

// Check if DNA is valid
if (Bridge->IsValidDNA())
{
    // DNA is loaded and ready to use
}
```

#### Saving DNA Files

```cpp
// Save to new file
Bridge->SaveDNAToFile(TEXT("/Game/MetaHuman/ModifiedCharacter.dna"));

// Save with backup
FString BackupPath = Bridge->CreateDNABackup(TEXT("BeforeModification"));
// ... make modifications ...
Bridge->SaveDNAToFile(TEXT("/Game/MetaHuman/ModifiedCharacter.dna"));
```

#### DNA Validation

```cpp
// Validate DNA file before loading
FDNAValidationResult Result = Bridge->ValidateDNAFile(
    TEXT("/Game/MetaHuman/Character.dna"), 
    EDNAValidationType::Complete
);

if (Result.bIsValid)
{
    UE_LOG(LogTemp, Log, TEXT("DNA file is valid"));
}
else
{
    for (const FString& Error : Result.Errors)
    {
        UE_LOG(LogTemp, Error, TEXT("DNA Error: %s"), *Error);
    }
}
```

### Joint Manipulation

#### Basic Joint Operations

```cpp
// Get joint information
int32 JointCount = Bridge->GetJointCount();
for (int32 i = 0; i < JointCount; ++i)
{
    FString JointName = Bridge->GetJointName(i);
    FVector Translation = Bridge->GetJointTranslation(i);
    FRotator Rotation = Bridge->GetJointRotation(i);
    FVector Scale = Bridge->GetJointScale(i);
    
    UE_LOG(LogTemp, Log, TEXT("Joint %d: %s"), i, *JointName);
}
```

#### Modifying Joint Transforms

```cpp
// Set joint position
Bridge->SetJointTranslation(0, FVector(10.0f, 0.0f, 0.0f));

// Set joint rotation
Bridge->SetJointRotation(1, FRotator(0.0f, 45.0f, 0.0f));

// Set joint scale
Bridge->SetJointScale(2, FVector(1.1f, 1.1f, 1.1f));
```

### Blend Shape Operations

#### Working with Blend Shapes

```cpp
// Get blend shape information
int32 MeshCount = Bridge->GetMeshCount();
for (int32 MeshIndex = 0; MeshIndex < MeshCount; ++MeshIndex)
{
    int32 BlendShapeCount = Bridge->GetBlendShapeTargetCount(MeshIndex);
    
    for (int32 TargetIndex = 0; TargetIndex < BlendShapeCount; ++TargetIndex)
    {
        FString TargetName = Bridge->GetBlendShapeTargetName(MeshIndex, TargetIndex);
        float Weight = Bridge->GetBlendShapeWeight(MeshIndex, TargetIndex);
        
        UE_LOG(LogTemp, Log, TEXT("Blend Shape %s: %.2f"), *TargetName, Weight);
    }
}
```

#### Modifying Blend Shapes

```cpp
// Set facial expression
Bridge->SetBlendShapeWeight(0, 0, 0.8f); // Smile
Bridge->SetBlendShapeWeight(0, 1, 0.3f); // Eye squint
Bridge->SetBlendShapeWeight(0, 2, 0.0f); // Reset frown
```

### Mesh Deformation

#### Direct Vertex Manipulation

```cpp
// Get current vertex positions
TArray<FVector> Vertices = Bridge->GetVertexPositions(0);

// Modify vertices (example: move all vertices up by 1 unit)
for (FVector& Vertex : Vertices)
{
    Vertex.Z += 1.0f;
}

// Apply modified vertices
Bridge->SetVertexPositions(0, Vertices);
```

## Advanced Features

### Texture Generation

```cpp
// Configure texture generation
TMap<FString, FString> TextureConfig;
TextureConfig.Add(TEXT("SkinTone"), TEXT("Medium"));
TextureConfig.Add(TEXT("AgeLevel"), TEXT("25"));
TextureConfig.Add(TEXT("DetailLevel"), TEXT("High"));

// Generate skin texture
FString TexturePath = Bridge->GenerateSkinTexture(TextureConfig);
UE_LOG(LogTemp, Log, TEXT("Generated texture: %s"), *TexturePath);
```

### Hair System Integration

```cpp
// Configure hair generation
TMap<FString, FString> HairConfig;
HairConfig.Add(TEXT("HairStyle"), TEXT("Long"));
HairConfig.Add(TEXT("HairColor"), TEXT("Brown"));
HairConfig.Add(TEXT("HairDensity"), TEXT("High"));

// Generate hair system
bool bHairGenerated = Bridge->GenerateHairSystem(HairConfig);
```

### Clothing Adaptation

```cpp
// Adapt clothing to modified body
TMap<FString, FString> ClothingConfig;
ClothingConfig.Add(TEXT("ClothingType"), TEXT("Casual"));
ClothingConfig.Add(TEXT("FitType"), TEXT("Fitted"));

bool bClothingAdapted = Bridge->AdaptClothing(ClothingConfig);
```

### Performance Optimization

```cpp
// Initialize performance optimization
TMap<FString, FString> PerfConfig;
PerfConfig.Add(TEXT("EnableGPU"), TEXT("true"));
PerfConfig.Add(TEXT("MemoryPoolSize"), TEXT("1024"));
PerfConfig.Add(TEXT("ThreadCount"), TEXT("4"));

Bridge->InitializePerformanceOptimization(PerfConfig);

// Monitor performance
TMap<FString, FString> Metrics = Bridge->GetCurrentPerformanceMetrics();
for (const auto& Metric : Metrics)
{
    UE_LOG(LogTemp, Log, TEXT("%s: %s"), *Metric.Key, *Metric.Value);
}
```

### Error Handling

```cpp
// Configure error handling
Bridge->SetErrorHandlingMode(true, true); // Auto-recovery, detailed logging
Bridge->SetErrorReportingEnabled(true);

// Monitor system health
float HealthScore = Bridge->GetSystemHealthScore();
if (HealthScore < 0.8f)
{
    UE_LOG(LogTemp, Warning, TEXT("System health low: %.2f"), HealthScore);
    
    // Get error history
    TArray<FErrorInfo> Errors = Bridge->GetErrorHistory(10);
    for (const FErrorInfo& Error : Errors)
    {
        UE_LOG(LogTemp, Error, TEXT("Error %s: %s"), *Error.ErrorCode, *Error.ErrorMessage);
    }
}
```

## Python Scripting

### Basic Python Usage

```python
import MetaHuman

# Load DNA file
if MetaHuman.load_dna_from_file("/Game/MetaHuman/Character.dna"):
    print("DNA loaded successfully!")
    
    # Get basic information
    mesh_count = MetaHuman.get_mesh_count()
    joint_count = MetaHuman.get_joint_count()
    print(f"Meshes: {mesh_count}, Joints: {joint_count}")
    
    # Modify character
    MetaHuman.set_joint_translation(0, 0.0, 0.0, 5.0)
    MetaHuman.set_blendshape_weight(0, 0, 0.5)
    
    # Save changes
    MetaHuman.save_dna_to_file("/Game/MetaHuman/ModifiedCharacter.dna")
    print("Character saved!")
```

### Batch Processing

```python
import MetaHuman
import os

def process_all_characters(input_dir, output_dir):
    """Process all DNA files in a directory"""
    for filename in os.listdir(input_dir):
        if filename.endswith('.dna'):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, f"modified_{filename}")
            
            if MetaHuman.load_dna_from_file(input_path):
                # Apply modifications
                MetaHuman.set_joint_translation(0, 0.0, 0.0, 2.0)
                MetaHuman.set_blendshape_weight(0, 0, 0.3)
                
                # Save modified version
                MetaHuman.save_dna_to_file(output_path)
                print(f"Processed: {filename}")

# Usage
process_all_characters("/Game/MetaHuman/Input/", "/Game/MetaHuman/Output/")
```

### Random Character Generation

```python
import MetaHuman
import random

def generate_random_character(base_dna_path, output_path):
    """Generate a random character variation"""
    if MetaHuman.load_dna_from_file(base_dna_path):
        joint_count = MetaHuman.get_joint_count()
        
        # Randomize joint positions slightly
        for i in range(min(10, joint_count)):  # First 10 joints
            x_offset = random.uniform(-2.0, 2.0)
            y_offset = random.uniform(-2.0, 2.0)
            z_offset = random.uniform(-1.0, 1.0)
            
            current_pos = MetaHuman.get_joint_translation(i)
            new_pos = (
                current_pos[0] + x_offset,
                current_pos[1] + y_offset,
                current_pos[2] + z_offset
            )
            MetaHuman.set_joint_translation(i, *new_pos)
        
        # Randomize blend shapes
        mesh_count = MetaHuman.get_mesh_count()
        for mesh_idx in range(mesh_count):
            blend_count = MetaHuman.get_blendshape_target_count(mesh_idx)
            for blend_idx in range(min(5, blend_count)):  # First 5 blend shapes
                weight = random.uniform(0.0, 1.0)
                MetaHuman.set_blendshape_weight(mesh_idx, blend_idx, weight)
        
        MetaHuman.save_dna_to_file(output_path)
        return True
    return False

# Generate 10 random variations
for i in range(10):
    generate_random_character(
        "/Game/MetaHuman/Base.dna",
        f"/Game/MetaHuman/Random/Character_{i:03d}.dna"
    )
```

## Blueprint Integration

The MetaHuman Bridge provides complete Blueprint integration. All C++ functions are exposed as Blueprint nodes.

### Common Blueprint Patterns

1. **Load and Validate DNA**:
   - Use "Load DNA From File" node
   - Check "Is Valid DNA" before proceeding
   - Handle errors with "Get Error History"

2. **Modify Character**:
   - Use joint manipulation nodes
   - Use blend shape nodes
   - Apply changes incrementally

3. **Save Results**:
   - Use "Save DNA To File" node
   - Create backups with "Create DNA Backup"
   - Validate results with "Validate DNA File"

## Performance Tips

### Memory Management

1. **Use object pooling** for bridge instances
2. **Clear unused DNA data** regularly
3. **Monitor memory usage** with performance metrics
4. **Enable garbage collection** when appropriate

### GPU Acceleration

1. **Enable GPU processing** in configuration
2. **Use batch operations** for multiple modifications
3. **Optimize vertex operations** for large meshes
4. **Monitor GPU memory usage**

### Best Practices

1. **Validate DNA files** before processing
2. **Use error handling** for robust applications
3. **Cache frequently used data**
4. **Profile performance** regularly
5. **Use async operations** for heavy processing

---

For more advanced topics and examples, see the [Examples](../Examples/README.md) section.
