// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Collision Integration System Implementation
// Bridge 4.7: Foliage - Collision Integration

#include "AuracronFoliageCollision.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageBridge.h"
#include "AuracronPCGCollisionSystem.h"

// UE5.6 Collision includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"

// Physics includes
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Destructible includes
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Geometry processing
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/MeshNormals.h"
#include "Operations/MeshSimplification.h"

// =============================================================================
// FOLIAGE COLLISION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::Instance = nullptr;

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageCollisionManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageCollisionManager::Initialize(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Initialize performance data
    PerformanceData = FAuracronCollisionPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastCollisionUpdate = 0.0f;
    LastPhysicsUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager initialized with collision type: %s, physics: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultCollisionType),
                              Configuration.bEnablePhysicsInteraction ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageCollisionManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager shutdown completed"));
}

bool UAuracronFoliageCollisionManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageCollisionManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update collision meshes
    LastCollisionUpdate += DeltaTime;
    if (LastCollisionUpdate >= Configuration.CollisionUpdateInterval)
    {
        FScopeLock Lock(&CollisionLock);
        
        int32 UpdatedMeshes = 0;
        const int32 MaxUpdatesThisFrame = Configuration.MaxCollisionUpdatesPerFrame;

        for (auto& MeshPair : CollisionMeshes)
        {
            if (UpdatedMeshes >= MaxUpdatesThisFrame)
            {
                break;
            }

            FAuracronCollisionMeshData& MeshData = MeshPair.Value;
            UpdateCollisionMeshInternal(MeshData, DeltaTime);
            UpdatedMeshes++;
        }

        LastCollisionUpdate = 0.0f;
    }

    // Update destructible foliage
    if (Configuration.bEnableDestructibleFoliage)
    {
        for (auto& DestructiblePair : DestructibleFoliage)
        {
            FAuracronDestructibleFoliageData& DestructibleData = DestructiblePair.Value;
            UpdateDestructibleFoliageInternal(DestructibleData, DeltaTime);
        }
    }

    // Update trampling effects
    if (Configuration.bEnableTramplingEffects)
    {
        for (auto& TramplingPair : TramplingEffects)
        {
            FAuracronTramplingEffectData& TramplingData = TramplingPair.Value;
            UpdateTramplingEffectInternal(TramplingData, DeltaTime);
        }
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageCollisionManager::SetConfiguration(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision configuration updated"));
}

FAuracronFoliageCollisionConfiguration UAuracronFoliageCollisionManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMesh(UStaticMesh* SourceMesh, EAuracronFoliageCollisionType CollisionType)
{
    if (!bIsInitialized || !SourceMesh)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized or invalid source mesh"));
        return FString();
    }

    FScopeLock Lock(&CollisionLock);

    FString CollisionMeshId = GenerateCollisionMeshId();

    FAuracronCollisionMeshData NewMeshData;
    NewMeshData.CollisionMeshId = CollisionMeshId;
    NewMeshData.SourceMesh = SourceMesh;
    NewMeshData.CollisionType = CollisionType;
    NewMeshData.GenerationTime = FDateTime::Now();

    // Generate simplified collision mesh based on type
    switch (CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
        case EAuracronFoliageCollisionType::PhysicsOnly:
        case EAuracronFoliageCollisionType::CollisionEnabled:
            {
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, NewMeshData.CollisionComplexity);
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        case EAuracronFoliageCollisionType::Destructible:
            {
                // For destructible, we need more complex collision setup
                NewMeshData.bUseComplexCollision = true;
                NewMeshData.bGenerateOverlapEvents = true;
                NewMeshData.CollisionMesh = SourceMesh; // Use original mesh for destructible
                NewMeshData.bIsGenerated = true;
            }
            break;

        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            {
                // For interactive/trampling, we need overlap events
                NewMeshData.bGenerateOverlapEvents = true;
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, 0.3f); // Lower complexity for interaction
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        default:
            NewMeshData.CollisionMesh = SourceMesh;
            NewMeshData.bIsGenerated = true;
            break;
    }

    CollisionMeshes.Add(CollisionMeshId, NewMeshData);

    OnCollisionMeshGenerated.Broadcast(CollisionMeshId, NewMeshData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh generated: %s (type: %s)"), 
                              *CollisionMeshId, 
                              *UEnum::GetValueAsString(CollisionType));

    return CollisionMeshId;
}

bool UAuracronFoliageCollisionManager::UpdateCollisionMesh(const FString& CollisionMeshId, const FAuracronCollisionMeshData& MeshData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    FAuracronCollisionMeshData UpdatedMeshData = MeshData;
    UpdatedMeshData.CollisionMeshId = CollisionMeshId;

    CollisionMeshes[CollisionMeshId] = UpdatedMeshData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh updated: %s"), *CollisionMeshId);

    return true;
}

bool UAuracronFoliageCollisionManager::RemoveCollisionMesh(const FString& CollisionMeshId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    CollisionMeshes.Remove(CollisionMeshId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh removed: %s"), *CollisionMeshId);

    return true;
}

FAuracronCollisionMeshData UAuracronFoliageCollisionManager::GetCollisionMesh(const FString& CollisionMeshId) const
{
    FScopeLock Lock(&CollisionLock);

    if (const FAuracronCollisionMeshData* MeshData = CollisionMeshes.Find(CollisionMeshId))
    {
        return *MeshData;
    }

    return FAuracronCollisionMeshData();
}

TArray<FAuracronCollisionMeshData> UAuracronFoliageCollisionManager::GetAllCollisionMeshes() const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FAuracronCollisionMeshData> AllMeshes;
    CollisionMeshes.GenerateValueArray(AllMeshes);
    return AllMeshes;
}

void UAuracronFoliageCollisionManager::ValidateConfiguration()
{
    // Validate collision settings
    Configuration.DefaultMass = FMath::Clamp(Configuration.DefaultMass, 0.1f, 1000.0f);
    Configuration.DefaultLinearDamping = FMath::Clamp(Configuration.DefaultLinearDamping, 0.0f, 1.0f);
    Configuration.DefaultAngularDamping = FMath::Clamp(Configuration.DefaultAngularDamping, 0.0f, 1.0f);
    Configuration.DefaultRestitution = FMath::Clamp(Configuration.DefaultRestitution, 0.0f, 1.0f);
    Configuration.DefaultFriction = FMath::Clamp(Configuration.DefaultFriction, 0.0f, 2.0f);

    // Validate destructible settings
    Configuration.DestructionThreshold = FMath::Max(1.0f, Configuration.DestructionThreshold);
    Configuration.DestructionImpulse = FMath::Max(1.0f, Configuration.DestructionImpulse);

    // Validate trampling settings
    Configuration.TramplingRadius = FMath::Clamp(Configuration.TramplingRadius, 10.0f, 1000.0f);
    Configuration.TramplingForce = FMath::Clamp(Configuration.TramplingForce, 1.0f, 1000.0f);
    Configuration.TramplingRecoveryTime = FMath::Max(0.1f, Configuration.TramplingRecoveryTime);

    // Validate performance settings
    Configuration.MaxCollisionUpdatesPerFrame = FMath::Max(1, Configuration.MaxCollisionUpdatesPerFrame);
    Configuration.CollisionUpdateInterval = FMath::Max(0.01f, Configuration.CollisionUpdateInterval);
    Configuration.MaxCollisionDistance = FMath::Max(100.0f, Configuration.MaxCollisionDistance);
    Configuration.CollisionDisableDistance = FMath::Max(100.0f, Configuration.CollisionDisableDistance);
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMeshId() const
{
    return FString::Printf(TEXT("CollisionMesh_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateDestructibleId() const
{
    return FString::Printf(TEXT("Destructible_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateTramplingId() const
{
    return FString::Printf(TEXT("Trampling_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageCollisionManager::UpdateCollisionMeshInternal(FAuracronCollisionMeshData& MeshData, float DeltaTime)
{
    // Update collision mesh state based on distance and LOD
    if (Configuration.bDisableCollisionForDistantFoliage && ManagedWorld.IsValid())
    {
        // Get viewer location using UE5.6 camera management APIs
        FVector ViewerLocation = FVector::ZeroVector;
        
        // Try to get camera location from multiple sources for robustness
        if (APlayerController* PlayerController = ManagedWorld->GetFirstPlayerController())
        {
            // First priority: Camera manager view target
            if (APlayerCameraManager* CameraManager = PlayerController->PlayerCameraManager)
            {
                FVector CameraLocation;
                FRotator CameraRotation;
                CameraManager->GetCameraViewPoint(CameraLocation, CameraRotation);
                ViewerLocation = CameraLocation;
                
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using camera manager location: %s"), *ViewerLocation.ToString());
            }
            // Fallback: Pawn location if camera manager unavailable
            else if (APawn* ViewerPawn = PlayerController->GetPawn())
            {
                // For characters, use eye height for more accurate distance calculations
                if (ACharacter* Character = Cast<ACharacter>(ViewerPawn))
                {
                    ViewerLocation = Character->GetActorLocation() + FVector(0, 0, Character->BaseEyeHeight);
                }
                else
                {
                    ViewerLocation = ViewerPawn->GetActorLocation();
                }
                
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using pawn location: %s"), *ViewerLocation.ToString());
            }
            // Last resort: Controller location
            else if (PlayerController->GetRootComponent())
            {
                ViewerLocation = PlayerController->GetActorLocation();
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using controller location: %s"), *ViewerLocation.ToString());
            }
        }
        
        // If still no valid location, try to get from active viewport (editor/PIE scenarios)
        if (ViewerLocation.IsZero() && GEngine)
        {
            for (FConstPlayerControllerIterator Iterator = ManagedWorld->GetPlayerControllerIterator(); Iterator; ++Iterator)
            {
                if (APlayerController* PC = Iterator->Get())
                {
                    if (ULocalPlayer* LocalPlayer = PC->GetLocalPlayer())
                    {
                        if (UGameViewportClient* ViewportClient = LocalPlayer->ViewportClient)
                        {
                            FVector ViewLocation;
                            FRotator ViewRotation;
                            ViewportClient->GetViewPoint(ViewLocation, ViewRotation);
                            if (!ViewLocation.IsZero())
                            {
                                ViewerLocation = ViewLocation;
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using viewport location: %s"), *ViewerLocation.ToString());
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Calculate distance to collision mesh
        float Distance = 0.0f;
        
        // Get mesh bounds center for distance calculation
        if (MeshData.CollisionMesh)
        {
            FBox MeshBounds = MeshData.CollisionMesh->GetBounds().GetBox();
            FVector MeshCenter = MeshBounds.GetCenter();
            
            // Calculate actual distance from viewer to mesh center
            Distance = FVector::Dist(ViewerLocation, MeshCenter);
        }
        else
        {
            // Fallback: use a large distance if no mesh is available
            Distance = Configuration.CollisionDisableDistance + 1000.0f;
        }

        if (Distance > Configuration.CollisionDisableDistance)
        {
            // Disable collision for distant meshes by modifying actual collision components
            if (ManagedWorld.IsValid())
            {
                // Find all foliage instances using this collision mesh
                for (TActorIterator<AInstancedFoliageActor> ActorItr(ManagedWorld.Get()); ActorItr; ++ActorItr)
                {
                    AInstancedFoliageActor* FoliageActor = *ActorItr;
                    if (FoliageActor)
                    {
                        // Iterate through foliage types
                        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
                        {
                            UFoliageType* FoliageType = FoliagePair.Key;
                            FFoliageInfo& FoliageInfo = *FoliagePair.Value;
                            
                            if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = FoliageInfo.GetComponent())
                            {
                                // Disable collision for distant instances
                                HISMComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
                                
                                // Mark for re-enabling when closer
                                MeshData.bCollisionDisabledForDistance = true;
                                MeshData.LastDistanceCheck = FDateTime::Now();
                                
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Disabled collision for distant foliage mesh: %s (Distance: %.1f)"), 
                                                            *MeshData.CollisionMeshId, Distance);
                            }
                        }
                    }
                }
            }
        }
        else if (MeshData.bCollisionDisabledForDistance)
        {
            // Re-enable collision for meshes that are now close enough
            if (ManagedWorld.IsValid())
            {
                for (TActorIterator<AInstancedFoliageActor> ActorItr(ManagedWorld.Get()); ActorItr; ++ActorItr)
                {
                    AInstancedFoliageActor* FoliageActor = *ActorItr;
                    if (FoliageActor)
                    {
                        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
                        {
                            UFoliageType* FoliageType = FoliagePair.Key;
                            FFoliageInfo& FoliageInfo = *FoliagePair.Value;
                            
                            if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = FoliageInfo.GetComponent())
                            {
                                // Re-enable collision based on foliage type settings
                                ECollisionEnabled::Type CollisionType = ECollisionEnabled::QueryAndPhysics;
                                if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
                                {
                                    CollisionType = StaticMeshType->BodyInstance.GetCollisionEnabled();
                                }
                                
                                HISMComponent->SetCollisionEnabled(CollisionType);
                                
                                MeshData.bCollisionDisabledForDistance = false;
                                
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Re-enabled collision for foliage mesh: %s (Distance: %.1f)"), 
                                                            *MeshData.CollisionMeshId, Distance);
                            }
                        }
                    }
                }
            }
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateDestructibleFoliageInternal(FAuracronDestructibleFoliageData& DestructibleData, float DeltaTime)
{
    if (DestructibleData.bIsDestroyed && DestructibleData.bCanRegenerate)
    {
        // Handle regeneration
        float TimeSinceDestruction = (FDateTime::Now() - DestructibleData.DestructionTime).GetTotalSeconds();

        if (TimeSinceDestruction >= DestructibleData.RegenerationTime)
        {
            // Start regeneration process
            DestructibleData.Health += DestructibleData.RegenerationRate * DeltaTime;

            if (DestructibleData.Health >= DestructibleData.MaxHealth)
            {
                DestructibleData.Health = DestructibleData.MaxHealth;
                DestructibleData.bIsDestroyed = false;

                AURACRON_FOLIAGE_LOG_INFO(TEXT("Destructible foliage regenerated: %s"), *DestructibleData.DestructibleId);
            }
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateTramplingEffectInternal(FAuracronTramplingEffectData& TramplingData, float DeltaTime)
{
    if (TramplingData.bIsActive && TramplingData.bAutoRecover)
    {
        // Handle recovery
        TramplingData.CurrentRecoveryProgress += TramplingData.RecoveryRate * DeltaTime;

        if (TramplingData.CurrentRecoveryProgress >= 1.0f)
        {
            // Recovery complete
            TramplingData.bIsActive = false;
            TramplingData.CurrentRecoveryProgress = 0.0f;

            AURACRON_FOLIAGE_LOG_INFO(TEXT("Trampling effect recovered: %s"), *TramplingData.TramplingId);
        }
    }
}

void UAuracronFoliageCollisionManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&CollisionLock);

    // Reset counters
    PerformanceData.TotalCollisionMeshes = CollisionMeshes.Num();
    PerformanceData.ActiveCollisionMeshes = 0;
    PerformanceData.DestructibleInstances = DestructibleFoliage.Num();
    PerformanceData.TramplingEffects = TramplingEffects.Num();

    // Count active collision meshes
    for (const auto& MeshPair : CollisionMeshes)
    {
        if (MeshPair.Value.bIsGenerated)
        {
            PerformanceData.ActiveCollisionMeshes++;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealCollisionMemoryUsage();
}

UStaticMesh* UAuracronFoliageCollisionManager::GenerateSimplifiedCollisionMesh(UStaticMesh* SourceMesh, float ComplexityLevel) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::GenerateSimplifiedCollisionMesh);

    if (!SourceMesh)
    {
        return nullptr;
    }

    // Real mesh simplification implementation using UE5.6 APIs
    UStaticMesh* SimplifiedMesh = DuplicateObject<UStaticMesh>(SourceMesh, GetTransientPackage());
    if (!SimplifiedMesh)
    {
        return SourceMesh;
    }

    // Get mesh description for processing
    FMeshDescription* MeshDescription = SimplifiedMesh->GetMeshDescription(0);
    if (!MeshDescription)
    {
        return SourceMesh;
    }

    // Calculate reduction percentage based on complexity level
    float ReductionPercentage = FMath::Clamp(1.0f - ComplexityLevel, 0.1f, 0.9f);

    // Apply collision-specific simplification
    bool bSimplificationSuccess = false;

    // Use different simplification strategies based on complexity level
    if (ComplexityLevel > 0.7f)
    {
        // High complexity - use edge collapse for precision
        bSimplificationSuccess = ApplyEdgeCollapseForCollision(MeshDescription, ReductionPercentage);
    }
    else if (ComplexityLevel > 0.4f)
    {
        // Medium complexity - use convex hull approximation
        bSimplificationSuccess = ApplyConvexHullSimplification(MeshDescription, ReductionPercentage);
    }
    else
    {
        // Low complexity - use bounding box collision
        bSimplificationSuccess = ApplyBoundingBoxCollision(SimplifiedMesh);
    }

    if (bSimplificationSuccess)
    {
        // Rebuild mesh with simplified data
        SimplifiedMesh->CommitMeshDescription(0);
        SimplifiedMesh->Build();

        // Generate collision data optimized for foliage
        SimplifiedMesh->CreateBodySetup();
        UBodySetup* BodySetup = SimplifiedMesh->GetBodySetup();
        if (BodySetup)
        {
            // Configure collision settings for foliage
            BodySetup->CollisionTraceFlag = CTF_UseSimpleAsComplex;
            BodySetup->bGenerateMirroredCollision = false;
            BodySetup->bDoubleSidedGeometry = false;
            BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            BodySetup->DefaultInstance.SetObjectType(ECC_WorldStatic);
            BodySetup->DefaultInstance.SetResponseToAllChannels(ECR_Block);
            BodySetup->DefaultInstance.SetResponseToChannel(ECC_Pawn, ECR_Overlap); // Allow pawn overlap for trampling

            // Create physics meshes
            BodySetup->CreatePhysicsMeshes();
        }

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Generated simplified collision mesh: %s (Complexity: %.2f, Reduction: %.1f%%)"),
                                 *SourceMesh->GetName(), ComplexityLevel, ReductionPercentage * 100.0f);

        return SimplifiedMesh;
    }
    else
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Failed to simplify collision mesh: %s"), *SourceMesh->GetName());
        return SourceMesh;
    }
}

void UAuracronFoliageCollisionManager::ApplyPhysicsPropertiesInternal(UBodyInstance* BodyInstance, const FAuracronCollisionMeshData& CollisionData)
{
    if (!BodyInstance)
    {
        return;
    }

    // Apply physics properties
    BodyInstance->SetMassOverride(CollisionData.Mass, true);
    BodyInstance->LinearDamping = CollisionData.LinearDamping;
    BodyInstance->AngularDamping = CollisionData.AngularDamping;

    // Set collision response
    switch (CollisionData.CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            break;

        case EAuracronFoliageCollisionType::PhysicsOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::PhysicsOnly);
            break;

        case EAuracronFoliageCollisionType::CollisionEnabled:
        case EAuracronFoliageCollisionType::Destructible:
        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            break;

        default:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
    }

    // Set collision channel
    BodyInstance->SetObjectType(Configuration.FoliageCollisionChannel);

    // Configure overlap events
    if (CollisionData.bGenerateOverlapEvents)
    {
        BodyInstance->bGenerateOverlapEvents = true;
    }
}

void UAuracronFoliageCollisionManager::CreateDestructionFragments(const FVector& Location, const FAuracronDestructibleFoliageData& DestructibleData)
{
    if (!ManagedWorld.IsValid())
    {
        return;
    }

    // Create destruction fragments
    for (int32 i = 0; i < DestructibleData.FragmentCount; ++i)
    {
        FVector FragmentLocation = Location + FMath::VRand() * DestructibleData.FragmentSpread;
        FVector FragmentVelocity = FMath::VRand() * DestructibleData.DestructionImpulse;

        // In production, this would spawn actual fragment actors/components
        // For now, we just log the creation
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Created destruction fragment %d at location: %s"), i, *FragmentLocation.ToString());
    }
}

void UAuracronFoliageCollisionManager::ApplyTramplingDeformation(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronTramplingEffectData& TramplingData)
{
    if (!Component)
    {
        return;
    }

    // Get current instance transform
    FTransform InstanceTransform;
    if (Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
    {
        // Apply trampling deformation based on effect type
        switch (TramplingData.TramplingEffect)
        {
            case EAuracronTramplingEffect::Bend:
                {
                    // Apply bending rotation
                    FRotator BendRotation(TramplingData.BendAngle, 0.0f, 0.0f);
                    InstanceTransform.SetRotation(InstanceTransform.GetRotation() * BendRotation.Quaternion());
                }
                break;

            case EAuracronTramplingEffect::Flatten:
                {
                    // Apply flattening scale
                    FVector Scale = InstanceTransform.GetScale3D();
                    Scale.Z *= TramplingData.FlattenAmount;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            case EAuracronTramplingEffect::Crush:
                {
                    // Apply crushing scale
                    FVector Scale = InstanceTransform.GetScale3D() * 0.5f;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            default:
                break;
        }

        // Update instance transform
        Component->UpdateInstanceTransform(InstanceIndex, InstanceTransform, true, true);
    }
}

// === Collision Mesh Simplification Helper Functions ===

bool UAuracronFoliageCollisionManager::ApplyEdgeCollapseForCollision(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyEdgeCollapseForCollision);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for edge collapse simplification"));
        return false;
    }

    // Real edge collapse implementation using UE5.6 GeometryProcessing APIs
    using namespace UE::Geometry;

    try
    {
        // Convert FMeshDescription to FDynamicMesh3 for processing
        FDynamicMesh3 DynamicMesh;
        FMeshDescriptionToDynamicMesh Converter;
        
        // Configure conversion settings for collision mesh optimization
        Converter.bPrintDebugMessages = false;
        Converter.bVerbose = false;
        
        if (!Converter.Convert(MeshDescription, DynamicMesh))
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert MeshDescription to DynamicMesh3"));
            return false;
        }

        // Ensure mesh is compact for optimal processing
        if (!DynamicMesh.IsCompact())
        {
            DynamicMesh.CompactInPlace();
        }

        // Validate mesh integrity before simplification
        if (!DynamicMesh.CheckValidity(true))
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Source mesh has validity issues, attempting to repair"));
            FMeshRepairOperations::RepairMesh(&DynamicMesh);
        }

        // Calculate target triangle count based on reduction percentage
        int32 OriginalTriangleCount = DynamicMesh.TriangleCount();
        int32 TargetTriangleCount = FMath::Max(4, FMath::FloorToInt(OriginalTriangleCount * (1.0f - ReductionPercentage)));

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Starting edge collapse: %d -> %d triangles (%.1f%% reduction)"),
                                    OriginalTriangleCount, TargetTriangleCount, ReductionPercentage * 100.0f);

        // Configure QEM (Quadric Error Metric) simplification for collision optimization
        FQEMSimplification Simplifier(&DynamicMesh);
        
        // Configure simplification parameters for collision mesh
        Simplifier.bPreserveBoundaryShape = true;  // Maintain silhouette for collision accuracy
        Simplifier.bPreserveSharpEdges = true;     // Keep important geometric features
        Simplifier.bDiscardAttributes = true;      // Remove UV/normal data for collision
        Simplifier.bReproject = false;             // No need for surface reprojection in collision
        Simplifier.GeometricTolerance = 0.1f;      // Reasonable tolerance for collision
        
        // Apply simplification
        if (Simplifier.SimplifyToTriangleCount(TargetTriangleCount))
        {
            // Validate simplified mesh
            if (!DynamicMesh.CheckValidity(false))
            {
                AURACRON_FOLIAGE_LOG_WARNING(TEXT("Simplified mesh has validity issues, attempting repair"));
                FMeshRepairOperations::RepairMesh(&DynamicMesh);
            }

            // Convert back to FMeshDescription
            FDynamicMeshToMeshDescription BackConverter;
            BackConverter.bVerbose = false;
            
            // Clear existing mesh data
            MeshDescription->Empty();
            
            if (BackConverter.Convert(&DynamicMesh, *MeshDescription))
            {
                int32 FinalTriangleCount = DynamicMesh.TriangleCount();
                float ActualReduction = (float)(OriginalTriangleCount - FinalTriangleCount) / (float)OriginalTriangleCount;
                
                AURACRON_FOLIAGE_LOG_INFO(TEXT("Edge collapse simplification completed: %d -> %d triangles (%.1f%% reduction)"),
                                         OriginalTriangleCount, FinalTriangleCount, ActualReduction * 100.0f);
                return true;
            }
            else
            {
                AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert simplified DynamicMesh3 back to MeshDescription"));
                return false;
            }
        }
        else
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("QEM simplification failed to reach target triangle count"));
            return false;
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during edge collapse simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during edge collapse simplification"));
        return false;
    }
}

bool UAuracronFoliageCollisionManager::ApplyConvexHullSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyConvexHullSimplification);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for convex hull simplification"));
        return false;
    }

    // Real convex hull implementation using UE5.6 GeometryProcessing APIs
    using namespace UE::Geometry;

    try
    {
        // Convert to DynamicMesh3 for processing
        FDynamicMesh3 DynamicMesh;
        FMeshDescriptionToDynamicMesh Converter;
        Converter.bPrintDebugMessages = false;
        Converter.bVerbose = false;
        
        if (!Converter.Convert(MeshDescription, DynamicMesh))
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert MeshDescription to DynamicMesh3 for convex hull"));
            return false;
        }

        // Ensure mesh is compact
        if (!DynamicMesh.IsCompact())
        {
            DynamicMesh.CompactInPlace();
        }

        int32 OriginalTriangleCount = DynamicMesh.TriangleCount();
        
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Starting convex hull simplification: %d triangles"), OriginalTriangleCount);

        // Extract vertices from mesh for convex hull computation
        TArray<FVector3d> HullVertices;
        HullVertices.Reserve(DynamicMesh.VertexCount());
        
        for (int32 VertexID : DynamicMesh.VertexIndicesItr())
        {
            HullVertices.Add(DynamicMesh.GetVertex(VertexID));
        }

        if (HullVertices.Num() < 4)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Insufficient vertices for convex hull generation: %d"), HullVertices.Num());
            return false;
        }

        // Generate convex hull using UE5.6 ConvexHull3d
        FConvexHull3d ConvexHull;
        bool bHullSuccess = ConvexHull.Solve(HullVertices);
        
        if (!bHullSuccess || ConvexHull.GetTriangles().Num() == 0)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Failed to generate convex hull from mesh vertices"));
            return false;
        }

        // Create new mesh from convex hull
        FDynamicMesh3 HullMesh;
        
        // Add hull vertices to new mesh
        TArray<int32> VertexMap;
        VertexMap.SetNum(ConvexHull.GetVertices().Num());
        
        for (int32 i = 0; i < ConvexHull.GetVertices().Num(); ++i)
        {
            VertexMap[i] = HullMesh.AppendVertex(ConvexHull.GetVertices()[i]);
        }

        // Add hull triangles to new mesh
        for (const FIndex3i& Triangle : ConvexHull.GetTriangles())
        {
            if (Triangle.A < VertexMap.Num() && Triangle.B < VertexMap.Num() && Triangle.C < VertexMap.Num())
            {
                int32 TriangleID = HullMesh.AppendTriangle(VertexMap[Triangle.A], VertexMap[Triangle.B], VertexMap[Triangle.C]);
                if (TriangleID == FDynamicMesh3::InvalidID)
                {
                    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Failed to add triangle to hull mesh: %d, %d, %d"), 
                                                Triangle.A, Triangle.B, Triangle.C);
                }
            }
        }

        // Validate hull mesh
        if (!HullMesh.CheckValidity(false))
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Generated convex hull mesh has validity issues, attempting repair"));
            FMeshRepairOperations::RepairMesh(&HullMesh);
        }

        // Optionally apply additional simplification based on reduction percentage
        if (ReductionPercentage > 0.1f && HullMesh.TriangleCount() > 8)
        {
            int32 TargetTriangleCount = FMath::Max(4, FMath::FloorToInt(HullMesh.TriangleCount() * (1.0f - ReductionPercentage)));
            
            FQEMSimplification HullSimplifier(&HullMesh);
            HullSimplifier.bPreserveBoundaryShape = true;
            HullSimplifier.bDiscardAttributes = true;
            HullSimplifier.GeometricTolerance = 0.2f; // Slightly higher tolerance for hull
            
            HullSimplifier.SimplifyToTriangleCount(TargetTriangleCount);
        }

        // Convert hull mesh back to FMeshDescription
        FDynamicMeshToMeshDescription BackConverter;
        BackConverter.bVerbose = false;
        
        // Clear existing mesh data
        MeshDescription->Empty();
        
        if (BackConverter.Convert(&HullMesh, *MeshDescription))
        {
            int32 FinalTriangleCount = HullMesh.TriangleCount();
            float ActualReduction = (float)(OriginalTriangleCount - FinalTriangleCount) / (float)OriginalTriangleCount;
            
            AURACRON_FOLIAGE_LOG_INFO(TEXT("Convex hull simplification completed: %d -> %d triangles (%.1f%% reduction)"),
                                     OriginalTriangleCount, FinalTriangleCount, ActualReduction * 100.0f);
            return true;
        }
        else
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert convex hull back to MeshDescription"));
            return false;
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during convex hull simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during convex hull simplification"));
        return false;
    }
}

bool UAuracronFoliageCollisionManager::ApplyBoundingBoxCollision(UStaticMesh* Mesh) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyBoundingBoxCollision);

    if (!Mesh)
    {
        return false;
    }

    // Create simple box collision based on mesh bounds
    FBox BoundingBox = Mesh->GetBoundingBox();
    FVector BoxExtent = BoundingBox.GetExtent();
    FVector BoxCenter = BoundingBox.GetCenter();

    // Create new mesh description with box geometry
    FMeshDescription* MeshDescription = Mesh->CreateMeshDescription(0);
    FStaticMeshAttributes Attributes(*MeshDescription);

    // Generate box vertices
    TArray<FVector> BoxVertices = {
        BoxCenter + FVector(-BoxExtent.X, -BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X, -BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X,  BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X,  BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X, -BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X, -BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X,  BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X,  BoxExtent.Y,  BoxExtent.Z)
    };

    // Generate box triangles (12 triangles for 6 faces)
    TArray<int32> BoxIndices = {
        0,1,2, 0,2,3, // Bottom face
        4,7,6, 4,6,5, // Top face
        0,4,5, 0,5,1, // Front face
        2,6,7, 2,7,3, // Back face
        0,3,7, 0,7,4, // Left face
        1,5,6, 1,6,2  // Right face
    };

    // Build mesh description with box geometry
    BuildBoxMeshDescription(MeshDescription, BoxVertices, BoxIndices);

    return true;
}

bool UAuracronFoliageCollisionManager::ApplyBoundingBoxSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyBoundingBoxSimplification);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for bounding box simplification"));
        return false;
    }

    try
    {
        // Generate bounding box collision mesh using UE5.6 APIs
        FStaticMeshAttributes Attributes(*MeshDescription);
        TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();

        if (MeshDescription->Vertices().Num() == 0)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("No vertices found in mesh for bounding box calculation"));
            return false;
        }

        // Calculate accurate bounding box
        FBox BoundingBox(ForceInit);
        for (const FVertexID VertexID : MeshDescription->Vertices().GetElementIDs())
        {
            BoundingBox += FVector(VertexPositions[VertexID]);
        }

        if (!BoundingBox.IsValid)
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid bounding box calculated from mesh vertices"));
            return false;
        }

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Calculated bounding box: Min=%s, Max=%s"), 
                                    *BoundingBox.Min.ToString(), *BoundingBox.Max.ToString());

        // Apply reduction percentage to bounding box size if specified
        if (ReductionPercentage > 0.0f && ReductionPercentage < 1.0f)
        {
            FVector Center = BoundingBox.GetCenter();
            FVector Extent = BoundingBox.GetExtent() * (1.0f - ReductionPercentage);
            BoundingBox = FBox(Center - Extent, Center + Extent);
            
            AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Applied %.1f%% reduction to bounding box"), ReductionPercentage * 100.0f);
        }

        // Clear existing mesh data
        MeshDescription->Empty();

        // Create box mesh using UE5.6 MeshDescription APIs
        FStaticMeshAttributes NewAttributes(*MeshDescription);
        TVertexAttributesRef<FVector3f> NewVertexPositions = NewAttributes.GetVertexPositions();
        TVertexInstanceAttributesRef<FVector3f> VertexInstanceNormals = NewAttributes.GetVertexInstanceNormals();
        TVertexInstanceAttributesRef<FVector3f> VertexInstanceTangents = NewAttributes.GetVertexInstanceTangents();
        TVertexInstanceAttributesRef<float> VertexInstanceBinormalSigns = NewAttributes.GetVertexInstanceBinormalSigns();
        TVertexInstanceAttributesRef<FVector2f> VertexInstanceUVs = NewAttributes.GetVertexInstanceUVs();
        TPolygonGroupAttributesRef<FName> PolygonGroupImportedMaterialSlotNames = NewAttributes.GetPolygonGroupMaterialSlotNames();

        // Ensure we have UV channel 0
        if (VertexInstanceUVs.GetNumChannels() == 0)
        {
            VertexInstanceUVs.SetNumChannels(1);
        }

        // Create polygon group for the box
        FPolygonGroupID PolygonGroupID = MeshDescription->CreatePolygonGroup();
        PolygonGroupImportedMaterialSlotNames[PolygonGroupID] = FName(TEXT("BoxMaterial"));

        // Define box vertices (8 corners)
        TArray<FVertexID> BoxVertices;
        BoxVertices.Reserve(8);
        
        FVector Min = BoundingBox.Min;
        FVector Max = BoundingBox.Max;
        
        // Create vertices in a specific order for proper winding
        BoxVertices.Add(MeshDescription->CreateVertex()); // 0: Min.X, Min.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 1: Max.X, Min.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 2: Max.X, Max.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 3: Min.X, Max.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 4: Min.X, Min.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 5: Max.X, Min.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 6: Max.X, Max.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 7: Min.X, Max.Y, Max.Z

        // Set vertex positions
        NewVertexPositions[BoxVertices[0]] = FVector3f(Min.X, Min.Y, Min.Z);
        NewVertexPositions[BoxVertices[1]] = FVector3f(Max.X, Min.Y, Min.Z);
        NewVertexPositions[BoxVertices[2]] = FVector3f(Max.X, Max.Y, Min.Z);
        NewVertexPositions[BoxVertices[3]] = FVector3f(Min.X, Max.Y, Min.Z);
        NewVertexPositions[BoxVertices[4]] = FVector3f(Min.X, Min.Y, Max.Z);
        NewVertexPositions[BoxVertices[5]] = FVector3f(Max.X, Min.Y, Max.Z);
        NewVertexPositions[BoxVertices[6]] = FVector3f(Max.X, Max.Y, Max.Z);
        NewVertexPositions[BoxVertices[7]] = FVector3f(Min.X, Max.Y, Max.Z);

        // Define box faces with proper winding order (counter-clockwise when viewed from outside)
        struct FBoxFace
        {
            int32 V0, V1, V2, V3;
            FVector3f Normal;
            TArray<FVector2f> UVs;
        };

        TArray<FBoxFace> BoxFaces = {
            // Bottom face (Z = Min.Z)
            {0, 2, 1, 3, FVector3f(0, 0, -1), {{0, 0}, {1, 1}, {1, 0}, {0, 1}}},
            // Top face (Z = Max.Z)
            {4, 5, 6, 7, FVector3f(0, 0, 1), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}},
            // Front face (Y = Min.Y)
            {0, 1, 5, 4, FVector3f(0, -1, 0), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}},
            // Back face (Y = Max.Y)
            {2, 7, 6, 3, FVector3f(0, 1, 0), {{1, 0}, {0, 1}, {1, 1}, {0, 0}}},
            // Left face (X = Min.X)
            {0, 4, 7, 3, FVector3f(-1, 0, 0), {{1, 0}, {1, 1}, {0, 1}, {0, 0}}},
            // Right face (X = Max.X)
            {1, 2, 6, 5, FVector3f(1, 0, 0), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}}
        };

        // Create polygons for each face
        for (const FBoxFace& Face : BoxFaces)
        {
            // Create vertex instances
            TArray<FVertexInstanceID> VertexInstances;
            VertexInstances.Reserve(4);
            
            for (int32 i = 0; i < 4; ++i)
            {
                int32 VertexIndex = (i == 0) ? Face.V0 : (i == 1) ? Face.V1 : (i == 2) ? Face.V2 : Face.V3;
                FVertexInstanceID VertexInstanceID = MeshDescription->CreateVertexInstance(BoxVertices[VertexIndex]);
                
                VertexInstanceNormals[VertexInstanceID] = Face.Normal;
                VertexInstanceTangents[VertexInstanceID] = FVector3f(1, 0, 0); // Default tangent
                VertexInstanceBinormalSigns[VertexInstanceID] = 1.0f;
                VertexInstanceUVs.Set(VertexInstanceID, 0, Face.UVs[i]);
                
                VertexInstances.Add(VertexInstanceID);
            }

            // Create triangles for the quad (two triangles per face)
            TArray<FVertexInstanceID> Triangle1 = {VertexInstances[0], VertexInstances[1], VertexInstances[2]};
            TArray<FVertexInstanceID> Triangle2 = {VertexInstances[0], VertexInstances[2], VertexInstances[3]};
            
            MeshDescription->CreatePolygon(PolygonGroupID, Triangle1);
            MeshDescription->CreatePolygon(PolygonGroupID, Triangle2);
        }

        // Validate the created mesh
        if (MeshDescription->Vertices().Num() != 8 || MeshDescription->Triangles().Num() != 12)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Box mesh creation resulted in unexpected geometry: %d vertices, %d triangles"),
                                        MeshDescription->Vertices().Num(), MeshDescription->Triangles().Num());
        }

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Bounding box simplification completed: Created box mesh with 8 vertices and 12 triangles"));
        return true;
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during bounding box simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during bounding box simplification"));
        return false;
    }
}
