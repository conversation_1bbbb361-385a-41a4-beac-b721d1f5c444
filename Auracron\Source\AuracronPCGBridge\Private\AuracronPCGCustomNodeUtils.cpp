// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node Utilities Implementation
// Bridge 2.13: PCG Framework - Custom Node Creation

#include "AuracronPCGCustomNodeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"

// =============================================================================
// CUSTOM NODE UTILITIES IMPLEMENTATION
// =============================================================================

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeUtils::CreateBasicTemplate(const FString& NodeName, EAuracronPCGCustomNodeTemplateType TemplateType)
{
    FAuracronPCGCustomNodeTemplate Template;
    
    Template.TemplateName = NodeName;
    Template.DisplayName = NodeName;
    Template.Description = FString::Printf(TEXT("Custom %s node"), *NodeName);
    Template.TemplateType = TemplateType;
    
    // Set category based on template type
    switch (TemplateType)
    {
        case EAuracronPCGCustomNodeTemplateType::Generator:
            Template.Category = EAuracronPCGNodeCategory::Generator;
            break;
        case EAuracronPCGCustomNodeTemplateType::Modifier:
            Template.Category = EAuracronPCGNodeCategory::Modifier;
            break;
        case EAuracronPCGCustomNodeTemplateType::Filter:
            Template.Category = EAuracronPCGNodeCategory::Filter;
            break;
        case EAuracronPCGCustomNodeTemplateType::Sampler:
            Template.Category = EAuracronPCGNodeCategory::Sampler;
            break;
        case EAuracronPCGCustomNodeTemplateType::Debug:
            Template.Category = EAuracronPCGNodeCategory::Debug;
            break;
        default:
            Template.Category = EAuracronPCGNodeCategory::Modifier;
            break;
    }
    
    // Add default input and output pins
    FAuracronPCGCustomPinDescriptor InputPin = CreateInputPin(TEXT("Input"), EPCGDataType::Point);
    Template.InputPins.Add(InputPin);
    
    FAuracronPCGCustomPinDescriptor OutputPin = CreateOutputPin(TEXT("Output"), EPCGDataType::Point);
    Template.OutputPins.Add(OutputPin);
    
    return Template;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateFloatParameter(const FString& Name, float DefaultValue, float MinValue, float MaxValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Float;
    Parameter.DefaultValue = FString::SanitizeFloat(DefaultValue);
    Parameter.bHasMinValue = true;
    Parameter.MinValue = MinValue;
    Parameter.bHasMaxValue = true;
    Parameter.MaxValue = MaxValue;
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateIntParameter(const FString& Name, int32 DefaultValue, int32 MinValue, int32 MaxValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Integer;
    Parameter.DefaultValue = FString::FromInt(DefaultValue);
    Parameter.bHasMinValue = true;
    Parameter.MinValue = static_cast<float>(MinValue);
    Parameter.bHasMaxValue = true;
    Parameter.MaxValue = static_cast<float>(MaxValue);
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateBoolParameter(const FString& Name, bool DefaultValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Boolean;
    Parameter.DefaultValue = DefaultValue ? TEXT("true") : TEXT("false");
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateStringParameter(const FString& Name, const FString& DefaultValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::String;
    Parameter.DefaultValue = DefaultValue;
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateVectorParameter(const FString& Name, const FVector& DefaultValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Vector;
    Parameter.DefaultValue = DefaultValue.ToString();
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateColorParameter(const FString& Name, const FLinearColor& DefaultValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Color;
    Parameter.DefaultValue = DefaultValue.ToString();
    
    return Parameter;
}

FAuracronPCGCustomParameterDescriptor UAuracronPCGCustomNodeUtils::CreateEnumParameter(const FString& Name, const TArray<FString>& EnumValues, const FString& DefaultValue)
{
    FAuracronPCGCustomParameterDescriptor Parameter;
    
    Parameter.ParameterName = Name;
    Parameter.DisplayName = Name;
    Parameter.ParameterType = EAuracronPCGCustomParameterType::Enum;
    Parameter.DefaultValue = DefaultValue;
    Parameter.EnumValues = EnumValues;
    
    return Parameter;
}

FAuracronPCGCustomPinDescriptor UAuracronPCGCustomNodeUtils::CreateInputPin(const FString& Name, EPCGDataType DataType, bool bRequired)
{
    FAuracronPCGCustomPinDescriptor Pin;
    
    Pin.PinName = Name;
    Pin.DisplayName = Name;
    Pin.AllowedTypes = DataType;
    Pin.bIsInput = true;
    Pin.bIsRequired = bRequired;
    Pin.bAllowMultipleConnections = false;
    
    return Pin;
}

FAuracronPCGCustomPinDescriptor UAuracronPCGCustomNodeUtils::CreateOutputPin(const FString& Name, EPCGDataType DataType)
{
    FAuracronPCGCustomPinDescriptor Pin;
    
    Pin.PinName = Name;
    Pin.DisplayName = Name;
    Pin.AllowedTypes = DataType;
    Pin.bIsInput = false;
    Pin.bIsRequired = false;
    Pin.bAllowMultipleConnections = true;
    
    return Pin;
}

bool UAuracronPCGCustomNodeUtils::ValidateParameterName(const FString& ParameterName)
{
    if (ParameterName.IsEmpty())
    {
        return false;
    }
    
    // Check for valid identifier characters
    for (int32 i = 0; i < ParameterName.Len(); i++)
    {
        TCHAR Char = ParameterName[i];
        if (i == 0)
        {
            // First character must be letter or underscore
            if (!FChar::IsAlpha(Char) && Char != '_')
            {
                return false;
            }
        }
        else
        {
            // Subsequent characters can be letters, digits, or underscores
            if (!FChar::IsAlnum(Char) && Char != '_')
            {
                return false;
            }
        }
    }
    
    return true;
}

bool UAuracronPCGCustomNodeUtils::ValidatePinName(const FString& PinName)
{
    return ValidateParameterName(PinName); // Same validation rules
}

bool UAuracronPCGCustomNodeUtils::ValidateNodeName(const FString& NodeName)
{
    return ValidateParameterName(NodeName); // Same validation rules
}

FString UAuracronPCGCustomNodeUtils::SanitizeParameterName(const FString& ParameterName)
{
    FString SanitizedName = ParameterName;
    
    // Replace invalid characters with underscores
    for (int32 i = 0; i < SanitizedName.Len(); i++)
    {
        TCHAR& Char = SanitizedName[i];
        if (i == 0)
        {
            if (!FChar::IsAlpha(Char) && Char != '_')
            {
                Char = '_';
            }
        }
        else
        {
            if (!FChar::IsAlnum(Char) && Char != '_')
            {
                Char = '_';
            }
        }
    }
    
    // Ensure name is not empty
    if (SanitizedName.IsEmpty())
    {
        SanitizedName = TEXT("Parameter");
    }
    
    return SanitizedName;
}

FString UAuracronPCGCustomNodeUtils::SanitizePinName(const FString& PinName)
{
    return SanitizeParameterName(PinName); // Same sanitization rules
}

FString UAuracronPCGCustomNodeUtils::SanitizeNodeName(const FString& NodeName)
{
    return SanitizeParameterName(NodeName); // Same sanitization rules
}

FString UAuracronPCGCustomNodeUtils::ParameterTypeToString(EAuracronPCGCustomParameterType ParameterType)
{
    switch (ParameterType)
    {
        case EAuracronPCGCustomParameterType::Boolean:
            return TEXT("Boolean");
        case EAuracronPCGCustomParameterType::Integer:
            return TEXT("Integer");
        case EAuracronPCGCustomParameterType::Float:
            return TEXT("Float");
        case EAuracronPCGCustomParameterType::String:
            return TEXT("String");
        case EAuracronPCGCustomParameterType::Vector:
            return TEXT("Vector");
        case EAuracronPCGCustomParameterType::Rotator:
            return TEXT("Rotator");
        case EAuracronPCGCustomParameterType::Transform:
            return TEXT("Transform");
        case EAuracronPCGCustomParameterType::Color:
            return TEXT("Color");
        case EAuracronPCGCustomParameterType::Object:
            return TEXT("Object");
        case EAuracronPCGCustomParameterType::Class:
            return TEXT("Class");
        case EAuracronPCGCustomParameterType::Enum:
            return TEXT("Enum");
        case EAuracronPCGCustomParameterType::Struct:
            return TEXT("Struct");
        case EAuracronPCGCustomParameterType::Array:
            return TEXT("Array");
        case EAuracronPCGCustomParameterType::Map:
            return TEXT("Map");
        case EAuracronPCGCustomParameterType::Set:
            return TEXT("Set");
        case EAuracronPCGCustomParameterType::Delegate:
            return TEXT("Delegate");
        case EAuracronPCGCustomParameterType::Custom:
            return TEXT("Custom");
        default:
            return TEXT("Unknown");
    }
}

EAuracronPCGCustomParameterType UAuracronPCGCustomNodeUtils::StringToParameterType(const FString& TypeString)
{
    if (TypeString == TEXT("Boolean"))
        return EAuracronPCGCustomParameterType::Boolean;
    else if (TypeString == TEXT("Integer"))
        return EAuracronPCGCustomParameterType::Integer;
    else if (TypeString == TEXT("Float"))
        return EAuracronPCGCustomParameterType::Float;
    else if (TypeString == TEXT("String"))
        return EAuracronPCGCustomParameterType::String;
    else if (TypeString == TEXT("Vector"))
        return EAuracronPCGCustomParameterType::Vector;
    else if (TypeString == TEXT("Rotator"))
        return EAuracronPCGCustomParameterType::Rotator;
    else if (TypeString == TEXT("Transform"))
        return EAuracronPCGCustomParameterType::Transform;
    else if (TypeString == TEXT("Color"))
        return EAuracronPCGCustomParameterType::Color;
    else if (TypeString == TEXT("Object"))
        return EAuracronPCGCustomParameterType::Object;
    else if (TypeString == TEXT("Class"))
        return EAuracronPCGCustomParameterType::Class;
    else if (TypeString == TEXT("Enum"))
        return EAuracronPCGCustomParameterType::Enum;
    else if (TypeString == TEXT("Struct"))
        return EAuracronPCGCustomParameterType::Struct;
    else if (TypeString == TEXT("Array"))
        return EAuracronPCGCustomParameterType::Array;
    else if (TypeString == TEXT("Map"))
        return EAuracronPCGCustomParameterType::Map;
    else if (TypeString == TEXT("Set"))
        return EAuracronPCGCustomParameterType::Set;
    else if (TypeString == TEXT("Delegate"))
        return EAuracronPCGCustomParameterType::Delegate;
    else if (TypeString == TEXT("Custom"))
        return EAuracronPCGCustomParameterType::Custom;
    else
        return EAuracronPCGCustomParameterType::Float; // Default
}

FString UAuracronPCGCustomNodeUtils::ExecutionModeToString(EAuracronPCGCustomNodeExecutionMode ExecutionMode)
{
    switch (ExecutionMode)
    {
        case EAuracronPCGCustomNodeExecutionMode::Synchronous:
            return TEXT("Synchronous");
        case EAuracronPCGCustomNodeExecutionMode::Asynchronous:
            return TEXT("Asynchronous");
        case EAuracronPCGCustomNodeExecutionMode::Threaded:
            return TEXT("Threaded");
        case EAuracronPCGCustomNodeExecutionMode::GPU:
            return TEXT("GPU");
        case EAuracronPCGCustomNodeExecutionMode::Distributed:
            return TEXT("Distributed");
        case EAuracronPCGCustomNodeExecutionMode::Custom:
            return TEXT("Custom");
        default:
            return TEXT("Unknown");
    }
}

EAuracronPCGCustomNodeExecutionMode UAuracronPCGCustomNodeUtils::StringToExecutionMode(const FString& ModeString)
{
    if (ModeString == TEXT("Synchronous"))
        return EAuracronPCGCustomNodeExecutionMode::Synchronous;
    else if (ModeString == TEXT("Asynchronous"))
        return EAuracronPCGCustomNodeExecutionMode::Asynchronous;
    else if (ModeString == TEXT("Threaded"))
        return EAuracronPCGCustomNodeExecutionMode::Threaded;
    else if (ModeString == TEXT("GPU"))
        return EAuracronPCGCustomNodeExecutionMode::GPU;
    else if (ModeString == TEXT("Distributed"))
        return EAuracronPCGCustomNodeExecutionMode::Distributed;
    else if (ModeString == TEXT("Custom"))
        return EAuracronPCGCustomNodeExecutionMode::Custom;
    else
        return EAuracronPCGCustomNodeExecutionMode::Synchronous; // Default
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeUtils::GenerateTemplateFromBlueprint(UBlueprint* Blueprint)
{
    FAuracronPCGCustomNodeTemplate Template;
    
    if (!Blueprint)
    {
        return Template;
    }
    
    // Extract information from blueprint
    Template.TemplateName = Blueprint->GetName();
    Template.DisplayName = Blueprint->GetName();
    Template.Description = FString::Printf(TEXT("Generated from blueprint %s"), *Blueprint->GetName());
    Template.BlueprintImplementation = Blueprint;
    
    // Real blueprint analysis using UE5.6 blueprint reflection system
    if (UBlueprintGeneratedClass* GeneratedClass = Cast<UBlueprintGeneratedClass>(Blueprint->GeneratedClass))
    {
        // Analyze blueprint properties for parameters
        for (TFieldIterator<FProperty> PropIt(GeneratedClass); PropIt; ++PropIt)
        {
            FProperty* Property = *PropIt;
            if (Property && Property->HasAnyPropertyFlags(CPF_Edit | CPF_BlueprintVisible))
            {
                FAuracronPCGCustomParameterDescriptor ParamDesc;
                ParamDesc.ParameterName = Property->GetName();
                ParamDesc.DisplayName = Property->GetDisplayNameText().ToString();
                
                // Determine parameter type from property
                if (Property->IsA<FFloatProperty>())
                {
                    ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Float;
                    ParamDesc.DefaultFloatValue = 0.0f;
                }
                else if (Property->IsA<FIntProperty>())
                {
                    ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Int;
                    ParamDesc.DefaultIntValue = 0;
                }
                else if (Property->IsA<FBoolProperty>())
                {
                    ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Bool;
                    ParamDesc.DefaultBoolValue = false;
                }
                else if (Property->IsA<FStrProperty>())
                {
                    ParamDesc.ParameterType = EAuracronPCGCustomParameterType::String;
                    ParamDesc.DefaultStringValue = TEXT("");
                }
                
                Template.Parameters.Add(ParamDesc);
            }
        }
        
        // Set template type based on class hierarchy
        if (GeneratedClass->IsChildOf(UPCGSettings::StaticClass()))
        {
            Template.TemplateType = EAuracronPCGCustomNodeTemplateType::Modifier;
            Template.Category = EAuracronPCGNodeCategory::Modifier;
        }
    }

    return Template;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeUtils::GenerateTemplateFromClass(UClass* NodeClass)
{
    FAuracronPCGCustomNodeTemplate Template;
    
    if (!NodeClass)
    {
        return Template;
    }
    
    // Extract information from class
    Template.TemplateName = NodeClass->GetName();
    Template.DisplayName = NodeClass->GetName();
    Template.Description = FString::Printf(TEXT("Generated from class %s"), *NodeClass->GetName());
    Template.NativeClassName = NodeClass->GetName();
    
    // Real class analysis using UE5.6 reflection system
    // Analyze class properties for parameters
    for (TFieldIterator<FProperty> PropIt(NodeClass); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        if (Property && Property->HasAnyPropertyFlags(CPF_Edit | CPF_BlueprintVisible))
        {
            FAuracronPCGCustomParameterDescriptor ParamDesc;
            ParamDesc.ParameterName = Property->GetName();
            ParamDesc.DisplayName = Property->GetDisplayNameText().ToString();
            
            // Determine parameter type from property
            if (Property->IsA<FFloatProperty>())
            {
                ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Float;
                ParamDesc.DefaultFloatValue = 0.0f;
            }
            else if (Property->IsA<FIntProperty>())
            {
                ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Int;
                ParamDesc.DefaultIntValue = 0;
            }
            else if (Property->IsA<FBoolProperty>())
            {
                ParamDesc.ParameterType = EAuracronPCGCustomParameterType::Bool;
                ParamDesc.DefaultBoolValue = false;
            }
            else if (Property->IsA<FStrProperty>())
            {
                ParamDesc.ParameterType = EAuracronPCGCustomParameterType::String;
                ParamDesc.DefaultStringValue = TEXT("");
            }
            
            Template.Parameters.Add(ParamDesc);
        }
    }
    
    // Analyze class functions for execution methods
    for (TFieldIterator<UFunction> FuncIt(NodeClass); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        if (Function && Function->GetName() == TEXT("ExecuteInternal"))
        {
            Template.ExecutionMode = EAuracronPCGCustomNodeExecutionMode::Synchronous;
        }
        else if (Function && Function->GetName() == TEXT("ExecuteInternalAsync"))
        {
            Template.ExecutionMode = EAuracronPCGCustomNodeExecutionMode::Asynchronous;
        }
    }
    
    // Set template type based on class hierarchy
    if (NodeClass->IsChildOf(UPCGSettings::StaticClass()))
    {
        Template.TemplateType = EAuracronPCGCustomNodeTemplateType::Modifier;
        Template.Category = EAuracronPCGNodeCategory::Modifier;
    }

    return Template;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeUtils::GenerateTemplatesFromDirectory(const FString& Directory)
{
    TArray<FAuracronPCGCustomNodeTemplate> Templates;
    
    // Find all template files in directory
    TArray<FString> TemplateFiles = UAuracronPCGCustomNodeFactory::GetAvailableTemplateFiles(Directory);
    
    for (const FString& TemplateFile : TemplateFiles)
    {
        FString FullPath = Directory / TemplateFile;
        FAuracronPCGCustomNodeTemplate Template = UAuracronPCGCustomNodeFactory::LoadTemplateFromFile(FullPath);
        
        if (!Template.TemplateName.IsEmpty())
        {
            Templates.Add(Template);
        }
    }
    
    return Templates;
}
