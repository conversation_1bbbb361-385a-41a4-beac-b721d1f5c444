// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronNaniteBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronNaniteBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONNANITEBRIDGE_API UClass* Z_Construct_UClass_UAuracronNaniteBridge();
AURACRONNANITEBRIDGE_API UClass* Z_Construct_UClass_UAuracronNaniteBridge_NoRegister();
AURACRONNANITEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType();
AURACRONNANITEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality();
AURACRONNANITEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature();
AURACRONNANITEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature();
AURACRONNANITEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGeometryInstance();
AURACRONNANITEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNaniteConfiguration();
AURACRONNANITEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
PROCEDURALMESHCOMPONENT_API UClass* Z_Construct_UClass_UProceduralMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronNaniteBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronNaniteGeometryType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronNaniteGeometryType;
static UEnum* EAuracronNaniteGeometryType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType, (UObject*)Z_Construct_UPackage__Script_AuracronNaniteBridge(), TEXT("EAuracronNaniteGeometryType"));
	}
	return Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.OuterSingleton;
}
template<> AURACRONNANITEBRIDGE_API UEnum* StaticEnum<EAuracronNaniteGeometryType>()
{
	return EAuracronNaniteGeometryType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Architecture.DisplayName", "Architecture" },
		{ "Architecture.Name", "EAuracronNaniteGeometryType::Architecture" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de geometria Nanite\n */" },
#endif
		{ "Decoration.DisplayName", "Decoration" },
		{ "Decoration.Name", "EAuracronNaniteGeometryType::Decoration" },
		{ "Destructible.DisplayName", "Destructible" },
		{ "Destructible.Name", "EAuracronNaniteGeometryType::Destructible" },
		{ "Foliage.DisplayName", "Foliage" },
		{ "Foliage.Name", "EAuracronNaniteGeometryType::Foliage" },
		{ "InstancedMesh.DisplayName", "Instanced Mesh" },
		{ "InstancedMesh.Name", "EAuracronNaniteGeometryType::InstancedMesh" },
		{ "LandscapeSpline.DisplayName", "Landscape Spline" },
		{ "LandscapeSpline.Name", "EAuracronNaniteGeometryType::LandscapeSpline" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronNaniteGeometryType::None" },
		{ "ProceduralMesh.DisplayName", "Procedural Mesh" },
		{ "ProceduralMesh.Name", "EAuracronNaniteGeometryType::ProceduralMesh" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EAuracronNaniteGeometryType::StaticMesh" },
		{ "Terrain.DisplayName", "Terrain" },
		{ "Terrain.Name", "EAuracronNaniteGeometryType::Terrain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de geometria Nanite" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronNaniteGeometryType::None", (int64)EAuracronNaniteGeometryType::None },
		{ "EAuracronNaniteGeometryType::StaticMesh", (int64)EAuracronNaniteGeometryType::StaticMesh },
		{ "EAuracronNaniteGeometryType::InstancedMesh", (int64)EAuracronNaniteGeometryType::InstancedMesh },
		{ "EAuracronNaniteGeometryType::ProceduralMesh", (int64)EAuracronNaniteGeometryType::ProceduralMesh },
		{ "EAuracronNaniteGeometryType::LandscapeSpline", (int64)EAuracronNaniteGeometryType::LandscapeSpline },
		{ "EAuracronNaniteGeometryType::Foliage", (int64)EAuracronNaniteGeometryType::Foliage },
		{ "EAuracronNaniteGeometryType::Architecture", (int64)EAuracronNaniteGeometryType::Architecture },
		{ "EAuracronNaniteGeometryType::Decoration", (int64)EAuracronNaniteGeometryType::Decoration },
		{ "EAuracronNaniteGeometryType::Terrain", (int64)EAuracronNaniteGeometryType::Terrain },
		{ "EAuracronNaniteGeometryType::Destructible", (int64)EAuracronNaniteGeometryType::Destructible },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
	nullptr,
	"EAuracronNaniteGeometryType",
	"EAuracronNaniteGeometryType",
	Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType()
{
	if (!Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.InnerSingleton, Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronNaniteGeometryType.InnerSingleton;
}
// ********** End Enum EAuracronNaniteGeometryType *************************************************

// ********** Begin Enum EAuracronNaniteQuality ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronNaniteQuality;
static UEnum* EAuracronNaniteQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronNaniteQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronNaniteQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality, (UObject*)Z_Construct_UPackage__Script_AuracronNaniteBridge(), TEXT("EAuracronNaniteQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronNaniteQuality.OuterSingleton;
}
template<> AURACRONNANITEBRIDGE_API UEnum* StaticEnum<EAuracronNaniteQuality>()
{
	return EAuracronNaniteQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EAuracronNaniteQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de Nanite\n */" },
#endif
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EAuracronNaniteQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EAuracronNaniteQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EAuracronNaniteQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de Nanite" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EAuracronNaniteQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronNaniteQuality::Low", (int64)EAuracronNaniteQuality::Low },
		{ "EAuracronNaniteQuality::Medium", (int64)EAuracronNaniteQuality::Medium },
		{ "EAuracronNaniteQuality::High", (int64)EAuracronNaniteQuality::High },
		{ "EAuracronNaniteQuality::Ultra", (int64)EAuracronNaniteQuality::Ultra },
		{ "EAuracronNaniteQuality::Cinematic", (int64)EAuracronNaniteQuality::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
	nullptr,
	"EAuracronNaniteQuality",
	"EAuracronNaniteQuality",
	Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronNaniteQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronNaniteQuality.InnerSingleton, Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronNaniteQuality.InnerSingleton;
}
// ********** End Enum EAuracronNaniteQuality ******************************************************

// ********** Begin ScriptStruct FAuracronNaniteConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration;
class UScriptStruct* FAuracronNaniteConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronNaniteConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronNaniteBridge(), TEXT("AuracronNaniteConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Nanite\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNanite_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteQuality_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade de Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade de Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRenderDistance_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAggressiveCulling_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar culling agressivo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar culling agressivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODBias_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "-5.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bias de LOD */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bias de LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMeshStreaming_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar streaming de mesh */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar streaming de mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPoolSizeMB_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "2048" },
		{ "ClampMin", "64" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do pool de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do pool de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMeshCompression_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar compress\xc3\x83\xc2\xa3o de mesh */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar compress\xc3\x83\xc2\xa3o de mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionLevel_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de compress\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de compress\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFallbackForMobile_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar fallback para mobile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar fallback para mobile" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FallbackMesh_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh de fallback */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh de fallback" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAutoInstancing_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar instancing autom\xc3\x83\xc2\xa1tico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar instancing autom\xc3\x83\xc2\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancingThreshold_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "2" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold para instancing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold para instancing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseOcclusionCulling_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar occlusion culling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar occlusion culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFrustumCulling_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar frustum culling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar frustum culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceCulling_MetaData[] = {
		{ "Category", "Nanite Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar distance culling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar distance culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Nanite Configuration" },
		{ "ClampMax", "100000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia de culling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia de culling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUseNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNanite;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NaniteQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NaniteQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRenderDistance;
	static void NewProp_bUseAggressiveCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAggressiveCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODBias;
	static void NewProp_bUseMeshStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMeshStreaming;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPoolSizeMB;
	static void NewProp_bUseMeshCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMeshCompression;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompressionLevel;
	static void NewProp_bUseFallbackForMobile_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFallbackForMobile;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FallbackMesh;
	static void NewProp_bUseAutoInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAutoInstancing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstancingThreshold;
	static void NewProp_bUseOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseOcclusionCulling;
	static void NewProp_bUseFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFrustumCulling;
	static void NewProp_bUseDistanceCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronNaniteConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseNanite_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseNanite = { "bUseNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNanite_MetaData), NewProp_bUseNanite_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_NaniteQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_NaniteQuality = { "NaniteQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, NaniteQuality), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteQuality_MetaData), NewProp_NaniteQuality_MetaData) }; // 3779901557
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_MaxRenderDistance = { "MaxRenderDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, MaxRenderDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRenderDistance_MetaData), NewProp_MaxRenderDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAggressiveCulling_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseAggressiveCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAggressiveCulling = { "bUseAggressiveCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAggressiveCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAggressiveCulling_MetaData), NewProp_bUseAggressiveCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_LODBias = { "LODBias", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, LODBias), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODBias_MetaData), NewProp_LODBias_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshStreaming_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseMeshStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshStreaming = { "bUseMeshStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMeshStreaming_MetaData), NewProp_bUseMeshStreaming_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_StreamingPoolSizeMB = { "StreamingPoolSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, StreamingPoolSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPoolSizeMB_MetaData), NewProp_StreamingPoolSizeMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshCompression_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseMeshCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshCompression = { "bUseMeshCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMeshCompression_MetaData), NewProp_bUseMeshCompression_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_CompressionLevel = { "CompressionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, CompressionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionLevel_MetaData), NewProp_CompressionLevel_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFallbackForMobile_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseFallbackForMobile = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFallbackForMobile = { "bUseFallbackForMobile", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFallbackForMobile_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFallbackForMobile_MetaData), NewProp_bUseFallbackForMobile_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_FallbackMesh = { "FallbackMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, FallbackMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FallbackMesh_MetaData), NewProp_FallbackMesh_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAutoInstancing_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseAutoInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAutoInstancing = { "bUseAutoInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAutoInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAutoInstancing_MetaData), NewProp_bUseAutoInstancing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_InstancingThreshold = { "InstancingThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, InstancingThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancingThreshold_MetaData), NewProp_InstancingThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseOcclusionCulling_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseOcclusionCulling = { "bUseOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseOcclusionCulling_MetaData), NewProp_bUseOcclusionCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFrustumCulling_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFrustumCulling = { "bUseFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFrustumCulling_MetaData), NewProp_bUseFrustumCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseDistanceCulling_SetBit(void* Obj)
{
	((FAuracronNaniteConfiguration*)Obj)->bUseDistanceCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseDistanceCulling = { "bUseDistanceCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNaniteConfiguration), &Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseDistanceCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceCulling_MetaData), NewProp_bUseDistanceCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNaniteConfiguration, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_NaniteQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_NaniteQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_MaxRenderDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAggressiveCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_LODBias,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_StreamingPoolSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseMeshCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_CompressionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFallbackForMobile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_FallbackMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseAutoInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_InstancingThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_bUseDistanceCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewProp_CullingDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
	nullptr,
	&NewStructOps,
	"AuracronNaniteConfiguration",
	Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::PropPointers),
	sizeof(FAuracronNaniteConfiguration),
	alignof(FAuracronNaniteConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronNaniteConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronNaniteConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronRealmGeometryConfiguration *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration;
class UScriptStruct* FAuracronRealmGeometryConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronNaniteBridge(), TEXT("AuracronRealmGeometryConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de geometria de realm\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de geometria de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceRealmMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes da Plan\xc3\x83\xc2\xad""cie Radiante */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes da Plan\xc3\x83\xc2\xad""cie Radiante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyRealmMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes do Firmamento Zephyr */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes do Firmamento Zephyr" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UndergroundRealmMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes do Abismo Umbrio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes do Abismo Umbrio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes de portais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes de portais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalConnectorMeshes_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Meshes de conectores verticais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Meshes de conectores verticais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmGeometryDensity_MetaData[] = {
		{ "Category", "Realm Geometry" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de geometria por realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de geometria por realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseProceduralGeometry_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar geometria procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar geometria procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralSeed_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralComplexity_MetaData[] = {
		{ "Category", "Realm Geometry" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Complexidade da geometria procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Complexidade da geometria procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMeshVariations_MetaData[] = {
		{ "Category", "Realm Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de mesh */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshVariations_MetaData[] = {
		{ "Category", "Realm Geometry" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es por mesh */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es por mesh" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SurfaceRealmMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SurfaceRealmMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SkyRealmMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SkyRealmMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UndergroundRealmMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UndergroundRealmMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TransitionMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PortalMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PortalMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_VerticalConnectorMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VerticalConnectorMeshes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RealmGeometryDensity_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmGeometryDensity;
	static void NewProp_bUseProceduralGeometry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseProceduralGeometry;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProceduralSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProceduralComplexity;
	static void NewProp_bUseMeshVariations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMeshVariations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshVariations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmGeometryConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SurfaceRealmMeshes_Inner = { "SurfaceRealmMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SurfaceRealmMeshes = { "SurfaceRealmMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, SurfaceRealmMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceRealmMeshes_MetaData), NewProp_SurfaceRealmMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SkyRealmMeshes_Inner = { "SkyRealmMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SkyRealmMeshes = { "SkyRealmMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, SkyRealmMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyRealmMeshes_MetaData), NewProp_SkyRealmMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_UndergroundRealmMeshes_Inner = { "UndergroundRealmMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_UndergroundRealmMeshes = { "UndergroundRealmMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, UndergroundRealmMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UndergroundRealmMeshes_MetaData), NewProp_UndergroundRealmMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_TransitionMeshes_Inner = { "TransitionMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_TransitionMeshes = { "TransitionMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, TransitionMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionMeshes_MetaData), NewProp_TransitionMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_PortalMeshes_Inner = { "PortalMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_PortalMeshes = { "PortalMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, PortalMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalMeshes_MetaData), NewProp_PortalMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_VerticalConnectorMeshes_Inner = { "VerticalConnectorMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_VerticalConnectorMeshes = { "VerticalConnectorMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, VerticalConnectorMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalConnectorMeshes_MetaData), NewProp_VerticalConnectorMeshes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_RealmGeometryDensity_Inner = { "RealmGeometryDensity", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_RealmGeometryDensity = { "RealmGeometryDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, RealmGeometryDensity), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmGeometryDensity_MetaData), NewProp_RealmGeometryDensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseProceduralGeometry_SetBit(void* Obj)
{
	((FAuracronRealmGeometryConfiguration*)Obj)->bUseProceduralGeometry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseProceduralGeometry = { "bUseProceduralGeometry", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmGeometryConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseProceduralGeometry_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseProceduralGeometry_MetaData), NewProp_bUseProceduralGeometry_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_ProceduralSeed = { "ProceduralSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, ProceduralSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralSeed_MetaData), NewProp_ProceduralSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_ProceduralComplexity = { "ProceduralComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, ProceduralComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralComplexity_MetaData), NewProp_ProceduralComplexity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseMeshVariations_SetBit(void* Obj)
{
	((FAuracronRealmGeometryConfiguration*)Obj)->bUseMeshVariations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseMeshVariations = { "bUseMeshVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmGeometryConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseMeshVariations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMeshVariations_MetaData), NewProp_bUseMeshVariations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_MeshVariations = { "MeshVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmGeometryConfiguration, MeshVariations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshVariations_MetaData), NewProp_MeshVariations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SurfaceRealmMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SurfaceRealmMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SkyRealmMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_SkyRealmMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_UndergroundRealmMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_UndergroundRealmMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_TransitionMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_TransitionMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_PortalMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_PortalMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_VerticalConnectorMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_VerticalConnectorMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_RealmGeometryDensity_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_RealmGeometryDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseProceduralGeometry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_ProceduralSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_ProceduralComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_bUseMeshVariations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewProp_MeshVariations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmGeometryConfiguration",
	Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::PropPointers),
	sizeof(FAuracronRealmGeometryConfiguration),
	alignof(FAuracronRealmGeometryConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmGeometryConfiguration *********************************

// ********** Begin ScriptStruct FAuracronGeometryInstance *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance;
class UScriptStruct* FAuracronGeometryInstance::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGeometryInstance, (UObject*)Z_Construct_UPackage__Script_AuracronNaniteBridge(), TEXT("AuracronGeometryInstance"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para inst\xc3\x83\xc2\xa2ncia de geometria\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para inst\xc3\x83\xc2\xa2ncia de geometria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceID_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico da inst\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico da inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mesh_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh da inst\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh da inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryType_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de geometria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de geometria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transform da inst\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transform da inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOverride_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material override */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material override" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNanite_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseInstancing_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar instancing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar instancing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceCount_MetaData[] = {
		{ "Category", "Geometry Instance" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de inst\xc3\x83\xc2\xa2ncias */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de inst\xc3\x83\xc2\xa2ncias" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceCulling_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar culling por dist\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar culling por dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Geometry Instance" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia de culling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia de culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderPriority_MetaData[] = {
		{ "Category", "Geometry Instance" },
		{ "ClampMax", "10" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmIndex_MetaData[] = {
		{ "Category", "Geometry Instance" },
		{ "ClampMax", "2" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm onde est\xc3\x83\xc2\xa1 localizada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm onde est\xc3\x83\xc2\xa1 localizada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTags_MetaData[] = {
		{ "Category", "Geometry Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags da inst\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags da inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceID;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FBytePropertyParams NewProp_GeometryType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GeometryType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOverride;
	static void NewProp_bUseNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNanite;
	static void NewProp_bUseInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseInstancing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceCount;
	static void NewProp_bUseDistanceCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderPriority;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGeometryInstance>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceID = { "InstanceID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, InstanceID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceID_MetaData), NewProp_InstanceID_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mesh_MetaData), NewProp_Mesh_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_GeometryType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_GeometryType = { "GeometryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, GeometryType), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteGeometryType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryType_MetaData), NewProp_GeometryType_MetaData) }; // 2793815717
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_MaterialOverride = { "MaterialOverride", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, MaterialOverride), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOverride_MetaData), NewProp_MaterialOverride_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseNanite_SetBit(void* Obj)
{
	((FAuracronGeometryInstance*)Obj)->bUseNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseNanite = { "bUseNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGeometryInstance), &Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNanite_MetaData), NewProp_bUseNanite_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseInstancing_SetBit(void* Obj)
{
	((FAuracronGeometryInstance*)Obj)->bUseInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseInstancing = { "bUseInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGeometryInstance), &Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseInstancing_MetaData), NewProp_bUseInstancing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceCount = { "InstanceCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, InstanceCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceCount_MetaData), NewProp_InstanceCount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseDistanceCulling_SetBit(void* Obj)
{
	((FAuracronGeometryInstance*)Obj)->bUseDistanceCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseDistanceCulling = { "bUseDistanceCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGeometryInstance), &Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseDistanceCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceCulling_MetaData), NewProp_bUseDistanceCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_RenderPriority = { "RenderPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, RenderPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderPriority_MetaData), NewProp_RenderPriority_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, RealmIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmIndex_MetaData), NewProp_RealmIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceTags = { "InstanceTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeometryInstance, InstanceTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTags_MetaData), NewProp_InstanceTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_GeometryType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_GeometryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_MaterialOverride,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_bUseDistanceCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_RenderPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewProp_InstanceTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
	nullptr,
	&NewStructOps,
	"AuracronGeometryInstance",
	Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::PropPointers),
	sizeof(FAuracronGeometryInstance),
	alignof(FAuracronGeometryInstance),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGeometryInstance()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGeometryInstance *******************************************

// ********** Begin Delegate FOnGeometrySpawned ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics
{
	struct AuracronNaniteBridge_eventOnGeometrySpawned_Parms
	{
		UStaticMeshComponent* MeshComponent;
		FAuracronGeometryInstance GeometryConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando geometria \xc3\x83\xc2\xa9 spawnada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando geometria \xc3\x83\xc2\xa9 spawnada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeometryConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOnGeometrySpawned_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::NewProp_GeometryConfig = { "GeometryConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOnGeometrySpawned_Parms, GeometryConfig), Z_Construct_UScriptStruct_FAuracronGeometryInstance, METADATA_PARAMS(0, nullptr) }; // 263596609
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::NewProp_GeometryConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "OnGeometrySpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::AuracronNaniteBridge_eventOnGeometrySpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::AuracronNaniteBridge_eventOnGeometrySpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNaniteBridge::FOnGeometrySpawned_DelegateWrapper(const FMulticastScriptDelegate& OnGeometrySpawned, UStaticMeshComponent* MeshComponent, FAuracronGeometryInstance GeometryConfig)
{
	struct AuracronNaniteBridge_eventOnGeometrySpawned_Parms
	{
		UStaticMeshComponent* MeshComponent;
		FAuracronGeometryInstance GeometryConfig;
	};
	AuracronNaniteBridge_eventOnGeometrySpawned_Parms Parms;
	Parms.MeshComponent=MeshComponent;
	Parms.GeometryConfig=GeometryConfig;
	OnGeometrySpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGeometrySpawned ******************************************************

// ********** Begin Delegate FOnNaniteQualityChanged ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics
{
	struct AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms
	{
		EAuracronNaniteQuality OldQuality;
		EAuracronNaniteQuality NewQuality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando qualidade de Nanite muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando qualidade de Nanite muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldQuality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewQuality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_OldQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_OldQuality = { "OldQuality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms, OldQuality), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality, METADATA_PARAMS(0, nullptr) }; // 3779901557
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_NewQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_NewQuality = { "NewQuality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms, NewQuality), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality, METADATA_PARAMS(0, nullptr) }; // 3779901557
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_OldQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_OldQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_NewQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::NewProp_NewQuality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "OnNaniteQualityChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNaniteBridge::FOnNaniteQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnNaniteQualityChanged, EAuracronNaniteQuality OldQuality, EAuracronNaniteQuality NewQuality)
{
	struct AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms
	{
		EAuracronNaniteQuality OldQuality;
		EAuracronNaniteQuality NewQuality;
	};
	AuracronNaniteBridge_eventOnNaniteQualityChanged_Parms Parms;
	Parms.OldQuality=OldQuality;
	Parms.NewQuality=NewQuality;
	OnNaniteQualityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnNaniteQualityChanged *************************************************

// ********** Begin Class UAuracronNaniteBridge Function AddGeometryInstance ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics
{
	struct AuracronNaniteBridge_eventAddGeometryInstance_Parms
	{
		UInstancedStaticMeshComponent* InstancedComponent;
		FTransform Transform;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar inst\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InstancedComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_InstancedComponent = { "InstancedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventAddGeometryInstance_Parms, InstancedComponent), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancedComponent_MetaData), NewProp_InstancedComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventAddGeometryInstance_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
void Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventAddGeometryInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventAddGeometryInstance_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_InstancedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "AddGeometryInstance", Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::AuracronNaniteBridge_eventAddGeometryInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::AuracronNaniteBridge_eventAddGeometryInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execAddGeometryInstance)
{
	P_GET_OBJECT(UInstancedStaticMeshComponent,Z_Param_InstancedComponent);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddGeometryInstance(Z_Param_InstancedComponent,Z_Param_Out_Transform);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function AddGeometryInstance *************************

// ********** Begin Class UAuracronNaniteBridge Function CleanupUnusedGeometry *********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics
{
	struct AuracronNaniteBridge_eventCleanupUnusedGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar geometria n\xc3\x83\xc2\xa3o utilizada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar geometria n\xc3\x83\xc2\xa3o utilizada" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventCleanupUnusedGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventCleanupUnusedGeometry_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "CleanupUnusedGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::AuracronNaniteBridge_eventCleanupUnusedGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::AuracronNaniteBridge_eventCleanupUnusedGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execCleanupUnusedGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CleanupUnusedGeometry();
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function CleanupUnusedGeometry ***********************

// ********** Begin Class UAuracronNaniteBridge Function ConvertMeshToNanite ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics
{
	struct AuracronNaniteBridge_eventConvertMeshToNanite_Parms
	{
		UStaticMesh* SourceMesh;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Conversion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Converter mesh para Nanite\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Converter mesh para Nanite" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventConvertMeshToNanite_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventConvertMeshToNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventConvertMeshToNanite_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "ConvertMeshToNanite", Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::AuracronNaniteBridge_eventConvertMeshToNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::AuracronNaniteBridge_eventConvertMeshToNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execConvertMeshToNanite)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConvertMeshToNanite(Z_Param_SourceMesh);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function ConvertMeshToNanite *************************

// ********** Begin Class UAuracronNaniteBridge Function ConvertProceduralToNanite *****************
struct Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics
{
	struct AuracronNaniteBridge_eventConvertProceduralToNanite_Parms
	{
		UProceduralMeshComponent* ProceduralComponent;
		UStaticMesh* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Converter procedural para Nanite\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Converter procedural para Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProceduralComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::NewProp_ProceduralComponent = { "ProceduralComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventConvertProceduralToNanite_Parms, ProceduralComponent), Z_Construct_UClass_UProceduralMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralComponent_MetaData), NewProp_ProceduralComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventConvertProceduralToNanite_Parms, ReturnValue), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::NewProp_ProceduralComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "ConvertProceduralToNanite", Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::AuracronNaniteBridge_eventConvertProceduralToNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::AuracronNaniteBridge_eventConvertProceduralToNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execConvertProceduralToNanite)
{
	P_GET_OBJECT(UProceduralMeshComponent,Z_Param_ProceduralComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UStaticMesh**)Z_Param__Result=P_THIS->ConvertProceduralToNanite(Z_Param_ProceduralComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function ConvertProceduralToNanite *******************

// ********** Begin Class UAuracronNaniteBridge Function CreateGeometryInstances *******************
struct Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics
{
	struct AuracronNaniteBridge_eventCreateGeometryInstances_Parms
	{
		UStaticMesh* Mesh;
		TArray<FTransform> Transforms;
		UInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar inst\xc3\x83\xc2\xa2ncias de geometria\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar inst\xc3\x83\xc2\xa2ncias de geometria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventCreateGeometryInstances_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventCreateGeometryInstances_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventCreateGeometryInstances_Parms, ReturnValue), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "CreateGeometryInstances", Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::AuracronNaniteBridge_eventCreateGeometryInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::AuracronNaniteBridge_eventCreateGeometryInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execCreateGeometryInstances)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UInstancedStaticMeshComponent**)Z_Param__Result=P_THIS->CreateGeometryInstances(Z_Param_Mesh,Z_Param_Out_Transforms);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function CreateGeometryInstances *********************

// ********** Begin Class UAuracronNaniteBridge Function GenerateNaniteLODs ************************
struct Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics
{
	struct AuracronNaniteBridge_eventGenerateNaniteLODs_Parms
	{
		UStaticMesh* TargetMesh;
		int32 NumLODs;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar LODs para Nanite\n     */" },
#endif
		{ "CPP_Default_NumLODs", "8" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar LODs para Nanite" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumLODs;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateNaniteLODs_Parms, TargetMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_NumLODs = { "NumLODs", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateNaniteLODs_Parms, NumLODs), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventGenerateNaniteLODs_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventGenerateNaniteLODs_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_NumLODs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "GenerateNaniteLODs", Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::AuracronNaniteBridge_eventGenerateNaniteLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::AuracronNaniteBridge_eventGenerateNaniteLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execGenerateNaniteLODs)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_TargetMesh);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumLODs);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateNaniteLODs(Z_Param_TargetMesh,Z_Param_NumLODs);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function GenerateNaniteLODs **************************

// ********** Begin Class UAuracronNaniteBridge Function GenerateProceduralGeometry ****************
struct Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics
{
	struct AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms
	{
		FString GenerationType;
		TMap<FString,float> Parameters;
		UProceduralMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar geometria procedural\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar geometria procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GenerationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_GenerationType = { "GenerationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms, GenerationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationType_MetaData), NewProp_GenerationType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms, ReturnValue), Z_Construct_UClass_UProceduralMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_GenerationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "GenerateProceduralGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::AuracronNaniteBridge_eventGenerateProceduralGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execGenerateProceduralGeometry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GenerationType);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UProceduralMeshComponent**)Z_Param__Result=P_THIS->GenerateProceduralGeometry(Z_Param_GenerationType,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function GenerateProceduralGeometry ******************

// ********** Begin Class UAuracronNaniteBridge Function GenerateRealmGeometry *********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics
{
	struct AuracronNaniteBridge_eventGenerateRealmGeometry_Parms
	{
		int32 RealmIndex;
		FVector CenterLocation;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar geometria de realm\n     */" },
#endif
		{ "CPP_Default_Radius", "10000.000000" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar geometria de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateRealmGeometry_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateRealmGeometry_Parms, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGenerateRealmGeometry_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventGenerateRealmGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventGenerateRealmGeometry_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "GenerateRealmGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::AuracronNaniteBridge_eventGenerateRealmGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::AuracronNaniteBridge_eventGenerateRealmGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execGenerateRealmGeometry)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateRealmGeometry(Z_Param_RealmIndex,Z_Param_Out_CenterLocation,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function GenerateRealmGeometry ***********************

// ********** Begin Class UAuracronNaniteBridge Function GetNaniteStatistics ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics
{
	struct AuracronNaniteBridge_eventGetNaniteStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\x83\xc2\xadsticas de Nanite\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\x83\xc2\xadsticas de Nanite" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventGetNaniteStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "GetNaniteStatistics", Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::AuracronNaniteBridge_eventGetNaniteStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::AuracronNaniteBridge_eventGetNaniteStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execGetNaniteStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetNaniteStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function GetNaniteStatistics *************************

// ********** Begin Class UAuracronNaniteBridge Function OptimizeGeometryByDistance ****************
struct Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics
{
	struct AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms
	{
		FVector ViewerLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar geometria por dist\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar geometria por dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
void Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "OptimizeGeometryByDistance", Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::AuracronNaniteBridge_eventOptimizeGeometryByDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execOptimizeGeometryByDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeGeometryByDistance(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function OptimizeGeometryByDistance ******************

// ********** Begin Class UAuracronNaniteBridge Function OptimizeMeshForNanite *********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics
{
	struct AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms
	{
		UStaticMesh* TargetMesh;
		int32 TargetTriangles;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar mesh para Nanite\n     */" },
#endif
		{ "CPP_Default_TargetTriangles", "1000000" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar mesh para Nanite" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetMesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetTriangles;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_TargetMesh = { "TargetMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms, TargetMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_TargetTriangles = { "TargetTriangles", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms, TargetTriangles), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_TargetMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_TargetTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "OptimizeMeshForNanite", Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::AuracronNaniteBridge_eventOptimizeMeshForNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execOptimizeMeshForNanite)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_TargetMesh);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetTriangles);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeMeshForNanite(Z_Param_TargetMesh,Z_Param_TargetTriangles);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function OptimizeMeshForNanite ***********************

// ********** Begin Class UAuracronNaniteBridge Function RemoveGeometryInstance ********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics
{
	struct AuracronNaniteBridge_eventRemoveGeometryInstance_Parms
	{
		UInstancedStaticMeshComponent* InstancedComponent;
		int32 InstanceIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover inst\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InstancedComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_InstancedComponent = { "InstancedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventRemoveGeometryInstance_Parms, InstancedComponent), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancedComponent_MetaData), NewProp_InstancedComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventRemoveGeometryInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventRemoveGeometryInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventRemoveGeometryInstance_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_InstancedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "RemoveGeometryInstance", Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::AuracronNaniteBridge_eventRemoveGeometryInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::AuracronNaniteBridge_eventRemoveGeometryInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execRemoveGeometryInstance)
{
	P_GET_OBJECT(UInstancedStaticMeshComponent,Z_Param_InstancedComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveGeometryInstance(Z_Param_InstancedComponent,Z_Param_InstanceIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function RemoveGeometryInstance **********************

// ********** Begin Class UAuracronNaniteBridge Function SetNaniteQuality **************************
struct Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics
{
	struct AuracronNaniteBridge_eventSetNaniteQuality_Parms
	{
		EAuracronNaniteQuality Quality;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir qualidade de Nanite\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir qualidade de Nanite" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSetNaniteQuality_Parms, Quality), Z_Construct_UEnum_AuracronNaniteBridge_EAuracronNaniteQuality, METADATA_PARAMS(0, nullptr) }; // 3779901557
void Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventSetNaniteQuality_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventSetNaniteQuality_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "SetNaniteQuality", Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::AuracronNaniteBridge_eventSetNaniteQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::AuracronNaniteBridge_eventSetNaniteQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execSetNaniteQuality)
{
	P_GET_ENUM(EAuracronNaniteQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetNaniteQuality(EAuracronNaniteQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function SetNaniteQuality ****************************

// ********** Begin Class UAuracronNaniteBridge Function SpawnNaniteGeometry ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics
{
	struct AuracronNaniteBridge_eventSpawnNaniteGeometry_Parms
	{
		FAuracronGeometryInstance GeometryConfig;
		UStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar geometria Nanite\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar geometria Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeometryConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::NewProp_GeometryConfig = { "GeometryConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSpawnNaniteGeometry_Parms, GeometryConfig), Z_Construct_UScriptStruct_FAuracronGeometryInstance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryConfig_MetaData), NewProp_GeometryConfig_MetaData) }; // 263596609
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSpawnNaniteGeometry_Parms, ReturnValue), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::NewProp_GeometryConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "SpawnNaniteGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::AuracronNaniteBridge_eventSpawnNaniteGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::AuracronNaniteBridge_eventSpawnNaniteGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execSpawnNaniteGeometry)
{
	P_GET_STRUCT_REF(FAuracronGeometryInstance,Z_Param_Out_GeometryConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UStaticMeshComponent**)Z_Param__Result=P_THIS->SpawnNaniteGeometry(Z_Param_Out_GeometryConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function SpawnNaniteGeometry *************************

// ********** Begin Class UAuracronNaniteBridge Function SpawnPortalGeometry ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics
{
	struct AuracronNaniteBridge_eventSpawnPortalGeometry_Parms
	{
		FVector Location;
		int32 DestinationRealm;
		UStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar portal geom\xc3\x83\xc2\xa9trico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar portal geom\xc3\x83\xc2\xa9trico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DestinationRealm;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSpawnPortalGeometry_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_DestinationRealm = { "DestinationRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSpawnPortalGeometry_Parms, DestinationRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventSpawnPortalGeometry_Parms, ReturnValue), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_DestinationRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "SpawnPortalGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::AuracronNaniteBridge_eventSpawnPortalGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::AuracronNaniteBridge_eventSpawnPortalGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execSpawnPortalGeometry)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_DestinationRealm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UStaticMeshComponent**)Z_Param__Result=P_THIS->SpawnPortalGeometry(Z_Param_Out_Location,Z_Param_DestinationRealm);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function SpawnPortalGeometry *************************

// ********** Begin Class UAuracronNaniteBridge Function TransitionRealmGeometry *******************
struct Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics
{
	struct AuracronNaniteBridge_eventTransitionRealmGeometry_Parms
	{
		int32 FromRealm;
		int32 ToRealm;
		float TransitionTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Transicionar geometria entre realms\n     */" },
#endif
		{ "CPP_Default_TransitionTime", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transicionar geometria entre realms" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_FromRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ToRealm;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_FromRealm = { "FromRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventTransitionRealmGeometry_Parms, FromRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ToRealm = { "ToRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventTransitionRealmGeometry_Parms, ToRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventTransitionRealmGeometry_Parms, TransitionTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventTransitionRealmGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventTransitionRealmGeometry_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_FromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ToRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "TransitionRealmGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::AuracronNaniteBridge_eventTransitionRealmGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::AuracronNaniteBridge_eventTransitionRealmGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execTransitionRealmGeometry)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_FromRealm);
	P_GET_PROPERTY(FIntProperty,Z_Param_ToRealm);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TransitionRealmGeometry(Z_Param_FromRealm,Z_Param_ToRealm,Z_Param_TransitionTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function TransitionRealmGeometry *********************

// ********** Begin Class UAuracronNaniteBridge Function UpdateGeometryInstance ********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics
{
	struct AuracronNaniteBridge_eventUpdateGeometryInstance_Parms
	{
		UInstancedStaticMeshComponent* InstancedComponent;
		int32 InstanceIndex;
		FTransform NewTransform;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Instancing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar inst\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar inst\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewTransform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InstancedComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewTransform;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_InstancedComponent = { "InstancedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateGeometryInstance_Parms, InstancedComponent), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancedComponent_MetaData), NewProp_InstancedComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateGeometryInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_NewTransform = { "NewTransform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateGeometryInstance_Parms, NewTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewTransform_MetaData), NewProp_NewTransform_MetaData) };
void Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventUpdateGeometryInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventUpdateGeometryInstance_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_InstancedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_NewTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "UpdateGeometryInstance", Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::AuracronNaniteBridge_eventUpdateGeometryInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::AuracronNaniteBridge_eventUpdateGeometryInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execUpdateGeometryInstance)
{
	P_GET_OBJECT(UInstancedStaticMeshComponent,Z_Param_InstancedComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_NewTransform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateGeometryInstance(Z_Param_InstancedComponent,Z_Param_InstanceIndex,Z_Param_Out_NewTransform);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function UpdateGeometryInstance **********************

// ********** Begin Class UAuracronNaniteBridge Function UpdateProceduralGeometry ******************
struct Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics
{
	struct AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms
	{
		UProceduralMeshComponent* ProceduralComponent;
		TMap<FString,float> NewParameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar geometria procedural\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar geometria procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewParameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProceduralComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NewParameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ProceduralComponent = { "ProceduralComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms, ProceduralComponent), Z_Construct_UClass_UProceduralMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralComponent_MetaData), NewProp_ProceduralComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters_ValueProp = { "NewParameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters_Key_KeyProp = { "NewParameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters = { "NewParameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms, NewParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewParameters_MetaData), NewProp_NewParameters_MetaData) };
void Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ProceduralComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_NewParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "UpdateProceduralGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::AuracronNaniteBridge_eventUpdateProceduralGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execUpdateProceduralGeometry)
{
	P_GET_OBJECT(UProceduralMeshComponent,Z_Param_ProceduralComponent);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_NewParameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateProceduralGeometry(Z_Param_ProceduralComponent,Z_Param_Out_NewParameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function UpdateProceduralGeometry ********************

// ********** Begin Class UAuracronNaniteBridge Function UpdateRealmGeometry ***********************
struct Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics
{
	struct AuracronNaniteBridge_eventUpdateRealmGeometry_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Nanite|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar geometria de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar geometria de realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNaniteBridge_eventUpdateRealmGeometry_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNaniteBridge_eventUpdateRealmGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNaniteBridge_eventUpdateRealmGeometry_Parms), &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNaniteBridge, nullptr, "UpdateRealmGeometry", Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::AuracronNaniteBridge_eventUpdateRealmGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::AuracronNaniteBridge_eventUpdateRealmGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNaniteBridge::execUpdateRealmGeometry)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateRealmGeometry(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronNaniteBridge Function UpdateRealmGeometry *************************

// ********** Begin Class UAuracronNaniteBridge ****************************************************
void UAuracronNaniteBridge::StaticRegisterNativesUAuracronNaniteBridge()
{
	UClass* Class = UAuracronNaniteBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddGeometryInstance", &UAuracronNaniteBridge::execAddGeometryInstance },
		{ "CleanupUnusedGeometry", &UAuracronNaniteBridge::execCleanupUnusedGeometry },
		{ "ConvertMeshToNanite", &UAuracronNaniteBridge::execConvertMeshToNanite },
		{ "ConvertProceduralToNanite", &UAuracronNaniteBridge::execConvertProceduralToNanite },
		{ "CreateGeometryInstances", &UAuracronNaniteBridge::execCreateGeometryInstances },
		{ "GenerateNaniteLODs", &UAuracronNaniteBridge::execGenerateNaniteLODs },
		{ "GenerateProceduralGeometry", &UAuracronNaniteBridge::execGenerateProceduralGeometry },
		{ "GenerateRealmGeometry", &UAuracronNaniteBridge::execGenerateRealmGeometry },
		{ "GetNaniteStatistics", &UAuracronNaniteBridge::execGetNaniteStatistics },
		{ "OptimizeGeometryByDistance", &UAuracronNaniteBridge::execOptimizeGeometryByDistance },
		{ "OptimizeMeshForNanite", &UAuracronNaniteBridge::execOptimizeMeshForNanite },
		{ "RemoveGeometryInstance", &UAuracronNaniteBridge::execRemoveGeometryInstance },
		{ "SetNaniteQuality", &UAuracronNaniteBridge::execSetNaniteQuality },
		{ "SpawnNaniteGeometry", &UAuracronNaniteBridge::execSpawnNaniteGeometry },
		{ "SpawnPortalGeometry", &UAuracronNaniteBridge::execSpawnPortalGeometry },
		{ "TransitionRealmGeometry", &UAuracronNaniteBridge::execTransitionRealmGeometry },
		{ "UpdateGeometryInstance", &UAuracronNaniteBridge::execUpdateGeometryInstance },
		{ "UpdateProceduralGeometry", &UAuracronNaniteBridge::execUpdateProceduralGeometry },
		{ "UpdateRealmGeometry", &UAuracronNaniteBridge::execUpdateRealmGeometry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronNaniteBridge;
UClass* UAuracronNaniteBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronNaniteBridge;
	if (!Z_Registration_Info_UClass_UAuracronNaniteBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronNaniteBridge"),
			Z_Registration_Info_UClass_UAuracronNaniteBridge.InnerSingleton,
			StaticRegisterNativesUAuracronNaniteBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronNaniteBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronNaniteBridge_NoRegister()
{
	return UAuracronNaniteBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronNaniteBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Nanite" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Geometria Virtualizada Nanite\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de geometria com Nanite\n */" },
#endif
		{ "DisplayName", "AURACRON Nanite Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronNaniteBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Geometria Virtualizada Nanite\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de geometria com Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmGeometryConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de geometria de realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de geometria de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveGeometryInstances_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inst\xc3\x83\xc2\xa2ncias de geometria ativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inst\xc3\x83\xc2\xa2ncias de geometria ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveMeshComponents_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de mesh ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de mesh ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveInstancedComponents_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes instanciados ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes instanciados ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteStatistics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\x83\xc2\xadsticas de Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\x83\xc2\xadsticas de Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGeometrySpawned_MetaData[] = {
		{ "Category", "AURACRON Nanite|Events" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnNaniteQualityChanged_MetaData[] = {
		{ "Category", "AURACRON Nanite|Events" },
		{ "ModuleRelativePath", "Public/AuracronNaniteBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NaniteConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmGeometryConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveGeometryInstances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveGeometryInstances;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveMeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveMeshComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveInstancedComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveInstancedComponents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NaniteStatistics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NaniteStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NaniteStatistics;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGeometrySpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnNaniteQualityChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_AddGeometryInstance, "AddGeometryInstance" }, // 652761625
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_CleanupUnusedGeometry, "CleanupUnusedGeometry" }, // 3699453148
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_ConvertMeshToNanite, "ConvertMeshToNanite" }, // 257199012
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_ConvertProceduralToNanite, "ConvertProceduralToNanite" }, // 4172106267
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_CreateGeometryInstances, "CreateGeometryInstances" }, // 3441669938
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_GenerateNaniteLODs, "GenerateNaniteLODs" }, // 897292604
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_GenerateProceduralGeometry, "GenerateProceduralGeometry" }, // 3250774638
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_GenerateRealmGeometry, "GenerateRealmGeometry" }, // 1090479966
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_GetNaniteStatistics, "GetNaniteStatistics" }, // 835035743
		{ &Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature, "OnGeometrySpawned__DelegateSignature" }, // 3945965436
		{ &Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature, "OnNaniteQualityChanged__DelegateSignature" }, // 1092337869
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeGeometryByDistance, "OptimizeGeometryByDistance" }, // 810178360
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_OptimizeMeshForNanite, "OptimizeMeshForNanite" }, // 248155478
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_RemoveGeometryInstance, "RemoveGeometryInstance" }, // 1442680021
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_SetNaniteQuality, "SetNaniteQuality" }, // 1758584246
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_SpawnNaniteGeometry, "SpawnNaniteGeometry" }, // 3244997344
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_SpawnPortalGeometry, "SpawnPortalGeometry" }, // 3107605294
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_TransitionRealmGeometry, "TransitionRealmGeometry" }, // 3717718826
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateGeometryInstance, "UpdateGeometryInstance" }, // 1142239341
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateProceduralGeometry, "UpdateProceduralGeometry" }, // 3748797267
		{ &Z_Construct_UFunction_UAuracronNaniteBridge_UpdateRealmGeometry, "UpdateRealmGeometry" }, // 381768496
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronNaniteBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteConfiguration = { "NaniteConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, NaniteConfiguration), Z_Construct_UScriptStruct_FAuracronNaniteConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteConfiguration_MetaData), NewProp_NaniteConfiguration_MetaData) }; // 306756608
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_RealmGeometryConfiguration = { "RealmGeometryConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, RealmGeometryConfiguration), Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmGeometryConfiguration_MetaData), NewProp_RealmGeometryConfiguration_MetaData) }; // 748842970
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveGeometryInstances_Inner = { "ActiveGeometryInstances", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGeometryInstance, METADATA_PARAMS(0, nullptr) }; // 263596609
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveGeometryInstances = { "ActiveGeometryInstances", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, ActiveGeometryInstances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveGeometryInstances_MetaData), NewProp_ActiveGeometryInstances_MetaData) }; // 263596609
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveMeshComponents_Inner = { "ActiveMeshComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveMeshComponents = { "ActiveMeshComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, ActiveMeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveMeshComponents_MetaData), NewProp_ActiveMeshComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveInstancedComponents_Inner = { "ActiveInstancedComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveInstancedComponents = { "ActiveInstancedComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, ActiveInstancedComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveInstancedComponents_MetaData), NewProp_ActiveInstancedComponents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics_ValueProp = { "NaniteStatistics", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics_Key_KeyProp = { "NaniteStatistics_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics = { "NaniteStatistics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, NaniteStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteStatistics_MetaData), NewProp_NaniteStatistics_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_OnGeometrySpawned = { "OnGeometrySpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, OnGeometrySpawned), Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnGeometrySpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGeometrySpawned_MetaData), NewProp_OnGeometrySpawned_MetaData) }; // 3945965436
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_OnNaniteQualityChanged = { "OnNaniteQualityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNaniteBridge, OnNaniteQualityChanged), Z_Construct_UDelegateFunction_UAuracronNaniteBridge_OnNaniteQualityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnNaniteQualityChanged_MetaData), NewProp_OnNaniteQualityChanged_MetaData) }; // 1092337869
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronNaniteBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_RealmGeometryConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveGeometryInstances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveGeometryInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveMeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveMeshComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveInstancedComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_ActiveInstancedComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_NaniteStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_OnGeometrySpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNaniteBridge_Statics::NewProp_OnNaniteQualityChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNaniteBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronNaniteBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNaniteBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNaniteBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronNaniteBridge_Statics::ClassParams = {
	&UAuracronNaniteBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronNaniteBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNaniteBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNaniteBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronNaniteBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronNaniteBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronNaniteBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronNaniteBridge.OuterSingleton, Z_Construct_UClass_UAuracronNaniteBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronNaniteBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronNaniteBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_NaniteConfiguration(TEXT("NaniteConfiguration"));
	const bool bIsValid = true
		&& Name_NaniteConfiguration == ClassReps[(int32)ENetFields_Private::NaniteConfiguration].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronNaniteBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronNaniteBridge);
UAuracronNaniteBridge::~UAuracronNaniteBridge() {}
// ********** End Class UAuracronNaniteBridge ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronNaniteGeometryType_StaticEnum, TEXT("EAuracronNaniteGeometryType"), &Z_Registration_Info_UEnum_EAuracronNaniteGeometryType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2793815717U) },
		{ EAuracronNaniteQuality_StaticEnum, TEXT("EAuracronNaniteQuality"), &Z_Registration_Info_UEnum_EAuracronNaniteQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3779901557U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronNaniteConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronNaniteConfiguration_Statics::NewStructOps, TEXT("AuracronNaniteConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronNaniteConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronNaniteConfiguration), 306756608U) },
		{ FAuracronRealmGeometryConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmGeometryConfiguration_Statics::NewStructOps, TEXT("AuracronRealmGeometryConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronRealmGeometryConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmGeometryConfiguration), 748842970U) },
		{ FAuracronGeometryInstance::StaticStruct, Z_Construct_UScriptStruct_FAuracronGeometryInstance_Statics::NewStructOps, TEXT("AuracronGeometryInstance"), &Z_Registration_Info_UScriptStruct_FAuracronGeometryInstance, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGeometryInstance), 263596609U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronNaniteBridge, UAuracronNaniteBridge::StaticClass, TEXT("UAuracronNaniteBridge"), &Z_Registration_Info_UClass_UAuracronNaniteBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronNaniteBridge), 4180814594U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_612123867(TEXT("/Script/AuracronNaniteBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNaniteBridge_Public_AuracronNaniteBridge_h__Script_AuracronNaniteBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
