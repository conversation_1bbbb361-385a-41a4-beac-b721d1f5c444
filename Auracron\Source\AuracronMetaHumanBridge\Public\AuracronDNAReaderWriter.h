#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// MetaHuman DNA includes
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
#include "dna/BinaryStreamReader.h"
#include "dna/BinaryStreamWriter.h"
#include "dna/JSONStreamReader.h"
#include "dna/JSONStreamWriter.h"
#include "dna/FileStream.h"
#include "dna/Status.h"
#include "dna/DataLayer.h"
#include "dnacalib/DNACalibDNAReader.h"
#include "dnacalib/Command.h"
#include "dnacalib/commands/SetNeutralJointTranslationsCommand.h"
#include "dnacalib/commands/SetNeutralJointRotationsCommand.h"
#include "dnacalib/commands/SetVertexPositionsCommand.h"
#include "dnacalib/commands/SetBlendShapeTargetDeltasCommand.h"
#include "dnacalib/types/Aliases.h"
#endif

// Forward declarations
enum class EMetaHumanDNADataLayer : uint8;
struct FMetaHumanDNADescriptor;

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronDNAReaderWriter, Log, All);

/**
 * Thread-safe wrapper for MetaHuman DNA file reading operations
 * Provides type-safe abstraction over BinaryStreamReader and JSONStreamReader
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronDNAReader
{
public:
    FAuracronDNAReader();
    ~FAuracronDNAReader();

    // Core DNA reading operations
    bool LoadFromFile(const FString& FilePath, EMetaHumanDNADataLayer DataLayer = EMetaHumanDNADataLayer::All);
    bool IsValid() const;
    void Reset();

    // DNA descriptor access
    FMetaHumanDNADescriptor GetDescriptor() const;

    // Mesh data access
    int32 GetMeshCount() const;
    FString GetMeshName(int32 MeshIndex) const;
    TArray<FVector> GetVertexPositions(int32 MeshIndex) const;
    TArray<int32> GetVertexIndices(int32 MeshIndex) const;
    TArray<FVector> GetVertexNormals(int32 MeshIndex) const;
    TArray<FVector2D> GetVertexUVs(int32 MeshIndex, int32 UVChannel = 0) const;

    // Blend shape data access
    int32 GetBlendShapeTargetCount(int32 MeshIndex) const;
    FString GetBlendShapeChannelName(int32 ChannelIndex) const;
    TArray<FVector> GetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex) const;
    TArray<int32> GetBlendShapeTargetVertexIndices(int32 MeshIndex, int32 TargetIndex) const;

    // Joint data access
    int32 GetJointCount() const;
    FString GetJointName(int32 JointIndex) const;
    int32 GetJointParentIndex(int32 JointIndex) const;
    FVector GetJointTranslation(int32 JointIndex) const;
    FRotator GetJointRotation(int32 JointIndex) const;
    FVector GetJointScale(int32 JointIndex) const;

    // Skin weights access
    TArray<float> GetSkinWeights(int32 MeshIndex, int32 VertexIndex) const;
    TArray<int32> GetSkinWeightJointIndices(int32 MeshIndex, int32 VertexIndex) const;

    // Animation data access
    int32 GetAnimatedMapCount() const;
    TArray<float> GetAnimatedMapValues(int32 MapIndex) const;
    FString GetAnimatedMapName(int32 MapIndex) const;

    // Control rig data access
    int32 GetControlCount() const;
    FString GetControlName(int32 ControlIndex) const;
    TArray<float> GetControlValues(int32 ControlIndex) const;

    // Machine learning data access
    int32 GetMLControlCount() const;
    FString GetMLControlName(int32 ControlIndex) const;
    TArray<float> GetMLControlValues(int32 ControlIndex) const;

    // Neural network data access
    int32 GetNeuralNetworkCount() const;
    FString GetNeuralNetworkName(int32 NetworkIndex) const;
    TArray<float> GetNeuralNetworkInputs(int32 NetworkIndex) const;
    TArray<float> GetNeuralNetworkOutputs(int32 NetworkIndex) const;

    // Validation methods
    bool ValidateIntegrity() const;
    FString GetLastError() const;

    // Thread safety
    mutable FCriticalSection AccessMutex;

private:
    // Native DNA reader instance
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    TUniquePtr<dna::BinaryStreamReader> NativeReader;
#else
    void* NativeReader;
#endif

    // State tracking
    FThreadSafeBool bIsValid;
    FString LastError;
    FString LoadedFilePath;

    // Internal helper methods
    bool ValidateMeshIndex(int32 MeshIndex) const;
    bool ValidateJointIndex(int32 JointIndex) const;
    bool ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const;
    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;

    // Prevent copying
    FAuracronDNAReader(const FAuracronDNAReader&) = delete;
    FAuracronDNAReader& operator=(const FAuracronDNAReader&) = delete;
};

/**
 * Thread-safe wrapper for MetaHuman DNA file writing operations
 * Provides type-safe abstraction over BinaryStreamWriter and JSONStreamWriter
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronDNAWriter
{
public:
    FAuracronDNAWriter();
    ~FAuracronDNAWriter();

    // Core DNA writing operations
    bool InitializeFromReader(const FAuracronDNAReader& Reader, EMetaHumanDNADataLayer DataLayer = EMetaHumanDNADataLayer::All);
    bool SaveToFile(const FString& FilePath, bool bBinaryFormat = true);
    bool IsValid() const;
    void Reset();

    // DNA descriptor modification
    bool SetDescriptor(const FMetaHumanDNADescriptor& Descriptor);

    // Mesh data modification
    bool SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions);
    bool SetVertexNormals(int32 MeshIndex, const TArray<FVector>& Normals);
    bool SetVertexUVs(int32 MeshIndex, const TArray<FVector2D>& UVs, int32 UVChannel = 0);

    // Blend shape data modification
    bool SetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, const TArray<FVector>& Deltas);
    bool SetBlendShapeTargetVertexIndices(int32 MeshIndex, int32 TargetIndex, const TArray<int32>& VertexIndices);
    bool AddBlendShapeTarget(int32 MeshIndex, const FString& BlendShapeName, const TArray<int32>& VertexIndices, const TArray<FVector>& Deltas);
    bool RemoveBlendShapeTarget(int32 MeshIndex, int32 TargetIndex);

    // Joint data modification
    bool SetNeutralJointTranslations(const TArray<FVector>& Translations);
    bool SetNeutralJointRotations(const TArray<FRotator>& Rotations);
    bool SetJointHierarchy(const TArray<int32>& ParentIndices);

    // Skin weights modification
    bool SetSkinWeights(int32 MeshIndex, int32 VertexIndex, const TArray<float>& Weights, const TArray<int32>& JointIndices);

    // Animation data modification
    bool SetAnimatedMapValues(int32 MapIndex, const TArray<float>& Values);
    bool AddAnimatedMap(const FString& MapName, const TArray<float>& Values);
    bool RemoveAnimatedMap(int32 MapIndex);

    // Control rig data modification
    bool SetControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveControl(int32 ControlIndex);

    // Machine learning data modification
    bool SetMLControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddMLControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveMLControl(int32 ControlIndex);

    // Neural network data modification
    bool SetNeuralNetworkInputs(int32 NetworkIndex, const TArray<float>& Inputs);
    bool SetNeuralNetworkOutputs(int32 NetworkIndex, const TArray<float>& Outputs);
    bool AddNeuralNetwork(const FString& NetworkName, const TArray<float>& Inputs, const TArray<float>& Outputs);
    bool RemoveNeuralNetwork(int32 NetworkIndex);

    // Validation and optimization
    bool OptimizeForSize();
    bool ValidateBeforeSave() const;
    FString GetLastError() const;

    // Thread safety
    mutable FCriticalSection AccessMutex;

private:
    // Native DNA writer instance
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    TUniquePtr<dna::BinaryStreamWriter> NativeWriter;
#else
    void* NativeWriter;
#endif

    // State tracking
    FThreadSafeBool bIsValid;
    FThreadSafeBool bIsBinaryFormat;
    FString LastError;
    FString TargetFilePath;

    // Internal helper methods
    bool ValidateMeshIndex(int32 MeshIndex) const;
    bool ValidateJointIndex(int32 JointIndex) const;
    bool ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const;
    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;

    // Prevent copying
    FAuracronDNAWriter(const FAuracronDNAWriter&) = delete;
    FAuracronDNAWriter& operator=(const FAuracronDNAWriter&) = delete;
};
