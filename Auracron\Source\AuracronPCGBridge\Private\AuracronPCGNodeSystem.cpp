// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Node System Implementation
// Bridge 2.2: PCG Framework - Graph System Core

#include "AuracronPCGNodeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "PCGData.h"
#include "PCGPointData.h"

// Engine includes
#include "Engine/World.h"
#include "UObject/ConstructorHelpers.h"

// Initialize static member
UAuracronPCGNodeRegistry* UAuracronPCGNodeRegistry::GlobalRegistryInstance = nullptr;

// UAuracronPCGNodeSettings implementation

UAuracronPCGNodeSettings::UAuracronPCGNodeSettings()
{
    // Initialize node metadata with defaults
    NodeMetadata = FAuracronPCGNodeMetadata();
    NodeMetadata.NodeName = GetClass()->GetName();
    NodeMetadata.NodeDescription = TEXT("Custom AURACRON PCG Node");
    
    // Node settings
    bCacheResults = true;
    bShowAdvancedSettings = false;
    bAllowParallelExecution = true;
    
    // Base settings
    bEnabled = true;
    bEnableMultithreading = true;
    ElementName = TEXT("AuracronPCGNode");
}

bool UAuracronPCGNodeSettings::ValidateNodeSettings(TArray<FString>& ValidationErrors) const
{
    ValidationErrors.Empty();

    // Basic validation
    if (NodeMetadata.NodeName.IsEmpty())
    {
        ValidationErrors.Add(TEXT("Node name cannot be empty"));
    }

    if (NodeMetadata.NodeDescription.IsEmpty())
    {
        ValidationErrors.Add(TEXT("Node description cannot be empty"));
    }

    // Validate element parameters
    if (ElementParameters.StringParameters.Contains(TEXT("")))
    {
        ValidationErrors.Add(TEXT("Empty parameter names are not allowed"));
    }

    // Performance validation
    if (BatchSize <= 0)
    {
        ValidationErrors.Add(TEXT("Batch size must be greater than zero"));
    }

    if (TimeoutSeconds <= 0.0f)
    {
        ValidationErrors.Add(TEXT("Timeout must be greater than zero"));
    }

    // GPU validation
    if (NodeMetadata.bRequiresGPU && !bEnableMultithreading)
    {
        ValidationErrors.Add(TEXT("GPU nodes should enable multithreading for optimal performance"));
    }

    return ValidationErrors.Num() == 0;
}

bool UAuracronPCGNodeSettings::IsNodeConfigurationValid() const
{
    TArray<FString> ValidationErrors;
    return ValidateNodeSettings(ValidationErrors);
}

FString UAuracronPCGNodeSettings::GetNodeDisplayName() const
{
    return NodeMetadata.NodeName.IsEmpty() ? GetClass()->GetName() : NodeMetadata.NodeName;
}

FString UAuracronPCGNodeSettings::GetNodeTooltip() const
{
    FString Tooltip = NodeMetadata.NodeDescription;
    
    if (!NodeMetadata.Author.IsEmpty())
    {
        Tooltip += FString::Printf(TEXT("\nAuthor: %s"), *NodeMetadata.Author);
    }
    
    if (!NodeMetadata.Version.IsEmpty())
    {
        Tooltip += FString::Printf(TEXT("\nVersion: %s"), *NodeMetadata.Version);
    }
    
    if (NodeMetadata.bIsExperimental)
    {
        Tooltip += TEXT("\n[EXPERIMENTAL]");
    }
    
    if (NodeMetadata.bRequiresGPU)
    {
        Tooltip += TEXT("\n[GPU Required]");
    }
    
    return Tooltip;
}

TArray<FPCGPinProperties> UAuracronPCGNodeSettings::GetCustomInputPinProperties() const
{
    TArray<FPCGPinProperties> InputPins;
    ConfigureInputPins(InputPins);
    return InputPins;
}

TArray<FPCGPinProperties> UAuracronPCGNodeSettings::GetCustomOutputPinProperties() const
{
    TArray<FPCGPinProperties> OutputPins;
    ConfigureOutputPins(OutputPins);
    return OutputPins;
}

void UAuracronPCGNodeSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    // Default implementation - single spatial input
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGNodeSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    // Default implementation - single point output
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

#if WITH_EDITOR
FText UAuracronPCGNodeSettings::GetDefaultNodeTitle() const
{
    return FText::FromString(GetNodeDisplayName());
}

FText UAuracronPCGNodeSettings::GetNodeTooltipText() const
{
    return FText::FromString(GetNodeTooltip());
}

FLinearColor UAuracronPCGNodeSettings::GetNodeTitleColor() const
{
    return NodeMetadata.NodeColor;
}
#endif

// FAuracronPCGNodeElement implementation

FAuracronPCGNodeElement::FAuracronPCGNodeElement()
{
    ProfilingData.Empty();
}

bool FAuracronPCGNodeElement::Execute(FPCGContext* Context) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    if (!Context)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Execute called with null context"));
        return false;
    }

    // Pre-execution validation and setup
    if (!PreExecute(Context))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Pre-execution failed"));
        return false;
    }

    // Check if we should use cache
    if (ShouldUseCache(Context))
    {
        if (LoadFromCache(Context, Context->OutputData))
        {
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Loaded result from cache"));
            return true;
        }
    }

    // Begin profiling
    BeginNodeProfiling(Context);

    // Execute the base element logic
    bool bSuccess = FAuracronPCGElementBase::Execute(Context);

    // End profiling and get result
    FAuracronPCGElementResult Result;
    Result.bSuccess = bSuccess;
    Result.ExecutionTimeSeconds = EndPerformanceTimer();
    Result.OutputDataCount = Context->OutputData.GetInputs().Num();

    EndNodeProfiling(Context, Result);

    // Post-execution processing
    if (bSuccess)
    {
        PostExecute(Context, Result);
        
        // Save to cache if successful
        if (ShouldUseCache(Context))
        {
            SaveToCache(Context, Context->OutputData);
        }
    }

    return bSuccess;
}

bool FAuracronPCGNodeElement::PreExecute(FPCGContext* Context) const
{
    if (!Context)
    {
        return false;
    }

    // Validate node execution requirements
    if (!ValidateNodeExecution(Context))
    {
        return false;
    }

    // Log execution start
    LogExecutionStart(Context);

    return true;
}

bool FAuracronPCGNodeElement::PostExecute(FPCGContext* Context, const FAuracronPCGElementResult& Result) const
{
    if (!Context)
    {
        return false;
    }

    // Log execution end
    LogExecutionEnd(Context, Result);

    // Additional post-processing can be added here
    return true;
}

bool FAuracronPCGNodeElement::ValidateNodeExecution(FPCGContext* Context) const
{
    if (!Context || !Context->Node)
    {
        return false;
    }

    // Check if node settings are valid
    if (const UAuracronPCGNodeSettings* NodeSettings = Cast<UAuracronPCGNodeSettings>(Context->Node->GetSettings()))
    {
        TArray<FString> ValidationErrors;
        if (!NodeSettings->ValidateNodeSettings(ValidationErrors))
        {
            for (const FString& Error : ValidationErrors)
            {
                ReportError(Context, Error);
            }
            return false;
        }

        // Check GPU requirements
        if (NodeSettings->NodeMetadata.bRequiresGPU && !GRHISupportsComputeShaders)
        {
            ReportError(Context, TEXT("Node requires GPU support but compute shaders are not available"));
            return false;
        }
    }

    return true;
}

void FAuracronPCGNodeElement::BeginNodeProfiling(FPCGContext* Context) const
{
    if (!Context || !Context->Node)
    {
        return;
    }

    FScopeLock Lock(&ProfilingLock);

    FString NodeName = Context->Node->GetNodeTitle().ToString();
    double StartTime = FPlatformTime::Seconds();
    
    ProfilingData.Add(NodeName + TEXT("_StartTime"), StartTime);

    AURACRON_PCG_LOG_PERFORMANCE(Verbose, TEXT("Started profiling node: %s"), *NodeName);
}

void FAuracronPCGNodeElement::EndNodeProfiling(FPCGContext* Context, const FAuracronPCGElementResult& Result) const
{
    if (!Context || !Context->Node)
    {
        return;
    }

    FScopeLock Lock(&ProfilingLock);

    FString NodeName = Context->Node->GetNodeTitle().ToString();
    double EndTime = FPlatformTime::Seconds();
    
    if (const double* StartTimePtr = ProfilingData.Find(NodeName + TEXT("_StartTime")))
    {
        double ExecutionTime = EndTime - *StartTimePtr;
        ProfilingData.Add(NodeName + TEXT("_ExecutionTime"), ExecutionTime);
        
        AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Node '%s' execution time: %.6f seconds (Points: %d, Success: %s)"), 
                                     *NodeName, ExecutionTime, Result.PointsProcessed, 
                                     Result.bSuccess ? TEXT("Yes") : TEXT("No"));
    }
}

bool FAuracronPCGNodeElement::ShouldUseCache(FPCGContext* Context) const
{
    if (!Context || !Context->Node)
    {
        return false;
    }

    if (const UAuracronPCGNodeSettings* NodeSettings = Cast<UAuracronPCGNodeSettings>(Context->Node->GetSettings()))
    {
        return NodeSettings->bCacheResults;
    }

    return false;
}

bool FAuracronPCGNodeElement::LoadFromCache(FPCGContext* Context, FPCGDataCollection& OutputData) const
{
    if (!Context || !Context->Node || !Context->Node->GetSettings())
    {
        return false;
    }
    
    // Get cache key from node settings and input data
    const UAuracronPCGNodeSettings* NodeSettings = Cast<UAuracronPCGNodeSettings>(Context->Node->GetSettings());
    if (!NodeSettings)
    {
        return false;
    }
    
    // Generate cache key based on node configuration and input data
    FString CacheKey = GenerateCacheKey(Context);
    if (CacheKey.IsEmpty())
    {
        return false;
    }
    
    // Try to load from PCG cache system
    if (Context->GetCacheManager())
    {
        FPCGDataCollection CachedData;
        if (Context->GetCacheManager()->GetFromCache(CacheKey, CachedData))
        {
            OutputData = CachedData;
            UE_LOG(LogAuracronPCGBridge, VeryVerbose, TEXT("Cache hit for node: %s"), *NodeSettings->GetNodeDisplayName());
            return true;
        }
    }
    
    return false;
}

void FAuracronPCGNodeElement::SaveToCache(FPCGContext* Context, const FPCGDataCollection& OutputData) const
{
    if (!Context || !Context->Node || !Context->Node->GetSettings())
    {
        return;
    }
    
    const UAuracronPCGNodeSettings* NodeSettings = Cast<UAuracronPCGNodeSettings>(Context->Node->GetSettings());
    if (!NodeSettings)
    {
        return;
    }
    
    // Generate cache key based on node configuration and input data
    FString CacheKey = GenerateCacheKey(Context);
    if (CacheKey.IsEmpty())
    {
        return;
    }
    
    // Save to PCG cache system
    if (Context->GetCacheManager())
    {
        Context->GetCacheManager()->StoreInCache(CacheKey, OutputData);
        UE_LOG(LogAuracronPCGBridge, VeryVerbose, TEXT("Cached output for node: %s"), *NodeSettings->GetNodeDisplayName());
    }
}

FString FAuracronPCGNodeElement::GenerateCacheKey(FPCGContext* Context) const
{
    if (!Context || !Context->Node || !Context->Node->GetSettings())
    {
        return FString();
    }
    
    const UAuracronPCGNodeSettings* NodeSettings = Cast<UAuracronPCGNodeSettings>(Context->Node->GetSettings());
    if (!NodeSettings)
    {
        return FString();
    }
    
    // Build cache key from multiple components
    FString CacheKey;
    
    // Add node class name
    CacheKey += NodeSettings->GetClass()->GetName();
    CacheKey += TEXT("_");
    
    // Add node settings hash
    uint32 SettingsHash = GetTypeHash(NodeSettings->GetNodeDisplayName());
    CacheKey += FString::Printf(TEXT("%u_"), SettingsHash);
    
    // Add input data hash
    if (Context->InputData.TaggedData.Num() > 0)
    {
        uint32 InputHash = 0;
        for (const FPCGTaggedData& TaggedData : Context->InputData.TaggedData)
        {
            if (TaggedData.Data)
            {
                InputHash = HashCombine(InputHash, GetTypeHash(TaggedData.Data->GetUniqueID()));
                InputHash = HashCombine(InputHash, GetTypeHash(TaggedData.Pin));
            }
        }
        CacheKey += FString::Printf(TEXT("%u_"), InputHash);
    }
    
    // Add execution parameters hash
    uint32 ParamsHash = GetTypeHash(NodeSettings->bCacheResults);
    ParamsHash = HashCombine(ParamsHash, GetTypeHash(NodeSettings->bAllowParallelExecution));
    CacheKey += FString::Printf(TEXT("%u"), ParamsHash);
    
    return CacheKey;
}

// UAuracronPCGNodeRegistry implementation

UAuracronPCGNodeRegistry::UAuracronPCGNodeRegistry()
{
    RegisteredNodes.Empty();
    NodeMetadataMap.Empty();
    
    InitializeRegistry();
}

bool UAuracronPCGNodeRegistry::RegisterNode(TSubclassOf<UAuracronPCGNodeSettings> NodeClass)
{
    if (!NodeClass)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot register null node class"));
        return false;
    }

    if (!ValidateNodeRegistration(NodeClass))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Node validation failed: %s"), *NodeClass->GetName());
        return false;
    }

    FScopeLock Lock(&RegistryLock);

    if (RegisteredNodes.Contains(NodeClass))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node already registered: %s"), *NodeClass->GetName());
        return true;
    }

    RegisteredNodes.Add(NodeClass);
    NodeMetadataMap.Add(NodeClass, ExtractNodeMetadata(NodeClass));

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered PCG node: %s"), *NodeClass->GetName());
    return true;
}

bool UAuracronPCGNodeRegistry::UnregisterNode(TSubclassOf<UAuracronPCGNodeSettings> NodeClass)
{
    if (!NodeClass)
    {
        return false;
    }

    FScopeLock Lock(&RegistryLock);

    bool bRemoved = RegisteredNodes.Remove(NodeClass) > 0;
    NodeMetadataMap.Remove(NodeClass);

    if (bRemoved)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered PCG node: %s"), *NodeClass->GetName());
    }

    return bRemoved;
}

bool UAuracronPCGNodeRegistry::IsNodeRegistered(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const
{
    FScopeLock Lock(&RegistryLock);
    return RegisteredNodes.Contains(NodeClass);
}

TArray<TSubclassOf<UAuracronPCGNodeSettings>> UAuracronPCGNodeRegistry::GetAllRegisteredNodes() const
{
    FScopeLock Lock(&RegistryLock);
    return RegisteredNodes;
}

TArray<TSubclassOf<UAuracronPCGNodeSettings>> UAuracronPCGNodeRegistry::GetNodesByCategory(EAuracronPCGNodeCategory Category) const
{
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> CategoryNodes;

    FScopeLock Lock(&RegistryLock);

    for (TSubclassOf<UAuracronPCGNodeSettings> NodeClass : RegisteredNodes)
    {
        if (const FAuracronPCGNodeMetadata* Metadata = NodeMetadataMap.Find(NodeClass))
        {
            if (Metadata->Category == Category)
            {
                CategoryNodes.Add(NodeClass);
            }
        }
    }

    return CategoryNodes;
}

TArray<TSubclassOf<UAuracronPCGNodeSettings>> UAuracronPCGNodeRegistry::SearchNodesByName(const FString& SearchTerm) const
{
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> MatchingNodes;

    if (SearchTerm.IsEmpty())
    {
        return MatchingNodes;
    }

    FScopeLock Lock(&RegistryLock);

    for (TSubclassOf<UAuracronPCGNodeSettings> NodeClass : RegisteredNodes)
    {
        if (const FAuracronPCGNodeMetadata* Metadata = NodeMetadataMap.Find(NodeClass))
        {
            if (Metadata->NodeName.Contains(SearchTerm) ||
                Metadata->NodeDescription.Contains(SearchTerm) ||
                NodeClass->GetName().Contains(SearchTerm))
            {
                MatchingNodes.Add(NodeClass);
            }
        }
    }

    return MatchingNodes;
}

TArray<TSubclassOf<UAuracronPCGNodeSettings>> UAuracronPCGNodeRegistry::GetNodesByTag(const FString& Tag) const
{
    TArray<TSubclassOf<UAuracronPCGNodeSettings>> TaggedNodes;

    if (Tag.IsEmpty())
    {
        return TaggedNodes;
    }

    FScopeLock Lock(&RegistryLock);

    for (TSubclassOf<UAuracronPCGNodeSettings> NodeClass : RegisteredNodes)
    {
        if (const FAuracronPCGNodeMetadata* Metadata = NodeMetadataMap.Find(NodeClass))
        {
            if (Metadata->Tags.Contains(Tag))
            {
                TaggedNodes.Add(NodeClass);
            }
        }
    }

    return TaggedNodes;
}

FAuracronPCGNodeMetadata UAuracronPCGNodeRegistry::GetNodeMetadata(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const
{
    FScopeLock Lock(&RegistryLock);

    if (const FAuracronPCGNodeMetadata* Metadata = NodeMetadataMap.Find(NodeClass))
    {
        return *Metadata;
    }

    return FAuracronPCGNodeMetadata();
}

TMap<EAuracronPCGNodeCategory, int32> UAuracronPCGNodeRegistry::GetNodeCountByCategory() const
{
    TMap<EAuracronPCGNodeCategory, int32> CategoryCounts;

    FScopeLock Lock(&RegistryLock);

    for (const auto& MetadataPair : NodeMetadataMap)
    {
        EAuracronPCGNodeCategory Category = MetadataPair.Value.Category;
        int32& Count = CategoryCounts.FindOrAdd(Category);
        Count++;
    }

    return CategoryCounts;
}

UAuracronPCGNodeSettings* UAuracronPCGNodeRegistry::CreateNodeInstance(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const
{
    if (!NodeClass || !IsNodeRegistered(NodeClass))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot create instance of unregistered node: %s"),
                                  NodeClass ? *NodeClass->GetName() : TEXT("null"));
        return nullptr;
    }

    UAuracronPCGNodeSettings* Instance = NewObject<UAuracronPCGNodeSettings>(GetTransientPackage(), NodeClass);
    if (Instance)
    {
        AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Created node instance: %s"), *NodeClass->GetName());
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create node instance: %s"), *NodeClass->GetName());
    }

    return Instance;
}

void UAuracronPCGNodeRegistry::RefreshRegistry()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Refreshing PCG node registry"));

    FScopeLock Lock(&RegistryLock);

    // Clear existing data
    RegisteredNodes.Empty();
    NodeMetadataMap.Empty();

    // Re-discover nodes
    DiscoverBuiltInNodes();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registry refreshed - %d nodes registered"), RegisteredNodes.Num());
}

void UAuracronPCGNodeRegistry::ClearRegistry()
{
    FScopeLock Lock(&RegistryLock);

    RegisteredNodes.Empty();
    NodeMetadataMap.Empty();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG node registry cleared"));
}

bool UAuracronPCGNodeRegistry::ValidateNodeClass(TSubclassOf<UAuracronPCGNodeSettings> NodeClass, TArray<FString>& ValidationErrors) const
{
    ValidationErrors.Empty();

    if (!NodeClass)
    {
        ValidationErrors.Add(TEXT("Node class is null"));
        return false;
    }

    // Check if it's a valid subclass
    if (!NodeClass->IsChildOf(UAuracronPCGNodeSettings::StaticClass()))
    {
        ValidationErrors.Add(TEXT("Node class must inherit from UAuracronPCGNodeSettings"));
        return false;
    }

    // Create temporary instance for validation
    UAuracronPCGNodeSettings* TempInstance = NewObject<UAuracronPCGNodeSettings>(GetTransientPackage(), NodeClass);
    if (TempInstance)
    {
        TArray<FString> NodeValidationErrors;
        if (!TempInstance->ValidateNodeSettings(NodeValidationErrors))
        {
            ValidationErrors.Append(NodeValidationErrors);
        }
    }
    else
    {
        ValidationErrors.Add(TEXT("Failed to create temporary instance for validation"));
    }

    return ValidationErrors.Num() == 0;
}

UAuracronPCGNodeRegistry* UAuracronPCGNodeRegistry::GetGlobalRegistry()
{
    if (!GlobalRegistryInstance)
    {
        GlobalRegistryInstance = NewObject<UAuracronPCGNodeRegistry>();
        GlobalRegistryInstance->AddToRoot(); // Prevent garbage collection
    }
    return GlobalRegistryInstance;
}

void UAuracronPCGNodeRegistry::InitializeRegistry()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Initializing PCG node registry"));

    // Discover built-in nodes
    DiscoverBuiltInNodes();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG node registry initialized with %d nodes"), RegisteredNodes.Num());
}

void UAuracronPCGNodeRegistry::DiscoverBuiltInNodes()
{
    // Real discovery and registration of built-in AURACRON nodes using UE5.6 reflection
    AURACRON_PCG_LOG_ELEMENTS(Verbose, TEXT("Discovering built-in PCG nodes"));

    DiscoverAndRegisterBuiltInNodes();
}

bool UAuracronPCGNodeRegistry::ValidateNodeRegistration(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const
{
    TArray<FString> ValidationErrors;
    return ValidateNodeClass(NodeClass, ValidationErrors);
}

FAuracronPCGNodeMetadata UAuracronPCGNodeRegistry::ExtractNodeMetadata(TSubclassOf<UAuracronPCGNodeSettings> NodeClass) const
{
    FAuracronPCGNodeMetadata Metadata;

    if (!NodeClass)
    {
        return Metadata;
    }

    // Create temporary instance to extract metadata
    UAuracronPCGNodeSettings* TempInstance = NewObject<UAuracronPCGNodeSettings>(GetTransientPackage(), NodeClass);
    if (TempInstance)
    {
        Metadata = TempInstance->NodeMetadata;

        // Set defaults if not specified
        if (Metadata.NodeName.IsEmpty())
        {
            Metadata.NodeName = NodeClass->GetName();
        }

        if (Metadata.NodeDescription.IsEmpty())
        {
            Metadata.NodeDescription = FString::Printf(TEXT("Custom PCG node: %s"), *NodeClass->GetName());
        }
    }
    else
    {
        // Fallback metadata
        Metadata.NodeName = NodeClass->GetName();
        Metadata.NodeDescription = FString::Printf(TEXT("PCG node: %s"), *NodeClass->GetName());
    }

    return Metadata;
}

// AuracronPCGNodeUtils namespace implementation

namespace AuracronPCGNodeUtils
{
    TSubclassOf<UAuracronPCGNodeSettings> FindNodeByName(const FString& NodeName)
    {
        UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
        if (!Registry)
        {
            return nullptr;
        }

        TArray<TSubclassOf<UAuracronPCGNodeSettings>> MatchingNodes = Registry->SearchNodesByName(NodeName);

        // Return exact match if found
        for (TSubclassOf<UAuracronPCGNodeSettings> NodeClass : MatchingNodes)
        {
            FAuracronPCGNodeMetadata Metadata = Registry->GetNodeMetadata(NodeClass);
            if (Metadata.NodeName.Equals(NodeName, ESearchCase::IgnoreCase))
            {
                return NodeClass;
            }
        }

        // Return first partial match
        return MatchingNodes.Num() > 0 ? MatchingNodes[0] : nullptr;
    }

    TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetCompatibleNodes(EPCGDataType InputType, EPCGDataType OutputType)
    {
        TArray<TSubclassOf<UAuracronPCGNodeSettings>> CompatibleNodes;

        UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
        if (!Registry)
        {
            return CompatibleNodes;
        }

        TArray<TSubclassOf<UAuracronPCGNodeSettings>> AllNodes = Registry->GetAllRegisteredNodes();

        for (TSubclassOf<UAuracronPCGNodeSettings> NodeClass : AllNodes)
        {
            if (IsNodeCompatibleWithData(NodeClass, InputType))
            {
                CompatibleNodes.Add(NodeClass);
            }
        }

        return CompatibleNodes;
    }

    bool IsNodeCompatibleWithData(TSubclassOf<UAuracronPCGNodeSettings> NodeClass, EPCGDataType DataType)
    {
        if (!NodeClass)
        {
            return false;
        }

        // Create temporary instance to check pin compatibility
        UAuracronPCGNodeSettings* TempInstance = NewObject<UAuracronPCGNodeSettings>(GetTransientPackage(), NodeClass);
        if (!TempInstance)
        {
            return false;
        }

        // Check input pins
        TArray<FPCGPinProperties> InputPins = TempInstance->GetCustomInputPinProperties();
        for (const FPCGPinProperties& Pin : InputPins)
        {
            if ((Pin.AllowedTypes & DataType) != EPCGDataType::None)
            {
                return true;
            }
        }

        return false;
    }

    FString GenerateNodeDocumentation(TSubclassOf<UAuracronPCGNodeSettings> NodeClass)
    {
        if (!NodeClass)
        {
            return TEXT("Invalid node class");
        }

        UAuracronPCGNodeRegistry* Registry = UAuracronPCGNodeRegistry::GetGlobalRegistry();
        if (!Registry)
        {
            return TEXT("Registry not available");
        }

        FAuracronPCGNodeMetadata Metadata = Registry->GetNodeMetadata(NodeClass);

        FString Documentation;
        Documentation += FString::Printf(TEXT("# %s\n\n"), *Metadata.NodeName);
        Documentation += FString::Printf(TEXT("**Description:** %s\n\n"), *Metadata.NodeDescription);
        Documentation += FString::Printf(TEXT("**Category:** %s\n"), *UEnum::GetValueAsString(Metadata.Category));
        Documentation += FString::Printf(TEXT("**Priority:** %s\n"), *UEnum::GetValueAsString(Metadata.Priority));

        if (!Metadata.Author.IsEmpty())
        {
            Documentation += FString::Printf(TEXT("**Author:** %s\n"), *Metadata.Author);
        }

        if (!Metadata.Version.IsEmpty())
        {
            Documentation += FString::Printf(TEXT("**Version:** %s\n"), *Metadata.Version);
        }

        if (Metadata.bIsExperimental)
        {
            Documentation += TEXT("**Status:** Experimental\n");
        }

        if (Metadata.bRequiresGPU)
        {
            Documentation += TEXT("**Requirements:** GPU Support\n");
        }

        if (Metadata.Tags.Num() > 0)
        {
            Documentation += FString::Printf(TEXT("**Tags:** %s\n"), *FString::Join(Metadata.Tags, TEXT(", ")));
        }

        return Documentation;
    }
}
