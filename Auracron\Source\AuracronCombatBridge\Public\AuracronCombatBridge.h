﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge
// IntegraÃ§Ã£o C++ para combate 3D com targeting vertical usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/GameplayAbility.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "GameplayAbilities/Public/GameplayAbilityTargetActor.h"
#include "GameplayAbilities/Public/GameplayAbilityTargetActor_Trace.h"
#include "GameplayAbilities/Public/GameplayAbilityTargetActor_Radius.h"
#include "GameplayAbilities/Public/GameplayAbilityWorldReticle.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "Engine/OverlapResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "FieldSystem/FieldSystemComponent.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "MetasoundSource.h"
#include "AuracronCombatBridge.generated.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UGameplayAbilityTargetActor;
class UGameplayAbilityWorldReticle;
class UFieldSystemComponent;
class UGeometryCollectionComponent;
class UNiagaraSystem;
class UNiagaraComponent;

/**
 * EnumeraÃ§Ã£o para tipos de dano
 */
UENUM(BlueprintType)
enum class EAuracronDamageType : uint8
{
    None            UMETA(DisplayName = "None"),
    Physical        UMETA(DisplayName = "Physical"),
    Magical         UMETA(DisplayName = "Magical"),
    TrueDamage      UMETA(DisplayName = "True Damage"),
    Healing         UMETA(DisplayName = "Healing"),
    Shield          UMETA(DisplayName = "Shield"),
    Percentage      UMETA(DisplayName = "Percentage"),
    OverTime        UMETA(DisplayName = "Over Time"),
    Area            UMETA(DisplayName = "Area of Effect")
};

/**
 * EnumeraÃ§Ã£o para camadas de combate 3D
 */
UENUM(BlueprintType)
enum class EAuracronCombatLayer : uint8
{
    Surface         UMETA(DisplayName = "Surface Layer"),
    Sky             UMETA(DisplayName = "Sky Layer"),
    Underground     UMETA(DisplayName = "Underground Layer"),
    All             UMETA(DisplayName = "All Layers")
};

/**
 * EnumeraÃ§Ã£o para tipos de targeting
 */
UENUM(BlueprintType)
enum class EAuracronTargetingType : uint8
{
    None                UMETA(DisplayName = "None"),
    SingleTarget        UMETA(DisplayName = "Single Target"),
    LineTrace           UMETA(DisplayName = "Line Trace"),
    AreaOfEffect        UMETA(DisplayName = "Area of Effect"),
    Cone                UMETA(DisplayName = "Cone"),
    Sphere              UMETA(DisplayName = "Sphere"),
    Box                 UMETA(DisplayName = "Box"),
    Cylinder            UMETA(DisplayName = "Cylinder"),
    VerticalColumn      UMETA(DisplayName = "Vertical Column"),
    CrossLayer          UMETA(DisplayName = "Cross Layer"),
    GroundTarget        UMETA(DisplayName = "Ground Target"),
    SkyTarget           UMETA(DisplayName = "Sky Target"),
    UndergroundTarget   UMETA(DisplayName = "Underground Target")
};

/**
 * Estrutura para configuraÃ§Ã£o de dano
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronDamageConfiguration
{
    GENERATED_BODY()

    /** Tipo de dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    EAuracronDamageType DamageType = EAuracronDamageType::Physical;

    /** Valor base do dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float BaseDamage = 100.0f;

    /** Escalonamento com Attack Damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float AttackDamageScaling = 1.0f;

    /** Escalonamento com Ability Power */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float AbilityPowerScaling = 0.0f;

    /** Escalonamento com HP mÃ¡ximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MaxHealthScaling = 0.0f;

    /** Escalonamento com HP atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float CurrentHealthScaling = 0.0f;

    /** Pode causar crÃ­tico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bCanCrit = true;

    /** Ignora armadura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bIgnoresArmor = false;

    /** Ignora resistÃªncia mÃ¡gica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bIgnoresMagicResistance = false;

    /** Penetra shields */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bPenetratesShields = false;

    /** Dano ao longo do tempo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bDamageOverTime = false;

    /** DuraÃ§Ã£o do DoT (se aplicÃ¡vel) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.1", ClampMax = "60.0"))
    float DotDuration = 5.0f;

    /** Intervalo do DoT (se aplicÃ¡vel) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float DotInterval = 1.0f;

    /** Camadas afetadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    TArray<EAuracronCombatLayer> AffectedLayers;
};

/**
 * Estrutura para configuraÃ§Ã£o de targeting 3D
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronTargetingConfiguration
{
    GENERATED_BODY()

    /** Tipo de targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    EAuracronTargetingType TargetingType = EAuracronTargetingType::SingleTarget;

    /** Alcance mÃ¡ximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "50.0", ClampMax = "5000.0"))
    float MaxRange = 800.0f;

    /** Alcance mÃ­nimo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "1000.0"))
    float MinRange = 0.0f;

    /** Raio da Ã¡rea de efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float AreaOfEffectRadius = 300.0f;

    /** Altura da Ã¡rea de efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float AreaOfEffectHeight = 200.0f;

    /** Ã‚ngulo do cone (para targeting cÃ´nico) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "1.0", ClampMax = "360.0"))
    float ConeAngle = 45.0f;

    /** Permite targeting atravÃ©s de camadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bAllowCrossLayerTargeting = false;

    /** Requer line of sight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bRequiresLineOfSight = true;

    /** Ignora obstÃ¡culos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bIgnoresObstacles = false;

    /** Targeting apenas aliados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bTargetAlliesOnly = false;

    /** Targeting apenas inimigos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bTargetEnemiesOnly = true;

    /** Inclui o prÃ³prio caster */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bIncludeSelf = false;

    /** Camadas de targeting permitidas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<EAuracronCombatLayer> AllowedTargetingLayers;

    /** Canais de colisÃ£o para line of sight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<TEnumAsByte<ECollisionChannel>> LineOfSightChannels;

    /** Canais de colisÃ£o para targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<TEnumAsByte<ECollisionChannel>> TargetingChannels;

    /** Offset vertical para targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "-1000.0", ClampMax = "1000.0"))
    float VerticalOffset = 0.0f;

    /** PrecisÃ£o do targeting (0.0 = perfeito, 1.0 = muito impreciso) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TargetingAccuracy = 0.95f;
};

/**
 * Estrutura para resultado de targeting
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronTargetingResult
{
    GENERATED_BODY()

    /** Targeting foi bem-sucedido */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    bool bSuccessful = false;

    /** Atores alvo encontrados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<TObjectPtr<AActor>> TargetActors;

    /** PosiÃ§Ãµes alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<FVector> TargetLocations;

    /** InformaÃ§Ãµes de hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<FHitResult> HitResults;

    /** Camada onde o targeting ocorreu */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    EAuracronCombatLayer TargetLayer = EAuracronCombatLayer::Surface;

    /** DistÃ¢ncia atÃ© o alvo principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result", meta = (ClampMin = "0.0"))
    float DistanceToTarget = 0.0f;

    /** Ã‚ngulo atÃ© o alvo principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result", meta = (ClampMin = "0.0", ClampMax = "360.0"))
    float AngleToTarget = 0.0f;

    /** Tem line of sight para o alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    bool bHasLineOfSight = false;
};

/**
 * Estrutura para configuraÃ§Ã£o de efeitos de combate
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronCombatEffectsConfiguration
{
    GENERATED_BODY()

    /** Efeito de impacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ImpactEffect;

    /** Efeito de projÃ©til */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ProjectileEffect;

    /** Efeito de Ã¡rea */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> AreaEffect;

    /** Efeito de cura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> HealingEffect;

    /** Efeito de shield */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ShieldEffect;

    /** Som de impacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<USoundCue> ImpactSound;

    /** Som de projÃ©til */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<USoundCue> ProjectileSound;

    /** Som de habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UMetaSoundSource> AbilitySound;

    /** DuraÃ§Ã£o dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float EffectDuration = 2.0f;

    /** Escala dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float EffectScale = 1.0f;

    /** Cor dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    FLinearColor EffectColor = FLinearColor::White;

    /** Usar Field System para destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    bool bUseFieldSystemDestruction = false;

    /** ForÃ§a do Field System */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float FieldSystemForce = 1000.0f;

    /** Raio do Field System */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float FieldSystemRadius = 500.0f;
};

/**
 * Classe principal do Bridge para Sistema de Combate 3D Vertical
 * ResponsÃ¡vel pelo gerenciamento completo de combate com targeting vertical
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Combat", meta = (DisplayName = "AURACRON Combat Bridge", BlueprintSpawnableComponent))
class AURACRONCOMBABRIDGE_API UAuracronCombatBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronCombatBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Combat Management ===

    /**
     * Executar targeting 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    FAuracronTargetingResult ExecuteTargeting(const FAuracronTargetingConfiguration& TargetingConfig, const FVector& SourceLocation, const FVector& TargetDirection);

    /**
     * Aplicar dano a um alvo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    bool ApplyDamageToTarget(AActor* TargetActor, const FAuracronDamageConfiguration& DamageConfig, AActor* SourceActor = nullptr);

    /**
     * Aplicar dano em Ã¡rea
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    bool ApplyAreaDamage(const FVector& Location, const FAuracronDamageConfiguration& DamageConfig, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor = nullptr);

    /**
     * Verificar line of sight entre dois pontos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    bool CheckLineOfSight(const FVector& StartLocation, const FVector& EndLocation, const TArray<TEnumAsByte<ECollisionChannel>>& CollisionChannels);

    /**
     * Obter alvos em raio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInRadius(const FVector& Location, float Radius, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Obter alvos em cone
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInCone(const FVector& SourceLocation, const FVector& Direction, float Range, float ConeAngle, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Obter alvos em coluna vertical
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInVerticalColumn(const FVector& Location, float Radius, float Height, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Calcular dano final
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    float CalculateFinalDamage(const FAuracronDamageConfiguration& DamageConfig, AActor* SourceActor, AActor* TargetActor);

    /**
     * Verificar se alvo Ã© vÃ¡lido
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    bool IsValidTarget(AActor* TargetActor, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor = nullptr);

    /**
     * Obter camada de combate de um ator
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Layers", CallInEditor)
    EAuracronCombatLayer GetActorCombatLayer(AActor* Actor) const;

    // === Effects and Destruction ===

    /**
     * Spawnar efeitos de combate
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Effects", CallInEditor)
    bool SpawnCombatEffects(const FVector& Location, const FAuracronCombatEffectsConfiguration& EffectsConfig);

    /**
     * Aplicar Field System para destruiÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Destruction", CallInEditor)
    bool ApplyFieldSystemDestruction(const FVector& Location, float Force, float Radius);

    /**
     * Criar explosÃ£o com Chaos Physics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Destruction", CallInEditor)
    bool CreateChaosExplosion(const FVector& Location, float Force, float Radius, bool bAffectAllLayers = false);

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de combate */
    bool InitializeCombatSystem();

    /** Configurar canais de colisÃ£o */
    bool SetupCollisionChannels();

    /** Executar line trace */
    bool ExecuteLineTrace(const FVector& Start, const FVector& End, FHitResult& OutHit, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Executar sphere trace */
    bool ExecuteSphereTrace(const FVector& Start, const FVector& End, float Radius, TArray<FHitResult>& OutHits, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Executar overlap test */
    bool ExecuteOverlapTest(const FVector& Location, float Radius, TArray<FOverlapResult>& OutOverlaps, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Filtrar alvos por configuraÃ§Ã£o */
    TArray<AActor*> FilterTargetsByConfiguration(const TArray<AActor*>& PotentialTargets, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor);

    /** Validar configuraÃ§Ã£o de targeting */
    bool ValidateTargetingConfiguration(const FAuracronTargetingConfiguration& TargetingConfig) const;

    /** Validar configuraÃ§Ã£o de dano */
    bool ValidateDamageConfiguration(const FAuracronDamageConfiguration& DamageConfig) const;

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes de targeting padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronTargetingConfiguration DefaultTargetingConfig;

    /** ConfiguraÃ§Ãµes de dano padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronDamageConfiguration DefaultDamageConfig;

    /** ConfiguraÃ§Ãµes de efeitos padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronCombatEffectsConfiguration DefaultEffectsConfig;

    /** Camada de combate atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_CurrentCombatLayer)
    EAuracronCombatLayer CurrentCombatLayer = EAuracronCombatLayer::Surface;

    /** Ãšltimo resultado de targeting */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    FAuracronTargetingResult LastTargetingResult;

    /** ReferÃªncia ao AbilitySystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** ReferÃªncia ao FieldSystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UFieldSystemComponent> FieldSystemComponent;

private:
    // === Internal State ===

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** Componentes de efeitos ativos */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveCombatEffects;

    /** Mutex para thread safety */
    mutable FCriticalSection CombatMutex;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_CurrentCombatLayer();

public:
    // === Delegates ===

    /** Delegate chamado quando targeting Ã© executado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTargetingExecuted, FAuracronTargetingResult, Result, FAuracronTargetingConfiguration, Config);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnTargetingExecuted OnTargetingExecuted;

    /** Delegate chamado quando dano Ã© aplicado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnDamageApplied, AActor*, TargetActor, float, DamageAmount, EAuracronDamageType, DamageType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnDamageApplied OnDamageApplied;

    /** Delegate chamado quando camada de combate muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCombatLayerChanged, EAuracronCombatLayer, OldLayer, EAuracronCombatLayer, NewLayer);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnCombatLayerChanged OnCombatLayerChanged;

    /** Delegate chamado quando efeitos sÃ£o spawnados */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCombatEffectsSpawned, FVector, Location, FAuracronCombatEffectsConfiguration, EffectsConfig);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnCombatEffectsSpawned OnCombatEffectsSpawned;
};

