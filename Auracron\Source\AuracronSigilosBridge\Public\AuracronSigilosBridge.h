// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de SÃ­gilos Auracron Bridge
// IntegraÃ§Ã£o C++ para o sistema de fusÃ£o de campeÃµes usando as APIs mais modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/GameplayAbility.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "GameplayAbilities/Public/AttributeSet.h"
#include "GameplayAbilities/Public/GameplayAbilitySet.h"
#include "GameplayAbilities/Public/GameplayAbilitySpec.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "GameplayTasks/Classes/GameplayTask.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponentManager.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"

#include "Materials/MaterialParameterCollection.h"
#include "Particles/ParticleSystem.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"
#include "MetasoundSource.h"
#include "AuracronSigilosBridge.generated.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UAttributeSet;
class UNiagaraSystem;
class UNiagaraComponent;
class USoundCue;
class UMetasoundSource;
class UMaterialParameterCollection;
class UCurveFloat;
struct FAuracronSigiloConfiguration;
struct FAuracronSigiloProperties;
struct FAuracronSigiloPassiveBonuses;
struct FAuracronSigiloExclusiveAbility;
struct FAuracronSigiloVisualEffects;
struct FAuracronSigiloAudioEffects;

/**
 * EnumeraÃ§Ã£o para tipos de SÃ­gilos Auracron
 */
UENUM(BlueprintType)
enum class EAuracronSigiloType : uint8
{
    None        UMETA(DisplayName = "None"),
    Aegis       UMETA(DisplayName = "Aegis (Tank)"),
    Ruin        UMETA(DisplayName = "Ruin (Damage)"),
    Vesper      UMETA(DisplayName = "Vesper (Utility)")
};

/**
 * EnumeraÃ§Ã£o para estados de fusÃ£o do Sigilo
 */
UENUM(BlueprintType)
enum class EAuracronSigiloFusionState : uint8
{
    Inactive        UMETA(DisplayName = "Inactive"),
    Charging        UMETA(DisplayName = "Charging"),
    Active          UMETA(DisplayName = "Active"),
    Cooldown        UMETA(DisplayName = "Cooldown"),
    Reforging       UMETA(DisplayName = "Reforging")
};

/**
 * Estrutura para propriedades base de um Sigilo
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloProperties
{
    GENERATED_BODY()

    /** Tipo do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    /** Nome do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FText SigiloName;

    /** DescriÃ§Ã£o do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FText SigiloDescription;

    /** Ãcone do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    TSoftObjectPtr<UTexture2D> SigiloIcon;

    /** Cor primÃ¡ria do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundÃ¡ria do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FLinearColor SecondaryColor = FLinearColor::Gray;

    /** Tempo para fusÃ£o (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "1.0", ClampMax = "600.0"))
    float FusionTime = 360.0f; // 6 minutos

    /** Cooldown para re-forjamento (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "30.0", ClampMax = "300.0"))
    float ReforgeCooldown = 120.0f; // 2 minutos

    /** NÃ­vel do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "1", ClampMax = "20"))
    int32 SigiloLevel = 1;

    /** ExperiÃªncia do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "0"))
    int32 SigiloExperience = 0;

    /** ExperiÃªncia necessÃ¡ria para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "100"))
    int32 ExperienceToNextLevel = 1000;
};

/**
 * Estrutura para bÃ´nus passivos do Sigilo
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloPassiveBonuses
{
    GENERATED_BODY()

    /** BÃ´nus de HP (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float HealthBonus = 0.0f;

    /** BÃ´nus de Armadura (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ArmorBonus = 0.0f;

    /** BÃ´nus de Ataque (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AttackBonus = 0.0f;

    /** BÃ´nus de Poder de Habilidade (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AbilityPowerBonus = 0.0f;

    /** BÃ´nus de Velocidade de Movimento (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MovementSpeedBonus = 0.0f;

    /** ReduÃ§Ã£o de Cooldown (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "0.5"))
    float CooldownReduction = 0.0f;

    /** RegeneraÃ§Ã£o de Mana (por segundo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float ManaRegeneration = 0.0f;

    /** ResistÃªncia a Dano (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "0.8"))
    float DamageResistance = 0.0f;
};

/**
 * Estrutura para habilidade exclusiva do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloExclusiveAbility
{
    GENERATED_BODY()

    /** Classe da habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    TSoftClassPtr<UGameplayAbility> AbilityClass;

    /** Nome da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FText AbilityName;

    /** Descrição da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FText AbilityDescription;

    /** Ícone da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    TSoftObjectPtr<UTexture2D> AbilityIcon;

    /** Cooldown da habilidade (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Cooldown = 60.0f;

    /** Custo de mana da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float ManaCost = 100.0f;

    /** Duração da habilidade (segundos, 0 = instantânea) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Duration = 0.0f;

    /** Alcance da habilidade (0 = self-target) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Range = 0.0f;

    /** GameplayTags necessários para ativar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FGameplayTagContainer RequiredTags;

    /** GameplayTags bloqueados durante uso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FGameplayTagContainer BlockedTags;
};

/**
 * Estrutura para efeitos visuais do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloVisualEffects
{
    GENERATED_BODY()

    /** Sistema de partículas de fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> FusionParticleSystem;

    /** Sistema de partículas de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> ActivationParticleSystem;

    /** Sistema de partículas de habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> AbilityParticleSystem;

    /** Sistema de partículas de re-forjamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> ReforgeParticleSystem;

    /** Material de overlay durante fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UMaterialInterface> FusionOverlayMaterial;

    /** Cor primária do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundária do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    FLinearColor SecondaryColor = FLinearColor::Black;

    /** Intensidade dos efeitos visuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float EffectIntensity = 1.0f;
};

/**
 * Estrutura para efeitos sonoros do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloAudioEffects
{
    GENERATED_BODY()

    /** Som de fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<UMetaSoundSource> FusionSound;

    /** Som de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<UMetaSoundSource> ActivationSound;

    /** Som de habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<UMetaSoundSource> AbilitySound;

    /** Som de re-forjamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<UMetaSoundSource> ReforgeSound;

    /** Volume dos efeitos sonoros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float EffectVolume = 1.0f;
};

/**
 * Estrutura completa de configuração de um Sigilo (moved before FAuracronSigiloConfigurationEntry)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloConfiguration
{
    GENERATED_BODY()

    /** Propriedades básicas do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloProperties Properties;

    /** Bônus passivos do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloPassiveBonuses PassiveBonuses;

    /** Habilidade exclusiva do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloExclusiveAbility ExclusiveAbility;

    /** Efeitos visuais do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloVisualEffects VisualEffects;

    /** Efeitos sonoros do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloAudioEffects AudioEffects;

    /** GameplayEffects aplicados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    TArray<TSoftClassPtr<UGameplayEffect>> FusionGameplayEffects;

    /** GameplayTags adicionados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FGameplayTagContainer FusionTags;

    /** GameplayTags bloqueados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FGameplayTagContainer BlockedTags;
};

/**
 * Estrutura para entrada de configuração de Sigilo (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloConfigurationEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloConfiguration Configuration;

    FAuracronSigiloConfigurationEntry()
    {
        SigiloType = EAuracronSigiloType::None;
    }

    FAuracronSigiloConfigurationEntry(EAuracronSigiloType InSigiloType, const FAuracronSigiloConfiguration& InConfiguration)
        : SigiloType(InSigiloType), Configuration(InConfiguration)
    {
    }
};

// FAuracronSigiloExclusiveAbility moved to before FAuracronSigiloConfiguration

// Incorrect FAuracronSigiloVisualEffects structure removed - correct definition is above
// All orphaned content removed




    /** Ãcone da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    TSoftObjectPtr<UTexture2D> AbilityIcon;

    /** Cooldown da habilidade (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float AbilityCooldown = 30.0f;

    /** Custo de mana da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0", ClampMax = "1000.0"))
    float ManaCost = 100.0f;

    /** DuraÃ§Ã£o da habilidade (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.1", ClampMax = "60.0"))
    float AbilityDuration = 3.0f;

    /** Alcance da habilidade (em unidades) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0", ClampMax = "5000.0"))
    float AbilityRange = 800.0f;

    /** Ãrea de efeito (em unidades) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float AreaOfEffect = 300.0f;
};

// Duplicate structures removed - definitions moved to before FAuracronSigiloConfiguration





// FAuracronSigiloConfiguration moved to before FAuracronSigiloConfigurationEntry

/**
 * Classe principal do Bridge para Sistema de SÃ­gilos Auracron
 * ResponsÃ¡vel pela fusÃ£o de campeÃµes e gerenciamento de habilidades alternativas
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Sigilos", meta = (DisplayName = "AURACRON Sigilos Bridge", BlueprintSpawnableComponent))
class AURACRONSIGILOSBRIDGE_API UAuracronSigilosBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronSigilosBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Sigilo Management ===

    /**
     * Selecionar Sigilo durante a fase de seleÃ§Ã£o de campeÃµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Selection", CallInEditor)
    bool SelectSigilo(EAuracronSigiloType SigiloType);

    /**
     * Iniciar processo de fusÃ£o do Sigilo (aos 6 minutos)
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool StartSigiloFusion();

    /**
     * Completar fusÃ£o do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool CompleteSigiloFusion();

    /**
     * Re-forjar Sigilo no Nexus
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Reforge", CallInEditor)
    bool ReforgeSigilo(EAuracronSigiloType NewSigiloType);

    /**
     * Cancelar fusÃ£o em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool CancelSigiloFusion();

    // === Ability Management ===

    /**
     * Ativar habilidade exclusiva do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Abilities", CallInEditor)
    bool ActivateExclusiveAbility();

    /**
     * Obter Ã¡rvore de habilidades alternativas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Abilities", CallInEditor)
    TArray<TSubclassOf<UGameplayAbility>> GetAlternativeAbilityTree() const;

    /**
     * Aplicar bÃ´nus passivos do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Bonuses", CallInEditor)
    bool ApplyPassiveBonuses();

    /**
     * Remover bÃ´nus passivos do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Bonuses", CallInEditor)
    bool RemovePassiveBonuses();

    // === State Management ===

    /**
     * Obter estado atual da fusÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    EAuracronSigiloFusionState GetFusionState() const { return CurrentFusionState; }

    /**
     * Obter Sigilo atualmente selecionado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    EAuracronSigiloType GetSelectedSigilo() const { return SelectedSigiloType; }

    /**
     * Obter tempo restante para fusÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    float GetTimeToFusion() const;

    /**
     * Obter tempo restante de cooldown para re-forjamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    float GetReforgeCooldownRemaining() const;

    /**
     * Verificar se pode re-forjar
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    bool CanReforge() const;

    // === Configuration Management ===

    /**
     * Obter configuraÃ§Ã£o de um Sigilo especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    FAuracronSigiloConfiguration GetSigiloConfiguration(EAuracronSigiloType SigiloType) const;

    /**
     * Definir configuraÃ§Ã£o de um Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    void SetSigiloConfiguration(EAuracronSigiloType SigiloType, const FAuracronSigiloConfiguration& Configuration);

    /**
     * Carregar configuraÃ§Ãµes padrÃ£o dos SÃ­gilos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    bool LoadDefaultSigiloConfigurations();

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de SÃ­gilos */
    bool InitializeSigiloSystem();

    /** Aplicar GameplayEffects do Sigilo */
    bool ApplySigiloGameplayEffects(const FAuracronSigiloConfiguration& Configuration);

    /** Remover GameplayEffects do Sigilo */
    bool RemoveSigiloGameplayEffects();

    /** Atualizar efeitos visuais */
    bool UpdateVisualEffects(const FAuracronSigiloVisualEffects& VisualEffects);

    /** Reproduzir efeitos sonoros */
    bool PlayAudioEffects(const FAuracronSigiloAudioEffects& AudioEffects);

    /** Validar seleÃ§Ã£o de Sigilo */
    bool ValidateSigiloSelection(EAuracronSigiloType SigiloType) const;

    /** Processar fusÃ£o em andamento */
    void ProcessFusion(float DeltaTime);

    /** Processar cooldown de re-forjamento */
    void ProcessReforgeCooldown(float DeltaTime);

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes dos SÃ­gilos disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronSigiloConfigurationEntry> SigiloConfigurations;

    /** Sigilo atualmente selecionado */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_SelectedSigiloType)
    EAuracronSigiloType SelectedSigiloType = EAuracronSigiloType::None;

    /** Estado atual da fusÃ£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_FusionState)
    EAuracronSigiloFusionState CurrentFusionState = EAuracronSigiloFusionState::Inactive;

    /** Tempo de jogo quando a fusÃ£o foi iniciada */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float FusionStartTime = 0.0f;

    /** Tempo quando o Ãºltimo re-forjamento foi feito */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float LastReforgeTime = -1000.0f;

    /** Componente de timeline para animaÃ§Ãµes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UTimelineComponent> FusionTimeline;

    /** ReferÃªncia ao AbilitySystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

private:
    // === Internal State ===

    /** GameplayEffects ativos do Sigilo */
    TArray<FActiveGameplayEffectHandle> ActiveSigiloEffects;

    /** Componentes de efeitos visuais */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveVisualEffects;

    /** Componentes de Ã¡udio */
    TArray<TObjectPtr<UAudioComponent>> ActiveAudioComponents;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** FusÃ£o disponÃ­vel (apÃ³s 6 minutos) */
    bool bFusionAvailable = false;

    /** Timer para verificaÃ§Ãµes periÃ³dicas */
    FTimerHandle SystemUpdateTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection SigiloMutex;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_SelectedSigiloType();

    UFUNCTION()
    void OnRep_FusionState();

public:
    // === Delegates ===

    /** Delegate chamado quando Sigilo Ã© selecionado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigiloSelected, EAuracronSigiloType, SigiloType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnSigiloSelected OnSigiloSelected;

    /** Delegate chamado quando fusÃ£o Ã© iniciada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnFusionStarted);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnFusionStarted OnFusionStarted;

    /** Delegate chamado quando fusÃ£o Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFusionCompleted, EAuracronSigiloType, SigiloType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnFusionCompleted OnFusionCompleted;

    /** Delegate chamado quando Sigilo Ã© re-forjado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigiloReforged, EAuracronSigiloType, OldSigilo, EAuracronSigiloType, NewSigilo);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnSigiloReforged OnSigiloReforged;
};

