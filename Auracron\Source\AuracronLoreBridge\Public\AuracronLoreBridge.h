﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Lore DinÃ¢mico Bridge
// IntegraÃ§Ã£o C++ para lore interativo usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ModularGameplay/Public/GameFrameworkComponent.h"
#include "CommonActivatableWidget.h"
#include "CommonUserWidget.h"
#include "Components/Widget.h"
#include "Blueprint/UserWidget.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Internationalization/Text.h"
#include "Internationalization/Internationalization.h"
#include "LevelSequence.h"
#include "LevelSequenceActor.h"
#include "LevelSequencePlayer.h"
#include "AuracronLoreBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de lore
 */
UENUM(BlueprintType)
enum class EAuracronLoreType : uint8
{
    None                UMETA(DisplayName = "None"),
    ChampionBackstory   UMETA(DisplayName = "Champion Backstory"),
    RealmHistory        UMETA(DisplayName = "Realm History"),
    SigiloOrigins       UMETA(DisplayName = "Sigilo Origins"),
    WorldLore           UMETA(DisplayName = "World Lore"),
    EventLore           UMETA(DisplayName = "Event Lore"),
    ItemLore            UMETA(DisplayName = "Item Lore"),
    LocationLore        UMETA(DisplayName = "Location Lore"),
    FactionLore         UMETA(DisplayName = "Faction Lore"),
    MythologyLore       UMETA(DisplayName = "Mythology Lore"),
    SecretLore          UMETA(DisplayName = "Secret Lore")
};

/**
 * EnumeraÃ§Ã£o para raridade de lore
 */
UENUM(BlueprintType)
enum class EAuracronLoreRarity : uint8
{
    Common              UMETA(DisplayName = "Common"),
    Uncommon            UMETA(DisplayName = "Uncommon"),
    Rare                UMETA(DisplayName = "Rare"),
    Epic                UMETA(DisplayName = "Epic"),
    Legendary           UMETA(DisplayName = "Legendary"),
    Mythic              UMETA(DisplayName = "Mythic"),
    Secret              UMETA(DisplayName = "Secret")
};

/**
 * EnumeraÃ§Ã£o para mÃ©todo de descoberta
 */
UENUM(BlueprintType)
enum class EAuracronLoreDiscoveryMethod : uint8
{
    Automatic           UMETA(DisplayName = "Automatic"),
    Exploration         UMETA(DisplayName = "Exploration"),
    Combat              UMETA(DisplayName = "Combat"),
    Quest               UMETA(DisplayName = "Quest"),
    Achievement         UMETA(DisplayName = "Achievement"),
    Purchase            UMETA(DisplayName = "Purchase"),
    Event               UMETA(DisplayName = "Event"),
    Secret              UMETA(DisplayName = "Secret"),
    Social              UMETA(DisplayName = "Social"),
    Progression         UMETA(DisplayName = "Progression")
};

/**
 * Estrutura para entrada de lore
 */
USTRUCT(BlueprintType)
struct AURACRONLOREBRIDGE_API FAuracronLoreEntry
{
    GENERATED_BODY()

    /** ID Ãºnico da entrada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FString LoreID;

    /** TÃ­tulo da entrada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FText LoreTitle;

    /** ConteÃºdo da entrada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FText LoreContent;

    /** Resumo da entrada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FText LoreSummary;

    /** Tipo de lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    EAuracronLoreType LoreType = EAuracronLoreType::WorldLore;

    /** Raridade do lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    EAuracronLoreRarity LoreRarity = EAuracronLoreRarity::Common;

    /** MÃ©todo de descoberta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    EAuracronLoreDiscoveryMethod DiscoveryMethod = EAuracronLoreDiscoveryMethod::Automatic;

    /** Foi descoberto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    bool bIsDiscovered = false;

    /** Foi lido */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    bool bIsRead = false;

    /** Data de descoberta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FDateTime DiscoveryDate;

    /** Data de leitura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FDateTime ReadDate;

    /** Imagem do lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TSoftObjectPtr<UTexture2D> LoreImage;

    /** Ãudio do lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TSoftObjectPtr<USoundBase> LoreAudio;

    /** SequÃªncia cinemÃ¡tica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TSoftObjectPtr<ULevelSequence> LoreCinematic;

    /** LocalizaÃ§Ã£o relacionada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FVector RelatedLocation = FVector::ZeroVector;

    /** Realm relacionado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry", meta = (ClampMin = "0", ClampMax = "2"))
    int32 RelatedRealm = 0;

    /** CampeÃ£o relacionado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FString RelatedChampion;

    /** Entradas relacionadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TArray<FString> RelatedEntries;

    /** PrÃ©-requisitos para descoberta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TArray<FString> DiscoveryPrerequisites;

    /** CondiÃ§Ãµes de descoberta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TMap<FString, FString> DiscoveryConditions;

    /** Recompensas por descoberta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TMap<FString, int32> DiscoveryRewards;

    /** Pontos de lore concedidos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 LorePoints = 10;

    /** ExperiÃªncia concedida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry", meta = (ClampMin = "0", ClampMax = "10000"))
    int32 ExperienceReward = 50;

    /** Tags do lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    FGameplayTagContainer LoreTags;

    /** Metadados customizados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Entry")
    TMap<FString, FString> CustomMetadata;
};

/**
 * Estrutura para coleÃ§Ã£o de lore
 */
USTRUCT(BlueprintType)
struct AURACRONLOREBRIDGE_API FAuracronLoreCollection
{
    GENERATED_BODY()

    /** ID da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FString CollectionID;

    /** Nome da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FText CollectionName;

    /** DescriÃ§Ã£o da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FText CollectionDescription;

    /** Entradas na coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    TArray<FString> LoreEntryIDs;

    /** ColeÃ§Ã£o foi completada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    bool bIsCompleted = false;

    /** Progresso da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float CompletionProgress = 0.0f;

    /** Recompensa de conclusÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    TMap<FString, int32> CompletionRewards;

    /** Ãcone da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    TSoftObjectPtr<UTexture2D> CollectionIcon;

    /** Cor da coleÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FLinearColor CollectionColor = FLinearColor::White;

    /** Ordem de exibiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection", meta = (ClampMin = "0", ClampMax = "1000"))
    int32 DisplayOrder = 0;

    /** ColeÃ§Ã£o Ã© secreta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    bool bIsSecret = false;

    /** Data de criaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FDateTime CreationDate;

    /** Data de conclusÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lore Collection")
    FDateTime CompletionDate;
};

/**
 * Classe principal do Bridge para Sistema de Lore DinÃ¢mico
 * ResponsÃ¡vel pelo gerenciamento completo de lore interativo
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Lore", meta = (DisplayName = "AURACRON Lore Bridge", BlueprintSpawnableComponent))
class AURACRONLOREBRIDGE_API UAuracronLoreBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronLoreBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Lore Management ===

    /**
     * Descobrir entrada de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Discovery", CallInEditor)
    bool DiscoverLoreEntry(const FString& LoreID, EAuracronLoreDiscoveryMethod DiscoveryMethod);

    /**
     * Ler entrada de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Reading", CallInEditor)
    bool ReadLoreEntry(const FString& LoreID);

    /**
     * Obter entrada de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Access", CallInEditor)
    FAuracronLoreEntry GetLoreEntry(const FString& LoreID) const;

    /**
     * Obter todas as entradas descobertas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Access", CallInEditor)
    TArray<FAuracronLoreEntry> GetDiscoveredLoreEntries() const;

    /**
     * Verificar se lore foi descoberto
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Access", CallInEditor)
    bool IsLoreDiscovered(const FString& LoreID) const;

    // === Collection Management ===

    /**
     * Criar coleÃ§Ã£o de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Collections", CallInEditor)
    bool CreateLoreCollection(const FAuracronLoreCollection& CollectionConfig);

    /**
     * Obter coleÃ§Ã£o de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Collections", CallInEditor)
    FAuracronLoreCollection GetLoreCollection(const FString& CollectionID) const;

    /**
     * Obter todas as coleÃ§Ãµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Collections", CallInEditor)
    TArray<FAuracronLoreCollection> GetAllLoreCollections() const;

    /**
     * Verificar se coleÃ§Ã£o foi completada
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Collections", CallInEditor)
    bool IsCollectionCompleted(const FString& CollectionID) const;

    /**
     * Atualizar progresso da coleÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Collections", CallInEditor)
    bool UpdateCollectionProgress(const FString& CollectionID);

    // === Interactive Lore ===

    /**
     * Reproduzir cinemÃ¡tica de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Interactive", CallInEditor)
    bool PlayLoreCinematic(const FString& LoreID);

    /**
     * Reproduzir Ã¡udio de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Interactive", CallInEditor)
    bool PlayLoreAudio(const FString& LoreID);

    /**
     * Mostrar UI de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Interactive", CallInEditor)
    bool ShowLoreUI(const FString& LoreID);

    /**
     * Esconder UI de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Interactive", CallInEditor)
    bool HideLoreUI();

    // === Discovery System ===

    /**
     * Verificar condiÃ§Ãµes de descoberta
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Discovery", CallInEditor)
    bool CheckDiscoveryConditions(const FString& LoreID, const TMap<FString, FString>& CurrentConditions);

    /**
     * Descobrir lore por localizaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Discovery", CallInEditor)
    TArray<FString> DiscoverLoreByLocation(const FVector& Location, float Radius = 500.0f);

    /**
     * Descobrir lore por aÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Discovery", CallInEditor)
    TArray<FString> DiscoverLoreByAction(const FString& ActionType, const TMap<FString, FString>& ActionData);

    /**
     * Descobrir lore por campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Discovery", CallInEditor)
    TArray<FString> DiscoverLoreByChampion(const FString& ChampionID);

    // === Analytics ===

    /**
     * Rastrear descoberta de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Analytics", CallInEditor)
    bool TrackLoreDiscovery(const FString& LoreID, EAuracronLoreDiscoveryMethod Method);

    /**
     * Rastrear leitura de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Analytics", CallInEditor)
    bool TrackLoreReading(const FString& LoreID, float ReadingTime);

    /**
     * Obter estatÃ­sticas de lore
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Lore|Analytics", CallInEditor)
    TMap<FString, float> GetLoreStatistics() const;

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de lore */
    bool InitializeLoreSystem();
    
    /** Carregar entradas de lore */
    bool LoadLoreEntries();
    
    /** Processar descobertas */
    void ProcessDiscoveries(float DeltaTime);
    
    /** Validar entrada de lore */
    bool ValidateLoreEntry(const FAuracronLoreEntry& Entry) const;
    
    /** Conceder recompensas de descoberta */
    bool GrantDiscoveryRewards(const FAuracronLoreEntry& Entry);

public:
    // === Configuration Properties ===

    /** Todas as entradas de lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FAuracronLoreEntry> AllLoreEntries;

    /** ColeÃ§Ãµes de lore */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FAuracronLoreCollection> LoreCollections;

    /** Entradas descobertas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FString> DiscoveredLoreIDs;

    /** Entradas lidas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FString> ReadLoreIDs;

    /** Pontos de lore totais */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    int32 TotalLorePoints = 0;

    /** Widget de lore atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UUserWidget> CurrentLoreWidget;

    /** Player de sequÃªncia ativo */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<ULevelSequencePlayer> ActiveSequencePlayer;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para descobertas */
    FTimerHandle DiscoveryTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection LoreMutex;
    
    /** Cache de descobertas */
    TMap<FString, FDateTime> DiscoveryCache;

public:
    // === Delegates ===
    
    /** Delegate chamado quando lore Ã© descoberto */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLoreDiscovered, FString, LoreID, EAuracronLoreDiscoveryMethod, DiscoveryMethod);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Lore|Events")
    FOnLoreDiscovered OnLoreDiscovered;
    
    /** Delegate chamado quando lore Ã© lido */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLoreRead, FString, LoreID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Lore|Events")
    FOnLoreRead OnLoreRead;
    
    /** Delegate chamado quando coleÃ§Ã£o Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCollectionCompleted, FString, CollectionID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Lore|Events")
    FOnCollectionCompleted OnCollectionCompleted;
};

