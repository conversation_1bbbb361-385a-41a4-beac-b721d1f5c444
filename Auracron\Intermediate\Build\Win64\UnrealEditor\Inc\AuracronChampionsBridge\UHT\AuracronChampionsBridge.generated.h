// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronChampionsBridge.h"

#ifdef AURACRONCHAMPIONSBRIDGE_AuracronChampionsBridge_generated_h
#error "AuracronChampionsBridge.generated.h already included, missing '#pragma once' in AuracronChampionsBridge.h"
#endif
#define AURACRONCHAMPIONSBRIDGE_AuracronChampionsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronChampionState : uint8;
struct FAuracronChampionBaseAttributes;
struct FAuracronChampionConfiguration;

// ********** Begin ScriptStruct FAuracronChampionBaseAttributes ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_111_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionBaseAttributes;
// ********** End ScriptStruct FAuracronChampionBaseAttributes *************************************

// ********** Begin ScriptStruct FAuracronChampionAbilities ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_204_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionAbilities;
// ********** End ScriptStruct FAuracronChampionAbilities ******************************************

// ********** Begin ScriptStruct FAuracronChampionVisualConfig *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_253_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionVisualConfig;
// ********** End ScriptStruct FAuracronChampionVisualConfig ***************************************

// ********** Begin ScriptStruct FAuracronChampionMovementConfig ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_326_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionMovementConfig;
// ********** End ScriptStruct FAuracronChampionMovementConfig *************************************

// ********** Begin ScriptStruct FAuracronChampionInputConfig **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_395_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionInputConfig;
// ********** End ScriptStruct FAuracronChampionInputConfig ****************************************

// ********** Begin ScriptStruct FAuracronChampionConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_464_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionConfiguration;
// ********** End ScriptStruct FAuracronChampionConfiguration **************************************

// ********** Begin ScriptStruct FAuracronChampionConfigurationEntry *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_541_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChampionConfigurationEntry;
// ********** End ScriptStruct FAuracronChampionConfigurationEntry *********************************

// ********** Begin Delegate FOnChampionSelected ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_911_DELEGATE \
static void FOnChampionSelected_DelegateWrapper(const FMulticastScriptDelegate& OnChampionSelected, const FString& ChampionID);


// ********** End Delegate FOnChampionSelected *****************************************************

// ********** Begin Delegate FOnChampionSpawned ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_916_DELEGATE \
static void FOnChampionSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnChampionSpawned, const FString& ChampionID);


// ********** End Delegate FOnChampionSpawned ******************************************************

// ********** Begin Delegate FOnChampionDied *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_921_DELEGATE \
static void FOnChampionDied_DelegateWrapper(const FMulticastScriptDelegate& OnChampionDied, const FString& ChampionID);


// ********** End Delegate FOnChampionDied *********************************************************

// ********** Begin Delegate FOnChampionLevelUp ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_926_DELEGATE \
static void FOnChampionLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnChampionLevelUp, const FString& ChampionID, int32 NewLevel);


// ********** End Delegate FOnChampionLevelUp ******************************************************

// ********** Begin Delegate FOnAbilityActivated ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_931_DELEGATE \
static void FOnAbilityActivated_DelegateWrapper(const FMulticastScriptDelegate& OnAbilityActivated, const FString& ChampionID, const FString& AbilitySlot);


// ********** End Delegate FOnAbilityActivated *****************************************************

// ********** Begin Delegate FOnAttributesUpdated **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_936_DELEGATE \
static void FOnAttributesUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnAttributesUpdated, const FString& ChampionID, FAuracronChampionBaseAttributes NewAttributes);


// ********** End Delegate FOnAttributesUpdated ****************************************************

// ********** Begin Class UAuracronChampionsBridge *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_CurrentAttributes); \
	DECLARE_FUNCTION(execOnRep_ChampionState); \
	DECLARE_FUNCTION(execOnRep_SelectedChampionID); \
	DECLARE_FUNCTION(execGetAvailableChampions); \
	DECLARE_FUNCTION(execLoadDefaultChampionConfigurations); \
	DECLARE_FUNCTION(execSetChampionConfiguration); \
	DECLARE_FUNCTION(execGetChampionConfiguration); \
	DECLARE_FUNCTION(execProcessLookInput); \
	DECLARE_FUNCTION(execProcessMovementInput); \
	DECLARE_FUNCTION(execSetupChampionInput); \
	DECLARE_FUNCTION(execResetToDefaultAppearance); \
	DECLARE_FUNCTION(execApplyAlternativeSkin); \
	DECLARE_FUNCTION(execCustomizeFacialAppearance); \
	DECLARE_FUNCTION(execApplyMetaHumanConfiguration); \
	DECLARE_FUNCTION(execCalculateAttributesForLevel); \
	DECLARE_FUNCTION(execGetExperienceToNextLevel); \
	DECLARE_FUNCTION(execLevelUp); \
	DECLARE_FUNCTION(execGainExperience); \
	DECLARE_FUNCTION(execModifyAttribute); \
	DECLARE_FUNCTION(execGetManaPercentage); \
	DECLARE_FUNCTION(execGetHealthPercentage); \
	DECLARE_FUNCTION(execRestoreMana); \
	DECLARE_FUNCTION(execHealChampion); \
	DECLARE_FUNCTION(execApplyDamage); \
	DECLARE_FUNCTION(execGetAbilityLevel); \
	DECLARE_FUNCTION(execIsAbilityAvailable); \
	DECLARE_FUNCTION(execGetAbilityCooldown); \
	DECLARE_FUNCTION(execLevelUpAbility); \
	DECLARE_FUNCTION(execCancelActiveAbility); \
	DECLARE_FUNCTION(execActivateAbility); \
	DECLARE_FUNCTION(execGetChampionState); \
	DECLARE_FUNCTION(execIsChampionAlive); \
	DECLARE_FUNCTION(execGetCurrentChampionConfiguration); \
	DECLARE_FUNCTION(execGetSelectedChampionID); \
	DECLARE_FUNCTION(execDespawnChampion); \
	DECLARE_FUNCTION(execSpawnChampion); \
	DECLARE_FUNCTION(execSelectChampion);


AURACRONCHAMPIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronChampionsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronChampionsBridge(); \
	friend struct Z_Construct_UClass_UAuracronChampionsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONCHAMPIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronChampionsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronChampionsBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronChampionsBridge"), Z_Construct_UClass_UAuracronChampionsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronChampionsBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ChampionConfigurations=NETFIELD_REP_START, \
		SelectedChampionID, \
		CurrentChampionState, \
		CurrentAttributes, \
		NETFIELD_REP_END=CurrentAttributes	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronChampionsBridge(UAuracronChampionsBridge&&) = delete; \
	UAuracronChampionsBridge(const UAuracronChampionsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronChampionsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronChampionsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronChampionsBridge) \
	NO_API virtual ~UAuracronChampionsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_564_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h_567_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronChampionsBridge;

// ********** End Class UAuracronChampionsBridge ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h

// ********** Begin Enum EAuracronChampionType *****************************************************
#define FOREACH_ENUM_EAURACRONCHAMPIONTYPE(op) \
	op(EAuracronChampionType::None) \
	op(EAuracronChampionType::Tank) \
	op(EAuracronChampionType::Damage) \
	op(EAuracronChampionType::Support) \
	op(EAuracronChampionType::Assassin) \
	op(EAuracronChampionType::Mage) \
	op(EAuracronChampionType::Marksman) 

enum class EAuracronChampionType : uint8;
template<> struct TIsUEnumClass<EAuracronChampionType> { enum { Value = true }; };
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionType>();
// ********** End Enum EAuracronChampionType *******************************************************

// ********** Begin Enum EAuracronChampionRarity ***************************************************
#define FOREACH_ENUM_EAURACRONCHAMPIONRARITY(op) \
	op(EAuracronChampionRarity::Common) \
	op(EAuracronChampionRarity::Uncommon) \
	op(EAuracronChampionRarity::Rare) \
	op(EAuracronChampionRarity::Epic) \
	op(EAuracronChampionRarity::Legendary) \
	op(EAuracronChampionRarity::Mythic) 

enum class EAuracronChampionRarity : uint8;
template<> struct TIsUEnumClass<EAuracronChampionRarity> { enum { Value = true }; };
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionRarity>();
// ********** End Enum EAuracronChampionRarity *****************************************************

// ********** Begin Enum EAuracronChampionState ****************************************************
#define FOREACH_ENUM_EAURACRONCHAMPIONSTATE(op) \
	op(EAuracronChampionState::Idle) \
	op(EAuracronChampionState::Moving) \
	op(EAuracronChampionState::Attacking) \
	op(EAuracronChampionState::CastingAbility) \
	op(EAuracronChampionState::Stunned) \
	op(EAuracronChampionState::Dead) \
	op(EAuracronChampionState::Respawning) \
	op(EAuracronChampionState::Channeling) \
	op(EAuracronChampionState::Invisible) \
	op(EAuracronChampionState::Invulnerable) 

enum class EAuracronChampionState : uint8;
template<> struct TIsUEnumClass<EAuracronChampionState> { enum { Value = true }; };
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionState>();
// ********** End Enum EAuracronChampionState ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
