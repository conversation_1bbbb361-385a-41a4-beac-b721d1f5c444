// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronVerticalTransitionsBridge.h"

#ifdef AURACRONVERTICALTRANSITIONSBRIDGE_AuracronVerticalTransitionsBridge_generated_h
#error "AuracronVerticalTransitionsBridge.generated.h already included, missing '#pragma once' in AuracronVerticalTransitionsBridge.h"
#endif
#define AURACRONVERTICALTRANSITIONSBRIDGE_AuracronVerticalTransitionsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APawn;
enum class ERealmType : uint8;
struct FTransitionPointData;

// ********** Begin ScriptStruct FTransitionProperties *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTransitionProperties_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTransitionProperties;
// ********** End ScriptStruct FTransitionProperties ***********************************************

// ********** Begin ScriptStruct FTransitionPointData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTransitionPointData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTransitionPointData;
// ********** End ScriptStruct FTransitionPointData ************************************************

// ********** Begin ScriptStruct FStringArrayWrapper ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_161_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStringArrayWrapper_Statics; \
	static class UScriptStruct* StaticStruct();


struct FStringArrayWrapper;
// ********** End ScriptStruct FStringArrayWrapper *************************************************

// ********** Begin ScriptStruct FTransitionNetworkData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTransitionNetworkData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTransitionNetworkData;
// ********** End ScriptStruct FTransitionNetworkData **********************************************

// ********** Begin Class UAuracronVerticalTransitionsBridge ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearTransitionNetwork); \
	DECLARE_FUNCTION(execValidateNetworkIntegrity); \
	DECLARE_FUNCTION(execGetNetworkStatistics); \
	DECLARE_FUNCTION(execGetTransitionDataForPython); \
	DECLARE_FUNCTION(execExecutePythonScript); \
	DECLARE_FUNCTION(execInitializePythonBindings); \
	DECLARE_FUNCTION(execSetupAudioEffects); \
	DECLARE_FUNCTION(execSetupVisualEffects); \
	DECLARE_FUNCTION(execPlayTransitionEffect); \
	DECLARE_FUNCTION(execCancelTransitionChanneling); \
	DECLARE_FUNCTION(execStartTransitionChanneling); \
	DECLARE_FUNCTION(execGetNearbyTransitions); \
	DECLARE_FUNCTION(execCanPlayerUseTransition); \
	DECLARE_FUNCTION(execFindPathBetweenRealms); \
	DECLARE_FUNCTION(execValidateNetworkConnectivity); \
	DECLARE_FUNCTION(execOptimizeTransitionNetwork); \
	DECLARE_FUNCTION(execBuildTransitionNetwork); \
	DECLARE_FUNCTION(execExecuteTransition); \
	DECLARE_FUNCTION(execDeactivateTransition); \
	DECLARE_FUNCTION(execActivateTransition); \
	DECLARE_FUNCTION(execRemoveTransitionPoint); \
	DECLARE_FUNCTION(execCreateTransitionPoint);


AURACRONVERTICALTRANSITIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronVerticalTransitionsBridge(); \
	friend struct Z_Construct_UClass_UAuracronVerticalTransitionsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONVERTICALTRANSITIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronVerticalTransitionsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronVerticalTransitionsBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronVerticalTransitionsBridge"), Z_Construct_UClass_UAuracronVerticalTransitionsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronVerticalTransitionsBridge)


#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronVerticalTransitionsBridge(UAuracronVerticalTransitionsBridge&&) = delete; \
	UAuracronVerticalTransitionsBridge(const UAuracronVerticalTransitionsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronVerticalTransitionsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronVerticalTransitionsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronVerticalTransitionsBridge) \
	NO_API virtual ~UAuracronVerticalTransitionsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_201_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h_204_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronVerticalTransitionsBridge;

// ********** End Class UAuracronVerticalTransitionsBridge *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronVerticalTransitionsBridge_Public_AuracronVerticalTransitionsBridge_h

// ********** Begin Enum ERealmType ****************************************************************
#define FOREACH_ENUM_EREALMTYPE(op) \
	op(ERealmType::PlanicieRadiante) \
	op(ERealmType::FirmamentoZephyr) \
	op(ERealmType::AbismoUmbrio) 

enum class ERealmType : uint8;
template<> struct TIsUEnumClass<ERealmType> { enum { Value = true }; };
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<ERealmType>();
// ********** End Enum ERealmType ******************************************************************

// ********** Begin Enum EAuracronVerticalTransitionType *******************************************
#define FOREACH_ENUM_EAURACRONVERTICALTRANSITIONTYPE(op) \
	op(EAuracronVerticalTransitionType::Portal) \
	op(EAuracronVerticalTransitionType::Elevator) \
	op(EAuracronVerticalTransitionType::Stairs) \
	op(EAuracronVerticalTransitionType::JumpPad) \
	op(EAuracronVerticalTransitionType::TeleportCircle) \
	op(EAuracronVerticalTransitionType::WindCurrent) \
	op(EAuracronVerticalTransitionType::CaveEntrance) \
	op(EAuracronVerticalTransitionType::FloatingPlatform) 

enum class EAuracronVerticalTransitionType : uint8;
template<> struct TIsUEnumClass<EAuracronVerticalTransitionType> { enum { Value = true }; };
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<EAuracronVerticalTransitionType>();
// ********** End Enum EAuracronVerticalTransitionType *********************************************

// ********** Begin Enum ETransitionDirection ******************************************************
#define FOREACH_ENUM_ETRANSITIONDIRECTION(op) \
	op(ETransitionDirection::Up) \
	op(ETransitionDirection::Down) \
	op(ETransitionDirection::Bidirectional) 

enum class ETransitionDirection : uint8;
template<> struct TIsUEnumClass<ETransitionDirection> { enum { Value = true }; };
template<> AURACRONVERTICALTRANSITIONSBRIDGE_API UEnum* StaticEnum<ETransitionDirection>();
// ********** End Enum ETransitionDirection ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
