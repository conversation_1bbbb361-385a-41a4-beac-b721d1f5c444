// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMeshDeformation.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronMeshDeformation_generated_h
#error "AuracronMeshDeformation.generated.h already included, missing '#pragma once' in AuracronMeshDeformation.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronMeshDeformation_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FVertexData *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h_64_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FVertexData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FVertexData;
// ********** End ScriptStruct FVertexData *********************************************************

// ********** Begin ScriptStruct FMeshDeformationData **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h_94_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMeshDeformationData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMeshDeformationData;
// ********** End ScriptStruct FMeshDeformationData ************************************************

// ********** Begin ScriptStruct FLODGenerationSettings ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h_124_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLODGenerationSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FLODGenerationSettings;
// ********** End ScriptStruct FLODGenerationSettings **********************************************

// ********** Begin ScriptStruct FMeshValidationResult *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h_151_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMeshValidationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMeshValidationResult;
// ********** End ScriptStruct FMeshValidationResult ***********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMeshDeformation_h

// ********** Begin Enum EVertexManipulationType ***************************************************
#define FOREACH_ENUM_EVERTEXMANIPULATIONTYPE(op) \
	op(EVertexManipulationType::Absolute) \
	op(EVertexManipulationType::Relative) \
	op(EVertexManipulationType::Additive) \
	op(EVertexManipulationType::Multiplicative) 

enum class EVertexManipulationType : uint8;
template<> struct TIsUEnumClass<EVertexManipulationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EVertexManipulationType>();
// ********** End Enum EVertexManipulationType *****************************************************

// ********** Begin Enum ENormalRecalculationType **************************************************
#define FOREACH_ENUM_ENORMALRECALCULATIONTYPE(op) \
	op(ENormalRecalculationType::None) \
	op(ENormalRecalculationType::Weighted) \
	op(ENormalRecalculationType::Uniform) \
	op(ENormalRecalculationType::AngleWeighted) 

enum class ENormalRecalculationType : uint8;
template<> struct TIsUEnumClass<ENormalRecalculationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ENormalRecalculationType>();
// ********** End Enum ENormalRecalculationType ****************************************************

// ********** Begin Enum EUVPreservationType *******************************************************
#define FOREACH_ENUM_EUVPRESERVATIONTYPE(op) \
	op(EUVPreservationType::None) \
	op(EUVPreservationType::Stretch) \
	op(EUVPreservationType::Conformal) \
	op(EUVPreservationType::Authalic) 

enum class EUVPreservationType : uint8;
template<> struct TIsUEnumClass<EUVPreservationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EUVPreservationType>();
// ********** End Enum EUVPreservationType *********************************************************

// ********** Begin Enum EMeshValidationType *******************************************************
#define FOREACH_ENUM_EMESHVALIDATIONTYPE(op) \
	op(EMeshValidationType::Basic) \
	op(EMeshValidationType::Comprehensive) \
	op(EMeshValidationType::Topology) \
	op(EMeshValidationType::Geometry) \
	op(EMeshValidationType::UVMapping) 

enum class EMeshValidationType : uint8;
template<> struct TIsUEnumClass<EMeshValidationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMeshValidationType>();
// ********** End Enum EMeshValidationType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
