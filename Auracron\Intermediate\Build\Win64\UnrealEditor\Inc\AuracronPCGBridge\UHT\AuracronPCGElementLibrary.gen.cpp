// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGElementLibrary.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGElementLibrary() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGElementLibrary();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGElementLibrary_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings_NoRegister();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory();
AURACRONPCGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronPCGElementCategory ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGElementCategory;
static UEnum* EAuracronPCGElementCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGElementCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGElementCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGElementCategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGElementCategory.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGElementCategory>()
{
	return EAuracronPCGElementCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element library categories\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGElementCategory::Custom" },
		{ "Filters.DisplayName", "Filters" },
		{ "Filters.Name", "EAuracronPCGElementCategory::Filters" },
		{ "Generators.DisplayName", "Generators" },
		{ "Generators.Name", "EAuracronPCGElementCategory::Generators" },
		{ "Landscape.DisplayName", "Landscape" },
		{ "Landscape.Name", "EAuracronPCGElementCategory::Landscape" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
		{ "Noise.DisplayName", "Noise" },
		{ "Noise.Name", "EAuracronPCGElementCategory::Noise" },
		{ "Samplers.DisplayName", "Samplers" },
		{ "Samplers.Name", "EAuracronPCGElementCategory::Samplers" },
		{ "Spatial.DisplayName", "Spatial" },
		{ "Spatial.Name", "EAuracronPCGElementCategory::Spatial" },
		{ "Spawners.DisplayName", "Spawners" },
		{ "Spawners.Name", "EAuracronPCGElementCategory::Spawners" },
		{ "Spline.DisplayName", "Spline" },
		{ "Spline.Name", "EAuracronPCGElementCategory::Spline" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element library categories" },
#endif
		{ "Transformers.DisplayName", "Transformers" },
		{ "Transformers.Name", "EAuracronPCGElementCategory::Transformers" },
		{ "Utilities.DisplayName", "Utilities" },
		{ "Utilities.Name", "EAuracronPCGElementCategory::Utilities" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGElementCategory::Generators", (int64)EAuracronPCGElementCategory::Generators },
		{ "EAuracronPCGElementCategory::Samplers", (int64)EAuracronPCGElementCategory::Samplers },
		{ "EAuracronPCGElementCategory::Filters", (int64)EAuracronPCGElementCategory::Filters },
		{ "EAuracronPCGElementCategory::Transformers", (int64)EAuracronPCGElementCategory::Transformers },
		{ "EAuracronPCGElementCategory::Spawners", (int64)EAuracronPCGElementCategory::Spawners },
		{ "EAuracronPCGElementCategory::Utilities", (int64)EAuracronPCGElementCategory::Utilities },
		{ "EAuracronPCGElementCategory::Noise", (int64)EAuracronPCGElementCategory::Noise },
		{ "EAuracronPCGElementCategory::Spatial", (int64)EAuracronPCGElementCategory::Spatial },
		{ "EAuracronPCGElementCategory::Landscape", (int64)EAuracronPCGElementCategory::Landscape },
		{ "EAuracronPCGElementCategory::Spline", (int64)EAuracronPCGElementCategory::Spline },
		{ "EAuracronPCGElementCategory::Custom", (int64)EAuracronPCGElementCategory::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGElementCategory",
	"EAuracronPCGElementCategory",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGElementCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGElementCategory.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGElementCategory.InnerSingleton;
}
// ********** End Enum EAuracronPCGElementCategory *************************************************

// ********** Begin Enum EAuracronPCGNoiseType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGNoiseType;
static UEnum* EAuracronPCGNoiseType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGNoiseType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGNoiseType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGNoiseType>()
{
	return EAuracronPCGNoiseType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise types for procedural generation\n" },
#endif
		{ "Fractal.DisplayName", "Fractal" },
		{ "Fractal.Name", "EAuracronPCGNoiseType::Fractal" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
		{ "Perlin.DisplayName", "Perlin" },
		{ "Perlin.Name", "EAuracronPCGNoiseType::Perlin" },
		{ "Ridge.DisplayName", "Ridge" },
		{ "Ridge.Name", "EAuracronPCGNoiseType::Ridge" },
		{ "Simplex.DisplayName", "Simplex" },
		{ "Simplex.Name", "EAuracronPCGNoiseType::Simplex" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise types for procedural generation" },
#endif
		{ "Turbulence.DisplayName", "Turbulence" },
		{ "Turbulence.Name", "EAuracronPCGNoiseType::Turbulence" },
		{ "Worley.DisplayName", "Worley" },
		{ "Worley.Name", "EAuracronPCGNoiseType::Worley" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGNoiseType::Perlin", (int64)EAuracronPCGNoiseType::Perlin },
		{ "EAuracronPCGNoiseType::Simplex", (int64)EAuracronPCGNoiseType::Simplex },
		{ "EAuracronPCGNoiseType::Worley", (int64)EAuracronPCGNoiseType::Worley },
		{ "EAuracronPCGNoiseType::Ridge", (int64)EAuracronPCGNoiseType::Ridge },
		{ "EAuracronPCGNoiseType::Fractal", (int64)EAuracronPCGNoiseType::Fractal },
		{ "EAuracronPCGNoiseType::Turbulence", (int64)EAuracronPCGNoiseType::Turbulence },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGNoiseType",
	"EAuracronPCGNoiseType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGNoiseType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGNoiseType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGNoiseType.InnerSingleton;
}
// ********** End Enum EAuracronPCGNoiseType *******************************************************

// ********** Begin Enum EAuracronPCGDistributionPattern *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern;
static UEnum* EAuracronPCGDistributionPattern_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGDistributionPattern"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGDistributionPattern>()
{
	return EAuracronPCGDistributionPattern_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distribution patterns for point generation\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronPCGDistributionPattern::Custom" },
		{ "Grid.DisplayName", "Grid" },
		{ "Grid.Name", "EAuracronPCGDistributionPattern::Grid" },
		{ "Hexagonal.DisplayName", "Hexagonal" },
		{ "Hexagonal.Name", "EAuracronPCGDistributionPattern::Hexagonal" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
		{ "Poisson.DisplayName", "Poisson Disk" },
		{ "Poisson.Name", "EAuracronPCGDistributionPattern::Poisson" },
		{ "Radial.DisplayName", "Radial" },
		{ "Radial.Name", "EAuracronPCGDistributionPattern::Radial" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EAuracronPCGDistributionPattern::Random" },
		{ "Spiral.DisplayName", "Spiral" },
		{ "Spiral.Name", "EAuracronPCGDistributionPattern::Spiral" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distribution patterns for point generation" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGDistributionPattern::Random", (int64)EAuracronPCGDistributionPattern::Random },
		{ "EAuracronPCGDistributionPattern::Grid", (int64)EAuracronPCGDistributionPattern::Grid },
		{ "EAuracronPCGDistributionPattern::Hexagonal", (int64)EAuracronPCGDistributionPattern::Hexagonal },
		{ "EAuracronPCGDistributionPattern::Poisson", (int64)EAuracronPCGDistributionPattern::Poisson },
		{ "EAuracronPCGDistributionPattern::Spiral", (int64)EAuracronPCGDistributionPattern::Spiral },
		{ "EAuracronPCGDistributionPattern::Radial", (int64)EAuracronPCGDistributionPattern::Radial },
		{ "EAuracronPCGDistributionPattern::Custom", (int64)EAuracronPCGDistributionPattern::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGDistributionPattern",
	"EAuracronPCGDistributionPattern",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern.InnerSingleton;
}
// ********** End Enum EAuracronPCGDistributionPattern *********************************************

// ********** Begin Enum EAuracronPCGBiomeType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronPCGBiomeType;
static UEnum* EAuracronPCGBiomeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGBiomeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronPCGBiomeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("EAuracronPCGBiomeType"));
	}
	return Z_Registration_Info_UEnum_EAuracronPCGBiomeType.OuterSingleton;
}
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGBiomeType>()
{
	return EAuracronPCGBiomeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome types for ecosystem generation\n" },
#endif
		{ "Crystal.DisplayName", "Crystal" },
		{ "Crystal.Name", "EAuracronPCGBiomeType::Crystal" },
		{ "Desert.DisplayName", "Desert" },
		{ "Desert.Name", "EAuracronPCGBiomeType::Desert" },
		{ "Forest.DisplayName", "Forest" },
		{ "Forest.Name", "EAuracronPCGBiomeType::Forest" },
		{ "Grassland.DisplayName", "Grassland" },
		{ "Grassland.Name", "EAuracronPCGBiomeType::Grassland" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
		{ "Mountain.DisplayName", "Mountain" },
		{ "Mountain.Name", "EAuracronPCGBiomeType::Mountain" },
		{ "Ocean.DisplayName", "Ocean" },
		{ "Ocean.Name", "EAuracronPCGBiomeType::Ocean" },
		{ "Swamp.DisplayName", "Swamp" },
		{ "Swamp.Name", "EAuracronPCGBiomeType::Swamp" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome types for ecosystem generation" },
#endif
		{ "Tundra.DisplayName", "Tundra" },
		{ "Tundra.Name", "EAuracronPCGBiomeType::Tundra" },
		{ "Urban.DisplayName", "Urban" },
		{ "Urban.Name", "EAuracronPCGBiomeType::Urban" },
		{ "Volcanic.DisplayName", "Volcanic" },
		{ "Volcanic.Name", "EAuracronPCGBiomeType::Volcanic" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronPCGBiomeType::Forest", (int64)EAuracronPCGBiomeType::Forest },
		{ "EAuracronPCGBiomeType::Desert", (int64)EAuracronPCGBiomeType::Desert },
		{ "EAuracronPCGBiomeType::Grassland", (int64)EAuracronPCGBiomeType::Grassland },
		{ "EAuracronPCGBiomeType::Mountain", (int64)EAuracronPCGBiomeType::Mountain },
		{ "EAuracronPCGBiomeType::Swamp", (int64)EAuracronPCGBiomeType::Swamp },
		{ "EAuracronPCGBiomeType::Tundra", (int64)EAuracronPCGBiomeType::Tundra },
		{ "EAuracronPCGBiomeType::Ocean", (int64)EAuracronPCGBiomeType::Ocean },
		{ "EAuracronPCGBiomeType::Urban", (int64)EAuracronPCGBiomeType::Urban },
		{ "EAuracronPCGBiomeType::Volcanic", (int64)EAuracronPCGBiomeType::Volcanic },
		{ "EAuracronPCGBiomeType::Crystal", (int64)EAuracronPCGBiomeType::Crystal },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	"EAuracronPCGBiomeType",
	"EAuracronPCGBiomeType",
	Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType()
{
	if (!Z_Registration_Info_UEnum_EAuracronPCGBiomeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronPCGBiomeType.InnerSingleton, Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronPCGBiomeType.InnerSingleton;
}
// ********** End Enum EAuracronPCGBiomeType *******************************************************

// ********** Begin Class UAuracronPCGPointGridSettings ********************************************
void UAuracronPCGPointGridSettings::StaticRegisterNativesUAuracronPCGPointGridSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGPointGridSettings;
UClass* UAuracronPCGPointGridSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGPointGridSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGPointGridSettings"),
			Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGPointGridSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings_NoRegister()
{
	return UAuracronPCGPointGridSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionPattern_MetaData[] = {
		{ "Category", "Grid Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSpacing_MetaData[] = {
		{ "Category", "Grid Settings" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Jitter_MetaData[] = {
		{ "Category", "Grid Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "Category", "Grid Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoise_MetaData[] = {
		{ "Category", "Noise Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Noise Settings" },
		{ "ClampMax", "8" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseThreshold_MetaData[] = {
		{ "Category", "Noise Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDensity_MetaData[] = {
		{ "Category", "Density Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Density configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Density configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityVariation_MetaData[] = {
		{ "Category", "Density Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionPattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Jitter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridSize;
	static void NewProp_bUseNoise_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoise;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DensityVariation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGPointGridSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DistributionPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DistributionPattern = { "DistributionPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, DistributionPattern), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionPattern_MetaData), NewProp_DistributionPattern_MetaData) }; // 1555853986
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_GridSpacing = { "GridSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, GridSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSpacing_MetaData), NewProp_GridSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_Jitter = { "Jitter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, Jitter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Jitter_MetaData), NewProp_Jitter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, GridSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
void Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_bUseNoise_SetBit(void* Obj)
{
	((UAuracronPCGPointGridSettings*)Obj)->bUseNoise = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_bUseNoise = { "bUseNoise", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGPointGridSettings), &Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_bUseNoise_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoise_MetaData), NewProp_bUseNoise_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 2308707076
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseThreshold = { "NoiseThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, NoiseThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseThreshold_MetaData), NewProp_NoiseThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_BaseDensity = { "BaseDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, BaseDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDensity_MetaData), NewProp_BaseDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DensityVariation = { "DensityVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGPointGridSettings, DensityVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityVariation_MetaData), NewProp_DensityVariation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DistributionPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DistributionPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_GridSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_Jitter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_bUseNoise,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_NoiseThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_BaseDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::NewProp_DensityVariation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::ClassParams = {
	&UAuracronPCGPointGridSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGPointGridSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGPointGridSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGPointGridSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGPointGridSettings);
UAuracronPCGPointGridSettings::~UAuracronPCGPointGridSettings() {}
// ********** End Class UAuracronPCGPointGridSettings **********************************************

// ********** Begin Class UAuracronPCGBiomeGeneratorSettings ***************************************
void UAuracronPCGBiomeGeneratorSettings::StaticRegisterNativesUAuracronPCGBiomeGeneratorSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings;
UClass* UAuracronPCGBiomeGeneratorSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGBiomeGeneratorSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGBiomeGeneratorSettings"),
			Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGBiomeGeneratorSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_NoRegister()
{
	return UAuracronPCGBiomeGeneratorSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "Category", "Biome Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeIntensity_MetaData[] = {
		{ "Category", "Biome Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDistance_MetaData[] = {
		{ "Category", "Biome Settings" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationMeshes_MetaData[] = {
		{ "Category", "Vegetation Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Vegetation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vegetation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationWeights_MetaData[] = {
		{ "Category", "Vegetation Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationDensity_MetaData[] = {
		{ "Category", "Vegetation Settings" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Environment Settings" },
		{ "ClampMax", "90.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Environmental settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Environmental settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Environment Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoistureLevel_MetaData[] = {
		{ "Category", "Environment Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureLevel_MetaData[] = {
		{ "Category", "Environment Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BiomeIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDistance;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_VegetationMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VegetationMeshes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VegetationWeights_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VegetationWeights;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VegetationDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MoistureLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TemperatureLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGBiomeGeneratorSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, BiomeType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGBiomeType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) }; // 3055528382
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeIntensity = { "BiomeIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, BiomeIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeIntensity_MetaData), NewProp_BiomeIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_TransitionDistance = { "TransitionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, TransitionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDistance_MetaData), NewProp_TransitionDistance_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationMeshes_Inner = { "VegetationMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationMeshes = { "VegetationMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, VegetationMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationMeshes_MetaData), NewProp_VegetationMeshes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationWeights_Inner = { "VegetationWeights", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationWeights = { "VegetationWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, VegetationWeights), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationWeights_MetaData), NewProp_VegetationWeights_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationDensity = { "VegetationDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, VegetationDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationDensity_MetaData), NewProp_VegetationDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_MoistureLevel = { "MoistureLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, MoistureLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoistureLevel_MetaData), NewProp_MoistureLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_TemperatureLevel = { "TemperatureLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBiomeGeneratorSettings, TemperatureLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureLevel_MetaData), NewProp_TemperatureLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_BiomeIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_TransitionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationWeights_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_VegetationDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_MoistureLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::NewProp_TemperatureLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::ClassParams = {
	&UAuracronPCGBiomeGeneratorSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGBiomeGeneratorSettings);
UAuracronPCGBiomeGeneratorSettings::~UAuracronPCGBiomeGeneratorSettings() {}
// ********** End Class UAuracronPCGBiomeGeneratorSettings *****************************************

// ********** Begin Class UAuracronPCGAdvancedSurfaceSamplerSettings *******************************
void UAuracronPCGAdvancedSurfaceSamplerSettings::StaticRegisterNativesUAuracronPCGAdvancedSurfaceSamplerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings;
UClass* UAuracronPCGAdvancedSurfaceSamplerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedSurfaceSamplerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedSurfaceSamplerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedSurfaceSamplerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_NoRegister()
{
	return UAuracronPCGAdvancedSurfaceSamplerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsPerSquareMeter_MetaData[] = {
		{ "Category", "Sampling Settings" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.001" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sampling configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sampling configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingPattern_MetaData[] = {
		{ "Category", "Sampling Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingJitter_MetaData[] = {
		{ "Category", "Sampling Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSurfaceAngle_MetaData[] = {
		{ "Category", "Surface Constraints" },
		{ "ClampMax", "90.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Surface constraints\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Surface constraints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProjectToSurface_MetaData[] = {
		{ "Category", "Surface Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionDistance_MetaData[] = {
		{ "Category", "Surface Constraints" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHighQualitySampling_MetaData[] = {
		{ "Category", "Quality Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quality settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingIterations_MetaData[] = {
		{ "Category", "Quality Settings" },
		{ "ClampMax", "16" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PointsPerSquareMeter;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SamplingPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SamplingPattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SamplingJitter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSurfaceAngle;
	static void NewProp_bProjectToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProjectToSurface;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProjectionDistance;
	static void NewProp_bUseHighQualitySampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHighQualitySampling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SamplingIterations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedSurfaceSamplerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_PointsPerSquareMeter = { "PointsPerSquareMeter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, PointsPerSquareMeter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsPerSquareMeter_MetaData), NewProp_PointsPerSquareMeter_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingPattern = { "SamplingPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, SamplingPattern), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGDistributionPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingPattern_MetaData), NewProp_SamplingPattern_MetaData) }; // 1555853986
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingJitter = { "SamplingJitter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, SamplingJitter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingJitter_MetaData), NewProp_SamplingJitter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_MaxSurfaceAngle = { "MaxSurfaceAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, MaxSurfaceAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSurfaceAngle_MetaData), NewProp_MaxSurfaceAngle_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bProjectToSurface_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSurfaceSamplerSettings*)Obj)->bProjectToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bProjectToSurface = { "bProjectToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSurfaceSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bProjectToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProjectToSurface_MetaData), NewProp_bProjectToSurface_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_ProjectionDistance = { "ProjectionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, ProjectionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionDistance_MetaData), NewProp_ProjectionDistance_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bUseHighQualitySampling_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedSurfaceSamplerSettings*)Obj)->bUseHighQualitySampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bUseHighQualitySampling = { "bUseHighQualitySampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedSurfaceSamplerSettings), &Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bUseHighQualitySampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHighQualitySampling_MetaData), NewProp_bUseHighQualitySampling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingIterations = { "SamplingIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedSurfaceSamplerSettings, SamplingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingIterations_MetaData), NewProp_SamplingIterations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_PointsPerSquareMeter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingJitter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_MaxSurfaceAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bProjectToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_ProjectionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_bUseHighQualitySampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::NewProp_SamplingIterations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedSurfaceSamplerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedSurfaceSamplerSettings);
UAuracronPCGAdvancedSurfaceSamplerSettings::~UAuracronPCGAdvancedSurfaceSamplerSettings() {}
// ********** End Class UAuracronPCGAdvancedSurfaceSamplerSettings *********************************

// ********** Begin Class UAuracronPCGLandscapeSamplerSettings *************************************
void UAuracronPCGLandscapeSamplerSettings::StaticRegisterNativesUAuracronPCGLandscapeSamplerSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings;
UClass* UAuracronPCGLandscapeSamplerSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGLandscapeSamplerSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGLandscapeSamplerSettings"),
			Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGLandscapeSamplerSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_NoRegister()
{
	return UAuracronPCGLandscapeSamplerSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleHeight_MetaData[] = {
		{ "Category", "Landscape Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Landscape sampling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Landscape sampling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleSlope_MetaData[] = {
		{ "Category", "Landscape Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSampleMaterials_MetaData[] = {
		{ "Category", "Landscape Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialLayerNames_MetaData[] = {
		{ "Category", "Landscape Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Height Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Height constraints\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height constraints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Height Constraints" },
		{ "ClampMax", "90.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingResolution_MetaData[] = {
		{ "Category", "Quality Settings" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sampling quality\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sampling quality" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInterpolateSamples_MetaData[] = {
		{ "Category", "Quality Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSampleHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleHeight;
	static void NewProp_bSampleSlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleSlope;
	static void NewProp_bSampleMaterials_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSampleMaterials;
	static const UECodeGen_Private::FNamePropertyParams NewProp_MaterialLayerNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialLayerNames;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SamplingResolution;
	static void NewProp_bInterpolateSamples_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterpolateSamples;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGLandscapeSamplerSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleHeight_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeSamplerSettings*)Obj)->bSampleHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleHeight = { "bSampleHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleHeight_MetaData), NewProp_bSampleHeight_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleSlope_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeSamplerSettings*)Obj)->bSampleSlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleSlope = { "bSampleSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleSlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleSlope_MetaData), NewProp_bSampleSlope_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleMaterials_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeSamplerSettings*)Obj)->bSampleMaterials = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleMaterials = { "bSampleMaterials", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleMaterials_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSampleMaterials_MetaData), NewProp_bSampleMaterials_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaterialLayerNames_Inner = { "MaterialLayerNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaterialLayerNames = { "MaterialLayerNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeSamplerSettings, MaterialLayerNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialLayerNames_MetaData), NewProp_MaterialLayerNames_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeSamplerSettings, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeSamplerSettings, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_SamplingResolution = { "SamplingResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGLandscapeSamplerSettings, SamplingResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingResolution_MetaData), NewProp_SamplingResolution_MetaData) };
void Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bInterpolateSamples_SetBit(void* Obj)
{
	((UAuracronPCGLandscapeSamplerSettings*)Obj)->bInterpolateSamples = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bInterpolateSamples = { "bInterpolateSamples", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGLandscapeSamplerSettings), &Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bInterpolateSamples_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInterpolateSamples_MetaData), NewProp_bInterpolateSamples_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bSampleMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaterialLayerNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaterialLayerNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_SamplingResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::NewProp_bInterpolateSamples,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::ClassParams = {
	&UAuracronPCGLandscapeSamplerSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGLandscapeSamplerSettings);
UAuracronPCGLandscapeSamplerSettings::~UAuracronPCGLandscapeSamplerSettings() {}
// ********** End Class UAuracronPCGLandscapeSamplerSettings ***************************************

// ********** Begin Class UAuracronPCGAdvancedFilterSettings ***************************************
void UAuracronPCGAdvancedFilterSettings::StaticRegisterNativesUAuracronPCGAdvancedFilterSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings;
UClass* UAuracronPCGAdvancedFilterSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGAdvancedFilterSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGAdvancedFilterSettings"),
			Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGAdvancedFilterSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_NoRegister()
{
	return UAuracronPCGAdvancedFilterSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByDensity_MetaData[] = {
		{ "Category", "Filter Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Filter criteria\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Filter criteria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityRange_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterByHeight_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFilterBySlope_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Filter Settings" },
		{ "ClampMax", "90.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomAttribute_MetaData[] = {
		{ "Category", "Advanced Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomAttributeName_MetaData[] = {
		{ "Category", "Advanced Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeRange_MetaData[] = {
		{ "Category", "Advanced Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNoiseFilter_MetaData[] = {
		{ "Category", "Noise Filter" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Noise-based filtering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise-based filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseType_MetaData[] = {
		{ "Category", "Noise Filter" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Noise Filter" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseThreshold_MetaData[] = {
		{ "Category", "Noise Filter" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bFilterByDensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DensityRange;
	static void NewProp_bFilterByHeight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterByHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static void NewProp_bFilterBySlope_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFilterBySlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static void NewProp_bUseCustomAttribute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomAttribute;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomAttributeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AttributeRange;
	static void NewProp_bUseNoiseFilter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNoiseFilter;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NoiseType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NoiseType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGAdvancedFilterSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByDensity_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedFilterSettings*)Obj)->bFilterByDensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByDensity = { "bFilterByDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByDensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByDensity_MetaData), NewProp_bFilterByDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_DensityRange = { "DensityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, DensityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityRange_MetaData), NewProp_DensityRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByHeight_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedFilterSettings*)Obj)->bFilterByHeight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByHeight = { "bFilterByHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByHeight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterByHeight_MetaData), NewProp_bFilterByHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterBySlope_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedFilterSettings*)Obj)->bFilterBySlope = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterBySlope = { "bFilterBySlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterBySlope_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFilterBySlope_MetaData), NewProp_bFilterBySlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseCustomAttribute_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedFilterSettings*)Obj)->bUseCustomAttribute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseCustomAttribute = { "bUseCustomAttribute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseCustomAttribute_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomAttribute_MetaData), NewProp_bUseCustomAttribute_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_CustomAttributeName = { "CustomAttributeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, CustomAttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomAttributeName_MetaData), NewProp_CustomAttributeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_AttributeRange = { "AttributeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, AttributeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeRange_MetaData), NewProp_AttributeRange_MetaData) };
void Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseNoiseFilter_SetBit(void* Obj)
{
	((UAuracronPCGAdvancedFilterSettings*)Obj)->bUseNoiseFilter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseNoiseFilter = { "bUseNoiseFilter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGAdvancedFilterSettings), &Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseNoiseFilter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNoiseFilter_MetaData), NewProp_bUseNoiseFilter_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseType = { "NoiseType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, NoiseType), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGNoiseType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseType_MetaData), NewProp_NoiseType_MetaData) }; // 2308707076
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseThreshold = { "NoiseThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGAdvancedFilterSettings, NoiseThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseThreshold_MetaData), NewProp_NoiseThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_DensityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterByHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bFilterBySlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseCustomAttribute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_CustomAttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_AttributeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_bUseNoiseFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::NewProp_NoiseThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGNodeSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::ClassParams = {
	&UAuracronPCGAdvancedFilterSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGAdvancedFilterSettings);
UAuracronPCGAdvancedFilterSettings::~UAuracronPCGAdvancedFilterSettings() {}
// ********** End Class UAuracronPCGAdvancedFilterSettings *****************************************

// ********** Begin Class UAuracronPCGElementLibrary Function CreateElementInstance ****************
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics
{
	struct AuracronPCGElementLibrary_eventCreateElementInstance_Parms
	{
		TSubclassOf<UAuracronPCGNodeSettings> ElementClass;
		UAuracronPCGNodeSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element creation helpers\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element creation helpers" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ElementClass;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::NewProp_ElementClass = { "ElementClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventCreateElementInstance_Parms, ElementClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventCreateElementInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::NewProp_ElementClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "CreateElementInstance", Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::AuracronPCGElementLibrary_eventCreateElementInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::AuracronPCGElementLibrary_eventCreateElementInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execCreateElementInstance)
{
	P_GET_OBJECT(UClass,Z_Param_ElementClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronPCGNodeSettings**)Z_Param__Result=UAuracronPCGElementLibrary::CreateElementInstance(Z_Param_ElementClass);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function CreateElementInstance ******************

// ********** Begin Class UAuracronPCGElementLibrary Function GetAvailableCategories ***************
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics
{
	struct AuracronPCGElementLibrary_eventGetAvailableCategories_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventGetAvailableCategories_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "GetAvailableCategories", Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::AuracronPCGElementLibrary_eventGetAvailableCategories_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::AuracronPCGElementLibrary_eventGetAvailableCategories_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execGetAvailableCategories)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=UAuracronPCGElementLibrary::GetAvailableCategories();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function GetAvailableCategories *****************

// ********** Begin Class UAuracronPCGElementLibrary Function GetElementCount **********************
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics
{
	struct AuracronPCGElementLibrary_eventGetElementCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventGetElementCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "GetElementCount", Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::AuracronPCGElementLibrary_eventGetElementCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::AuracronPCGElementLibrary_eventGetElementCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execGetElementCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UAuracronPCGElementLibrary::GetElementCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function GetElementCount ************************

// ********** Begin Class UAuracronPCGElementLibrary Function GetElementsByCategory ****************
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics
{
	struct AuracronPCGElementLibrary_eventGetElementsByCategory_Parms
	{
		EAuracronPCGElementCategory Category;
		TArray<TSubclassOf<UAuracronPCGNodeSettings>> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventGetElementsByCategory_Parms, Category), Z_Construct_UEnum_AuracronPCGBridge_EAuracronPCGElementCategory, METADATA_PARAMS(0, nullptr) }; // 2802989045
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0014000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventGetElementsByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "GetElementsByCategory", Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::AuracronPCGElementLibrary_eventGetElementsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::AuracronPCGElementLibrary_eventGetElementsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execGetElementsByCategory)
{
	P_GET_ENUM(EAuracronPCGElementCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<TSubclassOf<UAuracronPCGNodeSettings>>*)Z_Param__Result=UAuracronPCGElementLibrary::GetElementsByCategory(EAuracronPCGElementCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function GetElementsByCategory ******************

// ********** Begin Class UAuracronPCGElementLibrary Function RegisterAllElements ******************
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Library management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Library management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "RegisterAllElements", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execRegisterAllElements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	UAuracronPCGElementLibrary::RegisterAllElements();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function RegisterAllElements ********************

// ********** Begin Class UAuracronPCGElementLibrary Function ValidateElementConfiguration *********
struct Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics
{
	struct AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms
	{
		UAuracronPCGNodeSettings* ElementSettings;
		TArray<FString> ValidationErrors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Element Library" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ElementSettings;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ElementSettings = { "ElementSettings", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms, ElementSettings), Z_Construct_UClass_UAuracronPCGNodeSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms), &Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ElementSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGElementLibrary, nullptr, "ValidateElementConfiguration", Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::AuracronPCGElementLibrary_eventValidateElementConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGElementLibrary::execValidateElementConfiguration)
{
	P_GET_OBJECT(UAuracronPCGNodeSettings,Z_Param_ElementSettings);
	P_GET_TARRAY_REF(FString,Z_Param_Out_ValidationErrors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronPCGElementLibrary::ValidateElementConfiguration(Z_Param_ElementSettings,Z_Param_Out_ValidationErrors);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGElementLibrary Function ValidateElementConfiguration ***********

// ********** Begin Class UAuracronPCGElementLibrary ***********************************************
void UAuracronPCGElementLibrary::StaticRegisterNativesUAuracronPCGElementLibrary()
{
	UClass* Class = UAuracronPCGElementLibrary::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateElementInstance", &UAuracronPCGElementLibrary::execCreateElementInstance },
		{ "GetAvailableCategories", &UAuracronPCGElementLibrary::execGetAvailableCategories },
		{ "GetElementCount", &UAuracronPCGElementLibrary::execGetElementCount },
		{ "GetElementsByCategory", &UAuracronPCGElementLibrary::execGetElementsByCategory },
		{ "RegisterAllElements", &UAuracronPCGElementLibrary::execRegisterAllElements },
		{ "ValidateElementConfiguration", &UAuracronPCGElementLibrary::execValidateElementConfiguration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGElementLibrary;
UClass* UAuracronPCGElementLibrary::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGElementLibrary;
	if (!Z_Registration_Info_UClass_UAuracronPCGElementLibrary.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGElementLibrary"),
			Z_Registration_Info_UClass_UAuracronPCGElementLibrary.InnerSingleton,
			StaticRegisterNativesUAuracronPCGElementLibrary,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGElementLibrary.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGElementLibrary_NoRegister()
{
	return UAuracronPCGElementLibrary::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGElementLibrary_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element library manager\n" },
#endif
		{ "IncludePath", "AuracronPCGElementLibrary.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementLibrary.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element library manager" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_CreateElementInstance, "CreateElementInstance" }, // 3102295786
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_GetAvailableCategories, "GetAvailableCategories" }, // 3375252779
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementCount, "GetElementCount" }, // 2517468549
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_GetElementsByCategory, "GetElementsByCategory" }, // 2003449923
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_RegisterAllElements, "RegisterAllElements" }, // 151335311
		{ &Z_Construct_UFunction_UAuracronPCGElementLibrary_ValidateElementConfiguration, "ValidateElementConfiguration" }, // 559537068
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGElementLibrary>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::ClassParams = {
	&UAuracronPCGElementLibrary::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGElementLibrary()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGElementLibrary.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGElementLibrary.OuterSingleton, Z_Construct_UClass_UAuracronPCGElementLibrary_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGElementLibrary.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGElementLibrary);
UAuracronPCGElementLibrary::~UAuracronPCGElementLibrary() {}
// ********** End Class UAuracronPCGElementLibrary *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronPCGElementCategory_StaticEnum, TEXT("EAuracronPCGElementCategory"), &Z_Registration_Info_UEnum_EAuracronPCGElementCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2802989045U) },
		{ EAuracronPCGNoiseType_StaticEnum, TEXT("EAuracronPCGNoiseType"), &Z_Registration_Info_UEnum_EAuracronPCGNoiseType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2308707076U) },
		{ EAuracronPCGDistributionPattern_StaticEnum, TEXT("EAuracronPCGDistributionPattern"), &Z_Registration_Info_UEnum_EAuracronPCGDistributionPattern, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1555853986U) },
		{ EAuracronPCGBiomeType_StaticEnum, TEXT("EAuracronPCGBiomeType"), &Z_Registration_Info_UEnum_EAuracronPCGBiomeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3055528382U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGPointGridSettings, UAuracronPCGPointGridSettings::StaticClass, TEXT("UAuracronPCGPointGridSettings"), &Z_Registration_Info_UClass_UAuracronPCGPointGridSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGPointGridSettings), 783073315U) },
		{ Z_Construct_UClass_UAuracronPCGBiomeGeneratorSettings, UAuracronPCGBiomeGeneratorSettings::StaticClass, TEXT("UAuracronPCGBiomeGeneratorSettings"), &Z_Registration_Info_UClass_UAuracronPCGBiomeGeneratorSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGBiomeGeneratorSettings), 2581890304U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings, UAuracronPCGAdvancedSurfaceSamplerSettings::StaticClass, TEXT("UAuracronPCGAdvancedSurfaceSamplerSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedSurfaceSamplerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedSurfaceSamplerSettings), 3014363735U) },
		{ Z_Construct_UClass_UAuracronPCGLandscapeSamplerSettings, UAuracronPCGLandscapeSamplerSettings::StaticClass, TEXT("UAuracronPCGLandscapeSamplerSettings"), &Z_Registration_Info_UClass_UAuracronPCGLandscapeSamplerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGLandscapeSamplerSettings), 1265646112U) },
		{ Z_Construct_UClass_UAuracronPCGAdvancedFilterSettings, UAuracronPCGAdvancedFilterSettings::StaticClass, TEXT("UAuracronPCGAdvancedFilterSettings"), &Z_Registration_Info_UClass_UAuracronPCGAdvancedFilterSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGAdvancedFilterSettings), 982070913U) },
		{ Z_Construct_UClass_UAuracronPCGElementLibrary, UAuracronPCGElementLibrary::StaticClass, TEXT("UAuracronPCGElementLibrary"), &Z_Registration_Info_UClass_UAuracronPCGElementLibrary, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGElementLibrary), 359127327U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_2204940012(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementLibrary_h__Script_AuracronPCGBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
