// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Collision System Utilities Implementation
// Bridge 2.10: PCG Framework - Collision e Physics

#include "AuracronPCGCollisionSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Kismet/KismetSystemLibrary.h"

// =============================================================================
// COLLISION SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

bool UAuracronPCGCollisionSystemUtils::PerformCollisionTest(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, FHitResult& OutHitResult)
{
    if (!World)
    {
        return false;
    }

    switch (CollisionDescriptor.CollisionTestType)
    {
        case EAuracronPCGCollisionTestType::LineTrace:
            return AuracronPCGCollisionSystemUtils::LineTrace(World, Start, End, CollisionDescriptor, OutHitResult);
        case EAuracronPCGCollisionTestType::SphereTrace:
            return AuracronPCGCollisionSystemUtils::SphereTrace(World, Start, End, CollisionDescriptor.SphereRadius, CollisionDescriptor, OutHitResult);
        case EAuracronPCGCollisionTestType::BoxTrace:
            return AuracronPCGCollisionSystemUtils::BoxTrace(World, Start, End, CollisionDescriptor.BoxExtent, CollisionDescriptor, OutHitResult);
        case EAuracronPCGCollisionTestType::CapsuleTrace:
            return AuracronPCGCollisionSystemUtils::CapsuleTrace(World, Start, End, CollisionDescriptor.CapsuleRadius, CollisionDescriptor.CapsuleHalfHeight, CollisionDescriptor, OutHitResult);
        default:
            return AuracronPCGCollisionSystemUtils::LineTrace(World, Start, End, CollisionDescriptor, OutHitResult);
    }
}

bool UAuracronPCGCollisionSystemUtils::PerformOverlapTest(UWorld* World, const FVector& Location, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, TArray<FOverlapResult>& OutOverlaps)
{
    if (!World)
    {
        return false;
    }

    switch (CollisionDescriptor.CollisionTestType)
    {
        case EAuracronPCGCollisionTestType::OverlapSphere:
            return AuracronPCGCollisionSystemUtils::SphereOverlap(World, Location, CollisionDescriptor.SphereRadius, CollisionDescriptor, OutOverlaps);
        case EAuracronPCGCollisionTestType::OverlapBox:
            return AuracronPCGCollisionSystemUtils::BoxOverlap(World, Location, CollisionDescriptor.BoxExtent, CollisionDescriptor, OutOverlaps);
        case EAuracronPCGCollisionTestType::OverlapCapsule:
            return AuracronPCGCollisionSystemUtils::CapsuleOverlap(World, Location, CollisionDescriptor.CapsuleRadius, CollisionDescriptor.CapsuleHalfHeight, CollisionDescriptor, OutOverlaps);
        default:
            return AuracronPCGCollisionSystemUtils::SphereOverlap(World, Location, CollisionDescriptor.SphereRadius, CollisionDescriptor, OutOverlaps);
    }
}

bool UAuracronPCGCollisionSystemUtils::ValidatePlacement(UWorld* World, const FVector& Location, const FAuracronPCGCollisionDescriptor& CollisionDescriptor, EAuracronPCGPlacementValidation ValidationMode)
{
    if (!World)
    {
        return false;
    }

    switch (ValidationMode)
    {
        case EAuracronPCGPlacementValidation::GroundCheck:
        {
            FHitResult HitResult;
            FVector TraceStart = Location + FVector(0.0f, 0.0f, 100.0f);
            FVector TraceEnd = Location + FVector(0.0f, 0.0f, -1000.0f);
            return PerformCollisionTest(World, TraceStart, TraceEnd, CollisionDescriptor, HitResult);
        }
        case EAuracronPCGPlacementValidation::ClearanceCheck:
            return CheckClearance(World, Location, 50.0f, 200.0f, CollisionDescriptor);
        case EAuracronPCGPlacementValidation::StabilityCheck:
            return CalculateStabilityScore(World, Location, 100.0f, 8, CollisionDescriptor) >= 0.7f;
        default:
            return true;
    }
}

FVector UAuracronPCGCollisionSystemUtils::ProjectToSurface(UWorld* World, const FVector& Location, float ProjectionDistance, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
{
    if (!World)
    {
        return Location;
    }

    FHitResult HitResult;
    FVector TraceStart = Location + FVector(0.0f, 0.0f, ProjectionDistance * 0.5f);
    FVector TraceEnd = Location - FVector(0.0f, 0.0f, ProjectionDistance * 0.5f);

    if (PerformCollisionTest(World, TraceStart, TraceEnd, CollisionDescriptor, HitResult))
    {
        return HitResult.Location;
    }

    return Location;
}

TArray<AActor*> UAuracronPCGCollisionSystemUtils::PerformSpatialQuery(UWorld* World, const FVector& Location, const FAuracronPCGSpatialQueryDescriptor& QueryDescriptor)
{
    TArray<AActor*> FoundActors;
    
    if (!World)
    {
        return FoundActors;
    }

    switch (QueryDescriptor.QueryType)
    {
        case EAuracronPCGSpatialQueryType::WithinRadius:
        {
            TArray<FOverlapResult> Overlaps;
            FCollisionShape SphereShape = FCollisionShape::MakeSphere(QueryDescriptor.SearchRadius);
            FCollisionQueryParams QueryParams;
            QueryParams.bTraceComplex = QueryDescriptor.bUseComplexCollision;

            if (World->OverlapMultiByObjectType(Overlaps, Location, FQuat::Identity, FCollisionObjectQueryParams::AllObjects, SphereShape, QueryParams))
            {
                for (const FOverlapResult& Overlap : Overlaps)
                {
                    if (Overlap.GetActor())
                    {
                        FoundActors.Add(Overlap.GetActor());
                    }
                }
            }
            break;
        }
        case EAuracronPCGSpatialQueryType::WithinBox:
        {
            TArray<FOverlapResult> Overlaps;
            FCollisionShape BoxShape = FCollisionShape::MakeBox(QueryDescriptor.SearchExtent);
            FCollisionQueryParams QueryParams;
            QueryParams.bTraceComplex = QueryDescriptor.bUseComplexCollision;

            if (World->OverlapMultiByObjectType(Overlaps, Location, FQuat::Identity, FCollisionObjectQueryParams::AllObjects, BoxShape, QueryParams))
            {
                for (const FOverlapResult& Overlap : Overlaps)
                {
                    if (Overlap.GetActor())
                    {
                        FoundActors.Add(Overlap.GetActor());
                    }
                }
            }
            break;
        }
        default:
        {
            // Default to radius search
            TArray<FOverlapResult> Overlaps;
            FCollisionShape SphereShape = FCollisionShape::MakeSphere(QueryDescriptor.SearchRadius);
            FCollisionQueryParams QueryParams;

            if (World->OverlapMultiByObjectType(Overlaps, Location, FQuat::Identity, FCollisionObjectQueryParams::AllObjects, SphereShape, QueryParams))
            {
                for (const FOverlapResult& Overlap : Overlaps)
                {
                    if (Overlap.GetActor())
                    {
                        FoundActors.Add(Overlap.GetActor());
                    }
                }
            }
            break;
        }
    }

    // Apply filters
    if (QueryDescriptor.bFilterByType && QueryDescriptor.TargetTypes.Num() > 0)
    {
        AuracronPCGCollisionSystemUtils::FilterActorsByClass(FoundActors, QueryDescriptor.TargetTypes);
    }

    // Sort by distance if requested
    if (QueryDescriptor.bSortByDistance)
    {
        AuracronPCGCollisionSystemUtils::SortActorsByDistance(FoundActors, Location);
    }

    // Limit results
    if (FoundActors.Num() > QueryDescriptor.MaxResults)
    {
        FoundActors.SetNum(QueryDescriptor.MaxResults);
    }

    return FoundActors;
}

float UAuracronPCGCollisionSystemUtils::CalculateDistance(const FVector& PointA, const FVector& PointB)
{
    return FVector::Dist(PointA, PointB);
}

float UAuracronPCGCollisionSystemUtils::CalculateAngle(const FVector& PointA, const FVector& PointB, const FVector& Reference)
{
    FVector VectorA = (PointA - Reference).GetSafeNormal();
    FVector VectorB = (PointB - Reference).GetSafeNormal();
    
    float DotProduct = FVector::DotProduct(VectorA, VectorB);
    return FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
}

bool UAuracronPCGCollisionSystemUtils::CheckLineOfSight(UWorld* World, const FVector& Start, const FVector& End, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
{
    if (!World)
    {
        return false;
    }

    FHitResult HitResult;
    return !PerformCollisionTest(World, Start, End, CollisionDescriptor, HitResult);
}

FVector UAuracronPCGCollisionSystemUtils::SimulatePhysicsStep(const FVector& Position, const FVector& Velocity, const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor, float DeltaTime)
{
    FVector NewVelocity = Velocity;
    
    // Apply gravity
    if (PhysicsDescriptor.bEnableGravity)
    {
        NewVelocity += FVector(0.0f, 0.0f, -980.0f) * DeltaTime;
    }
    
    // Apply damping
    NewVelocity *= (1.0f - PhysicsDescriptor.LinearDamping * DeltaTime);
    
    // Calculate new position
    return Position + NewVelocity * DeltaTime;
}

FVector UAuracronPCGCollisionSystemUtils::CalculateGravityForce(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor)
{
    if (PhysicsDescriptor.bEnableGravity)
    {
        return FVector(0.0f, 0.0f, -980.0f) * PhysicsDescriptor.Mass;
    }
    return FVector::ZeroVector;
}

bool UAuracronPCGCollisionSystemUtils::IsPhysicsStable(const FVector& Velocity, const FVector& AngularVelocity, float Threshold)
{
    return Velocity.SizeSquared() < (Threshold * Threshold) && AngularVelocity.SizeSquared() < (Threshold * Threshold);
}

float UAuracronPCGCollisionSystemUtils::CalculateGroundAngle(const FVector& Normal)
{
    FVector UpVector = FVector::UpVector;
    float DotProduct = FVector::DotProduct(Normal, UpVector);
    float Angle = FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
    return FMath::RadiansToDegrees(Angle);
}

float UAuracronPCGCollisionSystemUtils::CalculateStabilityScore(UWorld* World, const FVector& Location, float Radius, int32 Samples, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
{
    if (!World || Samples <= 0)
    {
        return 0.0f;
    }

    TArray<FVector> SamplePoints = AuracronPCGCollisionSystemUtils::GenerateStabilitySamplePoints(Location, Radius, Samples);
    return AuracronPCGCollisionSystemUtils::CalculateTerrainStability(World, SamplePoints, CollisionDescriptor);
}

bool UAuracronPCGCollisionSystemUtils::CheckClearance(UWorld* World, const FVector& Location, float Radius, float Height, const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
{
    if (!World)
    {
        return false;
    }

    TArray<FOverlapResult> Overlaps;
    FCollisionShape CapsuleShape = FCollisionShape::MakeCapsule(Radius, Height * 0.5f);
    FCollisionQueryParams QueryParams = AuracronPCGCollisionSystemUtils::CreateCollisionQueryParams(CollisionDescriptor);

    return !World->OverlapAnyTestByChannel(Location, FQuat::Identity, CollisionDescriptor.CollisionChannel, CapsuleShape, QueryParams);
}

bool UAuracronPCGCollisionSystemUtils::ValidateCollisionDescriptor(const FAuracronPCGCollisionDescriptor& CollisionDescriptor)
{
    // Validate trace distance
    if (CollisionDescriptor.TraceDistance <= 0.0f)
    {
        return false;
    }

    // Validate shape parameters
    if (CollisionDescriptor.SphereRadius <= 0.0f)
    {
        return false;
    }

    if (CollisionDescriptor.BoxExtent.GetMin() <= 0.0f)
    {
        return false;
    }

    if (CollisionDescriptor.CapsuleRadius <= 0.0f || CollisionDescriptor.CapsuleHalfHeight <= 0.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGCollisionSystemUtils::ValidatePhysicsDescriptor(const FAuracronPCGPhysicsDescriptor& PhysicsDescriptor)
{
    // Validate mass
    if (PhysicsDescriptor.Mass <= 0.0f)
    {
        return false;
    }

    // Validate damping values
    if (PhysicsDescriptor.LinearDamping < 0.0f || PhysicsDescriptor.AngularDamping < 0.0f)
    {
        return false;
    }

    // Validate material properties
    if (PhysicsDescriptor.Friction < 0.0f || PhysicsDescriptor.Restitution < 0.0f || PhysicsDescriptor.Density <= 0.0f)
    {
        return false;
    }

    return true;
}
