// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGElementBase.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGElementBase() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGElementParams();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGElementResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
PCG_API UClass* Z_Construct_UClass_UPCGSettings();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronPCGElementResult *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult;
class UScriptStruct* FAuracronPCGElementResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGElementResult, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGElementResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element execution result\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element execution result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputDataCount_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionTimeSeconds_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointsProcessed_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metadata_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OutputDataCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionTimeSeconds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PointsProcessed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metadata_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metadata_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Metadata;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGElementResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronPCGElementResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPCGElementResult), &Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_OutputDataCount = { "OutputDataCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementResult, OutputDataCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputDataCount_MetaData), NewProp_OutputDataCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_ExecutionTimeSeconds = { "ExecutionTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementResult, ExecutionTimeSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionTimeSeconds_MetaData), NewProp_ExecutionTimeSeconds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_PointsProcessed = { "PointsProcessed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementResult, PointsProcessed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointsProcessed_MetaData), NewProp_PointsProcessed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata_ValueProp = { "Metadata", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata_Key_KeyProp = { "Metadata_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata = { "Metadata", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementResult, Metadata), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metadata_MetaData), NewProp_Metadata_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_OutputDataCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_ExecutionTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_PointsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewProp_Metadata,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGElementResult",
	Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::PropPointers),
	sizeof(FAuracronPCGElementResult),
	alignof(FAuracronPCGElementResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGElementResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGElementResult *******************************************

// ********** Begin ScriptStruct FAuracronPCGElementParams *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams;
class UScriptStruct* FAuracronPCGElementParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPCGElementParams, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronPCGElementParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element execution parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element execution parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StringParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoolParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotatorParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StringParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StringParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StringParameters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FloatParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FloatParameters;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IntParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IntParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_IntParameters;
	static const UECodeGen_Private::FBoolPropertyParams NewProp_BoolParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BoolParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BoolParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotatorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RotatorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RotatorParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPCGElementParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters_ValueProp = { "StringParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters_Key_KeyProp = { "StringParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters = { "StringParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, StringParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StringParameters_MetaData), NewProp_StringParameters_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters_ValueProp = { "FloatParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters_Key_KeyProp = { "FloatParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters = { "FloatParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, FloatParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatParameters_MetaData), NewProp_FloatParameters_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters_ValueProp = { "IntParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters_Key_KeyProp = { "IntParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters = { "IntParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, IntParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntParameters_MetaData), NewProp_IntParameters_MetaData) };
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters_ValueProp = { "BoolParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters_Key_KeyProp = { "BoolParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters = { "BoolParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, BoolParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoolParameters_MetaData), NewProp_BoolParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters_ValueProp = { "RotatorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters_Key_KeyProp = { "RotatorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters = { "RotatorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPCGElementParams, RotatorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotatorParameters_MetaData), NewProp_RotatorParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_StringParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_FloatParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_IntParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_BoolParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_VectorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewProp_RotatorParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronPCGElementParams",
	Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::PropPointers),
	sizeof(FAuracronPCGElementParams),
	alignof(FAuracronPCGElementParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPCGElementParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPCGElementParams *******************************************

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
void UAuracronPCGSettingsBase::StaticRegisterNativesUAuracronPCGSettingsBase()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSettingsBase;
UClass* UAuracronPCGSettingsBase::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSettingsBase;
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSettingsBase"),
			Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSettingsBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister()
{
	return UAuracronPCGSettingsBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base settings class for AURACRON PCG elements\n * Provides common functionality and parameter management\n */" },
#endif
		{ "IncludePath", "AuracronPCGElementBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base settings class for AURACRON PCG elements\nProvides common functionality and parameter management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementParameters_MetaData[] = {
		{ "Category", "Element Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Element parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Element parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDebugMode_MetaData[] = {
		{ "Category", "Element Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// bEnabled is inherited from UPCGSettingsInterface, no need to redeclare\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "bEnabled is inherited from UPCGSettingsInterface, no need to redeclare" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementName_MetaData[] = {
		{ "Category", "Element Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementDescription_MetaData[] = {
		{ "Category", "Element Settings" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMultithreading_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGElementBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementParameters;
	static void NewProp_bDebugMode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDebugMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ElementName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ElementDescription;
	static void NewProp_bEnableMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSettingsBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementParameters = { "ElementParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, ElementParameters), Z_Construct_UScriptStruct_FAuracronPCGElementParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementParameters_MetaData), NewProp_ElementParameters_MetaData) }; // 2554864125
void Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bDebugMode_SetBit(void* Obj)
{
	((UAuracronPCGSettingsBase*)Obj)->bDebugMode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bDebugMode = { "bDebugMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSettingsBase), &Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bDebugMode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDebugMode_MetaData), NewProp_bDebugMode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementName = { "ElementName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, ElementName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementName_MetaData), NewProp_ElementName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementDescription = { "ElementDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, ElementDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementDescription_MetaData), NewProp_ElementDescription_MetaData) };
void Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bEnableMultithreading_SetBit(void* Obj)
{
	((UAuracronPCGSettingsBase*)Obj)->bEnableMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bEnableMultithreading = { "bEnableMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGSettingsBase), &Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bEnableMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMultithreading_MetaData), NewProp_bEnableMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize = { "BatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, BatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchSize_MetaData), NewProp_BatchSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bDebugMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_ElementDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_bEnableMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_TimeoutSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPCGSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams = {
	&UAuracronPCGSettingsBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton, Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSettingsBase);
UAuracronPCGSettingsBase::~UAuracronPCGSettingsBase() {}
// ********** End Class UAuracronPCGSettingsBase ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPCGElementResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGElementResult_Statics::NewStructOps, TEXT("AuracronPCGElementResult"), &Z_Registration_Info_UScriptStruct_FAuracronPCGElementResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGElementResult), 3116935018U) },
		{ FAuracronPCGElementParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronPCGElementParams_Statics::NewStructOps, TEXT("AuracronPCGElementParams"), &Z_Registration_Info_UScriptStruct_FAuracronPCGElementParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPCGElementParams), 2554864125U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGSettingsBase, UAuracronPCGSettingsBase::StaticClass, TEXT("UAuracronPCGSettingsBase"), &Z_Registration_Info_UClass_UAuracronPCGSettingsBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSettingsBase), 3156270579U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_690847978(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGElementBase_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
