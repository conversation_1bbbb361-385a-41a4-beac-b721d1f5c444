// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronSigilosBridge.h"

#ifdef AURACRONSIGILOSBRIDGE_AuracronSigilosBridge_generated_h
#error "AuracronSigilosBridge.generated.h already included, missing '#pragma once' in AuracronSigilosBridge.h"
#endif
#define AURACRONSIGILOSBRIDGE_AuracronSigilosBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UGameplayAbility;
enum class EAuracronSigiloFusionState : uint8;
enum class EAuracronSigiloType : uint8;
struct FAuracronSigiloConfiguration;

// ********** Begin ScriptStruct FAuracronSigiloProperties *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_85_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloProperties;
// ********** End ScriptStruct FAuracronSigiloProperties *******************************************

// ********** Begin ScriptStruct FAuracronSigiloPassiveBonuses *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloPassiveBonuses;
// ********** End ScriptStruct FAuracronSigiloPassiveBonuses ***************************************

// ********** Begin ScriptStruct FAuracronSigiloConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloConfiguration;
// ********** End ScriptStruct FAuracronSigiloConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronSigiloConfigurationEntry *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_220_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloConfigurationEntry;
// ********** End ScriptStruct FAuracronSigiloConfigurationEntry ***********************************

// ********** Begin ScriptStruct FAuracronSigiloExclusiveAbility ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_245_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloExclusiveAbility;
// ********** End ScriptStruct FAuracronSigiloExclusiveAbility *************************************

// ********** Begin ScriptStruct FAuracronSigiloVisualEffects **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_290_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloVisualEffects;
// ********** End ScriptStruct FAuracronSigiloVisualEffects ****************************************

// ********** Begin ScriptStruct FAuracronSigiloAudioEffects ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_331_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloAudioEffects;
// ********** End ScriptStruct FAuracronSigiloAudioEffects *****************************************

// ********** Begin Delegate FOnSigiloSelected *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_584_DELEGATE \
static void FOnSigiloSelected_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloSelected, EAuracronSigiloType SigiloType);


// ********** End Delegate FOnSigiloSelected *******************************************************

// ********** Begin Delegate FOnFusionStarted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_589_DELEGATE \
static void FOnFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionStarted);


// ********** End Delegate FOnFusionStarted ********************************************************

// ********** Begin Delegate FOnFusionCompleted ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_594_DELEGATE \
static void FOnFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionCompleted, EAuracronSigiloType SigiloType);


// ********** End Delegate FOnFusionCompleted ******************************************************

// ********** Begin Delegate FOnSigiloReforged *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_599_DELEGATE \
static void FOnSigiloReforged_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloReforged, EAuracronSigiloType OldSigilo, EAuracronSigiloType NewSigilo);


// ********** End Delegate FOnSigiloReforged *******************************************************

// ********** Begin Class UAuracronSigilosBridge ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_FusionState); \
	DECLARE_FUNCTION(execOnRep_SelectedSigiloType); \
	DECLARE_FUNCTION(execLoadDefaultSigiloConfigurations); \
	DECLARE_FUNCTION(execSetSigiloConfiguration); \
	DECLARE_FUNCTION(execGetSigiloConfiguration); \
	DECLARE_FUNCTION(execCanReforge); \
	DECLARE_FUNCTION(execGetReforgeCooldownRemaining); \
	DECLARE_FUNCTION(execGetTimeToFusion); \
	DECLARE_FUNCTION(execGetSelectedSigilo); \
	DECLARE_FUNCTION(execGetFusionState); \
	DECLARE_FUNCTION(execRemovePassiveBonuses); \
	DECLARE_FUNCTION(execApplyPassiveBonuses); \
	DECLARE_FUNCTION(execGetAlternativeAbilityTree); \
	DECLARE_FUNCTION(execActivateExclusiveAbility); \
	DECLARE_FUNCTION(execCancelSigiloFusion); \
	DECLARE_FUNCTION(execReforgeSigilo); \
	DECLARE_FUNCTION(execCompleteSigiloFusion); \
	DECLARE_FUNCTION(execStartSigiloFusion); \
	DECLARE_FUNCTION(execSelectSigilo);


AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronSigilosBridge(); \
	friend struct Z_Construct_UClass_UAuracronSigilosBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronSigilosBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronSigilosBridge"), Z_Construct_UClass_UAuracronSigilosBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronSigilosBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SigiloConfigurations=NETFIELD_REP_START, \
		SelectedSigiloType, \
		CurrentFusionState, \
		FusionStartTime, \
		LastReforgeTime, \
		NETFIELD_REP_END=LastReforgeTime	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronSigilosBridge(UAuracronSigilosBridge&&) = delete; \
	UAuracronSigilosBridge(const UAuracronSigilosBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronSigilosBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronSigilosBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronSigilosBridge) \
	NO_API virtual ~UAuracronSigilosBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_364_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_367_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronSigilosBridge;

// ********** End Class UAuracronSigilosBridge *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h

// ********** Begin Enum EAuracronSigiloType *******************************************************
#define FOREACH_ENUM_EAURACRONSIGILOTYPE(op) \
	op(EAuracronSigiloType::None) \
	op(EAuracronSigiloType::Aegis) \
	op(EAuracronSigiloType::Ruin) \
	op(EAuracronSigiloType::Vesper) 

enum class EAuracronSigiloType : uint8;
template<> struct TIsUEnumClass<EAuracronSigiloType> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloType>();
// ********** End Enum EAuracronSigiloType *********************************************************

// ********** Begin Enum EAuracronSigiloFusionState ************************************************
#define FOREACH_ENUM_EAURACRONSIGILOFUSIONSTATE(op) \
	op(EAuracronSigiloFusionState::Inactive) \
	op(EAuracronSigiloFusionState::Charging) \
	op(EAuracronSigiloFusionState::Active) \
	op(EAuracronSigiloFusionState::Cooldown) \
	op(EAuracronSigiloFusionState::Reforging) 

enum class EAuracronSigiloFusionState : uint8;
template<> struct TIsUEnumClass<EAuracronSigiloFusionState> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloFusionState>();
// ********** End Enum EAuracronSigiloFusionState **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
