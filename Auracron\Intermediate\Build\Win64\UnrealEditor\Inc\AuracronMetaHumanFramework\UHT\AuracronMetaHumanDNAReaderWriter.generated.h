// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanDNAReaderWriter.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanDNAReaderWriter_generated_h
#error "AuracronMetaHumanDNAReaderWriter.generated.h already included, missing '#pragma once' in AuracronMetaHumanDNAReaderWriter.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanDNAReaderWriter_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FAuracronDNAData;
struct FAuracronDNAReadResult;
struct FAuracronDNAWriteResult;

// ********** Begin ScriptStruct FAuracronVertexDelta **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_15_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVertexDelta_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVertexDelta;
// ********** End ScriptStruct FAuracronVertexDelta ************************************************

// ********** Begin ScriptStruct FAuracronBlendShapeData *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBlendShapeData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBlendShapeData;
// ********** End ScriptStruct FAuracronBlendShapeData *********************************************

// ********** Begin ScriptStruct FAuracronDNAData **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_42_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAData;
// ********** End ScriptStruct FAuracronDNAData ****************************************************

// ********** Begin ScriptStruct FAuracronDNAReadResult ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_66_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAReadResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAReadResult;
// ********** End ScriptStruct FAuracronDNAReadResult **********************************************

// ********** Begin ScriptStruct FAuracronDNAWriteResult *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_86_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAWriteResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAWriteResult;
// ********** End ScriptStruct FAuracronDNAWriteResult *********************************************

// ********** Begin Class UAuracronMetaHumanDNAReaderWriter ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetWriterStatus); \
	DECLARE_FUNCTION(execGetReaderStatus); \
	DECLARE_FUNCTION(execIsWriterReady); \
	DECLARE_FUNCTION(execIsReaderReady); \
	DECLARE_FUNCTION(execGetCurrentDNAData); \
	DECLARE_FUNCTION(execValidateDNAData); \
	DECLARE_FUNCTION(execWriteDNAToMemory); \
	DECLARE_FUNCTION(execWriteDNAToFile); \
	DECLARE_FUNCTION(execReadDNAFromMemory); \
	DECLARE_FUNCTION(execReadDNAFromFile); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanDNAReaderWriter(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanDNAReaderWriter, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanDNAReaderWriter_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanDNAReaderWriter)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanDNAReaderWriter(UAuracronMetaHumanDNAReaderWriter&&) = delete; \
	UAuracronMetaHumanDNAReaderWriter(const UAuracronMetaHumanDNAReaderWriter&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanDNAReaderWriter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanDNAReaderWriter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanDNAReaderWriter)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_114_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h_117_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanDNAReaderWriter;

// ********** End Class UAuracronMetaHumanDNAReaderWriter ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNAReaderWriter_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
