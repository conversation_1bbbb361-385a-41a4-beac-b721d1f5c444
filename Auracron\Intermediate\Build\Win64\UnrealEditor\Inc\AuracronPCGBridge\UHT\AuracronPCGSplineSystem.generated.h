// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGSplineSystem.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGSplineSystem_generated_h
#error "AuracronPCGSplineSystem.generated.h already included, missing '#pragma once' in AuracronPCGSplineSystem.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGSplineSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGPointData;
class USplineComponent;
class USplineMeshComponent;
class UStaticMesh;
enum class EAuracronPCGSplineDistributionMode : uint8;
enum class EAuracronPCGSplineTangentMode : uint8;
struct FAuracronPCGPathFindingDescriptor;
struct FAuracronPCGSplineMeshDescriptor;

// ********** Begin ScriptStruct FAuracronPCGSplinePointDescriptor *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_139_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGSplinePointDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGSplinePointDescriptor;
// ********** End ScriptStruct FAuracronPCGSplinePointDescriptor ***********************************

// ********** Begin ScriptStruct FAuracronPCGSplineMeshDescriptor **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_200_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGSplineMeshDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGSplineMeshDescriptor;
// ********** End ScriptStruct FAuracronPCGSplineMeshDescriptor ************************************

// ********** Begin ScriptStruct FAuracronPCGPathFindingDescriptor *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_271_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPCGPathFindingDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPCGPathFindingDescriptor;
// ********** End ScriptStruct FAuracronPCGPathFindingDescriptor ***********************************

// ********** Begin Class UAuracronPCGAdvancedSplineCreatorSettings ********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_349_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGAdvancedSplineCreatorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGAdvancedSplineCreatorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGAdvancedSplineCreatorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGAdvancedSplineCreatorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_349_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGAdvancedSplineCreatorSettings(UAuracronPCGAdvancedSplineCreatorSettings&&) = delete; \
	UAuracronPCGAdvancedSplineCreatorSettings(const UAuracronPCGAdvancedSplineCreatorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGAdvancedSplineCreatorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGAdvancedSplineCreatorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGAdvancedSplineCreatorSettings) \
	NO_API virtual ~UAuracronPCGAdvancedSplineCreatorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_346_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_349_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_349_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_349_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGAdvancedSplineCreatorSettings;

// ********** End Class UAuracronPCGAdvancedSplineCreatorSettings **********************************

// ********** Begin Class UAuracronPCGSplinePointDistributorSettings *******************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_443_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSplinePointDistributorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSplinePointDistributorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSplinePointDistributorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSplinePointDistributorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_443_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSplinePointDistributorSettings(UAuracronPCGSplinePointDistributorSettings&&) = delete; \
	UAuracronPCGSplinePointDistributorSettings(const UAuracronPCGSplinePointDistributorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSplinePointDistributorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSplinePointDistributorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGSplinePointDistributorSettings) \
	NO_API virtual ~UAuracronPCGSplinePointDistributorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_440_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_443_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_443_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_443_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSplinePointDistributorSettings;

// ********** End Class UAuracronPCGSplinePointDistributorSettings *********************************

// ********** Begin Class UAuracronPCGSplineMeshGeneratorSettings **********************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_558_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSplineMeshGeneratorSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSplineMeshGeneratorSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSplineMeshGeneratorSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSplineMeshGeneratorSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_558_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSplineMeshGeneratorSettings(UAuracronPCGSplineMeshGeneratorSettings&&) = delete; \
	UAuracronPCGSplineMeshGeneratorSettings(const UAuracronPCGSplineMeshGeneratorSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSplineMeshGeneratorSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSplineMeshGeneratorSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGSplineMeshGeneratorSettings) \
	NO_API virtual ~UAuracronPCGSplineMeshGeneratorSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_555_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_558_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_558_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_558_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSplineMeshGeneratorSettings;

// ********** End Class UAuracronPCGSplineMeshGeneratorSettings ************************************

// ********** Begin Class UAuracronPCGSplinePathFinderSettings *************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_651_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSplinePathFinderSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSplinePathFinderSettings, UAuracronPCGNodeSettings, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSplinePathFinderSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSplinePathFinderSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_651_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSplinePathFinderSettings(UAuracronPCGSplinePathFinderSettings&&) = delete; \
	UAuracronPCGSplinePathFinderSettings(const UAuracronPCGSplinePathFinderSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSplinePathFinderSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSplinePathFinderSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPCGSplinePathFinderSettings) \
	NO_API virtual ~UAuracronPCGSplinePathFinderSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_648_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_651_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_651_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_651_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSplinePathFinderSettings;

// ********** End Class UAuracronPCGSplinePathFinderSettings ***************************************

// ********** Begin Class UAuracronPCGSplineSystemUtils ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetOptimalPointCount); \
	DECLARE_FUNCTION(execSimplifySpline); \
	DECLARE_FUNCTION(execOptimizeSpline); \
	DECLARE_FUNCTION(execGetSplineWidthAtDistance); \
	DECLARE_FUNCTION(execGetSplineDirectionAtDistance); \
	DECLARE_FUNCTION(execCalculateSplineCurvature); \
	DECLARE_FUNCTION(execSmoothPath); \
	DECLARE_FUNCTION(execIsPathValid); \
	DECLARE_FUNCTION(execFindPath); \
	DECLARE_FUNCTION(execGenerateSplineMeshes); \
	DECLARE_FUNCTION(execCreateSplineMeshComponent); \
	DECLARE_FUNCTION(execCalculateSplineLength); \
	DECLARE_FUNCTION(execDistributeTransformsAlongSpline); \
	DECLARE_FUNCTION(execDistributePointsAlongSpline); \
	DECLARE_FUNCTION(execValidateSplinePoints); \
	DECLARE_FUNCTION(execCreateSplineFromPointData); \
	DECLARE_FUNCTION(execCreateSplineFromPoints);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSplineSystemUtils(); \
	friend struct Z_Construct_UClass_UAuracronPCGSplineSystemUtils_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSplineSystemUtils_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSplineSystemUtils, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSplineSystemUtils_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSplineSystemUtils)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronPCGSplineSystemUtils(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSplineSystemUtils(UAuracronPCGSplineSystemUtils&&) = delete; \
	UAuracronPCGSplineSystemUtils(const UAuracronPCGSplineSystemUtils&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSplineSystemUtils); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSplineSystemUtils); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGSplineSystemUtils) \
	NO_API virtual ~UAuracronPCGSplineSystemUtils();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_724_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h_727_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSplineSystemUtils;

// ********** End Class UAuracronPCGSplineSystemUtils **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGSplineSystem_h

// ********** Begin Enum EAuracronPCGSplineCreationMode ********************************************
#define FOREACH_ENUM_EAURACRONPCGSPLINECREATIONMODE(op) \
	op(EAuracronPCGSplineCreationMode::FromPoints) \
	op(EAuracronPCGSplineCreationMode::FromCurve) \
	op(EAuracronPCGSplineCreationMode::FromPath) \
	op(EAuracronPCGSplineCreationMode::Procedural) \
	op(EAuracronPCGSplineCreationMode::Bezier) \
	op(EAuracronPCGSplineCreationMode::CatmullRom) \
	op(EAuracronPCGSplineCreationMode::Linear) \
	op(EAuracronPCGSplineCreationMode::Smooth) 

enum class EAuracronPCGSplineCreationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplineCreationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineCreationMode>();
// ********** End Enum EAuracronPCGSplineCreationMode **********************************************

// ********** Begin Enum EAuracronPCGSplineDistributionMode ****************************************
#define FOREACH_ENUM_EAURACRONPCGSPLINEDISTRIBUTIONMODE(op) \
	op(EAuracronPCGSplineDistributionMode::Uniform) \
	op(EAuracronPCGSplineDistributionMode::ByDistance) \
	op(EAuracronPCGSplineDistributionMode::BySegment) \
	op(EAuracronPCGSplineDistributionMode::ByParameter) \
	op(EAuracronPCGSplineDistributionMode::Random) \
	op(EAuracronPCGSplineDistributionMode::Noise) \
	op(EAuracronPCGSplineDistributionMode::Curve) \
	op(EAuracronPCGSplineDistributionMode::Adaptive) 

enum class EAuracronPCGSplineDistributionMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplineDistributionMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineDistributionMode>();
// ********** End Enum EAuracronPCGSplineDistributionMode ******************************************

// ********** Begin Enum EAuracronPCGSplineMeshMode ************************************************
#define FOREACH_ENUM_EAURACRONPCGSPLINEMESHMODE(op) \
	op(EAuracronPCGSplineMeshMode::SingleMesh) \
	op(EAuracronPCGSplineMeshMode::SegmentMesh) \
	op(EAuracronPCGSplineMeshMode::TiledMesh) \
	op(EAuracronPCGSplineMeshMode::AdaptiveMesh) \
	op(EAuracronPCGSplineMeshMode::ProceduralMesh) \
	op(EAuracronPCGSplineMeshMode::InstancedMesh) \
	op(EAuracronPCGSplineMeshMode::SplineMeshComponent) \
	op(EAuracronPCGSplineMeshMode::CustomMesh) 

enum class EAuracronPCGSplineMeshMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplineMeshMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineMeshMode>();
// ********** End Enum EAuracronPCGSplineMeshMode **************************************************

// ********** Begin Enum EAuracronPCGSplineTangentMode *********************************************
#define FOREACH_ENUM_EAURACRONPCGSPLINETANGENTMODE(op) \
	op(EAuracronPCGSplineTangentMode::Auto) \
	op(EAuracronPCGSplineTangentMode::User) \
	op(EAuracronPCGSplineTangentMode::Break) \
	op(EAuracronPCGSplineTangentMode::Linear) \
	op(EAuracronPCGSplineTangentMode::Constant) \
	op(EAuracronPCGSplineTangentMode::CurveClamped) \
	op(EAuracronPCGSplineTangentMode::CurveBreak) \
	op(EAuracronPCGSplineTangentMode::None) 

enum class EAuracronPCGSplineTangentMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplineTangentMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineTangentMode>();
// ********** End Enum EAuracronPCGSplineTangentMode ***********************************************

// ********** Begin Enum EAuracronPCGPathFindingMode ***********************************************
#define FOREACH_ENUM_EAURACRONPCGPATHFINDINGMODE(op) \
	op(EAuracronPCGPathFindingMode::None) \
	op(EAuracronPCGPathFindingMode::AStar) \
	op(EAuracronPCGPathFindingMode::Dijkstra) \
	op(EAuracronPCGPathFindingMode::FlowField) \
	op(EAuracronPCGPathFindingMode::NavMesh) \
	op(EAuracronPCGPathFindingMode::Custom) \
	op(EAuracronPCGPathFindingMode::Hybrid) \
	op(EAuracronPCGPathFindingMode::RRT) 

enum class EAuracronPCGPathFindingMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGPathFindingMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGPathFindingMode>();
// ********** End Enum EAuracronPCGPathFindingMode *************************************************

// ********** Begin Enum EAuracronPCGSplineDeformationMode *****************************************
#define FOREACH_ENUM_EAURACRONPCGSPLINEDEFORMATIONMODE(op) \
	op(EAuracronPCGSplineDeformationMode::None) \
	op(EAuracronPCGSplineDeformationMode::Bend) \
	op(EAuracronPCGSplineDeformationMode::Twist) \
	op(EAuracronPCGSplineDeformationMode::Scale) \
	op(EAuracronPCGSplineDeformationMode::Noise) \
	op(EAuracronPCGSplineDeformationMode::Wave) \
	op(EAuracronPCGSplineDeformationMode::Spiral) \
	op(EAuracronPCGSplineDeformationMode::Custom) 

enum class EAuracronPCGSplineDeformationMode : uint8;
template<> struct TIsUEnumClass<EAuracronPCGSplineDeformationMode> { enum { Value = true }; };
template<> AURACRONPCGBRIDGE_API UEnum* StaticEnum<EAuracronPCGSplineDeformationMode>();
// ********** End Enum EAuracronPCGSplineDeformationMode *******************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
