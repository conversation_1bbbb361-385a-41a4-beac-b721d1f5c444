// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageInstanced.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageInstanced() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstanceBatchData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstanceClusterData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronInstanceRenderingMode ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode;
static UEnum* EAuracronInstanceRenderingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronInstanceRenderingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceRenderingMode>()
{
	return EAuracronInstanceRenderingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustered.DisplayName", "Clustered" },
		{ "Clustered.Name", "EAuracronInstanceRenderingMode::Clustered" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance rendering modes\n" },
#endif
		{ "GPU.DisplayName", "GPU Driven" },
		{ "GPU.Name", "EAuracronInstanceRenderingMode::GPU" },
		{ "Hierarchical.DisplayName", "Hierarchical" },
		{ "Hierarchical.Name", "EAuracronInstanceRenderingMode::Hierarchical" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EAuracronInstanceRenderingMode::Nanite" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EAuracronInstanceRenderingMode::Standard" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance rendering modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInstanceRenderingMode::Standard", (int64)EAuracronInstanceRenderingMode::Standard },
		{ "EAuracronInstanceRenderingMode::Hierarchical", (int64)EAuracronInstanceRenderingMode::Hierarchical },
		{ "EAuracronInstanceRenderingMode::Clustered", (int64)EAuracronInstanceRenderingMode::Clustered },
		{ "EAuracronInstanceRenderingMode::GPU", (int64)EAuracronInstanceRenderingMode::GPU },
		{ "EAuracronInstanceRenderingMode::Nanite", (int64)EAuracronInstanceRenderingMode::Nanite },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronInstanceRenderingMode",
	"EAuracronInstanceRenderingMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode.InnerSingleton;
}
// ********** End Enum EAuracronInstanceRenderingMode **********************************************

// ********** Begin Enum EAuracronInstanceCullingMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInstanceCullingMode;
static UEnum* EAuracronInstanceCullingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronInstanceCullingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceCullingMode>()
{
	return EAuracronInstanceCullingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined" },
		{ "Combined.Name", "EAuracronInstanceCullingMode::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance culling modes\n" },
#endif
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EAuracronInstanceCullingMode::Distance" },
		{ "Frustum.DisplayName", "Frustum" },
		{ "Frustum.Name", "EAuracronInstanceCullingMode::Frustum" },
		{ "GPU.DisplayName", "GPU Culling" },
		{ "GPU.Name", "EAuracronInstanceCullingMode::GPU" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronInstanceCullingMode::None" },
		{ "Occlusion.DisplayName", "Occlusion" },
		{ "Occlusion.Name", "EAuracronInstanceCullingMode::Occlusion" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance culling modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInstanceCullingMode::None", (int64)EAuracronInstanceCullingMode::None },
		{ "EAuracronInstanceCullingMode::Frustum", (int64)EAuracronInstanceCullingMode::Frustum },
		{ "EAuracronInstanceCullingMode::Distance", (int64)EAuracronInstanceCullingMode::Distance },
		{ "EAuracronInstanceCullingMode::Occlusion", (int64)EAuracronInstanceCullingMode::Occlusion },
		{ "EAuracronInstanceCullingMode::Combined", (int64)EAuracronInstanceCullingMode::Combined },
		{ "EAuracronInstanceCullingMode::GPU", (int64)EAuracronInstanceCullingMode::GPU },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronInstanceCullingMode",
	"EAuracronInstanceCullingMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceCullingMode.InnerSingleton;
}
// ********** End Enum EAuracronInstanceCullingMode ************************************************

// ********** Begin Enum EAuracronInstanceLODMode **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInstanceLODMode;
static UEnum* EAuracronInstanceLODMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceLODMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInstanceLODMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronInstanceLODMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceLODMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceLODMode>()
{
	return EAuracronInstanceLODMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronInstanceLODMode::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance LOD modes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
		{ "PerBatch.DisplayName", "Per Batch" },
		{ "PerBatch.Name", "EAuracronInstanceLODMode::PerBatch" },
		{ "PerCluster.DisplayName", "Per Cluster" },
		{ "PerCluster.Name", "EAuracronInstanceLODMode::PerCluster" },
		{ "Performance.DisplayName", "Performance Based" },
		{ "Performance.Name", "EAuracronInstanceLODMode::Performance" },
		{ "PerInstance.DisplayName", "Per Instance" },
		{ "PerInstance.Name", "EAuracronInstanceLODMode::PerInstance" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance LOD modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInstanceLODMode::PerInstance", (int64)EAuracronInstanceLODMode::PerInstance },
		{ "EAuracronInstanceLODMode::PerCluster", (int64)EAuracronInstanceLODMode::PerCluster },
		{ "EAuracronInstanceLODMode::PerBatch", (int64)EAuracronInstanceLODMode::PerBatch },
		{ "EAuracronInstanceLODMode::Adaptive", (int64)EAuracronInstanceLODMode::Adaptive },
		{ "EAuracronInstanceLODMode::Performance", (int64)EAuracronInstanceLODMode::Performance },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronInstanceLODMode",
	"EAuracronInstanceLODMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceLODMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInstanceLODMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceLODMode.InnerSingleton;
}
// ********** End Enum EAuracronInstanceLODMode ****************************************************

// ********** Begin Enum EAuracronInstanceUpdateMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode;
static UEnum* EAuracronInstanceUpdateMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronInstanceUpdateMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronInstanceUpdateMode>()
{
	return EAuracronInstanceUpdateMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Async.DisplayName", "Async" },
		{ "Async.Name", "EAuracronInstanceUpdateMode::Async" },
		{ "Batched.DisplayName", "Batched" },
		{ "Batched.Name", "EAuracronInstanceUpdateMode::Batched" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance update modes\n" },
#endif
		{ "Deferred.DisplayName", "Deferred" },
		{ "Deferred.Name", "EAuracronInstanceUpdateMode::Deferred" },
		{ "GPU.DisplayName", "GPU Update" },
		{ "GPU.Name", "EAuracronInstanceUpdateMode::GPU" },
		{ "Immediate.DisplayName", "Immediate" },
		{ "Immediate.Name", "EAuracronInstanceUpdateMode::Immediate" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance update modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronInstanceUpdateMode::Immediate", (int64)EAuracronInstanceUpdateMode::Immediate },
		{ "EAuracronInstanceUpdateMode::Deferred", (int64)EAuracronInstanceUpdateMode::Deferred },
		{ "EAuracronInstanceUpdateMode::Batched", (int64)EAuracronInstanceUpdateMode::Batched },
		{ "EAuracronInstanceUpdateMode::Async", (int64)EAuracronInstanceUpdateMode::Async },
		{ "EAuracronInstanceUpdateMode::GPU", (int64)EAuracronInstanceUpdateMode::GPU },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronInstanceUpdateMode",
	"EAuracronInstanceUpdateMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode.InnerSingleton;
}
// ********** End Enum EAuracronInstanceUpdateMode *************************************************

// ********** Begin ScriptStruct FAuracronInstancedMeshConfiguration *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration;
class UScriptStruct* FAuracronInstancedMeshConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronInstancedMeshConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Instanced Mesh Configuration\n * Configuration settings for instanced static mesh system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instanced Mesh Configuration\nConfiguration settings for instanced static mesh system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstancedRendering_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHierarchicalInstancing_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUCulling_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNaniteSupport_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultRenderingMode_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultCullingMode_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLODMode_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultUpdateMode_MetaData[] = {
		{ "Category", "Instanced Mesh" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerComponent_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerBatch_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance0_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance1_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance2_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance3_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceDataCompression_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstancePooling_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncLoading_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstancePoolSize_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryBudgetMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFrustumCulling_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusionCulling_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDistanceCulling_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatchOptimization_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceMerging_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchOptimizationInterval_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstanceDebugDraw_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInstanceBounds_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowClusterBounds_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowCullingInfo_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableInstancedRendering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstancedRendering;
	static void NewProp_bEnableHierarchicalInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHierarchicalInstancing;
	static void NewProp_bEnableGPUCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUCulling;
	static void NewProp_bEnableNaniteSupport_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNaniteSupport;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultRenderingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultRenderingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultCullingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultCullingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultLODMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultLODMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultUpdateMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultUpdateMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerBatch;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ClusterSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance0;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance1;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance3;
	static void NewProp_bEnableInstanceDataCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceDataCompression;
	static void NewProp_bEnableInstancePooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstancePooling;
	static void NewProp_bEnableAsyncLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncLoading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstancePoolSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryBudgetMB;
	static void NewProp_bEnableFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFrustumCulling;
	static void NewProp_bEnableOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusionCulling;
	static void NewProp_bEnableDistanceCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDistanceCulling;
	static void NewProp_bEnableBatchOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatchOptimization;
	static void NewProp_bEnableInstanceMerging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceMerging;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BatchOptimizationInterval;
	static void NewProp_bEnableInstanceDebugDraw_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstanceDebugDraw;
	static void NewProp_bShowInstanceBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInstanceBounds;
	static void NewProp_bShowClusterBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowClusterBounds;
	static void NewProp_bShowCullingInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowCullingInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronInstancedMeshConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancedRendering_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableInstancedRendering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancedRendering = { "bEnableInstancedRendering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancedRendering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstancedRendering_MetaData), NewProp_bEnableInstancedRendering_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableHierarchicalInstancing_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableHierarchicalInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableHierarchicalInstancing = { "bEnableHierarchicalInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableHierarchicalInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHierarchicalInstancing_MetaData), NewProp_bEnableHierarchicalInstancing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableGPUCulling_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableGPUCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableGPUCulling = { "bEnableGPUCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableGPUCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUCulling_MetaData), NewProp_bEnableGPUCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableNaniteSupport = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableNaniteSupport = { "bEnableNaniteSupport", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableNaniteSupport_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNaniteSupport_MetaData), NewProp_bEnableNaniteSupport_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultRenderingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultRenderingMode = { "DefaultRenderingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, DefaultRenderingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultRenderingMode_MetaData), NewProp_DefaultRenderingMode_MetaData) }; // 2353152906
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultCullingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultCullingMode = { "DefaultCullingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, DefaultCullingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultCullingMode_MetaData), NewProp_DefaultCullingMode_MetaData) }; // 3579063172
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultLODMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultLODMode = { "DefaultLODMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, DefaultLODMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceLODMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLODMode_MetaData), NewProp_DefaultLODMode_MetaData) }; // 1350265438
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultUpdateMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultUpdateMode = { "DefaultUpdateMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, DefaultUpdateMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceUpdateMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultUpdateMode_MetaData), NewProp_DefaultUpdateMode_MetaData) }; // 941898752
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MaxInstancesPerComponent = { "MaxInstancesPerComponent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, MaxInstancesPerComponent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerComponent_MetaData), NewProp_MaxInstancesPerComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MaxInstancesPerBatch = { "MaxInstancesPerBatch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, MaxInstancesPerBatch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerBatch_MetaData), NewProp_MaxInstancesPerBatch_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_ClusterSize = { "ClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, ClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterSize_MetaData), NewProp_ClusterSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance0 = { "LODDistance0", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, LODDistance0), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance0_MetaData), NewProp_LODDistance0_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance1 = { "LODDistance1", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, LODDistance1), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance1_MetaData), NewProp_LODDistance1_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance2 = { "LODDistance2", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, LODDistance2), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance2_MetaData), NewProp_LODDistance2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance3 = { "LODDistance3", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, LODDistance3), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance3_MetaData), NewProp_LODDistance3_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDataCompression_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableInstanceDataCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDataCompression = { "bEnableInstanceDataCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDataCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceDataCompression_MetaData), NewProp_bEnableInstanceDataCompression_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancePooling_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableInstancePooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancePooling = { "bEnableInstancePooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancePooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstancePooling_MetaData), NewProp_bEnableInstancePooling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableAsyncLoading_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableAsyncLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableAsyncLoading = { "bEnableAsyncLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableAsyncLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncLoading_MetaData), NewProp_bEnableAsyncLoading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_InstancePoolSize = { "InstancePoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, InstancePoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstancePoolSize_MetaData), NewProp_InstancePoolSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MemoryBudgetMB = { "MemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, MemoryBudgetMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryBudgetMB_MetaData), NewProp_MemoryBudgetMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableFrustumCulling = { "bEnableFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFrustumCulling_MetaData), NewProp_bEnableFrustumCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableOcclusionCulling = { "bEnableOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusionCulling_MetaData), NewProp_bEnableOcclusionCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableDistanceCulling_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableDistanceCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableDistanceCulling = { "bEnableDistanceCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableDistanceCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDistanceCulling_MetaData), NewProp_bEnableDistanceCulling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableBatchOptimization_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableBatchOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableBatchOptimization = { "bEnableBatchOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableBatchOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatchOptimization_MetaData), NewProp_bEnableBatchOptimization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceMerging_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableInstanceMerging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceMerging = { "bEnableInstanceMerging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceMerging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceMerging_MetaData), NewProp_bEnableInstanceMerging_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_BatchOptimizationInterval = { "BatchOptimizationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstancedMeshConfiguration, BatchOptimizationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchOptimizationInterval_MetaData), NewProp_BatchOptimizationInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDebugDraw_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bEnableInstanceDebugDraw = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDebugDraw = { "bEnableInstanceDebugDraw", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDebugDraw_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstanceDebugDraw_MetaData), NewProp_bEnableInstanceDebugDraw_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowInstanceBounds_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bShowInstanceBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowInstanceBounds = { "bShowInstanceBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowInstanceBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInstanceBounds_MetaData), NewProp_bShowInstanceBounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowClusterBounds_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bShowClusterBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowClusterBounds = { "bShowClusterBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowClusterBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowClusterBounds_MetaData), NewProp_bShowClusterBounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowCullingInfo_SetBit(void* Obj)
{
	((FAuracronInstancedMeshConfiguration*)Obj)->bShowCullingInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowCullingInfo = { "bShowCullingInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstancedMeshConfiguration), &Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowCullingInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowCullingInfo_MetaData), NewProp_bShowCullingInfo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancedRendering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableHierarchicalInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableGPUCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableNaniteSupport,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultRenderingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultRenderingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultCullingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultCullingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultLODMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultLODMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultUpdateMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_DefaultUpdateMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MaxInstancesPerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MaxInstancesPerBatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_ClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance0,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_LODDistance3,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDataCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstancePooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableAsyncLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_InstancePoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_MemoryBudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableFrustumCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableDistanceCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableBatchOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceMerging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_BatchOptimizationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bEnableInstanceDebugDraw,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowInstanceBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowClusterBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewProp_bShowCullingInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronInstancedMeshConfiguration",
	Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::PropPointers),
	sizeof(FAuracronInstancedMeshConfiguration),
	alignof(FAuracronInstancedMeshConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronInstancedMeshConfiguration *********************************

// ********** Begin ScriptStruct FAuracronInstanceBatchData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData;
class UScriptStruct* FAuracronInstanceBatchData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronInstanceBatchData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronInstanceBatchData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Instance Batch Data\n * Data structure for batched instance rendering\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance Batch Data\nData structure for batched instance rendering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StaticMesh_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Material_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransforms_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceColors_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceCustomData_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderingMode_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingMode_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReceiveDecals_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoundingBox_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Batch" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Material;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceTransforms;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceColors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceColors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InstanceCustomData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceCustomData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RenderingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RenderingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CullingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CullingMode;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static void NewProp_bReceiveDecals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReceiveDecals;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BoundingBox;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronInstanceBatchData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StaticMesh_MetaData), NewProp_StaticMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Material_MetaData), NewProp_Material_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceTransforms_Inner = { "InstanceTransforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceTransforms = { "InstanceTransforms", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, InstanceTransforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransforms_MetaData), NewProp_InstanceTransforms_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceColors_Inner = { "InstanceColors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceColors = { "InstanceColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, InstanceColors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceColors_MetaData), NewProp_InstanceColors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceCustomData_Inner = { "InstanceCustomData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceCustomData = { "InstanceCustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, InstanceCustomData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceCustomData_MetaData), NewProp_InstanceCustomData_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_RenderingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_RenderingMode = { "RenderingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, RenderingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceRenderingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderingMode_MetaData), NewProp_RenderingMode_MetaData) }; // 2353152906
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CullingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CullingMode = { "CullingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, CullingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronInstanceCullingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingMode_MetaData), NewProp_CullingMode_MetaData) }; // 3579063172
void Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuracronInstanceBatchData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstanceBatchData), &Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FAuracronInstanceBatchData*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstanceBatchData), &Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bReceiveDecals_SetBit(void* Obj)
{
	((FAuracronInstanceBatchData*)Obj)->bReceiveDecals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bReceiveDecals = { "bReceiveDecals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstanceBatchData), &Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bReceiveDecals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReceiveDecals_MetaData), NewProp_bReceiveDecals_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_BoundingBox = { "BoundingBox", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, BoundingBox), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoundingBox_MetaData), NewProp_BoundingBox_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceBatchData, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceTransforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceTransforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceColors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceColors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceCustomData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_InstanceCustomData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_RenderingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_RenderingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CullingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CullingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bCastShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_bReceiveDecals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_BoundingBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronInstanceBatchData",
	Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::PropPointers),
	sizeof(FAuracronInstanceBatchData),
	alignof(FAuracronInstanceBatchData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstanceBatchData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronInstanceBatchData ******************************************

// ********** Begin ScriptStruct FAuracronInstanceClusterData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData;
class UScriptStruct* FAuracronInstanceClusterData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronInstanceClusterData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronInstanceClusterData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Instance Cluster Data\n * Data structure for hierarchical instance clustering\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance Cluster Data\nData structure for hierarchical instance clustering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterId_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParentBatchId_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceIndices_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterBounds_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterCenter_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCulled_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToViewer_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenSize_MetaData[] = {
		{ "Category", "Cluster" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClusterId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParentBatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceIndices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ClusterBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ClusterCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_bIsCulled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCulled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToViewer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronInstanceClusterData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterId = { "ClusterId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ClusterId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterId_MetaData), NewProp_ClusterId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ParentBatchId = { "ParentBatchId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ParentBatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParentBatchId_MetaData), NewProp_ParentBatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_InstanceIndices_Inner = { "InstanceIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_InstanceIndices = { "InstanceIndices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, InstanceIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceIndices_MetaData), NewProp_InstanceIndices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterBounds = { "ClusterBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ClusterBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterBounds_MetaData), NewProp_ClusterBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterCenter = { "ClusterCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ClusterCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterCenter_MetaData), NewProp_ClusterCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuracronInstanceClusterData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstanceClusterData), &Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
void Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsCulled_SetBit(void* Obj)
{
	((FAuracronInstanceClusterData*)Obj)->bIsCulled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsCulled = { "bIsCulled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronInstanceClusterData), &Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsCulled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCulled_MetaData), NewProp_bIsCulled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_DistanceToViewer = { "DistanceToViewer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, DistanceToViewer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToViewer_MetaData), NewProp_DistanceToViewer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ScreenSize = { "ScreenSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronInstanceClusterData, ScreenSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenSize_MetaData), NewProp_ScreenSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ParentBatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_InstanceIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_InstanceIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_bIsCulled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_DistanceToViewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewProp_ScreenSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronInstanceClusterData",
	Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::PropPointers),
	sizeof(FAuracronInstanceClusterData),
	alignof(FAuracronInstanceClusterData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronInstanceClusterData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronInstanceClusterData ****************************************

// ********** Begin Delegate FOnBatchCreated *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics
{
	struct AuracronFoliageInstancedManager_eventOnBatchCreated_Parms
	{
		FString BatchId;
		FAuracronInstanceBatchData BatchData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BatchData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnBatchCreated_Parms, BatchId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::NewProp_BatchData = { "BatchData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnBatchCreated_Parms, BatchData), Z_Construct_UScriptStruct_FAuracronInstanceBatchData, METADATA_PARAMS(0, nullptr) }; // 2758718836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::NewProp_BatchData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OnBatchCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInstancedManager::FOnBatchCreated_DelegateWrapper(const FMulticastScriptDelegate& OnBatchCreated, const FString& BatchId, FAuracronInstanceBatchData BatchData)
{
	struct AuracronFoliageInstancedManager_eventOnBatchCreated_Parms
	{
		FString BatchId;
		FAuracronInstanceBatchData BatchData;
	};
	AuracronFoliageInstancedManager_eventOnBatchCreated_Parms Parms;
	Parms.BatchId=BatchId;
	Parms.BatchData=BatchData;
	OnBatchCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBatchCreated *********************************************************

// ********** Begin Delegate FOnBatchDestroyed *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics
{
	struct AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms, BatchId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OnBatchDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInstancedManager::FOnBatchDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnBatchDestroyed, const FString& BatchId)
{
	struct AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms
	{
		FString BatchId;
	};
	AuracronFoliageInstancedManager_eventOnBatchDestroyed_Parms Parms;
	Parms.BatchId=BatchId;
	OnBatchDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBatchDestroyed *******************************************************

// ********** Begin Delegate FOnInstanceAdded ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics
{
	struct AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms, BatchId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::NewProp_InstanceIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OnInstanceAdded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInstancedManager::FOnInstanceAdded_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceAdded, const FString& BatchId, int32 InstanceIndex)
{
	struct AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
	};
	AuracronFoliageInstancedManager_eventOnInstanceAdded_Parms Parms;
	Parms.BatchId=BatchId;
	Parms.InstanceIndex=InstanceIndex;
	OnInstanceAdded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnInstanceAdded ********************************************************

// ********** Begin Delegate FOnInstanceRemoved ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms, BatchId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OnInstanceRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInstancedManager::FOnInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceRemoved, const FString& BatchId, int32 InstanceIndex)
{
	struct AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
	};
	AuracronFoliageInstancedManager_eventOnInstanceRemoved_Parms Parms;
	Parms.BatchId=BatchId;
	Parms.InstanceIndex=InstanceIndex;
	OnInstanceRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnInstanceRemoved ******************************************************

// ********** Begin Delegate FOnBatchOptimized *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics
{
	struct AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms, BatchId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OnBatchOptimized__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageInstancedManager::FOnBatchOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnBatchOptimized, const FString& BatchId)
{
	struct AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms
	{
		FString BatchId;
	};
	AuracronFoliageInstancedManager_eventOnBatchOptimized_Parms Parms;
	Parms.BatchId=BatchId;
	OnBatchOptimized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBatchOptimized *******************************************************

// ********** Begin Class UAuracronFoliageInstancedManager Function AddInstance ********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics
{
	struct AuracronFoliageInstancedManager_eventAddInstance_Parms
	{
		FString BatchId;
		FTransform Transform;
		FLinearColor Color;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance management\n" },
#endif
		{ "CPP_Default_Color", "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstance_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstance_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstance_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "AddInstance", Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::AuracronFoliageInstancedManager_eventAddInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::AuracronFoliageInstancedManager_eventAddInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execAddInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AddInstance(Z_Param_BatchId,Z_Param_Out_Transform,Z_Param_Out_Color);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function AddInstance **********************

// ********** Begin Class UAuracronFoliageInstancedManager Function AddInstances *******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics
{
	struct AuracronFoliageInstancedManager_eventAddInstances_Parms
	{
		FString BatchId;
		TArray<FTransform> Transforms;
		TArray<FLinearColor> Colors;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Colors_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Colors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Colors;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstances_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstances_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Colors_Inner = { "Colors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Colors = { "Colors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstances_Parms, Colors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Colors_MetaData), NewProp_Colors_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstances_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Colors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_Colors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "AddInstances", Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::AuracronFoliageInstancedManager_eventAddInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::AuracronFoliageInstancedManager_eventAddInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execAddInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_GET_TARRAY_REF(FLinearColor,Z_Param_Out_Colors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AddInstances(Z_Param_BatchId,Z_Param_Out_Transforms,Z_Param_Out_Colors);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function AddInstances *********************

// ********** Begin Class UAuracronFoliageInstancedManager Function AddInstancesSimple *************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics
{
	struct AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms
	{
		FString BatchId;
		TArray<FTransform> Transforms;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "AddInstancesSimple", Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::AuracronFoliageInstancedManager_eventAddInstancesSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execAddInstancesSimple)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AddInstancesSimple(Z_Param_BatchId,Z_Param_Out_Transforms);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function AddInstancesSimple ***************

// ********** Begin Class UAuracronFoliageInstancedManager Function BuildClusters ******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics
{
	struct AuracronFoliageInstancedManager_eventBuildClusters_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Clustering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clustering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventBuildClusters_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "BuildClusters", Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::AuracronFoliageInstancedManager_eventBuildClusters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::AuracronFoliageInstancedManager_eventBuildClusters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execBuildClusters)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BuildClusters(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function BuildClusters ********************

// ********** Begin Class UAuracronFoliageInstancedManager Function ClearInstances *****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics
{
	struct AuracronFoliageInstancedManager_eventClearInstances_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventClearInstances_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "ClearInstances", Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::AuracronFoliageInstancedManager_eventClearInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::AuracronFoliageInstancedManager_eventClearInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execClearInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearInstances(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function ClearInstances *******************

// ********** Begin Class UAuracronFoliageInstancedManager Function CompactBatches *****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "CompactBatches", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execCompactBatches)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompactBatches();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function CompactBatches *******************

// ********** Begin Class UAuracronFoliageInstancedManager Function CompressInstanceData ***********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics
{
	struct AuracronFoliageInstancedManager_eventCompressInstanceData_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventCompressInstanceData_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "CompressInstanceData", Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::AuracronFoliageInstancedManager_eventCompressInstanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::AuracronFoliageInstancedManager_eventCompressInstanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execCompressInstanceData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompressInstanceData(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function CompressInstanceData *************

// ********** Begin Class UAuracronFoliageInstancedManager Function CreateInstanceBatch ************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics
{
	struct AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms
	{
		UStaticMesh* StaticMesh;
		UMaterialInterface* Material;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Batch management\n" },
#endif
		{ "CPP_Default_Material", "None" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Material;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "CreateInstanceBatch", Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::AuracronFoliageInstancedManager_eventCreateInstanceBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execCreateInstanceBatch)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_StaticMesh);
	P_GET_OBJECT(UMaterialInterface,Z_Param_Material);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateInstanceBatch(Z_Param_StaticMesh,Z_Param_Material);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function CreateInstanceBatch **************

// ********** Begin Class UAuracronFoliageInstancedManager Function DestroyInstanceBatch ***********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics
{
	struct AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms
	{
		FString BatchId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "DestroyInstanceBatch", Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::AuracronFoliageInstancedManager_eventDestroyInstanceBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execDestroyInstanceBatch)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroyInstanceBatch(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function DestroyInstanceBatch *************

// ********** Begin Class UAuracronFoliageInstancedManager Function DrawDebugVisualization *********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics
{
	struct AuracronFoliageInstancedManager_eventDrawDebugVisualization_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventDrawDebugVisualization_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "DrawDebugVisualization", Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::AuracronFoliageInstancedManager_eventDrawDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::AuracronFoliageInstancedManager_eventDrawDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execDrawDebugVisualization)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugVisualization(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function DrawDebugVisualization ***********

// ********** Begin Class UAuracronFoliageInstancedManager Function EnableDebugVisualization *******
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageInstancedManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::AuracronFoliageInstancedManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::AuracronFoliageInstancedManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function EnableDebugVisualization *********

// ********** Begin Class UAuracronFoliageInstancedManager Function EnableGPUCulling ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics
{
	struct AuracronFoliageInstancedManager_eventEnableGPUCulling_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU operations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU operations" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventEnableGPUCulling_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventEnableGPUCulling_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "EnableGPUCulling", Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::AuracronFoliageInstancedManager_eventEnableGPUCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::AuracronFoliageInstancedManager_eventEnableGPUCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execEnableGPUCulling)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableGPUCulling(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function EnableGPUCulling *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function FlushGPUCommands ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "FlushGPUCommands", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execFlushGPUCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FlushGPUCommands();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function FlushGPUCommands *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetAllInstanceBatches **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics
{
	struct AuracronFoliageInstancedManager_eventGetAllInstanceBatches_Parms
	{
		TArray<FAuracronInstanceBatchData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronInstanceBatchData, METADATA_PARAMS(0, nullptr) }; // 2758718836
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetAllInstanceBatches_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2758718836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetAllInstanceBatches", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::AuracronFoliageInstancedManager_eventGetAllInstanceBatches_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::AuracronFoliageInstancedManager_eventGetAllInstanceBatches_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetAllInstanceBatches)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronInstanceBatchData>*)Z_Param__Result=P_THIS->GetAllInstanceBatches();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetAllInstanceBatches ************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetBatchCount ******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics
{
	struct AuracronFoliageInstancedManager_eventGetBatchCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetBatchCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetBatchCount", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::AuracronFoliageInstancedManager_eventGetBatchCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::AuracronFoliageInstancedManager_eventGetBatchCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetBatchCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetBatchCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetBatchCount ********************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetBatchMemoryUsageMB **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics
{
	struct AuracronFoliageInstancedManager_eventGetBatchMemoryUsageMB_Parms
	{
		FString BatchId;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetBatchMemoryUsageMB_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetBatchMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetBatchMemoryUsageMB", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::AuracronFoliageInstancedManager_eventGetBatchMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::AuracronFoliageInstancedManager_eventGetBatchMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetBatchMemoryUsageMB)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBatchMemoryUsageMB(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetBatchMemoryUsageMB ************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetClusters ********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics
{
	struct AuracronFoliageInstancedManager_eventGetClusters_Parms
	{
		FString BatchId;
		TArray<FAuracronInstanceClusterData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetClusters_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronInstanceClusterData, METADATA_PARAMS(0, nullptr) }; // 178761944
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetClusters_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 178761944
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetClusters", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::AuracronFoliageInstancedManager_eventGetClusters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::AuracronFoliageInstancedManager_eventGetClusters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetClusters)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronInstanceClusterData>*)Z_Param__Result=P_THIS->GetClusters(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetClusters **********************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics
{
	struct AuracronFoliageInstancedManager_eventGetConfiguration_Parms
	{
		FAuracronInstancedMeshConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration, METADATA_PARAMS(0, nullptr) }; // 2665853798
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::AuracronFoliageInstancedManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::AuracronFoliageInstancedManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronInstancedMeshConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetConfiguration *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetHierarchicalComponent *******
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics
{
	struct AuracronFoliageInstancedManager_eventGetHierarchicalComponent_Parms
	{
		FString BatchId;
		UHierarchicalInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetHierarchicalComponent_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetHierarchicalComponent_Parms, ReturnValue), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetHierarchicalComponent", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::AuracronFoliageInstancedManager_eventGetHierarchicalComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::AuracronFoliageInstancedManager_eventGetHierarchicalComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetHierarchicalComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UHierarchicalInstancedStaticMeshComponent**)Z_Param__Result=P_THIS->GetHierarchicalComponent(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetHierarchicalComponent *********

// ********** Begin Class UAuracronFoliageInstancedManager Function GetInstance ********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics
{
	struct AuracronFoliageInstancedManager_eventGetInstance_Parms
	{
		UAuracronFoliageInstancedManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::AuracronFoliageInstancedManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::AuracronFoliageInstancedManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageInstancedManager**)Z_Param__Result=UAuracronFoliageInstancedManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetInstance **********************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetInstanceBatch ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics
{
	struct AuracronFoliageInstancedManager_eventGetInstanceBatch_Parms
	{
		FString BatchId;
		FAuracronInstanceBatchData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceBatch_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceBatch_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronInstanceBatchData, METADATA_PARAMS(0, nullptr) }; // 2758718836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetInstanceBatch", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::AuracronFoliageInstancedManager_eventGetInstanceBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::AuracronFoliageInstancedManager_eventGetInstanceBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetInstanceBatch)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronInstanceBatchData*)Z_Param__Result=P_THIS->GetInstanceBatch(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetInstanceBatch *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetInstanceCount ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics
{
	struct AuracronFoliageInstancedManager_eventGetInstanceCount_Parms
	{
		FString BatchId;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceCount_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetInstanceCount", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetInstanceCount)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetInstanceCount(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetInstanceCount *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetInstancedComponent **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics
{
	struct AuracronFoliageInstancedManager_eventGetInstancedComponent_Parms
	{
		FString BatchId;
		UInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Component access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstancedComponent_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstancedComponent_Parms, ReturnValue), Z_Construct_UClass_UInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetInstancedComponent", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::AuracronFoliageInstancedManager_eventGetInstancedComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::AuracronFoliageInstancedManager_eventGetInstancedComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetInstancedComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UInstancedStaticMeshComponent**)Z_Param__Result=P_THIS->GetInstancedComponent(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetInstancedComponent ************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetInstanceTransforms **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics
{
	struct AuracronFoliageInstancedManager_eventGetInstanceTransforms_Parms
	{
		FString BatchId;
		TArray<FTransform> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceTransforms_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetInstanceTransforms_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetInstanceTransforms", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::AuracronFoliageInstancedManager_eventGetInstanceTransforms_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::AuracronFoliageInstancedManager_eventGetInstanceTransforms_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetInstanceTransforms)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTransform>*)Z_Param__Result=P_THIS->GetInstanceTransforms(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetInstanceTransforms ************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetMemoryUsageMB ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics
{
	struct AuracronFoliageInstancedManager_eventGetMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetMemoryUsageMB", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::AuracronFoliageInstancedManager_eventGetMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::AuracronFoliageInstancedManager_eventGetMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetMemoryUsageMB *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetRenderingStatistics *********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics
{
	struct AuracronFoliageInstancedManager_eventGetRenderingStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetRenderingStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetRenderingStatistics", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::AuracronFoliageInstancedManager_eventGetRenderingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::AuracronFoliageInstancedManager_eventGetRenderingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetRenderingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetRenderingStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetRenderingStatistics ***********

// ********** Begin Class UAuracronFoliageInstancedManager Function GetTotalInstanceCount **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics
{
	struct AuracronFoliageInstancedManager_eventGetTotalInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetTotalInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetTotalInstanceCount", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetTotalInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetTotalInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetTotalInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetTotalInstanceCount ************

// ********** Begin Class UAuracronFoliageInstancedManager Function GetVisibleInstanceCount ********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics
{
	struct AuracronFoliageInstancedManager_eventGetVisibleInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventGetVisibleInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "GetVisibleInstanceCount", Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetVisibleInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::AuracronFoliageInstancedManager_eventGetVisibleInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execGetVisibleInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetVisibleInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function GetVisibleInstanceCount **********

// ********** Begin Class UAuracronFoliageInstancedManager Function Initialize *********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics
{
	struct AuracronFoliageInstancedManager_eventInitialize_Parms
	{
		FAuracronInstancedMeshConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665853798
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::AuracronFoliageInstancedManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::AuracronFoliageInstancedManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronInstancedMeshConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function Initialize ***********************

// ********** Begin Class UAuracronFoliageInstancedManager Function IsDebugVisualizationEnabled ****
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageInstancedManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageInstancedManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageInstancedManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function IsDebugVisualizationEnabled ******

// ********** Begin Class UAuracronFoliageInstancedManager Function IsGPUCullingEnabled ************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics
{
	struct AuracronFoliageInstancedManager_eventIsGPUCullingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventIsGPUCullingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventIsGPUCullingEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "IsGPUCullingEnabled", Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::AuracronFoliageInstancedManager_eventIsGPUCullingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::AuracronFoliageInstancedManager_eventIsGPUCullingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execIsGPUCullingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGPUCullingEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function IsGPUCullingEnabled **************

// ********** Begin Class UAuracronFoliageInstancedManager Function IsInitialized ******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics
{
	struct AuracronFoliageInstancedManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::AuracronFoliageInstancedManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::AuracronFoliageInstancedManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function IsInitialized ********************

// ********** Begin Class UAuracronFoliageInstancedManager Function MergeBatches *******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics
{
	struct AuracronFoliageInstancedManager_eventMergeBatches_Parms
	{
		TArray<FString> BatchIds;
		FString NewBatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchIds_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewBatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchIds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BatchIds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewBatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_BatchIds_Inner = { "BatchIds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_BatchIds = { "BatchIds", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventMergeBatches_Parms, BatchIds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchIds_MetaData), NewProp_BatchIds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_NewBatchId = { "NewBatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventMergeBatches_Parms, NewBatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewBatchId_MetaData), NewProp_NewBatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_BatchIds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_BatchIds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::NewProp_NewBatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "MergeBatches", Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::AuracronFoliageInstancedManager_eventMergeBatches_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::AuracronFoliageInstancedManager_eventMergeBatches_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execMergeBatches)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_BatchIds);
	P_GET_PROPERTY(FStrProperty,Z_Param_NewBatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MergeBatches(Z_Param_Out_BatchIds,Z_Param_NewBatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function MergeBatches *********************

// ********** Begin Class UAuracronFoliageInstancedManager Function OptimizeBatches ****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rendering optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OptimizeBatches", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execOptimizeBatches)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeBatches();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function OptimizeBatches ******************

// ********** Begin Class UAuracronFoliageInstancedManager Function OptimizeMemoryUsage ************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "OptimizeMemoryUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function OptimizeMemoryUsage **************

// ********** Begin Class UAuracronFoliageInstancedManager Function RebuildClusters ****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics
{
	struct AuracronFoliageInstancedManager_eventRebuildClusters_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventRebuildClusters_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "RebuildClusters", Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::AuracronFoliageInstancedManager_eventRebuildClusters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::AuracronFoliageInstancedManager_eventRebuildClusters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execRebuildClusters)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RebuildClusters(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function RebuildClusters ******************

// ********** Begin Class UAuracronFoliageInstancedManager Function RemoveInstance *****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics
{
	struct AuracronFoliageInstancedManager_eventRemoveInstance_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventRemoveInstance_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventRemoveInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventRemoveInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventRemoveInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "RemoveInstance", Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::AuracronFoliageInstancedManager_eventRemoveInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::AuracronFoliageInstancedManager_eventRemoveInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execRemoveInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveInstance(Z_Param_BatchId,Z_Param_InstanceIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function RemoveInstance *******************

// ********** Begin Class UAuracronFoliageInstancedManager Function RemoveInstances ****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics
{
	struct AuracronFoliageInstancedManager_eventRemoveInstances_Parms
	{
		FString BatchId;
		TArray<int32> InstanceIndices;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceIndices_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceIndices;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventRemoveInstances_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_InstanceIndices_Inner = { "InstanceIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_InstanceIndices = { "InstanceIndices", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventRemoveInstances_Parms, InstanceIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceIndices_MetaData), NewProp_InstanceIndices_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventRemoveInstances_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventRemoveInstances_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_InstanceIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_InstanceIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "RemoveInstances", Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::AuracronFoliageInstancedManager_eventRemoveInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::AuracronFoliageInstancedManager_eventRemoveInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execRemoveInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_TARRAY_REF(int32,Z_Param_Out_InstanceIndices);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveInstances(Z_Param_BatchId,Z_Param_Out_InstanceIndices);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function RemoveInstances ******************

// ********** Begin Class UAuracronFoliageInstancedManager Function SetBatchLOD ********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics
{
	struct AuracronFoliageInstancedManager_eventSetBatchLOD_Parms
	{
		FString BatchId;
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetBatchLOD_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetBatchLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "SetBatchLOD", Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::AuracronFoliageInstancedManager_eventSetBatchLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::AuracronFoliageInstancedManager_eventSetBatchLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execSetBatchLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBatchLOD(Z_Param_BatchId,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function SetBatchLOD **********************

// ********** Begin Class UAuracronFoliageInstancedManager Function SetBatchVisibility *************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics
{
	struct AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms
	{
		FString BatchId;
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "SetBatchVisibility", Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::AuracronFoliageInstancedManager_eventSetBatchVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execSetBatchVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBatchVisibility(Z_Param_BatchId,Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function SetBatchVisibility ***************

// ********** Begin Class UAuracronFoliageInstancedManager Function SetClusterLOD ******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics
{
	struct AuracronFoliageInstancedManager_eventSetClusterLOD_Parms
	{
		FString BatchId;
		FString ClusterId;
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClusterId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetClusterLOD_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_ClusterId = { "ClusterId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetClusterLOD_Parms, ClusterId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterId_MetaData), NewProp_ClusterId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetClusterLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_ClusterId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "SetClusterLOD", Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::AuracronFoliageInstancedManager_eventSetClusterLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::AuracronFoliageInstancedManager_eventSetClusterLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execSetClusterLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClusterId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetClusterLOD(Z_Param_BatchId,Z_Param_ClusterId,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function SetClusterLOD ********************

// ********** Begin Class UAuracronFoliageInstancedManager Function SetConfiguration ***************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics
{
	struct AuracronFoliageInstancedManager_eventSetConfiguration_Parms
	{
		FAuracronInstancedMeshConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665853798
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::AuracronFoliageInstancedManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::AuracronFoliageInstancedManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronInstancedMeshConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function SetConfiguration *****************

// ********** Begin Class UAuracronFoliageInstancedManager Function Shutdown ***********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function Shutdown *************************

// ********** Begin Class UAuracronFoliageInstancedManager Function SplitBatch *********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics
{
	struct AuracronFoliageInstancedManager_eventSplitBatch_Parms
	{
		FString BatchId;
		int32 MaxInstancesPerBatch;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerBatch;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSplitBatch_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::NewProp_MaxInstancesPerBatch = { "MaxInstancesPerBatch", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventSplitBatch_Parms, MaxInstancesPerBatch), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::NewProp_MaxInstancesPerBatch,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "SplitBatch", Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::AuracronFoliageInstancedManager_eventSplitBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::AuracronFoliageInstancedManager_eventSplitBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execSplitBatch)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxInstancesPerBatch);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SplitBatch(Z_Param_BatchId,Z_Param_MaxInstancesPerBatch);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function SplitBatch ***********************

// ********** Begin Class UAuracronFoliageInstancedManager Function Tick ***************************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics
{
	struct AuracronFoliageInstancedManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::AuracronFoliageInstancedManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::AuracronFoliageInstancedManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function Tick *****************************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateCulling ******************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateCulling_Parms
	{
		FVector ViewerLocation;
		FVector ViewerDirection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling and LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling and LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerDirection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateCulling_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::NewProp_ViewerDirection = { "ViewerDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateCulling_Parms, ViewerDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerDirection_MetaData), NewProp_ViewerDirection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::NewProp_ViewerDirection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateCulling", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::AuracronFoliageInstancedManager_eventUpdateCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::AuracronFoliageInstancedManager_eventUpdateCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateCulling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCulling(Z_Param_Out_ViewerLocation,Z_Param_Out_ViewerDirection);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateCulling ********************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateGPUInstances *************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateGPUInstances_Parms
	{
		FString BatchId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateGPUInstances_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::NewProp_BatchId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateGPUInstances", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::AuracronFoliageInstancedManager_eventUpdateGPUInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::AuracronFoliageInstancedManager_eventUpdateGPUInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateGPUInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateGPUInstances(Z_Param_BatchId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateGPUInstances ***************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateInstance *****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateInstance_Parms
	{
		FString BatchId;
		int32 InstanceIndex;
		FTransform Transform;
		FLinearColor Color;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "CPP_Default_Color", "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstance_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_InstanceIndex = { "InstanceIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstance_Parms, InstanceIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstance_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstance_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventUpdateInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventUpdateInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_InstanceIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateInstance", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::AuracronFoliageInstancedManager_eventUpdateInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::AuracronFoliageInstancedManager_eventUpdateInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_PROPERTY(FIntProperty,Z_Param_InstanceIndex);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateInstance(Z_Param_BatchId,Z_Param_InstanceIndex,Z_Param_Out_Transform,Z_Param_Out_Color);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateInstance *******************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateInstanceBatch ************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms
	{
		FAuracronInstanceBatchData BatchData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BatchData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_BatchData = { "BatchData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms, BatchData), Z_Construct_UScriptStruct_FAuracronInstanceBatchData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchData_MetaData), NewProp_BatchData_MetaData) }; // 2758718836
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_BatchData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateInstanceBatch", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::AuracronFoliageInstancedManager_eventUpdateInstanceBatch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateInstanceBatch)
{
	P_GET_STRUCT_REF(FAuracronInstanceBatchData,Z_Param_Out_BatchData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateInstanceBatch(Z_Param_Out_BatchData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateInstanceBatch **************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateInstances ****************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateInstances_Parms
	{
		FString BatchId;
		TArray<int32> InstanceIndices;
		TArray<FTransform> Transforms;
		TArray<FLinearColor> Colors;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceIndices_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Colors_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceIndices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Colors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Colors;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstances_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_InstanceIndices_Inner = { "InstanceIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_InstanceIndices = { "InstanceIndices", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstances_Parms, InstanceIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceIndices_MetaData), NewProp_InstanceIndices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstances_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Colors_Inner = { "Colors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Colors = { "Colors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstances_Parms, Colors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Colors_MetaData), NewProp_Colors_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventUpdateInstances_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventUpdateInstances_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_InstanceIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_InstanceIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Colors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_Colors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateInstances", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::AuracronFoliageInstancedManager_eventUpdateInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::AuracronFoliageInstancedManager_eventUpdateInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_TARRAY_REF(int32,Z_Param_Out_InstanceIndices);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_GET_TARRAY_REF(FLinearColor,Z_Param_Out_Colors);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateInstances(Z_Param_BatchId,Z_Param_Out_InstanceIndices,Z_Param_Out_Transforms,Z_Param_Out_Colors);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateInstances ******************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateInstancesSimple **********
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms
	{
		FString BatchId;
		TArray<int32> InstanceIndices;
		TArray<FTransform> Transforms;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceIndices_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BatchId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InstanceIndices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_BatchId = { "BatchId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms, BatchId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchId_MetaData), NewProp_BatchId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_InstanceIndices_Inner = { "InstanceIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_InstanceIndices = { "InstanceIndices", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms, InstanceIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceIndices_MetaData), NewProp_InstanceIndices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms), &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_BatchId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_InstanceIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_InstanceIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateInstancesSimple", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::AuracronFoliageInstancedManager_eventUpdateInstancesSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateInstancesSimple)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BatchId);
	P_GET_TARRAY_REF(int32,Z_Param_Out_InstanceIndices);
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateInstancesSimple(Z_Param_BatchId,Z_Param_Out_InstanceIndices,Z_Param_Out_Transforms);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateInstancesSimple ************

// ********** Begin Class UAuracronFoliageInstancedManager Function UpdateLOD **********************
struct Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics
{
	struct AuracronFoliageInstancedManager_eventUpdateLOD_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instanced Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageInstancedManager_eventUpdateLOD_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageInstancedManager, nullptr, "UpdateLOD", Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::AuracronFoliageInstancedManager_eventUpdateLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::AuracronFoliageInstancedManager_eventUpdateLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageInstancedManager::execUpdateLOD)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLOD(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageInstancedManager Function UpdateLOD ************************

// ********** Begin Class UAuracronFoliageInstancedManager *****************************************
void UAuracronFoliageInstancedManager::StaticRegisterNativesUAuracronFoliageInstancedManager()
{
	UClass* Class = UAuracronFoliageInstancedManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddInstance", &UAuracronFoliageInstancedManager::execAddInstance },
		{ "AddInstances", &UAuracronFoliageInstancedManager::execAddInstances },
		{ "AddInstancesSimple", &UAuracronFoliageInstancedManager::execAddInstancesSimple },
		{ "BuildClusters", &UAuracronFoliageInstancedManager::execBuildClusters },
		{ "ClearInstances", &UAuracronFoliageInstancedManager::execClearInstances },
		{ "CompactBatches", &UAuracronFoliageInstancedManager::execCompactBatches },
		{ "CompressInstanceData", &UAuracronFoliageInstancedManager::execCompressInstanceData },
		{ "CreateInstanceBatch", &UAuracronFoliageInstancedManager::execCreateInstanceBatch },
		{ "DestroyInstanceBatch", &UAuracronFoliageInstancedManager::execDestroyInstanceBatch },
		{ "DrawDebugVisualization", &UAuracronFoliageInstancedManager::execDrawDebugVisualization },
		{ "EnableDebugVisualization", &UAuracronFoliageInstancedManager::execEnableDebugVisualization },
		{ "EnableGPUCulling", &UAuracronFoliageInstancedManager::execEnableGPUCulling },
		{ "FlushGPUCommands", &UAuracronFoliageInstancedManager::execFlushGPUCommands },
		{ "GetAllInstanceBatches", &UAuracronFoliageInstancedManager::execGetAllInstanceBatches },
		{ "GetBatchCount", &UAuracronFoliageInstancedManager::execGetBatchCount },
		{ "GetBatchMemoryUsageMB", &UAuracronFoliageInstancedManager::execGetBatchMemoryUsageMB },
		{ "GetClusters", &UAuracronFoliageInstancedManager::execGetClusters },
		{ "GetConfiguration", &UAuracronFoliageInstancedManager::execGetConfiguration },
		{ "GetHierarchicalComponent", &UAuracronFoliageInstancedManager::execGetHierarchicalComponent },
		{ "GetInstance", &UAuracronFoliageInstancedManager::execGetInstance },
		{ "GetInstanceBatch", &UAuracronFoliageInstancedManager::execGetInstanceBatch },
		{ "GetInstanceCount", &UAuracronFoliageInstancedManager::execGetInstanceCount },
		{ "GetInstancedComponent", &UAuracronFoliageInstancedManager::execGetInstancedComponent },
		{ "GetInstanceTransforms", &UAuracronFoliageInstancedManager::execGetInstanceTransforms },
		{ "GetMemoryUsageMB", &UAuracronFoliageInstancedManager::execGetMemoryUsageMB },
		{ "GetRenderingStatistics", &UAuracronFoliageInstancedManager::execGetRenderingStatistics },
		{ "GetTotalInstanceCount", &UAuracronFoliageInstancedManager::execGetTotalInstanceCount },
		{ "GetVisibleInstanceCount", &UAuracronFoliageInstancedManager::execGetVisibleInstanceCount },
		{ "Initialize", &UAuracronFoliageInstancedManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageInstancedManager::execIsDebugVisualizationEnabled },
		{ "IsGPUCullingEnabled", &UAuracronFoliageInstancedManager::execIsGPUCullingEnabled },
		{ "IsInitialized", &UAuracronFoliageInstancedManager::execIsInitialized },
		{ "MergeBatches", &UAuracronFoliageInstancedManager::execMergeBatches },
		{ "OptimizeBatches", &UAuracronFoliageInstancedManager::execOptimizeBatches },
		{ "OptimizeMemoryUsage", &UAuracronFoliageInstancedManager::execOptimizeMemoryUsage },
		{ "RebuildClusters", &UAuracronFoliageInstancedManager::execRebuildClusters },
		{ "RemoveInstance", &UAuracronFoliageInstancedManager::execRemoveInstance },
		{ "RemoveInstances", &UAuracronFoliageInstancedManager::execRemoveInstances },
		{ "SetBatchLOD", &UAuracronFoliageInstancedManager::execSetBatchLOD },
		{ "SetBatchVisibility", &UAuracronFoliageInstancedManager::execSetBatchVisibility },
		{ "SetClusterLOD", &UAuracronFoliageInstancedManager::execSetClusterLOD },
		{ "SetConfiguration", &UAuracronFoliageInstancedManager::execSetConfiguration },
		{ "Shutdown", &UAuracronFoliageInstancedManager::execShutdown },
		{ "SplitBatch", &UAuracronFoliageInstancedManager::execSplitBatch },
		{ "Tick", &UAuracronFoliageInstancedManager::execTick },
		{ "UpdateCulling", &UAuracronFoliageInstancedManager::execUpdateCulling },
		{ "UpdateGPUInstances", &UAuracronFoliageInstancedManager::execUpdateGPUInstances },
		{ "UpdateInstance", &UAuracronFoliageInstancedManager::execUpdateInstance },
		{ "UpdateInstanceBatch", &UAuracronFoliageInstancedManager::execUpdateInstanceBatch },
		{ "UpdateInstances", &UAuracronFoliageInstancedManager::execUpdateInstances },
		{ "UpdateInstancesSimple", &UAuracronFoliageInstancedManager::execUpdateInstancesSimple },
		{ "UpdateLOD", &UAuracronFoliageInstancedManager::execUpdateLOD },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageInstancedManager;
UClass* UAuracronFoliageInstancedManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageInstancedManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageInstancedManager"),
			Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageInstancedManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager_NoRegister()
{
	return UAuracronFoliageInstancedManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Instanced Manager\n * Manager for instanced static mesh rendering system\n */" },
#endif
		{ "IncludePath", "AuracronFoliageInstanced.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Instanced Manager\nManager for instanced static mesh rendering system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBatchCreated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBatchDestroyed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInstanceAdded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInstanceRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBatchOptimized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageInstanced.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBatchCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBatchDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInstanceAdded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInstanceRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBatchOptimized;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstance, "AddInstance" }, // 183365101
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstances, "AddInstances" }, // 1661339828
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_AddInstancesSimple, "AddInstancesSimple" }, // 2288627661
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_BuildClusters, "BuildClusters" }, // 3910423310
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_ClearInstances, "ClearInstances" }, // 1958040688
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompactBatches, "CompactBatches" }, // 1351414444
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_CompressInstanceData, "CompressInstanceData" }, // 1593674456
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_CreateInstanceBatch, "CreateInstanceBatch" }, // 3290808655
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_DestroyInstanceBatch, "DestroyInstanceBatch" }, // 1378678904
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_DrawDebugVisualization, "DrawDebugVisualization" }, // 536006755
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 226384482
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_EnableGPUCulling, "EnableGPUCulling" }, // 3157323357
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_FlushGPUCommands, "FlushGPUCommands" }, // 3479824066
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetAllInstanceBatches, "GetAllInstanceBatches" }, // 3007383465
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchCount, "GetBatchCount" }, // 2693138363
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetBatchMemoryUsageMB, "GetBatchMemoryUsageMB" }, // 2244112252
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetClusters, "GetClusters" }, // 2295667357
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetConfiguration, "GetConfiguration" }, // 1145138825
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetHierarchicalComponent, "GetHierarchicalComponent" }, // 580903087
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstance, "GetInstance" }, // 558143997
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceBatch, "GetInstanceBatch" }, // 274318920
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceCount, "GetInstanceCount" }, // 2537372939
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstancedComponent, "GetInstancedComponent" }, // 3604912370
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetInstanceTransforms, "GetInstanceTransforms" }, // 1557976811
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetMemoryUsageMB, "GetMemoryUsageMB" }, // 343412138
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetRenderingStatistics, "GetRenderingStatistics" }, // 1774993700
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetTotalInstanceCount, "GetTotalInstanceCount" }, // 3803879311
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_GetVisibleInstanceCount, "GetVisibleInstanceCount" }, // 2814768766
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_Initialize, "Initialize" }, // 1481745461
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 1475467548
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsGPUCullingEnabled, "IsGPUCullingEnabled" }, // 3011850184
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_IsInitialized, "IsInitialized" }, // 3306544777
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_MergeBatches, "MergeBatches" }, // 378012161
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature, "OnBatchCreated__DelegateSignature" }, // 5201938
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature, "OnBatchDestroyed__DelegateSignature" }, // 3098565464
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature, "OnBatchOptimized__DelegateSignature" }, // 814676263
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature, "OnInstanceAdded__DelegateSignature" }, // 4003973241
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature, "OnInstanceRemoved__DelegateSignature" }, // 3400062835
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeBatches, "OptimizeBatches" }, // 3035624552
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 678287699
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_RebuildClusters, "RebuildClusters" }, // 3126671682
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstance, "RemoveInstance" }, // 397677758
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_RemoveInstances, "RemoveInstances" }, // 1746327496
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchLOD, "SetBatchLOD" }, // 3067816173
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetBatchVisibility, "SetBatchVisibility" }, // 517955668
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetClusterLOD, "SetClusterLOD" }, // 2191621219
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SetConfiguration, "SetConfiguration" }, // 2897498933
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_Shutdown, "Shutdown" }, // 2539934684
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_SplitBatch, "SplitBatch" }, // 3372555664
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_Tick, "Tick" }, // 1635033675
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateCulling, "UpdateCulling" }, // 2694140830
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateGPUInstances, "UpdateGPUInstances" }, // 181591135
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstance, "UpdateInstance" }, // 3761782415
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstanceBatch, "UpdateInstanceBatch" }, // 1922441667
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstances, "UpdateInstances" }, // 546123143
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateInstancesSimple, "UpdateInstancesSimple" }, // 1096601917
		{ &Z_Construct_UFunction_UAuracronFoliageInstancedManager_UpdateLOD, "UpdateLOD" }, // 3421649857
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageInstancedManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchCreated = { "OnBatchCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, OnBatchCreated), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBatchCreated_MetaData), NewProp_OnBatchCreated_MetaData) }; // 5201938
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchDestroyed = { "OnBatchDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, OnBatchDestroyed), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBatchDestroyed_MetaData), NewProp_OnBatchDestroyed_MetaData) }; // 3098565464
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnInstanceAdded = { "OnInstanceAdded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, OnInstanceAdded), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceAdded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInstanceAdded_MetaData), NewProp_OnInstanceAdded_MetaData) }; // 4003973241
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnInstanceRemoved = { "OnInstanceRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, OnInstanceRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnInstanceRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInstanceRemoved_MetaData), NewProp_OnInstanceRemoved_MetaData) }; // 3400062835
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchOptimized = { "OnBatchOptimized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, OnBatchOptimized), Z_Construct_UDelegateFunction_UAuracronFoliageInstancedManager_OnBatchOptimized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBatchOptimized_MetaData), NewProp_OnBatchOptimized_MetaData) }; // 814676263
void Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageInstancedManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageInstancedManager), &Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, Configuration), Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2665853798
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageInstancedManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnInstanceAdded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnInstanceRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_OnBatchOptimized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::ClassParams = {
	&UAuracronFoliageInstancedManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageInstancedManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageInstancedManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageInstancedManager.OuterSingleton;
}
UAuracronFoliageInstancedManager::UAuracronFoliageInstancedManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageInstancedManager);
UAuracronFoliageInstancedManager::~UAuracronFoliageInstancedManager() {}
// ********** End Class UAuracronFoliageInstancedManager *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronInstanceRenderingMode_StaticEnum, TEXT("EAuracronInstanceRenderingMode"), &Z_Registration_Info_UEnum_EAuracronInstanceRenderingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2353152906U) },
		{ EAuracronInstanceCullingMode_StaticEnum, TEXT("EAuracronInstanceCullingMode"), &Z_Registration_Info_UEnum_EAuracronInstanceCullingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3579063172U) },
		{ EAuracronInstanceLODMode_StaticEnum, TEXT("EAuracronInstanceLODMode"), &Z_Registration_Info_UEnum_EAuracronInstanceLODMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1350265438U) },
		{ EAuracronInstanceUpdateMode_StaticEnum, TEXT("EAuracronInstanceUpdateMode"), &Z_Registration_Info_UEnum_EAuracronInstanceUpdateMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 941898752U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronInstancedMeshConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronInstancedMeshConfiguration_Statics::NewStructOps, TEXT("AuracronInstancedMeshConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronInstancedMeshConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronInstancedMeshConfiguration), 2665853798U) },
		{ FAuracronInstanceBatchData::StaticStruct, Z_Construct_UScriptStruct_FAuracronInstanceBatchData_Statics::NewStructOps, TEXT("AuracronInstanceBatchData"), &Z_Registration_Info_UScriptStruct_FAuracronInstanceBatchData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronInstanceBatchData), 2758718836U) },
		{ FAuracronInstanceClusterData::StaticStruct, Z_Construct_UScriptStruct_FAuracronInstanceClusterData_Statics::NewStructOps, TEXT("AuracronInstanceClusterData"), &Z_Registration_Info_UScriptStruct_FAuracronInstanceClusterData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronInstanceClusterData), 178761944U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageInstancedManager, UAuracronFoliageInstancedManager::StaticClass, TEXT("UAuracronFoliageInstancedManager"), &Z_Registration_Info_UClass_UAuracronFoliageInstancedManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageInstancedManager), 1913104778U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_588401398(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageInstanced_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
