// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAnalyticsBridge.h"

#ifdef AURACRONANALYTICSBRIDGE_AuracronAnalyticsBridge_generated_h
#error "AuracronAnalyticsBridge.generated.h already included, missing '#pragma once' in AuracronAnalyticsBridge.h"
#endif
#define AURACRONANALYTICSBRIDGE_AuracronAnalyticsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
struct FAuracronABTestConfiguration;
struct FAuracronAnalyticsEvent;
struct FAuracronBalanceMetrics;

// ********** Begin ScriptStruct FAuracronAnalyticsEvent *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_73_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAnalyticsEvent;
// ********** End ScriptStruct FAuracronAnalyticsEvent *********************************************

// ********** Begin ScriptStruct FAuracronBalanceMetrics *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBalanceMetrics;
// ********** End ScriptStruct FAuracronBalanceMetrics *********************************************

// ********** Begin ScriptStruct FAuracronABTestConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_199_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronABTestConfiguration;
// ********** End ScriptStruct FAuracronABTestConfiguration ****************************************

// ********** Begin Delegate FOnAnalyticsEventRecorded *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_493_DELEGATE \
static void FOnAnalyticsEventRecorded_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsEventRecorded, FAuracronAnalyticsEvent Event);


// ********** End Delegate FOnAnalyticsEventRecorded ***********************************************

// ********** Begin Delegate FOnAnalyticsDataSynced ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_498_DELEGATE \
static void FOnAnalyticsDataSynced_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsDataSynced, bool bSuccess);


// ********** End Delegate FOnAnalyticsDataSynced **************************************************

// ********** Begin Class UAuracronAnalyticsBridge *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSendDataToAnalyticsServer); \
	DECLARE_FUNCTION(execSyncWithFirebaseAnalytics); \
	DECLARE_FUNCTION(execExportAnalyticsData); \
	DECLARE_FUNCTION(execMonitorLoadingTimes); \
	DECLARE_FUNCTION(execMonitorNetworkLatency); \
	DECLARE_FUNCTION(execMonitorMemoryUsage); \
	DECLARE_FUNCTION(execMonitorFrameRate); \
	DECLARE_FUNCTION(execTrackEngagement); \
	DECLARE_FUNCTION(execRecordGameSession); \
	DECLARE_FUNCTION(execTrackPlayerBehavior); \
	DECLARE_FUNCTION(execCollectRealmMetrics); \
	DECLARE_FUNCTION(execCollectSigiloMetrics); \
	DECLARE_FUNCTION(execCollectAbilityMetrics); \
	DECLARE_FUNCTION(execCollectChampionMetrics); \
	DECLARE_FUNCTION(execRecordABTestResult); \
	DECLARE_FUNCTION(execGetPlayerVariant); \
	DECLARE_FUNCTION(execStopABTest); \
	DECLARE_FUNCTION(execStartABTest); \
	DECLARE_FUNCTION(execRecordBalanceData); \
	DECLARE_FUNCTION(execRecordPerformanceMetric); \
	DECLARE_FUNCTION(execRecordGameplayEvent); \
	DECLARE_FUNCTION(execRecordAnalyticsEvent);


AURACRONANALYTICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronAnalyticsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAnalyticsBridge(); \
	friend struct Z_Construct_UClass_UAuracronAnalyticsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONANALYTICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronAnalyticsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAnalyticsBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronAnalyticsBridge"), Z_Construct_UClass_UAuracronAnalyticsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAnalyticsBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		BalanceMetrics=NETFIELD_REP_START, \
		ActiveABTests, \
		NETFIELD_REP_END=ActiveABTests	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAnalyticsBridge(UAuracronAnalyticsBridge&&) = delete; \
	UAuracronAnalyticsBridge(const UAuracronAnalyticsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAnalyticsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAnalyticsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAnalyticsBridge) \
	NO_API virtual ~UAuracronAnalyticsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_258_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h_261_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAnalyticsBridge;

// ********** End Class UAuracronAnalyticsBridge ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h

// ********** Begin Enum EAuracronAnalyticsEventType ***********************************************
#define FOREACH_ENUM_EAURACRONANALYTICSEVENTTYPE(op) \
	op(EAuracronAnalyticsEventType::None) \
	op(EAuracronAnalyticsEventType::GameplayEvent) \
	op(EAuracronAnalyticsEventType::PerformanceMetric) \
	op(EAuracronAnalyticsEventType::UserBehavior) \
	op(EAuracronAnalyticsEventType::BalanceData) \
	op(EAuracronAnalyticsEventType::MonetizationEvent) \
	op(EAuracronAnalyticsEventType::TechnicalMetric) \
	op(EAuracronAnalyticsEventType::SecurityEvent) \
	op(EAuracronAnalyticsEventType::ABTestEvent) \
	op(EAuracronAnalyticsEventType::CustomEvent) 

enum class EAuracronAnalyticsEventType : uint8;
template<> struct TIsUEnumClass<EAuracronAnalyticsEventType> { enum { Value = true }; };
template<> AURACRONANALYTICSBRIDGE_API UEnum* StaticEnum<EAuracronAnalyticsEventType>();
// ********** End Enum EAuracronAnalyticsEventType *************************************************

// ********** Begin Enum EAuracronBalanceMetricCategory ********************************************
#define FOREACH_ENUM_EAURACRONBALANCEMETRICCATEGORY(op) \
	op(EAuracronBalanceMetricCategory::ChampionPerformance) \
	op(EAuracronBalanceMetricCategory::AbilityUsage) \
	op(EAuracronBalanceMetricCategory::ItemEffectiveness) \
	op(EAuracronBalanceMetricCategory::SigiloImpact) \
	op(EAuracronBalanceMetricCategory::RealmBalance) \
	op(EAuracronBalanceMetricCategory::MatchDuration) \
	op(EAuracronBalanceMetricCategory::PlayerProgression) \
	op(EAuracronBalanceMetricCategory::EconomyBalance) \
	op(EAuracronBalanceMetricCategory::TeamComposition) \
	op(EAuracronBalanceMetricCategory::ObjectiveControl) 

enum class EAuracronBalanceMetricCategory : uint8;
template<> struct TIsUEnumClass<EAuracronBalanceMetricCategory> { enum { Value = true }; };
template<> AURACRONANALYTICSBRIDGE_API UEnum* StaticEnum<EAuracronBalanceMetricCategory>();
// ********** End Enum EAuracronBalanceMetricCategory **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
