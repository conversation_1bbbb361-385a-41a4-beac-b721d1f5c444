# MetaHuman Bridge API Reference

**Complete API documentation for the AURACRON MetaHuman Bridge**

## Table of Contents

1. [Core Classes](#core-classes)
2. [C++ API](#c-api)
3. [Python API](#python-api)
4. [Blueprint API](#blueprint-api)
5. [Data Structures](#data-structures)
6. [Error Codes](#error-codes)
7. [Events and Delegates](#events-and-delegates)

## Core Classes

### UAuracronMetaHumanBridge

The main bridge class providing all MetaHuman DNA manipulation functionality.

**Inheritance:** `UObject`

**Module:** `AuracronMetaHumanBridge`

**Header:** `AuracronMetaHumanBridge.h`

#### Public Methods

##### DNA File Operations

```cpp
/**
 * Load DNA data from file
 * @param FilePath Path to the DNA file
 * @return True if loaded successfully
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|DNA")
bool LoadDNAFromFile(const FString& FilePath);

/**
 * Save DNA data to file
 * @param FilePath Path where to save the DNA file
 * @return True if saved successfully
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|DNA")
bool SaveDNAToFile(const FString& FilePath);

/**
 * Check if DNA is currently loaded and valid
 * @return True if DNA is valid
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|DNA")
bool IsValidDNA() const;

/**
 * Get the number of meshes in the loaded DNA
 * @return Number of meshes
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|DNA")
int32 GetMeshCount() const;

/**
 * Get the number of joints in the loaded DNA
 * @return Number of joints
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|DNA")
int32 GetJointCount() const;
```

##### Joint Manipulation

```cpp
/**
 * Get the name of a joint by index
 * @param JointIndex Index of the joint
 * @return Name of the joint
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Joints")
FString GetJointName(int32 JointIndex) const;

/**
 * Set joint translation
 * @param JointIndex Index of the joint
 * @param Translation New translation vector
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Joints")
bool SetJointTranslation(int32 JointIndex, const FVector& Translation);

/**
 * Get joint translation
 * @param JointIndex Index of the joint
 * @return Current translation vector
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Joints")
FVector GetJointTranslation(int32 JointIndex) const;

/**
 * Set joint rotation
 * @param JointIndex Index of the joint
 * @param Rotation New rotation
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Joints")
bool SetJointRotation(int32 JointIndex, const FRotator& Rotation);

/**
 * Get joint rotation
 * @param JointIndex Index of the joint
 * @return Current rotation
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Joints")
FRotator GetJointRotation(int32 JointIndex) const;

/**
 * Set joint scale
 * @param JointIndex Index of the joint
 * @param Scale New scale vector
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Joints")
bool SetJointScale(int32 JointIndex, const FVector& Scale);

/**
 * Get joint scale
 * @param JointIndex Index of the joint
 * @return Current scale vector
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Joints")
FVector GetJointScale(int32 JointIndex) const;
```

##### Blend Shape Operations

```cpp
/**
 * Get the number of blend shape targets for a mesh
 * @param MeshIndex Index of the mesh
 * @return Number of blend shape targets
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|BlendShapes")
int32 GetBlendShapeTargetCount(int32 MeshIndex) const;

/**
 * Get blend shape target name
 * @param MeshIndex Index of the mesh
 * @param TargetIndex Index of the blend shape target
 * @return Name of the blend shape target
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|BlendShapes")
FString GetBlendShapeTargetName(int32 MeshIndex, int32 TargetIndex) const;

/**
 * Set blend shape weight
 * @param MeshIndex Index of the mesh
 * @param TargetIndex Index of the blend shape target
 * @param Weight New weight value (0.0 to 1.0)
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|BlendShapes")
bool SetBlendShapeWeight(int32 MeshIndex, int32 TargetIndex, float Weight);

/**
 * Get blend shape weight
 * @param MeshIndex Index of the mesh
 * @param TargetIndex Index of the blend shape target
 * @return Current weight value
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|BlendShapes")
float GetBlendShapeWeight(int32 MeshIndex, int32 TargetIndex) const;
```

##### Mesh Deformation

```cpp
/**
 * Get vertex positions for a mesh
 * @param MeshIndex Index of the mesh
 * @return Array of vertex positions
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Mesh")
TArray<FVector> GetVertexPositions(int32 MeshIndex) const;

/**
 * Set vertex positions for a mesh
 * @param MeshIndex Index of the mesh
 * @param Positions Array of new vertex positions
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Mesh")
bool SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions);
```

##### Performance Optimization

```cpp
/**
 * Initialize performance optimization system
 * @param Config Configuration parameters
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Performance")
bool InitializePerformanceOptimization(const TMap<FString, FString>& Config);

/**
 * Optimize memory usage
 * @param bForceGarbageCollection Force garbage collection
 * @return True if successful
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Performance")
bool OptimizeMemoryUsage(bool bForceGarbageCollection = false);

/**
 * Get current performance metrics
 * @return Map of performance metrics
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Performance")
TMap<FString, FString> GetCurrentPerformanceMetrics() const;
```

##### Error Handling

```cpp
/**
 * Set error handling mode
 * @param bEnableAutoRecovery Enable automatic error recovery
 * @param bEnableDetailedLogging Enable detailed error logging
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Error")
void SetErrorHandlingMode(bool bEnableAutoRecovery, bool bEnableDetailedLogging);

/**
 * Get system health score
 * @return Health score (0.0 to 1.0)
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Error")
float GetSystemHealthScore() const;

/**
 * Get error history
 * @param MaxEntries Maximum number of entries to return
 * @return Array of error information
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Error")
TArray<FErrorInfo> GetErrorHistory(int32 MaxEntries = 10) const;

/**
 * Validate system configuration
 * @return True if configuration is valid
 */
UFUNCTION(BlueprintCallable, Category = "MetaHuman Bridge|Error")
bool ValidateSystemConfiguration() const;
```

##### Python Bindings

```cpp
/**
 * Get bridge version string
 * @return Version string
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Python")
FString GetBridgeVersionString() const;

/**
 * Get supported DNA versions
 * @return Array of supported DNA version strings
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Python")
TArray<FString> GetSupportedDNAVersions() const;

/**
 * Check if bridge is initialized for Python
 * @return True if initialized for Python
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Python")
bool IsBridgeInitializedForPython() const;

/**
 * Get bridge information
 * @return Map of bridge information
 */
UFUNCTION(BlueprintCallable, BlueprintPure, Category = "MetaHuman Bridge|Python")
TMap<FString, FString> GetBridgeInformation() const;
```

## Python API

### Core Functions

```python
def load_dna_from_file(file_path: str) -> bool:
    """
    Load DNA data from file
    
    Args:
        file_path: Path to the DNA file
        
    Returns:
        True if loaded successfully
    """

def save_dna_to_file(file_path: str) -> bool:
    """
    Save DNA data to file
    
    Args:
        file_path: Path where to save the DNA file
        
    Returns:
        True if saved successfully
    """

def is_valid_dna() -> bool:
    """
    Check if DNA is currently loaded and valid
    
    Returns:
        True if DNA is valid
    """

def get_mesh_count() -> int:
    """
    Get the number of meshes in the loaded DNA
    
    Returns:
        Number of meshes
    """

def get_joint_count() -> int:
    """
    Get the number of joints in the loaded DNA
    
    Returns:
        Number of joints
    """
```

### Joint Manipulation

```python
def get_joint_name(joint_index: int) -> str:
    """Get the name of a joint by index"""

def set_joint_translation(joint_index: int, x: float, y: float, z: float) -> bool:
    """Set joint translation"""

def get_joint_translation(joint_index: int) -> Tuple[float, float, float]:
    """Get joint translation"""

def set_joint_rotation(joint_index: int, pitch: float, yaw: float, roll: float) -> bool:
    """Set joint rotation"""

def get_joint_rotation(joint_index: int) -> Tuple[float, float, float]:
    """Get joint rotation"""

def set_joint_scale(joint_index: int, x: float, y: float, z: float) -> bool:
    """Set joint scale"""

def get_joint_scale(joint_index: int) -> Tuple[float, float, float]:
    """Get joint scale"""
```

### Blend Shapes

```python
def get_blendshape_target_count(mesh_index: int) -> int:
    """Get the number of blend shape targets for a mesh"""

def get_blendshape_target_name(mesh_index: int, target_index: int) -> str:
    """Get blend shape target name"""

def set_blendshape_weight(mesh_index: int, target_index: int, weight: float) -> bool:
    """Set blend shape weight"""

def get_blendshape_weight(mesh_index: int, target_index: int) -> float:
    """Get blend shape weight"""
```

### Performance and Error Handling

```python
def initialize_performance_optimization(config: Dict[str, str]) -> bool:
    """Initialize performance optimization system"""

def optimize_memory_usage(force_gc: bool = False) -> bool:
    """Optimize memory usage"""

def get_current_performance_metrics() -> Dict[str, str]:
    """Get current performance metrics"""

def set_error_handling_mode(auto_recovery: bool, detailed_logging: bool) -> None:
    """Set error handling mode"""

def get_system_health_score() -> float:
    """Get system health score"""

def validate_system_configuration() -> bool:
    """Validate system configuration"""
```

## Data Structures

### FErrorInfo

```cpp
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FErrorInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorCode;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Timestamp;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Severity;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Context;
};
```

### FDNAValidationResult

```cpp
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNAValidationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int64 FileSizeBytes;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    FString DNAVersion;
};
```

### EDNAValidationType

```cpp
UENUM(BlueprintType)
enum class EDNAValidationType : uint8
{
    Basic       UMETA(DisplayName = "Basic"),
    Detailed    UMETA(DisplayName = "Detailed"),
    Complete    UMETA(DisplayName = "Complete")
};
```

## Error Codes

| Code | Description | Severity | Recovery |
|------|-------------|----------|----------|
| DNA_001 | File not found | Error | Check file path |
| DNA_002 | Invalid DNA format | Error | Use valid DNA file |
| DNA_003 | Corrupted DNA data | Error | Restore from backup |
| DNA_004 | Unsupported DNA version | Warning | Update bridge |
| JOINT_001 | Invalid joint index | Error | Check joint count |
| JOINT_002 | Joint constraint violation | Warning | Adjust values |
| BLEND_001 | Invalid blend shape index | Error | Check blend shape count |
| BLEND_002 | Weight out of range | Warning | Clamp to 0.0-1.0 |
| PERF_001 | Memory allocation failed | Error | Free memory |
| PERF_002 | GPU acceleration unavailable | Warning | Use CPU fallback |

## Events and Delegates

### C++ Delegates

```cpp
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDNALoaded, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDNASaved, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnErrorOccurred, const FString&, ErrorCode, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceWarning, const FString&, WarningMessage);
```

### Blueprint Events

- **On DNA Loaded** - Called when DNA file is loaded
- **On DNA Saved** - Called when DNA file is saved
- **On Error Occurred** - Called when an error occurs
- **On Performance Warning** - Called when performance issues are detected

---

For more detailed examples and usage patterns, see the [Examples](../Examples/README.md) section.
