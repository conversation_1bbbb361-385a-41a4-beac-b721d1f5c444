// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronVFXBridge_init() {}
	AURACRONVFXBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature();
	AURACRONVFXBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronVFXBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronVFXBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronVFXBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronVFXBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x7A101966,
				0x023508EA,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronVFXBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronVFXBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronVFXBridge(Z_Construct_UPackage__Script_AuracronVFXBridge, TEXT("/Script/AuracronVFXBridge"), Z_Registration_Info_UPackage__Script_AuracronVFXBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x7A101966, 0x023508EA));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
